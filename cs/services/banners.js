import graph from './graph';
import io from './io';

const transformBannerToData = (banner) => ({
    color: banner.color,
    status: banner.status,
    start_date: banner.dateFrom,
    end_date: banner.dateTo,
    analytics_category: banner.analyticsCategory,
    positions: banner.positions?.length ? banner.positions : null,
    rates: banner.rates?.length ? banner.rates : null,
    audience_type: banner.audienceType || null,
    employees_count: banner.audienceSize?.length ? banner.audienceSize : null,
    outgoing_documents_count: banner?.outgoingDocumentsCount?.length
        ? banner.outgoingDocumentsCount
        : null,
    incoming_documents_sign_count: banner?.incomingDocumentsSignCount?.length
        ? banner.incomingDocumentsSignCount
        : null,
    activity_period: banner.activityPeriod || null,
    days_before_signature_expires: banner.daysBeforeSignatureExpires || null,
    content: [
        {
            language: 'uk',
            text: banner.text,
            link_text: banner.linkText,
            link_url: banner.link,
        },
        {
            language: 'en',
            text: banner.textEn,
            link_text: banner.linkTextEn,
            link_url: banner.link,
        },
    ],
});

const BANNER_FRAGMENT = `
    id
    text
    textEn
    linkText
    linkTextEn
    link
    color
    analyticsCategory
    status
    dateFrom
    dateTo
    positions
    rates
    audienceType
    outgoingDocumentsCount
    incomingDocumentsSignCount
    activityPeriod
    employeesCount
    daysBeforeSignatureExpires
`;

export async function getActiveBanner() {
    const { activeBanner } = await graph.get(`{
        activeBanner {
            ${BANNER_FRAGMENT}
        }
    }`);

    return activeBanner;
}

export async function getBannersList() {
    const { saBanner } = await graph.get(`{
        saBanner {
            ${BANNER_FRAGMENT}
        }
    }`);

    return saBanner;
}

export async function updateBanner(banner) {
    return await io.request(
        'PUT',
        `/internal-api/banner/${banner.id}`,
        transformBannerToData(banner),
    );
}

export async function createBanner(banner) {
    return await io.post('/internal-api/banner', transformBannerToData(banner));
}

/**
 * Отримати поточний промо банер для користувача
 *
 * @returns {Promise<import('types/banner').PromoBanner | null>}
 */
export async function resolvePromoBanner() {
    return await io.post('/internal-api/banner/promo/resolve', undefined, true);
}

/**
 * Відмітити, що ми показали промо-банер користувачу
 *
 * @param {Object} params
 * @param {import('types/banner').PromoBannerCampaign} params.campaign - Тип банера, наприклад 'app_promo' або 'kasa_promo'
 * @returns {Promise<void>}
 */
export async function updatePromoBannerShown({ campaign }) {
    await io.post('/internal-api/banner/promo/shown', {
        campaign,
    });
}
