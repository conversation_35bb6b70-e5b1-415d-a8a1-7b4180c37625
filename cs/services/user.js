import { INVITE_EMAIL_LOCAL_STORAGE_KEY } from 'components/auth/utils';
import { sortRoles } from 'lib/ts/helpers';
import { getLocalStorageItem } from 'lib/webStorage';
import {
    apiCompany,
    apiCurrentRole,
    apiCurrentSignSession,
    apiCurrentUser,
    transformTokenUserData,
} from 'records/user';
import { broadcastAuthChannel } from 'services/auth';

import { RoleStatuses, SignSessionStatus } from './enums';

import graph, {
    BILLING_COMPANY_CONFIG_FRAGMENT,
    BILLING_FRAGMENT,
    EMPLOYEE_ROLE_FRAGMENT,
    RATES_FRAGMENT,
    ROLE_FRAGMENT,
    TRIAL_RATES_FRAGMENT,
    USER_FRAGMENT,
} from './graph';
import io from './io';
import { prepareSignSessionOptions } from './sign-session';
import {
    getCurrentCompanyUsedDocumentCount,
    updateProfile,
    updateRole,
} from './ts/user';

const getDataFromToken = async (token) => {
    const userData = await io.get(
        `/internal-api/bill-generation/${token}`,
        {},
        true,
    );

    return userData ? transformTokenUserData(userData) : null;
};

async function getCurrentUser() {
    const { currentRole, currentUser } = await graph.get(/* GraphQL */ `
        {
            currentRole {
                ${ROLE_FRAGMENT}
                isFreeRateBannerShown
                isFreeTrialEndBannerShown
                dateCreated
                company {
                    id
                    config
                    edrpou
                    name
                    isLegal
                    phone

                    activeRates
                    emailDomains
                    allowedIps
                    allowedApiIps
                    inactivityTimeout
                    dateCreated
                    usedDocumentCount

                    rateExtensions {
                        id
                        status
                        type
                        bill_id
                        date_expiring
                        bill_document_id
                    }

                    roles {
                        id
                        status
                    }

                    hasInvalidSignedDocuments

                    renderSignatureInInterface
                    renderSignatureOnPrintDocument

                    ${RATES_FRAGMENT}
                    ${TRIAL_RATES_FRAGMENT}
                    ${BILLING_FRAGMENT}
                    ${BILLING_COMPANY_CONFIG_FRAGMENT}
                }
                user {
                    ${USER_FRAGMENT}
                    roles {
                        id
                        status
                        company {
                            id
                            edrpou
                            name
                            isLegal
                            activeRates
                            usedDocumentCount
                            employeesNumber
                            ${BILLING_FRAGMENT}
                            ${RATES_FRAGMENT}
                            ${BILLING_COMPANY_CONFIG_FRAGMENT}
                            roles {
                                status
                            }
                        }
                        dateAgreed
                        userRole
                        position

                        isAdmin
                        canEditCompany
                    }
                }
                sortDocuments
                showInviteTooltip
            }
            currentUser {
                ${USER_FRAGMENT}
            }
        }
    `);

    if (currentUser) {
        broadcastAuthChannel.logIn(currentUser.id);
    }

    if (currentRole?.user?.id) {
        return apiCurrentRole(currentRole);
    }

    return currentUser ? apiCurrentUser(currentUser) : null;
}

async function getBillingAccounts() {
    const { currentRole } = await graph.get(/* GraphQL */ `
        {
            currentRole {
                company {
                    ${BILLING_FRAGMENT}
                }
            }
        }
    `);

    return currentRole.company.billingAccounts;
}

async function getCurrentSignSession() {
    const { currentSignSession } = await graph.get(
        /* GraphQL */
        `
            {
                currentSignSession {
                    id
                    documentId

                    roleId
                    edrpou
                    email
                    isLegal

                    type
                    status
                    documentStatus
                    source

                    finishUrl
                    cancelUrl
                    signParameters

                    role {
                        status
                        user {
                            phone
                            isPhoneVerified
                        }
                    }
                }
            }
        `,
        '',
        prepareSignSessionOptions(),
    );
    if (
        currentSignSession &&
        (currentSignSession.status === SignSessionStatus.STARTED ||
            currentSignSession.status === SignSessionStatus.CREATED)
    ) {
        const { role, ...rest } = currentSignSession;
        const flattenSignSession = {
            ...rest,
            phone: (role && role.user && role.user.phone) || null,
            roleStatus: (role && role.status) || RoleStatuses.PENDING,
            isPhoneVerified:
                (role && role.user && role.user.isPhoneVerified) || false,
        };
        return apiCurrentSignSession(flattenSignSession);
    }
    return null;
}

async function getContactPersons(args = {}) {
    const contactPersonsArgs = {
        offset: args.offset,
        search: args.search,
    };
    const {
        allContactPersons: { contact_persons },
    } = await graph.get(`
        {
            allContactPersons${graph.formatArguments(contactPersonsArgs)} {
                contact_persons {
                    id
                    email
                    mainRecipient
                    firstName
                    secondName
                    lastName
                    contact {
                        id
                        name
                        shortName
                        edrpou
                    }
                }
            }
        }
    `);
    return {
        users: contact_persons,
    };
}

async function getContacts(args = {}) {
    const {
        allContacts: { contacts, count },
        countUnregisteredContacts: countUnregistered,
    } = await graph.get(`
        {
            allContacts${graph.formatArguments(args)} {
                contacts {
                    id
                    isRegistered
                    name
                    edrpou
                    shortName
                    persons {
                        id
                        email
                        isEmailHidden
                        firstName
                        secondName
                        lastName
                        mainRecipient
                        phones {
                            phone
                        }
                    }
                    tags {
                        id
                        name
                    }
                }
                count
            }
            countUnregisteredContacts
        }
    `);

    return {
        contacts,
        count,
        countUnregistered,
    };
}

async function getUnregisteredContactsCount() {
    const { countUnregisteredContacts } = await graph.get(/* GraphQL */ `
        {
            countUnregisteredContacts
        }
    `);
    return countUnregisteredContacts;
}

/**
 *
 * @param {object} [args]
 * @param {string} [args.search]
 * @param {boolean} [args.canSignAndRejectDocument]
 * @return {Promise<{currentCompanyRoles: import('../types/user').CurrentCompanyCoworkerRole[]}>}
 */
async function getCurrentCompanyRoles(args = {}) {
    const { currentCompanyRoles } = await graph.query({
        query: /* GraphQL */ `
            query CurrentCompanyRoles(
                $search: String
                $canSignAndRejectDocument: Boolean
            ) {
                currentCompanyRoles(
                    search: $search
                    canSignAndRejectDocument: $canSignAndRejectDocument
                ) {
                    id
                    isAdmin
                    canSignAndRejectDocument
                    canSignAndRejectDocumentExternal
                    canSignAndRejectDocumentInternal
                    user {
                        email
                        authPhone
                        firstName
                        secondName
                        lastName
                        isRegistered
                    }
                }
            }
        `,
        variables: args,
    });

    return {
        currentCompanyRoles: currentCompanyRoles.toSorted(sortRoles),
    };
}

async function getRolesForReviewRequest(docId = null, args = {}) {
    const rolesForReviewData = await graph.get(`
        {
            ${
                docId
                    ? `document(id: "${docId}") { reviewRequests { toRole { id } } }`
                    : ''
            }
            currentRole { id }
            currentCompanyRoles${graph.formatArguments(args)} {
                id
                user {
                    email
                    authPhone
                    firstName
                    secondName
                    lastName
                }
            }
        }
    `);
    const sortedRoles = rolesForReviewData.currentCompanyRoles.sort(sortRoles);
    return { ...rolesForReviewData, currentCompanyRoles: sortedRoles };
}

async function getCompany(id, tags) {
    const { company } = await graph.get(`
        {
            company${graph.formatArguments({ id })} {
                id
                edrpou
                ipn
                name

                phone

                hasInvalidSignedDocuments

                activeRates
                config
                ${RATES_FRAGMENT}
                ${TRIAL_RATES_FRAGMENT}
                ${BILLING_FRAGMENT}
                ${BILLING_COMPANY_CONFIG_FRAGMENT}

                roles${graph.formatArguments({ tags })} {
                    ${EMPLOYEE_ROLE_FRAGMENT}
                    tags {
                        id
                        name
                    }

                    fields {
                        id
                        name
                    }

                    user {
                        id
                        email
                        authPhone
                        firstName
                        secondName
                        lastName
                        isRegistered
                    }
                    hasToken
                    registrationReferralUrl

                    position
                }
            }
        }
    `);
    // sorting by name. If there is no name sort by email
    const sortedRoles = company.roles.sort(sortRoles);

    return company ? apiCompany({ ...company, roles: sortedRoles }) : null;
}

async function getCompanyRolesForConfigApi(id) {
    const {
        company: { roles },
    } = await graph.get(`
        {
            company${graph.formatArguments({ id })} {
                roles {
                    ${EMPLOYEE_ROLE_FRAGMENT}
                    tags {
                        id
                        name
                    }

                    fields {
                        id
                        name
                    }

                    user {
                        id
                        email
                        authPhone
                        firstName
                        secondName
                        lastName
                        isRegistered
                    }
                    hasToken
                    registrationReferralUrl

                    position
                }
            }
        }
    `);
    return roles.toSorted(sortRoles);
}

const sortTagsAlphabetically = (a, b) => {
    const nameA = a.name.trim().toLowerCase();
    const nameB = b.name.trim().toLowerCase();
    if (nameA < nameB) return -1;
    if (nameA > nameB) return 1;
    return 0;
};

async function getCurrentCompanyTags(args = {}) {
    const { allTags } = await graph.get(`
        {
            allTags${graph.formatArguments(args)} {
                id
                name
            }
        }
    `);
    return allTags.toSorted(sortTagsAlphabetically);
}

async function getCurrentCompanyDocumentFields(args = {}) {
    const { documentsFields } = await graph.get(`
    {
        documentsFields${graph.formatArguments(args)} {
            id
            name
            canEdit

            roles {
                id
                user {
                    id
                    email
                    firstName
                    secondName
                    lastName
                }
            }
        }
    }`);
    return documentsFields;
}

async function getTagsForDocumentFilter() {
    const { allTagsForDocumentFilter } = await graph.get(/* GraphQL */ `
        {
            allTagsForDocumentFilter {
                id
                name
            }
        }
    `);
    return allTagsForDocumentFilter;
}

async function getRecipients(getParams) {
    const url = io.buildUrl('/internal-api/statistics/recipients', getParams);
    return await io.getAsJson(url);
}

async function syncContacts() {
    await io.post('/internal-api/contacts/sync');
}

async function changeLanguage(language) {
    return await io.post(
        '/internal-api/profile/language',
        { language },
        false,
        null,
        true,
    );
}

async function update2FAState(enable) {
    return await io.post(
        '/internal-api/profile/2fa',
        { enable },
        false,
        null,
        true,
    );
}

async function updateCompany(edrpou, data) {
    return await io.request(
        'PATCH',
        `/internal-api/companies/${edrpou}`,
        data,
        false,
        prepareSignSessionOptions(),
    );
}

/**
 * Оновлення налаштувань компанії супер адміном
 */
async function updateCompanyAdminConfig(companyId, data) {
    return await io.request(
        'PATCH',
        `/internal-api/companies/${companyId}/configs`,
        { admin_config: data },
        false,
    );
}

/**
 * Оновлення налаштувань компанії її співробітником
 */
async function updateCompanyAdditionalConfig(data) {
    return await io.request(
        'PATCH',
        '/internal-api/companies/additional/configs',
        data,
        false,
    );
}

async function restoreRole(roleId) {
    return await updateRole(roleId, { status: RoleStatuses.ACTIVE });
}

async function deleteRole(roleId) {
    return await io.request('DELETE', `/internal-api/roles/${roleId}`);
}

async function denyRole(roleId) {
    return await io.post(`/internal-api/roles/${roleId}/deny`);
}

async function resendVerifyEmail() {
    return await io.post(
        '/internal-api/registration/resend-confirmation-email',
    );
}

async function completeRegistration(data = {}, options) {
    const inviteEmailParams =
        getLocalStorageItem(INVITE_EMAIL_LOCAL_STORAGE_KEY) || {};
    Object.entries(inviteEmailParams).forEach(([paramKey, paramValue]) => {
        data.append(paramKey, paramValue);
    });

    return await io.post(
        '/internal-api/registration/complete',
        data,
        false,
        options,
    );
}

async function newCompanyAgreed(data) {
    await io.post('/internal-api/roles/agree', data);
}

async function addCompany(data = {}, options) {
    const inviteEmailParams =
        getLocalStorageItem(INVITE_EMAIL_LOCAL_STORAGE_KEY) || {};
    Object.entries(inviteEmailParams).forEach(([paramKey, paramValue]) => {
        data.append(paramKey, paramValue);
    });

    return await io.post('/internal-api/companies', data, true, options);
}

/**
 * Get token that should be signed to register a new active role
 *
 * @returns {Promise<{
 *  token: string,
 *  hash: string
 * }>}
 */
async function getRegistrationToken() {
    return await io.post(
        '/companies/registration/token',
        null,
        true,
        prepareSignSessionOptions(),
    );
}

async function createToken(data) {
    const { token } = await io.post('/internal-api/tokens', data, true);
    return token;
}

async function deleteToken(data) {
    return await io.request('DELETE', '/internal-api/tokens', data);
}

async function searchIsContactExistsOrFailure(search) {
    const {
        allContacts: { count },
    } = await graph.query({
        query: /* GraphQL */ `
            query SearchContacts($search: String, $limit: Int = 1) {
                allContacts(search: $search, limit: $limit) {
                    count
                }
            }
        `,
        variables: { search },
    });

    if (count === 0) {
        throw new Error('Contact not found');
    }

    return count;
}

async function createContactPerson(data) {
    return await io.post('/internal-api/contacts/persons', data);
}

async function updateContactPerson(id, data) {
    return await io.request(
        'PATCH',
        `/internal-api/contacts/persons/${id}`,
        data,
    );
}

async function deleteContactPerson(id) {
    return await io.request('DELETE', `/internal-api/contacts/persons/${id}`);
}

async function updateContact(id, data) {
    return await io.request('PATCH', `/internal-api/contacts/${id}`, data);
}

async function inviteUnregisteredContacts() {
    return await io.post('/internal-api/invite/contacts');
}

function getContactDossierURL(edrpou) {
    return `/internal-api/contacts/dossier?code=${edrpou}`;
}

async function createTagsAccess(data) {
    return await io.post('/internal-api/roles/tags', data);
}

async function createDocumentsFieldsAccess(data) {
    return await io.post('/internal-api/documents/fields/roles', data);
}

async function updateTagsAccess(data) {
    return await io.post('/internal-api/roles/tags/connections', data);
}

async function deleteTagsAccess(data) {
    return await io.request(
        'DELETE',
        '/internal-api/roles/tags/connections',
        data,
    );
}

async function deleteDocumentsFieldsAccess(data) {
    return await io.request(
        'DELETE',
        '/internal-api/documents/fields/roles',
        data,
    );
}

async function createContactsTags(data) {
    return await io.post('/internal-api/contacts/tags', data);
}

async function updateContactsTags(data) {
    return await io.post('/internal-api/contacts/tags/connections', data);
}

async function deleteContactsTags(data) {
    return await io.request(
        'DELETE',
        '/internal-api/contacts/tags/connections',
        data,
    );
}

async function subscribeToEsputnik(data) {
    return await io.post('/internal-api/profile/subscription', data);
}

/**
 * Register a new signer in sign session
 *
 * @param {{
 *   token: string,
 *   signature: string,
 *   signatureFormat: string
 * }} data
 */
async function registerSigner(data) {
    return await io.post(
        '/internal-api/registration/signers',
        {
            token: data.token,
            signature: data.signature,
            signature_format: data.signatureFormat,
        },
        false,
        prepareSignSessionOptions(),
    );
}

async function updateDefaultRecipient(roleId) {
    return io.post('/internal-api/roles/default-recipient', {
        role_id: roleId,
    });
}

async function setRegistrationSource(source) {
    return io.post('/internal-api/registration/source', { source });
}

async function getRolesWithInvalidSignatures() {
    const { currentRole } = await graph.get(`
        {
            currentRole {
                company {
                    roles${graph.formatArguments({
                        hasInvalidSignatures: true,
                    })} {
                        id
                        user {
                            email
                        }
                    }
                }
            }
        }
    `);

    return { rolesWithInvalidSignatures: currentRole?.company?.roles };
}

/**
 * @param {import('gql-types').BillingCompanyConfig} config
 * @param {PermissionCategory} permission
 * @returns {boolean}
 */
function isAvailableFunctionality(config, permission) {
    const isProhibited =
        !config.hasOwnProperty(permission) ||
        config[permission] === 0 ||
        config[permission] === false;
    if (isProhibited) {
        return false;
    }
    // if there are the oldest pro or start rates, we will show available functions but ban adding new
    if (Number.isInteger(config[permission]) && config[permission] > 0) {
        return true;
    }
    return true;
}

/**
 * Saves to the backend that the user's device is mobile
 * @returns Promise<void>
 */
async function saveIsMobileDeviceUsage() {
    return await io.post('/internal-api/phone-usage', { mobile_usage: true });
}

/**
 * @param {string} roleId
 * @param {Object} permission
 * @returns Promise<void>
 */
async function updateGlobalAdminPermissions(roleId, permission) {
    return io.patch(`/internal-api/roles/${roleId}`, {
        super_admin_permissions: permission,
    });
}

/**
 * @param {string} feedback
 * @returns  Promise<void>
 */
async function sendFeedback(feedback) {
    return io.post(`/internal-api/feedback`, { feedback });
}

async function syncUserRoles() {
    return io.get(`/internal-api/registration/sync-roles`, {}, true);
}

async function updateUserTrialAutoEnabledField(data) {
    return io.patch(`/internal-api/profile/trial-auto-enabled`, {
        trial_auto_enabled: data,
    });
}

export {
    createTagsAccess,
    createDocumentsFieldsAccess,
    deleteTagsAccess,
    deleteDocumentsFieldsAccess,
    getCurrentUser,
    getCurrentSignSession,
    getContactPersons,
    getContacts,
    getCompany,
    getCompanyRolesForConfigApi,
    getRecipients,
    getCurrentCompanyRoles,
    getCurrentCompanyTags,
    getCurrentCompanyDocumentFields,
    getBillingAccounts,
    getRolesForReviewRequest,
    getRegistrationToken,
    getUnregisteredContactsCount,
    getContactDossierURL,
    getTagsForDocumentFilter,
    syncContacts,
    updateProfile,
    changeLanguage,
    update2FAState,
    updateCompany,
    updateCompanyAdminConfig,
    updateRole,
    resendVerifyEmail,
    restoreRole,
    registerSigner,
    completeRegistration,
    newCompanyAgreed,
    addCompany,
    deleteRole,
    denyRole,
    subscribeToEsputnik,
    createToken,
    deleteToken,
    createContactPerson,
    updateContactPerson,
    searchIsContactExistsOrFailure,
    deleteContactPerson,
    updateContact,
    updateTagsAccess,
    updateCompanyAdditionalConfig,
    inviteUnregisteredContacts,
    createContactsTags,
    updateContactsTags,
    deleteContactsTags,
    updateDefaultRecipient,
    setRegistrationSource,
    getCurrentCompanyUsedDocumentCount,
    isAvailableFunctionality,
    getRolesWithInvalidSignatures,
    saveIsMobileDeviceUsage,
    updateGlobalAdminPermissions,
    sendFeedback,
    syncUserRoles,
    updateUserTrialAutoEnabledField,
    getDataFromToken,
};
