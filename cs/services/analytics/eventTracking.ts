import {
    sendPromoBannerToGTMV4,
    sendPromoPopupToGTMV4,
    sendSystemBannerToGTMV4,
    sendToGTMV4,
} from './gtm';

interface AppConfig {
    ANALYTICS_GA_TRACKING_ID?: string | null;
    ANALYTICS_GA_KASA_TRACKING_ID?: string | null;
    ANALYTICS_GTM_KASA_TRACKING_ID?: string | null;
    ANALYTICS_FB_PIXEL_ID?: string | null;
    DEBUG?: boolean;
}

declare const config: AppConfig;
declare function ga(...args: any[]): void;
declare function gtagKasa(
    command: string,
    action: string,
    options: Record<string, any>,
): void;
declare function fbq(
    command: 'track',
    eventName: string,
    customData?: Record<string, any>,
): void;

declare global {
    interface Window {
        dataLayer: Record<string, any>[];
        [key: string]: any;
    }
}

interface GAOptions {
    type: 'event' | 'pageview';
    category: string;
    action: string;
    label?: string | null;
    value?: number;
    callback?: () => void;
    nonInteraction?: boolean;
}

interface GTMV4Options {
    event?: string;
    category?: string | string[];
    action?: string | string[];
    label?: string | null | string[];
    param?: string;
    param1?: string;
}

interface GAKasaOptions {
    category: string;
    action: string;
    label?: string | null;
    value?: number;
    callback?: () => void;
}

type LabelsObject = Record<string, string | number | boolean>;

export const isGAEnabled: boolean =
    config.ANALYTICS_GA_TRACKING_ID !== undefined &&
    config.ANALYTICS_GA_TRACKING_ID !== null &&
    typeof ga === 'function';

const isGAKasaEnabled: boolean =
    config.ANALYTICS_GA_KASA_TRACKING_ID !== undefined &&
    config.ANALYTICS_GA_KASA_TRACKING_ID !== null &&
    typeof gtagKasa === 'function';

const isFBPEnabled: boolean =
    !!config.ANALYTICS_FB_PIXEL_ID && typeof fbq === 'function';

function composeLabels(labels: LabelsObject): string {
    return Object.entries(labels)
        .map(([key, value]) => `${key}:${value}`)
        .join(', ');
}

function createFunctionWithTimeout(
    callback: () => void,
    timeout: number = 1000,
): () => void {
    let called = false;

    const fn = (): void => {
        if (!called) {
            called = true;
            callback();
        }
    };

    setTimeout(fn, timeout);
    return fn;
}

function pageview(location: string, page: string): void {
    if (isGAEnabled) {
        ga('send', { hitType: 'pageview', location, page });
    }
}

function sendToGA(option: GAOptions): void {
    if (isGAEnabled) {
        ga('send', {
            hitType: option.type,
            eventCategory: option.category,
            eventAction: option.action,
            eventLabel: option.label,
            eventValue: option.value,
            hitCallback: option.callback,
            nonInteraction: option.nonInteraction,
        });
    }
}

/**
 * @deprecated - use sendToGTMV4 instead
 * Variant to send events with GTM
 */
export function sendToGTM({
    event = 'ga_event',
    category,
    action,
    label,
    param,
    param1,
}: GTMV4Options): void {
    if (config.DEBUG) {
        console.log('>'.repeat(10), ' GTM: SEND EVENT ', '<'.repeat(10));
        console.table({ event, category, action, label, param, param1 });
    }
    window.dataLayer = window.dataLayer || [];

    const data: Record<string, any> = {
        event,
        eventAction: action || '',
        eventCategory: category || '',
        eventLabel: label || '',
    };

    if (param) {
        data.eventParam = param;
    }

    if (param1) {
        data.eventParam1 = param1;
    }

    window.dataLayer.push(data);
}

function sendToGAKasa(option: GAKasaOptions): void {
    if (isGAKasaEnabled) {
        const {
            ANALYTICS_GA_KASA_TRACKING_ID: trackingGAKasaId,
            ANALYTICS_GTM_KASA_TRACKING_ID: trackingKasaId,
        } = config;

        if (trackingKasaId) window[`ga-disable-${trackingKasaId}`] = false;
        if (trackingGAKasaId) window[`ga-disable-${trackingGAKasaId}`] = false;

        gtagKasa('event', option.action, {
            event_category: option.category,
            event_action: option.action,
            event_label: option.label,
            value: option.value,
            event_callback: option.callback,
        });

        if (trackingKasaId) window[`ga-disable-${trackingKasaId}`] = true;
        if (trackingGAKasaId) window[`ga-disable-${trackingGAKasaId}`] = true;
    }
}

function setCustomDimensions(
    dimensions: Record<string, any>,
    sendNewDimensions = false,
): void {
    if (isGAEnabled) {
        ga('set', dimensions);
    }
    if (sendNewDimensions) {
        sendToGA({
            type: 'event',
            category: 'custom_dimension',
            action: 'set',
            nonInteraction: true,
        });
        sendToGTM({ category: 'custom_dimension', action: 'set' });
    }
}

/**
 * @deprecated
 * @use sendToGTM
 */
function sendEvent(
    category: string,
    action: string,
    label?: string | LabelsObject | null,
    callback?: () => void,
): void {
    if (config.DEBUG) {
        console.log('>'.repeat(10), ' GA: SEND EVENT ', '<'.repeat(10));
        console.table({ category, action, label });
    }
    try {
        const eventLabel: string | undefined | null =
            typeof label === 'object' && label !== null
                ? composeLabels(label)
                : label;

        sendToGA({
            type: 'event',
            category,
            action,
            label: eventLabel,
            callback: callback
                ? createFunctionWithTimeout(callback)
                : undefined,
        });
        sendToGTM({ category, action, label: eventLabel ?? undefined });
    } catch (error) {
        // it's ok
    }
}

function sendEventKasa(
    category: string,
    action: string,
    label?: string | LabelsObject | null,
    callback?: () => void,
): void {
    const eventLabel: string | undefined | null =
        typeof label === 'object' && label !== null
            ? composeLabels(label)
            : label;

    sendToGAKasa({
        category,
        action,
        label: eventLabel,
        callback: callback ? createFunctionWithTimeout(callback) : undefined,
    });
}

function sendEventFBP(
    eventName: string,
    customData: Record<string, any> = {},
): void {
    if (isFBPEnabled) {
        fbq('track', eventName, customData);
    }
}

export default {
    pageview,
    sendToGTM,
    sendToGTMV4,
    sendEvent,
    sendEventKasa,
    setCustomDimensions,
    sendEventFBP,
    sendPromoBannerToGTMV4,
    sendPromoPopupToGTMV4,
    sendSystemBannerToGTMV4,
};
