/**
 * @see <https://docs.google.com/document/d/1WOb20PGJGw8LVT4TDlr1PJMvARveEFQDCYijH3HuiX4/edit?tab=t.0>
 */

export type GTMEventName<T extends string> =
    // ec_назва_івенту - івент в кабінеті едо
    | `ec_${T}`
    //el_назва_івенту - івент на лендінгу едо
    | `el_${T}`
    | string;

interface SendToGTMV4Params {
    event: GTMEventName<string>;
    category?: string;
    action?: string;
    label?: string;
}

interface SendToGTMV4Payload {
    // завжди передаємо значення event_ga4
    event: 'event_ga4';
    // а тут вже назву івента з таблички
    event_name: GTMEventName<string>;
    // значення із колонки Category, якщо його не зазначено можна просто передавати пусту строку
    eventCategory: string;
    // значення із колонки Action, якщо його не зазначено можна просто передавати пусту строку
    eventAction: string;
    // значення із колонки Label, якщо його не зазначено можна просто передавати пусту строку
    eventLabel: string;
}

export const sendToGTMV4 = (params: SendToGTMV4Params) => {
    // @ts-ignore
    window.dataLayer = window.dataLayer || [];

    const data: SendToGTMV4Payload = {
        event: 'event_ga4',
        event_name: params.event,
        eventAction: params.action || '',
        eventCategory: params.category || '',
        eventLabel: params.label || '',
    };

    // @ts-ignore
    window.dataLayer.push(data);

    if (config.DEBUG) {
        console.log('>'.repeat(10), ' GTM V4: SEND EVENT ', '<'.repeat(10));
        // @ts-ignore
        console.table(window.dataLayer[window.dataLayer.length - 1]);
    }
};

/**
 * Уніфікована функція для відправки івентів про промо-банери у GTM V4
 *
 * Все що повʼязано з просуванням якихось функцій або сервісів, має бути
 * відправлено через цю функцію, щоб дотримуватися однакового формату
 */
export const sendPromoBannerToGTMV4 = (params: {
    campaign:
        | 'detected_in_time' // https://vchasno-group.atlassian.net/browse/DOC-7485
        | 'detected_in_time_sidebar' // https://vchasno-group.atlassian.net/browse/DOC-7485
        | 'app_sidebar' // https://vchasno-group.atlassian.net/browse/DOC-6556
        | 'drop_files' // https://vchasno-group.atlassian.net/browse/DOC-6278
        | 'kep_rates' // https://vchasno-group.atlassian.net/browse/DOC-6265
        | 'try_trial_sidebar' // https://vchasno-group.atlassian.net/browse/DOC-6758
        | 'archive_top' // https://vchasno-group.atlassian.net/browse/DOC-7332
        | 'app_responsive_top'; // https://vchasno-group.atlassian.net/browse/DOC-6556
    action: 'shown' | 'click' | 'closed_manual' | 'closed_auto';
}) =>
    sendToGTMV4({
        event: `banner_${params.action}_${params.campaign}`,
    });

/**
 * Уніфікована функція для відправки івентів про промо-попапи у GTM V4
 *
 * Все що повʼязано з просуванням якихось функцій або сервісів, має бути
 * відправлено через цю функцію, щоб дотримуватися однакового формату
 */
export const sendPromoPopupToGTMV4 = (params: {
    campaign:
        | 'app_sign_success' // https://vchasno-group.atlassian.net/browse/DOC-7399
        | 'kasa_sign_success' // https://vchasno-group.atlassian.net/browse/EC-7
        | 'app_responsive' // https://vchasno-group.atlassian.net/browse/DOC-7279
        | 'guide_sidebar' // https://vchasno-group.atlassian.net/browse/DOC-4072
        | 'archive_survey' // https://vchasno-group.atlassian.net/browse/DOC-7283
        | 'get_kep_ad' // https://vchasno-group.atlassian.net/browse/QES-411
        | 'view_limit_exceeded' // https://vchasno-group.atlassian.net/browse/DOC-7332
        | 'view_limit_almost_exceeded' // https://vchasno-group.atlassian.net/browse/DOC-7332
        | 'unlimit_sending_fop' // https://vchasno-group.atlassian.net/browse/DOC-7331
        | 'trial_info'; // https://vchasno-group.atlassian.net/browse/DOC-6758
    action: 'shown' | 'click' | 'closed_manual' | 'closed_auto';
}) =>
    sendToGTMV4({
        event: `popup_${params.action}_${params.campaign}`,
    });

/**
 * Це банер-полоска над списком документів
 */
export const sendSystemBannerToGTMV4 = (params: {
    action: 'shown' | 'click' | 'closed_manual';
    category: string; // з бекенду
}) =>
    sendToGTMV4({
        event: `banner_${params.action}_system`,
        category: params.category,
    });
