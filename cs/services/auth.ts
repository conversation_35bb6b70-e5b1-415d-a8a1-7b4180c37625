import { IS_SHOW_CHANGED_ROLE_POPUP_SEARCH_PARAM } from 'components/ChangedRolePopup/constants';
import {
    SESSION_EXPIRED_REDIRECT_LINK,
    WS_KEY_REGISTRATION_ATTEMPTS,
    WS_KEY_REMEMBER_KEYS,
} from 'lib/constants';
import { redirect } from 'lib/navigation';
import { getLocalStorageItem, removeLocalStorageItem } from 'lib/webStorage';

import { Nullable } from '../types/general';
import { RolePermissionsPayload } from '../types/user';

import io from './io';

export interface RegistrationMetaData {
    referrer?: string;
    /**
     * Link after finishing the fist registration step
     */
    redirect?: string;
    /**
     * From what landing (edi, kasa, edo)
     */
    source?: string;
    token?: string;
    /**
     * Enable trial after success sign-up
     */
    isActiveProTrial?: boolean;
    invite_email?: string | null;
}

export interface GoogleAuthOptions extends RegistrationMetaData {
    token: string;
}

export interface MicrosoftAuthOptions extends RegistrationMetaData {
    access_token: string;
    id_token: string;
}

export interface AppleAuthUserPayload {
    email: string;
    name: {
        firstName: string;
        lastName: string;
    };
}

export interface AppleAuthOptions extends RegistrationMetaData {
    code: string;
    user?: AppleAuthUserPayload;
}

async function colbertIdentify() {
    return await io.getAsJson('/internal-api/colbert/identify');
}

interface LoginPayload {
    email: string;
    password: string;
    remember: boolean;
}

async function login(payload: LoginPayload) {
    return await io.post('/auth-api/login', payload, true, {});
}

export interface RegistrationOptions extends RegistrationMetaData {
    email: string;
    password: string;
    isActiveProTrial?: boolean;
}

interface CommonRegistrationPayload
    extends Omit<RegistrationOptions, 'isActiveProTrial'> {
    trial_auto_enable: boolean;
}

async function register({
    isActiveProTrial = false,
    token,
    referrer,
    ...common
}: RegistrationOptions) {
    const payload: CommonRegistrationPayload = {
        ...common,
        trial_auto_enable: isActiveProTrial,
    };

    if (token) {
        payload.token = token;

        return await io.post('/auth-api/registration/token', payload);
    }

    payload.referrer = referrer;

    return await io.post('/auth-api/registration', payload);
}

const LOGOUT_REDIRECT_URL = config.LANDING_URL || '/auth';

const successLogout = (redirectUrl: string) => {
    sessionStorage.clear();
    removeLocalStorageItem(WS_KEY_REMEMBER_KEYS);
    broadcastAuthChannel.logOut();
    redirect(redirectUrl);
};

async function logout({ redirectUrl = LOGOUT_REDIRECT_URL } = {}) {
    const resp = await io.post('/auth-api/logout');
    if (resp.ok) {
        successLogout(redirectUrl);
    }
}

async function logoutAllSessions({ redirectUrl = LOGOUT_REDIRECT_URL } = {}) {
    const resp = await io.post('/auth-api/logout-all-sessions', {});
    if (resp.ok) {
        successLogout(redirectUrl);
    }
}

export interface InviteUserPermissions extends RolePermissionsPayload {
    is_admin: boolean;
}

export interface InviteUserOptionsPayload {
    email: string;
    edrpou: string;
    permissions?: InviteUserPermissions;
    isPermissionChecked?: boolean;
    position?: Nullable<string>;
}

async function inviteUser(payload: InviteUserOptionsPayload) {
    return await io.post('/internal-api/invite', payload);
}

interface RemindPassOptions {
    email: string;
}

async function remindPass(options: RemindPassOptions) {
    return await io.post('/internal-api/profile/password/remind', options);
}

interface RecoverPassOptions {
    password: string;
    token: string;
}

async function recoverPass(options: RecoverPassOptions) {
    const resp = await io.post(
        '/internal-api/profile/password/recover',
        options,
    );
    if (resp.ok) {
        redirect('/auth');
    }
}

interface UpdatePasswordOptions {
    /**
     * In case if password was auto-generated we can update with new one but only once
     */
    new_password: string;
    current_password?: string;
    isAutogeneratedPassword?: boolean;
}

async function updatePass(options: UpdatePasswordOptions) {
    await io.post('/internal-api/profile/password', options);
}

async function activateRole(roleId: string) {
    return await io.post(`/internal-api/roles/${roleId}/activate`);
}

async function verifyPhone2FACode(code: string, trusted: boolean) {
    const response = await io.post(
        '/internal-api/2fa/phone/verify',
        { code, trusted },
        true,
    );
    return response.next_url;
}

async function resendPhone2FACode() {
    await io.post('/internal-api/2fa/phone/resend');
}

async function sendRegistrationAttempts() {
    const attempts = JSON.parse(
        getLocalStorageItem(WS_KEY_REGISTRATION_ATTEMPTS),
    );
    await io.post('/auth-api/registration/info', { attempts });
}

interface ShortRegisterOptions {
    password: string;
    token: string;
}

async function shortRegister(options: ShortRegisterOptions) {
    return await io.post('/auth-api/registration/short', options);
}

type GoogleAuthFlow = 'registration' | 'login';

export interface ThirdPartyAPIAuthResponse {
    next_url: string;
    is_2fa_enabled?: boolean;
    flow: GoogleAuthFlow;
    email: string;
}

export type APIGoogleAuthResponse = ThirdPartyAPIAuthResponse;
export type APIMicrosoftAuthResponse = ThirdPartyAPIAuthResponse;
export type APIAuthAuthResponse = ThirdPartyAPIAuthResponse;

export interface ThirdPartyAuthResponse {
    nextUrl: string;
    is2FAEnabled: boolean;
    flow: GoogleAuthFlow;
    email: string;
}

export type GoogleAuthResponse = ThirdPartyAuthResponse;
export type MicrosoftAuthResponse = ThirdPartyAuthResponse;
export type AppleAuthResponse = ThirdPartyAuthResponse;

async function googleAuth(
    options: GoogleAuthOptions,
): Promise<GoogleAuthResponse> {
    const response: APIGoogleAuthResponse = await io.post(
        '/auth-api/providers/google',
        options,
        true,
    );
    return {
        nextUrl: response.next_url,
        is2FAEnabled: Boolean(response.is_2fa_enabled),
        flow: response.flow,
        email: response.email,
    };
}

async function microsoftAuth(
    options: MicrosoftAuthOptions,
): Promise<MicrosoftAuthResponse> {
    const response: APIMicrosoftAuthResponse = await io.post(
        '/auth-api/providers/microsoft',
        options,
        true,
    );

    return {
        nextUrl: response.next_url,
        is2FAEnabled: Boolean(response.is_2fa_enabled),
        flow: response.flow,
        email: response.email,
    };
}

export const appleAuth = async (
    options: AppleAuthOptions,
): Promise<AppleAuthResponse> => {
    const response: APIAuthAuthResponse = await io.post(
        '/auth-api/providers/apple',
        options,
        true,
    );

    return {
        nextUrl: response.next_url,
        is2FAEnabled: Boolean(response.is_2fa_enabled),
        flow: response.flow,
        email: response.email,
    };
};

async function checkUserIsRegistered(email: string): Promise<void> {
    const formattedEmail = encodeURIComponent(email);
    return await io.get(
        `/internal-api/registration/check-email?email=${formattedEmail}`,
    );
}

async function changeRegistrationEmail(
    email: string,
    redirectLink: string,
): Promise<void> {
    return await io.patch('/auth-api/registration/change-email', {
        email,
        redirect: redirectLink,
    });
}

async function getHiddenPhone(): Promise<{ phone: string }> {
    return await io.getAsJson('/internal-api/2fa/phone/hidden-phone');
}

async function getHiddenEmail(): Promise<{ email: string }> {
    return await io.getAsJson('/internal-api/2fa/email/hidden-email');
}

interface BroadcastAuthMessageProps {
    action: 'logIn' | 'logOut' | 'sessionExpired' | 'changeRole';
    currentUserID: Nullable<string>;
}

export class BroadcastAuthChannel {
    private broadcastChannel = new BroadcastChannel('AUTH');
    private static instance: Nullable<BroadcastAuthChannel> = null;

    public currentUserID: Nullable<string> = null;

    constructor() {
        if (!BroadcastAuthChannel.instance) {
            BroadcastAuthChannel.instance = this;

            this.broadcastChannel.addEventListener(
                'message',
                (event: MessageEvent<BroadcastAuthMessageProps>) => {
                    const { data } = event;

                    const isSettingsPage = window.location.pathname.includes(
                        '/app/settings/companies/',
                    );

                    if (data.action === 'changeRole' && isSettingsPage) {
                        const searchParams = new URLSearchParams(
                            window.location.search,
                        );
                        searchParams.set(
                            IS_SHOW_CHANGED_ROLE_POPUP_SEARCH_PARAM,
                            'true',
                        );
                        window.location.search = searchParams.toString();

                        return;
                    }

                    if (
                        data.action === 'logIn' &&
                        data.currentUserID !== this.currentUserID
                    ) {
                        window.location.reload();
                        return;
                    }

                    if (data.action === 'logOut') {
                        window.location.reload();
                        return;
                    }

                    if (data.action === 'sessionExpired') {
                        window.location.assign(SESSION_EXPIRED_REDIRECT_LINK);
                        return;
                    }
                },
            );
        }

        return BroadcastAuthChannel.instance;
    }

    logIn(userID: string) {
        this.currentUserID = userID;
        this.broadcastChannel.postMessage({
            action: 'logIn',
            currentUserID: userID,
        });
    }

    changeRole() {
        this.broadcastChannel.postMessage({
            action: 'changeRole',
        });
    }

    logOut() {
        this.currentUserID = null;
        this.broadcastChannel.postMessage({
            action: 'logOut',
            currentUserID: null,
        });
    }

    sessionExpired() {
        this.currentUserID = null;
        this.broadcastChannel.postMessage({
            action: 'sessionExpired',
            currentUserID: null,
        });
    }
}

export const broadcastAuthChannel = new BroadcastAuthChannel();

export const logoutSession = async ({
    sessionId,
    userId = null, // передаємо id користувача, якщо потрібно завершити сесію конкретного користувача (параметр для адміна)
}: {
    sessionId: string;
    userId?: Nullable<string>;
}) =>
    await io.post('/auth-api/logout-session', {
        session_id: sessionId,
        user_id: userId,
    });

export const logoutSessions = async ({
    userId = null, // передаємо id користувача, якщо потрібно завершити сесії конкретного користувача (параметр для адміна)
    withMobile = false, // чи потрібно завершувати сесії на мобільних пристроях
    logoutCurrent = false, // чи потрібно завершувати поточну сесію
}: {
    userId: Nullable<string>;
    withMobile?: Nullable<boolean>;
    logoutCurrent?: boolean;
}) =>
    await io.post('/auth-api/logout-all-sessions', {
        with_mobile: withMobile,
        terminate_current_session: logoutCurrent,
        user_id: userId,
    });

//TODO: add TS types backend responses

export const validateUserEmailChange = async (newEmail: string) =>
    await io.post('/internal-api/profile/email-change/validate-start', {
        new_email: newEmail,
    });

export const startUserEmailChange = async (newEmail: string) =>
    await io.post('/internal-api/profile/email-change/start', {
        new_email: newEmail,
    });

export const verifyPasswordForEmailChange = async (password: string) =>
    await io.post('/internal-api/profile/email-change/verify-password', {
        password,
    });

export const verifyEmailForEmailChange = async (jwtToken: string) =>
    await io.post('/internal-api/profile/email-change/verify-email', {
        token: jwtToken,
    });

export const verify2FAForEmailChange = async (code: string) =>
    await io.post('/internal-api/profile/email-change/verify-2fa', {
        code,
    });

export const resendVerify2FAEmailChange = async () =>
    await io.post('/internal-api/profile/email-change/resend-2fa');

export const resendVerifyEmailChange = async () =>
    await io.post('/internal-api/profile/email-change/resend-verify-email');

export const resendConfirmEmailChange = async () =>
    await io.post('/internal-api/profile/email-change/resend-confirm');

export const confirmUserEmailChange = async (jwtToken: string) =>
    await io.post('/internal-api/profile/email-change/confirm', {
        token: jwtToken,
    });

export const cancelUserEmailChange = async () =>
    await io.post('/internal-api/profile/email-change/cancel');

export const updateContactsAfterEmailChange = async () =>
    await io.post('/internal-api/profile/email-change/update-contacts');

export const checkContactExistOnUserEmailChange = async () =>
    await io.post('/internal-api/profile/email-change/check-contacts');

/**
 * This method sends a verification code to the phone number provided
 * for authentication or registration.
 */
async function sendPhoneAuthCode(options: {
    phone: string;
    method: 'sms' | 'cascade';
}): Promise<void> {
    await io.post('/auth-api/phone-auth/send-code', options, false);
}

/**
 * This method verifies the code sent to the phone number and authenticates the user.
 * It will either log in an existing user or register a new one if the phone number
 * is not yet associated with an account.
 */
async function processPhoneAuthCode(options: {
    phone: string;
    code: string;
}): Promise<{
    nextUrl: string;
    flow: 'registration' | 'login';
    is2FAEnabled: boolean;
}> {
    const response = await io.post(
        '/auth-api/phone-auth/process-code',
        options,
        true,
    );
    return {
        nextUrl: response.next_url,
        flow: response.flow,
        is2FAEnabled: Boolean(response.is_2fa_enabled),
    };
}

/**
 * Set 2FA phone as phone for primary authentication
 */
export async function updatePhoneAuth(options: {
    enable: boolean;
}): Promise<void> {
    await io.post('/internal-api/profile/phone-auth', {
        enable: options.enable,
    });
}

/**
 * Метод який очікує пароль до акаунту, коли пошта виступає як другий фактор автентифікації
 */
async function verifyEmail2FA(options: {
    password: string;
    trusted: boolean;
}): Promise<{
    nextUrl: string;
}> {
    const response = await io.post(
        '/internal-api/2fa/email/verify',
        options,
        true,
    );
    return {
        nextUrl: response.next_url,
    };
}

/**
 * У випадках, коли другим фактором автентифікації виступає електронна пошта,
 * то ми зазвичай просимо вказати пароль, але якщо у користувача немає пароля
 * або він його забув, то альтернативно ми пропонуємо надіслати посилання на пошту
 * для підтвердження по посиланню
 */
async function sendEmail2FAToken() {
    await io.post('/internal-api/2fa/email/send-token');
}

export type VerifyEmail2FATokenResponse = {
    nextUrl: string;
};

async function verifyEmail2FAToken(options: {
    token: string;
}): Promise<VerifyEmail2FATokenResponse> {
    const response = await io.post(
        '/internal-api/2fa/email/verify-token',
        options,
        true,
    );

    return {
        nextUrl: response.next_url,
    };
}

export default {
    activateRole,
    colbertIdentify,
    inviteUser,
    login,
    logout,
    logoutAllSessions,
    recoverPass,
    register,
    remindPass,
    resendPhone2FACode,
    updatePass,
    verifyPhone2FACode,
    verifyEmail2FA,
    sendRegistrationAttempts,
    shortRegister,
    googleAuth,
    microsoftAuth,
    appleAuth,
    sendPhoneAuthCode,
    processPhoneAuthCode,
    checkUserIsRegistered,
    changeRegistrationEmail,
    getHiddenPhone,
    getHiddenEmail,
    logoutSession,
    startUserEmailChange,
    updatePhoneAuth,
    sendEmail2FAToken,
    verifyEmail2FAToken,
};
