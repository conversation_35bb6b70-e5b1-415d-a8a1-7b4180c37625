import { queryClient } from 'lib/queries';
import { GET_COMPANY_USED_DOCS_COUNT } from 'lib/queriesConstants';
import graph from 'services/graph';
import io from 'services/io';

import { IRole } from '../../types/user';

export const updateCompanySecurityPermissions = async (data: {
    email_domains?: Nullable<string[]>;
    allowed_ips?: Nullable<string[]>;
    allowed_api_ips?: Nullable<string[]>;
    inactivity_timeout?: Nullable<number>;
}) => {
    return await io.request(
        'PATCH',
        `/internal-api/companies/security/permissions`,
        data,
        false,
    );
};

export interface UpdateCompanyProfilePayload {
    company_name?: string;
    first_name?: string;
    second_name?: string;
    last_name?: string;
    user_id?: string; // Тільки для адміністраторів
}

export async function updateProfile({
    company_name = '',
    first_name = '',
    second_name,
    last_name = '',
    user_id,
}: UpdateCompanyProfilePayload) {
    const payload = {
        first_name,
        last_name,
        ...(typeof second_name === 'string' && { second_name }),
        ...(user_id && { user_id }),
        ...(company_name && { company_name }),
    };

    return await io.post(
        '/internal-api/profile',
        payload,
        false,
        undefined,
        true,
    );
}

export const getCurrentCompanyRoleById = async (id: string) => {
    const { currentCompanyRoles } = await graph.query({
        query: /* GraphQL */ `
            query CurrentCompanyRoles($id: String!) {
                currentCompanyRoles(id: $id) {
                    id
                    user {
                        id
                        sessions {
                            id
                            ip
                            accessedAt
                            browser
                            browserVersion
                            device
                            os
                            osVersion
                            country
                            city
                            isCurrent
                        }
                    }
                }
            }
        `,
        variables: { id },
    });

    return currentCompanyRoles?.[0];
};

export interface UpdateRolePayload extends Partial<IRole> {}

export const updateRole = async (
    roleId: string,
    data: UpdateRolePayload,
): Promise<void> => {
    return await io.request('PATCH', `/internal-api/roles/${roleId}`, data);
};

export const getCurrentCompanyUsedDocumentCount = async () => {
    try {
        const {
            currentRole: {
                company: { usedDocumentCount },
            },
        } = await queryClient.fetchQuery({
            queryKey: [GET_COMPANY_USED_DOCS_COUNT],
            queryFn: async () =>
                await graph.get(/* GraphQL */ `
                    {
                        currentRole {
                            company {
                                usedDocumentCount
                            }
                        }
                    }
                `),
        });

        return usedDocumentCount as number;
    } catch (error) {
        return null;
    }
};

/**
 * Метод для зміни номера телефону користувача. Вимагає введення пароля та OTP коду.
 */
export async function changeUserPhone(params: {
    phone: string;
    password?: string;
    code?: string;
}): Promise<void> {
    await io.post('/internal-api/profile/phone/update', {
        phone: params.phone,
        // Пароль і OTP код обовʼязкові тільки коли змінюємо верифікований номер телефону.
        password: params.password,
        code: params.code,
    });
}

export async function sendVerifyUserPhone(params: {
    phone: string;
    password?: string;
}): Promise<void> {
    await io.post('/internal-api/profile/phone/send', {
        phone: params.phone,
        // Пароль обовʼязковий тільки коли змінюємо верифікований номер телефону.
        password: params.password,
    });
}

/**
 * Цей метод очікує OTP код, який ми надіслали СМС-кою на номер телефону користувача.
 */
export async function verifyUserPhone(code: string): Promise<void> {
    await io.post('/internal-api/profile/phone/verify', {
        code: code,
    });
}

export const sendFreeBannersEvents = async (
    eventType: 'banner_free_update_view' | 'banner_end_trial_view',
) => {
    await io.post('/internal-api/view-free-rate-banner', {
        event_type: eventType,
    });
};
