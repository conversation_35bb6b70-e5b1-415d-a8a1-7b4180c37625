import merge from 'lodash/merge';
import { prepareSignSessionOptions } from 'services/sign-session';

import io from './io';

const ENSURE_NUMBER_ARGUMENTS = [
    'folderId',
    'limit',
    'offset',
    'statusId',
    'statusIds',
];

export const USER_FRAGMENT = `
    id
    email
    phone
    authPhone
    language

    firstName
    secondName
    lastName
    dateCreated

    source
    emailConfirmed
    registrationCompleted
    registrationMethod
    isAutogeneratedPassword
    isPhoneVerified
    is2FAEnabledInProfile
    is2FAEnabledByRule
    isSubscribedEsputnik
    showKEPAppPopup
    createdBy
    trialAutoEnable

    userMeta {
        mobileUsage
        hasMobileApp
        hasActiveMobileApp
    }

    hasPassword
    activeSurveys
    isReadyForNextCsat
    csatSurveys {
        id
        type
        dateCreated
        estimate
    }
`;

export const ROLE_FRAGMENT = `
    id
    userRole

    canViewDocument
    canCommentDocument
    canUploadDocument
    canDownloadDocument
    canPrintDocument
    canDeleteDocument
    canSignAndRejectDocument
    canSignAndRejectDocumentExternal
    canSignAndRejectDocumentInternal
    canInviteCoworkers
    canEditCompany
    canEditRoles
    canCreateTags
    canEditDirectories
    canEditDocumentTemplates
    canEditDocumentFields
    canDeleteArchivedDocuments
    canArchiveDocuments
    canEditTemplates
    canRemoveItselfFromApproval
    canEditDocumentCategory
    canViewCoworkers
    canDownloadActions
    canEditCompanyContact
    canEditSecurity
    canChangeDocumentSignersAndReviewers
    canDeleteDocumentExtended
    canEditRequiredFields
    canViewPrivateDocument

    canReceiveInbox
    canReceiveInboxAsDefault
    canReceiveComments
    canReceiveRejects
    canReceiveReminders
    canReceiveReviews
    canReceiveNotifications
    canReceiveAccessToDoc
    canReceiveDeleteRequests
    canReceiveFinishedDocs
    canReceiveReviewProcessFinished
    canReceiveReviewProcessFinishedAssigner
    canReceiveSignProcessFinished
    canReceiveSignProcessFinishedAssigner

    canReceiveNewRoles
    canReceiveTokenExpiration
    canReceiveAdminRoleDeletion
    canReceiveEmailChange

    hasSignedDocuments
    showChildDocuments

    hasFewSignatures

    isAdmin
    isMasterAdmin

    canViewClientData
    canEditClientData
    canEditSpecialFeatures

    position
`;

export const EMPLOYEE_ROLE_FRAGMENT = `
    ${ROLE_FRAGMENT}
    allowedIps
    allowedApiIps
    status
    hasToken
    isDefaultRecipient
    dateDeleted
    deletedBy
    dateCreated
    dateInvited
    invitedBy
    activatedBy
    activationSource
    dateActivated
`;

export const BILLING_FRAGMENT = `
    billingAccounts {
        dateDeleted
        pricePerUser
        type
        rate
        status
        unitsLeft
        units
    }
`;

export const BILLING_COMPANY_CONFIG_FRAGMENT = `
    billingCompanyConfig {
        maxArchiveDocumentsCount
        maxAdditionalFieldsCount
        maxEmployeesCount
        maxDocumentsCount
        maxTagsCount
        maxTemplatesCount
        maxRequiredFieldsCount
        maxVersionsCount
        maxVisibleDocumentsCount
        apiEnabled
        canEnforce2FA
        externalCommentsEnabled
        internalCommentsEnabled
        internalDocumentsEnabled
        canManageEmployeeAccess
        reviewsEnabled
    }
`;

const RATE_FIELDS = `
        id
        rate
        status
        startDate
        endDate
        amount
        config
        billNumber
        units
        unitsLeft
`;

export const RATES_FRAGMENT = `
    rates {
        ${RATE_FIELDS}
    }
`;

const BILL_FIELDS = `
        id
        date_created
        status_id
        rate
        count_documents
        number
        amount
        payment_status
        services {
            type
            rate
        }
        servicesType
`;

export const BILLS_FRAGMENT = `
    bills {
        ${BILL_FIELDS}
    }
`;

export const TRIAL_RATES_FRAGMENT = `
    trialRates {
        ${RATE_FIELDS}
        source
    }
`;

export function addSlashes(value) {
    return value.replace(/[\\"]/g, '\\$&').replace(/\u0000/g, '\\0');
}

export function formatArgument(arg, value) {
    const type = typeof value;
    if (['boolean', 'number'].indexOf(type) !== -1) {
        return value.toString();
    }

    if (Array.isArray(value)) {
        let arr = value;
        if (ENSURE_NUMBER_ARGUMENTS.indexOf(arg) !== -1) {
            arr = value.map((item) => parseInt(item, 10));
        }
        return JSON.stringify(arr);
    }

    // Special case to not convert integer fields to string even they passed
    // as string to arguments
    if (ENSURE_NUMBER_ARGUMENTS.indexOf(arg) !== -1) {
        return value;
    }

    return `"${addSlashes(value)}"`;
}

export function formatArguments(args) {
    // TODO: Provide less uglier hack for passing array of GET params to backend
    if (Array.isArray(args.statusId)) {
        args.statusIds = args.statusId;
        delete args.statusId;
    }
    if (Array.isArray(args.condition)) {
        args.conditions = args.condition;
        delete args.condition;
    }
    if (Array.isArray(args.tag)) {
        args.tags = args.tag;
        delete args.tag;
    }

    if (args.search === '') {
        delete args.search;
    }

    const result = Object.keys(args)
        .filter((key) => args[key] !== undefined && args[key] !== null)
        .map((key) => `${key}: ${formatArgument(key, args[key])}`)
        .join(', ');
    return result ? `(${result})` : result;
}

export async function get(query, origin = '', requestOptions = {}) {
    origin = origin || location.origin || '';
    const response = await io.post(
        `${origin}/internal-api/graphql`,
        { query },
        true,
        requestOptions,
    );
    return response.data;
}

export async function graphQuery({ query, variables, options = {} }) {
    const response = await io.post(
        `${location.origin}/internal-api/graphql`,
        { query, variables },
        true,
        merge(prepareSignSessionOptions(), options),
    );
    return response.data;
}

export default {
    EMPLOYEE_ROLE_FRAGMENT,
    ROLE_FRAGMENT,
    USER_FRAGMENT,
    formatArguments,
    get,
    query: graphQuery,
};
