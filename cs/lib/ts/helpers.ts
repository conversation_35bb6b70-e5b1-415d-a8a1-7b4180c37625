import I from 'immutable';
import { formatFullName } from 'lib/helpers';
import camelCase from 'lodash/camelCase';
import { Signature, Signer } from 'services/documents/ts/types';
import { RoleStatuses } from 'services/enums';

import { IRole } from '../../types/user';

import { EDRPOU_PATTERN } from '../constants';

interface PasswordRules {
    minLength: number;
}

export const checkIsValidEdrpou = (edrpouValue: string) =>
    !!edrpouValue && EDRPOU_PATTERN.test(edrpouValue.trim());

export const getSignerGroupBySignatureRoleId = (
    signers?: Signer[],
    signature?: Signature,
) =>
    signers?.find((signer) => signer.groupSignerId === signature?.roleId) ||
    null;

/*
 * This function is used to remove duplicates from an array of objects by a specific key
 * @param arr - array of objects
 * @param predicate - key to remove duplicates by
 * @returns - array of objects without duplicates
 */
export function uniqBy<T>(arr: T[], predicate: (item: T) => string): T[] {
    const seen = new Set<string>();
    return arr.filter((item) => {
        const key = predicate(item);
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}

export const convertToCamelCase = (
    obj: Nullable<Record<string, any> | string>,
) => {
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }

    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const camelKey = camelCase(key);

            if (
                typeof obj[key] === 'object' &&
                obj[key] !== null &&
                !Array.isArray(obj[key])
            ) {
                obj[camelKey] = convertToCamelCase(obj[key]);
            } else {
                obj[camelKey] = obj[key];
            }

            if (camelKey !== key) {
                delete obj[key];
            }
        }
    }

    return obj;
};

export const extractNumbers = (input: unknown) => {
    if (typeof input === 'number') {
        return String(input);
    }

    if (typeof input === 'string') {
        return input?.replace(/\D/g, ''); // Remove all non-digit characters
    }

    return null;
};

export const getIsArchivePageUtility = (pathname?: string) =>
    pathname?.includes('/archive') && !pathname?.includes('/archive-preview');

const customDomainPasswordRules: Record<string, PasswordRules> = {
    'ua.nestle.com': {
        minLength: 16,
    },
    'ua.bosch.com': {
        minLength: 12,
    },
    'boschrexroth.com.ua': {
        minLength: 12,
    },
    'bosch.com': {
        minLength: 12,
    },
};

const defaultPasswordRules: PasswordRules = {
    minLength: 8,
};

export const getPasswordRuleByEmail = (
    email: string | undefined | null = '',
): PasswordRules => {
    if (!email) {
        return defaultPasswordRules;
    }

    const key = Object.keys(customDomainPasswordRules).find((domain) =>
        email.endsWith(domain),
    );

    return key ? customDomainPasswordRules[key] : defaultPasswordRules;
};

/**
 * Сортуємо ролей спочатку по імені користувача. Якщо імена однакові,
 * то сортуємо по логіну (email або телефон для входу).
 */
export function sortRoles<
    T extends {
        user: {
            email?: string;
            authPhone?: string;
            firstName?: string;
            lastName?: string;
        };
    }
>(a: T, b: T): number {
    const name1 = formatFullName(a.user)?.toLowerCase() ?? '';
    const name2 = formatFullName(b.user)?.toLowerCase() ?? '';

    // "0" означає, що імена однакові
    const nameComparison = name1.localeCompare(name2, 'ua');
    if (nameComparison !== 0) {
        return nameComparison;
    }

    const login1 = a.user.email?.toLowerCase() ?? a.user.authPhone ?? '';
    const login2 = b.user.email?.toLowerCase() ?? b.user.authPhone ?? '';

    return login1.localeCompare(login2);
}

/**
 * Ця функція повертає ідентифікатор користувача, який можна показати в інтерфейсі.
 *
 * Цей ідентифікатор не для щоб посилатися на користувача в запитах до API, для цього
 * краще використовувати `user.id`.
 */
export function formatUserIdentifier(
    user:
        | {
              email: string | null | undefined;
              authPhone: string | null | undefined;
          }
        | undefined,
    fallback: string = '',
): string {
    return user?.email ?? user?.authPhone ?? fallback;
}

export const getActiveEmployees = (currentCompanyRoles: I.List<IRole>) => {
    return Array.from<IRole>(currentCompanyRoles).filter(
        (item) => item.status === RoleStatuses.ACTIVE,
    );
};
