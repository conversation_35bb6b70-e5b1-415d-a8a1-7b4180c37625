import { differenceInDays } from 'date-fns';
import I from 'immutable';
import moment, {
    DurationInputArg1,
    DurationInputArg2,
    Moment,
    unitOfTime,
} from 'moment';
import { t } from 'ttag';

type ArgumentDateType = Date | Moment | string | null | undefined;

export const isMoment = (date: unknown): date is Moment => {
    return moment.isMoment(date);
};

export const toMoment = (
    date: ArgumentDateType = null,
    format: string = '',
) => {
    const dateMoment = isMoment(date) ? date : moment(date, format);
    return dateMoment.isValid() ? dateMoment : null;
};

export const getNowDate = () => {
    return moment().toDate();
};

export const addDate = (
    date: ArgumentDateType,
    addValue: DurationInputArg1,
    unit: DurationInputArg2 = 'day',
) => {
    const validDate = toMoment(date);
    return validDate ? validDate.clone().add(addValue, unit).toDate() : null;
};

export const subtractDate = (
    date: ArgumentDateType,
    amount: DurationInputArg1,
    unit: DurationInputArg2 = 'day',
) => {
    const validDate = toMoment(date);
    return validDate ? validDate.clone().subtract(amount, unit).toDate() : null;
};

export const isDateBefore = (
    dateToCheck: ArgumentDateType = null,
    targetDate: ArgumentDateType = null,
    unit: unitOfTime.StartOf = 'day',
) => {
    return !dateToCheck || !targetDate
        ? false
        : moment(dateToCheck).isBefore(targetDate, unit);
};

export const getQuarterInfo = (date: ArgumentDateType) => {
    date = toMoment(date);
    if (!date) return null;

    return {
        index: date.quarter(),
        dateStart: date.startOf('quarter').toDate(),
        dateEnd: date.endOf('quarter').toDate(),
    } as const;
};

export const getLastQuarters = (date: ArgumentDateType, count = 0) => {
    date = toMoment(date);
    if (!date) return null;

    // TODO: remove @ts-ignore
    // @ts-ignore
    return new I.Range(0, count)
        .map((idx: number) => {
            const currentDate = addDate(date, -idx, 'quarter');
            const { index, dateStart, dateEnd } = getQuarterInfo(currentDate)!;
            const year = toMoment(currentDate)?.year();
            return {
                dateStart,
                dateEnd,
                value: `last_quarter_${idx + 1}`,
                label: t`${index}й квартал ${year}`,
            };
        })
        .toArray();
};

export const formatDate = (date: ArgumentDateType, format = 'DD.MM.YY') => {
    const validDate = toMoment(date);
    if (!validDate) return null;

    return validDate.format(format);
};

export const isSame = (
    date1: ArgumentDateType,
    date2: ArgumentDateType,
    unit: unitOfTime.StartOf = 'day',
) => {
    if (!date1 || !date2) {
        return false;
    }

    const validDate1 = toMoment(date1);
    const validDate2 = toMoment(date2);

    return validDate1 && validDate2
        ? validDate1.isSame(validDate2, unit)
        : false;
};

export const isToday = (date: ArgumentDateType) => {
    if (!date) return false;

    return isSame(date, moment(), 'day');
};

export const formatActualDate = (date: ArgumentDateType) => {
    if (isToday(date)) {
        return formatDate(date, 'HH:mm');
    }
    return formatDate(date);
};

export const toDate = (dateString: ArgumentDateType, format?: string) => {
    if (!dateString) return null;

    const validDate = toMoment(dateString, format);
    return validDate ? validDate.toDate() : null;
};

export const toServerDate = (date: Date | unknown) => {
    return date instanceof Date ? date.toISOString() : date;
};

export const toStartOf = (
    date: ArgumentDateType,
    unit: unitOfTime.StartOf = 'day',
) => {
    const validDate = toMoment(date);

    return validDate ? validDate.startOf(unit).toDate() : null;
};

export const getPeriod = (
    periodBegin: ArgumentDateType,
    periodEnd: ArgumentDateType,
    units: unitOfTime.Diff = 'days',
) => {
    const dateFrom = toMoment(periodBegin, 'DD.MM.YY');
    const dateTo = toMoment(periodEnd, 'DD.MM.YY');

    if (!dateFrom || !dateTo) {
        return null;
    }

    return dateTo.diff(dateFrom, units);
};

export const getDifferenceInDays: typeof differenceInDays = (
    dateLeft,
    dateRight,
) => {
    return differenceInDays(dateLeft, dateRight);
};
