/* eslint-disable no-unused-expressions */
import { expect } from 'chai';

import {
    capitalizeFirstLetter,
    concatUnique,
    extractNumbers,
    flattenInnerArray,
    flattenObjectsArray,
    formatError,
    formatFullName,
    getCommonElements,
    getEmailDomain,
    getFileNames,
    getNewOrderType,
    getPKCodeTitle,
    getPKPositionTitle,
    groupByProperty,
    isEmptyObject,
    joinStrings,
    moveArrayItem,
    removeDuplicatesFromArray,
    removeWhitespace,
    sortEmployeesByDateDeleted,
    splitArrayIntoChunks,
    truncateText,
} from '../../helpers';

describe('lib/helpers', () => {
    describe('Truncate text', () => {
        it('should truncate long text', () => {
            const input =
                '1234567890123456789012345678901234567890123456789012345678901234567890';
            const output =
                '12345678901234567890123456789012345678901234567890123456789012...';
            expect(truncateText(input)).to.equal(output);
        });

        it('should truncate text longer than 10 letters', () => {
            const input = '12345678901';
            const output = '1234567890...';
            expect(truncateText(input, 10)).to.equal(output);
        });

        it('should not truncate short text', () => {
            const input = 'foo';
            const output = 'foo';
            expect(truncateText(input)).to.equal(output);
        });
    });

    describe('Remove all whitespace in text', () => {
        it('should remove spaces', () => {
            const input = ' f  oo  ';
            const output = 'foo';
            expect(removeWhitespace(input)).to.equal(output);
        });

        it('should remove tabs', () => {
            const input = '\u0009f\u0009oo\u0009';
            const output = 'foo';
            expect(removeWhitespace(input)).to.equal(output);
        });

        it('should remove whitespace', () => {
            const input = '\u0009 f\u0009o  o  \u0009';
            const output = 'foo';
            expect(removeWhitespace(input)).to.equal(output);
        });

        it('should return empty string', () => {
            const input = ' \u0009   \u0009    \u0009   ';
            const output = '';
            expect(removeWhitespace(input)).to.equal(output);
        });
    });

    describe('Join strings', () => {
        it('should join strings', () => {
            expect(joinStrings(['foo', 'bar', 'buz'])).to.equal('foo bar buz');
            expect(joinStrings(['foo', 'bar', 'buz'], ' - ')).to.equal(
                'foo - bar - buz',
            );
        });

        it('should be empty parts', () => {
            expect(joinStrings(['', 'bar', 'buz'])).to.equal('bar buz');
            expect(joinStrings(['foo', null, 'buz'], '-')).to.equal('foo-buz');
            expect(joinStrings([null, 'bar', undefined])).to.equal('bar');
        });

        it('should be empty string', () => {
            expect(joinStrings([])).to.equal('');
            // @ts-expect-error TS2345 [FIXME] Comment is autogenerated
            expect(joinStrings({})).to.equal('');
            expect(joinStrings()).to.equal('');
        });
    });

    describe('Format full name', () => {
        it('should format full name', () => {
            expect(
                formatFullName({
                    firstName: 'foo',
                    secondName: 'bar',
                    lastName: 'buz',
                }),
            ).to.equal('buz foo bar');
        });

        it('should be empty parts', () => {
            expect(
                formatFullName({
                    firstName: '',
                    secondName: 'bar',
                    lastName: 'buz',
                }),
            ).to.equal('buz bar');
            expect(
                formatFullName({
                    firstName: 'foo',
                    secondName: null,
                    lastName: 'buz',
                }),
            ).to.equal('buz foo');
            expect(
                formatFullName({ firstName: 'foo', lastName: 'buz' }),
            ).to.equal('buz foo');
            expect(
                formatFullName({
                    firstName: null,
                    secondName: 'bar',
                    lastName: undefined,
                }),
            ).to.equal('bar');
        });

        it('should be empty string', () => {
            expect(
                formatFullName({
                    firstName: '',
                    secondName: null,
                    lastName: undefined,
                }),
            ).to.equal('');
            expect(formatFullName({})).to.equal('');
            expect(formatFullName()).to.equal('');
        });
    });

    describe('Get email domain', () => {
        it('should get email domain', () => {
            expect(getEmailDomain('<EMAIL>')).to.equal('bar.com');
        });

        it('should be null', () => {
            expect(getEmailDomain('foo@bar')).to.be.null;
            expect(getEmailDomain('')).to.be.null;
            expect(getEmailDomain(null)).to.be.null;
            expect(getEmailDomain(undefined)).to.be.null;
        });
    });

    describe('Capitalize first letter', () => {
        it('should capitalize first letter', () => {
            expect(capitalizeFirstLetter('foo')).to.equal('Foo');
            expect(capitalizeFirstLetter('Foo')).to.equal('Foo');
            expect(capitalizeFirstLetter('fooBar')).to.equal('FooBar');
        });

        it('should be empty string', () => {
            expect(capitalizeFirstLetter('')).to.equal('');
            expect(capitalizeFirstLetter(null)).to.equal('');
            expect(capitalizeFirstLetter(undefined)).to.equal('');
        });
    });

    describe('Get PK code title', () => {
        it('legal key', () => {
            const keyInfo = {
                caServer: 'АЦСК ПАТ КБ «ПРИВАТБАНК»',
                companyName: 'ТАМАРКА',
                edrpou: '3235608644',
                isLegal: true,
                ownerFullName: 'КУЧУГУРНА ТАМАРА СЕРГІЇВНА',
                serialNumber: '123456789012345678901234567890',
            };
            expect(getPKCodeTitle(keyInfo)).to.equal('ЄДРПОУ/ІПН');
        });

        it('natural key', () => {
            const keyInfo = {
                caServer: 'АЦСК ПАТ КБ «ПРИВАТБАНК»',
                companyName: 'ФІЗИЧНА ОСОБА',
                edrpou: '3235608644',
                isLegal: false,
                ownerFullName: 'КУЧУГУРНА ТАМАРА СЕРГІЇВНА',
                serialNumber: '123456789012345678901234567890',
            };
            expect(getPKCodeTitle(keyInfo)).to.equal('Ідентифікаційний код');
        });
    });

    describe('Get PK position title', () => {
        it('has position', () => {
            const keyInfo = {
                position: 'Test Position',
            };
            expect(getPKPositionTitle(keyInfo)).to.equal('Test Position');
        });

        it('has empty position', () => {
            const keyInfo = {
                position: '',
            };
            expect(getPKPositionTitle(keyInfo)).to.equal('Власник ключа');
        });

        it('has null position', () => {
            const keyInfo = {
                position: null,
            };
            expect(getPKPositionTitle(keyInfo)).to.equal('Власник ключа');
        });

        it('has not position', () => {
            const keyInfo = {};
            expect(getPKPositionTitle(keyInfo)).to.equal('Власник ключа');
        });
    });

    describe('Concatenate arrays and leave only unique elements', () => {
        it('should concatenate arrays with numbers', () => {
            const input1 = [1, 2, 3];
            const input2 = [3, 4, 5];
            const output = [1, 2, 3, 4, 5];
            expect(concatUnique(input1, input2)).to.eql(output);
        });

        it('should concatenate arrays with strings', () => {
            const input1 = ['foo', 'bar'];
            const input2 = ['buz', 'bar', 'foo'];
            const output = ['foo', 'bar', 'buz'];
            expect(concatUnique(input1, input2)).to.eql(output);
        });

        it('should concatenate empty array', () => {
            // @ts-expect-error TS7034 [FIXME] Comment is autogenerated
            const input1 = [];
            // @ts-expect-error TS7034 [FIXME] Comment is autogenerated
            const input2 = [];
            // @ts-expect-error TS7034 [FIXME] Comment is autogenerated
            const output = [];
            // @ts-expect-error TS7005 [FIXME] Comment is autogenerated
            expect(concatUnique(input1, input2)).to.eql(output);
        });
    });

    describe('Flatten inner array by property name', () => {
        it('should flatten inner array with 1 element', () => {
            const input = [
                {
                    name: 'Leia Organa',
                    type: 'Human',
                    appearsIn: [
                        {
                            name: 'NEWHOPE',
                            episode: 1,
                        },
                    ],
                },
            ];
            const output = [
                {
                    name: 'Leia Organa',
                    type: 'Human',
                    appearsIn: {
                        name: 'NEWHOPE',
                        episode: 1,
                    },
                },
            ];
            expect(flattenInnerArray(input, 'appearsIn')).to.eql(output);
        });

        it('should flatten inner array with many elements', () => {
            const input = [
                {
                    name: 'R2-D2',
                    type: 'Droid',
                    appearsIn: [
                        {
                            name: 'NEWHOPE',
                            episode: 1,
                        },
                        {
                            name: 'EMPIRE',
                            episode: 2,
                        },
                        {
                            name: 'JEDI',
                            episode: 3,
                        },
                    ],
                },
            ];
            const output = [
                {
                    name: 'R2-D2',
                    type: 'Droid',
                    appearsIn: {
                        name: 'NEWHOPE',
                        episode: 1,
                    },
                },
                {
                    name: 'R2-D2',
                    type: 'Droid',
                    appearsIn: {
                        name: 'EMPIRE',
                        episode: 2,
                    },
                },
                {
                    name: 'R2-D2',
                    type: 'Droid',
                    appearsIn: {
                        name: 'JEDI',
                        episode: 3,
                    },
                },
            ];
            expect(flattenInnerArray(input, 'appearsIn')).to.eql(output);
        });

        it('should not flatten if no inner array', () => {
            const input = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                },
            ];
            const output = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                },
            ];
            expect(flattenInnerArray(input, 'appearsIn')).to.eql(output);
        });

        it('should not flatten if inner array is empty', () => {
            const input = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                    appearsIn: [],
                },
            ];
            const output = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                    appearsIn: [],
                },
            ];
            expect(flattenInnerArray(input, 'appearsIn')).to.eql(output);
        });

        it('should return chunks by 3 items', () => {
            const input = ['test', 1, 2, 3, 4, 5, 'text', 6, 7, 8, 9];
            const output = [
                ['test', 1, 2],
                [3, 4, 5],
                ['text', 6, 7],
                [8, 9],
            ];
            expect(splitArrayIntoChunks(input, 3)).to.eql(output);
        });

        it('should return array of one array', () => {
            const input = [1];
            const output = [[1]];
            expect(splitArrayIntoChunks(input, 3)).to.eql(output);
        });
    });

    describe('Flatten array of objects deeply by property name', () => {
        it('should flatten inner array with 1 element', () => {
            const input = [
                {
                    name: 'Leia Organa',
                    type: 'Human',
                    appearsIn: [
                        {
                            name: 'NEWHOPE',
                            episode: 1,
                        },
                    ],
                },
            ];
            const output = [
                {
                    name: 'Leia Organa',
                    type: 'Human',
                    appearsIn: [],
                },
                {
                    name: 'NEWHOPE',
                    episode: 1,
                },
            ];
            expect(flattenObjectsArray(input, 'appearsIn')).to.eql(output);
        });

        it('should flatten inner array with many elements', () => {
            const input = [
                {
                    name: 'R2-D2',
                    type: 'Droid',
                    appearsIn: [
                        {
                            name: 'NEWHOPE',
                            episode: 1,
                        },
                        {
                            name: 'EMPIRE',
                            episode: 2,
                        },
                        {
                            name: 'JEDI',
                            episode: 3,
                        },
                    ],
                },
            ];
            const output = [
                {
                    name: 'R2-D2',
                    type: 'Droid',
                    appearsIn: [],
                },
                {
                    name: 'NEWHOPE',
                    episode: 1,
                },
                {
                    name: 'EMPIRE',
                    episode: 2,
                },
                {
                    name: 'JEDI',
                    episode: 3,
                },
            ];
            expect(flattenObjectsArray(input, 'appearsIn')).to.eql(output);
        });

        it('should not flatten if no inner array', () => {
            const input = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                },
            ];
            const output = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                },
            ];
            expect(flattenObjectsArray(input, 'appearsIn')).to.eql(output);
        });

        it('should not flatten if inner array is empty', () => {
            const input = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                    appearsIn: [],
                },
            ];
            const output = [
                {
                    name: 'Han Solo',
                    type: 'Human',
                    appearsIn: [],
                },
            ];
            expect(flattenObjectsArray(input, 'appearsIn')).to.eql(output);
        });
    });

    describe('Is empty object', () => {
        it('should be true for empty object', () => {
            expect(isEmptyObject({})).to.be.true;
        });

        it('should be false for not empty object', () => {
            expect(isEmptyObject({ foo: 'bar' })).to.be.false;
            expect(isEmptyObject(undefined)).to.be.false;
            expect(isEmptyObject(null)).to.be.false;
            expect(isEmptyObject(true)).to.be.false;
            expect(isEmptyObject(false)).to.be.false;
            expect(isEmptyObject(0)).to.be.false;
            expect(isEmptyObject(1234)).to.be.false;
            expect(isEmptyObject('')).to.be.false;
            expect(isEmptyObject('foo')).to.be.false;
            expect(isEmptyObject([])).to.be.false;
            expect(isEmptyObject([1, 2, 3])).to.be.false;
        });
    });

    describe('Format error', () => {
        it('should format error', () => {
            expect(formatError(new Error('Test Error'))).to.eql(
                'Error: Test Error',
            );
        });

        it('should format non-error', () => {
            class Obj {
                // @ts-expect-error TS7006 [FIXME] Comment is autogenerated
                constructor(name) {
                    // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
                    this.name = name;
                }
            }

            expect(formatError(10)).to.eql('10');
            expect(formatError('Text')).to.eql('Text');
            expect(formatError(new Obj('name'))).to.eql('{"name":"name"}');
        });
    });

    describe('Get files list', () => {
        it('empty array', () => {
            expect(getFileNames([])).to.eql('');
        });

        it('one file name', () => {
            expect(getFileNames([{ name: 'first.txt' }])).to.eql('first.txt');
        });

        it('multiple file names', () => {
            expect(
                getFileNames([
                    { name: 'first.txt' },
                    { name: 'second.txt' },
                    { name: 'third.txt' },
                ]),
            ).to.eql('first.txt, second.txt, third.txt');
        });
    });

    describe('groupBy', () => {
        it('empty array', () => {
            // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
            expect(groupByProperty([], 'age')).to.deep.eql({});
        });
        it('array with inconsistent object', () => {
            expect(
                groupByProperty(
                    [
                        { name: 'Kate', age: 18 },
                        { name: 'Mary', age: 18 },
                        { name: 'Pete' },
                        { name: 'John', age: 30 },
                    ],
                    'age',
                ),
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
            ).deep.to.eql({
                18: [
                    { name: 'Kate', age: 18 },
                    { name: 'Mary', age: 18 },
                ],
                30: [{ name: 'John', age: 30 }],
            });
        });
        it('array with no such key', () => {
            expect(
                groupByProperty(
                    [
                        { name: 'Kate' },
                        { name: 'Mary' },
                        { name: 'Pete' },
                        { name: 'John' },
                    ],
                    'age',
                ),
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
            ).deep.to.eql({});
        });
        it('valid array', () => {
            expect(
                groupByProperty(
                    [
                        { name: 'Kate', age: 18 },
                        { name: 'Mary', age: 18 },
                        { name: 'John', age: 30 },
                    ],
                    'age',
                ),
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
            ).to.deep.eql({
                18: [
                    { name: 'Kate', age: 18 },
                    { name: 'Mary', age: 18 },
                ],
                30: [{ name: 'John', age: 30 }],
            });
        });
    });

    describe('Rearrange array item', () => {
        it('empty array', () => {
            expect(moveArrayItem([])).to.eql([]);
        });

        it('rearrange array of objects', () => {
            expect(
                moveArrayItem(
                    [
                        { name: 'first.txt' },
                        { name: 'second.txt' },
                        { name: 'third.txt' },
                    ],
                    2,
                    0,
                ),
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
            ).to.deep.eql([
                { name: 'third.txt' },
                { name: 'first.txt' },
                { name: 'second.txt' },
            ]);
        });

        it('rearrange array of objects', () => {
            expect(
                moveArrayItem(
                    [{ name: 'first.txt' }, { name: 'second.txt' }],
                    0,
                    1,
                ),
                // @ts-expect-error TS2339 [FIXME] Comment is autogenerated
            ).to.deep.eql([{ name: 'second.txt' }, { name: 'first.txt' }]);
        });

        it('get common elements in array', () => {
            const arrays = [
                ['test', 'test12', 'test13'],
                ['test', 'test22', 'test23'],
                ['test', 'test32', 'test33'],
            ];
            expect(getCommonElements(arrays)).to.eql(['test']);
        });

        it('get empty array if no common elements in array', () => {
            const arrays = [
                ['test1', 'test12', 'test13'],
                ['test2', 'test22', 'test23'],
                ['test3', 'test32', 'test33'],
            ];
            expect(getCommonElements(arrays)).to.eql([]);
        });
    });

    describe('getNewOrderType', () => {
        it('should return "asc" when currentOrderType is not provided', () => {
            expect(getNewOrderType()).to.equal('asc');
        });

        it('should return "desc" when currentOrderType is "asc"', () => {
            expect(getNewOrderType('asc')).to.equal('desc');
        });

        it('should return "asc" when currentOrderType is "desc"', () => {
            expect(getNewOrderType('desc')).to.equal('asc');
        });

        it('should return "asc" when currentOrderType is any other string', () => {
            expect(getNewOrderType('random')).to.equal('asc');
        });
    });

    describe('sortEmployeesByDateDeleted', () => {
        const employees = [
            { id: 1, dateDeleted: '2023-04-20' },
            { id: 2, dateDeleted: '2023-04-22' },
            { id: 3, dateDeleted: '2023-04-21' },
        ];

        it('should return the same array if no order is provided', () => {
            const result = sortEmployeesByDateDeleted(employees);
            expect(result).to.deep.equal(employees);
        });

        it('should return a new array and not modify the original array', () => {
            const originalArray = [...employees];
            sortEmployeesByDateDeleted(employees, 'asc');
            expect(employees).to.deep.equal(originalArray);
        });

        it('should sort employees in ascending order by dateDeleted', () => {
            const expectedResult = [
                { id: 1, dateDeleted: '2023-04-20' },
                { id: 3, dateDeleted: '2023-04-21' },
                { id: 2, dateDeleted: '2023-04-22' },
            ];
            const result = sortEmployeesByDateDeleted(employees, 'asc');
            expect(result).to.deep.equal(expectedResult);
        });

        it('should sort employees in descending order by dateDeleted', () => {
            const expectedResult = [
                { id: 2, dateDeleted: '2023-04-22' },
                { id: 3, dateDeleted: '2023-04-21' },
                { id: 1, dateDeleted: '2023-04-20' },
            ];
            const result = sortEmployeesByDateDeleted(employees, 'desc');
            expect(result).to.deep.equal(expectedResult);
        });
    });

    describe('removeDuplicatesFromArray', () => {
        it('should remove duplicates based on the provided fields', () => {
            const targetArray = [
                { id: 1, name: 'item1' },
                { id: 2, name: 'item2' },
                { id: 3, name: 'item3' },
            ];
            const referenceArray = [
                { id: 1, name: 'refItem1' },
                { id: 2, name: 'refItem2' },
            ];

            const result = removeDuplicatesFromArray(
                targetArray,
                referenceArray,
                'id',
                'id',
            );

            expect(result).to.deep.equal([{ id: 3, name: 'item3' }]);
        });

        it('should return the original array if there are no duplicates', () => {
            const targetArray = [
                { id: 1, name: 'item1' },
                { id: 2, name: 'item2' },
                { id: 3, name: 'item3' },
            ];
            const referenceArray = [
                { id: 4, name: 'refItem1' },
                { id: 5, name: 'refItem2' },
            ];

            const result = removeDuplicatesFromArray(
                targetArray,
                referenceArray,
                'id',
                'id',
            );

            expect(result).to.deep.equal(targetArray);
        });

        it('should work with non-id comparison fields', () => {
            const targetArray = [
                { value: 1, name: 'item1' },
                { value: 2, name: 'item2' },
                { value: 3, name: 'item3' },
            ];
            const referenceArray = [
                { value: 1, name: 'refItem1' },
                { value: 2, name: 'refItem2' },
            ];

            const result = removeDuplicatesFromArray(
                targetArray,
                referenceArray,
                'value',
                'value',
            );

            expect(result).to.deep.equal([{ value: 3, name: 'item3' }]);
        });
    });

    describe('extractNumbers', () => {
        it('should extract numbers from a mixed string', () => {
            expect(extractNumbers('abc123xyz456')).to.equal('123456');
        });

        it('should remove all non-numeric characters', () => {
            expect(extractNumbers('Price: $789.00!')).to.equal('78900');
        });

        it('should return an empty string when no numbers are found', () => {
            expect(extractNumbers('No numbers here!')).to.equal('');
        });

        it('should return string with inputs (numbers) for non-string inputs (numbers)', () => {
            expect(extractNumbers(12345)).to.equal('12345');
        });

        it('should return null for non-string inputs (boolean)', () => {
            expect(extractNumbers(true)).to.null;
        });

        it('should return null for non-string inputs (null)', () => {
            expect(extractNumbers(null)).to.null;
        });

        it('should return null for non-string inputs (undefined)', () => {
            expect(extractNumbers(undefined)).to.null;
        });

        it('should return null for non-string inputs (object)', () => {
            expect(extractNumbers({})).to.null;
        });

        it('should return null for non-string inputs (array)', () => {
            expect(extractNumbers(['123'])).to.null;
        });
    });
});
