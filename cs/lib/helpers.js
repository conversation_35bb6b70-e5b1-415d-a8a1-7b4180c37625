import Bowser from 'bowser';
import moment from 'moment';
import { AntivirusStatusTypes } from 'services/antivirus/enums';
import { getLocationSearchArgs } from 'services/documents/api';
import { getIsOneSign } from 'services/documents/utils';
import {
    DocumentReviewType,
    DocumentSort,
    DocumentsFilteredByDeliveredState,
    LogLevel,
    SignatureFormat,
} from 'services/enums';
import { t } from 'ttag';

import { formatUserIdentifier } from './ts/helpers';

import loggerService from '../services/logger';
import {
    ACCEPT_EXTENSIONS,
    ACCEPT_EXTENSIONS_TEXT,
    ACCEPT_SIGNATURE_EXTENSIONS,
    ACCEPT_VERSIONS_EXTENSIONS,
    ACCEPT_VERSIONS_EXTENSIONS_TEXT,
    BROWSERS_MIN_VERSION,
    CONTAINS_NUMBER_PATTERN,
    CONTAINS_SPECIALS_PATTERN,
    EMAIL_PATTERN,
    PHONE_PATTERN,
    PHONE_PREFIX,
    VCHASNO_EDRPOU,
    WS_KEY_REGISTRATION_ATTEMPTS,
    WS_KEY_SORT_DOCUMENTS_BY,
} from './constants';
import { amountValueToPayloadValue } from './numbers';
import { getLocationQuery } from './url';
import { getLocalStorageItem, setLocalStorageItem } from './webStorage';

// String helpers
export function joinStrings(arr = [], separator = ' ') {
    return Array.isArray(arr) ? arr.filter((el) => !!el).join(separator) : '';
}

export function reverseStr(str) {
    return str.split('').reverse().join('');
}

export function capitalizeFirstLetter(text) {
    return text ? `${text.charAt(0).toUpperCase()}${text.slice(1)}` : '';
}

export function formatFullName({ firstName, secondName, lastName } = {}) {
    return joinStrings([lastName, firstName, secondName]);
}

export function getEmailDomain(email) {
    return EMAIL_PATTERN.test(email) ? email.split('@')[1] : null;
}

export function getPKCodeTitle(keyInfo) {
    return keyInfo.isLegal ? t`ЄДРПОУ/ІПН` : t`Ідентифікаційний код`;
}

export function getPKPositionTitle(keyInfo) {
    return keyInfo.position ? keyInfo.position : t`Власник ключа`;
}

export function getSortDate() {
    const query = getLocationQuery(document.location);

    if (query.sort_date) {
        return query.sort_date;
    }

    return (
        getLocalStorageItem(WS_KEY_SORT_DOCUMENTS_BY) ||
        DocumentSort.DATE_LISTING
    );
}

export function removeWhitespace(text) {
    return text.replace(/\s/g, '');
}

export function stringToUint8Array(value) {
    const arr = new Uint8Array(value.length);
    value.split('').forEach((item, idx) => {
        arr[idx] = value.charCodeAt(idx);
    });
    return arr;
}

export function truncateText(text, length = 62) {
    return text && text.length > length
        ? `${text.substring(0, length)}...`
        : text;
}

// Array helpers
export function concatUnique(array1, array2) {
    return Array.from(new Set(array1.concat(array2)));
}

export function flattenInnerArray(array, propName) {
    const result = [];

    function saveItem(item) {
        if (item[propName] && item[propName].length > 0) {
            item[propName].forEach((innerItem) =>
                saveItem({
                    ...item,
                    [propName]: { ...innerItem },
                }),
            );
        } else {
            result.push(item);
        }
    }

    array.forEach((item) => saveItem(item));
    return result;
}

export function flattenObjectsArray(array, propName) {
    return array.reduce((arr, item) => {
        arr = arr.concat(item);
        if (item[propName]) {
            arr = arr.concat(flattenObjectsArray(item[propName]));
            item[propName] = [];
        }
        return arr;
    }, []);
}

export function moveArrayItem(arr, from, to) {
    if (arr.length) {
        const rearrangedArray = [...arr];
        const currentItem = rearrangedArray[from];
        rearrangedArray.splice(from, 1);
        rearrangedArray.splice(to, 0, currentItem);
        return rearrangedArray;
    }
    return [];
}

export function splitArrayIntoChunks(arr, chunkSize = 100) {
    const chunks = [];
    if (arr.length > chunkSize) {
        for (let i = 0; i < arr.length; i += chunkSize) {
            chunks.push(arr.slice(i, i + chunkSize));
        }
    } else {
        chunks.push(arr);
    }
    return chunks;
}

// Object helpers
export function isEmptyObject(obj) {
    return obj
        ? Object.keys(obj).length === 0 && obj.constructor === Object
        : false;
}

// Error helpers
export function createWarningError(message) {
    const err = new Error(message);
    err.level = LogLevel.WARNING;
    return err;
}

export function formatError(err) {
    let errStr = err && err.toString ? err.toString() : `${err}`;
    if (errStr === '[object Object]')
        errStr = err.message ? err.message : JSON.stringify(err);
    return errStr;
}

export function logErrorByLevel(errorLabel, ctx, level = LogLevel.ERROR) {
    if (level === LogLevel.WARNING) {
        loggerService.log(errorLabel, ctx);
    } else {
        loggerService.error(errorLabel, ctx);
    }
}

// Files
export function saveBlobAs(blob, filename) {
    if (window.navigator.msSaveOrOpenBlob) {
        // For IE
        window.navigator.msSaveBlob(blob, filename);
    } else {
        const a = window.document.createElement('a');
        a.href = window.URL.createObjectURL(blob, { type: 'text/plain' });
        a.download = filename;
        document.body.appendChild(a);
        a.click(); // IE 10 treats blob URL as cross-origin and denies access
        document.body.removeChild(a);
    }
}

// DOM
export function moveCursorToInputEnd(el) {
    if (el.setSelectionRange) {
        // Double the length for Opera, because it can return two value per character
        const length = el.value.length * 2;
        setTimeout(() => {
            el.setSelectionRange(length, length);
        }, 1);
    } else {
        // As a fallback, replace the contents with itself
        el.value = el.value;
    }
}

// Browser
export function isBrowserSupported() {
    const bowser = Bowser.getParser(window.navigator.userAgent);
    return bowser.satisfies(BROWSERS_MIN_VERSION);
}

export const getBrowserInfo = () => {
    const bowser = Bowser.getParser(window.navigator.userAgent);

    return {
        name: bowser.getBrowserName(),
        version: bowser.getBrowserVersion(),
    };
};

// Other
// Here you need to add each new filter !
export function getDocumentsGraphArguments(location) {
    const query = getLocationQuery(location);
    return {
        ...getLocationSearchArgs(query),
        folderId: query.folder_id,
        statusId: query.status_id,
        isOneSign: getIsOneSign(query),
        dateFrom: query.date_from,
        dateTo: query.date_to,
        amountGte: query.amount_gte
            ? amountValueToPayloadValue(query.amount_gte)
            : null,
        amountLte: query.amount_lte
            ? amountValueToPayloadValue(query.amount_lte)
            : null,
        tag: query.tag,
        hasComments: query.has_comments === 'true' ? true : undefined,
        isWaitMySign: query.wait_my_sign === 'true' ? true : undefined,
        conditions2: query.conditions2,
        reviewFolder: query.review_folder,
        withoutTags:
            query.without_tags === 'true' && !query.tag ? true : undefined,
        hasDateDelivered: query.has_date_delivered
            ? DocumentsFilteredByDeliveredState.IS_NOT_DELIVERED
            : DocumentsFilteredByDeliveredState.ALL,
    };
}

export function isClientRendering() {
    return typeof window !== 'undefined';
}

export function isEmptyList(query) {
    const queryFiltered = { ...query };
    delete queryFiltered.after_registration;
    return Object.keys(queryFiltered).length === 0;
}

export function isIOSDevice() {
    if (isClientRendering()) {
        return (
            !!navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)
        );
    }
    return false;
}

export function isUrl2PdfViewer() {
    if (window.navigator.userAgent.includes('HeadlessChrome')) {
        return true;
    }
    return false;
}

export function isVisibleElement(el) {
    let isVisible = true;
    if (el && typeof el.getClientRects === 'function') {
        isVisible = !!(
            el.offsetWidth ||
            el.offsetHeight ||
            el.getClientRects().length
        );
    }
    return isVisible;
}

export function isWorkersSupport() {
    return (window.URL || window.webkitURL) && window.Blob && window.Worker;
}

export function getFileNames(files) {
    return Array.from(files)
        .map((item) => item.name)
        .join(', ');
}

export function convertFileToDataUrl(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            resolve(reader.result);
        };
        reader.onerror = reject;
    });
}

export function convertDataUrlToFile(dataUrl) {
    const splitedData = dataUrl.split(',');
    const mime = splitedData[0].match(/:(.*?);/)[1];
    const bstr = atob(splitedData[1]);
    let n = bstr.length;
    const u8array = new Uint8Array(n);
    while (n--) {
        u8array[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8array], { type: mime });
}

export function groupByProperty(array, property) {
    return array.reduce((groups, item) => {
        const value = item[property];
        if (value) {
            groups[value] = groups[value] || [];
            groups[value].push(item);
        }
        return groups;
    }, {});
}

export function getCommonElements(arrays) {
    // Assumes that we are dealing with an array of arrays of integers
    let currentValues = {};
    let commonValues = {};
    for (let i = arrays[0].length - 1; i >= 0; i--) {
        // Iterating backwards for efficiency
        currentValues[arrays[0][i]] = 1; // Doesn't really matter what we set it to
    }
    for (let i = arrays.length - 1; i > 0; i--) {
        const currentArray = arrays[i];

        for (let j = currentArray.length - 1; j >= 0; j--) {
            if (currentArray[j] in currentValues) {
                commonValues[currentArray[j]] = 1; // Once again, the `1` doesn't matter
            }
        }
        currentValues = commonValues;
        commonValues = {};
    }
    return Object.keys(currentValues);
}

export function isValidPhone(phone, isPrefixValid) {
    if (isPrefixValid && phone === PHONE_PREFIX) {
        return true;
    }
    return phone ? PHONE_PATTERN.test(phone) : true;
}

export function getValidPhone(phone) {
    if (phone === PHONE_PREFIX) {
        return null;
    }
    return phone || null;
}

export function validatePassword(password, options = {}) {
    const minLength = options?.minLength || 8;
    const isValidLength = password.length >= minLength;
    const hasLetters = password.toLowerCase() !== password.toUpperCase();
    const hasNumber = CONTAINS_NUMBER_PATTERN.test(password);
    const hasSpecial = CONTAINS_SPECIALS_PATTERN.test(password);
    if (!isValidLength && !hasNumber && !hasSpecial) {
        return t`Має містити щонайменше ${minLength} символів, хоча б одну цифру і один спеціальний символ (!"#$%*-/:;=?)`;
    } else if (!isValidLength && hasNumber && hasSpecial) {
        return t`Має містити щонайменше ${minLength} символів`;
    } else if (!isValidLength && !hasNumber && hasSpecial) {
        return t`Має містити щонайменше ${minLength} символів і хоча б одну цифру`;
    } else if (!isValidLength && hasNumber && !hasSpecial) {
        return t`Має містити щонайменше ${minLength} символів і один спеціальний символ (!"#$%*-/:;=?)`;
    } else if (isValidLength && !hasNumber && !hasSpecial) {
        return t`Має містити хоча б одну цифру і один спеціальний символ (!"#$%*-/:;=?)`;
    } else if (isValidLength && !hasNumber && hasSpecial) {
        return t`Має містити хоча б одну цифру`;
    } else if (isValidLength && hasNumber && !hasSpecial) {
        return t`Має містити хоча б один спеціальний символ (!"#$%*-/:;=?)`;
    } else if (!hasLetters) {
        return t`Має містити хоча б одну літеру`;
    }
    return null;
}

export function saveRegistrationSources(params) {
    const item = getLocalStorageItem(WS_KEY_REGISTRATION_ATTEMPTS);
    let registrationAttempts;
    try {
        registrationAttempts = JSON.parse(item);
    } catch (_) {
        registrationAttempts = [];
    }
    if (!Array.isArray(registrationAttempts)) {
        registrationAttempts = [];
    }

    registrationAttempts.push({ ...params, current_datetime: Date.now() });
    setLocalStorageItem(
        WS_KEY_REGISTRATION_ATTEMPTS,
        JSON.stringify(registrationAttempts),
    );
}

export function isTagAlreadyInList(id, tags) {
    if (id && tags) {
        const tagIds = tags.map((tag) => tag.id);
        return tagIds.includes(id);
    }
    return false;
}

export function isTagNameAlreadyInList(name, tags) {
    if (name && tags) {
        const tagNames = tags.map((tag) => tag.name);
        return tagNames.includes(name);
    }
    return false;
}

export function sortByOrder(a, b) {
    return a.order - b.order;
}

export function sortRolesByFullName(a, b) {
    const getName = (role) => {
        return formatFullName(role.user).toLowerCase();
    };
    const firstFullName = getName(a);
    const secondFullName = getName(b);

    if (!firstFullName && !secondFullName) return 0;
    if (firstFullName && !secondFullName) return -1;
    if (!firstFullName && secondFullName) return 1;

    return firstFullName.localeCompare(secondFullName, 'ua');
}

export function sortRolesByEmail(a, b) {
    const firstEmail = a.user.email.toLowerCase();
    const secondEmail = b.user.email.toLowerCase();
    return firstEmail > secondEmail;
}

export function getReviewStats(doc) {
    const stats = {
        pendedCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
        reviews: [],
        activeReviews: [],
    };

    stats.reviews = doc.reviews.filter((review) => review.type);
    const reviewsEmails = stats.reviews.length
        ? stats.reviews.map((review) => review.role.user.email)
        : [];

    const reviewsGroupIds = stats.reviews.length
        ? stats.reviews.map((review) => review.groupId)
        : [];

    stats.activeReviews = (doc.reviewRequests || []).filter((request) => {
        if (request.toRoleId) {
            return !reviewsEmails.includes(request.toRole.user.email);
        } else {
            return !reviewsGroupIds.includes(request.toGroupId);
        }
    });

    stats.pendedCount = stats.activeReviews.length || 0;
    stats.approvedCount = doc.reviews.length
        ? doc.reviews.filter((review) => {
              return review.type === DocumentReviewType.APPROVE;
          }).length
        : 0;
    stats.rejectedCount = doc.reviews.length
        ? doc.reviews.filter((review) => {
              return review.type === DocumentReviewType.REJECT;
          }).length
        : 0;
    return stats;
}

export function getCurrentExecutor(doc) {
    let displayValue = null;

    // Remove withdrawn reviews (type equals null)
    const reviews = doc.reviews.filter((review) => review.type);

    const rejectedReviews = reviews.filter(
        (review) => review.type === DocumentReviewType.REJECT,
    );
    const rejectedReviewsCount = rejectedReviews.length;

    if (rejectedReviewsCount) {
        const lastRejectedReview = rejectedReviews[rejectedReviewsCount - 1];
        displayValue = lastRejectedReview.groupId
            ? lastRejectedReview.group.name
            : formatUserIdentifier(lastRejectedReview.role.user);
    } else if (doc.reviewRequests.length) {
        const reviewsRoleIds = reviews
            .map((review) => review.roleId)
            .filter(Boolean);

        const reviewsGroupIds = reviews
            .map((review) => review.groupId)
            .filter(Boolean);

        const pendingReviewRequests = doc.reviewRequests
            .filter((request) => {
                if (!request.toGroupId) {
                    return !reviewsRoleIds.includes(request.toRoleId);
                } else {
                    return !reviewsGroupIds.includes(request.toGroupId);
                }
            })
            .toSorted(sortByOrder);

        const pendingReviewRequestsCount = pendingReviewRequests.length;

        if (pendingReviewRequestsCount) {
            const firstPendingReview = pendingReviewRequests[0];
            const firstReviewer = firstPendingReview.toGroupId
                ? firstPendingReview.toGroup.name
                : formatUserIdentifier(firstPendingReview.toRole.user);
            if (
                doc.reviewSetting &&
                doc.reviewSetting.isParallel &&
                pendingReviewRequestsCount > 1
            ) {
                displayValue = `${firstReviewer} та ще ${
                    pendingReviewRequestsCount - 1
                }`;
            } else {
                displayValue = firstReviewer;
            }
        }
    }

    if (!displayValue && doc.signers.length) {
        const isOrdereredSigningProcess = doc.signers.some((signer) =>
            Boolean(signer.order),
        );
        const signedRoleIds = new Set(
            doc.signatures.map((signature) => signature.roleId),
        );
        const pendingSigners = doc.signers
            .filter((signer) => {
                if (signer.roleId) {
                    return !signedRoleIds.has(signer.roleId);
                } else {
                    return !signedRoleIds.has(signer.groupSignerId);
                }
            })
            .toSorted(sortByOrder);
        const pendingSignersCount = pendingSigners.length;

        if (pendingSignersCount) {
            const firstPendingSigner = pendingSigners[0];
            const firstSigner = firstPendingSigner.groupId
                ? firstPendingSigner.group.name
                : formatUserIdentifier(firstPendingSigner.role.user);
            if (!isOrdereredSigningProcess && pendingSignersCount > 1) {
                displayValue = `${firstSigner} та ще ${
                    pendingSignersCount - 1
                }`;
            } else {
                displayValue = firstSigner;
            }
        }
    }

    return displayValue;
}

export const isNeedAntivirusPopup = (doc) =>
    doc.antivirusChecks?.[0] &&
    doc.antivirusChecks[0].status !== AntivirusStatusTypes.clean &&
    doc.antivirusChecks[0].status !== AntivirusStatusTypes.encrypted;

export const getAcceptSignatureExtensions = () =>
    window.innerWidth > 768 ? ACCEPT_SIGNATURE_EXTENSIONS : null;

export const getIsMaxActiveEmployees = (
    activeEmployees,
    currentCompanyMaxEmployees,
) => {
    if (!currentCompanyMaxEmployees) {
        return false;
    }

    return currentCompanyMaxEmployees <= activeEmployees;
};

const getCircularReplacer = () => {
    const seen = new WeakSet();
    return (key, value) => {
        if (typeof value === 'object' && value !== null) {
            if (seen.has(value)) {
                return;
            }
            seen.add(value);
        }
        return value;
    };
};
export const stringify = (value) =>
    JSON.stringify(value, getCircularReplacer());

export const isTovCompany = (companyEdrpou) =>
    companyEdrpou &&
    companyEdrpou.length === 8 &&
    companyEdrpou.match(/^[0-9]+$/) !== null;

/**
 * @param {string} companyEdrpou
 * @returns boolean
 */
export const isVchasnoCompany = (companyEdrpou) =>
    companyEdrpou === VCHASNO_EDRPOU;

export const getInputsAcceptExtensions = (isVersioned) =>
    isVersioned ? ACCEPT_VERSIONS_EXTENSIONS : ACCEPT_EXTENSIONS;

export const getInputsAcceptExtensionsText = (isVersioned) =>
    isVersioned ? ACCEPT_VERSIONS_EXTENSIONS_TEXT : ACCEPT_EXTENSIONS_TEXT;

/**
 * @param {object} user
 * @param {string} searchQuery
 */
export const matchUserSearch = (user, searchQuery) => {
    const search = searchQuery.toLowerCase().trim();
    return (
        formatFullName(user).toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search) ||
        user.phone?.includes(search)
    );
};

/**
 * @param {object} employees
 * @param {string} [order]
 */
export const sortEmployeesByDateDeleted = (employees, order) => {
    if (!order) {
        return employees;
    }

    return employees.slice().sort((a, b) => {
        const diff = moment(a.dateDeleted).diff(moment(b.dateDeleted));
        return order === 'asc' ? diff : -diff;
    });
};

/**
 * @param {string | null} [currentOrderType]
 */
export const getNewOrderType = (currentOrderType) => {
    if (!currentOrderType) {
        return 'asc';
    } else if (currentOrderType === 'asc') {
        return 'desc';
    } else {
        return 'asc';
    }
};

// We need this for issue, when user try sign document in "internal_asic" signature
// format (now it can be sign only using diia.euro) but not use euro signature
/**
 * @param {string} signatureFormat
 */
export const getFormatDocumentForSendToBackend = (signatureFormat) =>
    signatureFormat === SignatureFormat.INTERNAL_ASIC
        ? SignatureFormat.EXTERNAL_SEPARATED
        : signatureFormat;

/**
 * @param {string | undefined} source
 * @returns boolean
 */
export const getIsCurrentUserSourceAfterSign = (source) =>
    source === 'after_sign';

export { removeDuplicatesFromArray } from '../components/EmployeeGroup/utils';

export const getIsSignerGroup = (signer) => {
    return signer.group?.members?.length > 0;
};

export const getIsReviewRequestElementGroup = (review) => {
    return review.toGroup?.members?.length > 0;
};

export const getBankIdFromIbanUA = (iban) => {
    const cleanIban = iban.replace(/\s+/g, '').toUpperCase();

    if (cleanIban.startsWith('UA') && cleanIban.length === 29) {
        return cleanIban.slice(4, 10);
    }

    return null;
};

export default {
    joinStrings,
    reverseStr,

    capitalizeFirstLetter,
    formatFullName,
    getEmailDomain,
    getPKCodeTitle,
    getPKPositionTitle,
    removeWhitespace,
    stringToUint8Array,
    truncateText,

    concatUnique,
    flattenInnerArray,
    flattenObjectsArray,
    moveArrayItem,
    getCommonElements,

    isEmptyObject,

    createWarningError,
    formatError,
    logErrorByLevel,

    getIsSignerGroup,
    getIsReviewRequestElementGroup,

    moveCursorToInputEnd,

    isBrowserSupported,

    getDocumentsGraphArguments,
    isClientRendering,
    isIOSDevice,
    isUrl2PdfViewer,
    isVisibleElement,
    isWorkersSupport,
    getFileNames,

    convertFileToDataUrl,
    convertDataUrlToFile,

    isValidPhone,
    getValidPhone,

    saveRegistrationSources,

    isTagAlreadyInList,
    isTagNameAlreadyInList,

    sortByOrder,

    getReviewStats,
    getCurrentExecutor,

    getAcceptSignatureExtensions,

    stringify,

    isTovCompany,

    getInputsAcceptExtensions,
    getInputsAcceptExtensionsText,

    getFormatDocumentForSendToBackend,
    getIsCurrentUserSourceAfterSign,
};

export {
    checkIsValidEdrpou,
    extractNumbers,
    getActiveEmployees,
    getIsArchivePageUtility,
} from './ts/helpers';
