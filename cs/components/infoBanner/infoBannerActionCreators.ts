import { getLocale } from '../../lib/i18n/utils';

import { Thunk } from '../../types';

import actions from './infoBannerActions';

import { getLocalStorageItem, setLocalStorageItem } from '../../lib/webStorage';
import eventTracking from '../../services/analytics/eventTracking';
import { getActiveBanner } from '../../services/banners';
import { LAST_HIDDEN_BANNER_ID } from './constants';

function onHideBanner(): Thunk {
    return (dispatch, getState) => {
        const { id, analyticsCategory, text, textEn } = getState().infoBanner;
        const isUk = getLocale() === 'uk';
        const label = isUk ? text : textEn;
        dispatch({ type: actions.INFO_BANNER__HIDE });
        setLocalStorageItem(LAST_HIDDEN_BANNER_ID, id);
        eventTracking.sendEvent(analyticsCategory, 'close', label);
        eventTracking.sendSystemBannerToGTMV4({
            action: 'closed_manual',
            category: analyticsCategory,
        });
    };
}

function onMount(): Thunk {
    return async (dispatch) => {
        const activeBanner = await getActiveBanner();
        const lastHiddenBannerId = getLocalStorageItem(LAST_HIDDEN_BANNER_ID);

        if (activeBanner && activeBanner.id !== lastHiddenBannerId) {
            eventTracking.sendSystemBannerToGTMV4({
                action: 'shown',
                category: activeBanner.analyticsCategory,
            });
            dispatch({ type: actions.INFO_BANNER__SHOW, data: activeBanner });
        }
    };
}

export default {
    onHideBanner,
    onMount,
};
