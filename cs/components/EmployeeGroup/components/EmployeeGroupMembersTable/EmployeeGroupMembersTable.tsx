import React, { FC } from 'react';
import { useSelector } from 'react-redux';

import cn from 'classnames';
import { useEmployeeGroupContext } from 'components/EmployeeGroup/context';
import { formatFullName } from 'lib/helpers';
import { formatUserIdentifier } from 'lib/ts/helpers';
import {
    getCurrentUserRoleId,
    getIsCanEditRoles,
} from 'selectors/app.selectors';
import { t } from 'ttag';

import { GroupMemberRole } from '../../types';

import Icon from '../../../ui/icon/icon';
import Table from '../../../ui/table/table';

import { COLUMNS_SIZES, COLUMNS_TITLES } from '../../constants';

import CloseSvg from '../../../companyCard/components/CompanyEmployeesSettings/components/EmployeesList/components/SortedTableHeaderItem/images/close.svg';

import css from './EmployeeGroupMembersTable.css';

const EmployeeGroupMembersTable: FC = () => {
    const currentUserRoleId = useSelector(getCurrentUserRoleId);
    const isCanEdit = useSelector(getIsCanEditRoles);

    const { onSetMemberToDelete, membersToShow } = useEmployeeGroupContext();

    if (!membersToShow?.length) {
        return null;
    }

    const formattedGroupMembers = membersToShow.map(
        (member: GroupMemberRole) => ({
            id: member.roleId,
            items: {
                fullName: member?.user ? (
                    <div className={css.trimmedText}>
                        {formatFullName(member.user)}
                        {member?.roleId === currentUserRoleId && (
                            <span className={css.currentUserInTable}>
                                ({t`Ви`})
                            </span>
                        )}
                    </div>
                ) : (
                    ''
                ),
                identifier: (
                    <span className={cn(css.link, css.trimmedText)}>
                        {formatUserIdentifier(member?.user)}
                    </span>
                ),
                isAdmin: member?.isAdmin ? t`Адміністратор` : '',
                deleteBtn: (
                    <>
                        {isCanEdit ? (
                            <div
                                className={css.deleteMemberIconContainer}
                                onClick={() => onSetMemberToDelete(member)}
                            >
                                <Icon
                                    className={css.deleteMemberIcon}
                                    glyph={CloseSvg}
                                />
                            </div>
                        ) : (
                            ''
                        )}
                    </>
                ),
            },
        }),
    );

    return (
        <div className={css.container}>
            <Table
                headerItems={COLUMNS_TITLES}
                data={formattedGroupMembers}
                sizes={COLUMNS_SIZES}
                isFullWidth
            />
        </div>
    );
};

export default EmployeeGroupMembersTable;
