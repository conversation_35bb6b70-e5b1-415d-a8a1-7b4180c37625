import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import cn from 'classnames';
import ActiveRateSubtitle from 'components/CheckoutRates/ActiveRateSubtitle';
import { useCompanyEmployeeNumberPopup } from 'components/CompanyEmployeeNumber/useCompanyEmployeeNumberPopup';
import { useContactSalesMutation } from 'components/CompanyEmployeeNumber/useContactSalesMutation';
import { getIsProRate, getIsStartRate } from 'lib/rates/utils';
import { getLocationQuery, stringifyLocationQuery } from 'lib/url';
import {
    getActiveRatesNamesSelector,
    getCurrentCompanyIsFop,
    getIsCompanyHasOnlyFreeRate,
} from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { ACCOUNT_RATE_SHORT_TITLE_MAP } from 'services/billing';
import {
    UNLIMITED_RATES_SEND_DOCS_FOR_FOP,
    UNLIMITED_RATES_SET,
} from 'services/billing/constants';
import { AccountRate } from 'services/enums';
import { t } from 'ttag';

import { RateInformation } from '../../../Checkout/types';

import { getAdditionalInformation } from './helpers';

import Button from '../../../ui/button/button';
import Icon from '../../../ui/icon/icon';

import { CHECKOUT_PREVIOUS_URL } from '../../../Checkout/Order/constants';
import CompanyRateCard from '../../../companyCard/components/CompanyRateCard/CompanyRateCard';

import css from './Rate.css';

interface RateProps {
    rate: RateInformation;
    isExtendRate?: boolean;
    isRecommended?: boolean;
}

const Rate: React.FC<RateProps> = ({ rate, isExtendRate, isRecommended }) => {
    const contactSalesMutation = useContactSalesMutation();
    const employeeNumberPopup = useCompanyEmployeeNumberPopup();
    const history = useHistory();
    const location = useLocation();

    const isShowSubtitle = rate.price_per_month && rate.price_per_year;

    const additionalInformation = getAdditionalInformation(rate);

    const isUnlimitedRate = UNLIMITED_RATES_SET.has(rate.rate);
    const isFreeRate = rate.rate === AccountRate.FREE;
    const activeRates = useSelector(getActiveRatesNamesSelector);
    const isCompanyHasOnlyFreeRate = useSelector(getIsCompanyHasOnlyFreeRate);
    const isBigArchiveRate = activeRates?.includes(AccountRate.ARCHIVE_BIG);
    const companyIsFOP = useSelector(getCurrentCompanyIsFop);

    const isShowContinueRateButton =
        isExtendRate && !isUnlimitedRate && !isFreeRate;
    const isShowBuyRateButton =
        !isExtendRate && !isUnlimitedRate && !isFreeRate;

    const consultationOrder = () => {
        if (isUnlimitedRate) {
            eventTracking.sendToGTM({
                event: 'click_book_demo_ultimate',
            });

            if (!companyIsFOP) {
                // для компаній показуємо попап з кнопкою замовити і селектом для обрання кількості працівників в компанії
                employeeNumberPopup.setIsOpen(true);
            } else {
                // для фопів одразу відправляємо запит на створення ліда, без вказання кількості співробітників
                contactSalesMutation.mutate([]);
            }
        }
        if (isFreeRate) {
            history.push(
                `/app/checkout?rate=${
                    companyIsFOP
                        ? AccountRate.ARCHIVE_SMALL
                        : AccountRate.ARCHIVE_BIG
                }`,
            );
        }
    };

    const toCheckoutPage = (isRenewRate?: boolean) => {
        const query = getLocationQuery(location);
        query.rate = rate.rate;

        if (getIsStartRate(rate.rate)) {
            if (isRenewRate) {
                eventTracking.sendToGTM({
                    event: 'renew_start',
                    category: 'tariffs_page',
                });
            }
            if (!isRenewRate) {
                eventTracking.sendToGTM({
                    event: 'click_purchase_start',
                });
            }
        }

        if (getIsProRate(rate.rate)) {
            if (isRenewRate) {
                eventTracking.sendToGTM({
                    event: 'renew_pro',
                    category: 'tariffs_page',
                });
            }
            if (!isRenewRate) {
                eventTracking.sendToGTM({
                    event: 'click_purchase_pro',
                });
            }
        }

        sessionStorage.setItem(CHECKOUT_PREVIOUS_URL, location.pathname);

        history.push({
            pathname: '/app/checkout',
            search: stringifyLocationQuery(query),
        });
    };

    return (
        <CompanyRateCard
            className={css.rateCard}
            isRecommended={isRecommended}
            isActive={isExtendRate}
        >
            <div className={css.mainInfo}>
                <CompanyRateCard.Title
                    className={css.title}
                    isDisableInfoIcon
                    rateName={`${ACCOUNT_RATE_SHORT_TITLE_MAP[rate.rate]}`}
                >
                    <div className={css.subtitle}>
                        {!isExtendRate && (
                            <>
                                {isShowSubtitle && (
                                    <>
                                        <p className={css.subtitleMain}>
                                            {t`₴ ${rate.price_per_year} грн/рік`}
                                        </p>
                                        <p className={css.subtitleAdditional}>
                                            {t`лише ${rate.price_per_month} грн/міс. з ПДВ`}
                                        </p>
                                    </>
                                )}
                                {!isShowSubtitle && (
                                    <p className={css.subtitleMain}>
                                        {t`Договірна ціна`}
                                    </p>
                                )}
                            </>
                        )}
                        {isExtendRate && <ActiveRateSubtitle />}
                    </div>
                </CompanyRateCard.Title>
                <div className={css.infoBlockContainer}>
                    <CompanyRateCard.InfoBlock
                        className={css.info}
                        title={t`Документів`}
                        description={t`до надсилання`}
                        iconType="document"
                        isCoolUnlimit={
                            UNLIMITED_RATES_SEND_DOCS_FOR_FOP.has(rate.rate) &&
                            companyIsFOP
                        }
                    >
                        <>
                            {rate.documents_to_send !== null && (
                                <p className={css.infoText}>
                                    {rate.documents_to_send}/{t`рік`}
                                </p>
                            )}
                            {rate.documents_to_send === null && (
                                <p className={css.infoText}>∞</p>
                            )}
                        </>
                    </CompanyRateCard.InfoBlock>
                    <CompanyRateCard.InfoBlock
                        className={css.info}
                        title={t`Документів`}
                        description={t`на перегляд`}
                        iconType="document"
                    >
                        <p className={css.infoText}>
                            {rate.documents_to_view || '∞'}
                        </p>
                    </CompanyRateCard.InfoBlock>
                    <CompanyRateCard.InfoBlock
                        className={css.info}
                        title={t`Співробітників`}
                        description={t`додано`}
                        iconType="people"
                    >
                        <p className={css.infoText}>
                            {rate.employees ? t`до` : t`від`}{' '}
                            {/*TODO: REMOVE AFTER NEW FREE*/}
                            {isFreeRate ? 1 : rate.employees || 4}
                        </p>
                    </CompanyRateCard.InfoBlock>
                    <div className={css.button}>
                        {isFreeRate && !isBigArchiveRate && (
                            <Button
                                typeContour
                                width="full"
                                theme="blue"
                                onClick={consultationOrder}
                            >
                                {t`Додати архів`}
                            </Button>
                        )}
                        {isUnlimitedRate && (
                            <Button
                                typeContour
                                width="full"
                                theme="blue"
                                onClick={consultationOrder}
                            >{t`Замовити`}</Button>
                        )}
                        {isShowContinueRateButton && (
                            <Button
                                width="full"
                                theme="cta"
                                onClick={() => toCheckoutPage(true)}
                            >{t`Продовжити тариф`}</Button>
                        )}
                        {isShowBuyRateButton && (
                            <Button
                                width="full"
                                theme="cta"
                                onClick={() => toCheckoutPage(false)}
                            >
                                {/* тільки для користувачів на Базовому тарифі - Змінити назву кнопки з “Перейти на тариф“ > “Оплатити тариф“*/}
                                {!isCompanyHasOnlyFreeRate
                                    ? t`Перейти на тариф`
                                    : t`Оплатити тариф`}
                            </Button>
                        )}
                    </div>
                </div>
            </div>
            {!isExtendRate && (
                <div className={css.additionalInfo}>
                    <ul className={css.list}>
                        {additionalInformation.map((item) => (
                            <li key={item.label}>
                                <div
                                    className={cn(css.listItem, {
                                        [css.active]: item.isIncluded,
                                    })}
                                >
                                    <p className={css.listItemMainText}>
                                        {item.label}
                                    </p>{' '}
                                    <p className={css.listItemAvailability}>
                                        {!item.statusIcon && item.status && (
                                            <span>{item.status}</span>
                                        )}
                                        {!item.status && item.statusIcon && (
                                            <p className={css.listItemIcon}>
                                                <Icon glyph={item.statusIcon} />
                                            </p>
                                        )}
                                    </p>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </CompanyRateCard>
    );
};

export default Rate;
