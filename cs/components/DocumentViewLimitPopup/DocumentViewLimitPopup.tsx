import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import {
    ARCHIVE_BIG_PLUS_PRO_RATE_NUMBER_OF_DOCUMENTS,
    billGenerationProRateUrl,
} from 'components/ArchiveBanner/constants';
import { useCompanyTariffConfig } from 'hooks/useCompanyTariffConfig';
import { setLocalStorageItem } from 'lib/webStorage';
import {
    getActiveRatesNamesSelector,
    getCompanyConfigSettingsMaxArchiveDocumentsCount,
    getCompanyConfigSettingsMaxVisibleDocumentsCount,
    getCompanyStartProRatesCreatedBefore04092024,
    getCurrentUserRole,
    getIsAnyProRateExists,
    getUsedDocumentsCount,
} from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import {
    composeLocalStorageDocumentViewLimitPopupDateClose,
    getIsArchivePopupClosedMonthAgo,
} from './utils';

import Alert from '../ui/Alert/Alert';
import Button from '../ui/button/button';
import Popup from '../ui/popup/popup';

import Layout from './Layout';
import {
    ARCHIVE_BIG_NUMBER_OF_DOCUMENTS,
    billGenerationArchiveBigUrl,
    contactWithManagerUrl,
} from './constants';

import AlmostExceededSvg from './images/almostExceeded.svg';
import ExceededSvg from './images/documentsLimitMan.svg';

const DocumentViewLimitPopup: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    const {
        isUnlimited,
        maxDocumentsForView,
        companyDocumentsCount,
        isLimitExceeded,
        isAlmostExceeded,
        percentsLeft,
        isDataFetched,
    } = useCompanyTariffConfig();
    const [show, setShow] = useState(true);
    const [buttonLoading, setButtonLoading] = useState(false);
    const currentRole = useSelector(getCurrentUserRole);
    const companyStartProRatesCreatedBefore04092024 = useSelector(
        getCompanyStartProRatesCreatedBefore04092024,
    );

    const activeRates = useSelector(getActiveRatesNamesSelector);
    const maxVisibleDocumentsCount =
        useSelector(getCompanyConfigSettingsMaxVisibleDocumentsCount) || 0;
    const isAnyProRatesExist = useSelector(getIsAnyProRateExists);
    const archivedDocumentsCount = useSelector(
        getCompanyConfigSettingsMaxArchiveDocumentsCount,
    );
    const history = useHistory();

    const allDocumentsCount = useSelector(getUsedDocumentsCount);

    const isExistArchiveBig =
        archivedDocumentsCount === ARCHIVE_BIG_NUMBER_OF_DOCUMENTS;

    const blockedDocuments = allDocumentsCount - maxVisibleDocumentsCount;

    const closedMonthAgo = getIsArchivePopupClosedMonthAgo(currentRole.id);

    const isSuppressShowing =
        !isDataFetched ||
        !show ||
        isUnlimited ||
        !currentRole.isAdmin ||
        !closedMonthAgo;

    const showLimitExceeded = !isSuppressShowing && isLimitExceeded;

    const showAlmostExceeded = !isSuppressShowing && isAlmostExceeded;

    const blockedMoreThenBigArchive =
        blockedDocuments > ARCHIVE_BIG_NUMBER_OF_DOCUMENTS;

    useEffect(() => {
        setShow(true);
    }, [currentRole, companyDocumentsCount, maxDocumentsForView]);

    useEffect(() => {
        if (showLimitExceeded) {
            eventTracking.sendToGTM({
                category: 'view_limit_100%_pop-up',
                action: 'show',
                label: [...(activeRates || [])].sort().join('|'),
            });
            eventTracking.sendPromoPopupToGTMV4({
                action: 'shown',
                campaign: 'view_limit_exceeded',
            });
        }
    }, [showLimitExceeded, activeRates]);

    useEffect(() => {
        if (showAlmostExceeded) {
            eventTracking.sendToGTM({
                category: 'view_limit_90%_pop-up',
                action: 'show',
                label: [...(activeRates || [])].sort().join('|'),
            });
            eventTracking.sendPromoPopupToGTMV4({
                action: 'shown',
                campaign: 'view_limit_almost_exceeded',
            });
        }
    }, [showAlmostExceeded, activeRates]);

    if (!allDocumentsCount) {
        return null;
    }

    if (isSuppressShowing) {
        return null;
    }

    const saveStateInLocalStorage = () => {
        const dateStamp = new Date();
        setLocalStorageItem(
            composeLocalStorageDocumentViewLimitPopupDateClose(currentRole.id),
            dateStamp,
        );
    };

    const sendToGoogleTagManager = (onlyArchive: boolean) => {
        return eventTracking.sendToGTMV4({
            event: onlyArchive
                ? 'ec_buy_archive_popup'
                : 'ec_buy_tariff_archive_popup',
        });
    };

    const onClose = () => {
        saveStateInLocalStorage();
        setShow(() => false);
    };

    const handleClose = () => {
        onClose();
        eventTracking.sendPromoPopupToGTMV4({
            action: 'closed_manual',
            campaign: isLimitExceeded
                ? 'view_limit_exceeded'
                : 'view_limit_almost_exceeded',
        });
    };

    const handleChooseTariffBtnClick = () => {
        onClose();

        eventTracking.sendToGTM({
            category: isLimitExceeded
                ? 'view_limit_100%_pop-up'
                : 'view_limit_90%_pop-up',
            action: 'click_choose_tariff_button',
            label: [...(activeRates || [])].sort().join('|'),
        });
        eventTracking.sendPromoPopupToGTMV4({
            action: 'click',
            campaign: isLimitExceeded
                ? 'view_limit_exceeded'
                : 'view_limit_almost_exceeded',
        });

        if (isExistArchiveBig && !isAnyProRatesExist) {
            setButtonLoading(true);
            history.push(billGenerationProRateUrl);
            sendToGoogleTagManager(false);
        }

        if (isExistArchiveBig && isAnyProRatesExist) {
            setButtonLoading(true);
            history.push(contactWithManagerUrl);
            sendToGoogleTagManager(false);
        }

        if (
            !isExistArchiveBig &&
            isAnyProRatesExist &&
            blockedDocuments < ARCHIVE_BIG_NUMBER_OF_DOCUMENTS
        ) {
            setButtonLoading(true);
            history.push(billGenerationArchiveBigUrl);
            sendToGoogleTagManager(true);
        }

        if (
            isAnyProRatesExist &&
            blockedDocuments > ARCHIVE_BIG_NUMBER_OF_DOCUMENTS
        ) {
            setButtonLoading(true);
            history.push(contactWithManagerUrl);
            sendToGoogleTagManager(false);
        }

        if (
            blockedDocuments < ARCHIVE_BIG_NUMBER_OF_DOCUMENTS &&
            !isExistArchiveBig
        ) {
            setButtonLoading(true);
            history.push(billGenerationArchiveBigUrl);
            sendToGoogleTagManager(true);
        }

        if (
            blockedDocuments >= ARCHIVE_BIG_NUMBER_OF_DOCUMENTS &&
            blockedDocuments < ARCHIVE_BIG_PLUS_PRO_RATE_NUMBER_OF_DOCUMENTS &&
            !isAnyProRatesExist
        ) {
            setButtonLoading(true);
            history.push(billGenerationProRateUrl);
            sendToGoogleTagManager(false);
        }

        if (blockedDocuments >= ARCHIVE_BIG_PLUS_PRO_RATE_NUMBER_OF_DOCUMENTS) {
            setButtonLoading(true);
            history.push(contactWithManagerUrl);
            sendToGoogleTagManager(false);
        }
    };

    const actionButtons = (
        <>
            <Button
                onClick={handleChooseTariffBtnClick}
                theme="cta"
                width="full"
                isLoading={buttonLoading}
            >
                {blockedMoreThenBigArchive ? t`Обрати тариф` : t`Купити архів`}
            </Button>
        </>
    );

    if (companyStartProRatesCreatedBefore04092024.length > 0) {
        return null;
    }

    if (showLimitExceeded) {
        return (
            <Popup inPortal active onClose={handleClose}>
                <Layout
                    title={t`Ви досягли ліміту доступних для перегляду документів`}
                    icon={ExceededSvg}
                    text={t`Збільшуйте кількість документів, доступних для перегляду з будь-якого тарифу.`}
                    alert={
                        <Alert theme="info">
                            {blockedMoreThenBigArchive
                                ? t`Щоб збільшити кількість документів на перегляд придбайте тариф.`
                                : t`Щоб збільшити кількість документів на перегляд придбайте тариф Архіву.`}
                        </Alert>
                    }
                >
                    {actionButtons}
                </Layout>
            </Popup>
        );
    }

    if (showAlmostExceeded) {
        return (
            <Popup inPortal active onClose={handleClose}>
                <Layout
                    title={t`Залишилося ${percentsLeft}% доступних для перегляду документів!`}
                    text={t`За тарифом вашої компанії до перегляду доступно ${maxDocumentsForView} останніх документів. ${companyDocumentsCount} із них уже завантажено в обліковий запис.`}
                    icon={AlmostExceededSvg}
                    alert={
                        <Alert>{t`Попереджаємо заздалегідь, щоб ви могли перейти на більший тариф та не мали обмежень у перегляді і вивантаженні документів.`}</Alert>
                    }
                >
                    {actionButtons}
                </Layout>
            </Popup>
        );
    }

    return null;
};

export default DocumentViewLimitPopup;
