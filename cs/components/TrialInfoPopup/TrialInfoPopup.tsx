import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Alert, Button, FlexBox } from '@vchasno/ui-kit';

import DisplayTryTrial from 'components/DisplayTryTrial';
import appActionCreators from 'components/app/appActionCreators';
import notificationCenterActionCreators from 'components/notificationCenter/notificationCenterActionCreators';
import proRateTrialPopupActionCreators from 'components/proRateTrialPopup/proRateTrialPopupActionCreators';
import Popup from 'components/ui/popup/popup';
import { DATE_FORMAT } from 'lib/constants';
import { formatDate } from 'lib/date';
import { getActiveTrialProRate } from 'selectors/app.selectors';
import {
    getIsShowTargetPopup,
    getIsTrialInfoPopupActive,
    getIsTrialRateAlreadyExist,
} from 'selectors/trialInfoPopup.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { createTrialRateWhenTryUseLockedFunction } from 'services/billing';
import { updateUserTrialAutoEnabledField } from 'services/user';
import { setTrialInfoPopupOpen } from 'store/trialInfoPopup';
import { t } from 'ttag';

import TrialFunctionalityInfo from './TrialFunctionalityInfo';
import TrialFunctionalityMenu from './TrialFunctionalityMenu';

import css from './TrialInfoPopup.css';

const TrialInfoPopup: React.FC = () => {
    const dispatch = useDispatch();
    const [showStep, setShowStep] = useState<number>(0);
    const [currentIndex, changeIndex] = useState<number>(0);
    const isOpen = useSelector(getIsTrialInfoPopupActive);
    const isShowTargetPopup = useSelector(getIsShowTargetPopup);
    const roleHasUltimateRate = useSelector(getIsTrialRateAlreadyExist);
    const activeTrialProRate = useSelector(getActiveTrialProRate);

    const trialDeadLine = formatDate(activeTrialProRate?.endDate, DATE_FORMAT);

    useEffect(() => {
        if (isOpen) {
            eventTracking.sendPromoPopupToGTMV4({
                action: 'shown',
                campaign: 'trial_info',
            });
        }
    }, [isOpen]);

    const onClosePopup = async () => {
        await updateUserTrialAutoEnabledField(false);
        dispatch(
            setTrialInfoPopupOpen({
                isTrialInfoPopupOpen: false,
                isRoleHasUltimateRate: false,
            }),
        );

        setShowStep(0);
        changeIndex(0);
    };

    const handleClosePopup = async () => {
        eventTracking.sendPromoPopupToGTMV4({
            action: 'closed_manual',
            campaign: 'trial_info',
        });
        return onClosePopup();
    };

    const activateTrialHandler = async () => {
        try {
            const trialRate = await createTrialRateWhenTryUseLockedFunction();

            if (trialRate.ok) {
                await dispatch(appActionCreators.onMount());
                await onClosePopup();
                dispatch(proRateTrialPopupActionCreators.onShow());
                eventTracking.sendPromoPopupToGTMV4({
                    action: 'click',
                    campaign: 'trial_info',
                });
            }
        } catch (error) {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    type: 'text',
                    textType: 'error',
                    text: error.reason,
                    showCloseButton: true,
                    autoClose: 5000,
                }),
            );
        }
    };

    const renderContent = (
        notActiveTrial: string,
        activeTrial: string,
        expiredUltimateRate: string,
    ) => {
        if (activeTrialProRate) {
            return activeTrial;
        }
        if (roleHasUltimateRate) {
            return expiredUltimateRate;
        }
        return notActiveTrial;
    };

    return (
        <Popup fullContent active={isOpen} onClose={handleClosePopup}>
            <div className={css.root}>
                {showStep === 0 && !isShowTargetPopup && (
                    <div className={css.contentWrapper}>
                        <div className={css.sideMenu}>
                            <TrialFunctionalityMenu
                                changeIndex={changeIndex}
                                setShowStep={setShowStep}
                            />
                        </div>
                        <FlexBox
                            gap={12}
                            direction="column"
                            className={css.mainContent}
                        >
                            <h3 className={css.title}>
                                {renderContent(
                                    t`Увімкніть усі функції сервісу безкоштовно!`,
                                    t`Ура! Ви увімкнули всі функції сервісу!`,
                                    t`Ура! Ви успішно пройшли перевірку.`,
                                )}
                            </h3>

                            <p className={css.text}>
                                {renderContent(
                                    t`Додавайте до 200 співробітників і користуйтеся всіма функціями сервісу безкоштовно протягом 14 днів`,
                                    t`Додавайте до 200 співробітників і користуйтеся всіма функціями сервісу безкоштовно протягом 14 днів`,
                                    t`Тестовий період не активується, оскільки вам уже доступні всі можливості сервісу відповідно до поточного тарифу компанії.`,
                                )}
                            </p>
                            {activeTrialProRate && !roleHasUltimateRate && (
                                <Alert wide type="success">
                                    <FlexBox className={css.alertContent}>
                                        {t`Доступ надано до ${trialDeadLine} року`}
                                    </FlexBox>
                                </Alert>
                            )}
                            <DisplayTryTrial>
                                <Button
                                    theme="primary"
                                    className={css.buttonGetStarted}
                                    onClick={() => {
                                        activateTrialHandler();

                                        eventTracking.sendToGTMV4({
                                            event:
                                                'ec_start_welcome_popup_trial',
                                        });
                                    }}
                                >
                                    {t`Почати 14-ти денну безкоштовну версію`}
                                </Button>
                            </DisplayTryTrial>
                        </FlexBox>
                    </div>
                )}
                {(showStep === 1 || isShowTargetPopup) && (
                    <TrialFunctionalityInfo
                        currentIndex={currentIndex}
                        changeIndex={changeIndex}
                        comeBackStep={setShowStep}
                        activateTrialHandler={activateTrialHandler}
                    />
                )}
            </div>
        </Popup>
    );
};

export default TrialInfoPopup;
