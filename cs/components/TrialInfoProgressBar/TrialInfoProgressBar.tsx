import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { BlackTooltip, Button, FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import DaysLeft from 'components/companyCard/components/CompanyRateCard/components/RateTitleContainer/components/DaysLeft/DaysLeft';
import Icon from 'components/ui/icon';
import {
    canRoleActivateTrialWithCurrentRate,
    getActiveTrialProRate,
    getIsAnyUnlimitRateExists,
} from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { setTrialInfoPopupOpen } from 'store/trialInfoPopup';
import { t } from 'ttag';

import { setTrialProgressBarDays } from './helpers';

import SvgInfo from './images/info.svg';

import css from './TrialInfoProgressBar.css';

const PATH_TO_REDIRECT_FROM_PROGRESS_BAR = '/app/checkout-rates/web';

const TrialInfoProgressBar: React.FC = () => {
    const dispatch = useDispatch();
    const history = useHistory();
    const activeTrialRate = useSelector(getActiveTrialProRate);
    const isAnyUnlimitRateExists = useSelector(getIsAnyUnlimitRateExists);

    const canActivateTrial = useSelector(canRoleActivateTrialWithCurrentRate);

    const handleClickInfo = () =>
        dispatch(
            setTrialInfoPopupOpen({
                isTrialInfoPopupOpen: true,
                isRoleHasUltimateRate: false,
            }),
        );

    // const showTryTrialButton =
    //     !isAnyUnlimitRateExists && !activeTrialRate && canActivateTrial;

    // Виключили в рамках задачі https://vchasno-group.atlassian.net/browse/EC-49
    // TODO: видалити, якщо за півроку виявиться непотрібним
    // useEffect(() => {
    //     if (showTryTrialButton) {
    //         eventTracking.sendPromoBannerToGTMV4({
    //             campaign: 'try_trial_sidebar',
    //             action: 'shown',
    //         });
    //     }
    // }, [showTryTrialButton]);

    if (isAnyUnlimitRateExists || (!canActivateTrial && !activeTrialRate)) {
        return null;
    }

    if (!activeTrialRate && canActivateTrial) {
        // Виключили в рамках задачі https://vchasno-group.atlassian.net/browse/EC-49
        // TODO: видалити, якщо за півроку виявиться непотрібним
        return null;
        return (
            <BlackTooltip placement="right" title={t`Дізнатися більше`}>
                <FlexBox
                    align="center"
                    gap={8}
                    className={cn(css.root, css.pointer)}
                    onClick={() => {
                        handleClickInfo();

                        eventTracking.sendToGTMV4({
                            event: 'ec_inactive_block_click_sidebar_trial',
                        });

                        eventTracking.sendPromoBannerToGTMV4({
                            campaign: 'try_trial_sidebar',
                            action: 'click',
                        });
                    }}
                >
                    <div className={css.icon}>🚀</div>
                    <h3
                        className={css.headerText}
                    >{t`Спробуйте всі функції сервісу безкоштовно!`}</h3>
                </FlexBox>
            </BlackTooltip>
        );
    }

    if (!activeTrialRate) {
        return null;
    }

    const {
        daysLeft,
        daysSpentPercent,
        isDaysLefLessThenFour,
    } = setTrialProgressBarDays(activeTrialRate);

    const handleCheckRateRedirect = () => {
        history.push({
            pathname: PATH_TO_REDIRECT_FROM_PROGRESS_BAR,
        });

        eventTracking.sendToGTM({
            event: 'choose_tariff_trial',
            category: 'trial_block_sidebar',
        });
    };

    return (
        <FlexBox
            direction="column"
            justify="space-between"
            className={css.root}
        >
            <FlexBox className={css.header}>
                <div className={css.icon}>🚀</div>
                <h3
                    className={css.headerText}
                >{t`Тестовий період активовано!`}</h3>
                <BlackTooltip placement="right" title={t`Дізнатися більше`}>
                    <div
                        onClick={() => {
                            handleClickInfo();
                            eventTracking.sendToGTMV4({
                                event: 'ec_click_block_sidebar_trial',
                            });
                        }}
                        className={css.infoIcon}
                    >
                        <Icon glyph={SvgInfo} />
                    </div>
                </BlackTooltip>
            </FlexBox>
            <DaysLeft daysLeft={daysLeft} progressPercent={daysSpentPercent} />
            {isDaysLefLessThenFour && (
                <Button
                    onClick={() => {
                        handleCheckRateRedirect();

                        eventTracking.sendToGTMV4({
                            event: 'ec_purchase_block_sidebar_trial',
                        });
                    }}
                    size="sm"
                >{t`Обрати тарифний план`}</Button>
            )}
        </FlexBox>
    );
};

export default TrialInfoProgressBar;
