import React, { useEffect, useMemo } from 'react';
import { Controller } from 'react-hook-form';

import {
    FlexBox,
    Select,
    SelectComponents,
    SelectCreatable,
    SelectOption,
    SelectProps,
    Text,
} from '@vchasno/ui-kit';

import CollapsedBar from 'components/CollapsedBar';
import { IDocCategorySelectOption } from 'components/DocCategorySelect/types';
import AmountConditionInputs from 'components/documentTemplates/AmountConditionInputs';
import DocumentCategory from 'components/documentTemplates/DocumentCategory';
import { useCompanyRolesQuery } from 'hooks/useCompanyRolesQuery';
import { EMAIL_PATTERN } from 'lib/constants';
import { formatFullName } from 'lib/helpers';
import { t } from 'ttag';

import { TemplateConditionsFormProps } from './types';

import CollisionTemplateAlert from '../CollisionTemplateAlert';
import { formToConditionListMapper } from './mappers';
import { documentSideOptions, documentSignProcessOptions } from './options';
import { useTemplateConditionsForm } from './useTemplateConditionsForm';

import css from './DocumentTemplateConditionsForm.css';

interface CollapseBoxProps {
    title: string;
    hasValue?: boolean;
    children: React.ReactNode;
}

const CollapseBox: React.FC<CollapseBoxProps> = ({
    title,
    children,
    hasValue = false,
}) => {
    return (
        <CollapsedBar title={title} className={css.collapse} isShow={hasValue}>
            {children}
        </CollapsedBar>
    );
};

const commonComponents: SelectProps['components'] = {
    DropdownIndicator: () => null,
    IndicatorSeparator: () => null,
};

const components: SelectProps['components'] = {
    Option: (props) => {
        const [email, userName] = props.data.label.split(' - ').reverse();

        return (
            // @ts-ignore
            <SelectComponents.Option {...props}>
                <FlexBox justify="space-between" gap={10}>
                    <Text
                        ellipsis
                        style={{
                            flexBasis: 1,
                            flexGrow: 1,
                            flexShrink: 0,
                        }}
                        strong
                    >
                        {userName || ' '}
                    </Text>
                    <Text
                        ellipsis
                        style={{
                            flexBasis: 1,
                            flexGrow: 1,
                            flexShrink: 0,
                        }}
                    >
                        {email}
                    </Text>
                </FlexBox>
            </SelectComponents.Option>
        );
    },
};

const TemplateConditionsForm: React.FC<TemplateConditionsFormProps> = ({
    conditionsList,
    setConditions,
    templateId,
}) => {
    const methods = useTemplateConditionsForm(conditionsList);
    // список всіх користувачів компанії (активні, ті що мають право підпису та ті що не мають)
    const companyRolesQuery = useCompanyRolesQuery({ search: '' });

    const roleListOptions = useMemo(
        () =>
            companyRolesQuery.data?.currentCompanyRoles
                .filter((role) => role.user.email) // Користувачі з телефоном ще не підтримуються
                .map(
                    (role) =>
                        ({
                            label: [formatFullName(role.user), role.user.email]
                                .filter(Boolean)
                                .join(' - '),
                            value: role.user.email,
                            source: role,
                        } as SelectOption),
                ) ?? [],
        [companyRolesQuery.data],
    );

    useEffect(() => {
        return methods.watch(() => {
            setConditions(formToConditionListMapper(methods.getValues()));
        }).unsubscribe;
    }, [methods]);

    return (
        <FlexBox direction="column" gap={20}>
            <CollapseBox
                hasValue={methods.watch('documentSide').length > 0}
                title={t`Вхідний, вихідний, внутрішній`}
            >
                <Controller
                    control={methods.control}
                    name="documentSide"
                    render={({ field }) => {
                        return (
                            <Select
                                components={commonComponents}
                                wide
                                hideEmptyMeta
                                placeholder={t`Виберіть тип документа`}
                                isMulti
                                isClearable
                                isSearchable={false}
                                options={documentSideOptions}
                                value={documentSideOptions.filter((option) =>
                                    field.value.includes(option.value),
                                )}
                                onChange={(newValues: SelectOption[]) => {
                                    field.onChange(
                                        newValues.map((option) => option.value),
                                    );
                                }}
                            />
                        );
                    }}
                />
                <CollisionTemplateAlert
                    condition="#document_side"
                    templateId={templateId}
                    conditionsList={conditionsList}
                />
            </CollapseBox>
            <CollapseBox
                hasValue={methods.watch('documentCategories').length > 0}
                title={t`Тип документа`}
            >
                <Controller
                    control={methods.control}
                    name="documentCategories"
                    render={({ field }) => {
                        return (
                            <DocumentCategory
                                value={field.value}
                                onChange={(
                                    options: IDocCategorySelectOption[],
                                ) => {
                                    field.onChange(
                                        options.map((option) => option.value),
                                    );
                                }}
                                isClearable
                                isMulti
                                conditionsList={conditionsList}
                            />
                        );
                    }}
                />
                <CollisionTemplateAlert
                    condition="#document_category"
                    templateId={templateId}
                    conditionsList={conditionsList}
                />
            </CollapseBox>
            <CollapseBox
                hasValue={methods.watch('documentSignProcess').length > 0}
                title={t`Процес підписання`}
            >
                <Controller
                    control={methods.control}
                    name="documentSignProcess"
                    render={({ field }) => {
                        return (
                            <Select
                                wide
                                placeholder={t`Процес підписання`}
                                components={commonComponents}
                                hideEmptyMeta
                                isMulti
                                isClearable
                                isSearchable={false}
                                options={documentSignProcessOptions}
                                value={documentSignProcessOptions.filter(
                                    (option) =>
                                        field.value.includes(option.value),
                                )}
                                onChange={(newValues: SelectOption[]) => {
                                    field.onChange(
                                        newValues.map((option) => option.value),
                                    );
                                }}
                            />
                        );
                    }}
                />
                <CollisionTemplateAlert
                    condition="#document_sign_process"
                    templateId={templateId}
                    conditionsList={conditionsList}
                />
            </CollapseBox>
            <CollapseBox
                hasValue={methods.watch('counterpartyList').length > 0}
                title={t`ЄДРПОУ/ІПН контрагента`}
            >
                <Controller
                    control={methods.control}
                    name="counterpartyList"
                    render={({ field }) => {
                        return (
                            <SelectCreatable
                                wide
                                components={commonComponents}
                                isClearable
                                placeholder={t`Введіть ЄДРПОУ/ІПН контрагента`}
                                createOptionPosition="first"
                                formatCreateLabel={(label) => {
                                    return t`Додати: "${label}"`;
                                }}
                                hideEmptyMeta
                                isValidNewOption={(newValue) => {
                                    // Check if the new value is a valid EDRPOU or IPN
                                    const edrpouRegex = /^\d{8,10}$/;
                                    const ipnRegex = /^\d{10}$/;
                                    if (
                                        !edrpouRegex.test(newValue) &&
                                        !ipnRegex.test(newValue)
                                    ) {
                                        return false;
                                    }

                                    return newValue.trim().length > 0;
                                }}
                                isMulti
                                value={field.value.map((option) => {
                                    return {
                                        label: option.edrpou,
                                        value: option.edrpou,
                                    } as SelectOption;
                                })}
                                onChange={(newValues: SelectOption[]) => {
                                    field.onChange(
                                        newValues.map((option) => ({
                                            edrpou: option.value,
                                        })),
                                    );
                                }}
                            />
                        );
                    }}
                />
                <CollisionTemplateAlert
                    condition="#document_recipients.edrpou"
                    templateId={templateId}
                    conditionsList={conditionsList}
                />
            </CollapseBox>

            <CollapseBox
                hasValue={methods.watch('employeeUploadedBy').length > 0}
                title={t`Користувачі, що завантажили документ`}
            >
                <Controller
                    control={methods.control}
                    name="employeeUploadedBy"
                    render={({ field }) => {
                        return (
                            <Select
                                loading={companyRolesQuery.isFetching}
                                wide
                                placeholder={t`Введіть email або ім'я користувача`}
                                components={{
                                    ...commonComponents,
                                    ...components,
                                }}
                                isSearchable
                                isClearable
                                hideEmptyMeta
                                isMulti
                                options={roleListOptions}
                                value={roleListOptions.filter((option) => {
                                    return field.value.includes(option.value);
                                })}
                                onChange={(newValues: SelectOption[]) => {
                                    field.onChange(
                                        newValues.map((option) => option.value),
                                    );
                                }}
                            />
                        );
                    }}
                />
                <CollisionTemplateAlert
                    condition="#document_uploaded_by"
                    templateId={templateId}
                    conditionsList={conditionsList}
                />
            </CollapseBox>

            <CollapseBox
                hasValue={methods.watch('employeeRecipients').length > 0}
                title={t`Користувачі, отримувачі документу`}
            >
                <Controller
                    control={methods.control}
                    name="employeeRecipients"
                    render={({ field }) => {
                        return (
                            <SelectCreatable
                                loading={companyRolesQuery.isFetching}
                                wide
                                placeholder={t`Введіть email або ім'я користувача`}
                                components={commonComponents}
                                isSearchable
                                isClearable
                                hideEmptyMeta
                                isMulti
                                isValidNewOption={(email) =>
                                    EMAIL_PATTERN.test(email)
                                }
                                createOptionPosition="first"
                                formatCreateLabel={(label) => {
                                    return t`Додати пошту отримувача: "${label}"`;
                                }}
                                hint={t`Виберіть зі списку або введіть пошту повністю`}
                                options={roleListOptions}
                                value={field.value.map((emailItem) => {
                                    const option = roleListOptions.find(
                                        (item) => item.value === emailItem,
                                    );
                                    // міксуємо обрані опції зі списком ролей компанії та введені вручну емейли
                                    return (
                                        option ?? {
                                            label: emailItem,
                                            value: emailItem,
                                        }
                                    );
                                })}
                                onChange={(newValues: SelectOption[]) => {
                                    field.onChange(
                                        newValues.map((option) => option.value),
                                    );
                                }}
                            />
                        );
                    }}
                />
                <CollisionTemplateAlert
                    condition="#document_recipients.email"
                    templateId={templateId}
                    conditionsList={conditionsList}
                />
            </CollapseBox>

            <CollapseBox
                hasValue={methods.watch('amount') !== ''}
                title={t`Сума документу`}
            >
                <Controller
                    control={methods.control}
                    name="amount"
                    render={({ field }) => {
                        return (
                            <AmountConditionInputs
                                value={field.value}
                                onChange={(inputValue) => {
                                    if (inputValue === '|') {
                                        field.onChange('');
                                        return;
                                    }

                                    field.onChange(inputValue);
                                }}
                            />
                        );
                    }}
                />
                <CollisionTemplateAlert
                    condition="#document_amount"
                    templateId={templateId}
                    conditionsList={conditionsList}
                />
            </CollapseBox>
        </FlexBox>
    );
};

export default TemplateConditionsForm;
