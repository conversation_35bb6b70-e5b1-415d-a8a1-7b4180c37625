import React from 'react';

import cn from 'classnames';

import eventTracking from '../../services/analytics/eventTracking';

import css from './cards.css';

interface Props {
    data: any;
    alignCenter?: boolean;
    sizeSmall?: boolean;
}

interface Card {
    image: string;
    text?: string;
    href?: string;
}

const Cards = ({ data, alignCenter, sizeSmall }: Props) => (
    <div className={css.root}>
        {data.map((card: Card) => {
            const Tag = card.href ? 'a' : 'div';
            const linkProps = {
                href: card.href,
                rel: 'noopener noreferrer',
                target: '_blank',
                onClick: () =>
                    eventTracking.sendEvent(
                        'functions_blocks_click',
                        card.text ?? '',
                    ),
            };
            return (
                <Tag
                    key={card.image}
                    className={cn(css.card, {
                        [css.cardCentered]: alignCenter,
                        [css.cardSizeSmall]: sizeSmall,
                    })}
                    {...(card.href && linkProps)}
                >
                    <img
                        src={`${config.STATIC_HOST}/images/landing/${card.image}`}
                        alt=""
                    />
                    {card.text && <div className={css.text}>{card.text}</div>}
                </Tag>
            );
        })}
    </div>
);

export default Cards;
