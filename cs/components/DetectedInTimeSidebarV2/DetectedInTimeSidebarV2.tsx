import React, { useEffect } from 'react';

import { Button, FlexBox } from '@vchasno/ui-kit';

import eventTracking from 'services/analytics/eventTracking';

import css from './DetectedInTimeSidebarV2.css';

const DetectedInTimeSidebarV2: React.FC = () => {
    useEffect(() => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'shown',
            campaign: 'detected_in_time_sidebar',
        });
    }, []);

    const handleClick = () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'click',
            campaign: 'detected_in_time_sidebar',
        });
    };

    return (
        <FlexBox className={css.container}>
            <div className={css.backgroundTechnique} />
            <FlexBox
                direction="column"
                gap={0}
                style={{ zIndex: 2, width: '100%' }}
            >
                <FlexBox justify="start">
                    <h5 className={css.title}>Вчасно виявлено</h5>
                </FlexBox>
                <FlexBox direction="column" gap={10} className="description">
                    <p>
                        <span style={{ color: '#F44F36' }}>
                            Задонать від 200 грн.
                        </span>{' '}
                        на <br /> комплекси спостереження та <br /> бери участь{' '}
                        <b> у розіграші Iphone&nbsp;16</b>
                    </p>
                </FlexBox>
                <a
                    className={css.buttonWrapper}
                    href="https://send.monobank.ua/jar/5zqUFiMEPF"
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={handleClick}
                >
                    <Button wide>Задонатити</Button>
                </a>
            </FlexBox>
        </FlexBox>
    );
};

export default DetectedInTimeSidebarV2;
