import { UPLOAD_SCAN_DOCS_BUTTON_RELEASE_DATE } from 'components/OnboardingUploadScanDocsButton/constants';
import { BillingCompanyConfig, User } from 'gql-types';
import { DEFAULT_BILLING_COMPANY_CONFIG } from 'services/billing/constants';

import { IUser } from '../../types/user';

export const getIsCurrentUserRegisteredEarlyUploadScanFeature = (
    currentUser: User | IUser,
) => {
    if (!currentUser) {
        return false;
    }

    return (
        new Date(currentUser.dateCreated) <=
        new Date(UPLOAD_SCAN_DOCS_BUTTON_RELEASE_DATE)
    );
};

export const getIsCurrentCompanyHasArchive = (
    billingCompanyConfig: BillingCompanyConfig = DEFAULT_BILLING_COMPANY_CONFIG,
) => {
    if (!billingCompanyConfig) {
        return false;
    }

    const maxArchiveDocumentsCount =
        billingCompanyConfig.maxArchiveDocumentsCount;

    return (
        maxArchiveDocumentsCount === null ||
        (maxArchiveDocumentsCount && maxArchiveDocumentsCount > 0)
    );
};
