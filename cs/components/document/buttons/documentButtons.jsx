import React, { useCallback } from 'react';
import { connect, useSelector } from 'react-redux';

import { BlackTooltip, Button as VchasnoButton } from '@vchasno/ui-kit';

import cn from 'classnames';
import SignButton from 'components/DiiaSigner/SignButton';
import KepSignButton from 'components/KepSigner/KepSignButton';
import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { signWithKepFlowSelectors } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import { isTovCompany } from 'lib/helpers';
import PropTypes from 'prop-types';
import { getIsSharedDocumentViewMode } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { SignatureFormat } from 'services/enums';
import { mapStatetoHasPermission } from 'store/utils';
import { t } from 'ttag';
import Button from 'ui/button/button';

import { useCheckSignPermission } from '../hooks/useCheckSignPermission';
import Agreement from './Agreement';

// styles
import css from './documentButtons.css';

// Обгортка для кнопки підписання, який показує тултіп з причиною, чому кнопка вимкнена
const SignButtonWrapper = ({
    reasonForDisabled,
    ...signButtonWrapperProps
}) => {
    if (reasonForDisabled) {
        return (
            <BlackTooltip
                title={
                    <p style={{ textAlign: 'center' }}>{reasonForDisabled}</p>
                }
            >
                <div {...signButtonWrapperProps} />
            </BlackTooltip>
        );
    }

    return <div {...signButtonWrapperProps} />;
};

const DocumentButtons = (props) => {
    const isSharedDocumentViewMode = useSelector(getIsSharedDocumentViewMode);
    const checkDocSignPermissions = useCheckSignPermission();
    const isKepNewFlowEnabled = useSelector(
        signWithKepFlowSelectors.selectIsFlowEnabled,
    );

    function handleOpenSignPopup() {
        props.onSign();
    }

    function onDiiaSign() {
        props.onSign({
            isDiia: true,
        });
    }

    function onKepSign() {
        props.onSign({
            isKep: true,
        });
    }

    let reasonForDisabled = props.doc.isInternal
        ? checkDocSignPermissions.internalReason
        : checkDocSignPermissions.externalReason;

    if (!reasonForDisabled && props.disableReason) {
        reasonForDisabled = props.disableReason;
    }

    const canSignAndRejectDocument = reasonForDisabled === null;

    const disabled = !canSignAndRejectDocument || props.disabled;

    const wrappedSign = useCallback(() => {
        eventTracking.sendEvent('diia', 'sign_btn_click');
        onDiiaSign();
    }, [onDiiaSign]);

    const {
        EXTERNAL_SEPARATED,
        INTERNAL_SEPARATED,
        INTERNAL_ASIC,
        INTERNAL_WRAPPED,
    } = SignatureFormat;
    const isKepCloudSignEnabled =
        [
            EXTERNAL_SEPARATED,
            ...(props.featureFlags
                .ENABLE_KEP_SIGN_FOR_INTERNAL_SEPARATED_SIGNATURE_FORMAT
                ? [INTERNAL_SEPARATED, INTERNAL_ASIC, INTERNAL_WRAPPED]
                : []),
        ].includes(props.doc.expectedSignatureFormat) &&
        !isSharedDocumentViewMode;

    function onKepNewSign() {
        if (isKepCloudSignEnabled) {
            eventTracking.sendToGTMV4({
                event: KEP_SIGN_FLOW_ANALYTICS_EVENT.SIGN_DOC_PAGE,
            });
            props.onSign({
                isKepNew: true,
            });
        } else {
            props.onSign();
        }
    }

    const className = cn(css.button, props.className);

    if (isKepNewFlowEnabled) {
        return (
            <div>
                {isSharedDocumentViewMode && <Agreement />}

                <SignButtonWrapper
                    reasonForDisabled={reasonForDisabled}
                    className={className}
                >
                    <VchasnoButton
                        theme="pink"
                        wide
                        size="lg"
                        disabled={disabled}
                        onClick={onKepNewSign}
                    >
                        {props.signButtonText}
                    </VchasnoButton>
                </SignButtonWrapper>
            </div>
        );
    }

    return (
        <div>
            {isSharedDocumentViewMode && <Agreement />}
            <SignButtonWrapper
                reasonForDisabled={reasonForDisabled}
                className={className}
            >
                <Button
                    disabled={disabled}
                    theme="cta"
                    width="full"
                    onClick={handleOpenSignPopup}
                    dataQa="qa_sign_and_send_button"
                >
                    {props.signButtonText}
                </Button>
            </SignButtonWrapper>
            {!isTovCompany(props.currentCompany?.edrpou) && (
                <SignButtonWrapper
                    reasonForDisabled={reasonForDisabled}
                    className={className}
                >
                    <SignButton
                        disabled={disabled}
                        onSign={wrappedSign}
                        buttonTitle={t`Підписати Дія.Підписом`}
                    />
                </SignButtonWrapper>
            )}
            {isKepCloudSignEnabled && (
                <SignButtonWrapper
                    reasonForDisabled={reasonForDisabled}
                    className={className}
                >
                    <KepSignButton
                        width="full"
                        disabled={disabled}
                        onSign={onKepSign}
                        buttonTitle={t`Підписати Вчасно.КЕП`}
                    />
                </SignButtonWrapper>
            )}
            {isSharedDocumentViewMode && (
                <div className={className}>
                    <a
                        href="https://cap.vchasno.com.ua"
                        target="_blank"
                        rel="noreferrer noopener"
                    >
                        <Button width="full" theme="pink">
                            {t`Підписати Вчасно.КЕП`}
                        </Button>
                    </a>
                </div>
            )}
        </div>
    );
};

DocumentButtons.propTypes = {
    hasPermission: PropTypes.func,
    disabled: PropTypes.bool,
    disableReason: PropTypes.string,
    doc: PropTypes.object,
    currentCompany: PropTypes.object,
    signButtonText: PropTypes.string,
    onSign: PropTypes.func.isRequired,
    className: PropTypes.string,
};

const mapStateToProps = (state) => ({
    ...mapStatetoHasPermission(state),
    currentCompany: state.app.currentUser?.currentCompany,
    featureFlags: state.app.flags,
});

export default connect(mapStateToProps)(DocumentButtons);
