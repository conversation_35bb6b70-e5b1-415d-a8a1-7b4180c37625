import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@vchasno/ui-kit';

import { useDocument } from 'components/document/Variant2025/useDocument';
import documentActionCreators from 'components/document/documentActionCreators';
import {
    getDocumentRecipients,
    getDocumentVersionByLocationSelector,
    getIsDocumentActualVersionPageSelector,
    getIsVersionedDocumentFlowSelector,
} from 'selectors/document.selectors';
import { DocumentReviewStatus } from 'services/enums';
import { t } from 'ttag';

const SendVersionButton: React.FC = () => {
    const doc = useDocument();
    const dispatch = useDispatch();
    const documentVersion = useSelector(getDocumentVersionByLocationSelector);
    const isActualDocumentVersion = useSelector(
        getIsDocumentActualVersionPageSelector,
    );
    const isVersionedDocumentFlow = useSelector(
        getIsVersionedDocumentFlowSelector,
    );
    const recipients = useSelector(getDocumentRecipients);

    const isShowSendButton =
        isActualDocumentVersion &&
        !documentVersion?.isSent &&
        recipients.length > 0;

    const isDisabledSendButton = Boolean(
        doc.reviewSetting?.isRequired &&
            documentVersion?.reviewStatus &&
            documentVersion?.reviewStatus !== DocumentReviewStatus.APPROVED,
    );

    if (!isVersionedDocumentFlow) {
        return null;
    }

    if (!isShowSendButton) {
        return null;
    }

    const onSend = () => {
        dispatch(documentActionCreators.onSendDocument());
    };

    return (
        <Button
            theme="primary"
            wide
            onClick={onSend}
            disabled={isDisabledSendButton}
        >
            {t`Надіслати версію`}
        </Button>
    );
};

export default SendVersionButton;
