import React from 'react';

import { Al<PERSON>, BlackTooltip, FlexBox, Menu, Text } from '@vchasno/ui-kit';

import { useDocument } from 'components/document/Variant2025/useDocument';
import { useDocumentActions } from 'components/document/useDocumentActions';
import { t } from 'ttag';
import Icon from 'ui/icon';

import { useShowDeleteRequestAlert } from './useShowDeleteRequestAlert';

import DotsSvg from '../../../../../icons/dots.svg';

const DeleteRequestAlert: React.FC = () => {
    const doc = useDocument();
    const isShow = useShowDeleteRequestAlert();
    const documentAction = useDocumentActions();

    // тут потрібно розділити умови для ініціатора та для отримувач а також врахувати мультисторонні документи
    if (!isShow || !doc.deleteRequest) {
        return null;
    }

    if (doc.deleteRequest.isReceiver) {
        return (
            <FlexBox gap={5}>
                <Alert type="error">
                    {t`Контрагент надіслав запит на видалення документу.`}{' '}
                    <BlackTooltip
                        title={doc.deleteRequest.message}
                        disableInteractive
                    >
                        <Text type="likeLink">{t`Коментар`}</Text>
                    </BlackTooltip>
                </Alert>
                <Menu
                    menuButton={
                        <Menu.Button>
                            <Icon
                                glyph={DotsSvg}
                                style={{ width: 25, height: 25 }}
                            />
                        </Menu.Button>
                    }
                >
                    {doc.isMultilateral &&
                    doc.deleteRequest.status === 'rejected' ? (
                        <Menu.Item
                            key="multilateral_rejected"
                            onClick={() =>
                                documentAction.onCancelDeleteRequestVote([doc])
                            }
                        >
                            {t`Відхилити запит`}
                        </Menu.Item>
                    ) : (
                        <Menu.Item
                            key="rejected"
                            onClick={() => {
                                documentAction.onRejectDeleteRequest([doc]);
                            }}
                        >
                            {t`Вiдхилити запит на видалення`}
                        </Menu.Item>
                    )}
                    {doc.isMultilateral &&
                    doc.deleteRequest.status === 'accepted' ? (
                        <Menu.Item
                            key="multilateral_accepted"
                            onClick={() =>
                                documentAction.onCancelDeleteRequestVote([doc])
                            }
                        >
                            {t`Погодити видалення`}
                        </Menu.Item>
                    ) : (
                        <Menu.Item
                            key="accepted"
                            onClick={() =>
                                documentAction.onAcceptDeleteRequest([doc])
                            }
                        >
                            {t`Погодити видалення`}
                        </Menu.Item>
                    )}
                </Menu>
            </FlexBox>
        );
    }

    return (
        <Alert type="warning">
            {t`Ви надіслали запит на видалення документу`}
        </Alert>
    );
};

export default DeleteRequestAlert;
