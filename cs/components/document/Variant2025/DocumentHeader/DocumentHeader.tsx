import React from 'react';

import { FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';

import AnnulmentBlock from './AnnulmentBlock';
import ApprovalButtons from './ApprovalButtons';
import { BackButton } from './BackButton';
import DeleteRequestAlert from './DeleteRequestAlert';
import DocTitle from './DocTitle';
import { Logo } from './Logo';
import MainSignButton from './MainSignButton';
import RejectButton from './RejectButton';
import SendVersionButton from './SendVersionButton';
import { StatusBadge } from './StatusBadge';
import { SupportIcon } from './SupportIcon';
import VersionAlert from './VersionAlert';
import { VersionBadge } from './VersionBadge';
import { useShowHeaderAlert } from './useShowHeaderAlert';

import css from './DocumentHeader.css';

interface DocumentHeaderProps {
    className?: string;
    title?: boolean;
    children?: React.ReactNode;
}

/**
 * Для того, щоб забрати вільний простір та прижати до країв блоки
 */
const EmptySize = () => {
    return <span className={css.emptyContainer} />;
};

/**
 * Для того, щоб групувати кнопки в шапці
 */
const ButtonGroup: React.FC = ({ children }) => {
    const isShowSomeAlertInHeader = useShowHeaderAlert();

    // Для того, щоб приховати коли показуємо якийсь з алертів в шапці
    if (isShowSomeAlertInHeader) {
        return null;
    }

    return (
        <FlexBox className={css.buttonGroup} gap={12} align="center" shrink={1}>
            {children}
        </FlexBox>
    );
};
/**
 * Для того, щоб розташуватися в грід системі DocumentLayout
 */
const DocumentHeader = ({ className, children }: DocumentHeaderProps) => {
    return (
        <div data-area="header" className={cn(css.root, className)}>
            {children}
        </div>
    );
};

DocumentHeader.Logo = Logo;
DocumentHeader.BackButton = BackButton;
DocumentHeader.DocTitle = DocTitle;
DocumentHeader.StatusBadge = StatusBadge;
DocumentHeader.VersionBadge = VersionBadge;
DocumentHeader.SupportIcon = SupportIcon;
// Допоміжні компоненти для групування кнопок, вирівнювання та приховування
DocumentHeader.ButtonGroup = ButtonGroup;
DocumentHeader.EmptySize = EmptySize;
// Кнопки дій з документом
DocumentHeader.MainSignButton = MainSignButton;
DocumentHeader.RejectButton = RejectButton;
DocumentHeader.ApprovalButtons = ApprovalButtons;
DocumentHeader.SendVersionButton = SendVersionButton;
// Алерти в шапці - вони мають бути видимі завжди і приховувати кнопки
DocumentHeader.AnnulmentBlock = AnnulmentBlock;
DocumentHeader.VersionAlert = VersionAlert;
DocumentHeader.DeleteRequestAlert = DeleteRequestAlert;

export default DocumentHeader;
