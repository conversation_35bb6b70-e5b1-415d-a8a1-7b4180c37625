import React from 'react';

import VersionLabel from 'components/VersionLabel/VersionLabel';
import { useSidebarContext } from 'components/document/Variant2025/MainDocumentSidebar/context';
import { useDocument } from 'components/document/Variant2025/useDocument';

import css from './VersionBadge.css';

/**
 * Іконка версії - показуємо у випадку, коли документ зробили версійним. Відображаємо загальну кількість версій.
 * По кліку на неї переводимо в табу версійності.
 */
export const VersionBadge = () => {
    const doc = useDocument();
    const { setActiveTabKey } = useSidebarContext();

    return (
        <VersionLabel
            doc={doc}
            className={css.root}
            hovered
            onClick={() => {
                setActiveTabKey('versions');
            }}
        />
    );
};
