import { useSelector } from 'react-redux';

import { useShowDeleteRequestAlert } from 'components/document/Variant2025/DocumentHeader/DeleteRequestAlert/useShowDeleteRequestAlert';
import { useShowVersionAlert } from 'components/document/Variant2025/DocumentHeader/VersionAlert/useShowVersionAlert';
import { getIsAnnulmentActProcess } from 'selectors/document.selectors';

// Шапка сайту має складну логіку щодо підсвічування найбільш важливої інформації
// Також там відображаються основні кнопки для роботи з документом
// Через це коли показуємо Алерт то приберемо
export const useShowHeaderAlert = () => {
    return [
        // Показуємо Алерт, якщо це процес анулювання акту
        useSelector(getIsAnnulmentActProcess),
        // для версійних документів
        useShowVersionAlert(),
        // Якщо є запит на видалення показуємо відповідний Алерт
        useShowDeleteRequestAlert(),
        // додаємо інші умови, якщо потрібно
    ].some((isActive) => isActive);
};
