import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

import {
    BlackTooltip,
    FlexBox,
    TextInput,
    Title,
    snackbarToast,
} from '@vchasno/ui-kit';

import cn from 'classnames';
import { canEditDocumentTitle } from 'components/DocumentEdit/utils';
import EditIcon from 'components/document/Variant2025/EditIcon';
import { useDocument } from 'components/document/Variant2025/useDocument';
import { useDocumentUpdateMutation } from 'components/document/Variant2025/useDocumentUpdateMutation';
import actions from 'components/document/documentActions';
import { useTextHasEllipsis } from 'hooks/useTextHasEllipsis';
import { getCurrentUser } from 'selectors/app.selectors';
import { t } from 'ttag';

import { DocTitleForm, formResolver } from './schema';

import css from './DocTitle.css';

interface DocTitleProps {
    editable?: boolean;
}

/**
 * Назва документа – відображаємо назву документа. Якщо довга – ховаємо кінець назви під “…”.
 * По ховеру показуємо іконку редагування і при натиску даємо редагувати назву.
 */
export const DocTitle = ({ editable = false }: DocTitleProps) => {
    const dispatch = useDispatch();
    const [editing, setEditing] = React.useState(false);
    const doc = useDocument();
    const user = useSelector(getCurrentUser);
    const { textRef, isEllipsis } = useTextHasEllipsis([doc.title, editing]);
    const documentMutation = useDocumentUpdateMutation();
    const methods = useForm<DocTitleForm>({
        defaultValues: {
            title: doc.title || '',
        },
        resolver: formResolver,
        mode: 'onSubmit',
    });

    const canEditTitle = editable && user && canEditDocumentTitle(doc, user);

    const onSubmit = async (data: DocTitleForm) => {
        if (data.title === doc.title) {
            setEditing(false);
            return;
        }

        await documentMutation.mutateAsync(
            [
                doc.id,
                {
                    document_settings: {
                        title: data.title,
                    },
                },
            ],
            {
                onSuccess: () => {
                    snackbarToast.success(t`Назву документа змінено`);
                    // оновлюємо точково стан в редаксі - уникаючи рефетчу всього документа
                    dispatch({
                        type: actions.DOCUMENT__PATCH_TITLE,
                        payload: data.title,
                    });
                },
                onError: () => {
                    snackbarToast.error(t`Не вдалося змінити назву документа`);
                },
                onSettled: () => {
                    methods.reset({
                        title: data.title,
                    });
                    setEditing(false);
                },
            },
        );
    };

    if (editing) {
        return (
            <form onSubmit={methods.handleSubmit(onSubmit)}>
                <Controller
                    name="title"
                    control={methods.control}
                    render={({ field }) => (
                        <TextInput
                            autoFocus
                            className={css.input}
                            loading={methods.formState.isSubmitting}
                            hideEmptyMeta
                            {...field}
                            onBlur={() => {
                                setEditing(false);
                                methods.reset({
                                    title: doc.title,
                                });
                            }}
                        />
                    )}
                />
            </form>
        );
    }

    const handleClick = () => {
        if (!editable) {
            return;
        }

        // перевіряємо чи користувач має право редагувати назву документа
        if (canEditTitle) {
            setEditing(true);
        }
    };

    return (
        <FlexBox
            gap={0}
            shrink={0}
            className={cn(css.root, {
                [css.editableTitleContainer]: canEditTitle,
            })}
        >
            <BlackTooltip
                title={isEllipsis ? doc.title : ''}
                disableInteractive
            >
                <Title
                    ref={textRef as React.RefObject<HTMLHeadingElement>}
                    onClick={handleClick}
                    className={css.root}
                    ellipsis
                    level={3}
                >
                    {doc.title}
                </Title>
            </BlackTooltip>
            {canEditTitle && (
                <BlackTooltip
                    title={t`Змінити назву документа`}
                    disableInteractive
                >
                    <EditIcon onClick={handleClick} className={css.editIcon} />
                </BlackTooltip>
            )}
        </FlexBox>
    );
};

export default DocTitle;
