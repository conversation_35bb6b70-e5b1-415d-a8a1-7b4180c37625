.root,
.input :global(.vchasno-ui-input__wrapper) input {
    max-width: 200px;
}


.input :global(.vchasno-ui-input__wrapper) {
    min-height: 40px;
    padding: 0;
}

.input :global(.vchasno-ui-input__wrapper) input {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
}

.editableTitleContainer {
    margin-right: -20px;
    cursor: pointer;
}

.editableTitleContainer .editIcon {
    left: -2px;
    opacity: 0.1;
    transition: opacity var(--transition-duration-sec);
}

.editableTitleContainer:hover .editIcon {
    opacity: 1;
}
