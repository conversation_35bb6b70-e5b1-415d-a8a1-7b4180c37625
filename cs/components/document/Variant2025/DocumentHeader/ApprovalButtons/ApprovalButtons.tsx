import React from 'react';
import { useSelector } from 'react-redux';

import { BlackTooltip, FlexBox, Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import { useDocumentActions } from 'components/document/useDocumentActions';
import {
    getIsDocumentActualVersionPageSelector,
    getRoleApproveState,
} from 'selectors/document.selectors';
import { t } from 'ttag';

import approvePng from './assets/approve-emoji.png';
import rejectPng from './assets/reject-emoji.png';

import css from './ApprovalButtons.css';

export interface ApprovalButtonsProps {
    className?: string;
}

const ApprovalButtons: React.FC<ApprovalButtonsProps> = ({ className }) => {
    const isActualVersionShow = useSelector(
        getIsDocumentActualVersionPageSelector,
    );
    const approveState = useSelector(getRoleApproveState);
    const documentActions = useDocumentActions();
    // Якщо не очікується погодження, не показуємо кнопки
    if (
        !approveState.isApprove &&
        !approveState.isReject &&
        !approveState.isWaiting
    ) {
        return null;
    }

    // Не показуємо кнопки, якщо це не актуальна версія документа
    if (!isActualVersionShow) {
        return null;
    }

    const handleApproveClick = () => {
        if (approveState.isApprove) {
            documentActions.onDeleteReview();
        } else {
            documentActions.onReview('approve');
        }
    };

    const handleRejectClick = () => {
        if (approveState.isReject) {
            documentActions.onDeleteReview();
        } else {
            documentActions.onReview('reject');
        }
    };

    const isPrevReviewRequestNotCompleted =
        approveState.isWaiting && !approveState.isRoleCanReviewByRequest;

    const composeTooltipText = (variant: 'approve' | 'reject') => {
        if (isPrevReviewRequestNotCompleted) {
            return (
                <p
                    style={{ textAlign: 'center' }}
                >{t`Документ не можливо погодити, оскільки зараз не ваша черга погодження документу`}</p>
            );
        }

        if (variant === 'approve') {
            return approveState.isApprove
                ? t`Зняти погодження`
                : t`Погодити документ`;
        }

        if (variant === 'reject') {
            return approveState.isReject
                ? t`Зняти відхилення`
                : t`Відхилити документ`;
        }

        return '';
    };

    return (
        <FlexBox align="center" className={cn(css.root, className)}>
            <Text className={css.label}>
                {approveState.isApprove && t`Погоджено вами`}
                {approveState.isReject && t`Відхилено вами`}
                {approveState.isWaiting && t`Очікує вашого погодження`}
            </Text>
            <FlexBox align="center" className={css.btnContainer} gap={2}>
                <BlackTooltip
                    title={composeTooltipText('approve')}
                    disableInteractive
                    placement="bottom"
                >
                    <button
                        disabled={isPrevReviewRequestNotCompleted}
                        onClick={handleApproveClick}
                        type="button"
                        className={cn(css.btn, css.approveBtn, {
                            [css.active]: approveState.isApprove,
                        })}
                    >
                        <img src={approvePng} alt="appprove" />
                    </button>
                </BlackTooltip>
                <BlackTooltip
                    title={composeTooltipText('reject')}
                    disableInteractive
                    placement="bottom"
                >
                    <button
                        disabled={isPrevReviewRequestNotCompleted}
                        onClick={handleRejectClick}
                        type="button"
                        className={cn(css.btn, css.rejectBtn, {
                            [css.active]: approveState.isReject,
                        })}
                    >
                        <img src={rejectPng} alt="reject" />
                    </button>
                </BlackTooltip>
            </FlexBox>
        </FlexBox>
    );
};

export default ApprovalButtons;
