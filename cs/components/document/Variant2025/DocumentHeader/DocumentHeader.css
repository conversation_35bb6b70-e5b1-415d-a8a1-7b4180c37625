.root {
    display: flex;
    height: 60px;
    box-sizing: border-box;
    align-items: center;
    padding: 10px 20px;
    background-color: var(--white-bg);
    box-shadow: 0 1px 1px 0 var(--default-border);
    gap: 20px;
}

.logo {
    display: block;
    width: 40px;
    aspect-ratio: 1 / 1;
    cursor: pointer;
}

.statusText {
    font-size: 12px;
}

.backButton {
    flex-shrink: 0;
}

.buttonGroup:empty {
    display: none;
}

.emptyContainer {
    flex-basis: 0;
    flex-grow: 1;
    flex-shrink: 1;
}
