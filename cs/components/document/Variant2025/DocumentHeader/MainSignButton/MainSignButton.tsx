import React from 'react';
import { useSelector } from 'react-redux';

import { useDocument } from 'components/document/Variant2025/useDocument';
import DocumentButtons from 'components/document/buttons/documentButtons';
import { useDocumentActions } from 'components/document/useDocumentActions';
import { getRoleApproveState } from 'selectors/document.selectors';

import { useCanCompanySignDocumentOrVersion } from '../useCanCompanySignDocumentOrVersion';
import { useDisableSignReason } from './useDisableSignReason';
import { useSignBtnText } from './useSignBtnText';

import css from './MainSignButton.css';

/**
 * Кнопка “Підписати” – перехід до підписання документів.
 * Відображаємо кнопку неактивною, якщо у користувача немає дозволу на відхилення/підписання документа (або не його черга підписувати).
 */
const MainSignButton: React.FC = () => {
    const documentActions = useDocumentActions();
    const doc = useDocument();
    const signButtonText = useSignBtnText();
    const canCompanySignDocument = useCanCompanySignDocumentOrVersion();
    const disableReason = useDisableSignReason();
    const approveState = useSelector(getRoleApproveState);

    // Не показувати кнопку підписання для архівних документів
    if (doc.isArchived) {
        return null;
    }

    // Не показувати кнопку підписання, якщо компанія не може підписати документ
    if (!canCompanySignDocument) {
        return null;
    }

    // Не показуємо кнопку підписання, якщо користувач має погодити документ
    if (approveState.isWaiting) {
        return null;
    }

    return (
        <DocumentButtons
            className={css.root}
            disabled={disableReason !== null}
            disableReason={disableReason}
            doc={doc}
            signButtonText={signButtonText}
            onSign={documentActions.onSign}
        />
    );
};

export default MainSignButton;
