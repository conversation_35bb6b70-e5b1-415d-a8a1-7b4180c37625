import { useSelector } from 'react-redux';

import { useDocument } from 'components/document/Variant2025/useDocument';
import { getDocumentLocationVersionId } from 'selectors/router.selectors';
import { isReadyToSignAfterReview } from 'services/documents/utils';
import { t } from 'ttag';

import { isSomebodyRejectDocumentReview } from './utils';

export const useDisableSignReason = () => {
    const doc = useDocument();
    const documentVersionId =
        useSelector(getDocumentLocationVersionId) || doc.versions[0]?.id;

    if (!isReadyToSignAfterReview(doc, documentVersionId)) {
        if (isSomebodyRejectDocumentReview(doc)) {
            return t`Документ не можливо підписати, так як один із співробітників відхилив його під час внутрішнього погодження`;
        }

        return t`Документ можна буде підписати після завершення внутрішнього погодження`;
    }

    return null;
};
