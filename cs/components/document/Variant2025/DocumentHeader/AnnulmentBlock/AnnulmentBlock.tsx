import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Alert, BlackTooltip, Paragraph, Text } from '@vchasno/ui-kit';

import { downloadAnnulmentAct } from 'components/AnnulmentAct/AnnulmentActBlock/utils';
import { useIsShowAnnulmentAct } from 'components/AnnulmentAct/hooks/useIsShowAnnulmentAct';
import { useDocument } from 'components/document/Variant2025/useDocument';
import documentAction from 'components/document/documentActionCreators';
import {
    getAnnulmentAct,
    getIsCompletedAnnulmentAct,
    getIsCurrentCompanyInitiatorAnnulmentAct,
    getIsOwnerSignAnnulmentAct,
} from 'selectors/document.selectors';
import { openApproveAnnulmentActPopup } from 'store/annulmentAct';
import { t } from 'ttag';

import css from './AnnulmentBlock.css';

const AnnulmentBlock: React.FC = () => {
    const dispatch = useDispatch();
    const doc = useDocument();
    const isInitiator = useSelector(getIsCurrentCompanyInitiatorAnnulmentAct);
    const isInitiatorSign = useSelector(getIsOwnerSignAnnulmentAct);
    const annulmentAct = useSelector(getAnnulmentAct);
    const isCompleted = useSelector(getIsCompletedAnnulmentAct);
    const { isShowAnnulmentAct } = useIsShowAnnulmentAct([doc]);

    if (!isShowAnnulmentAct) {
        return null;
    }

    if (isCompleted) {
        return (
            <Alert className={css.root} type="error">
                <Paragraph ellipsis>
                    {t`Анульовано`}{' '}
                    <Text
                        type="link"
                        onClick={async () => {
                            await downloadAnnulmentAct(doc.id);
                        }}
                    >{t`Завантажити Акт`}</Text>
                </Paragraph>
            </Alert>
        );
    }

    // Показуємо для компанії ініціатора
    if (isInitiator) {
        // Якщо вже підписали
        if (isInitiatorSign) {
            return (
                <Alert className={css.root} type="error">
                    <Text
                        strong
                    >{t`Ваш співробітник ініціював Анулювання.`}</Text>{' '}
                    <Text>{t`Очікується підпис контрагента`}</Text>
                </Alert>
            );
        }
        // Якщо ще не підписали
        if (!isInitiatorSign) {
            return (
                <Alert className={css.root} type="error">
                    <Text>{t`Вами ініційовано Акт Анулювання`}</Text>{' '}
                    <Text
                        type="link"
                        onClick={() => {
                            dispatch(
                                documentAction.onSign({
                                    isSignActAnnulment: true,
                                }),
                            );
                        }}
                    >{t`Підписати`}</Text>
                </Alert>
            );
        }
    }

    // Показуємо для компанії опонента

    // Якщо уже підписано ініціатором
    if (isInitiatorSign) {
        return (
            <Alert className={css.root} type="error">
                {t`Ваш контрагент ініціював Акт Анулювання.`}{' '}
                <Text
                    type="link"
                    onClick={() => {
                        dispatch(
                            openApproveAnnulmentActPopup({
                                annulmentActID: annulmentAct.id,
                                documentID: doc.id,
                                documentTitle: doc.title,
                                reasonAnnulment: annulmentAct.reason,
                            }),
                        );
                    }}
                    strong
                >{t`Переглянути і підписати`}</Text>
            </Alert>
        );
    }

    // якщо Акт тільки створений, але ще не підписаний
    return (
        <Alert className={css.root} type="error">
            {t`Ваш контрагент ініціював Акт Анулювання, але ще не підписав його`}{' '}
            <BlackTooltip
                title={
                    <p>
                        {t`Акт створено:`} {annulmentAct.initiatorRole.email}
                        <br />
                        {t`Причина анулювання:`}{' '}
                        {annulmentAct.reason || t`Не вказано`}
                    </p>
                }
            >
                <Text type="link">{t`Деталі`}</Text>
            </BlackTooltip>
        </Alert>
    );
};

export default AnnulmentBlock;
