import React from 'react';

import { Alert } from '@vchasno/ui-kit';

import { useDocument } from 'components/document/Variant2025/useDocument';
import { t } from 'ttag';

import { isLastVersionSendToCounterparty } from './utils';

import { useShowVersionAlert } from './useShowVersionAlert';

const VersionAlert: React.FC = () => {
    const doc = useDocument();
    const isShow = useShowVersionAlert();

    if (!isShow) {
        return null;
    }

    if (isLastVersionSendToCounterparty(doc)) {
        return <Alert type="info">{t`Очікуємо дій від контрагента`}</Alert>;
    }

    return null;
};

export default VersionAlert;
