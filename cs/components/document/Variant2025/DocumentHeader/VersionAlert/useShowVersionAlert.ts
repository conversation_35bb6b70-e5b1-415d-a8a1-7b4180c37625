import { useDocument } from 'components/document/Variant2025/useDocument';
import { getIsVersionedDocumentFlow } from 'services/documents/ts/utils';

import { isLastVersionSendToCounterparty } from './utils';

export const useShowVersionAlert = () => {
    const doc = useDocument();
    const isVersioned = getIsVersionedDocumentFlow(doc);

    if (!isVersioned) {
        return false;
    }

    // якщо остання версія направлена контрагенту на підписаня - ми очікуємо дію від нього
    return isLastVersionSendToCounterparty(doc);
};
