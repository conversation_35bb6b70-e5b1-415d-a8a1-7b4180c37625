import React from 'react';
import { useHistory } from 'react-router-dom';

import { BlackTooltip } from '@vchasno/ui-kit';

import { t } from 'ttag';
import UIBackButton from 'ui/BackButton/BackButton';

import { DEFAULT_BACK_ROUTE } from './constants';

import css from './DocumentHeader.css';

/**
 * Кнопка “Назад” повертає користувача на попередню сторінку сервісу
 * або на головну, якщо немає історії.
 */
export const BackButton = () => {
    const history = useHistory();

    return (
        <BlackTooltip title={t`Назад`} disableInteractive>
            <span className={css.backButton}>
                <UIBackButton
                    variant="rounded"
                    onClick={() => {
                        if (history.length > 1) {
                            history.goBack();
                        } else {
                            history.push(DEFAULT_BACK_ROUTE);
                        }
                    }}
                />
            </span>
        </BlackTooltip>
    );
};
