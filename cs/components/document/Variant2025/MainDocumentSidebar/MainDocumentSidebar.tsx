import React from 'react';

import { BlackTooltip, FlexBox, Title } from '@vchasno/ui-kit';

import cn from 'classnames';
import Icon from 'ui/icon';

import { useSidebarContext } from './context';

import css from './MainDocumentSidebar.css';

export interface MainDocumentSidebarProps {
    className?: string;
}

const MainDocumentSidebar = ({ className }: MainDocumentSidebarProps) => {
    const { tabs, activeTab, setActiveTabKey } = useSidebarContext();

    return (
        <div data-area="sidebar" className={cn(css.root, className)}>
            <div className={css.tabs}>
                {tabs.map((tab) => (
                    <BlackTooltip
                        key={tab.key}
                        title={tab.tooltip || tab.title}
                        disableInteractive
                        placement="left"
                    >
                        <div
                            onClick={() => setActiveTabKey(tab.key)}
                            className={cn(css.tabBtn, {
                                [css.active]: activeTab?.key === tab.key,
                            })}
                        >
                            <Icon glyph={tab.icon} className={css.tabIcon} />
                            {tab.badge && (
                                <span className={css.badgeContainer}>
                                    {tab.badge}
                                </span>
                            )}
                        </div>
                    </BlackTooltip>
                ))}
            </div>
            {activeTab && (
                <FlexBox direction="column" grow={1}>
                    <div className={css.header}>
                        <Title className={css.title} level={4}>
                            {activeTab.title}
                        </Title>
                    </div>
                    <div className={css.scrollable}>{activeTab.content}</div>
                </FlexBox>
            )}
        </div>
    );
};

export default MainDocumentSidebar;
