.root {
    position: sticky;
    top: 0;
    display: flex;
    height: calc(100vh - var(--header-height));
    min-height: 700px;
    background-color: var(--white-bg);
}

.tabs {
    display: flex;
    width: 64px;
    box-sizing: border-box;
    flex-direction: column;
    padding: 20px 12px;
    border-right: 1px solid var(--default-border);
    gap: 8px;
}

.tabBtn {
    display: flex;
    width: 40px;
    height: 40px;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s;
}

.tabBtn.active,
.tabBtn:hover {
    background-color: var(--hover-bg);

}

.tabBtn.active > svg,
.tabBtn:hover > svg {
    color: var(--content-color)
}

.tabBtn > svg {
    width: 20px;
    height: 20px;
    color: var(--grey-color);
    pointer-events: none;
    transition: color 0.3s;
}

.header {
    box-sizing: border-box;
    padding: 30px 24px 12px 24px;
    border-bottom: 1px solid var(--default-border);
}

.title {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.25;
}

.scrollable {
    position: relative;
    z-index: 1;
    box-sizing: border-box;
}

