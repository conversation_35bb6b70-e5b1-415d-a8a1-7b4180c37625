import React from 'react';

import DocumentHeader from 'components/document/Variant2025/DocumentHeader';
import DocumentLayout from 'components/document/Variant2025/DocumentLayout';
import DocumentToolbarBox from 'components/document/Variant2025/DocumentToolbarBox';
import DocumentViewerBox from 'components/document/Variant2025/DocumentViewerBox';
import HtmlHeadTitle from 'components/document/Variant2025/HTMLHeadTitle/HTMLHeadTitle';
import MainDocumentSidebar from 'components/document/Variant2025/MainDocumentSidebar';
import SidebarProvider from 'components/document/Variant2025/MainDocumentSidebar/context';
import { useDocument } from 'components/document/Variant2025/useDocument';
import DocumentViewer from 'components/documentViewer/documentViewer';

import { tabs } from './tabs';

const EDISignSessionView: React.FC = () => {
    const doc = useDocument();

    return (
        <DocumentLayout>
            <HtmlHeadTitle />
            <DocumentHeader>
                <DocumentHeader.Logo />
                <DocumentHeader.DocTitle />
                <DocumentHeader.EmptySize />
                <DocumentHeader.ButtonGroup>
                    <DocumentHeader.MainSignButton />
                </DocumentHeader.ButtonGroup>
                <DocumentHeader.AnnulmentBlock />
            </DocumentHeader>
            <DocumentToolbarBox>
                <DocumentToolbarBox.PrintIcon />
                <DocumentToolbarBox.CreateAnnulmentActIcon />
                <DocumentToolbarBox.DownloadSignSummaryIcon />
            </DocumentToolbarBox>
            <DocumentViewerBox>
                <DocumentViewer doc={doc} />
            </DocumentViewerBox>
            <MainDocumentSidebar />
        </DocumentLayout>
    );
};

export default () => {
    return (
        <SidebarProvider tabs={tabs}>
            <EDISignSessionView />
        </SidebarProvider>
    );
};
