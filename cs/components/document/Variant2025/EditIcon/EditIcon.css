.root {
    position: relative;
    top: 3px;
    display: inline-block;
    width: 20px;
    height: 20px;
    cursor: pointer;
    opacity: 0;
    transition: opacity var(--transition-duration-sec);
}

.root svg {
    width: 100%;
    height: 100%;
    color: var(--grey-color);
    transition: color var(--transition-duration-sec);
}

.root:hover svg {
    color: var(--content-color);
}

*:hover > .root {
    opacity: 0.8;
}
