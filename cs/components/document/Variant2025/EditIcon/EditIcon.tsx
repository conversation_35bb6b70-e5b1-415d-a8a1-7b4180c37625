import React from 'react';

import cn from 'classnames';
import Icon from 'ui/icon';

import EditSVG from './edit.svg';

import css from './EditIcon.css';

export interface EditIconProps extends React.HTMLAttributes<HTMLSpanElement> {
    className?: string;
}

const EditIcon = React.forwardRef<HTMLSpanElement, EditIconProps>(
    ({ className, ...rest }, ref) => {
        return (
            <span className={cn(css.root, className)} {...rest} ref={ref}>
                <Icon glyph={EditSVG} />
            </span>
        );
    },
);

export default EditIcon;
