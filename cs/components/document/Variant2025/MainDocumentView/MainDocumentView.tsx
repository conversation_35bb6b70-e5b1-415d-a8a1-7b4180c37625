import React from 'react';

import AddToDirectoryPopup from 'components/AddToDirectoryPopup';
import { AddToDirectoryPopupProvider } from 'components/AddToDirectoryPopup/context';
import HtmlHeadTitle from 'components/document/Variant2025/HTMLHeadTitle/HTMLHeadTitle';
import { useDocument } from 'components/document/Variant2025/useDocument';
import DocumentViewer from 'components/documentViewer/documentViewer';

import DocumentHeader from '../DocumentHeader';
import DocumentLayout from '../DocumentLayout';
import DocumentToolbarBox from '../DocumentToolbarBox';
import DocumentViewerBox from '../DocumentViewerBox';
import MainDocumentSidebar from '../MainDocumentSidebar';
import SidebarProvider from '../MainDocumentSidebar/context';
import { tabs } from './tabs';

/**
 * Налаштовуємо поблочно що будемо виводити для основної сторінки перегляду документа.
 * DocumentHeader - елементи шапки документа
 * DocumentViewerBox - блок для перегляду документа
 * MainDocumentSidebar - бічна панель з вкладками
 */
const MainDocumentView: React.FC = () => {
    const doc = useDocument();

    return (
        <DocumentLayout>
            <HtmlHeadTitle />
            <DocumentHeader>
                <DocumentHeader.Logo />
                <DocumentHeader.BackButton />
                <DocumentHeader.DocTitle editable />
                <DocumentHeader.StatusBadge />
                <DocumentHeader.VersionBadge />
                <DocumentHeader.EmptySize />
                <DocumentHeader.SupportIcon />
                <DocumentHeader.ButtonGroup>
                    <DocumentHeader.ApprovalButtons />
                    <DocumentHeader.RejectButton />
                    <DocumentHeader.MainSignButton />
                    <DocumentHeader.SendVersionButton />
                </DocumentHeader.ButtonGroup>
                <DocumentHeader.AnnulmentBlock />
                <DocumentHeader.VersionAlert />
                <DocumentHeader.DeleteRequestAlert />
            </DocumentHeader>
            <DocumentToolbarBox>
                <DocumentToolbarBox.OpenRedactorIcon />
                <DocumentToolbarBox.PrintIcon />
                <DocumentToolbarBox.ShareAccessIcon />
                <DocumentToolbarBox.ArchiveIcon />
                <DocumentToolbarBox.DeleteIcon />
                <DocumentToolbarBox.DownloadSignSummaryIcon />
                <DocumentToolbarBox.CreateAnnulmentActIcon />
                <DocumentToolbarBox.DownloadIcon />
                <DocumentToolbarBox.CopyToTemplateIcon />
                <DocumentToolbarBox.DocumentsSwitcher placement="center" />
            </DocumentToolbarBox>
            <DocumentViewerBox>
                <DocumentViewer doc={doc} />
            </DocumentViewerBox>
            <MainDocumentSidebar />
        </DocumentLayout>
    );
};

export default () => (
    <SidebarProvider tabs={tabs}>
        <AddToDirectoryPopupProvider>
            <MainDocumentView />
            <AddToDirectoryPopup />
        </AddToDirectoryPopupProvider>
    </SidebarProvider>
);
