import React from 'react';

import { useDownloadSignSummary } from 'components/downloadSignSummaryButton/useDownloadSignSummary';
import { useIsShowDownloadSignSummaryButton } from 'components/downloadSignSummaryButton/useIsShowDownloadSignSummaryButton';
import { t } from 'ttag';

import ToolbarIcon from './ToolbarIcon';

import SvgSave from './assets/save-receipt.svg';

const DownloadSignSummaryIcon: React.FC = () => {
    const isShowDownloadSignSummaryButton = useIsShowDownloadSignSummaryButton();
    const {
        isSignSummaryLoading,
        handleDownloadSignSummary,
    } = useDownloadSignSummary();

    if (!isShowDownloadSignSummaryButton) {
        return null;
    }

    return (
        <ToolbarIcon
            tooltip={t`Зберегти квитанцію`}
            isLoading={isSignSummaryLoading}
            onClick={handleDownloadSignSummary}
            icon={SvgSave}
        />
    );
};

export default DownloadSignSummaryIcon;
