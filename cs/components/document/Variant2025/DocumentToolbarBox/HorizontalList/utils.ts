import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';

const DOC_TOOLBAR_ICONS_COLLAPSE_STATE_KEY = 'doc_toolbar_icons_collapse_state';

export const getDefaultExpandedState = () => {
    return Boolean(getLocalStorageItem(DOC_TOOLBAR_ICONS_COLLAPSE_STATE_KEY));
};

export const setDefaultExpandedState = (isExpanded: boolean) => {
    setLocalStorageItem(DOC_TOOLBAR_ICONS_COLLAPSE_STATE_KEY, isExpanded);
};
