import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import cn from 'classnames';
import SvgArrowLeft from 'components/TrialInfoPopup/svg/arrowLeft.svg';
import { t } from 'ttag';
import Icon from 'ui/icon';

import { getDefaultExpandedState, setDefaultExpandedState } from './utils';

import css from './HorizontalList.css';

interface HorizontalListProps {
    children: React.ReactNode;
    maxVisible?: number;
    gap?: number;
    btnClassName?: string;
}

const HorizontalList: React.FC<HorizontalListProps> = ({
    children,
    maxVisible = 5,
    gap = 0,
    btnClassName,
}) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [expanded, setExpanded] = useState(getDefaultExpandedState);
    const [childCount, setChildCount] = useState(0);
    const [collapsedWidth, setCollapsedWidth] = useState<number | null>(null);

    useEffect(() => {
        setDefaultExpandedState(expanded);
    }, [expanded]);

    // Підрахунок дітей у DOM
    useLayoutEffect(() => {
        if (!containerRef.current) {
            return;
        }

        const updateCount = () => {
            setChildCount(containerRef.current?.childElementCount || 0);
        };

        updateCount();

        const observer = new MutationObserver(updateCount);
        observer.observe(containerRef.current, { childList: true });

        return () => {
            observer.disconnect();
        };
    }, []);

    // Обчислення ширини перших N дітей
    useLayoutEffect(() => {
        if (!containerRef.current) {
            return;
        }

        const calcWidth = () => {
            const nodes = Array.from(containerRef.current!.children).slice(
                0,
                maxVisible,
            );
            const totalWidth = nodes.reduce(
                (acc, el) => acc + (el as HTMLElement).offsetWidth,
                0,
            );

            setCollapsedWidth(totalWidth + gap * (nodes.length - 1));
        };

        calcWidth();

        const resizeObserver = new ResizeObserver(calcWidth);

        Array.from(containerRef.current.children).forEach((child) =>
            resizeObserver.observe(child),
        );

        return () => {
            resizeObserver.disconnect();
        };
    }, [childCount, maxVisible]);

    return (
        <div className={css.root} style={{ gap }}>
            <div
                ref={containerRef}
                className={css.root}
                style={{
                    gap,
                    overflow: 'hidden',
                    width: expanded ? 'auto' : collapsedWidth ?? 'auto',
                }}
            >
                {children}
            </div>

            {childCount > maxVisible && (
                <BlackTooltip
                    title={expanded ? t`Згорнути` : t`Розгорнути всі дії`}
                    disableInteractive
                >
                    <span
                        key="switch-btn"
                        className={cn(css.switchBtn, btnClassName, {
                            [css.collapsed]: !expanded,
                        })}
                        onClick={() => setExpanded((prev) => !prev)}
                    >
                        <Icon glyph={SvgArrowLeft} />
                    </span>
                </BlackTooltip>
            )}
        </div>
    );
};

export default HorizontalList;
