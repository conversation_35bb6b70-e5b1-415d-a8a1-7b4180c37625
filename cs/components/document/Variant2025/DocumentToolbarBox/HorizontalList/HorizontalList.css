.root {
    display: flex;
    align-items: center;
}

.switchBtn {
    display: flex;
    width: 20px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--grey-border);
    aspect-ratio: 1 / 1;
    background-color: var(--white-bg);
    border-radius: 50%;
    color: var(--grey-color);
    cursor: pointer;
    transition: transform 0.3s, background-color 0.3s;
}

.switchBtn:hover {
    background-color: var(--hover-bg);
}

.switchBtn svg {
    position: relative;
    left: -1px;
    width: 6px;
    color: var(--grey-color);
}

.collapsed {
    transform: rotate(180deg);
}
