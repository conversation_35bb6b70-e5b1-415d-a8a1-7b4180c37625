import React from 'react';

import DocTemplatesSvg from 'components/AppSidebar/icons/docTemplates.svg';
import { useCopyToTemplatesAction } from 'components/actionTools/useCopyToTemplatesAction';
import { useIsShowCopyToTemplatesAction } from 'components/actionTools/useIsShowCopyToTemplatesAction';
import ToolbarIcon from 'components/document/Variant2025/DocumentToolbarBox/ToolbarIcon';
import { sendToGTMV4 } from 'services/analytics/gtm';
import { t } from 'ttag';

const CopyToTemplateIcon: React.FC = () => {
    const isShowCopyToTemplatesAction = useIsShowCopyToTemplatesAction();
    const { handleCopyToTemplates, isLoading } = useCopyToTemplatesAction();

    if (!isShowCopyToTemplatesAction) {
        return null;
    }

    const onClickCopyToTemplate = async () => {
        sendToGTMV4({
            event: 'ec_docpage_icon_template_copy_click',
        });

        await handleCopyToTemplates();
    };

    return (
        <ToolbarIcon
            onClick={onClickCopyToTemplate}
            tooltip={t`Зберегти як шаблон`}
            icon={DocTemplatesSvg}
            isLoading={isLoading}
        />
    );
};

export default CopyToTemplateIcon;
