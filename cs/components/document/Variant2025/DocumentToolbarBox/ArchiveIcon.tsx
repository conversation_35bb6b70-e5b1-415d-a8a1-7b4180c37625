import React from 'react';

import { useArchiveItems } from 'components/ArchiveButton/useArchiveItems';
import { useIsShowArchiveButton } from 'components/ArchiveButton/useIsShowArchiveButton';
import { useIsShowArchiveButtons } from 'components/ArchiveButton/useIsShowArchiveButtons';
import FolderCrossOutSvg from 'components/UnArchiveButton/images/folderCrossOut.svg';
import { useUnArchiveItems } from 'components/UnArchiveButton/useUnArchiveItems';
import { useDocument } from 'components/document/Variant2025/useDocument';
import { openInNewTab } from 'lib/navigation';
import { t } from 'ttag';

import ToolbarIcon from './ToolbarIcon';

import FolderPlusSvg from './assets/add-folder.svg';

import css from './ToolbarIcon/ToolbarIcon.css';

const ArchiveIcon = () => {
    const doc = useDocument();
    const isShowArchiveButtons = useIsShowArchiveButtons();
    const isShowArchiveButton = useIsShowArchiveButton();
    const { onUnArchiveItems, isDisabledUnArchive } = useUnArchiveItems([doc]);
    const {
        isDisabledArchiveButton,
        getTitleArchiveButton,
        onArchiveItems,
        isAccessArchiveItems,
    } = useArchiveItems([doc]);

    // Перевірка чи взагалі потрібно виводити кнопки з архівом
    if (!isShowArchiveButtons) {
        return null;
    }

    // Якщо немає доступу до архіву, то показуємо іконку з підказкою
    if (!isAccessArchiveItems) {
        return (
            <ToolbarIcon
                disabledTooltip={
                    <>
                        {getTitleArchiveButton()}
                        <br />
                        {!isAccessArchiveItems && (
                            <span
                                className={css.link}
                                onClick={() => {
                                    openInNewTab('/app/archive-preview');
                                }}
                            >{t`Дізнатися більше`}</span>
                        )}
                    </>
                }
                icon={FolderPlusSvg}
                disabled
            />
        );
    }

    // Якщо документ вже заархівований, то показуємо кнопку повернення з архіву
    if (!isShowArchiveButton) {
        return (
            <ToolbarIcon
                tooltip={t`Повернути до документів`}
                disabled={isDisabledUnArchive}
                onClick={onUnArchiveItems}
                icon={FolderCrossOutSvg}
            />
        );
    }

    return (
        <ToolbarIcon
            onClick={onArchiveItems}
            tooltip={getTitleArchiveButton()}
            icon={FolderPlusSvg}
            disabled={isDisabledArchiveButton}
        />
    );
};

export default ArchiveIcon;
