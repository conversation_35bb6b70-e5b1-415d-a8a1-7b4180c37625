.root {
    --toolbar-icon-color: var(--Color-Gray-300);

    display: inline-flex;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: var(--toolbar-icon-color);
    cursor: pointer;
    transition: color 0.3s, background-color 0.3s;
}

.root:hover {
    background-color: color(from var(--toolbar-icon-color) sRGB r g b / 0.2);
}

.root:active,
.root:focus {
    border: 0;
    outline: 0;
}

.icon {
    width: 20px;
    height: 20px;
    pointer-events: none;
}

.disabled {
    cursor: not-allowed;
    opacity: 0.5;
}
