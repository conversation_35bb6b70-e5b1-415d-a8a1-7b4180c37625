import React from 'react';

import { BlackTooltip, Spinner } from '@vchasno/ui-kit';

import cn from 'classnames';
import Icon, { Glyph } from 'ui/icon';

import css from './ToolbarIcon.css';

export interface ToolbarIconProps
    extends React.HTMLAttributes<HTMLSpanElement> {
    className?: string;
    icon: Glyph;
    tooltip?: string | React.ReactNode;
    disabled?: boolean;
    disabledTooltip?: string | React.ReactNode;
    color?: React.CSSProperties['color'];
    isLoading?: boolean;
}

const ToolbarIcon = React.forwardRef<HTMLSpanElement, ToolbarIconProps>(
    (
        {
            className,
            icon,
            tooltip,
            disabled,
            disabledTooltip,
            color,
            isLoading,
            ...rest
        },
        ref,
    ) => {
        const content = (
            <span
                ref={ref}
                {...rest}
                onClick={disabled ? undefined : rest.onClick}
                style={{ '--toolbar-icon-color': color } as React.CSSProperties}
                className={cn(
                    css.root,
                    { [css.disabled]: disabled },
                    className,
                )}
            >
                {isLoading ? (
                    <Spinner />
                ) : (
                    <Icon glyph={icon} className={css.icon} />
                )}
            </span>
        );

        if (disabled && disabledTooltip) {
            return (
                <BlackTooltip title={disabledTooltip} disableInteractive>
                    {content}
                </BlackTooltip>
            );
        }

        if (tooltip) {
            return <BlackTooltip title={tooltip}>{content}</BlackTooltip>;
        }

        return content;
    },
);

export default ToolbarIcon;
