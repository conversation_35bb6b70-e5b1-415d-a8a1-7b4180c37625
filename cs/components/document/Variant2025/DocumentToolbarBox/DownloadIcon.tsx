import React from 'react';

import { Menu } from '@vchasno/ui-kit';

import { useIsShowDownloadDocumentButton } from 'components/DownloadDocumentButton/useIsShowDownloadDocumentButton';
import { FeatureShow } from 'components/FeatureDisplay';
import { useDocument } from 'components/document/Variant2025/useDocument';
import { t } from 'ttag';

import ToolbarIcon from './ToolbarIcon';

import DownloadSVG from './assets/download.svg';

const DownloadIcon = () => {
    const [focus, setFocus] = React.useState(false);
    const doc = useDocument();
    const isShowDownloadDocumentButton = useIsShowDownloadDocumentButton();

    if (!isShowDownloadDocumentButton) {
        return null;
    }

    return (
        <Menu
            menuButton={
                focus ? (
                    <ToolbarIcon icon={DownloadSVG} />
                ) : (
                    <ToolbarIcon tooltip={t`Завантажити`} icon={DownloadSVG} />
                )
            }
            onFocus={() => setFocus(true)}
            onBlur={() => setFocus(false)}
        >
            <Menu.Item>{t`Завантажити`} ZIP</Menu.Item>
            {doc.hasEUSignatures && (
                <FeatureShow feature="ENABLE_DIIA_ECDSA">
                    {t`Завантажити`} ASIC-E
                </FeatureShow>
            )}
        </Menu>
    );
};

export default DownloadIcon;
