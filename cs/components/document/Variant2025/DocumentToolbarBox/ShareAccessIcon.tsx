import React from 'react';

import { useShareDocButton } from 'components/shareDocumentButton/useShareDocButton';
import { sendToGTMV4 } from 'services/analytics/gtm';
import { t } from 'ttag';

import ToolbarIcon from './ToolbarIcon';

import ShareSvg from './assets/share.svg';

const ShareAccessIcon: React.FC = () => {
    const { onOpenShareDocumentPopup } = useShareDocButton();

    const onClickShareDocument = () => {
        sendToGTMV4({
            event: 'ec_docpage_icon_forward_click',
        });
        onOpenShareDocumentPopup();
    };

    return (
        <ToolbarIcon
            onClick={onClickShareDocument}
            tooltip={t`Переслати документ співробітнику`}
            icon={ShareSvg}
        />
    );
};

export default ShareAccessIcon;
