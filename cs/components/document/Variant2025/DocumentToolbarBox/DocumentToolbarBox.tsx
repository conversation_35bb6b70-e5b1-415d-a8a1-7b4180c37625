import React from 'react';

import cn from 'classnames';

import { WithToolbarLayoutPlacement } from './types';

import ArchiveIcon from './ArchiveIcon';
import CopyToTemplateIcon from './CopyToTemplateIcon';
import CreateAnnulmentActIcon from './CreateAnnulmentActIcon';
import DeleteIcon from './DeleteIcon';
import DocumentsSwitcher from './DocumentsSwitcher';
import DownloadIcon from './DownloadIcon';
import DownloadSignSummaryIcon from './DownloadSignSummaryIcon';
import HorizontalList from './HorizontalList';
import OpenRedactorIcon from './OpenRedactorIcon';
import PrintIcon from './PrintIcon';
import ShareAccessIcon from './ShareAccessIcon';

import css from './DocumentToolbarBox.css';

export interface DocumentToolbarBoxProps {
    className?: string;
    children?: React.ReactNode;
}

// Перевіряємо проп placement у дочірніх елементах
const checkPlacementInChild = (
    child: React.ReactNode,
    placement?: WithToolbarLayoutPlacement['placement'],
): boolean => {
    return (
        React.isValidElement<WithToolbarLayoutPlacement>(child) &&
        child.props &&
        child.props.placement === placement
    );
};

const DocumentToolbarBox = ({
    className,
    children,
}: DocumentToolbarBoxProps) => {
    if (!children) {
        return null;
    }

    return (
        <div data-area="toolbar" className={cn(css.root, className)}>
            <div className={css.col}>
                <HorizontalList btnClassName={css.collapseBtn}>
                    {React.Children.map(children, (child) => {
                        return Boolean(
                            checkPlacementInChild(child) ||
                                checkPlacementInChild(child, 'left'),
                        )
                            ? child
                            : null;
                    })}
                </HorizontalList>
            </div>
            <div className={css.col} style={{ justifyContent: 'center' }}>
                {React.Children.map(children, (child) => {
                    return checkPlacementInChild(child, 'center')
                        ? child
                        : null;
                })}
            </div>
            <div className={css.col} style={{ justifyContent: 'flex-end' }}>
                {React.Children.map(children, (child) => {
                    return checkPlacementInChild(child, 'right') ? child : null;
                })}
            </div>
        </div>
    );
};

DocumentToolbarBox.PrintIcon = PrintIcon;
DocumentToolbarBox.CreateAnnulmentActIcon = CreateAnnulmentActIcon;
DocumentToolbarBox.OpenRedactorIcon = OpenRedactorIcon;
DocumentToolbarBox.DocumentsSwitcher = DocumentsSwitcher;
DocumentToolbarBox.ArchiveIcon = ArchiveIcon;
DocumentToolbarBox.DownloadSignSummaryIcon = DownloadSignSummaryIcon;
DocumentToolbarBox.DownloadIcon = DownloadIcon;
DocumentToolbarBox.DeleteIcon = DeleteIcon;
DocumentToolbarBox.CopyToTemplateIcon = CopyToTemplateIcon;
DocumentToolbarBox.ShareAccessIcon = ShareAccessIcon;

export default DocumentToolbarBox;
