import React from 'react';
import { useSelector } from 'react-redux';

import { BodyPortal } from '@vchasno/ui-kit';

import { useIsShowDeleteLink } from 'components/deleteLink/useIsShowDeleteLink';
import DeletePopup from 'components/deletePopup/deletePopup';
import ToolbarIcon from 'components/document/Variant2025/DocumentToolbarBox/ToolbarIcon';
import { useDocument } from 'components/document/Variant2025/useDocument';
import { useDocumentActions } from 'components/document/useDocumentActions';
import { getCurrentDocumentIsDeletePopupOpened } from 'selectors/document.selectors';
import { sendToGTMV4 } from 'services/analytics/gtm';
import { t } from 'ttag';

import DeleteSvg from './assets/delete.svg';

const DeleteIcon: React.FC = () => {
    const isShowDeleteLink = useIsShowDeleteLink();
    const doc = useDocument();
    const documentAction = useDocumentActions();
    const isDeletePopupOpened = useSelector(
        getCurrentDocumentIsDeletePopupOpened,
    );

    const onDelete = () => {
        sendToGTMV4({
            event: 'ec_docpage_icon_delete_click',
        });

        documentAction.onOpenDeletePopup();
    };

    if (!isShowDeleteLink) {
        return null;
    }

    return (
        <>
            <ToolbarIcon
                color="var(--Color-System-states-Error-300)"
                onClick={onDelete}
                tooltip={t`Видалити`}
                icon={DeleteSvg}
            />
            <BodyPortal>
                <DeletePopup
                    isDeletePopupOpened={isDeletePopupOpened}
                    documents={[doc]}
                    onCloseDeletePopup={documentAction.onCloseDeletePopup}
                    onDeleteDocuments={documentAction.onDeleteDoc}
                />
            </BodyPortal>
        </>
    );
};

export default DeleteIcon;
