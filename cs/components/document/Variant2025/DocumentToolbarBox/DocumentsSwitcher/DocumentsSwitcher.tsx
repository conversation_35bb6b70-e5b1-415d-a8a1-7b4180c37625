import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { FlexBox, Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import { useDocument } from 'components/document/Variant2025/useDocument';
import SvgLeft from 'components/sideMenu/images/left.svg';
import SvgRight from 'components/sideMenu/images/right.svg';
import { getSelectedDocuments } from 'selectors/documentList.selectors';
import { t } from 'ttag';
import Icon from 'ui/icon';

import { WithToolbarLayoutPlacement } from '../types';

import css from './DocumentsSwitcher.css';

export interface DocumentsSwitcherProps extends WithToolbarLayoutPlacement {
    className?: string;
}

// Цей компонент працює тільки коли були обрані документи в таблиці
// Потрібно його вдосконалити та можливо зробити для нього окремий стан в редаксі
const DocumentsSwitcher: React.FC<DocumentsSwitcherProps> = ({ className }) => {
    const history = useHistory();
    const documentList = useSelector(getSelectedDocuments);
    const doc = useDocument();
    const documentIndex = documentList.findIndex((item) => item.id === doc.id);
    const prevDoc = documentList[documentIndex - 1];
    const nextDox = documentList[documentIndex + 1];

    if (documentList.length <= 1 || documentIndex === -1) {
        return null;
    }

    return (
        <FlexBox className={cn(css.root, className)} align="center">
            <span
                className={css.btn}
                onClick={() => {
                    if (!prevDoc) {
                        history.replace(
                            `/app/documents/${documentList.reverse()[0].id}`,
                        );
                        return;
                    }
                    history.replace(`/app/documents/${prevDoc.id}`);
                }}
            >
                <Icon glyph={SvgLeft} className={css.icon} />
            </span>
            <Text type="secondary">
                {documentIndex + 1} {t`з`} {documentList.length}
            </Text>
            <span
                className={css.btn}
                onClick={() => {
                    if (!nextDox) {
                        history.replace(`/app/documents/${documentList[0].id}`);
                        return;
                    }
                    history.replace(`/app/documents/${nextDox.id}`);
                }}
            >
                <Icon glyph={SvgRight} className={css.icon} />
            </span>
        </FlexBox>
    );
};

export default DocumentsSwitcher;
