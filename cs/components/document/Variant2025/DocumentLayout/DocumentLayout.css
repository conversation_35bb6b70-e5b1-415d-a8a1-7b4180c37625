.root {
    --vchasno-document-layout-padding-lg: 16px;
    --vchasno-document-layout-padding-md: 12px;


    display: grid;
    width: 100%;
    min-height: 100vh;
    grid-auto-rows: auto;
    grid-template-areas:
        "header header"
        "toolbar sidebar"
        "viewer sidebar";
    grid-template-columns: 1fr calc(64px + 320px);
    grid-template-rows: var(--header-height) var(--header-height) auto;
}

@media screen and (min-width: 1600px) {
    .root {
        grid-template-columns: 1fr calc(64px + 480px);
    }
}

.root > [data-area='header'] {
    position: relative;
    z-index: 1;
    grid-area: header;
}

.root > [data-area='toolbar'] {
    margin-top: var(--vchasno-document-layout-padding-lg);
    margin-right: var(--vchasno-document-layout-padding-md);
    margin-left: var(--vchasno-document-layout-padding-lg);
    grid-area: toolbar;
}

.root > [data-area='sidebar'] {
    grid-area: sidebar;
}

.loadContainer {
    display: flex;
    width: 100%;
    min-height: 100vh;
    align-items: center;
    justify-content: center;
}

.root > [data-area='viewer'] {
    margin-top: var(--vchasno-document-layout-padding-md);
    margin-right: var(--vchasno-document-layout-padding-md);
    margin-left: var(--vchasno-document-layout-padding-lg);
    grid-area: viewer;
}
