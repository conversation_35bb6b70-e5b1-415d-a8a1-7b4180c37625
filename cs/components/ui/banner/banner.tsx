import React, { FC } from 'react';

import eventTracking from '../../../services/analytics/eventTracking';
import CloseButton from '../closeButton/closeButton';

import css from './banner.css';

interface AnalyticsData {
    category: string;
    action: string;
    label: string;
}

interface Props {
    analyticsData: AnalyticsData;
    link: string;
    color: 'yellow' | 'green' | 'blue';
    onClose: () => void;
}

const Banner: FC<React.PropsWithChildren<Props>> = (props) => {
    const { link, color, analyticsData, onClose } = props;
    const { category, action, label } = analyticsData;
    const capitalizeColor = color.charAt(0).toUpperCase() + color.slice(1);

    const handleClick = () => {
        eventTracking.sendEvent(category, action, label);
        eventTracking.sendSystemBannerToGTMV4({
            action: 'click',
            category: category,
        });
    };

    return (
        <a
            className={css[`root${capitalizeColor}`]}
            href={link}
            target="_blank"
            rel="noopener noreferrer"
            onClick={handleClick}
        >
            {props.children}
            <CloseButton
                onClose={(evt: React.MouseEvent) => {
                    evt.preventDefault();
                    evt.stopPropagation();
                    onClose();
                }}
            />
        </a>
    );
};

export default Banner;
