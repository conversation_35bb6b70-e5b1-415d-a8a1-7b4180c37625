import React from 'react';

import cn from 'classnames';

import Icon from '../icon/icon';

import ArrowLeft from './images/arrowleft.svg';

import css from './BackButton.css';

type ButtonVariant = 'rounded' | 'square';
type ButtonColor = 'pigeon' | 'grey';

interface BackButtonProps {
    variant?: ButtonVariant;
    color?: ButtonColor;
    type?: React.ButtonHTMLAttributes<HTMLButtonElement>['type'];
    icon?: React.ReactSVGElement;
    className?: string;
    onClick?: (e: React.SyntheticEvent<HTMLButtonElement>) => void;
}

const BackButton = React.forwardRef<HTMLButtonElement, BackButtonProps>(
    (
        {
            variant = 'square',
            type = 'button',
            color = 'grey',
            icon,
            className,
            onClick,
        },
        ref,
    ) => (
        <button
            ref={ref}
            className={cn(css.root, className, {
                [css.rounded]: variant === 'rounded',
                [css.square]: variant === 'square',
                [css.pigeon]: color === 'pigeon',
                [css.grey]: color === 'grey',
            })}
            type={type}
            onClick={onClick}
        >
            <Icon glyph={icon || ArrowLeft} />
        </button>
    ),
);

export default BackButton;
