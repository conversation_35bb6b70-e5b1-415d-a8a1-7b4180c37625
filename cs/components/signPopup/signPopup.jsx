import React, { Fragment } from 'react';

import { Alert, FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import { shouldShowOldSignSuccessPopup } from 'components/SignSuccessPopup/utils';
import { ACCEPT_SIGNATURE_EXTENSIONS } from 'lib/constants';
import { BILLING_OVERLIMIT_ERROR_CODE } from 'lib/errors';
import { BILL_GENERATION_PAGE_PATTERN } from 'lib/routing/constants';
import PropTypes from 'prop-types';
import { User } from 'records/user';
import eventTracking from 'services/analytics/eventTracking';
import { shouldOnlySign } from 'services/documents/utils';
import { ApplicationMode, SignPopupStatus } from 'services/enums';
import { jt, t } from 'ttag';

import Button from '../ui/button/button';
import Input from '../ui/input/input';
import Message from '../ui/message/message';

import IitSignWidget from '../IitSignWidget/IitSignWidget';
import KepSignForm from '../KepSigner/KepSignForm';
import RequiredFieldsResolver from '../RequiredFieldsResolver/RequiredFieldsResolver';
import SignDocumentDiia from '../SignDocumentDiia/SignDocumentDiia';
import SuccesfulSignView from '../SuccesfulSignView/SuccesfulSignView';
import CreateFlowForm from '../createFlowForm/createFlowForm';
import DelnotWarningPopup from '../delnotWarningPopup/delnotWarningPopup';
import DocumentsSignSummary from '../documentsSignSummary/documentsSignSummary';
import PrivateKeyUploader from '../privateKeyUploader/privateKeyUploader';
import RecipientFormContainer from '../recipientForm/recipientFormContainer';
import SignForm from '../signForm/signForm';
import UsbSignForm from '../usbSignForm/usbSignForm';
import OverdraftError from './OverdraftError/OverdraftError';
import Popup from './Popup/Popup';

import css from './signPopup.css';

class SignPopup extends React.Component {
    static propTypes = {
        applicationMode: PropTypes.string.isRequired,
        isActive: PropTypes.bool.isRequired,
        isConnectionProblemOccurred: PropTypes.bool.isRequired,
        isUploadedPK: PropTypes.bool.isRequired,
        isUploadedStamp: PropTypes.bool.isRequired,
        isPKEditDisabled: PropTypes.bool.isRequired,
        isStampEditDisabled: PropTypes.bool.isRequired,
        isPKChecked: PropTypes.bool.isRequired,
        isStampChecked: PropTypes.bool.isRequired,
        isCheckPKProcess: PropTypes.bool.isRequired,
        isCheckStampProcess: PropTypes.bool.isRequired,
        isSignAndSubmitProcess: PropTypes.bool.isRequired,
        isRememberedKeys: PropTypes.bool.isRequired,
        isMultiSign: PropTypes.bool,
        isLoadPKCertsFromFile: PropTypes.bool.isRequired,
        isLoadStampCertsFromFile: PropTypes.bool.isRequired,
        isPKCAServerEditDisabled: PropTypes.bool.isRequired,
        isStampCAServerEditDisabled: PropTypes.bool.isRequired,
        isRecipientEmailHidden: PropTypes.bool.isRequired,
        isSignerServiceLoaded: PropTypes.bool.isRequired,

        pkCertificateFiles: PropTypes.object,
        stampCertificateFiles: PropTypes.object,
        pkInfo: PropTypes.object,
        stampInfo: PropTypes.object,
        certificatePKInfo: PropTypes.object,
        certificateStampInfo: PropTypes.object,

        docs: PropTypes.array,
        currentUser: PropTypes.instanceOf(User).isRequired,
        currentRoleId: PropTypes.string.isRequired,
        caServers: PropTypes.array,
        multiSignResult: PropTypes.array,

        pkCAServerIdx: PropTypes.number,
        stampCAServerIdx: PropTypes.number,
        totalDocsCount: PropTypes.number,

        pkFileName: PropTypes.string,
        pkPassword: PropTypes.string,
        stampFileName: PropTypes.string,
        stampPassword: PropTypes.string,
        popupStatus: PropTypes.string,
        edrpouRecipient: PropTypes.string,
        emailRecipient: PropTypes.string,
        edrpouOwner: PropTypes.string,
        emailOwner: PropTypes.string,
        errorMessage: PropTypes.string,
        errorCode: PropTypes.string,
        pkErrorMessage: PropTypes.string,
        stampErrorMessage: PropTypes.string,
        showAutopickPKError: PropTypes.bool,
        showAutopickStampError: PropTypes.bool,
        origin: PropTypes.string,
        onClearPK: PropTypes.func,
        onClearStamp: PropTypes.func,

        hidePopup: PropTypes.func.isRequired,
        initSignService: PropTypes.func.isRequired,
        submitPK: PropTypes.func.isRequired,
        setPKPassword: PropTypes.func.isRequired,
        submitPKCertificates: PropTypes.func.isRequired,
        submitStamp: PropTypes.func.isRequired,
        setStampPassword: PropTypes.func.isRequired,
        submitStampCertificates: PropTypes.func.isRequired,
        changeCAServer: PropTypes.func.isRequired,
        readKey: PropTypes.func.isRequired,
        sign: PropTypes.func.isRequired,
        submitRecipient: PropTypes.func.isRequired,
        signAndSubmit: PropTypes.func.isRequired,
        retrySignAndSubmit: PropTypes.func.isRequired,
        multiSign: PropTypes.func.isRequired,
        toggleRememberKeys: PropTypes.func.isRequired,
        onDelnotWarningAgree: PropTypes.func.isRequired,
        onSubmitKeysFromStorage: PropTypes.func.isRequired,
        onSubmitRecipients: PropTypes.func.isRequired,

        // USB form
        isUSBSignerLoaded: PropTypes.bool,
        isFindingUSBDevices: PropTypes.bool,
        useUSBSPK: PropTypes.bool,
        useUSBStamp: PropTypes.bool,
        usbSigner: PropTypes.object,
        devices: PropTypes.array,
        usbLoadError: PropTypes.string,
        loadUSBSigner: PropTypes.func,
        loadIitSignWidget: PropTypes.func,
        usbSignAndSubmit: PropTypes.func,
        onSetUSBProxySettings: PropTypes.func.isRequired,
        onFindUSBDevices: PropTypes.func.isRequired,

        isPkUSBKeyReading: PropTypes.bool,
        isPkUSBChecked: PropTypes.bool,
        pkDevice: PropTypes.object,
        pkUsbCAServerIdx: PropTypes.number,
        pkUSBPassword: PropTypes.string,
        onPkUSBDeviceChange: PropTypes.func,
        onChangePkUSBCAServer: PropTypes.func,
        onSetPkUSBPassword: PropTypes.func,
        onReadPkUSBKey: PropTypes.func,
        onResetPkUSBKey: PropTypes.func,

        isStampUSBKeyReading: PropTypes.bool,
        isStampUSBChecked: PropTypes.bool,
        stampDevice: PropTypes.object,
        stampUsbCAServerIdx: PropTypes.number,
        stampUSBPassword: PropTypes.string,
        onStampUSBDeviceChange: PropTypes.func,
        onChangeStampUSBCAServer: PropTypes.func,
        onSetStampUSBPassword: PropTypes.func,
        onReadStampUSBKey: PropTypes.func,
        onResetStampUSBKey: PropTypes.func,
        onAddUSBStamp: PropTypes.func,
        onResetSignSteps: PropTypes.func,

        // JKS container
        pkJksKey: PropTypes.object,
        stampJksKey: PropTypes.object,
        pkJksKeys: PropTypes.array,
        stampJksKeys: PropTypes.array,
        onChangeJksKey: PropTypes.func.isRequired,

        // first sign popup
        onClickSendButton: PropTypes.func,

        isOnlyExtraSignatureDocuments: PropTypes.bool,

        isSharedDocumentViewMode: PropTypes.bool,

        //confirm close
        isNeedConfirmClosed: PropTypes.bool,
        isShowConfirmClose: PropTypes.bool,

        vchasnoTheme: PropTypes.string,

        flags: PropTypes.object,
    };

    state = {
        showConnectionProblemError: false,
        previousStatus: null,
    };

    handleClearPkFile = () => {
        this.props.onClearPK();
        this.pkFileInput.value = null;
    };

    handleClearStampFile = () => {
        this.props.onClearStamp();
        this.stampFileInput.value = null;
    };

    componentDidMount() {
        this.props.initSignService();
    }

    getChildren = () => {
        const isSignSessionMode =
            this.props.applicationMode === ApplicationMode.SIGN_SESSION;
        const isSharedDocumentViewMode =
            this.props.applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW;

        const getTrackingName = () => {
            if (isSignSessionMode) {
                return 'sign_session';
            }
            if (isSharedDocumentViewMode) {
                return 'shared_document_view';
            }
            return 'doc';
        };

        const trackingPrefix = getTrackingName();
        const trackingLabel = isSignSessionMode
            ? `${this.props.docs[0].edrpouOwner} ${this.props.docs[0].companyNameOwner}`
            : '';

        switch (this.props.popupStatus) {
            case SignPopupStatus.CHOOSE_RECIPIENT: {
                return (
                    <RecipientFormContainer
                        fullWidthButton
                        hideHint
                        buttonText={t`Продовжити`}
                        formTrackingName={`${trackingPrefix}_sign_step_1`}
                        docId={this.props.docs[0].id}
                        defaultEmail={this.props.emailRecipient}
                        defaultEdrpou={this.props.edrpouRecipient}
                        defaultEmailHidden={this.props.isRecipientEmailHidden}
                        onSubmit={this.props.submitRecipient}
                    />
                );
            }
            case SignPopupStatus.CHOOSE_DOCUMENT_FIELDS: {
                return <RequiredFieldsResolver />;
            }
            case SignPopupStatus.CREATE_FLOWS: {
                return (
                    <CreateFlowForm
                        isDocPage={this.props.origin === 'document'}
                        docs={[this.props.docs[0]]}
                        onSaveCallback={this.props.hidePopup}
                        onCancel={this.props.hidePopup}
                        onSubmitCallback={this.props.onSubmitRecipients}
                    />
                );
            }
            case SignPopupStatus.UPLOAD_PRIVATE_KEY:
                return (
                    <PrivateKeyUploader
                        isMultiSign={this.props.isMultiSign}
                        isSignerServiceLoaded={this.props.isSignerServiceLoaded}
                        theme="cta"
                        title=""
                        formTrackingName={`${trackingPrefix}_sign_step_2`}
                        formTrackingLabel={trackingLabel}
                        errorMessage={this.props.errorMessage}
                        onClick={() => this.pkFileInput.click()}
                        onUSBClick={this.props.loadUSBSigner}
                        onIitWidgetClick={this.props.loadIitSignWidget}
                        onSubmitKeysFromStorage={
                            this.props.onSubmitKeysFromStorage
                        }
                    />
                );
            case SignPopupStatus.CHECK_PRIVATE_KEY:
                return (
                    <SignForm
                        docId={this.props.docs[0].id}
                        isUploadedPK={this.props.isUploadedPK}
                        isUploadedStamp={this.props.isUploadedStamp}
                        isPKEditDisabled={this.props.isPKEditDisabled}
                        isStampEditDisabled={this.props.isStampEditDisabled}
                        isPKChecked={this.props.isPKChecked}
                        isStampChecked={this.props.isStampChecked}
                        isCheckPKProcess={this.props.isCheckPKProcess}
                        isCheckStampProcess={this.props.isCheckStampProcess}
                        isSignAndSubmitProcess={
                            this.props.isSignAndSubmitProcess
                        }
                        isRememberedKeys={this.props.isRememberedKeys}
                        isMultiSign={this.props.isMultiSign}
                        isLoadPKCertsFromFile={this.props.isLoadPKCertsFromFile}
                        isLoadStampCertsFromFile={
                            this.props.isLoadStampCertsFromFile
                        }
                        isPKCAServerEditDisabled={
                            this.props.isPKCAServerEditDisabled
                        }
                        isStampCAServerEditDisabled={
                            this.props.isStampCAServerEditDisabled
                        }
                        isSignOnly={shouldOnlySign(
                            this.props.docs[0],
                            this.props.currentRoleId,
                            this.props.isMultiSign,
                        )}
                        isInternal={this.props.docs.every(
                            (doc) => doc.isInternal,
                        )}
                        isNeedCurrentCompanyPK={
                            this.props.isNeedCurrentCompanyPK
                        }
                        pkCertificateFiles={this.props.pkCertificateFiles}
                        stampCertificateFiles={this.props.stampCertificateFiles}
                        pkInfo={this.props.pkInfo}
                        stampInfo={this.props.stampInfo}
                        certificatePKInfo={this.props.certificatePKInfo}
                        certificateStampInfo={this.props.certificateStampInfo}
                        formTrackingName={`${trackingPrefix}_sign_step_3`}
                        formTrackingLabel={trackingLabel}
                        pkFileName={this.props.pkFileName}
                        pkPassword={this.props.pkPassword}
                        stampFileName={this.props.stampFileName}
                        stampPassword={this.props.stampPassword}
                        caServers={this.props.caServers}
                        pkCAServerIdx={this.props.pkCAServerIdx}
                        stampCAServerIdx={this.props.stampCAServerIdx}
                        pkErrorMessage={this.props.pkErrorMessage}
                        stampErrorMessage={this.props.stampErrorMessage}
                        errorMessage={this.props.errorMessage}
                        showAutopickPKError={this.props.showAutopickPKError}
                        showAutopickStampError={
                            this.props.showAutopickStampError
                        }
                        onChangeCAServer={this.handleChangeCAServer}
                        onChangePKCertificates={() =>
                            this.pkCertificatesInput.click()
                        }
                        onChangeStampCertificates={() =>
                            this.stampCertificatesInput.click()
                        }
                        onReadKey={this.props.readKey}
                        onChangePK={() => this.pkFileInput.click()}
                        onSetPKPassword={this.props.setPKPassword}
                        onChangeStamp={() => this.stampFileInput.click()}
                        onSetStampPassword={this.props.setStampPassword}
                        onSignAndSubmit={this.props.signAndSubmit}
                        onMultiSign={this.props.multiSign}
                        onToggleRememberKeys={this.props.toggleRememberKeys}
                        onClose={this.handleClose}
                        // JKS container
                        pkJksKey={this.props.pkJksKey}
                        stampJksKey={this.props.stampJksKey}
                        pkJksKeys={this.props.pkJksKeys}
                        stampJksKeys={this.props.stampJksKeys}
                        onChangeJksKey={this.props.onChangeJksKey}
                        isOnlyExtraSignatureDocuments={
                            this.props.isOnlyExtraSignatureDocuments
                        }
                        onClearPK={this.handleClearPkFile}
                        onClearStamp={this.handleClearStampFile}
                    />
                );
            case SignPopupStatus.CHECK_USB_KEY:
                return (
                    <UsbSignForm
                        isLoaded={this.props.isUSBSignerLoaded}
                        isSignOnly={shouldOnlySign(
                            this.props.docs[0],
                            this.props.currentRoleId,
                            this.props.isMultiSign,
                        )}
                        isInternal={this.props.docs.every(
                            (doc) => doc.isInternal,
                        )}
                        isSignAndSubmitProcess={
                            this.props.isSignAndSubmitProcess
                        }
                        isFindingDevices={this.props.isFindingUSBDevices}
                        useStamp={this.props.useUSBStamp}
                        usePK={this.props.useUSBPK}
                        usbSigner={this.props.usbSigner}
                        devices={this.props.devices}
                        caServers={this.props.caServers}
                        usbLoadError={this.props.usbLoadError}
                        errorMessage={this.props.errorMessage}
                        onFindDevices={this.props.onFindUSBDevices}
                        onSetProxySettings={this.props.onSetUSBProxySettings}
                        onSignAndSubmit={this.props.usbSignAndSubmit}
                        onClose={this.handleClose}
                        isPkKeyReading={this.props.isPkUSBKeyReading}
                        isPkChecked={this.props.isPkUSBChecked}
                        pkDevice={this.props.pkDevice}
                        pkInfo={this.props.pkInfo}
                        pkCaServerIdx={this.props.pkUsbCAServerIdx}
                        pkPassword={this.props.pkUSBPassword}
                        pkErrorMessage={this.props.pkErrorMessage}
                        onPkDeviceChange={this.props.onPkUSBDeviceChange}
                        onChangePkCAServer={this.props.onChangePkUSBCAServer}
                        onSetPkPassword={this.props.onSetPkUSBPassword}
                        onReadPkKey={this.props.onReadPkUSBKey}
                        onResetPkKey={this.props.onResetPkUSBKey}
                        onAddPK={this.props.onAddUSBPK}
                        isStampKeyReading={this.props.isStampUSBKeyReading}
                        isStampChecked={this.props.isStampUSBChecked}
                        stampDevice={this.props.stampDevice}
                        stampInfo={this.props.stampInfo}
                        stampCaServerIdx={this.props.stampUsbCAServerIdx}
                        stampPassword={this.props.stampUSBPassword}
                        stampErrorMessage={this.props.stampErrorMessage}
                        onStampDeviceChange={this.props.onStampUSBDeviceChange}
                        onChangeStampCAServer={
                            this.props.onChangeStampUSBCAServer
                        }
                        onSetStampPassword={this.props.onSetStampUSBPassword}
                        onReadStampKey={this.props.onReadStampUSBKey}
                        onResetStampKey={this.props.onResetStampUSBKey}
                        onAddStamp={this.props.onAddUSBStamp}
                    />
                );
            case SignPopupStatus.SIGN_IIT_WIDGET:
                return <IitSignWidget />;
            case SignPopupStatus.KEP_SIGN:
                return <KepSignForm />;
            case SignPopupStatus.DIIA_SIGN:
                return <SignDocumentDiia />;
            case SignPopupStatus.SIGNED_SUCCESSFUL:
                if (shouldShowOldSignSuccessPopup(this.props)) {
                    return <SuccesfulSignView onClose={this.props.hidePopup} />;
                }
                // Нові попапи окремо рендеряться поза межами SignPopup. Раніше статус не був окремим
                // попапом, а частиною SignPopup
                return null;
            case SignPopupStatus.SHARED_DOCUMENT_VIEW_OVERDRAFT_ERROR:
                return <OverdraftError />;
            case SignPopupStatus.SIGN_CONNECTION_ERROR: {
                const br = <br key="br" />;
                return (
                    <Fragment>
                        <Message type="error">
                            {t`Не всі документи були опрацьовані`}
                        </Message>
                        <div className={css.block}>
                            {jt`Під час опрацювання документів на Вашому комп'ютері була проблема з підключенням
                            до Інтернету, тому не всі документи були підписані та надіслані.${br}
                            Після усунення проблеми з підключенням до Інтернету, можна буде продовжити
                            обробку документів.`}
                        </div>
                        <div className={css.block}>
                            <div className={css.retryButton}>
                                <Button
                                    theme="blue"
                                    onClick={
                                        this.handleRetryAfterConnectionProblem
                                    }
                                >
                                    {t`Обробити не підписані документи`}
                                </Button>
                            </div>
                            {!this.props.isSharedDocumentViewMode && (
                                <div className={css.backButton}>
                                    <Button
                                        typeContour
                                        theme="blue"
                                        onClick={this.handleClose}
                                    >
                                        {t`Повернутись до документів`}
                                    </Button>
                                </div>
                            )}
                        </div>
                        {this.state.showConnectionProblemError && (
                            <div className={css.block}>
                                <Message sizeSmall type="error">
                                    {t`Немає доступу до Інтернету`}.
                                </Message>
                            </div>
                        )}
                    </Fragment>
                );
            }
            case SignPopupStatus.SIGN_ERROR:
                return (
                    <FlexBox direction="column">
                        <Message>{t`Йой! Щось пішло не так.`}</Message>
                        <Alert type="error" hideIcon>
                            {this.props.errorCode ===
                            BILLING_OVERLIMIT_ERROR_CODE ? (
                                <Fragment>
                                    {t`У вас закінчились документи на балансі`}.{' '}
                                    <br />
                                    {t`Кількість документів, що не вдалося обробити через нестачу: 1`}
                                    <br />
                                    <a
                                        href={BILL_GENERATION_PAGE_PATTERN}
                                    >{t`Поповніть баланс`}</a>
                                </Fragment>
                            ) : (
                                <Fragment>
                                    {this.props.errorMessage ||
                                        t`Документи не вдалося підписати та надіслати`}
                                </Fragment>
                            )}
                        </Alert>
                        {!this.props.isSharedDocumentViewMode && (
                            <div className={css.block}>
                                <div className={css.backButton}>
                                    <Button
                                        typeContour
                                        theme="blue"
                                        onClick={this.handleClose}
                                    >
                                        {t`Повернутись до документів`}
                                    </Button>
                                </div>
                            </div>
                        )}
                    </FlexBox>
                );
            case SignPopupStatus.MULTI_SIGN_ERROR:
                return (
                    <DocumentsSignSummary
                        results={this.props.multiSignResult}
                    />
                );
            default:
                return null;
        }
    };

    handleChangeCAServer = (idx, isPK) => {
        this.props.changeCAServer(idx, isPK);
        if (isPK) {
            this.pkCertificatesInput.value = null;
        } else {
            this.stampCertificatesInput.value = null;
        }
    };

    handleRetryAfterConnectionProblem = () => {
        this.setState({ showConnectionProblemError: false });

        if (navigator.onLine) {
            this.props.retrySignAndSubmit();
        } else {
            this.setState({ showConnectionProblemError: true });
        }
    };

    handlePKFileChange = (evt) => {
        const [file] = evt.currentTarget.files;

        if (file) {
            this.props.submitPK(file);
        }
        this.pkCertificatesInput.value = null;
    };

    handleStampFileChange = async (evt) => {
        const file = evt.currentTarget.files[0];
        if (file) {
            // case when user try to change checked stamp to pk (reset to init state)
            if (this.props.isStampChecked && !this.props.isUploadedPK) {
                await this.props.onResetSignSteps();
                await this.props.submitPK(file);
            } else {
                await this.props.submitStamp(file);
            }
        }
        this.stampCertificatesInput.value = null;
    };

    handlePKCertificatesChange = (evt) => {
        const files = evt.currentTarget.files;
        if (files.length > 0) {
            this.props.submitPKCertificates(files);
        }
    };

    handleStampCertificatesChange = (evt) => {
        const files = evt.currentTarget.files;
        if (files.length > 0) {
            this.props.submitStampCertificates(files);
        }
    };

    handleClose = () => {
        if (
            this.props.flags['ENABLE_SIGNATURE_CANCELLATION_CONFIRMATION'] &&
            this.props.isNeedConfirmClosed
        ) {
            this.props.setShowConfirmClose(true);
            return;
        }

        this.props.hidePopup();
        if (this.props.popupStatus === SignPopupStatus.MULTI_SIGN_ERROR) {
            eventTracking.sendEvent('popup_cant_sign_docs', 'close');
        }
    };

    renderTitle() {
        if (this.props.popupStatus === SignPopupStatus.CHOOSE_RECIPIENT) {
            return t`Вкажіть ЄДРПОУ та email контрагента`;
        }
        if (this.props.popupStatus === SignPopupStatus.CHECK_PRIVATE_KEY) {
            return t`Підписати ключем КЕП/ЕЦП`;
        }
        if (this.props.popupStatus === SignPopupStatus.CHOOSE_DOCUMENT_FIELDS) {
            return t`Заповніть обов’язкові для контрагента поля`;
        }
        if (this.props.popupStatus === SignPopupStatus.UPLOAD_PRIVATE_KEY) {
            return t`Вкажіть ключ КЕП/ЕЦП`;
        }
        if (this.props.popupStatus === SignPopupStatus.CHECK_USB_KEY) {
            return t`Підписати апаратним ключем`;
        }
        if (
            this.props.popupStatus === SignPopupStatus.MULTI_SIGN_ERROR ||
            this.props.popupStatus === SignPopupStatus.SIGNED_SUCCESSFUL ||
            this.props.popupStatus === SignPopupStatus.DIIA_SIGN ||
            this.props.popupStatus === SignPopupStatus.KEP_SIGN
        ) {
            return null;
        }

        if (this.props.popupStatus === SignPopupStatus.CREATE_FLOWS) {
            return t`Вкажіть ЄДРПОУ та email контрагентів`;
        }
        if (this.props.isMultiSign) {
            const allInternalDocs = this.props.docs.every(
                (doc) => doc.isInternal,
            );
            if (!allInternalDocs) {
                return t`Підписання документів`;
            }
            return t`Підписання та надіслання документів`;
        }
        return '';
    }

    render() {
        if (
            this.props.popupStatus === SignPopupStatus.INIT ||
            this.props.popupStatus === SignPopupStatus.KEP_NEW_SIGN ||
            this.props.popupStatus === SignPopupStatus.SIGN_IN_PROGRESS
        ) {
            return null;
        }

        const isSignedSuccessfullView =
            this.props.popupStatus === SignPopupStatus.SIGNED_SUCCESSFUL;
        const isDiiaSigningView =
            this.props.popupStatus === SignPopupStatus.DIIA_SIGN;
        const isKepSigningView =
            this.props.popupStatus === SignPopupStatus.KEP_SIGN;
        const isDelnotWarningStatus =
            this.props.popupStatus === SignPopupStatus.DELNOT_WARNING;
        const isSharedDocumentViewOverdraftError =
            this.props.popupStatus ===
            SignPopupStatus.SHARED_DOCUMENT_VIEW_OVERDRAFT_ERROR;

        if (isDelnotWarningStatus) {
            return (
                <DelnotWarningPopup
                    onAction={this.props.onDelnotWarningAgree}
                    onClose={this.props.hidePopup}
                />
            );
        }

        const title = this.renderTitle();

        const isDefaultView =
            !isSignedSuccessfullView && !isDiiaSigningView && !isKepSigningView;

        const isShowFooter =
            !isDelnotWarningStatus &&
            !isKepSigningView &&
            !isSignedSuccessfullView &&
            !isSharedDocumentViewOverdraftError;

        const isRequiredDocumentFields =
            this.props.popupStatus === SignPopupStatus.CHOOSE_DOCUMENT_FIELDS;

        const content = this.getChildren();

        if (!content) {
            return null;
        }

        return (
            <Popup
                footer={isShowFooter}
                active={this.props.isActive}
                title={title}
                isCloseByButtonOnly={
                    isDefaultView || this.props.isShowConfirmClose
                }
                hiddenCloseButton={this.props.isShowConfirmClose}
                onClose={this.handleClose}
                dataQa="qa_ecp_popup"
                isDraggable={isRequiredDocumentFields}
                className={cn({
                    [css.popupWhiteBackground]:
                        this.props.popupStatus ===
                            SignPopupStatus.SIGN_IIT_WIDGET.value ||
                        this.props.popupStatus ===
                            SignPopupStatus.DIIA_SIGN.value,
                })}
            >
                <div className={css.root} data-qa="qa_ecp_sign_popup">
                    <Input
                        hidden={!config.TEST}
                        key={this.props.popupStatus}
                        type="file"
                        refFn={(el) => {
                            this.pkFileInput = el;
                        }}
                        onChange={this.handlePKFileChange}
                        dataQa="qa_ecp_key_upload"
                        accept={ACCEPT_SIGNATURE_EXTENSIONS}
                    />
                    <Input
                        hidden={!config.TEST}
                        type="file"
                        refFn={(el) => {
                            this.stampFileInput = el;
                        }}
                        onChange={this.handleStampFileChange}
                        dataQa="qa_ecp_stamp_upload"
                        accept={ACCEPT_SIGNATURE_EXTENSIONS}
                    />
                    <Input
                        hidden={!config.TEST}
                        multiple
                        type="file"
                        refFn={(el) => {
                            this.pkCertificatesInput = el;
                        }}
                        onChange={this.handlePKCertificatesChange}
                        dataQa="qa_ecp_certificates_upload"
                    />
                    <Input
                        hidden={!config.TEST}
                        multiple
                        type="file"
                        refFn={(el) => {
                            this.stampCertificatesInput = el;
                        }}
                        onChange={this.handleStampCertificatesChange}
                        dataQa="qa_ecp_certificates_second_upload"
                    />
                    <div>{content}</div>
                </div>
            </Popup>
        );
    }
}

export default SignPopup;
