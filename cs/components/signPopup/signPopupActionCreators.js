/* eslint-disable complexity */
import { prefetchPromoBanner } from 'components/SignSuccessPopup/hooks';
import { getSelectedDocuments } from 'components/documentList/selectors';
import { push } from 'connected-react-router';
import {
    EDI_DELNOT_DOC_TYPE,
    ROZETKA_EDRPOUS,
    WS_KEY_DOCUMENT_RECIPIENTS,
    WS_KEY_REMEMBER_KEYS,
} from 'lib/constants';
import {
    formatError,
    getDocumentsGraphArguments,
    getFormatDocumentForSendToBackend,
    logErrorByLevel,
    stringToUint8Array,
} from 'lib/helpers';
import {
    decryptStoredKeys,
    encryptStoredKeys,
    getLocalStorageItem,
    removeLocalStorageItem,
    setLocalStorageItem,
} from 'lib/webStorage';
import { isFormTrackerInstance } from 'services/analytics/formTracking';
import { hasUnlimitedRate } from 'services/billing';
import { SignDocValidationResultType } from 'services/documents/ts/enums';
import {
    canSignDocs,
    fetchDocumentsCheckRecipientRequiredFields,
    getDocumentsWithoutRequiredFieldsByEdrpou,
    getIsVersionedDocument,
    getIsVersionedDocumentFlow,
    validateSignDocs,
} from 'services/documents/ts/utils';
import { shouldSendAfterSign } from 'services/documents/utils';
import {
    ACSK,
    ApplicationMode,
    DocumentSource,
    FeatureFlags,
    SignPopupStatus,
    SignatureFormat,
} from 'services/enums';
import { getLocationId } from 'services/navigation-structure';
import { changeCAServers, initUSBSigner } from 'services/signer/usb';
import {
    autopickServers,
    formatSignResult,
    formatSignResultCtx,
    getACSK,
    getCAServerIdxByFileName,
} from 'services/signer/utils';
import { getRegistrationToken, registerSigner } from 'services/user';
import { t } from 'ttag';

import { RequiredFieldsCallbacks } from '../RequiredFieldsResolver/types';

import kepMobileAppPopupActionCreators from '../KepMobileAppPopup/kepMobileAppPopupActionCreators';
import requiredFieldsResolverActionCreators from '../RequiredFieldsResolver/requiredFieldsResolverActionCreators';
import appActionCreators from '../app/appActionCreators';
import documentActionCreators from '../document/documentActionCreators';
import documentListActionCreators from '../documentList/documentListActionCreators';
import headerActionCreators from '../header/headerActionCreators';
import notificationActionCreators from '../notificationCenter/notificationCenterActionCreators';
import uploaderActionCreators from '../uploader/uploaderActionCreators';
import actions from './signPopupActions';

import {
    signMultiDocsOtherErrors,
    signMultiDocsValidationErrors,
} from '../documentsSignSummary/helpers';
import {
    isDocumentsWithoutCurrentCompanyPK,
    shouldRegisterSignSessionSigner,
} from './helpers';
import {
    deleteSignedDocumentFromStorage,
    deleteSignedDocumentsFromStorage,
    errorContext,
    getKeys,
    getKeysFromWidgetIit,
    getUnsuccessfullySignedList,
    setUnsignedDocumentsToStorage,
    updateCertificateInfo,
    validateKeyFile,
    validateKeyInfo,
    validateUSBKeyInfo,
} from './utils';

import eventTracking from '../../services/analytics/eventTracking';
import {
    changeDocumentRecipient,
    getDocumentVersions,
    sendDocument as sendDocumentAPI,
    signDocument as signDocumentAPI,
} from '../../services/documents/api';
import signer from '../../services/signer/signer';
import { signWithKepFlowActions } from '../SignWithKepFlow/signWithKepFlowSlice';
import { isDocumentRequiredFieldsError } from '../changeDocumentPopup/helper';

const POPUP_STATUS_ORDER = [
    SignPopupStatus.INIT,
    SignPopupStatus.MULTI_SIGN_ERROR,
    SignPopupStatus.CHOOSE_RECIPIENT,
    SignPopupStatus.CHOOSE_DOCUMENT_FIELDS,
    SignPopupStatus.CREATE_FLOWS,
    SignPopupStatus.DELNOT_WARNING,
    SignPopupStatus.UPLOAD_PRIVATE_KEY,
    SignPopupStatus.CHECK_PRIVATE_KEY,
    SignPopupStatus.CHECK_USB_KEY,
    SignPopupStatus.SIGN_IIT_WIDGET,
    SignPopupStatus.SIGN_IN_PROGRESS,
    SignPopupStatus.AFTER_SIGN,
    SignPopupStatus.SIGN_CONNECTION_ERROR,
    SignPopupStatus.MULTI_SIGN_ERROR,
    SignPopupStatus.SIGN_ERROR,
    SignPopupStatus.SIGNED_SUCCESSFUL,
];

function showNextStatus() {
    return async (dispatch, getState) => {
        const {
            signPopup: {
                docs,
                origin,
                isActive,
                popupStatus,
                emailRecipient,
                edrpouRecipient,
                isRecipientEmailHidden,
                isMultiSign,
                multiSignResult,
                isConnectionProblemOccurred,
                signStatus,
                signDiia,
                signKep,
                signKepNew,
            },
            requiredFieldsResolver: { withoutRequiredFieldsDocs },
            app: { currentUser },
            documentList: {
                isSelectedAll: isSelectedAllDocuments,
                documentsCount: docsByFilterCount,
            },
        } = getState();

        const isVersionedDocumentFlow = docs.some((doc) =>
            getIsVersionedDocumentFlow(doc),
        );

        if (isVersionedDocumentFlow && !isActive) {
            if (isMultiSign) {
                dispatch({
                    type:
                        actions.SIGN_POPUP__SHOW_FIRST_SIGN_VERSIONED_DOCS_POPUP,
                });
            }
            if (!isMultiSign) {
                const doc = docs[0];

                try {
                    const {
                        versions: actualVersions,
                    } = await getDocumentVersions(doc.id, currentUser);

                    const isUserSignedActualVersion =
                        doc.versions[0].id === actualVersions[0].id;

                    if (!isUserSignedActualVersion) {
                        dispatch({
                            type:
                                actions.SIGN_POPUP__SHOW_EXIST_NEW_DOC_VERSION_POPUP,
                        });
                    } else {
                        dispatch({
                            type:
                                actions.SIGN_POPUP__SHOW_FIRST_SIGN_VERSIONED_DOCS_POPUP,
                        });
                    }
                } catch (error) {
                    dispatch(
                        notificationActionCreators.addNotification({
                            type: 'text',
                            textType: 'error',
                            text: `${t`Сталася помилка, зверніться в службу підтримки.`}: ${
                                error.message
                            }`,
                            showCloseButton: true,
                            autoClose: 5000,
                        }),
                    );
                }
            }
        }

        // Find next statuses for current status
        const statusIdx = POPUP_STATUS_ORDER.indexOf(popupStatus);
        const nextStates =
            statusIdx !== -1 ? POPUP_STATUS_ORDER.slice(statusIdx + 1) : [];

        // Choose status for popup from next status
        for (const nextStatus of nextStates) {
            if (nextStatus === SignPopupStatus.CHOOSE_RECIPIENT) {
                // For document without filled recipient, show step for choosing recipient
                const doc = docs[0];
                const recipientIsFilled =
                    edrpouRecipient &&
                    (emailRecipient || isRecipientEmailHidden);
                const isBilateral = !doc.isInternal && !doc.isMultilateral;
                if (
                    !isMultiSign &&
                    isBilateral &&
                    !doc.isInput &&
                    !recipientIsFilled
                ) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: nextStatus,
                    });
                    break;
                }
            } else if (nextStatus === SignPopupStatus.CHOOSE_DOCUMENT_FIELDS) {
                if (!isMultiSign && withoutRequiredFieldsDocs.length) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: nextStatus,
                    });
                    break;
                }
            } else if (nextStatus === SignPopupStatus.CREATE_FLOWS) {
                // For multilateral document without flow, show form for filling recipients
                const doc = docs[0];
                if (
                    !isMultiSign &&
                    doc.isMultilateral &&
                    doc.flows.length === 0
                ) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: nextStatus,
                    });
                    break;
                }
            } else if (nextStatus === SignPopupStatus.DELNOT_WARNING) {
                // Show warning about DELNOT only for EDI documents
                const isEDIDocSelected = docs.some(
                    (doc) =>
                        doc.type !== EDI_DELNOT_DOC_TYPE &&
                        ROZETKA_EDRPOUS.includes(edrpouRecipient),
                );
                if (isEDIDocSelected) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: nextStatus,
                    });
                    break;
                }
            } else if (nextStatus === SignPopupStatus.SIGN_CONNECTION_ERROR) {
                if (isConnectionProblemOccurred) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: nextStatus,
                    });
                    break;
                }
            } else if (nextStatus === SignPopupStatus.MULTI_SIGN_ERROR) {
                // Show documents sign summary only if multi sign result exists with validation errors
                if (
                    multiSignResult &&
                    multiSignResult.some(
                        (r) => r.validation !== SignDocValidationResultType.OK,
                    )
                ) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: nextStatus,
                    });
                    break;
                }
            } else if (nextStatus === SignPopupStatus.SIGN_ERROR) {
                if (!signStatus.success) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: nextStatus,
                    });
                    break;
                }
            } else if (
                nextStatus === SignPopupStatus.UPLOAD_PRIVATE_KEY &&
                signKepNew
            ) {
                dispatch({
                    type: actions.SIGN_POPUP__SHOW,
                    status: SignPopupStatus.KEP_NEW_SIGN,
                });
                dispatch(
                    signWithKepFlowActions.start({
                        docs,
                        meta: {
                            origin,
                            totalDocsCount: isSelectedAllDocuments
                                ? docsByFilterCount
                                : docs.length,
                        },
                    }),
                );
                break;
            } else if (
                nextStatus === SignPopupStatus.SIGNED_SUCCESSFUL &&
                signKepNew
            ) {
                const isAllSignsSuccessful = multiSignResult
                    ? multiSignResult.every((r) => r.success)
                    : signStatus.success;

                // if all signs are successful, show new success animation
                if (isAllSignsSuccessful) {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: SignPopupStatus.KEP_NEW_SIGN,
                    });
                    dispatch(signWithKepFlowActions.showSuccessAnimation());
                }
                // otherwise - show old success popup
                else {
                    dispatch({
                        type: actions.SIGN_POPUP__SHOW,
                        status: SignPopupStatus.SIGNED_SUCCESSFUL,
                    });
                }

                break;
            } else if (
                nextStatus === SignPopupStatus.UPLOAD_PRIVATE_KEY &&
                signDiia
            ) {
                dispatch({
                    type: actions.SIGN_POPUP__SHOW,
                    status: SignPopupStatus.DIIA_SIGN,
                });
                break;
            } else if (
                nextStatus === SignPopupStatus.UPLOAD_PRIVATE_KEY &&
                signKep
            ) {
                dispatch({
                    type: actions.SIGN_POPUP__SHOW,
                    status: SignPopupStatus.KEP_SIGN,
                });
                break;
            } else {
                // In other cases, just move to the next state
                dispatch({
                    type: actions.SIGN_POPUP__SHOW,
                    status: nextStatus,
                });
                break;
            }
        }
    };
}

function showPrevStatus() {
    return (dispatch, getState) => {
        const {
            signPopup: { popupStatus },
        } = getState();

        if (
            popupStatus === SignPopupStatus.CHOOSE_DOCUMENT_FIELDS ||
            popupStatus === SignPopupStatus.UPLOAD_PRIVATE_KEY
        ) {
            dispatch({
                type: actions.SIGN_POPUP__PREV_STATUS,
                status: SignPopupStatus.CHOOSE_RECIPIENT,
            });
        }
        if (
            popupStatus === SignPopupStatus.CHECK_PRIVATE_KEY ||
            popupStatus === SignPopupStatus.CHECK_USB_KEY
        ) {
            dispatch({
                type: actions.SIGN_POPUP__PREV_STATUS,
                status: SignPopupStatus.UPLOAD_PRIVATE_KEY,
            });
        }
    };
}

function leaveDocumentsOnlyReadySignature(docs) {
    return (dispatch) => {
        dispatch({
            type: actions.SIGN_POPUP__LEAVE_DOCUMENTS_ONLY_READY_SIGNATURE,
            docs: docs,
        });
    };
}

function initPopup(actionData) {
    return async (dispatch, getState) => {
        const state = getState();
        const {
            app: {
                currentUser: {
                    currentCompany,
                    currentRole: { id: roleId },
                },
            },
        } = state;

        const selectedDocuments = getSelectedDocuments(state);

        const { edrpou } = currentCompany;
        const docs = actionData.docs;
        const isMultiSign = docs.length > 1;
        const isVersionedDocumentFlow = docs.some(getIsVersionedDocumentFlow);

        let emailRecipient = '';
        let edrpouRecipient = '';
        let isRecipientEmailHidden = false;
        let emailOwner = '';
        let edrpouOwner = '';

        if (!isMultiSign) {
            const doc = docs[0];
            emailRecipient =
                actionData.emailRecipient || doc.emailRecipient || '';
            edrpouRecipient =
                actionData.edrpouRecipient || doc.edrpouRecipient || '';
            isRecipientEmailHidden =
                actionData.isRecipientEmailHidden ||
                doc.isRecipientEmailHidden ||
                false;
            emailOwner = doc.emailOwner;
            edrpouOwner = doc.edrpouOwner;
        }

        let multiSignResult = [];
        if (
            isMultiSign &&
            !actionData.isSignAllDocuments &&
            !canSignDocs(docs, roleId, edrpou)
        ) {
            multiSignResult = validateSignDocs(docs, roleId, edrpou);
        }

        dispatch({
            type: actions.SIGN_POPUP__INIT,
            isActive: !isVersionedDocumentFlow,
            isMultiSign,
            emailRecipient,
            isRecipientEmailHidden,
            edrpouRecipient,
            emailOwner,
            edrpouOwner,
            multiSignResult,
            docs: actionData.docs,
            isSignAllDocuments: actionData.isSignAllDocuments,
            totalDocsCount: actionData.totalDocsCount,
            origin: actionData.origin,
            signDiia: actionData.isDiia,
            signKep: actionData.isKep,
            signKepNew: actionData.isKepNew,
            isSignActAnnulment: actionData.isSignActAnnulment,
        });

        // Поки користувач підписує документи, ми на фоні підвантажуємо інформацію про те,
        // який промо банер треба буде показати після успішного підписання
        void prefetchPromoBanner(roleId);

        if (isMultiSign) {
            if (multiSignResult.length) {
                // Errors from validation
                const docsMapInput = signMultiDocsValidationErrors(
                    multiSignResult,
                );

                // Errors from server
                const otherErrors = signMultiDocsOtherErrors(multiSignResult);

                if (Array.from(docsMapInput).length || otherErrors.length) {
                    dispatch(showNextStatus());
                }
                return;
            } else {
                const withoutRequiredFieldsDocs = await fetchDocumentsCheckRecipientRequiredFields(
                    docs,
                    currentCompany,
                );

                if (
                    withoutRequiredFieldsDocs.length &&
                    selectedDocuments.length !==
                        withoutRequiredFieldsDocs.length
                ) {
                    dispatch(
                        requiredFieldsResolverActionCreators.onSetData({
                            requiredFieldsCallbackName:
                                RequiredFieldsCallbacks.SIGN_POPUP__INIT_POPUP,
                            withoutRequiredFieldsDocs,
                        }),
                    );

                    const notNeedRequiredFields = selectedDocuments.filter(
                        (doc) => {
                            return !withoutRequiredFieldsDocs.some(
                                (rDoc) => rDoc.doc.id === doc.id,
                            );
                        },
                    );

                    if (notNeedRequiredFields.length) {
                        dispatch(
                            leaveDocumentsOnlyReadySignature(
                                notNeedRequiredFields,
                            ),
                        );

                        dispatch(showNextStatus());

                        return;
                    }
                }

                if (withoutRequiredFieldsDocs.length) {
                    dispatch(
                        requiredFieldsResolverActionCreators.onSetData({
                            requiredFieldsCallbackName:
                                RequiredFieldsCallbacks.SIGN_POPUP__INIT_POPUP,
                            withoutRequiredFieldsDocs,
                        }),
                    );
                    dispatch({
                        type: actions.SIGN_POPUP__REQUIRED_FIELDS_ERROR,
                    });
                } else {
                    dispatch(showNextStatus());
                }
                return;
            }
        }

        // TODO refactor this hot fix
        const isCurrentCompanyRecipient = edrpouRecipient === edrpou;

        const isNeedCheckDocWithRecipient =
            (emailRecipient || isRecipientEmailHidden) &&
            edrpouRecipient &&
            !isCurrentCompanyRecipient &&
            !isMultiSign;

        // якщо є багатосторонній документ то завжди запускаємо перевірку на обовязкові поля
        const isNeedCheckDocWithoutRecipient = docs.some(
            ({ flows }) => (flows || []).length > 0,
        );

        // TODO refactor this
        if (isNeedCheckDocWithRecipient || isNeedCheckDocWithoutRecipient) {
            const doc = selectedDocuments[0] || docs[0];
            const statusId = doc.statusId;
            const isSourceEDI = docs?.[0].source === DocumentSource.edi;

            if (
                statusId === 7004 ||
                statusId === 7006 ||
                statusId === 7007 ||
                statusId === 7008 ||
                isSourceEDI
            ) {
                dispatch(showNextStatus());
                return;
            }

            const withoutRequiredFieldsDocs = await fetchDocumentsCheckRecipientRequiredFields(
                [doc],
                currentCompany,
            );

            const withoutRequiredFieldsDoc = withoutRequiredFieldsDocs?.[0];

            if (withoutRequiredFieldsDoc?.needFields?.length) {
                dispatch(
                    requiredFieldsResolverActionCreators.onSetData({
                        requiredFieldsCallbackName:
                            RequiredFieldsCallbacks.SIGN_POPUP__INIT_POPUP,
                        withoutRequiredFieldsDocs,
                    }),
                );
                dispatch({
                    type: actions.SIGN_POPUP__REQUIRED_FIELDS_ERROR,
                });

                eventTracking.sendEvent('form_doc_sign_step_1_1', 'show');
            } else {
                dispatch(showNextStatus());
            }
        } else {
            dispatch(showNextStatus());
        }
    };
}

function showPopup(actionData) {
    return (dispatch, getState) => {
        const {
            signPopup: { isSignAndSubmitProcess },
        } = getState();
        if (isSignAndSubmitProcess) {
            dispatch(
                appActionCreators.onAlertPopupShow(
                    t`Увага!`,
                    t`Функція недоступна, масове підписання документів не завершено.`,
                ),
            );
        } else {
            dispatch(initPopup(actionData));
        }
    };
}

function hidePopup() {
    return async (dispatch, getState) => {
        const {
            signPopup: { usbSigner, signKep },
        } = getState();

        dispatch({ type: actions.SIGN_POPUP__HIDE });
        // Clear keys context when closing sign popup to prevent keys duplication on next sign process
        if (config.VSIGNER_USE_CONTEXT) await signer.clearKeysCtx();

        if (signKep) {
            dispatch(kepMobileAppPopupActionCreators.onShow());
        }

        if (usbSigner) {
            try {
                await usbSigner.resetPrivateKey();
            } catch (err) {
                logErrorByLevel('Error on reset usb key');
            }
        }
    };
}

function changeCAServer(caServerIdx, isPK) {
    return async (dispatch, getState) => {
        const {
            signPopup: { caServers },
        } = getState();
        if (caServerIdx === undefined) {
            dispatch({
                type: actions.SIGN_POPUP__CHANGE_CA_SERVER,
                isPK,
                caServerIdx,
                loadCertsFromFile: false,
            });
            return {};
        }
        const caServerSettings = await signer.getCAServerSettings(
            caServers[caServerIdx],
        );
        dispatch({
            type: actions.SIGN_POPUP__CHANGE_CA_SERVER,
            isPK,
            caServerIdx,
            loadCertsFromFile: caServerSettings.loadCertsFromFile,
        });
        return caServerSettings;
    };
}

function onChangeJksKey(privateKeyName, isPK) {
    return (dispatch, getState) => {
        const { signPopup: state } = getState();
        const jksKeys = isPK ? state.pkJksKeys : state.stampJksKeys;
        const jksKey = jksKeys.find(
            (item) => item.privateKeyName === privateKeyName,
        );
        dispatch({
            type: isPK
                ? actions.SIGN_POPUP__SET_PK_JKS_KEY
                : actions.SIGN_POPUP__SET_STAMP_JKS_KEY,
            jksKey,
        });
    };
}

function readKey(isPK) {
    return async (dispatch, getState) => {
        dispatch({ type: actions.SIGN_POPUP__START_CHECK_KEY, isPK });

        const { signPopup: state, app: appState } = getState();
        const {
            currentUser: {
                currentCompany: { edrpou: userEdrpou },
            },
        } = appState;
        const password = isPK ? state.pkPassword : state.stampPassword;
        const errorType = isPK ? 'pkErrorMessage' : 'stampErrorMessage';
        const keyContextId = isPK ? state.pkContextId : state.stampContextId;
        const doc = state.docs[0];
        let caServerIdx = isPK ? state.pkCAServerIdx : state.stampCAServerIdx;
        let caName = null;
        let key = null;

        if (config.VSIGNER_USE_CONTEXT && keyContextId) {
            await signer.clearKeysCtx(keyContextId);
        }

        try {
            if (typeof caServerIdx === 'number') {
                caName = state.caServers[caServerIdx].name;
                const jksKey = isPK ? state.pkJksKey : state.stampJksKey;

                if (jksKey) {
                    const { keyData, certificates } = jksKey;
                    key = await signer.readJksKey(
                        keyData,
                        certificates,
                        password,
                        caServerIdx,
                    );
                } else {
                    key = await signer.readKey(
                        isPK ? state.pkFile : state.stampFile,
                        password,
                        caServerIdx,
                        isPK
                            ? state.pkCertificateFiles
                            : state.stampCertificateFiles,
                    );
                }
            } else {
                [key, caServerIdx] = await autopickServers(
                    isPK ? state.pkFile : state.stampFile,
                    password,
                );
                if (key && typeof caServerIdx === 'number') {
                    caName = state.caServers[caServerIdx].name;
                    await dispatch(changeCAServer(caServerIdx, isPK));
                } else {
                    dispatch({
                        type: actions.CHECK_KEY_POPUP__SHOW_AUTOPICK_ERROR,
                        error: {
                            [`showAutopick${isPK ? 'PK' : 'Stamp'}Error`]: true,
                        },
                    });
                    return;
                }
            }

            validateKeyInfo(
                isPK,
                key.keyInfo,
                key.certificateInfo,
                caName,
                userEdrpou,
                doc,
                state,
            );

            const keyStateData = {
                keyContextId: key.keyContextId,
                keyData: key.keyData,
                certificates: key.certificates,
                keyInfo: key.keyInfo,
                certificateInfo: key.certificateInfo,
            };

            if (isPK && key.certificateInfo.isStamp && !state.isStampChecked) {
                // user uploaded stamp instead of key
                dispatch(readStampKeyInsteadPk(keyStateData));
            } else {
                dispatch({
                    type: actions.SIGN_POPUP__FINISH_CHECK_KEY,
                    isPK,
                    ...keyStateData,
                });
                dispatch({
                    type: isPK
                        ? actions.SIGN_POPUP__DISABLE_PK_EDIT
                        : actions.SIGN_POPUP__DISABLE_STAMP_EDIT,
                });
            }

            // we can't sign documents only with stamp, so we check if document has PK
            dispatch(getIsNeedCurrentCompanyPk(key));

            await updateCertificateInfo(
                key.certificateInfo,
                key.keyInfo,
                'file_key',
            );
        } catch (err) {
            // Need to clear key context if key is already read
            if (config.VSIGNER_USE_CONTEXT && key)
                await signer.clearKeyCtx(key.keyContextId);

            const errMessage = err.message
                ? err.message.replace('{caName}', caName)
                : t`На жаль, не вдалося зчитати ключ через проблеми з АЦСК, спробуйте знову через кілька хвилин.`;
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                [errorType]: errMessage,
            });

            logErrorByLevel(
                'Error on check key before signing document',
                errorContext(err, errorType, doc.id, state, appState),
                err.level,
            );
        }
    };
}

function getIsNeedCurrentCompanyPk(key) {
    return async (dispatch, getState) => {
        const { signPopup: state, app: appState } = getState();
        const {
            currentUser: {
                currentCompany: { edrpou: userEdrpou },
            },
        } = appState;

        const isNeedCurrentCompanyPK =
            key.certificateInfo.isStamp &&
            !state.isPKChecked &&
            isDocumentsWithoutCurrentCompanyPK(state.docs, userEdrpou);

        dispatch({
            type: actions.SIGN_POPUP__CHECK_CURRENT_COMPANY_PK,
            isNeedCurrentCompanyPK,
        });
    };
}

function readStampKeyInsteadPk(keyStateData) {
    return async (dispatch, getState) => {
        const { signPopup: state } = getState();
        if (state.stampFile) {
            dispatch({ type: actions.SIGN_POPUP__CLEAR_STAMP_DATA });
        }
        dispatch({
            type: actions.SIGN_POPUP__SUBMIT_STAMP,
            stampFile: state.pkFile,
            password: state.pkPassword,
        });
        dispatch({
            type: actions.SIGN_POPUP__CHANGE_CA_SERVER,
            isPK: false,
            caServerIdx: state.pkCAServerIdx,
            loadCertsFromFile: state.isLoadPKCertsFromFile,
        });
        if (state.pkCertificateFiles) {
            dispatch({
                type: actions.SIGN_POPUP__SUBMIT_STAMP_CERTIFICATES,
                stampCertificateFiles: state.pkCertificateFiles,
            });
        }
        if (state.pkJksKey) {
            dispatch({
                type: actions.SIGN_POPUP__SET_STAMP_JKS_KEYS,
                jksKeys: state.pkJksKeys,
            });
            dispatch({
                type: actions.SIGN_POPUP__SET_STAMP_JKS_KEY,
                jksKey: state.pkJksKey,
            });
        }
        dispatch({
            type: actions.SIGN_POPUP__FINISH_CHECK_KEY,
            isPK: false,
            ...keyStateData,
        });
        dispatch({ type: actions.SIGN_POPUP__CLEAR_PK_DATA });
        dispatch({ type: actions.SIGN_POPUP__REMOVE_PK });
        dispatch({ type: actions.SIGN_POPUP__DISABLE_STAMP_EDIT });
    };
}

function onSubmitKeysFromStorage() {
    return async (dispatch, getState) => {
        const keysData = decryptStoredKeys(
            getLocalStorageItem(WS_KEY_REMEMBER_KEYS),
        );
        const {
            app: {
                currentUser: {
                    currentRole: {
                        company: { id },
                    },
                },
            },
        } = getState();
        const data = keysData ? keysData[id] : null;

        if (data) {
            dispatch({
                type: actions.SIGN_POPUP__TOGGLE_REMEMBER_KEYS,
                isRememberedKeys: true,
            });
            let loadedPk;
            let loadedStamp;

            if (data.pkFile) {
                loadedPk = await signer.loadKeyFile({
                    file: data.pkFile,
                    fileName: data.pkFileName,
                    password: data.pkPassword,
                    caServerAddress: data.pkCAServerAddress,
                    jksKeyName: data.pkJksKeyName,
                });
                dispatch({
                    type: actions.SIGN_POPUP__SUBMIT_PK,
                    pkFile: loadedPk.file,
                    pkFileName: loadedPk.file.name,
                    password: loadedPk.password,
                });

                // Handle JKS container
                if (loadedPk.jksKeyName) {
                    const jksKeys = await signer.readJksFile(loadedPk.file);
                    dispatch({
                        type: actions.SIGN_POPUP__SET_PK_JKS_KEYS,
                        jksKeys,
                    });
                    onChangeJksKey(loadedPk.jksKeyName, true)(
                        dispatch,
                        getState,
                    );
                }

                dispatch({ type: actions.SIGN_POPUP__DISABLE_PK_EDIT });
                dispatch({
                    type: actions.SIGN_POPUP__SET_PK_PASSWORD,
                    password: loadedPk.password,
                    pkErrorMessage: '',
                });
            }
            if (data.stampFile) {
                loadedStamp = await signer.loadKeyFile({
                    file: data.stampFile,
                    fileName: data.stampFileName,
                    password: data.stampPassword,
                    caServerAddress: data.stampCAServerAddress,
                    jksKeyName: data.stampJksKeyName,
                });
                dispatch({
                    type: actions.SIGN_POPUP__SUBMIT_STAMP,
                    stampFile: loadedStamp.file,
                    stampFileName: loadedStamp.file.name,
                    password: loadedStamp.password,
                });

                // Handle JKS container
                if (loadedStamp.jksKeyName) {
                    const jksKeys = await signer.readJksFile(loadedStamp.file);
                    dispatch({
                        type: actions.SIGN_POPUP__SET_STAMP_JKS_KEYS,
                        jksKeys,
                    });
                    onChangeJksKey(loadedStamp.jksKeyName, false)(
                        dispatch,
                        getState,
                    );
                }

                dispatch({ type: actions.SIGN_POPUP__DISABLE_STAMP_EDIT });
                dispatch({
                    type: actions.SIGN_POPUP__SET_STAMP_PASSWORD,
                    password: loadedStamp.password,
                    stampErrorMessage: '',
                });
            }

            if (loadedPk) {
                const caServerSettings = await dispatch(
                    changeCAServer(loadedPk.caServerIdx, true),
                );
                // TODO[TK]: get rid of this condition when we will remember certificates from file
                if (!caServerSettings.loadCertsFromFile)
                    await dispatch(readKey(true));
            }

            if (loadedStamp) {
                const caServerSettings = await dispatch(
                    changeCAServer(loadedStamp.caServerIdx, false),
                );
                // TODO[TK]: get rid of this condition when we will remember certificates from file
                if (!caServerSettings.loadCertsFromFile)
                    await dispatch(readKey(false));
            }
        }
    };
}

function rememberKeyData() {
    return async (dispatch, getState) => {
        const {
            signPopup: state,
            app: {
                currentUser: {
                    currentRole: {
                        company: { id },
                    },
                },
            },
        } = getState();
        const dumpedPk = await signer.dumpKeyFile({
            file: state.pkFile,
            password: state.pkPassword,
            caServerIdx: state.pkCAServerIdx,
            jksKey: state.pkJksKey,
        });
        const dumpedStamp = state.stampFile
            ? await signer.dumpKeyFile({
                  file: state.stampFile,
                  password: state.stampPassword,
                  caServerIdx: state.stampCAServerIdx,
                  jksKey: state.stampJksKey,
              })
            : null;
        const currentCompanyKey = {
            [id]: {
                pkPassword: dumpedPk.password,
                pkFile: dumpedPk.file,
                pkFileName: dumpedPk.fileName,
                pkCAServerAddress: dumpedPk.caServerAddress,
                pkJksKeyName: dumpedPk.jksKeyName,

                stampPassword: dumpedStamp && dumpedStamp.password,
                stampFile: dumpedStamp && dumpedStamp.file,
                stampFileName: dumpedStamp && dumpedStamp.fileName,
                stampCAServerAddress:
                    dumpedStamp && dumpedStamp.caServerAddress,
                stampJksKeyName: dumpedStamp && dumpedStamp.jksKeyName,
            },
        };

        const storedKeys = decryptStoredKeys(
            getLocalStorageItem(WS_KEY_REMEMBER_KEYS),
        );
        setLocalStorageItem(
            WS_KEY_REMEMBER_KEYS,
            encryptStoredKeys({ ...storedKeys, ...currentCompanyKey }),
        );
    };
}

function deleteKeyDataFromStorage() {
    return (dispatch, getState) => {
        const {
            app: {
                currentUser: {
                    currentRole: {
                        company: { id },
                    },
                },
            },
        } = getState();
        const storedKeys = decryptStoredKeys(
            getLocalStorageItem(WS_KEY_REMEMBER_KEYS),
        );
        if (storedKeys && id in storedKeys) {
            delete storedKeys[id];
            setLocalStorageItem(
                WS_KEY_REMEMBER_KEYS,
                encryptStoredKeys(storedKeys),
            );
        }
        dispatch({ type: actions.SIGN_POPUP__CLEAR_PK_DATA });
        dispatch({ type: actions.SIGN_POPUP__CLEAR_STAMP_DATA });
    };
}

function submitRecipient(
    emailRecipient,
    edrpouRecipient,
    isRecipientEmailHidden,
    formTracker,
) {
    return async (dispatch, getState) => {
        const stateStore = getState();
        const { signPopup: state } = stateStore;
        const selectedDocuments = getSelectedDocuments(stateStore);
        const doc = selectedDocuments[0] || state.docs[0];
        const docId = doc.id;
        const emails =
            !isRecipientEmailHidden && emailRecipient
                ? emailRecipient.split(',')
                : null;

        try {
            await changeDocumentRecipient(
                docId,
                emails,
                edrpouRecipient,
                isRecipientEmailHidden,
            );
            if (isFormTrackerInstance(formTracker)) formTracker.submitSuccess();
            eventTracking.sendEvent('form_doc_sign_step_1', 'finish');
            dispatch({
                type: actions.SIGN_POPUP__SUBMIT_RECIPIENT,
                emailRecipient,
                edrpouRecipient,
                isRecipientEmailHidden,
            });
            dispatch(documentListActionCreators.onLoadDocuments());
            dispatch(showNextStatus());
        } catch (err) {
            if (isDocumentRequiredFieldsError(err)) {
                const withoutRequiredFieldsDocs = await getDocumentsWithoutRequiredFieldsByEdrpou(
                    [doc],
                    [edrpouRecipient],
                );

                dispatch({
                    type: actions.SIGN_POPUP__REQUIRED_FIELDS_ERROR,
                });

                eventTracking.sendEvent('form_doc_sign_step_1_1', 'show');

                dispatch(
                    requiredFieldsResolverActionCreators.onSetData({
                        withoutRequiredFieldsDocs,
                        requiredFieldsCallbackName:
                            RequiredFieldsCallbacks.SIGN_POPUP__SUBMIT_RECIPIENT,
                        requiredFieldsCallbackData: {
                            emailRecipient,
                            edrpouRecipient,
                            isRecipientEmailHidden,
                            formTracker,
                        },
                    }),
                );
            }
        }
    };
}

function onSubmitRecipients() {
    return async (dispatch) => {
        dispatch({ type: actions.SIGN_POPUP__SUBMIT_RECIPIENTS });
        eventTracking.sendEvent('form_doc_sign_step_1', 'finish');
    };
}

function initSignService(cb) {
    return async (dispatch, getState) => {
        const { signPopup: state } = getState();

        dispatch({ type: actions.SIGN_POPUP__INIT_SIGN_SERVICE });
        try {
            // Explicit allocate memory for IIT library when:
            // * some of selected docs has internal signature
            // * isSignAllDocuments === true
            //
            // Otherwise IIT library allocate default size of memory:
            // 5Mb for desktop and 2Mb for mobile
            let allocateMemory = false;
            if (state.isSignAllDocuments) {
                allocateMemory = true;
            } else {
                for (const { signatures } of state.docs) {
                    const hasInternal = signatures
                        ? signatures.some((s) => s.isInternal)
                        : false;
                    allocateMemory = hasInternal || allocateMemory;
                }
            }
            await signer.init(allocateMemory);
            dispatch({
                type: actions.SIGN_POPUP__SET_CA_SERVERS,
                caServers: await signer.getCAServers(),
            });

            // Library is initialized
            dispatch({ type: actions.SIGN_POPUP__SIGN_SERVICE_INITIALIZED });

            // Clear keys context to make sure that there are no keys in the context
            if (config.VSIGNER_USE_CONTEXT) await signer.clearKeysCtx();

            // FIXME: Dumb implementation to init signService outside the SignPopup
            if (typeof cb === 'function') cb();
        } catch (err) {
            dispatch({
                type: actions.SIGN_POPUP__SHOW_EUSIGN_ERROR,
                errorMessage: t`Виникла помилка при завантаженні бібліотеки. Спробуйте оновити сторінку за деякий час.`,
            });
            logErrorByLevel(
                'Error on init EUSign library',
                { err: formatError(err) },
                err.level,
            );
        }
    };
}

function submitPK(pkFile) {
    return async (dispatch, getState) => {
        const {
            signPopup: state,
            app: { applicationMode },
        } = getState();
        const wrongFormatError = signer.checkKeyFormat(pkFile);
        const isSignSessionMode =
            applicationMode === ApplicationMode.SIGN_SESSION;
        const isSharedDocumentViewMode =
            applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW;

        const getTrackingName = () => {
            if (isSignSessionMode) {
                return 'sign_session';
            }
            if (isSharedDocumentViewMode) {
                return 'shared_document_view';
            }
            return 'doc';
        };

        const trackingNamePrefix = getTrackingName();
        let caServerIdx;

        // Clear previous key
        if (state.pkFile) {
            if (state.pkContextId) {
                await signer.clearKeyCtx(state.pkContextId);
            }
            dispatch({ type: actions.SIGN_POPUP__CLEAR_PK_DATA });
        }

        if (wrongFormatError) {
            const propName =
                state.popupStatus === 'UPLOAD_PRIVATE_KEY'
                    ? 'errorMessage'
                    : 'pkErrorMessage';
            if (state.popupStatus !== 'UPLOAD_PRIVATE_KEY') {
                dispatch({
                    type: actions.SIGN_POPUP__SUBMIT_PK,
                    pkFile,
                    password: '',
                });
            }
            dispatch({ type: actions.SIGN_POPUP__DISABLE_PK_EDIT });
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                [propName]: wrongFormatError,
            });
        } else {
            dispatch({
                type: actions.SIGN_POPUP__SUBMIT_PK,
                pkFile,
                password: '',
            });

            // Set CA server by key format
            const caServerIdxByFileName = getCAServerIdxByFileName(
                state.caServers,
                pkFile.name,
            );
            caServerIdx =
                typeof caServerIdxByFileName === 'number'
                    ? caServerIdxByFileName
                    : undefined;
            await dispatch(changeCAServer(caServerIdx, true));
            dispatch({
                type: actions.SIGN_POPUP__SET_CA_SERVER_EDITABLE,
                isPK: true,
                editDisabled: typeof caServerIdxByFileName === 'number',
            });

            // Handle JKS container
            const fileFormat = signer.getFileFormat(pkFile);
            if (fileFormat === 'jks') {
                const jksKeys = await signer.readJksFile(pkFile);
                dispatch({
                    type: actions.SIGN_POPUP__SET_PK_JKS_KEYS,
                    jksKeys,
                });
                dispatch({
                    type: actions.SIGN_POPUP__SET_PK_JKS_KEY,
                    jksKey: jksKeys[0],
                });
            }
        }

        const validateError = validateKeyFile(pkFile, caServerIdx, state);
        if (validateError) {
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                pkErrorMessage: validateError,
            });
        }

        eventTracking.sendEvent('key_upload', signer.getFileFormat(pkFile));

        // send event only if there was no key before (event from upload form)
        if (!state.pkFile) {
            eventTracking.sendEvent(
                `form_${trackingNamePrefix}_sign_step_2`,
                'finish',
            );
        }
    };
}

function onClearPK() {
    return async (dispatch, getState) => {
        const {
            signPopup: { pkContextId },
        } = getState();

        await signer.clearKeyCtx(pkContextId);

        dispatch({ type: actions.SIGN_POPUP__CLEAR_PK_DATA });
        dispatch({ type: actions.SIGN_POPUP__REMOVE_PK });
    };
}

function onClearStamp() {
    return async (dispatch, getState) => {
        const {
            signPopup: { stampContextId },
        } = getState();

        await signer.clearKeyCtx(stampContextId);

        dispatch({ type: actions.SIGN_POPUP__CLEAR_STAMP_DATA });
        dispatch({ type: actions.SIGN_POPUP__REMOVE_STAMP });
    };
}

function setPKPassword(password) {
    return { type: actions.SIGN_POPUP__SET_PK_PASSWORD, password };
}

function submitPKCertificates(pkCertificateFiles) {
    return async (dispatch) => {
        const wrongFormatError = signer.checkCertificatesFormat(
            pkCertificateFiles,
        );
        dispatch({
            type: actions.SIGN_POPUP__SUBMIT_PK_CERTIFICATES,
            pkCertificateFiles,
        });
        if (wrongFormatError) {
            dispatch({ type: actions.SIGN_POPUP__DISABLE_PK_EDIT });
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                pkErrorMessage: wrongFormatError,
            });
        }
    };
}

function submitStamp(stampFile) {
    return async (dispatch, getState) => {
        const { signPopup: state } = getState();
        const wrongFormatError = signer.checkKeyFormat(stampFile);
        let caServerIdx;

        // Clear previous key
        if (state.stampFile) {
            if (state.stampContextId) {
                await signer.clearKeyCtx(state.stampContextId);
            }
            dispatch({ type: actions.SIGN_POPUP__CLEAR_STAMP_DATA });
        }

        dispatch({
            type: actions.SIGN_POPUP__SUBMIT_STAMP,
            stampFile,
            password: '',
        });

        if (wrongFormatError) {
            const propName =
                state.popupStatus === 'UPLOAD_PRIVATE_KEY'
                    ? 'errorMessage'
                    : 'stampErrorMessage';
            dispatch({ type: actions.SIGN_POPUP__DISABLE_STAMP_EDIT });
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                [propName]: wrongFormatError,
            });
        } else {
            // Set CA server by key format
            const caServerIdxByFileName = getCAServerIdxByFileName(
                state.caServers,
                stampFile.name,
            );
            caServerIdx =
                typeof caServerIdxByFileName === 'number'
                    ? caServerIdxByFileName
                    : undefined;
            await dispatch(changeCAServer(caServerIdx, false));
            dispatch({
                type: actions.SIGN_POPUP__SET_CA_SERVER_EDITABLE,
                isPK: false,
                editDisabled: typeof caServerIdxByFileName === 'number',
            });

            // Handle JKS container
            const fileFormat = signer.getFileFormat(stampFile);
            if (fileFormat === 'jks') {
                const jksKeys = await signer.readJksFile(stampFile);
                dispatch({
                    type: actions.SIGN_POPUP__SET_STAMP_JKS_KEYS,
                    jksKeys,
                });
                dispatch({
                    type: actions.SIGN_POPUP__SET_STAMP_JKS_KEY,
                    jksKey: jksKeys[0],
                });
            }
        }

        const validateError = validateKeyFile(stampFile, caServerIdx, state);
        if (validateError) {
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                stampErrorMessage: validateError,
            });
        }

        eventTracking.sendEvent('key_upload', signer.getFileFormat(stampFile));
    };
}

function setStampPassword(password) {
    return { type: actions.SIGN_POPUP__SET_STAMP_PASSWORD, password };
}

function submitStampCertificates(stampCertificateFiles) {
    return async (dispatch) => {
        const wrongFormatError = signer.checkCertificatesFormat(
            stampCertificateFiles,
        );
        dispatch({
            type: actions.SIGN_POPUP__SUBMIT_STAMP_CERTIFICATES,
            stampCertificateFiles,
        });
        if (wrongFormatError) {
            dispatch({ type: actions.SIGN_POPUP__DISABLE_STAMP_EDIT });
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                stampErrorMessage: wrongFormatError,
            });
        }
    };
}

function createSigner(usbSignerCtx = null) {
    return async (dispatch, getState) => {
        const {
            app: { currentSignSession, currentUser },
            signPopup: state,
        } = getState();

        if (!shouldRegisterSignSessionSigner(currentUser, currentSignSession)) {
            return;
        }

        const { token, hash } = await getRegistrationToken();

        const [signs] = usbSignerCtx
            ? await signer.generateExternalSignatureByUSB(
                  usbSignerCtx,
                  stringToUint8Array(token),
              )
            : await signer.generateExternalSignatureByKey(hash);

        const keys = getKeys(state);
        const { pkSign: signature } = usbSignerCtx
            ? { ...signs[0], ...signs[1] }
            : formatSignResultCtx(keys, signs);

        if (!signature) {
            throw new Error(
                'Не вдалося згенерувати підпис для реєстрації підписанта',
            );
        }

        await registerSigner({
            token: token,
            signature: signature,
            signatureFormat: SignatureFormat.EXTERNAL_SEPARATED,
        });
    };
}

function sign(doc, signatureFormat, usbSignerCtx = null) {
    return async (dispatch, getState) => {
        dispatch({ type: actions.SIGN_POPUP__START_SIGN });

        const { signPopup: state, app: appState } = getState();
        const status = { success: false, error: '' };
        const keys = getKeys(state);

        try {
            await dispatch(createSigner(usbSignerCtx));
        } catch (err) {
            const { currentSignSession, currentUser } = appState;
            logErrorByLevel('Error on registering signer', {
                errorMessage: err.message,
                errorReason: err.reason,
                error: JSON.stringify(err, ['message', 'reason', 'code']),
                inSignSession: Boolean(currentSignSession),
                currentRoleStatus:
                    currentUser && currentUser.currentRole.status,
            });
            // Errors handled by backend always have code
            if (err.code) {
                return {
                    success: false,
                    error: err.reason ?? 'Не вдалося зареєструвати підписанта',
                    code: err.code,
                };
            }
            // Generic error message for unexpected errors
            return {
                success: false,
                error: 'Не вдалося зареєструвати підписанта',
            };
        }

        // TODO[AK]: Remove VSIGNER_USE_CONTEXT flag
        try {
            const [signs, p7s, archive] = await signer.signDocument(
                doc,
                signatureFormat,
                usbSignerCtx,
                state.isSignActAnnulment,
            );
            status.success = true;
            if (usbSignerCtx) {
                dispatch({
                    type: actions.SIGN_POPUP__FINISH_SIGN,
                    p7s,
                    ...signs[0],
                    ...signs[1],
                });
            } else if (config.VSIGNER_USE_CONTEXT) {
                dispatch({
                    type: actions.SIGN_POPUP__FINISH_SIGN,
                    p7s,
                    archive,
                    ...formatSignResultCtx(keys, signs),
                });
            } else {
                dispatch({
                    type: actions.SIGN_POPUP__FINISH_SIGN,
                    p7s,
                    archive,
                    ...formatSignResult(signs),
                });
            }
        } catch (err) {
            const saServer = usbSignerCtx
                ? state.caServers[state.pkUsbCAServerIdx]
                : state.caServers[state.pkCAServerIdx];
            const caName = saServer ? saServer.name : 'не обрано';
            status.error = err.message
                ? err.message.replace('{caName}', caName)
                : 'Проблеми з АЦСК, не вдалося підписати документ.';
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                errorMessage: status.error,
            });
            logErrorByLevel(
                'Error on signing document',
                errorContext(err, 'signDocument', doc.id, state, appState),
                err.level,
            );
        }

        return status;
    };
}

function submit(doc, signatureFormat, widgetSigns) {
    return async (dispatch, getState) => {
        const { signPopup: state, app: appState } = getState();
        const currentRoleId = appState.currentUser.currentRole.id;
        const signRequestParameters =
            appState.currentSignSession &&
            appState.currentSignSession.signParameters;
        const status = { success: false, error: '' };
        const keys = widgetSigns
            ? getKeysFromWidgetIit(widgetSigns[0])
            : getKeys(state);
        const pkSignAcsk = getACSK(state.pkCAServerIdx, state.caServers);
        const stampSignAcsk = getACSK(state.stampCAServerIdx, state.caServers);
        const keyCompanyName =
            state.pkInfo?.companyName ||
            state.stampInfo?.companyName ||
            widgetSigns?.[0]?.edsInfo.companyName;
        const data = {
            pkSign: state.pkSign,
            pkSignAcsk,
            stampSign: state.stampSign,
            stampSignAcsk,
            p7s: state.p7s,
            archive: state.archive,
            keyCompanyName,
        };

        try {
            await signDocumentAPI(
                doc.id,
                data,
                keys,
                signatureFormat,
                signRequestParameters,
                state.isSignActAnnulment,
                doc.revoke?.id,
            );
            if (shouldSendAfterSign(doc, currentRoleId)) {
                await sendDocumentAPI(doc.id);
            }
            status.success = true;
        } catch (err) {
            status.success = false;
            status.error = err.message || t`Не вдалося відправити документ.`;
            status.code = err.code;

            if (err.code !== 'access_denied') {
                dispatch({
                    type: actions.SIGN_POPUP__SHOW_ERROR,
                    errorMessage: status.error,
                    errorCode: err.code || '',
                });
            }

            logErrorByLevel(
                'Error on submitting document',
                errorContext(err, 'submitSign', doc.id, state, appState),
                err.level,
            );
        }

        return status;
    };
}

function signAndSubmitDocument(doc, usbSignerCtx = null, widgetSigns = null) {
    return async (dispatch, getState) => {
        const { signPopup } = getState();
        const status = { success: false, error: '', code: '' };
        const { expectedSignatureFormat } = doc;
        const signatureFormat = signPopup.isSignActAnnulment
            ? doc.revoke?.signatureFormat
            : getFormatDocumentForSendToBackend(expectedSignatureFormat);

        const isWidgetSign = !!widgetSigns?.length;

        try {
            let signStatus;
            if (!isWidgetSign) {
                signStatus = await dispatch(
                    sign(doc, signatureFormat, usbSignerCtx),
                );
            } else {
                dispatch({ type: actions.SIGN_POPUP__START_SIGN });
                if (config.VSIGNER_USE_CONTEXT) {
                    const keys = getKeysFromWidgetIit(widgetSigns[0]);

                    dispatch({
                        type: actions.SIGN_POPUP__FINISH_SIGN,
                        p7s: widgetSigns[0].p7s,
                        ...formatSignResultCtx(keys, widgetSigns),
                    });
                } else {
                    dispatch({
                        type: actions.SIGN_POPUP__FINISH_SIGN,
                        p7s: widgetSigns[0].p7s,
                        ...formatSignResult(widgetSigns),
                    });
                }
            }
            let submitStatus;

            if (signStatus?.success || isWidgetSign) {
                submitStatus = await dispatch(
                    submit(doc, signatureFormat, widgetSigns),
                );
                if (signPopup.isRememberedKeys) {
                    dispatch(rememberKeyData());
                } else if (submitStatus?.success) {
                    dispatch(deleteKeyDataFromStorage());
                }
            }

            if (
                (signStatus?.success || isWidgetSign) &&
                submitStatus?.success
            ) {
                const category =
                    doc.parent && doc.parent.id ? 'sub_document' : 'document';
                eventTracking.sendEvent(category, 'send');
                status.success = true;
                deleteSignedDocumentFromStorage(
                    doc.id,
                    getUnsuccessfullySignedList(),
                );
            } else {
                status.success = false;
                const submitError = submitStatus?.error;
                const signError = signStatus?.error;
                if (isWidgetSign) {
                    status.error = t`Помилка підписання - ${submitError}`;
                } else {
                    status.error = !signStatus?.success
                        ? t`Помилка підпису - ${signError}`
                        : t`Помилка відправки - ${submitError}`;
                }
                status.code = signStatus?.code || submitStatus?.code;
            }
            if (doc.isMultilateral) {
                const storedRecipients = getLocalStorageItem(
                    WS_KEY_DOCUMENT_RECIPIENTS,
                );
                if (storedRecipients && storedRecipients[doc.id]) {
                    if (storedRecipients[doc.id])
                        delete storedRecipients[doc.id];
                    setLocalStorageItem(
                        WS_KEY_DOCUMENT_RECIPIENTS,
                        storedRecipients,
                    );
                }
            }
        } catch (err) {
            status.success = false;
            status.error = err.message;
            status.code = err.code;
        }

        return status;
    };
}

function multiSign(
    isSignAllDocuments,
    usbSignerCtx = null,
    widgetSigns = null,
    kepSigns = null,
) {
    return async (dispatch, getState) => {
        const status = {
            success: true,
            successCount: 0,
            unsuccessCount: 0,
            incoming: 0,
            outgoing: 0,
        };
        const {
            signPopup: state,
            app: {
                currentUser,
                currentSignSession,
                flags: { [FeatureFlags.ES_SEARCH]: isEsSearchEnabled },
            },
        } = getState();
        const signRequestParameters =
            currentSignSession && currentSignSession.signParameters;
        const keys = widgetSigns
            ? getKeysFromWidgetIit(widgetSigns[0])
            : getKeys(state);

        dispatch({ type: actions.SIGN_POPUP__START_MULTI_SIGN });
        dispatch(documentListActionCreators.onDeselectAllDocuments());

        // Notification about sign process
        const [updateNotification, removeNotification] = dispatch(
            notificationActionCreators.addNotification({
                title: t`Підписання документів`,
                type: 'progress',
                progressTotalCount: state.totalDocsCount,
            }),
        );

        // Sign documents
        let signResults;
        try {
            let isConnectionProblemOccurred = false;
            const onOffline = () => {
                isConnectionProblemOccurred = true;
            };

            let signedDocCount = 0;
            const increaseSignedCount = () => {
                dispatch({
                    type: actions.SIGN_POPUP__UPDATE_MULTI_SIGN,
                    signedDocCount,
                }); // for mobile version
                updateNotification({ progressCurrentCount: signedDocCount });
                signedDocCount++;
            };

            const setTotalDocsCount = (docs) => {
                const totalDocsCount = docs.length;
                dispatch({
                    type: actions.SIGN_POPUP__SET_TOTAL_DOCS_COUNT,
                    totalDocsCount,
                });
                updateNotification({ progressTotalCount: totalDocsCount });
            };

            addEventListener('offline', onOffline);

            if (isSignAllDocuments) {
                const getDocumentsArgs = getDocumentsGraphArguments(
                    getState().router.location,
                );
                signResults = await signer.multiSign({
                    getDocumentsArgs,
                    keys,
                    currentUser,
                    usbSignerCtx,
                    afterSignCallback: increaseSignedCount,
                    beforeMultiSignCallback: setTotalDocsCount,
                    signRequestParameters,
                    isEsSearchEnabled,
                    widgetSigns,
                    kepSigns,
                });
            } else {
                signResults = await signer.multiSign({
                    docs: state.docs,
                    keys,
                    currentUser,
                    usbSignerCtx,
                    afterSignCallback: increaseSignedCount,
                    signRequestParameters,
                    isEsSearchEnabled,
                    widgetSigns,
                    kepSigns,
                });
            }

            removeEventListener('offline', onOffline);
            removeNotification();

            dispatch({
                type: actions.SIGN_POPUP__FINISH_MULTI_SIGN,
                multiSignResult: signResults,
                isConnectionProblemOccurred,
            });

            // Sign status
            const successfullySignedDocuments = signResults.filter(
                (item) => item.success === true,
            );
            const unsuccessfullySignedDocuments = signResults.filter(
                (item) => item.success === false,
            );
            const incomingSignedDocuments = successfullySignedDocuments.filter(
                ({ doc }) => doc.isInput,
            );
            const outgoingSignedDocuments = successfullySignedDocuments.filter(
                ({ doc }) => !doc.isInput,
            );
            status.successCount =
                signResults.length - unsuccessfullySignedDocuments.length;
            status.unsuccessCount = unsuccessfullySignedDocuments.length;
            status.incoming = incomingSignedDocuments.length;
            status.outgoing = outgoingSignedDocuments.length;

            // Save information about unsigned documents to web storage to inform user
            if (!isConnectionProblemOccurred) {
                deleteSignedDocumentsFromStorage(successfullySignedDocuments);
                setUnsignedDocumentsToStorage(unsuccessfullySignedDocuments);
            }

            // Remember key data
            if (state.isRememberedKeys) {
                dispatch(rememberKeyData());
            } else {
                removeLocalStorageItem(WS_KEY_REMEMBER_KEYS);
                dispatch({ type: actions.SIGN_POPUP__CLEAR_PK_DATA });
                dispatch({ type: actions.SIGN_POPUP__CLEAR_STAMP_DATA });
            }
        } catch (err) {
            status.success = false;
            status.error = err.message;
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                errorMessage: err.message,
            });
            removeNotification();
        }

        return status;
    };
}

/**
 * @typedef {object} CloudSignerSignatureItemOption
 * @property {string | null} pkSign
 * @property {string | null} stampSign
 * @property {string | null} pkSerialNumber
 * @property {string | null} stampSerialNumber
 * @property {object} doc
 */

/**
 * @typedef {object} CloudSignerCtxOption
 * @property {CloudSignerSignatureItemOption[]} [signatures]
 */

/**
 *
 * @param {object | null} formTracker
 * @param {object | null} usbSignerCtx
 * @param {CloudSignerCtxOption | null} cloudSignerCtx
 * @param {object | null} widgetSigns
 * @returns {(function(*, *): Promise<void>)|*}
 */
function signAndSubmit(
    formTracker,
    usbSignerCtx = null,
    cloudSignerCtx = {},
    widgetSigns = null,
) {
    return async (dispatch, getState) => {
        const {
            signPopup: state,
            app: { applicationMode, currentUser, currentSignSession },
        } = getState();
        const isSharedDocumentViewMode =
            applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW;
        const doc = state.docs[0];
        let status = {};

        // Prevent duplicate signing
        if (state.isSignAndSubmitProcess) {
            return;
        }

        dispatch({ type: actions.SIGN_POPUP__START_SIGN_AND_SUBMIT });
        if (isFormTrackerInstance(formTracker)) formTracker.submitTry();

        if (state.signDiia) {
            eventTracking.sendEvent('diia', 'sign');
            try {
                if (shouldSendAfterSign(doc, currentUser.currentRole.id)) {
                    await sendDocumentAPI(doc.id);
                }
                status = { success: true, error: '' };
            } catch (e) {
                eventTracking.sendEvent('diia', 'error_sign', e.message);
                status = { success: false, error: e.message };
            }
        } else if (state.signKep || state.signKepNew) {
            if (
                Array.isArray(cloudSignerCtx.signatures) &&
                cloudSignerCtx.signatures.length > 0
            ) {
                const getKeysFromKep = (pkSN = null, stampSN = null) => {
                    return {
                        pk: {
                            keyInfo: {
                                serialNumber: pkSN,
                            },
                        },
                        stamp: {
                            keyInfo: {
                                serialNumber: stampSN,
                            },
                        },
                    };
                };
                try {
                    const signRequestParameters =
                        currentSignSession && currentSignSession.signParameters;

                    if (cloudSignerCtx.signatures.length === 1) {
                        const [cloudSignature] = cloudSignerCtx.signatures;
                        const keys = getKeysFromKep(
                            cloudSignature.pkSerialNumber,
                            cloudSignature.stampSerialNumber,
                        );
                        const data = {
                            pkSign: cloudSignature.pkSign,
                            pkSignAcsk: ACSK.DEFAULT,
                            stampSign: cloudSignature.stampSign,
                            stampSignAcsk: ACSK.DEFAULT,
                            p7s: null, // do not pass p7s because KEP cannot sign internal signature format (useful info)
                        };

                        await signDocumentAPI(
                            cloudSignature.doc.id,
                            data,
                            keys,
                            'external_separated',
                            signRequestParameters,
                            state.isSignActAnnulment,
                            cloudSignature.doc.revoke?.id,
                        );

                        if (
                            shouldSendAfterSign(
                                cloudSignature.doc,
                                currentUser.currentRole.id,
                            )
                        ) {
                            await sendDocumentAPI(cloudSignature.doc.id);
                        }
                        status = { success: true, error: '' };
                        eventTracking.sendEvent('vchasno_kep', 'sign');
                    } else {
                        const kepSigns = await Promise.all(
                            cloudSignerCtx.signatures.map(async (item) => ({
                                doc: item.doc,
                                keys: getKeysFromKep(
                                    item.pkSerialNumber,
                                    item.stampSerialNumber,
                                ),
                                data: {
                                    pkSign: item.pkSign,
                                    pkSignAcsk: ACSK.DEFAULT,
                                    stampSign: item.stampSign,
                                    stampSignAcsk: ACSK.DEFAULT,
                                    p7s: null, // do not pass p7s because KEP cannot sign internal signature format (useful info)
                                },
                                signatureFormat: 'external_separated',
                                signRequestParameters,
                            })),
                        );
                        status = await dispatch(
                            multiSign(
                                state.isSignAllDocuments,
                                usbSignerCtx,
                                widgetSigns,
                                kepSigns,
                            ),
                        );
                        eventTracking.sendEvent('vchasno_kep', 'multi-sign');
                    }
                } catch (e) {
                    eventTracking.sendEvent(
                        'vchasno_kep',
                        'error',
                        e.message || e.details,
                    );
                    status = { success: false, error: e.message };
                }
            }
        } else if (widgetSigns) {
            // need different functions, because for one document we can add stamp
            // when the document was signed by all the necessary users
            if (widgetSigns.length > 1) {
                status = await dispatch(
                    multiSign(
                        state.isSignAllDocuments,
                        usbSignerCtx,
                        widgetSigns,
                    ),
                );
            } else {
                status = await dispatch(
                    signAndSubmitDocument(doc, usbSignerCtx, widgetSigns),
                );
            }
        } else if (state.isMultiSign) {
            status = await dispatch(
                multiSign(state.isSignAllDocuments, usbSignerCtx),
            );
        } else {
            status = await dispatch(signAndSubmitDocument(doc, usbSignerCtx));
        }

        if (!hasUnlimitedRate(currentUser)) {
            dispatch(appActionCreators.updateBillingAccounts());
        }

        dispatch({
            type: actions.SIGN_POPUP__FINISH_SIGN_AND_SUBMIT,
            signStatus: status,
        });
        dispatch(showNextStatus());

        if (state.origin === 'documentList') {
            await dispatch(
                documentListActionCreators.onLoadDocuments(null, {
                    isAfterSign: true,
                }),
            );
        } else if (state.origin === 'document') {
            dispatch(documentActionCreators.onLoadDocument(doc.id));
        }

        if (status.success) {
            if (isFormTrackerInstance(formTracker)) formTracker.submitSuccess();
            const hasSignSession =
                applicationMode === ApplicationMode.SIGN_SESSION;

            const getTrackingName = () => {
                if (hasSignSession) {
                    return 'sign_session';
                }
                if (isSharedDocumentViewMode) {
                    return 'shared_document_view';
                }
                return 'doc';
            };

            const trackingNamePrefix = getTrackingName();

            eventTracking.sendEvent(
                `form_${trackingNamePrefix}_sign_step_3`,
                'finish',
            );

            if (isSharedDocumentViewMode) {
                eventTracking.sendToGTM({
                    event: 'reg_first_sign',
                    category: '',
                    action: currentUser.email,
                });
            }

            // Sign when uploadDocuments case
            dispatch(uploaderActionCreators.onPopupClose());

            // Sign session case
            dispatch(appActionCreators.onSignSessionFinish());

            dispatch(headerActionCreators.hideInviteTooltip());
        } else {
            if (isSharedDocumentViewMode && status.code === 'overdraft') {
                dispatch({
                    type:
                        actions.SIGN_POPUP__SHOW_SHARED_DOCUMENT_VIEW_OVERDRAFT_ERROR,
                });
            } else {
                dispatch({
                    type: actions.SIGN_POPUP__SHOW_GENERAL_SIGN_ERROR,
                    errorMessage: status.error,
                });
            }
        }

        let trackingData = {
            location: getLocationId(getState().router.location),
            success: status.successCount,
            unsuccess: status.unsuccessCount,
        };
        if (status.incoming > 0)
            trackingData = { ...trackingData, incoming: status.incoming };
        if (status.outgoing > 0)
            trackingData = { ...trackingData, outgoing: status.outgoing };
        if (state.docs.length > 1) {
            eventTracking.sendEvent('document', 'mass_sign', trackingData);
        }

        state.docs.forEach((document) => {
            let trackingLabel = 'outcome';

            if (document.isInternal) {
                trackingLabel = 'internal';
            } else if (document.isInput) {
                trackingLabel = 'income';
            } else if (document.kind === 'recipient_sign_first') {
                trackingLabel = getIsVersionedDocument(document)
                    ? 'recipient_sign_version_first'
                    : 'recipient-sign-first';
            }

            eventTracking.sendEvent(
                'document',
                'all-signatures',
                trackingLabel,
            );
        });
    };
}

function retrySignAndSubmit() {
    return (dispatch, getState) => {
        const { signPopup: state } = getState();

        dispatch(documentListActionCreators.onLoadDocuments());

        if (state.isMultiSign && !state.isSignAllDocuments) {
            // Remove signed documents from state.docs
            const unsuccessDocIds = state.multiSignResult
                .filter((result) => result.success === false)
                .map((result) => result.doc.id);
            const docs = state.docs.filter(
                (doc) => unsuccessDocIds.indexOf(doc.id) >= 0,
            );
            dispatch({ type: actions.SIGN_POPUP__SET_DOCUMENTS, docs });
        }

        dispatch({ type: actions.SIGN_POPUP__RETRY_SIGN_AND_SUBMIT });
        dispatch(signAndSubmit());
    };
}

function toggleRememberKeys() {
    return (dispatch, getState) => {
        const {
            signPopup: { isRememberedKeys },
        } = getState();
        dispatch({
            type: actions.SIGN_POPUP__TOGGLE_REMEMBER_KEYS,
            isRememberedKeys: !isRememberedKeys,
        });
    };
}

function onDelnotWarningAgree() {
    return async (dispatch) => {
        dispatch(showNextStatus());
    };
}

// USB form

function formatUSBErrorMessage(err, caName) {
    let errorMessage = err.message
        ? err.message.replace('{caName}', caName)
        : t`На жаль, не вдалося зчитати ключ через проблеми з АЦСК, спробуйте знову через кілька хвилин.`;

    if (
        errorMessage ===
        'Виникла помилка при доступі до носія ключової інформації'
    ) {
        errorMessage = `${errorMessage}. Можливо, ви ввели невірний пароль.`;
    }

    return errorMessage;
}

function loadUSBSigner() {
    return async (dispatch) => {
        dispatch({ type: actions.SIGN_POPUP__LOAD_USB_SIGNER });

        let usbSigner;
        try {
            usbSigner = await initUSBSigner();
        } catch (err) {
            dispatch({
                type: actions.SIGN_POPUP__LOAD_USB_ERROR,
                usbLoadError: err.toString(),
            });
            return;
        }

        const devices = await usbSigner.findDevices();
        dispatch({
            type: actions.SIGN_POPUP__USB_SIGNER_LOADED,
            usbSigner,
            devices,
        });
    };
}

/**
 *
 * @param {string} companyWidgetUrl
 * @returns {(function(*, *): Promise<void>)|*}
 */
function loadIitSignWidget(companyWidgetUrl) {
    return async (dispatch) => {
        dispatch({
            type: actions.SIGN_POPUP__LOAD_IIT_WIDGET,
            companyWidgetUrl,
        });
    };
}

function onSetUSBProxySettings(proxySettings) {
    return async (dispatch, getState) => {
        const {
            signPopup: { usbSigner },
        } = getState();
        await usbSigner.setProxy(proxySettings);
    };
}

function onFindUSBDevices() {
    return async (dispatch, getState) => {
        const {
            signPopup: { usbSigner },
        } = getState();

        if (!usbSigner) return;

        dispatch({
            type: actions.SIGN_POPUP__START_FIND_USB_DEVICE,
            isFindingUSBDevices: true,
        });

        const devices = await usbSigner.findDevices();

        dispatch({
            type: actions.SIGN_POPUP__FINISH_FIND_USB_DEVICE,
            isFindingUSBDevices: false,
            devices,
        });
    };
}

function onPkUSBDeviceChange(id) {
    return (dispatch, getState) => {
        const {
            signPopup: { devices },
        } = getState();
        const pkDevice = devices.find((item) => item.id === id);
        dispatch({ type: actions.SIGN_POPUP__CHANGE_PK_USB_DEVICE, pkDevice });
    };
}

function onChangePkUSBCAServer(pkUsbCAServerIdx) {
    return async (dispatch) => {
        dispatch({
            type: actions.SIGN_POPUP__CHANGE_PK_USB_CA_SERVER,
            pkUsbCAServerIdx,
        });
    };
}

function onSetPkUSBPassword(pkUSBPassword) {
    return { type: actions.SIGN_POPUP__SET_PK_USB_PASSWORD, pkUSBPassword };
}

function onReadPkUSBKey() {
    return async (dispatch, getState) => {
        const { signPopup: state, app: appState } = getState();
        const {
            caServers,
            docs,
            pkDevice,
            pkUSBPassword,
            usbSigner,
            pkUsbCAServerIdx,
            isStampUSBChecked,
        } = state;
        const doc = docs[0];
        const {
            currentUser: {
                currentCompany: { edrpou: userEdrpou },
            },
        } = appState;
        const caName = caServers[pkUsbCAServerIdx].name;

        // Ensure available device
        if (!pkDevice) {
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                pkErrorMessage: t`
                    Ми не змогли знайти ключ.
                    Перевірте чи вставлений ключ в комп'ютер.
                `,
            });
            return;
        }

        dispatch({
            type: actions.SIGN_POPUP__READ_PK_USB_KEY,
            isPkUSBKeyReading: true,
            isPkUSBChecked: false,
        });

        try {
            const { typeIdx, devIdx } = pkDevice;

            // Ensure that device is hardware token
            const isHardware = await usbSigner.isHardwareKey(
                typeIdx,
                devIdx,
                pkUSBPassword,
            );
            if (!isHardware) {
                dispatch({
                    type: actions.SIGN_POPUP__READ_PK_USB_KEY,
                    isPkUSBKeyReading: false,
                });
                dispatch({
                    type: actions.SIGN_POPUP__SHOW_ERROR,
                    pkErrorMessage: 'Ключ не є апаратним',
                });
                return;
            }

            await changeCAServers(caServers, pkUsbCAServerIdx);
            await usbSigner.readPrivateKeyAndSaveCertificates(
                typeIdx,
                devIdx,
                pkUSBPassword,
            );

            const pkInfo = await usbSigner.getKeyInfo();
            const pkCertificateInfo = await usbSigner.getActualCertificateInfo();

            validateUSBKeyInfo(
                true,
                pkInfo,
                pkCertificateInfo,
                userEdrpou,
                doc,
                state,
            );

            // зберігаємо інфромацію про термін дії сертифіката usb/апаратний ключа
            updateCertificateInfo(pkCertificateInfo, pkInfo, 'usb_key');

            if (pkCertificateInfo.isStamp && !isStampUSBChecked) {
                // user uploaded stamp instead of key
                dispatch({ type: actions.SIGN_POPUP__ADD_USB_STAMP });
                dispatch({
                    type: actions.SIGN_POPUP__CHANGE_STAMP_USB_DEVICE,
                    stampDevice: pkDevice,
                });
                dispatch({
                    type: actions.SIGN_POPUP__CHANGE_STAMP_USB_CA_SERVER,
                    stampUsbCAServerIdx: pkUsbCAServerIdx,
                });
                dispatch({
                    type: actions.SIGN_POPUP__SET_STAMP_USB_PASSWORD,
                    stampUSBPassword: pkUSBPassword,
                });
                dispatch({
                    type: actions.SIGN_POPUP__READ_STAMP_USB_KEY,
                    isStampUSBChecked: true,
                    stampInfo: pkInfo,
                    stampCertificateInfo: pkCertificateInfo,
                    stampTypeIdx: typeIdx,
                    stampDevIdx: devIdx,
                });
                dispatch({ type: actions.SIGN_POPUP__REMOVE_USB_PK });
            } else {
                dispatch({
                    type: actions.SIGN_POPUP__READ_PK_USB_KEY,
                    isPkUSBKeyReading: false,
                    isPkUSBChecked: true,
                    pkInfo,
                    pkCertificateInfo,
                    pkTypeIdx: typeIdx,
                    pkDevIdx: devIdx,
                });
            }
        } catch (err) {
            dispatch({
                type: actions.SIGN_POPUP__READ_PK_USB_KEY,
                isPkUSBKeyReading: false,
            });
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                pkErrorMessage: formatUSBErrorMessage(err, caName),
            });
        } finally {
            // To read another key, need to reset current key
            await usbSigner.resetPrivateKey();
        }
    };
}

function onResetPkUSBKey() {
    return { type: actions.SIGN_POPUP__RESET_PK_USB_KEY };
}

function onAddUSBPK() {
    return { type: actions.SIGN_POPUP__ADD_USB_PK };
}

function onStampUSBDeviceChange(id) {
    return (dispatch, getState) => {
        const {
            signPopup: { devices },
        } = getState();
        const stampDevice = devices.find((item) => item.id === id);
        dispatch({
            type: actions.SIGN_POPUP__CHANGE_STAMP_USB_DEVICE,
            stampDevice,
        });
    };
}

function onChangeStampUSBCAServer(stampUsbCAServerIdx) {
    return async (dispatch) => {
        dispatch({
            type: actions.SIGN_POPUP__CHANGE_STAMP_USB_CA_SERVER,
            stampUsbCAServerIdx,
        });
    };
}

function onSetStampUSBPassword(stampUSBPassword) {
    return {
        type: actions.SIGN_POPUP__SET_STAMP_USB_PASSWORD,
        stampUSBPassword,
    };
}

function onReadStampUSBKey() {
    return async (dispatch, getState) => {
        const { signPopup: state, app: appState } = getState();
        const {
            caServers,
            docs,
            stampDevice,
            stampUSBPassword,
            stampUsbCAServerIdx,
            usbSigner,
        } = state;
        const doc = docs[0];
        const {
            currentUser: {
                currentCompany: { edrpou: userEdrpou },
            },
        } = appState;
        const caName = caServers[stampUsbCAServerIdx].name;

        // Ensure available device
        if (!stampDevice) {
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                stampErrorMessage: t`
                    Ми не змогли знайти ключ.
                    Перевірте чи вставлений ключ в комп'ютер.
                `,
            });
            return;
        }

        dispatch({
            type: actions.SIGN_POPUP__READ_STAMP_USB_KEY,
            isStampUSBKeyReading: true,
            isStampUSBChecked: false,
        });

        try {
            const { typeIdx, devIdx } = stampDevice;

            // Ensure that device is hardware token
            const isHardware = await usbSigner.isHardwareKey(
                typeIdx,
                devIdx,
                stampUSBPassword,
            );
            if (!isHardware) {
                dispatch({
                    type: actions.SIGN_POPUP__READ_STAMP_USB_KEY,
                    isStampUSBKeyReading: false,
                });
                dispatch({
                    type: actions.SIGN_POPUP__SHOW_ERROR,
                    stampErrorMessage: 'Ключ не є апаратним',
                });
                return;
            }

            await changeCAServers(caServers, stampUsbCAServerIdx);
            await usbSigner.readPrivateKeyAndSaveCertificates(
                typeIdx,
                devIdx,
                stampUSBPassword,
            );

            const stampInfo = await usbSigner.getKeyInfo();
            const stampCertificateInfo = await usbSigner.getActualCertificateInfo();

            validateUSBKeyInfo(
                false,
                stampInfo,
                stampCertificateInfo,
                userEdrpou,
                doc,
                state,
            );

            dispatch({
                type: actions.SIGN_POPUP__READ_STAMP_USB_KEY,
                isStampUSBKeyReading: false,
                isStampUSBChecked: true,
                stampInfo,
                stampTypeIdx: typeIdx,
                stampDevIdx: devIdx,
            });
        } catch (err) {
            dispatch({
                type: actions.SIGN_POPUP__READ_STAMP_USB_KEY,
                isStampUSBKeyReading: false,
            });
            dispatch({
                type: actions.SIGN_POPUP__SHOW_ERROR,
                stampErrorMessage: formatUSBErrorMessage(err, caName),
            });
        } finally {
            // To read another key, need to reset current key
            await usbSigner.resetPrivateKey();
        }
    };
}

function onResetStampUSBKey() {
    return { type: actions.SIGN_POPUP__RESET_STAMP_USB_KEY };
}

function onAddUSBStamp() {
    return { type: actions.SIGN_POPUP__ADD_USB_STAMP };
}

function usbSignAndSubmit() {
    return async (dispatch, getState) => {
        const {
            signPopup: { usbSigner, ...state },
        } = getState();
        const usbSignerCtx = {
            usbSigner,
            pk: state.useUSBPK
                ? {
                      password: state.pkUSBPassword,
                      typeIdx: state.pkTypeIdx,
                      devIdx: state.pkDevIdx,
                      caServerIdx: state.pkUsbCAServerIdx,
                  }
                : null,
            stamp: state.useUSBStamp
                ? {
                      password: state.stampUSBPassword,
                      typeIdx: state.stampTypeIdx,
                      devIdx: state.stampDevIdx,
                      caServerIdx: state.stampUsbCAServerIdx,
                  }
                : null,
        };
        await dispatch(signAndSubmit(null, usbSignerCtx));
        try {
            await usbSigner.resetPrivateKey();
        } catch (err) {
            logErrorByLevel('Error on reset usb key');
        }
    };
}

function onClickSendButton() {
    return (dispatch) => {
        dispatch(uploaderActionCreators.onShowPopup(''));
    };
}

const onResetSignSteps = () => async (dispatch, getState) => {
    const {
        signPopup: { stampFile, stampContextId, pkFile, pkContextId },
    } = getState();

    // Clear previous key
    if (stampFile && stampContextId) {
        await signer.clearKeyCtx(stampContextId);
    }
    if (pkFile && pkContextId) {
        await signer.clearKeyCtx(pkContextId);
    }

    dispatch({ type: actions.SIGN_POPUP__RESET_TO_CHOOSE_FIRST_KEY_STATE });
};

function toApp() {
    return push('/app');
}

const setNeedConfirmClosed = (isNeedConfirmClosed) => async (dispatch) =>
    dispatch({
        type: actions.SET_NEED_CONFIRM_CLOSED,
        payload: isNeedConfirmClosed,
    });

const setShowConfirmClose = (isShowConfirmClose) => async (dispatch) =>
    dispatch({
        type: actions.SET_SHOW_CONFIRM_CLOSE,
        payload: isShowConfirmClose,
    });

export default {
    showPopup,
    hidePopup,
    submitRecipient,
    changeCAServer,
    initSignService,
    submitPK,
    setPKPassword,
    submitPKCertificates,
    submitStamp,
    setStampPassword,
    submitStampCertificates,
    sign,
    readKey,
    signAndSubmit,
    retrySignAndSubmit,
    multiSign,
    toggleRememberKeys,
    rememberKeyData,
    deleteKeyDataFromStorage,
    onDelnotWarningAgree,
    onSubmitKeysFromStorage,
    onSubmitRecipients,
    showPrevStatus,
    showNextStatus,

    loadIitSignWidget,

    loadUSBSigner,
    usbSignAndSubmit,
    onSetUSBProxySettings,
    onFindUSBDevices,
    onPkUSBDeviceChange,
    onChangePkUSBCAServer,
    onSetPkUSBPassword,
    onReadPkUSBKey,
    onResetPkUSBKey,
    onAddUSBPK,
    onStampUSBDeviceChange,
    onChangeStampUSBCAServer,
    onSetStampUSBPassword,
    onReadStampUSBKey,
    onResetStampUSBKey,
    onAddUSBStamp,
    onResetSignSteps,
    onClearPK,
    onClearStamp,

    onChangeJksKey,
    onClickSendButton,
    toApp,

    setNeedConfirmClosed,
    setShowConfirmClose,
};
