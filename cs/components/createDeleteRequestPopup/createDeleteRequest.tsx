import * as React from 'react';
import { connect } from 'react-redux';

import { BlackTooltip, FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import { Document } from 'services/documents/ts/types';
import {
    canCreateDeleteRequest,
    isDeleteRequestInitiator,
    isDeleteRequestReceiver,
} from 'services/documents/utils';
import { mapStateToCurrentUser, mapStatetoHasPermission } from 'store/utils';
import { t } from 'ttag';
import IconButton from 'ui/iconButton/iconButton';

import { StoreState } from '../../types/store';
import { IUser } from '../../types/user';

import StatusButton from '../ui/StatusButton/StatusButton';
import Button from '../ui/button/button';
import Message from '../ui/message/message';
import PseudoLink from '../ui/pseudolink/pseudolink';

import SvgDeleteRequest from './images/delete-request.svg';
// icons
import SvgDelete from './images/delete.svg';

// styles
import css from './createDeleteRequest.css';

interface CreateDeleteRequestProps {
    docs: Array<Document>;
    docPage?: boolean;
    requestIconType?: boolean;
    isMobileSheet?: boolean;
    onCreateClick: (evt: Event) => void;
    onResolveRequestClick: (evt: Event) => void;
    onRejectRequestClick?: () => void;
    onAcceptRequestClick?: () => void;
    onCancelDeleteRequestClick?: () => void;
    onCancelDeleteRequestVote?: () => void;
    showComment?: boolean;
    currentUser: IUser;
    hasPermission: (permission: string) => boolean;
}

class CreateDeleteRequest extends React.Component<CreateDeleteRequestProps> {
    state = { showComment: this.props.showComment };

    onToggleMessage(evt: React.SyntheticEvent) {
        evt.preventDefault();
        this.setState({ showComment: !this.state.showComment });
    }

    getIconData() {
        if (this.props.requestIconType) {
            return {
                classname: css.iconRed,
                icon: SvgDeleteRequest,
            };
        }
        return {
            classname: css.icon,
            icon: SvgDelete,
        };
    }

    renderToolbarCanCreateView() {
        const createClick = (evt: any) => {
            this.props.onCreateClick(evt);
        };
        return (
            <BlackTooltip title={t`Видалити`}>
                <FlexBox gap={10} align="center" onClick={createClick}>
                    <div className={this.getIconData().classname}>
                        <IconButton svg={this.getIconData().icon} />
                    </div>
                    {this.props.isMobileSheet && t`Видалити`}
                </FlexBox>
            </BlackTooltip>
        );
    }

    renderToolbarReceiverView() {
        const onResolveRequestClick = (evt: any) => {
            this.props.onResolveRequestClick(evt);
        };
        return (
            <BlackTooltip
                placement="right"
                title={t`Контрагент надіслав запит на видалення документу`}
            >
                <FlexBox
                    gap={10}
                    align="center"
                    onClick={onResolveRequestClick}
                >
                    <div className={this.getIconData().classname}>
                        <IconButton svg={this.getIconData().icon} />
                    </div>
                    {this.props.isMobileSheet &&
                        t`Контрагент надіслав запит на видалення документу`}
                </FlexBox>
            </BlackTooltip>
        );
    }

    renderToolbarInitiatorView() {
        return (
            <BlackTooltip
                disableInteractive
                placement="right"
                title={t`Ви надіслали запит на видалення документу`}
            >
                <FlexBox gap={10} align="center">
                    <div
                        className={cn(
                            this.getIconData().classname,
                            css.iconDisabled,
                        )}
                    >
                        <IconButton svg={this.getIconData().icon} />
                    </div>
                    {this.props.isMobileSheet &&
                        t`Ви надіслали запит на видалення документу`}
                </FlexBox>
            </BlackTooltip>
        );
    }

    toolbarView(canCreate: boolean, isReceiver: boolean, isInitiator: boolean) {
        if (canCreate) return this.renderToolbarCanCreateView();
        if (isReceiver) return this.renderToolbarReceiverView();
        if (isInitiator) return this.renderToolbarInitiatorView();
        return null;
    }

    messageView() {
        return (
            <div className={css.reason}>
                <div className={css.comment} hidden={!this.state.showComment}>
                    {this.props.docs &&
                        this.props.docs[0].deleteRequest.message}
                </div>
                <PseudoLink
                    onClick={(evt: React.SyntheticEvent) =>
                        this.onToggleMessage(evt)
                    }
                >
                    {!this.state.showComment
                        ? t`Переглянути причину запиту`
                        : t`Сховати причину запиту`}
                </PseudoLink>
            </div>
        );
    }

    hasPermissionToDelete = () => {
        return (
            this.props.hasPermission('canDeleteDocument') ||
            this.props.hasPermission('canDeleteDocumentExtended')
        );
    };

    docPageInitiatorView() {
        return (
            <div>
                <Message sizeSmall type="error">
                    {t`Ви надіслали запит на видалення документу`}.
                </Message>
                {this.messageView()}
                <div className={css.button}>
                    <Button
                        disabled={!this.hasPermissionToDelete()}
                        typeContour
                        size="small"
                        theme="blue"
                        width="full"
                        onClick={this.props.onCancelDeleteRequestClick}
                    >
                        {t`Скасувати запит`}
                    </Button>
                </div>
            </div>
        );
    }

    docPageReceiverView() {
        const doc = this.props.docs[0];
        const isApproved = doc.deleteRequest.status === 'accepted';
        const isRejected = doc.deleteRequest.status === 'rejected';
        return (
            <div>
                <Message sizeSmall type="error">
                    {t`Контрагент надіслав запит на видалення документу.`}
                </Message>
                {this.messageView()}
                {isApproved && (
                    <Message sizeSmall type="hint">
                        {t`Очiкується пiдтвердження контрагентiв.`}
                    </Message>
                )}
                <div className={css.button}>
                    {doc.isMultilateral && isRejected ? (
                        <StatusButton
                            isColoredBorder={true}
                            onClick={this.props.onCancelDeleteRequestVote}
                            size="small"
                        >
                            <PseudoLink
                                color={'green'}
                            >{t`Вiдхилити запит`}</PseudoLink>
                        </StatusButton>
                    ) : (
                        <Button
                            disabled={!this.hasPermissionToDelete()}
                            typeContour
                            size="small"
                            width="full"
                            theme="blue"
                            onClick={this.props.onRejectRequestClick}
                        >
                            {t`Вiдхилити запит на видалення`}
                        </Button>
                    )}
                </div>
                <div className={css.button}>
                    {doc.isMultilateral && isApproved ? (
                        <StatusButton
                            isColoredBorder={true}
                            onClick={this.props.onCancelDeleteRequestVote}
                            size="small"
                            type="error"
                        >
                            <PseudoLink
                                color={'red'}
                            >{t`Погодити видалення`}</PseudoLink>
                        </StatusButton>
                    ) : (
                        <Button
                            disabled={!this.hasPermissionToDelete()}
                            typeContour
                            size="small"
                            width="full"
                            theme="red"
                            onClick={this.props.onAcceptRequestClick}
                        >
                            {t`Погодити видалення`}
                        </Button>
                    )}
                </div>
            </div>
        );
    }

    docPageView(isInitiator: boolean, isReceiver: boolean) {
        if (isInitiator) return this.docPageInitiatorView();
        if (isReceiver) return this.docPageReceiverView();
        return null;
    }

    render() {
        const {
            currentUser: {
                currentRole: {
                    company: { edrpou },
                },
            },
        } = this.props;

        const canCreate = canCreateDeleteRequest(
            this.props.docs,
            this.props.currentUser,
        );

        const isReceiver = isDeleteRequestReceiver(this.props.docs, edrpou);
        const isInitiator = isDeleteRequestInitiator(this.props.docs, edrpou);

        return this.props.docPage
            ? this.docPageView(isInitiator, isReceiver)
            : this.toolbarView(canCreate, isReceiver, isInitiator);
    }
}

const mapStateToProps = (state: StoreState) => ({
    ...mapStateToCurrentUser(state),
    ...mapStatetoHasPermission(state),
});

export default connect(mapStateToProps)(CreateDeleteRequest);
