import React, { FC, useEffect, useMemo, useState } from 'react';

import {
    Button,
    FlexBox,
    Select,
    SelectOption,
    snackbarToast,
} from '@vchasno/ui-kit';

import cn from 'classnames';
import { formatFullName, getActiveEmployees } from 'lib/helpers';
import { getCompany, updateDefaultRecipient } from 'services/user';
import { t } from 'ttag';

import { IRole } from '../../types/user';

import InfoCard from '../infoCard/infoCard';

import css from './defaultRecipient.css';

const hint1 = t`У випадку, коли контрагенти не знають, кому саме зі співробітників \n
    вашої компанії надіслати документи, вони можуть скористатися можливістю \n
    автоматичного підбору email. За замовчуванням ми направляємо такі документи \n
    на адміністраторів компанії.`;
const hint2 = t`Тут ви можете самостійно вказати співробітника, котрий буде опрацьовувати такі документи.`;
const plug = t`Адміністратори компанії`;

interface Props {
    companyId: string;
    className?: string;
}

const DefaultRecipient: FC<React.PropsWithChildren<Props>> = ({
    companyId,
    className,
}: Props) => {
    const [activeRoles, setActiveRoles] = useState<IRole[]>([]);
    const [isEditMode, toggleEditMode] = useState(false);
    const [currentRecipient, setCurrent] = useState<IRole | undefined>();
    const [recipient, setRecipient] = useState<IRole | undefined>();

    useEffect(() => {
        const getRoles = async () => {
            const company = await getCompany(companyId);
            const allActiveRoles: IRole[] = getActiveEmployees(
                company.roles,
                //@TO-DO: make type
                //@ts-ignore
            ).map((item) => item.toJS());
            const currentRecipientItem = allActiveRoles.find(
                (r: IRole) => r.isDefaultRecipient,
            );
            setActiveRoles(allActiveRoles);
            setCurrent(currentRecipientItem);
            setRecipient(currentRecipientItem);
        };
        getRoles();
    }, []);

    const recipientName = recipient
        ? `${formatFullName(recipient.user)} ${recipient.user.email}`
        : plug;

    const recipientData = useMemo(
        () => [{ name: t`Співробітник:`, value: recipientName }],
        [recipient],
    );

    const onChange = async () => {
        await updateDefaultRecipient(recipient?.id);
        snackbarToast.success(
            recipient
                ? t`Співробітника, який отримує документи за замовчуванням, успішно змінено`
                : t`Співробітника, який отримує документи за замовчуванням, успішно видалено`,
        );
        setCurrent(recipient);
        toggleEditMode(false);
    };

    const onCancel = () => {
        setRecipient(currentRecipient);
        toggleEditMode(false);
    };

    const renderDefaultRecipient = () => {
        return (
            <>
                <InfoCard data={recipientData} />
                <Button
                    key="edit"
                    theme="secondary"
                    onClick={() => toggleEditMode(true)}
                >{t`Редагувати`}</Button>
            </>
        );
    };

    const renderEditMode = () => {
        const options = activeRoles.map((item) => ({
            label: [formatFullName(item.user), item.user.email].join(' '),
            value: item.id,
        }));

        return (
            <>
                <Select
                    wide
                    isSearchable
                    isClearable
                    label={t`Вкажіть email або ім’я співробітника`}
                    options={options}
                    value={options.find((item) => item.value === recipient?.id)}
                    onChange={(option: SelectOption) => {
                        if (!option) {
                            setRecipient(undefined);
                            return;
                        }

                        setRecipient(
                            activeRoles?.find(
                                (item) => item.id === option?.value,
                            ),
                        );
                    }}
                />
                <FlexBox gap={12}>
                    <Button key="save" onClick={onChange}>{t`Зберегти`}</Button>
                    <Button
                        key="cancel"
                        theme="secondary"
                        onClick={onCancel}
                    >{t`Відмінити`}</Button>
                </FlexBox>
            </>
        );
    };

    return (
        <FlexBox
            direction="column"
            gap={18}
            align="self-start"
            className={cn(css.content, className)}
        >
            <p>{hint1}</p>
            <p>{hint2}</p>
            {isEditMode ? renderEditMode() : renderDefaultRecipient()}
        </FlexBox>
    );
};

export default DefaultRecipient;
