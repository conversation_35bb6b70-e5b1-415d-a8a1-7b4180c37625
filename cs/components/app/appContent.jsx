import React, { Fragment, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Route, Switch, useHistory } from 'react-router-dom';

import ChangedRolePopup from 'components/ChangedRolePopup';
import { DocumentEditModal } from 'components/DocumentEdit';
import DocumentsUploader from 'components/DocumentsUploader';
import ErrorBoundary from 'components/ErrorBoundary';
import InvitationSentPopup from 'components/InvitePopups/InvitationSentPopup/InvitationSentPopup';
import { MultiDocumentEditModal } from 'components/MultiDocumentEditModal';
import TemplateBuilderModal from 'components/TemplateBuilderPage/TemplateBuilderModal';
import { REGISTRATION_CONFIRM_EMAIL_URL } from 'lib/routing/constants';
import { getLocationQuery, stringifyLocationQuery } from 'lib/url';
import PropTypes from 'prop-types';
import { User } from 'records/user';
import {
    getIsNeedToVerifyEmailSelector,
    getIsUserActiveRoleNeededSelector,
} from 'selectors/app.selectors';
import { getIsVisibleSyncRoleErrorBanner } from 'selectors/syncRolesErrorBanner.selectors.ts';

import AlertPopup from '../ui/alertPopup/alertPopup';
import ConfirmPopup from '../ui/confirmPopup/confirmPopup';

import { CompanyEmployeeNumberPopup } from '../CompanyEmployeeNumber';
import { FeatureShow } from '../FeatureDisplay';
import SuccessVerifiedUser from '../SuccessVerifiedUser/SuccessVerifiedUser';
import { useDisplaySuccessVerifiedUser } from '../SuccessVerifiedUser/hooks/useDisplaySuccessVerifiedUser';
import SyncRolesErrorBanner from '../SyncRolesErrorBanner/SyncRolesErrorBanner';
import Documents from '../documents/documents';
import NotificationCenterContainer from '../notificationCenter/notificationCenterContainer';
import VerifyUser from '../verifyUser';
import WarningPopup from '../warningPopup/warningPopup';

const SignPopupContainer = React.lazy(() => import('../signPopup'));

const SignWithKepFlow = React.lazy(() =>
    import(/* webpackChunkName: "SignWithKepFlow" */ '../SignWithKepFlow'),
);

const CreateDeleteRequestPopupContainer = React.lazy(() =>
    import(
        /* webpackChunkName: "CreateDeleteRequestPopupContainer" */ '../createDeleteRequestPopup/createDeleteRequestPopupContainer'
    ),
);

const ShareDocumentPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ShareDocumentPopup" */ '../shareDocumentPopup/shareDocumentPopup'
    ),
);

const BillCreationStatusPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "BillCreationStatusPopup" */ '../billCreationStatusPopup'
    ),
);

const TagsEditPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "TagsEditPopup" */ '../tagsEditPopup/tagsEditPopup'
    ),
);

const ResolveDeleteRequestPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ResolveDeleteRequestPopup" */ '../resolveDeleteRequestPopup/resolveDeleteRequestPopup'
    ),
);

const ExportDocumentsDataPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ExportDocumentsDataPopup" */ '../filters/ExportDocumentsDataPopup'
    ),
);

const InviteRecipientPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "InviteRecipientPopup" */ '../InvitePopups/InviteRecipientPopup'
    ),
);

const InviteRecipientsPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "InviteRecipientsPopup" */ '../InvitePopups/InviteRecipientsPopup'
    ),
);

const InviteUserPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "InviteUserPopup" */ '../InvitePopups/InviteUserPopup'
    ),
);

const CompanyPickerPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "CompanyPickerPopup" */ '../CompanyPickerPopup'
    ),
);

const SuccessBillingForAddingEmployeesPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "SuccessBillingForAddingEmployeesPopup" */ '../InvitePopups/SuccessBillingForAddingEmployeesPopup'
    ),
);

const ReminderPayAddingEmployeesPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ReminderPayAddingEmployeesPopup" */ '../InvitePopups/ReminderPayAddingEmployeesPopup'
    ),
);

const CheckKeyPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "CheckKeyPopup" */ '../checkKeyPopup/checkKeyPopup'
    ),
);

const ChangePasswordPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ChangePasswordPopup" */ '../changePasswordPopup'
    ),
);

const NewCompaniesNotificationPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "NewCompaniesNotificationPopup" */ '../newCompaniesNotificationPopup/newCompaniesNotificationPopup'
    ),
);

const ProRateInfoPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ProRateInfoPopup" */ '../proRateInfoPopup/proRateInfoPopup'
    ),
);

const ProRateTrialPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ProRateTrialPopup" */ '../proRateTrialPopup/proRateTrialPopup'
    ),
);

const DocumentViewLimitPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "DocumentViewLimitPopup" */ '../DocumentViewLimitPopup'
    ),
);

const DocumentViewLockedPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "DocumentViewLockedPopup" */ '../DocumentViewLockedPopup'
    ),
);

const AdditionalInfoPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "AdditionalInfoPopup" */ '../AdditionalInfoPopup/AdditionalInfoPopup'
    ),
);

const CsatPopupContainer = React.lazy(() =>
    import(/* webpackChunkName: "CsatPopup" */ '../CsatPopup'),
);

const Add2FAPopup = React.lazy(() =>
    import(/* webpackChunkName: "Add2FAPopup" */ '../Add2FAPopup'),
);

const TerminateEmployeesPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "TerminateEmployeesPopup" */ '../TerminateEmployeesPopup/TerminateEmployeesPopup'
    ),
);

const KepMobileAppPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "KepMobileAppPopup" */ '../KepMobileAppPopup/KepMobileAppPopup'
    ),
);

const FeedbackPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "FeedbackPopup" */ '../FeedbackPopup/FeedbackPopup'
    ),
);

const AntivirusPopup = React.lazy(() =>
    import(/* webpackChunkName: "AntivirusPopup" */ '../AntivirusPopup'),
);

const DocumentsUploaderOnboardingPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "DocumentsUploaderOnboardingPopup" */ 'components/DocumentsUploader/OnboardingPopup'
    ),
);

const FinishTrialPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "FinishTrialPopup" */ 'components/FinishTrialPopup'
    ),
);

const TrialInfoPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "TrialInfoPopup" */ 'components/TrialInfoPopup/TrialInfoPopup'
    ),
);

const MobileAppEDOPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "MobileAppPopup" */ 'components/MobileAppEDOPopup/MobileAppEDOPopup'
    ),
);

const TrialRateSelectCompanyPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "TrialRateSelectCompanyPopup" */
        'components/TrialRateSelectCompanyPopup/TrialRateSelectCompanyPopup'
    ),
);

const ArchiveSurveyPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "ArchiveSurveyPopup" */
        'components/ArchiveSurveyPopup/ArchiveSurveyPopup'
    ),
);

const KepMobileAppAd = React.lazy(() =>
    import(
        /* webpackChunkName: "KepMobileAppAd" */ 'components/KepMobileAppAd/KepMobileAppAd'
    ),
);

const DocumentCreationModal = React.lazy(() =>
    import(
        /* webpackChunkName: "DocumentCreationModal" */ 'components/DocumentCreation/DocumentCreationModal'
    ),
);

const DocumentAutofillTemplateModal = React.lazy(() =>
    import(
        /* webpackChunkName: "DocumentAutofillTemplateModal" */ 'components/DocumentCreation/DocumentAutofillTemplateModal'
    ),
);

const CreateDraftTemplateContainer = React.lazy(() =>
    import(
        /* webpackChunkName: "CreateDraftTemplateContainer" */ 'components/DocumentCreation/CreateDraftTemplateEditor/CreateDraftTemplateContainer'
    ),
);

const TemplateEditorContainer = React.lazy(() =>
    import(
        /* webpackChunkName: "TemplateEditorContainer" */ 'components/DocumentCreation/TemplateEditor/TemplateEditorContainer'
    ),
);

const SignSuccessPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "SignSuccessPopup" */ 'components/SignSuccessPopup/SignSuccessPopup'
    ),
);

const AnnulmentActPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "AnnulmentActPopup" */ 'components/AnnulmentAct/AnnulmentActPopup'
    ),
);

const AppContent = (props) => {
    let content = props.children;

    const history = useHistory();

    const isVisibleSuccessVerifiedUserForm = useDisplaySuccessVerifiedUser();
    const isVisibleSyncRoleErrorBanner = useSelector(
        getIsVisibleSyncRoleErrorBanner,
    );

    // NOTE: this component should be rendered only when user is already fetched
    // and currentUser is available in the store to avoid wrong redirects.
    // Check AppContainer to see how this requirement is satisfied
    const needToVerifyEmail = useSelector(getIsNeedToVerifyEmailSelector);
    const needToVerifyUser = useSelector(getIsUserActiveRoleNeededSelector);

    // Redirect to confirm email page if user needs to verify email
    useEffect(() => {
        if (
            needToVerifyEmail &&
            history.location.pathname !== REGISTRATION_CONFIRM_EMAIL_URL
        ) {
            const searchQuery = getLocationQuery(history.location);
            history.push({
                pathname: REGISTRATION_CONFIRM_EMAIL_URL,
                search: stringifyLocationQuery(searchQuery),
            });
        }
    }, [needToVerifyEmail, history]);

    if (
        needToVerifyEmail &&
        location.pathname !== REGISTRATION_CONFIRM_EMAIL_URL
    ) {
        return null; // do not render anything if user needs to verify email we should redirect to confirm email page
    } else if (
        needToVerifyUser &&
        location.pathname !== '/app/registration/choose-project' &&
        location.pathname !== '/app/settings' &&
        !location.pathname.startsWith('/sign-sessions/') &&
        !isVisibleSyncRoleErrorBanner
    ) {
        content = (
            <Documents>
                <VerifyUser />
            </Documents>
        );
    } else if (isVisibleSuccessVerifiedUserForm) {
        content = (
            <Documents>
                <SuccessVerifiedUser />
            </Documents>
        );
    } else if (isVisibleSyncRoleErrorBanner) {
        content = (
            <Documents>
                <SyncRolesErrorBanner />
            </Documents>
        );
    }

    const showNewCompaniesPopup =
        props.newRoles.size > 0 && !props.isCheckKeyPopupShown && !config.TEST;

    return (
        <Fragment>
            {/* content should be before popups because of mobile popups in popup */}
            {content}

            <React.Suspense fallback={null}>
                <DocumentsUploaderOnboardingPopup />
                <SignPopupContainer />
                <SignSuccessPopup />
                <DocumentEditModal />
                <MultiDocumentEditModal />
                <CreateDeleteRequestPopupContainer />
                <ShareDocumentPopup />
                <BillCreationStatusPopup />
                <AntivirusPopup />
                <FinishTrialPopup />
                <MobileAppEDOPopup />
                <SignWithKepFlow />
                <FeatureShow feature="ENABLE_REVOKE_DOCUMENTS_FOR_EDI">
                    <AnnulmentActPopup />
                </FeatureShow>
                <ChangedRolePopup />
            </React.Suspense>

            <WarningPopup
                isActive={props.isWarningPopupShown}
                warningPopupType={props.warningPopupType}
                onClose={props.onWarningPopupClose}
            />
            <AlertPopup
                isActive={props.isAlertPopupShown}
                title={props.alertPopupTitle}
                content={props.alertPopupContent}
                onClose={props.onAlertPopupClose}
            />
            <ConfirmPopup
                isActive={props.isConfirmPopupShown}
                title={props.confirmPopupTitle}
                text={props.confirmPopupText}
                buttonText={props.confirmButtonText}
                buttonTheme={props.confirmButtonTheme}
                cancelText={props.cancelText}
                theme={props.theme}
                onConfirm={props.onConfirmPopupConfirm}
                onClose={props.onConfirmPopupClose}
            />
            <Switch>
                <Route path="/app">
                    <CompanyEmployeeNumberPopup />
                    <React.Suspense fallback={null}>
                        <DocumentCreationModal />
                        <DocumentAutofillTemplateModal />
                        <TemplateBuilderModal />
                        <CreateDraftTemplateContainer />
                        <TemplateEditorContainer />
                        <FeatureShow feature="ENABLE_NEW_UPLOAD_FLOW">
                            <DocumentsUploader />
                        </FeatureShow>
                        <TagsEditPopup />
                        <ResolveDeleteRequestPopup />
                        <ExportDocumentsDataPopup />
                        <CheckKeyPopup />
                        <InviteUserPopup />
                        <CompanyPickerPopup />
                        <SuccessBillingForAddingEmployeesPopup />
                        <ReminderPayAddingEmployeesPopup />
                        <InvitationSentPopup />
                        <InviteRecipientPopup />
                        <InviteRecipientsPopup />
                        <ChangePasswordPopup
                            active={props.isChangePasswordPopupShown}
                            onClose={props.onHideChangePasswordPopup}
                        />
                        {showNewCompaniesPopup && (
                            <NewCompaniesNotificationPopup
                                newRoles={props.newRoles}
                                active={props.isNewCompanyPopupShown}
                                onNewCompanyAgreed={props.onNewCompanyAgreed}
                            />
                        )}
                        <ProRateInfoPopup />
                        <ProRateTrialPopup />
                        <FeatureShow feature="ARCHIVE_PURCHASE_IMPROVEMENT">
                            <DocumentViewLimitPopup />
                        </FeatureShow>
                        <DocumentViewLockedPopup />
                        <AdditionalInfoPopup />
                        {/* Виключили в рамках задачі https://vchasno-group.atlassian.net/browse/EC-49
                            TODO: видалити, якщо за півроку виявиться непотрібним
                        */}
                        {/* <FeatureShow feature="ALERT_UNLIMIT_FOR_FOP">
                            <AlertUnlimitForFOP />
                        </FeatureShow> */}
                        <FeatureShow feature="CSAT_ARCHIVE_SCAN_RECOGNITION_BY_AI">
                            <CsatPopupContainer type="document_scans_meta_recognition_by_ai" />
                        </FeatureShow>
                        <Add2FAPopup />
                        <TerminateEmployeesPopup />
                        <KepMobileAppPopup />
                        <FeedbackPopup />
                        <TrialInfoPopup />
                        <TrialRateSelectCompanyPopup />
                        <FeatureShow feature="ENABLE_ARCHIVE_SURVEY_POPUP">
                            <ArchiveSurveyPopup />
                        </FeatureShow>
                        <ErrorBoundary fallback={null}>
                            <KepMobileAppAd />
                        </ErrorBoundary>
                    </React.Suspense>
                </Route>
            </Switch>

            <NotificationCenterContainer />
        </Fragment>
    );
};

AppContent.propTypes = {
    applicationMode: PropTypes.string.isRequired,
    isAlertPopupShown: PropTypes.bool,
    isChangePasswordPopupShown: PropTypes.bool,
    isCheckKeyPopupShown: PropTypes.bool,
    isNewCompanyPopupShown: PropTypes.bool,
    isWarningPopupShown: PropTypes.bool,
    currentUser: PropTypes.instanceOf(User).isRequired,
    newRoles: PropTypes.object,
    alertPopupTitle: PropTypes.string,
    alertPopupContent: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.element,
    ]),
    onAlertPopupClose: PropTypes.func.isRequired,
    onStartUserVerify: PropTypes.func.isRequired,
    onStartUserVerifyUSB: PropTypes.func.isRequired,
    onStartUserVerifyWidgetIit: PropTypes.func.isRequired,
    onNewCompanyAgreed: PropTypes.func.isRequired,
    onHideChangePasswordPopup: PropTypes.func.isRequired,
    warningPopupType: PropTypes.string,
    onWarningPopupClose: PropTypes.func.isRequired,
};

export default AppContent;
