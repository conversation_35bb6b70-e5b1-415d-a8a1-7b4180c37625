import appActions from 'components/app/appActions';
import { warningPopupContent } from 'components/warningPopup/enum';
import { push, replace } from 'connected-react-router';
import 'lib/constants';
import {
    WS_KEY_ES_HAS_ACTUAL_UPLOAD,
    WS_KEY_NOT_SHOW_OLD_BROWSER_ALERT,
    WS_KEY_UPLOAD_DOCS_IDS,
} from 'lib/constants';
import {
    checkIsValidEdrpou,
    getIsArchivePageUtility,
    getIsCurrentUserSourceAfterSign,
    isEmptyObject,
    isWorkersSupport,
} from 'lib/helpers';
import { redirect } from 'lib/navigation';
import { queryClient } from 'lib/queries';
import { setSentryTag, setSentryUser } from 'lib/sentry';
import { getLocationQuery, stringifyLocationQuery } from 'lib/url';
import {
    getLocalStorageItem,
    setLocalStorageItem,
    setSessionStorageItem,
} from 'lib/webStorage';
import merge from 'lodash/merge';
import { apiCompany, apiCurrentSignSessionUser } from 'records/user';
import {
    getAllActiveCompanyRoles,
    getUserRegistrationMethod,
} from 'selectors/app.selectors';
import { monitorVersionChange } from 'services/application';
import { createTrialRate } from 'services/billing';
import { LATEST_PRO_PLUS_TRIAL_RATE } from 'services/billing/constants';
import { getDocumentFields } from 'services/documentFields/api';
import {
    ApplicationMode,
    FeatureFlags,
    RoleStatuses,
    SignSessionSource,
    SignSessionStatus,
} from 'services/enums';
import { getFeatureFlags } from 'services/featureFlags';
import {
    finishSignSession,
    getIsSharedDocumentView,
    getSignSessionId,
    isSignSession,
} from 'services/sign-session';
import { getDocsIndexationStatus } from 'services/ts/application';
import {
    getBillingAccounts,
    getCompanyRolesForConfigApi,
    getCurrentSignSession,
    getCurrentUser,
    newCompanyAgreed,
    syncUserRoles,
} from 'services/user';
import { setTrialInfoPopupOpen } from 'store/trialInfoPopup';
import { setTrialRateSelectCompanyPopupOpen } from 'store/trialRateSelectCompanyPopupSlice';
import { t } from 'ttag';

import checkKeyPopupActionCreators from '../checkKeyPopup/checkKeyPopupActionCreators';
import DocumentActionCreators from '../document/documentActionCreators';
import documentListActionCreators from '../documentList/documentListActionCreators';
import { fetchRequiredDocuments } from '../documentSettings/documentSettingsActionCreators';
import notificationCenterActionCreators from '../notificationCenter/notificationCenterActionCreators';
import triggerNotificationActionCreators from '../triggerNotification/triggerNotificationActionCreators';
import actions from './appActions';

import utils, {
    checkOldBrowser,
    initColbert,
    sendFBPCompleteRegistrationEvent,
} from './utils';

import eventTracking from '../../services/analytics/eventTracking';
import auth from '../../services/auth';
import googleApi from '../../services/google/googleApi';
import loggerService from '../../services/logger';
import syncRolesErrorBannerActionsCreators from '../SyncRolesErrorBanner/syncRolesErrorBannerActionsCreators';

function showESUploadProgress({
    isAppInit = false,
    onSuccessCallback = null,
} = {}) {
    return async (dispatch, getState) => {
        // Check for actual upload exists
        const hasActualUpload = getLocalStorageItem(
            WS_KEY_ES_HAS_ACTUAL_UPLOAD,
        );

        if (!hasActualUpload) {
            return;
        }

        const {
            app: {
                currentUser,
                flags: { [FeatureFlags.ES_SEARCH]: isEsSearchEnabled },
            },
        } = getState();

        const uploadDocsIds = getLocalStorageItem(WS_KEY_UPLOAD_DOCS_IDS) || [];

        // No active upload
        if (!uploadDocsIds.length) {
            setLocalStorageItem(WS_KEY_ES_HAS_ACTUAL_UPLOAD, false);
            return;
        }

        const roleId =
            currentUser && currentUser.currentRole
                ? currentUser.currentRole.id
                : null;

        if (!roleId || !isEsSearchEnabled) return;

        // Start checking upload progress
        let uploadProgress;
        try {
            uploadProgress = await getDocsIndexationStatus(uploadDocsIds);
        } catch (err) {
            loggerService.error('Error on checking upload progress', {
                err: err.message,
                stack: err.stack,
            });
            return;
        }

        const uploadProgressProcess =
            uploadDocsIds.length - uploadProgress.length;

        // Upload ended
        if (uploadProgress.length === 0) {
            // Show success upload notification if not on app init
            if (!isAppInit) {
                dispatch(
                    notificationCenterActionCreators.addNotification({
                        title: t`Обробка документів`,
                        type: 'progress',
                        progressTotalCount: uploadDocsIds.length,
                        progressCurrentCount: uploadProgressProcess,
                        borderColor: 'green',
                        progressBarColor: 'green',
                        showCloseButton: true,
                        autoClose: 3000,
                    }),
                );

                onSuccessCallback?.();
            }

            setLocalStorageItem(WS_KEY_ES_HAS_ACTUAL_UPLOAD, false);
            setLocalStorageItem(WS_KEY_UPLOAD_DOCS_IDS, []);

            return;
        }

        const [updateNotification] = dispatch(
            notificationCenterActionCreators.addNotification({
                title: t`Обробка документів`,
                type: 'progress',
                progressTotalCount: uploadDocsIds.length,
                progressCurrentCount: uploadProgressProcess,
            }),
        );

        // Recursive function for checking upload progress
        async function checkUploadProgress() {
            try {
                uploadProgress = await getDocsIndexationStatus(uploadDocsIds);
            } catch (err) {
                loggerService.error('Error on checking upload progress', {
                    err: err.message,
                    stack: err.stack,
                });
                return;
            }

            const uploadProgressProcessInterval =
                uploadDocsIds.length - uploadProgress.length;

            // Still uploading, update info
            // Very important to check if uploadProgress.length is not 0
            if (uploadProgress.length) {
                updateNotification({
                    progressCurrentCount: uploadProgressProcessInterval,
                });
                // Wait 1 second and check again
                await new window.Promise((resolve) =>
                    setTimeout(resolve, 1_000),
                );
                await checkUploadProgress();
                // Upload finished
            } else {
                updateNotification({
                    borderColor: 'green',
                    progressBarColor: 'green',
                    progressCurrentCount: uploadProgressProcessInterval,
                    showCloseButton: true,
                    autoClose: 3000,
                });

                // Reload documents list
                if (isAppInit) {
                    documentListActionCreators.onLoadDocuments()(
                        dispatch,
                        getState,
                    );
                }

                onSuccessCallback?.();

                setLocalStorageItem(WS_KEY_ES_HAS_ACTUAL_UPLOAD, false);
                setLocalStorageItem(WS_KEY_UPLOAD_DOCS_IDS, []);
            }
        }

        await checkUploadProgress();
    };
}

function onAlertOldBrowserClose() {
    return (dispatch) => {
        dispatch({ type: actions.APP__HIDE_HEADER_ALERT });
        setSessionStorageItem(WS_KEY_NOT_SHOW_OLD_BROWSER_ALERT, true);
    };
}

function onStartUserVerify() {
    return (dispatch) => {
        dispatch(checkKeyPopupActionCreators.onShow('confirmation'));
    };
}

function onStartUserVerifyWidgetIit() {
    return (dispatch) => {
        dispatch(
            checkKeyPopupActionCreators.onShow(
                'confirmation',
                false,
                '',
                false,
                true,
            ),
        );
    };
}

function onStartUserVerifyUSB() {
    return (dispatch) => {
        dispatch(
            checkKeyPopupActionCreators.onShow('confirmation', false, '', true),
        );
    };
}

function onFetchCompanyDocumentFields() {
    return async (dispatch) => {
        const documentFields = await getDocumentFields();
        dispatch({
            type: actions.APP__SET_COMPANY_DOCUMENT_FIELDS,
            documentFields,
        });
        dispatch(documentListActionCreators.onSetTableSettings());
    };
}

/**
 * @param {IUser} currentUser
 * @param {object} [meta]
 * @param {boolean} meta.isInitial
 */
function setCurrentUser(currentUser, meta = {}) {
    return (dispatch, getState) => {
        const state = getState();
        const {
            app: {
                currentUser: {
                    source,
                    currentCompany: { config: currentCompanyConfig },
                },
            },
            documentList,
            router: {
                location: { pathname },
            },
        } = state;
        // this value need for remove duplication request
        const isInitialUserSet = Boolean(meta.isInitial);
        const isDocumentListPage = pathname.includes('/documents');
        const isArchivePage = getIsArchivePageUtility(pathname);
        const newRoles = currentUser.roles.filter((role) => !role.dateAgreed);
        const isNewCompanyPopupShown = !newRoles.isEmpty();

        queryClient.invalidateQueries();

        setSentryUser({
            id: currentUser.id,
            email: currentUser.email,
            username: [currentUser.firstName, currentUser.lastName].join(' '),
        });

        setSentryTag('company_id', currentUser.currentCompany.id);
        setSentryTag('company_edrpou', currentUser.currentCompany.edrpou);
        setSentryTag('role_id', currentUser.currentRole.id);

        const registrationMethod = getUserRegistrationMethod(state);
        const isSourceAfterSign = getIsCurrentUserSourceAfterSign(source);
        const isEnable2faForInternalUsers =
            currentCompanyConfig?.enable_2fa_for_internal_users;

        const isChangePasswordPopupShown =
            currentUser.isAutogeneratedPassword &&
            (isSourceAfterSign ? !isEnable2faForInternalUsers : true) &&
            !(
                registrationMethod === 'google' ||
                registrationMethod === 'microsoft'
            );

        dispatch({
            type: actions.APP__SET_USER,
            currentUser,
            newRoles,
            isChangePasswordPopupShown,
            isNewCompanyPopupShown,
        });

        loggerService.log('Start client role session', {
            companyId: currentUser.currentCompany.id,
            companyEdrpou: currentUser.currentCompany.edrpou,
            roleId: currentUser.currentRole.id,
            email: currentUser.email,
        });

        if (documentList) {
            dispatch(DocumentActionCreators.onUserChange());
            dispatch(
                documentListActionCreators.onClearDocumentsFilteringByProperty(
                    null,
                    isDocumentListPage,
                ),
            );
        }

        // Re-Load trigger notifications
        if ((isDocumentListPage || isArchivePage) && !isInitialUserSet) {
            dispatch(triggerNotificationActionCreators.deleteNotification());
            dispatch(triggerNotificationActionCreators.load(25));
        }
    };
}

export function onRefreshCurrentUser(appForceUpdate = false, forceUpdateRoute) {
    return async (dispatch) => {
        const currentUser = await getCurrentUser();

        eventTracking.setCustomDimensions({
            dimension3: currentUser.currentCompany.edrpou,
        });

        dispatch(setCurrentUser(currentUser));

        // Get extra document fields for new current company
        await Promise.all([
            dispatch(fetchRequiredDocuments()),
            dispatch(onFetchCompanyDocumentFields()),
        ]);

        if (appForceUpdate) {
            // Need first to redirect to main route for all data updating,
            // then redirect to second route if need (e.g., company config case)
            dispatch(push('/app/documents'));
            if (forceUpdateRoute) {
                dispatch(push(forceUpdateRoute));
            }
        }
    };
}

function onWarningPopupShow(warningPopupType) {
    return (dispatch) => {
        dispatch({
            type: actions.APP__SET_WARNING_POPUP_ACTIVITY,
            isActive: true,
            warningPopupType,
        });
    };
}

function onWarningPopupClose() {
    return (dispatch) => {
        dispatch({
            type: actions.APP__SET_WARNING_POPUP_ACTIVITY,
            isActive: false,
            warningPopupType: '',
        });
    };
}

export function onAlertPopupShow(title, content) {
    return (dispatch) => {
        dispatch({ type: actions.APP__SHOW_ALERT_POPUP, title, content });
    };
}

function onAlertPopupClose() {
    return (dispatch) => {
        dispatch({ type: actions.APP__HIDE_ALERT_POPUP });
    };
}

export function onConfirmPopupShow(popupProps) {
    return (dispatch) => {
        dispatch({ type: actions.APP__SHOW_CONFIRM_POPUP, ...popupProps });
    };
}

function onConfirmPopupClose() {
    return (dispatch) => {
        dispatch({ type: actions.APP__HIDE_CONFIRM_POPUP });
    };
}

function updateCurrentUser() {
    return async (dispatch) => {
        try {
            const currentUser = await getCurrentUser();
            dispatch({
                type: actions.APP__UPDATE_CURRENT_USER,
                currentUser,
            });
        } catch (error) {
            loggerService.error('Error during updating current user', {
                err: error.message,
                stack: error.stack,
            });
        }
    };
}

/**
 * Оновити стан поточного користувача в Redux без виклику запиту до API
 */
function updateCurrentUserState({ update }) {
    return async (dispatch, getState) => {
        const {
            app: { currentUser },
        } = getState();

        dispatch({
            type: actions.APP__UPDATE_CURRENT_USER,
            currentUser: currentUser.mergeDeep(update),
        });
    };
}

function getCurrentCompanyRolesForConfig() {
    return async (dispatch, getState) => {
        const {
            app: {
                currentUser: { currentCompany },
            },
        } = getState();

        try {
            const roles = await getCompanyRolesForConfigApi(currentCompany.id);
            dispatch(updateCompanyState({ payload: { roles } }));
        } catch (error) {
            loggerService.error('Error while getting roles', {
                err: error.message,
                stack: error.stack,
            });
        }
    };
}

function updateCompanyState({ payload }) {
    return async (dispatch, getState) => {
        const {
            app: { currentUser },
        } = getState();
        const currentCompanyObject = currentUser.currentCompany.toJS();

        dispatch({
            type: appActions.APP__UPDATE_CURRENT_COMPANY,
            currentCompany: apiCompany({
                ...currentCompanyObject,
                ...payload,
            }),
        });
    };
}

function updateCompanyConfigState({ payload }) {
    return async (dispatch, getState) => {
        const {
            app: { currentUser },
        } = getState();
        const currentCompanyObject = currentUser.currentCompany.toJS();

        dispatch({
            type: appActions.APP__UPDATE_CURRENT_COMPANY,
            currentCompany: apiCompany({
                ...currentCompanyObject,
                config: { ...merge(currentCompanyObject.config, payload) },
            }),
        });
    };
}

function setFeatureFlags(payload) {
    return {
        type: actions.APP__SET_FEATURE_FLAGS,
        payload,
    };
}

function reloadFeatureFlags() {
    return async (dispatch) => {
        const flags = await getFeatureFlags();

        dispatch(setFeatureFlags(flags));
    };
}

// relogin user if he created the bill for another available company
function handleRoleIdParam() {
    return async (dispatch) => {
        const params = getLocationQuery(location);
        if (params.role_id) {
            try {
                await auth.activateRole(params.role_id);
            } catch (error) {
                dispatch(onAlertPopupShow(t`Помилка!`, error.message));
            } finally {
                delete params.role_id;
                dispatch(
                    replace({
                        pathname: location.pathname,
                        search: stringifyLocationQuery(params),
                    }),
                );
            }
        }
    };
}

function updateAppVersionChange(isUpdated = false) {
    return (dispatch) => {
        dispatch({
            type: actions.APP_VERSION_UPDATED,
            appVersionUpdated: isUpdated,
        });
    };
}

function onMount() {
    // eslint-disable-next-line complexity
    return async (dispatch) => {
        const params = getLocationQuery(location);
        // if params.role_id query
        await dispatch(handleRoleIdParam());
        const isSharedDocumentView = getIsSharedDocumentView(location.pathname);
        // Get info about current user and sign session before mounting app component
        const currentSignSession =
            isSignSession(location.pathname) || isSharedDocumentView
                ? await getCurrentSignSession()
                : null;

        // Get feature flags
        const flags = await getFeatureFlags();

        if (params?.edrpou && flags?.NEW_REGISTRATION_SYNC_ROLES) {
            const isValidEdrpou = checkIsValidEdrpou(params.edrpou);
            if (isValidEdrpou) {
                await dispatch(syncRoles(params.edrpou));
            }
        }

        // для сесії шарингу документу якщо користувач не авторизований то currentSignSession буде null
        if (isSharedDocumentView && !currentSignSession) {
            return;
        }

        const currentUser =
            currentSignSession || isSharedDocumentView
                ? apiCurrentSignSessionUser(currentSignSession)
                : await getCurrentUser();
        eventTracking.setCustomDimensions({
            dimension3: currentUser.currentCompany.edrpou,
        });

        // Mounting app component
        dispatch({
            type: actions.APP__INITIALIZED,
            currentSignSession,
            currentUser,
            flags,
        });

        dispatch(setCurrentUser(currentUser, { isInitial: true }));

        const isStartedSignSessionStatus =
            currentSignSession &&
            Object.values(SignSessionStatus).includes(
                currentSignSession.status,
            );

        if (isSharedDocumentView) {
            // shared document view mode
            dispatch({
                type: actions.APP__SET_APPLICATION_MODE,
                applicationMode: ApplicationMode.SHARED_DOCUMENT_VIEW,
            });
        } else if (isStartedSignSessionStatus) {
            // Sign session mode
            dispatch({
                type: actions.APP__SET_APPLICATION_MODE,
                applicationMode: ApplicationMode.SIGN_SESSION,
            });
        } else {
            // Normal application mode
            // Monitor application version
            monitorVersionChange(() => dispatch(updateAppVersionChange(true)));

            // Initialize Colbert
            await initColbert();

            // Get extra document fields, templates for new current company
            await dispatch(onFetchCompanyDocumentFields());

            // Show ElasticSearch documents upload progress
            await dispatch(showESUploadProgress({ isAppInit: true }));

            // if user redirected to EDO from lending by button 'try free' to activate TrialRate it has 2 scenario - user has multiRoles or oneRole

            const isActivateTrialRateRequest =
                params.activeProTrial || currentUser.trialAutoEnable;

            const currentUserRoles = currentUser?.roles;

            const isUserHasMultipleRoles =
                currentUserRoles.toArray().length > 1;

            const activeRates = currentUser?.currentRole?.company?.activeRates.toArray();

            const currentRoleHasUltimateRate =
                activeRates && utils.isRoleHasUltimateRate(activeRates);

            // multiRoleScenario - open TrialRateCompaniesSelectPopup
            if (
                isActivateTrialRateRequest &&
                isUserHasMultipleRoles &&
                currentUser?.currentRole?.id
            ) {
                dispatch(setTrialRateSelectCompanyPopupOpen(true));
            }

            // oneRole scenario - activate TrialRate

            const isTrialRateAlreadyActivated =
                activeRates && utils.isRatesHasActiveTrial(activeRates);

            if (
                currentUser?.currentRole?.id &&
                isActivateTrialRateRequest &&
                !isUserHasMultipleRoles &&
                !isTrialRateAlreadyActivated &&
                currentRoleHasUltimateRate
            ) {
                dispatch(
                    setTrialInfoPopupOpen({
                        isTrialInfoPopupOpen: true,
                        isRoleHasUltimateRate: true,
                    }),
                );
            }

            if (
                currentUser?.currentRole?.id &&
                isActivateTrialRateRequest &&
                !isUserHasMultipleRoles &&
                !isTrialRateAlreadyActivated &&
                !currentRoleHasUltimateRate
            ) {
                try {
                    const trialRate = await createTrialRate(
                        LATEST_PRO_PLUS_TRIAL_RATE,
                    );
                    if (trialRate.ok) {
                        dispatch(
                            setTrialInfoPopupOpen({
                                isTrialInfoPopupOpen: true,
                                isTrialRateAlreadyExist: false,
                            }),
                        );
                    }
                    await dispatch(onRefreshCurrentUser());
                } catch (error) {
                    dispatch(
                        notificationCenterActionCreators.addNotification({
                            title: t`Перевірка тарифів`,
                            type: 'text',
                            textType: 'error',
                            text: t`Не вдалося активувати тестовий період, ${error.reason}`,
                            autoClose: 5000,
                        }),
                    );
                }
            }
        }

        // Use for both normal and minimal application modes
        // Send analytics
        if (params.after_registration) {
            sendFBPCompleteRegistrationEvent(
                params.after_registration,
                currentUser.email,
            );
        }

        // Confirm company from irid
        if (params.irid) {
            const currentPendingRole = currentUser.roles.find((role) => {
                return (
                    role.id === params.irid &&
                    role.status === RoleStatuses.PENDING
                );
            });
            if (currentPendingRole && !isEmptyObject(currentPendingRole)) {
                dispatch(
                    checkKeyPopupActionCreators.onShow(
                        'confirmation',
                        false,
                        currentPendingRole.company.edrpou,
                    ),
                );
            }
        }

        // Load Google API
        googleApi.loadAuthApi();

        // Check for web worker support
        if (!isWorkersSupport()) {
            dispatch(onWarningPopupShow(warningPopupContent.WORKER_SUPPORT));
        }

        // Check browser version
        if (checkOldBrowser())
            dispatch({ type: actions.APP__SHOW_HEADER_ALERT });

        // Виключили в рамках задачі https://vchasno-group.atlassian.net/browse/EC-49
        // TODO: видалити, якщо за півроку виявиться непотрібним
        // if (check2FARemind(currentUser) && !isSharedDocumentView) {
        //     dispatch(
        //         notificationCenterActionCreators.addNotification({
        //             type: 'text',
        //             title: t`Увімкніть двофакторну аутентифікацію`,
        //             text: t`Це додатковий рівень безпеки вашого облікового запису.`,
        //             link: '/app/settings',
        //             linkTitle: t`Налаштування`,
        //             showCloseButton: true,
        //             autoClose: 30000, // 30s
        //         }),
        //     );
        //     Cookies.set(COOKIE_KEY_2FA_REMINDED, true, {
        //         expires: 30,
        //         secure: config.COOKIE_SECURE,
        //     });
        // }
    };
}

function updateBillingAccounts() {
    return async (dispatch) => {
        if (getSignSessionId()) {
            return;
        }

        const billingAccounts = await getBillingAccounts();

        dispatch({
            type: actions.APP__SET_BILLING_ACCOUNTS,
            billingAccounts,
        });
    };
}

function onNewCompanyAgreed(ids) {
    return async (dispatch) => {
        await newCompanyAgreed({ role_ids: ids });
        dispatch({ type: actions.APP__HIDE_NEW_COMPANY_POPUP });
    };
}

function onHideChangePasswordPopup() {
    return (dispatch) => {
        dispatch({ type: actions.APP__HIDE_CHANGE_PASSWORD_POPUP });
    };
}

function onSignSessionCancel() {
    return async (dispatch, getState) => {
        const {
            app: {
                currentSignSession: { cancelUrl },
            },
        } = getState();
        if (!cancelUrl) {
            return;
        }

        try {
            redirect(await finishSignSession(cancelUrl));
        } catch (err) {
            notificationCenterActionCreators.addNotification({
                type: 'text',
                textType: 'error',
                text: err.message,
                showCloseButton: true,
            });
        }
    };
}

function onSignSessionFinish() {
    return async (_, getState) => {
        const {
            document,
            app: { currentSignSession, applicationMode },
            router: { location },
        } = getState();

        // Do nothing if we are not in sign session mode
        if (applicationMode !== ApplicationMode.SIGN_SESSION) {
            return;
        }

        // Do not finish sign session for link from inbox email and EDI
        if (
            currentSignSession.source === SignSessionSource.INBOX ||
            currentSignSession.source === SignSessionSource.EDI
        ) {
            return;
        }

        // To keep backward compatibility with old sign session links,
        // EDI sends is_edi=1 in query parameters
        const query = getLocationQuery(location);
        if (query.is_edi === '1') {
            return;
        }

        // Do nothing if sign session doesn't have finish URL
        if (!currentSignSession?.finishUrl) {
            return;
        }

        try {
            const finishSession = async () =>
                redirect(
                    await finishSignSession(
                        currentSignSession.finishUrl || null,
                    ),
                );
            eventTracking.sendEvent(
                'sign_session',
                'sign_and_send',
                `${document.edrpouOwner} ${document.companyNameOwner}`,
                finishSession,
            );
        } catch (err) {
            notificationCenterActionCreators.addNotification({
                type: 'text',
                textType: 'error',
                text: err.message,
                showCloseButton: true,
            });
            eventTracking.sendEvent(
                'sign_session',
                'error',
                `${document.edrpouOwner} ${document.companyNameOwner} ${err.message}`,
            );
        }
    };
}

function startUpdatingDocument(id) {
    return (dispatch) =>
        dispatch({
            type: actions.APP__START_UPDATING_DOCUMENT,
            id,
        });
}

function finishUpdatingDocument(id) {
    return (dispatch) =>
        dispatch({
            type: actions.APP__FINISH_UPDATING_DOCUMENT,
            id,
        });
}

function onOpenPopup() {
    return {
        type: actions.APP__SET_ANY_POPUP_VISIBLE,
        visible: true,
    };
}

function onClosePopup() {
    return {
        type: actions.APP__SET_ANY_POPUP_VISIBLE,
        visible: false,
    };
}

/**
 * @param {boolean} payload
 * @returns {{payload: boolean, type: string}}
 */
function setVisibleCompanyNamePopup(payload) {
    return {
        type: actions.APP__SET_COMPANY_NAME_POPUP_VISIBLE,
        payload,
    };
}

/**
 * @param {string} edrpou
 * @returns Promise<void>
 */
function syncRoles(edrpou) {
    return async (dispatch, getState) => {
        try {
            const { errors } = await syncUserRoles();

            await dispatch(onRefreshCurrentUser());

            const state = getState();
            const allActiveUserRoles = getAllActiveCompanyRoles(state);

            if (!allActiveUserRoles.size && !isEmptyObject(errors)) {
                const firstError = Object.entries(errors)[0];
                const edrpouFromError = firstError[0];
                const errorInfo = firstError[1];
                dispatch(
                    syncRolesErrorBannerActionsCreators.setDisplayStatusVisible(
                        {
                            edrpou: edrpouFromError,
                            errorInfo,
                        },
                    ),
                );
                return;
            }

            const roleForSetActive = allActiveUserRoles.find(
                (role) => role.company.edrpou === edrpou,
            );

            if (roleForSetActive?.id) {
                await auth.activateRole(roleForSetActive.id);
                // запамятовуємо сторінку яка має відкритися - це необхідно так як під час переключення ролі буде
                // здійснюватися forceRedirect /app/documents - і потім уже на цільову сторінку

                const url = new URL(window.location.href);
                url.searchParams.delete('edrpou');
                const nextUrl = url.pathname + url.search;

                await dispatch(onRefreshCurrentUser(true, nextUrl));
            }
        } catch (error) {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    title: t`Перевірка компанії`,
                    type: 'text',
                    textType: 'error',
                    text: t`Не вдалося синхронізувати компанії`,
                    autoClose: 5000,
                }),
            );
        }
    };
}

export default {
    onAlertOldBrowserClose,
    reloadFeatureFlags,
    onAlertPopupShow,
    onAlertPopupClose,
    onHideChangePasswordPopup,
    onMount,
    setCurrentUser,
    updateCurrentUser,
    updateCurrentUserState,
    updateCompanyState,
    updateCompanyConfigState,
    getCurrentCompanyRolesForConfig,
    onNewCompanyAgreed,
    onRefreshCurrentUser,
    onSignSessionCancel,
    onSignSessionFinish,
    onStartUserVerify,
    onStartUserVerifyUSB,
    onStartUserVerifyWidgetIit,
    onWarningPopupClose,
    startUpdatingDocument,
    finishUpdatingDocument,
    // Confirm popup
    onConfirmPopupShow,
    onConfirmPopupClose,
    updateBillingAccounts,

    // Document fields
    onFetchCompanyDocumentFields,

    // Elastic
    showESUploadProgress,

    onOpenPopup,
    onClosePopup,

    // Akurata project
    setVisibleCompanyNamePopup,

    syncRoles,
};
