import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import cn from 'classnames';
import KepVerification from 'components/KepSigner/KepVerification';
import Popup from 'components/ui/popup/popup';
import {
    getAppFlags,
    getCurrentCompanyIsFop,
    getIsCurrentCompanyTov,
} from 'selectors/app.selectors';
import { t } from 'ttag';

import { enrichUrlWithUTM } from '../../components/AllProjectsWidget/utils';

import Button from '../ui/button/button';
import Icon from '../ui/icon/icon';

import { openInNewTab } from '../../lib/navigation';
import eventTracking from '../../services/analytics/eventTracking';

import KEPAdvertisingBannerSvg from './images/KEPAdvertisingBanner.svg';
import ArrowSvg from './images/arrow.svg';
import RaySvg from './images/rays.svg';

import css from './KEPAdvertisingBanner.css';

const KEPAdvertisingBanner = () => {
    const companyIsFOP = useSelector(getCurrentCompanyIsFop);
    const isCurrentCompanyTov = useSelector(getIsCurrentCompanyTov);
    const [
        KEPVerificationPopupActive,
        SetKEPVerificationPopupActive,
    ] = useState<boolean>(false);
    const features = useSelector(getAppFlags);

    useEffect(() => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'shown',
            campaign: 'kep_rates',
        });
    }, []);

    const trackNavigate = () => {
        eventTracking.sendEvent('kepAdvertisingBanner', 'click', 'kep');
        eventTracking.sendPromoBannerToGTMV4({
            action: 'click',
            campaign: 'kep_rates',
        });
        openInNewTab('https://cap.vchasno.com.ua');
    };

    const onClickKEPButton = async () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'click',
            campaign: 'kep_rates',
        });
        if (
            (features['ENABLE_FOP_NEW_KEP_KEY_REGISTRATION'] && companyIsFOP) ||
            (features['ENABLE_TOV_NEW_KEP_KEY_REGISTRATION'] &&
                isCurrentCompanyTov)
        ) {
            SetKEPVerificationPopupActive(true);
        } else {
            eventTracking.sendEvent('kepAdvertisingBanner', 'click', 'kep');
            openInNewTab('https://cap.vchasno.ua/app');
        }
    };

    const handleClosePopup = () => {
        SetKEPVerificationPopupActive(false);
    };

    return (
        <div className={css.container}>
            <div className={css.contentContainer}>
                <p className={css.title}>
                    {t`Підписуй документи просто з «Вчасно.КЕП»`}
                </p>
                <p
                    className={css.description}
                >{t`Замовляйте захищені хмарні електронні ключі від кваліфікованого надавача електронних довірчих послуг «Вчасно Сервіс»`}</p>
                <div className={css.buttonsContainer}>
                    <Button className={css.button} onClick={onClickKEPButton}>
                        {t`Замовити Вчасно.КЕП`}
                        <Icon
                            className={cn(css.rayIcon, css.disableOnMobile)}
                            glyph={RaySvg}
                        />
                        <Icon
                            className={cn(
                                css.reverseRayIcon,
                                css.disableOnMobile,
                            )}
                            glyph={RaySvg}
                        />
                    </Button>
                    <a
                        href={enrichUrlWithUTM(config.KEP_LANDING)}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={trackNavigate}
                        className={css.getMoreLink}
                    >
                        <span>{t`Дізнатися більше`}</span>
                        <Icon className={css.arrowIcon} glyph={ArrowSvg} />
                    </a>
                </div>
            </div>
            <div className={css.iconContainer}>
                <Icon className={css.icon} glyph={KEPAdvertisingBannerSvg} />
            </div>
            <Popup
                title={t`Підтвердження КЕП/ЕЦП`}
                className={css.popupVerification}
                active={KEPVerificationPopupActive}
                onClose={handleClosePopup}
            >
                <KepVerification />
            </Popup>
        </div>
    );
};

export default KEPAdvertisingBanner;
