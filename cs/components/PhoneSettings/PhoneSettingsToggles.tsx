import React from 'react';
import { useSelector } from 'react-redux';

import { useMutation } from '@tanstack/react-query';
import { Alert, FlexBox, Switch, Text, Title } from '@vchasno/ui-kit';

import { getCurrentUser } from 'selectors/app.selectors';
import * as authService from 'services/auth';
import * as userService from 'services/user';
import { t } from 'ttag';
import { ApiError } from 'types/request';

import { useUpdateUserState } from './hooks';

/**
 * Перемикачі, щоб включити 2FA та вхід по телефону
 */
export function PhoneSettingsToggles() {
    const updateUserState = useUpdateUserState();
    const user = useSelector(getCurrentUser);
    const is2faEnabled = Boolean(user.is2FAEnabledInProfile);
    const isPhoneAuthEnabled = Boolean(user.authPhone);

    const update2fa = useMutation<void, ApiError, boolean>({
        mutationFn: async (enabled) => {
            await userService.update2FAState(enabled);
        },
        onMutate: (): void => {
            updatePhoneAuth.reset();
        },
        onSuccess: (_, enabled) => {
            updateUserState({ is2FAEnabledInProfile: enabled });
        },
    });

    const updatePhoneAuth = useMutation<void, ApiError, boolean>({
        mutationFn: async (enabled) => {
            await authService.updatePhoneAuth({ enable: enabled });
        },
        onMutate: (): void => {
            update2fa.reset();
        },
        onSuccess: (_, enabled) => {
            // authPhone завжди рівний user.phone, коли включено вхід по телефону
            updateUserState({ authPhone: enabled ? user.phone : null });
        },
    });

    return (
        <FlexBox direction="column" gap={32}>
            <PhoneSettingsToggle
                key="phoneAuthToggle"
                label={t`Логін / вхід у систему`}
                hint={t`Номер має бути унікальним і використовуватися тільки для
                    одного облікового запису`}
                onChange={(e) => updatePhoneAuth.mutate(e.target.checked)}
                value={isPhoneAuthEnabled}
                error={updatePhoneAuth.error?.reason}
            />
            <PhoneSettingsToggle
                key="twoFactorToggle"
                label={t`Двофакторна аутентифікація`}
                hint={t`Другий фактор авторизації ви можете налаштувати за
                    будь-яким номером телефону.`}
                onChange={(e) => update2fa.mutate(e.target.checked)}
                value={is2faEnabled}
                error={update2fa.error?.reason}
            />
        </FlexBox>
    );
}

function PhoneSettingsToggle(props: {
    label: string;
    hint: string;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    value: boolean;
    error?: string;
}) {
    return (
        <FlexBox direction="column">
            <FlexBox direction="row" justify="space-between" align="center">
                <Title level={4}>{props.label}</Title>
                <Switch
                    value={props.value}
                    size="sm"
                    onChange={props.onChange}
                />
            </FlexBox>
            <Text type="secondary">{props.hint}</Text>
            {props.error && <Alert type="error">{props.error}</Alert>}
        </FlexBox>
    );
}
