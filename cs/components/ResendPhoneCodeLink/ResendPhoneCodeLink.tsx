import React, { useState } from 'react';

import { useMutation } from '@tanstack/react-query';
import { FlexBox, Paragraph } from '@vchasno/ui-kit';
import Alert from '@vchasno/ui-kit/dist/components/Alert/Alert';

import { useInterval } from 'lib/reactHelpers/hooks';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

const trackEvent = (category?: string, action?: string, label?: string) => {
    if (!category || !action) {
        return;
    }
    eventTracking.sendEvent(category, action, label);
};

export const useResendPhoneCodeLink = (options: {
    resend: () => Promise<void>;
    retryAfterSeconds: number;
    eventCategory?: string;
    eventAction?: string;
}) => {
    const [timer, setTimer] = useState<number>(0);

    const { mutateAsync, isSuccess, isLoading } = useMutation({
        mutationFn: () => options.resend(),
        onSuccess: () => {
            trackEvent(options.eventCategory, options.eventAction, 'ok');
            setTimer(options.retryAfterSeconds);
        },
        onError: (error: any) => {
            const lockTime = error?.details?.lock_time;
            if (lockTime) {
                setTimer(lockTime);
                return;
            }
            trackEvent(
                options.eventCategory,
                options.eventAction,
                error.message,
            );
        },
    });

    // Якщо прийшла відповідь від серверу, що треба зачекати, то ми запускаємо
    // лічильник, який кожну секунду буде зменшувати таймер до 0.
    useInterval(
        () => {
            timer && setTimer(Math.max(timer - 1, 0));
        },
        timer ? 1_000 : null,
    );

    return {
        disabled: timer > 0 || isLoading,
        isSent: isSuccess,
        timer,
        onClick: mutateAsync,
    };
};

export const ResendPhoneCodeAlert = (props: {
    timer: number;
    isSent: boolean;
    sentMessage?: string;
}) => {
    if (props.timer <= 0) return null;
    return (
        <Alert type="info">
            <FlexBox direction="column" gap={8}>
                {props.isSent && (
                    <Paragraph style={{ fontWeight: '500' }}>
                        {props.sentMessage ||
                            t`Повідомлення з кодом було надіслано повторно.`}
                    </Paragraph>
                )}
                <Paragraph>{t`Щоб надіслати ще раз, зачекайте ${props.timer} с.`}</Paragraph>
            </FlexBox>
        </Alert>
    );
};
