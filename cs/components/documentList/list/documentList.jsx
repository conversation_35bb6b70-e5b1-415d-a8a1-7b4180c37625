import React from 'react';
import { connect } from 'react-redux';
import MediaQuery from 'react-responsive';

import { FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import { ArchiveBanner } from 'components/ArchiveBanner';
import ExpiresFreeRateBanner from 'components/ExpiresFreeRateBanner';
import expiresFreeRateBannerActionCreators from 'components/ExpiresFreeRateBanner/ExpiresFreeRateBannerActionCreators';
import RegisterUploadButton from 'components/RegisterUploadButton';
import DirectoryBreadcrumbs from 'components/documentList/DirectoryBreadcrumbs';
import { MEDIA_WIDTH } from 'lib/constants';
import PropTypes from 'prop-types';
import {
    getCompanyConfigSettingsMaxVisibleDocumentsCount,
    getIsCompanyHasActivePayedWebRates,
    getIsCompanyHasOnlyFreeRate,
    getIsFreeRateDocumentsTerminate,
    getUsedDocumentsCount,
    isAdminSelector,
} from 'selectors/app.selectors';
import { getIsArchiveBannerActive } from 'selectors/archiveBanner.selectors';
import { getWebTrialRatesForSettings } from 'selectors/companyCard.selectors';
import { getIsExpiresFreeRateBannerActive } from 'selectors/expiresFreeRateBanner.selectors';
import { getIsArchivePage } from 'selectors/router.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { setArchiveBannerOpen } from 'store/archiveBannerSlice';
import { t } from 'ttag';

import { getIsArchiveBannerClosedDaysAgo } from '../utils';

import Dropdown from '../../ui/dropdown/dropdown';
import PseudoLink from '../../ui/pseudolink/pseudolink';

import DocumentListFilterButton from '../../DocumentListFilterButton';
import WaitYourReviewBlock from '../../DocumentListFilterSidebar/components/WaitYourReviewBlock';
import WaitYourSignBlock from '../../DocumentListFilterSidebar/components/WaitYourSignBlock';
import DocumentListResetFiltersTooltip from '../../DocumentListResetFiltersTooltip';
import FiltersPopupContainer from '../../filtersPopup/filtersPopupContainer';
import ListFooter from '../../listFooter/listFooter';
import PageTitle from '../../pageTitle/pageTitle';
import SearchBarQuery from '../../searchBarQuery/searchBarQuery';
import constants, { AVERAGE_MONTH_DAYS, WEEK_DAYS } from '../constants';
import Header from '../header/documentListHeader';

// styles
import css from './documentList.css';

const CommentPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "CommentPopup" */ '../../commentPopup/commentPopup'
    ),
);

const GridTable = React.lazy(() =>
    import(/* webpackChunkName: "GridTable" */ '../GridTable'),
);

const ArchiveGridTable = React.lazy(() =>
    import(/* webpackChunkName: "ArchiveGridTable" */ '../ArchiveGridTable'),
);

const CreateFlowPopup = React.lazy(() =>
    import(
        /* webpackChunkName: "CreateFlowPopup" */ '../../createFlowPopup/createFlowPopup'
    ),
);

const BODY_TOOLBAR_STICKY_CLASS_NAME = 'ag-grid-toolbar-sticky';

class DocumentList extends React.Component {
    componentDidUpdate(prevProps) {
        if (this.props.documents !== prevProps.documents) {
            if (this.getSelectedDocuments().length) {
                if (
                    !document.body.classList.contains(
                        BODY_TOOLBAR_STICKY_CLASS_NAME,
                    )
                ) {
                    document.body.classList.add(BODY_TOOLBAR_STICKY_CLASS_NAME);
                }
            } else if (
                document.body.classList.contains(BODY_TOOLBAR_STICKY_CLASS_NAME)
            ) {
                document.body.classList.remove(BODY_TOOLBAR_STICKY_CLASS_NAME);
            }
        }

        if (
            this.props.usedDocumentCount >
                this.props.maxVisibleDocumentsCount &&
            !this.props.webTrialRateActive?.length
        ) {
            this.props.setArchiveBannerOpen({ isArchiveBannerOpen: true });
        } else {
            this.props.setArchiveBannerOpen({ isArchiveBannerOpen: false });
        }

        if (
            this.props.isFreeRateDocumentsTerminate &&
            this.props.isCompanyHasOnlyFreeRate &&
            this.props.isAdmin
        ) {
            this.props.setExpiresFreeRateBannerActive();
        }
    }

    componentWillUnmount() {
        if (document.body.classList.contains(BODY_TOOLBAR_STICKY_CLASS_NAME)) {
            document.body.classList.remove(BODY_TOOLBAR_STICKY_CLASS_NAME);
        }
    }

    getSelectedDocuments = () =>
        this.props.documents.filter((doc) => doc.selected);

    getSelectedDirectories = () =>
        this.props.directories.filter((directory) => directory.selected);

    render() {
        const selectedDocuments = this.getSelectedDocuments();
        const selectedDirectories = this.getSelectedDirectories();
        const selectedDocumentsCount = selectedDocuments.length;
        const selectedDirectoriesCount = selectedDirectories.length;
        const selectedCount = selectedDocumentsCount + selectedDirectoriesCount;
        const documentsPerPageOptions = constants.DOCUMENTS_PER_PAGE_OPTIONS.map(
            (amount) => {
                return { value: amount, label: amount.toString() };
            },
        );
        const showMultiSelectPseudolink =
            !this.props.isSelectedAll &&
            this.props.selectAllDocumentsText &&
            this.props.documentsCount !== selectedDocumentsCount;
        const openedCommentsPopupDocument = this.props.documents.find(
            (doc) => this.props.activeDocumentCommentsId === doc.id,
        );

        const isArchiveBannerClosedTimeAgo = this.props
            .isCompanyHasActivePayedWebRate
            ? getIsArchiveBannerClosedDaysAgo(AVERAGE_MONTH_DAYS)
            : getIsArchiveBannerClosedDaysAgo(WEEK_DAYS);

        const isArchiveBannerEnabled =
            this.props.featureFlags.ARCHIVE_PURCHASE_IMPROVEMENT &&
            this.props.isArchiveBannerOpen &&
            isArchiveBannerClosedTimeAgo;

        return (
            <div>
                <PageTitle />
                {this.props.isArchivePage && (
                    <div className={css.breadcrumbs}>
                        <DirectoryBreadcrumbs />
                    </div>
                )}
                <MediaQuery maxWidth={MEDIA_WIDTH.normal}>
                    {this.props.isArchivePage && (
                        <FiltersPopupContainer
                            sortButtons={constants.SORT_DATA}
                        />
                    )}
                    <div className={css.search} key="search">
                        <SearchBarQuery />
                    </div>
                </MediaQuery>
                <MediaQuery minWidth={MEDIA_WIDTH.normal + 1}>
                    <FlexBox
                        className={css.controls}
                        justify="space-between"
                        align="center"
                        key="buttons"
                    >
                        <FlexBox>
                            <DocumentListResetFiltersTooltip>
                                <DocumentListFilterButton />
                            </DocumentListResetFiltersTooltip>
                            <Dropdown
                                className={css.dateDropdown}
                                size="big"
                                options={constants.SORT_DATA.map(
                                    ({ text, value }) => ({
                                        value,
                                        label: text,
                                    }),
                                )}
                                value={this.props.dateSortType}
                                onChange={this.props.onSortDocumentsByDateType}
                            />
                        </FlexBox>
                        {!this.props.isArchivePage && (
                            <FlexBox
                                align="center"
                                justify="space-between"
                                grow={1}
                            >
                                <div className={css.filters}>
                                    <WaitYourSignBlock
                                        onChange={() => {
                                            eventTracking.sendToGTM({
                                                event:
                                                    'waiting_for_signature_click',
                                            });
                                        }}
                                    />
                                    <WaitYourReviewBlock
                                        onChange={() => {
                                            eventTracking.sendToGTM({
                                                event:
                                                    'waiting_for_approval_click',
                                            });
                                        }}
                                    />
                                </div>
                            </FlexBox>
                        )}
                        <RegisterUploadButton />
                    </FlexBox>
                    {this.props.isExpiresFreeRateBannerActive && (
                        <ExpiresFreeRateBanner />
                    )}
                    {isArchiveBannerEnabled &&
                        !this.props.isExpiresFreeRateBannerActive && (
                            <ArchiveBanner
                                usedDocumentCount={this.props.usedDocumentCount}
                                maxVisibleDocumentsCount={
                                    this.props.maxVisibleDocumentsCount
                                }
                            />
                        )}
                </MediaQuery>
                <div
                    key="header"
                    className={cn(css.headerBar, {
                        [css.expanded]: selectedCount > 0,
                    })}
                >
                    <Header
                        totalDocumentsCount={this.props.documentsCount}
                        showSelectionCheckbox={false}
                        isSelectedAllByFilter={this.props.isSelectedAll}
                        isSelectedAllOnPage={this.props.isSelectedAllOnPage}
                        isAddCommentsPopupOpened={
                            this.props.isAddCommentsPopupOpened
                        }
                        isRejectPopupOpened={this.props.isRejectPopupOpened}
                        isReviewPopupOpened={this.props.isReviewPopupOpened}
                        isChangeRecipientPopupOpened={
                            this.props.isChangeRecipientPopupOpened
                        }
                        isSignSummaryLoading={this.props.isSignSummaryLoading}
                        isHeaderFixed={this.props.isHeaderFixed}
                        isDeletePopupOpened={this.props.isDeletePopupOpened}
                        isSendPopupOpened={this.props.isSendPopupOpened}
                        isOverlimit={this.props.isOverlimit}
                        currentRoleId={this.props.currentUser.currentRole.id}
                        documentActions={this.props.documentActions}
                        reviewResults={this.props.reviewResults}
                        selectedDocuments={selectedDocuments}
                        selectedDirectories={selectedDirectories}
                        sentResults={this.props.sentResults}
                        tableSettings={this.props.tableSettings}
                        columns={this.props.columns}
                        selectedCount={selectedCount}
                        sendPopupStatus={this.props.sendPopupStatus}
                        errorMessage={this.props.errorMessage}
                        sortType={this.props.sortType}
                        filterBy={this.props.filterBy}
                        sentDocCount={this.props.sentDocCount}
                        onSelectAll={this.props.onSelectAllDocumentsOnPage}
                        onDeselectAll={this.props.onDeselectAllDocuments}
                        onDocumentLinksUpdate={this.props.onDocumentLinksUpdate}
                        onSendDocuments={this.props.onSendDocuments}
                        onSign={this.props.onSignDocuments}
                        onShowComments={this.props.onShowComments}
                        onReject={this.props.onRejectDocuments}
                        onReviewDocuments={this.props.onReviewDocuments}
                        onDeleteDocuments={this.props.onDeleteDocuments}
                        onRecipientChange={this.props.onRecipientChange}
                        onCommentDocuments={this.props.onCommentDocuments}
                        onOpenPopup={this.props.onOpenPopup}
                        onClosePopup={this.props.onClosePopup}
                        onDownloadSignSummary={this.props.onDownloadSignSummary}
                        onSortDocumentsByProperty={
                            this.props.onSortDocumentsByProperty
                        }
                        onClearSortingByProperty={
                            this.props.onClearSortingByProperty
                        }
                        onClearDocumentsFilteringByProperty={
                            this.props.onClearDocumentsFilteringByProperty
                        }
                        onChangeTableSettings={this.props.onChangeTableSettings}
                        onMultiDownloadDocuments={
                            this.props.onMultiDownloadDocuments
                        }
                        onOpenChangeDocumentPopup={
                            this.props.onOpenChangeDocumentPopup
                        }
                        onOpenTagsEditPopup={this.props.onOpenTagsEditPopup}
                        onOpenCreateDeleteRequestPopup={
                            this.props.onOpenCreateDeleteRequestPopup
                        }
                        onOpenResolveDeleteRequestPopup={
                            this.props.onOpenResolveDeleteRequestPopup
                        }
                        onFindEmailClick={this.props.onFindEmailClick}
                        onCheckReviewAbility={this.props.onCheckReviewAbility}
                    />
                </div>
                <div
                    key="counterBlock"
                    className={cn(css.counterBlock, {
                        [css.counterBlockExpanded]: selectedCount > 0,
                    })}
                >
                    {this.props.isSelectedAll ? (
                        <div className={css.counter}>
                            {this.props.selectedAllDocumentsText}:{' '}
                            <b>{this.props.documentsCount}</b>
                        </div>
                    ) : (
                        <div className={css.counter}>
                            {!!selectedDocumentsCount && (
                                <>
                                    {t`Обрано документів:`}{' '}
                                    <b>{selectedDocumentsCount}</b>
                                </>
                            )}
                            {!!selectedDocumentsCount &&
                                !!selectedDirectoriesCount && <span>, </span>}
                            {!!selectedDirectoriesCount && (
                                <>
                                    {t`Обрано папок:`}{' '}
                                    <b>{selectedDirectoriesCount}</b>
                                </>
                            )}
                        </div>
                    )}
                    {showMultiSelectPseudolink && (
                        <div className={css.counterAll}>
                            <PseudoLink
                                onClick={this.props.onSelectAllDocuments}
                            >
                                {this.props.selectAllDocumentsText}:{' '}
                                <b>{this.props.documentsCount}</b>
                            </PseudoLink>
                        </div>
                    )}
                </div>
                <React.Suspense fallback={null}>
                    {this.props.isArchivePage && <ArchiveGridTable />}
                    {!this.props.isArchivePage && <GridTable />}
                </React.Suspense>
                <ListFooter
                    key="ListFooter"
                    total={this.props.documentsCount}
                    currentPage={this.props.currentPage}
                    itemsPerPage={this.props.documentsPerPage}
                    hasNextPage={this.props.hasNextPage}
                    useSimplePagination={this.props.useSimplePagination}
                    onPageCounterClick={this.props.onPageCounterClick}
                    documentsPerPageOptions={documentsPerPageOptions}
                    onDocumentsPerPageChange={
                        this.props.onDocumentsPerPageChange
                    }
                />
                <React.Suspense fallback={null}>
                    {this.props.isCommentsListPopupOpened && (
                        <CommentPopup
                            comments={this.props.commentsList}
                            commentAdded
                            active={this.props.isCommentsListPopupOpened}
                            docs={
                                openedCommentsPopupDocument
                                    ? [openedCommentsPopupDocument]
                                    : selectedDocuments
                            }
                            analyticsLocation={'document_list'}
                            userEmail={this.props.currentUser.email}
                            onClose={() =>
                                this.props.onClosePopup('commentsList')
                            }
                            onCommentDocuments={this.props.onCommentDocuments}
                        />
                    )}
                    <CreateFlowPopup
                        isActive={this.props.isAddRecipientsPopupOpened}
                        docs={selectedDocuments}
                        onSaveCallback={() =>
                            this.props.onClosePopup('addRecipients')
                        }
                        onCancel={() =>
                            this.props.onClosePopup('addRecipients')
                        }
                        onSubmitCallback={() =>
                            this.props.onClosePopup('addRecipients', true)
                        }
                    />
                </React.Suspense>
            </div>
        );
    }
}

DocumentList.propTypes = {
    isSelectedAll: PropTypes.bool,
    isSelectedAllOnPage: PropTypes.bool,
    isAddCommentsPopupOpened: PropTypes.bool,
    isAddRecipientsPopupOpened: PropTypes.bool,
    isRejectPopupOpened: PropTypes.bool,
    isReviewPopupOpened: PropTypes.bool,
    isHeaderFixed: PropTypes.bool,
    isDeletePopupOpened: PropTypes.bool,
    isChangeRecipientPopupOpened: PropTypes.bool,
    isSendPopupOpened: PropTypes.bool,
    isOverlimit: PropTypes.bool,
    isCommentsListPopupOpened: PropTypes.bool,
    isSignSummaryLoading: PropTypes.bool,
    isCompanyHasActivePayedWebRate: PropTypes.bool,
    isFreeRateDocumentsTerminate: PropTypes.bool,
    isCompanyHasOnlyFreeRate: PropTypes.bool,
    isAdmin: PropTypes.bool,

    currentUser: PropTypes.object,
    documentActions: PropTypes.object.isRequired,
    unsuccessfullySignedDocuments: PropTypes.object,
    sortType: PropTypes.object,
    filterBy: PropTypes.object,

    commentsList: PropTypes.array,
    documents: PropTypes.array.isRequired,
    directories: PropTypes.array.isRequired,
    reviewResults: PropTypes.array.isRequired,
    sentResults: PropTypes.array.isRequired,
    tableSettings: PropTypes.array.isRequired,
    columns: PropTypes.array.isRequired,

    documentsPerPage: PropTypes.number,
    documentsCount: PropTypes.number,
    usedDocumentCount: PropTypes.number,
    currentPage: PropTypes.number.isRequired,
    sentDocCount: PropTypes.number.isRequired,

    hasNextPage: PropTypes.bool,
    useSimplePagination: PropTypes.bool,

    documentTitle: PropTypes.string,
    dateSortType: PropTypes.string,
    sendPopupStatus: PropTypes.string.isRequired,
    selectAllDocumentsText: PropTypes.string.isRequired,
    selectedAllDocumentsText: PropTypes.string.isRequired,
    errorMessage: PropTypes.string,
    activeDocumentCommentsId: PropTypes.string,

    onDownloadSignSummary: PropTypes.func.isRequired,
    onSelectAllDocuments: PropTypes.func.isRequired,
    onSelectAllDocumentsOnPage: PropTypes.func.isRequired,
    onDeselectAllDocuments: PropTypes.func.isRequired,
    onDocumentLinksUpdate: PropTypes.func.isRequired,
    onSelectDocument: PropTypes.func.isRequired,
    onDeselectDocument: PropTypes.func.isRequired,
    onSendDocuments: PropTypes.func.isRequired,
    onSignDocuments: PropTypes.func.isRequired,
    onRejectDocuments: PropTypes.func.isRequired,
    onReviewDocuments: PropTypes.func.isRequired,
    onDeleteDocuments: PropTypes.func.isRequired,
    onPageCounterClick: PropTypes.func.isRequired,
    onDocumentsPerPageChange: PropTypes.func.isRequired,
    onRecipientChange: PropTypes.func.isRequired,
    onCommentDocuments: PropTypes.func.isRequired,
    onSortDocumentsByDateType: PropTypes.func.isRequired,
    onToggleFixHeader: PropTypes.func.isRequired,
    onOpenPopup: PropTypes.func.isRequired,
    onClosePopup: PropTypes.func.isRequired,
    onLoadDocuments: PropTypes.func.isRequired,
    onSortDocumentsByProperty: PropTypes.func.isRequired,
    onClearSortingByProperty: PropTypes.func.isRequired,
    onClearDocumentsFilteringByProperty: PropTypes.func.isRequired,
    onShowComments: PropTypes.func.isRequired,
    onChangeTableSettings: PropTypes.func.isRequired,
    onMultiDownloadDocuments: PropTypes.func.isRequired,
    onOpenChangeDocumentPopup: PropTypes.func.isRequired,
    onOpenTagsEditPopup: PropTypes.func.isRequired,
    onOpenCreateDeleteRequestPopup: PropTypes.func.isRequired,
    onOpenResolveDeleteRequestPopup: PropTypes.func.isRequired,
    onFindEmailClick: PropTypes.func.isRequired,
    onCheckReviewAbility: PropTypes.func.isRequired,
    isArchivePage: PropTypes.bool,

    documentFields: PropTypes.array.isRequired,
    featureFlags: PropTypes.object.isRequired,

    maxVisibleDocumentsCount: PropTypes.number,
    isArchiveBannerOpen: PropTypes.bool,
    setArchiveBannerOpen: PropTypes.func,
    webTrialRateActive: PropTypes.array,
};

const mapDispatchToProps = (dispatch) => ({
    setArchiveBannerOpen: (payload) => dispatch(setArchiveBannerOpen(payload)),
    setExpiresFreeRateBannerActive: () =>
        dispatch(expiresFreeRateBannerActionCreators.onShow()),
});

const mapStateToProps = (state) => ({
    maxVisibleDocumentsCount: getCompanyConfigSettingsMaxVisibleDocumentsCount(
        state,
    ),
    isArchiveBannerOpen: getIsArchiveBannerActive(state),
    isArchivePage: getIsArchivePage(state),
    usedDocumentCount: getUsedDocumentsCount(state),
    webTrialRateActive: getWebTrialRatesForSettings(state),
    isCompanyHasActivePayedWebRate: getIsCompanyHasActivePayedWebRates(state),
    isFreeRateDocumentsTerminate: getIsFreeRateDocumentsTerminate(state),
    isCompanyHasOnlyFreeRate: getIsCompanyHasOnlyFreeRate(state),
    isExpiresFreeRateBannerActive: getIsExpiresFreeRateBannerActive(state),
    isAdmin: isAdminSelector(state),
});
export default connect(mapStateToProps, mapDispatchToProps)(DocumentList);
