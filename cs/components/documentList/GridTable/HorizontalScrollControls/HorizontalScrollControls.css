.collapseToggleBtn {
    --grid-collapse-toggle-btn-transition-delay: 300ms;

    position: absolute;
    z-index: 2;
    top: 11px;
    left: 0;
    display: flex;
    width: 26px;
    height: 26px;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: 2px;
    color: #9aaabf;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--grid-collapse-toggle-btn-transition-delay), width var(--grid-collapse-toggle-btn-transition-delay), color var(--grid-collapse-toggle-btn-transition-delay), background-color var(--grid-collapse-toggle-btn-transition-delay), transform var(--grid-collapse-toggle-btn-transition-delay);
}

.collapseToggleBtn:hover {
    background-color: var(--default-border);
    color: #fff;
    cursor: pointer;
}

.collapseToggleBtn svg {
    width: 20px;
    height: 20px;
    transform: rotate(180deg);
    transform-origin: center;
    transition: transform var(--grid-collapse-toggle-btn-transition-delay);
}

.collapseToggleBtn.btnAlt svg {
    transform: rotate(0deg);
}
