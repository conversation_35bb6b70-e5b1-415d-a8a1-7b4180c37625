import React, { useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';

import { GridApi } from '@ag-grid-community/core';

import cn from 'classnames';

import { getInvisibleColIds } from '../utils';

import Icon from '../../../ui/icon/icon';

import ArrowLeftSvg from './icons/arrowLeft.svg';

import css from './HorizontalScrollControls.css';

export interface HorizontalScrollControlsProps {
    api: GridApi;
}

const DELAY_TO_SHOW_CONTROLS = 300;

const btnStyles = {
    '--grid-collapse-toggle-btn-transition-delay': `${DELAY_TO_SHOW_CONTROLS}ms`,
} as React.CSSProperties;

const HorizontalScrollControls: React.FC<
    React.PropsWithChildren<HorizontalScrollControlsProps>
> = ({ api }) => {
    const headerViewportRef = useRef<Nullable<HTMLDivElement>>(null);
    const leftBtnRef = useRef<Nullable<HTMLDivElement>>(null);
    const rightBtnRef = useRef<Nullable<HTMLDivElement>>(null);
    if (!document.body.contains(headerViewportRef.current)) {
        headerViewportRef.current = document.querySelector(
            '.ag-header-viewport',
        );
    }

    useEffect(() => {
        let timeout: ReturnType<typeof setTimeout>;

        const handleScroll = () => {
            if (timeout) {
                clearTimeout(timeout);
            }
            const { left, right } = api.getHorizontalPixelRange();

            if (leftBtnRef.current) {
                leftBtnRef.current.style.transform = `translateX(${left}px)`;
                leftBtnRef.current.style.visibility = 'hidden';
            }

            if (rightBtnRef.current) {
                rightBtnRef.current.style.transform = `translateX(${
                    right - 26
                }px)`;
                rightBtnRef.current.style.visibility = 'hidden';
            }

            timeout = setTimeout(() => {
                if (leftBtnRef.current) {
                    leftBtnRef.current.style.visibility = 'visible';
                }
                if (rightBtnRef.current) {
                    rightBtnRef.current.style.visibility = 'visible';
                }
            }, DELAY_TO_SHOW_CONTROLS);
        };

        handleScroll();

        api.addEventListener('columnMoved', handleScroll);
        api.addEventListener('columnResized', handleScroll);
        api.addEventListener('columnPinned', handleScroll);
        api.addEventListener('bodyScroll', handleScroll);
        window.addEventListener('resize', handleScroll);

        return () => {
            api.removeEventListener('columnMoved', handleScroll);
            api.removeEventListener('columnResized', handleScroll);
            api.removeEventListener('columnPinned', handleScroll);
            api.removeEventListener('bodyScroll', handleScroll);
            window.removeEventListener('resize', handleScroll);
            clearTimeout(timeout);
        };
    }, []);

    return (
        <>
            {headerViewportRef.current &&
                ReactDOM.createPortal(
                    <>
                        <div
                            ref={leftBtnRef}
                            onClick={() => {
                                api.ensureColumnVisible(
                                    getInvisibleColIds(api).firstColId,
                                    'middle',
                                );
                            }}
                            className={cn(
                                css.collapseToggleBtn,
                                css.btnAlt,
                                'HorizontalScrollControls__left',
                            )}
                            style={btnStyles}
                        >
                            <Icon glyph={ArrowLeftSvg} />
                        </div>
                        <div
                            ref={rightBtnRef}
                            onClick={() => {
                                api.ensureColumnVisible(
                                    getInvisibleColIds(api).lastColId,
                                    'middle',
                                );
                            }}
                            className={cn(
                                css.collapseToggleBtn,
                                'HorizontalScrollControls__right',
                            )}
                            style={btnStyles}
                        >
                            <Icon glyph={ArrowLeftSvg} />
                        </div>
                    </>,
                    headerViewportRef.current,
                )}
        </>
    );
};

export default HorizontalScrollControls;
