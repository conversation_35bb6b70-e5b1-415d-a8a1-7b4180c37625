import React, { FC, useEffect, useRef, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useHistory } from 'react-router-dom';

import { yupResolver } from '@hookform/resolvers/yup';
import { Paragraph, Title } from '@vchasno/ui-kit';

import { useLoginSuccessEffect } from 'components/LoginForm/useLoginSuccessEffect';
import {
    ResendPhoneCodeAlert,
    useResendPhoneCodeLink,
} from 'components/ResendPhoneCodeLink/ResendPhoneCodeLink';
import PseudoLink from 'components/ui/pseudolink/pseudolink';
import { t } from 'ttag';
import * as yup from 'yup';

import { loadAuthFormStorageItem } from '../auth/utils';

import BackButton from '../ui/BackButton/BackButton';
import OtpInput, { OtpInputRef } from '../ui/OtpInput/OtpInput';
import Button from '../ui/button/button';

import { getLocationQuery, stringifyLocationQuery } from '../../lib/url';
import eventTracking from '../../services/analytics/eventTracking';
import auth from '../../services/auth';
import PartnerLogos from '../AuthLayout/PartnerLogos';
import FlexBox from '../FlexBox/FlexBox';
import { AUTH_ENTRYPOINT_PATH } from '../auth';
import { STATIC_ERROR_PHRASES } from '../auth/constants';
import PageTitle from '../pageTitle/pageTitle';
import { useRegistrationSuccessEffect } from '../registration/hooks/useRegistrationSuccessEffect';

import css from './AuthCheckPhoneCode.css';

interface OtpFormFields {
    code: string;
}

const otpSchema = yup.object().shape({
    code: yup
        .string()
        .required(STATIC_ERROR_PHRASES.REQUIRED_FIELD_ERROR)
        .matches(/^[0-9]{6}$/, t`Введіть 6‑значний код`),
});

const trackEvent = (action: string, label?: string) => {
    eventTracking.sendEvent('form-phone-auth', action, label);
};

const AuthCheckPhoneCode: FC = () => {
    const history = useHistory();
    const searchQuery = getLocationQuery(history.location);

    const [phoneNumber, setPhoneNumber] = useState('');
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const otpInputRef = useRef<OtpInputRef>(null);

    const registrationSuccessEffect = useRegistrationSuccessEffect();
    const loginSuccessEffect = useLoginSuccessEffect();

    const resendLink = useResendPhoneCodeLink({
        resend: async () => {
            await auth.sendPhoneAuthCode({
                phone: phoneNumber,
                // Коли перевідправляємо код, то використовуємо Viber, а потім SMS, замість
                // просто SMS як при першому надсиланні коду. Якщо SMS-ка не прийшла, то може
                // хоч Viber спрацює
                method: 'cascade',
            });
        },
        retryAfterSeconds: 60,
    });

    const {
        control,
        handleSubmit,
        setValue,
        watch,
        formState,
    } = useForm<OtpFormFields>({
        resolver: yupResolver(otpSchema),
        defaultValues: { code: '' },
    });

    const code = watch('code');

    useEffect(() => {
        trackEvent('render');
        const authData = loadAuthFormStorageItem();
        if (authData?.authType !== 'phone' || !authData?.login) {
            history.push({
                pathname: AUTH_ENTRYPOINT_PATH,
                search: stringifyLocationQuery(searchQuery),
            });
            return;
        }
        setPhoneNumber(authData.login);
    }, [history, searchQuery]);

    const handleCodeChange = (value: string) => {
        setErrorMessage(null);
        setValue('code', value);
    };

    const handleCodeComplete = (value: string) => {
        setValue('code', value);
        if (!formState.isSubmitting) {
            handleSubmit(onSubmit)();
        }
    };

    const onSubmit: SubmitHandler<OtpFormFields> = async ({
        code: submitCode,
    }) => {
        if (formState.isSubmitting) return;

        setErrorMessage(null);
        trackEvent('submit-otp');

        try {
            const response = await auth.processPhoneAuthCode({
                phone: phoneNumber,
                code: submitCode,
            });

            trackEvent('submit-otp-success');
            if (response.flow === 'registration') {
                await registrationSuccessEffect({
                    nextUrl: response.nextUrl,
                    login: phoneNumber,
                    registrationMethod: 'phone',
                });
            } else {
                loginSuccessEffect({
                    nextUrl: response.nextUrl,
                    is2FAEnabled: response.is2FAEnabled,
                    method: 'phone',
                });
            }
        } catch (error: any) {
            trackEvent('submit-otp-error', error.message);
            setErrorMessage(
                error.reason ||
                    error.message ||
                    STATIC_ERROR_PHRASES.COMMON_ERROR,
            );
            otpInputRef.current?.focus();
        }
    };

    const onBackHandler = () => {
        history.push({
            pathname: AUTH_ENTRYPOINT_PATH,
            search: stringifyLocationQuery(searchQuery),
        });
        trackEvent('click-back-from-otp');
    };

    return (
        <form
            id="phone-auth-form"
            onSubmit={handleSubmit(onSubmit)}
            style={{ height: '100%' }}
        >
            <PageTitle>{t`Вітаємо у Вчасно`}</PageTitle>
            <FlexBox className={css.container}>
                <FlexBox direction="column" gap={32} className={css.content}>
                    <BackButton
                        className={css.backBtn}
                        onClick={onBackHandler}
                    />
                    <PartnerLogos className={css.partnersLogos} />
                    <FlexBox direction="column" gap={12}>
                        <Title level={2}>{t`Введіть код з SMS`}</Title>
                        <Paragraph className={css.description}>
                            {t`Не отримали повідомлення? `}
                            <PseudoLink
                                className={css.resendLink}
                                onClick={() => resendLink.onClick()}
                                disabled={resendLink.disabled}
                            >{t`Надіслати повторно.`}</PseudoLink>{' '}
                            {t`Щоб змінити номер телефону поверніться назад.`}
                        </Paragraph>
                    </FlexBox>
                    <Controller
                        control={control}
                        name="code"
                        render={({ field }) => (
                            <div className={css.otpInputContainer}>
                                <OtpInput
                                    ref={otpInputRef}
                                    length={6}
                                    value={field.value}
                                    onChange={handleCodeChange}
                                    onComplete={handleCodeComplete}
                                    error={errorMessage}
                                    autoFocus
                                />
                            </div>
                        )}
                    />
                    <ResendPhoneCodeAlert
                        timer={resendLink.timer}
                        isSent={resendLink.isSent}
                        sentMessage={t`Ми повторно надіслали код Viber або SMS повідомленням.`}
                    />
                    <Button
                        className={css.submitBtn}
                        type="submit"
                        theme="darkGray"
                        isLoading={formState.isSubmitting}
                        disabled={
                            code.length < 6 ||
                            formState.isSubmitting ||
                            // this prevent resubmission before redirect page will be loaded
                            formState.isSubmitSuccessful
                        }
                    >
                        {t`Продовжити`}
                    </Button>
                </FlexBox>
            </FlexBox>
        </form>
    );
};

export default AuthCheckPhoneCode;
