import React from 'react';
import { connect } from 'react-redux';
import { useMediaQuery } from 'react-responsive';
import { Route, Switch } from 'react-router-dom';

import {
    DonateRatelMBanner,
    useDonateRatelMBannerContext,
} from '@vchasno/shared-components';

import { AlertLimitationEmployeesBanner } from 'components/AlertLimitationEmployeesBanner';
import { BusinessPaperlessBanner } from 'components/BusinessPaperlessBanner';
import DeminingBanner from 'components/DeminingBanner';
import DetectedInTimeBannerV2 from 'components/DetectedInTimeBannerV2';
import { useDetectedInTimeBannerV2Context } from 'components/DetectedInTimeBannerV2/context';
import DocumentListDropFilesArea from 'components/DocumentListDropFilesArea';
import { FeatureHide, FeatureShow } from 'components/FeatureDisplay';
import { HomeBanner } from 'components/HomeBanner';
import InternalDocumentsLimitedBanner from 'components/InternalDocumentsLimitedBanner';
import { useThemeContext } from 'contexts/theme';
import { MEDIA_WIDTH } from 'lib/constants';
import { ApplicationMode } from 'services/enums';
import { mapStateToApplicationMode, mapStateToCurrentUser } from 'store/utils';

import InfoBanner from '../infoBanner/infoBanner';

// styles
import css from './documents.css';

const Documents = (props) => {
    const {
        isClose: isClosedDonateRatelMBanner,
        setIsClose: setIsCloseDonateRatelMBanner,
    } = useDonateRatelMBannerContext();
    const {
        isClose: isCloseDetectedInTimeBannerV2,
    } = useDetectedInTimeBannerV2Context();
    const isMobile = useMediaQuery({ maxWidth: MEDIA_WIDTH.tablet });

    const { theme } = useThemeContext();
    const isSignSessionMode =
        props.applicationMode === ApplicationMode.SIGN_SESSION;

    /**
     * @type {React.CSSProperties}
     */
    const rootStyles = {};

    if (theme === 'autumn') {
        rootStyles.backgroundImage = `url("${config.STATIC_HOST}/images/theme/autumn/document-list-bg.png")`;
        rootStyles.backgroundRepeat = 'no-repeat';
        rootStyles.backgroundPosition = 'center bottom';
        rootStyles.backgroundSize = '100% auto';
        rootStyles.paddingBottom = '300px';
    }

    if (theme === 'spring') {
        rootStyles.backgroundImage = `url("${config.STATIC_HOST}/images/theme/spring/document-list-bg.png")`;
        rootStyles.backgroundRepeat = 'no-repeat';
        rootStyles.backgroundPosition = 'center bottom';
        rootStyles.backgroundSize = '100% auto';
        rootStyles.paddingBottom = '300px';
    }

    return (
        <div className={css.root} style={rootStyles}>
            <div
                className={isSignSessionMode ? css.contentMinimal : css.content}
            >
                <AlertLimitationEmployeesBanner />
                {/* Need to pass currentUser as a prop to update it after key confirmation */}
                <InfoBanner currentUser={props.currentUser} />
                <Switch>
                    <Route exact path="/app/documents">
                        <FeatureShow feature="DONATE_DETECTED_IN_TIME_BANNER_V2">
                            {!isCloseDetectedInTimeBannerV2 && (
                                <div className={css.donateBanner}>
                                    <DetectedInTimeBannerV2 />
                                </div>
                            )}
                        </FeatureShow>
                        <FeatureShow feature="DONATE_RATEL_M_BANNER">
                            {!isClosedDonateRatelMBanner && (
                                <div className={css.donateBanner}>
                                    <DonateRatelMBanner
                                        mobile={isMobile}
                                        onClose={setIsCloseDonateRatelMBanner}
                                    />
                                </div>
                            )}
                        </FeatureShow>
                        <FeatureShow feature="HOME_BANNER">
                            <HomeBanner />
                        </FeatureShow>
                        <BusinessPaperlessBanner />
                        <DeminingBanner />
                        <InternalDocumentsLimitedBanner />
                        <FeatureShow feature="ENABLE_NEW_UPLOAD_FLOW">
                            <DocumentListDropFilesArea className={css.wrapper}>
                                {props.children}
                            </DocumentListDropFilesArea>
                        </FeatureShow>
                        <FeatureHide feature="ENABLE_NEW_UPLOAD_FLOW">
                            <div className={css.wrapper}>{props.children}</div>
                        </FeatureHide>
                    </Route>
                    <Route path="*">
                        <div className={css.wrapper}>{props.children}</div>
                    </Route>
                </Switch>
            </div>
        </div>
    );
};

const mapStateToProps = (state) => ({
    ...mapStateToApplicationMode(state),
    ...mapStateToCurrentUser(state),
});

export default connect(mapStateToProps)(Documents);
