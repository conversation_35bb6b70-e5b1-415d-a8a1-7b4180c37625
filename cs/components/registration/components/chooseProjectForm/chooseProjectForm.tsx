import React, { FC, ReactElement, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import cn from 'classnames';
import { t } from 'ttag';

import { LandingProjectSource } from '../../../../services/enums';
// @ts-expect-error TS2305 [FIXME] Comment is autogenerated
import { ProjectsOption, sourceType } from './types';

import { getAppFlags } from '../../../../selectors/app.selectors';

import RadioButton from '../../../ui/RadioButton';
import Button from '../../../ui/button/button';

import { WS_KEY_REGISTRATION_SOURCE } from '../../../../lib/constants';
import { redirect } from '../../../../lib/navigation';
import {
    getLocalStorageItem,
    removeLocalStorageItem,
} from '../../../../lib/webStorage';
import eventTracking from '../../../../services/analytics/eventTracking';
import { setRegistrationSource } from '../../../../services/user';
import { VALID_PROJECT_SOURCES } from '../../constants';
import StepLabel from '../stepLabel/stepLabel';
import Title from '../title/title';
import { GTMEventsMap, PROJECTS_OPTIONS, SOURCE_LABELS } from './constants';

import css from './chooseProjectForm.css';

const trackEvent = (action: string, label?: string) => (
    eventCategory?: string,
    kasaEventCategory?: string,
) => {
    if (eventCategory) {
        eventTracking.sendEvent(eventCategory, action, label);
    }
    if (label === 'kasa' && kasaEventCategory) {
        eventTracking.sendEventKasa(kasaEventCategory, action, label);
    }
};

const sendEventToGTM = (projectSource: LandingProjectSource) => {
    eventTracking.sendToGTM({
        event: GTMEventsMap[projectSource],
        category: '',
        action: '',
        label: '',
    });
};

const ChooseProjectForm: FC<React.PropsWithChildren<unknown>> = () => {
    const flags = useSelector(getAppFlags);
    const [project, setProject] = useState('vchasno');
    const projects = useMemo(
        () =>
            PROJECTS_OPTIONS.filter(
                (item) =>
                    item.source !== 'ttn' ||
                    flags.ENABLE_TTN_SERVICE_REGISTRATION,
            ),
        [flags],
    );

    useEffect(() => {
        // Note that next useEffect will overwrite results of this one in case they collide
        const searchParams = new URLSearchParams(location.search);
        const source = searchParams.get('source');

        if (source) {
            setProject(source);
        }
    }, []);

    const activeOption: ProjectsOption =
        projects.find((o) => o.value === project) ?? projects[0];

    const getLabelForAnalytics = (option: ProjectsOption): string => {
        const source: sourceType = getLocalStorageItem(
            WS_KEY_REGISTRATION_SOURCE,
        );
        if (source && option.source !== source) {
            // @ts-expect-error TS7053 [FIXME] Comment is autogenerated
            return `from ${SOURCE_LABELS[source]} to ${
                SOURCE_LABELS[option.source]
            }`;
        }
        return 'unchanged';
    };

    const handleSubmit = async () => {
        trackEvent('project_change', getLabelForAnalytics(activeOption))(
            'registration_step_3',
            'registration_step_2',
        );
        trackEvent('choose-prod', activeOption.value)(
            'form-signup-new-step3-choose-prod',
            'form-signup-new-step2-choose-prod',
        );
        sendEventToGTM(activeOption.source as LandingProjectSource);
        removeLocalStorageItem(WS_KEY_REGISTRATION_SOURCE);
        await setRegistrationSource(activeOption.value);
        redirect(activeOption.redirect);
    };

    const renderOption = (option: ProjectsOption): ReactElement => {
        const isActive = option.value === project;

        return (
            <div
                key={`project-${option.value}`}
                data-option={option.value}
                className={cn(css.radioOption, {
                    [css.radioOptionActive]: isActive,
                })}
                data-qa={`qa_radio_button_${option.value}`}
                onClick={() => setProject(option.value)}
            >
                <div className={css.radioButton}>
                    {/*
                     // @ts-expect-error TS2741 [FIXME] Comment is autogenerated */}
                    <RadioButton value={option.value} checked={isActive} />
                </div>
                <div>
                    {option.logo ? (
                        <img
                            className={css.labelLogo}
                            src={option.logo}
                            alt={option.alt}
                        />
                    ) : (
                        <span className={css.optionName}>{option.alt}</span>
                    )}
                    <div>{option.label}</div>
                </div>
            </div>
        );
    };

    useEffect(() => {
        trackEvent('show')('registration_step_3');
        trackEvent('show')('form-signup-new-step3-choose-prod');
        const source = getLocalStorageItem(WS_KEY_REGISTRATION_SOURCE);
        if (VALID_PROJECT_SOURCES.some((item) => item === source)) {
            setProject(source);
        }
    }, []);

    return (
        <div className={css.root} data-qa="qa_step3_choice_project">
            <div className={css.sidePanel}>
                <StepLabel>{t`Крок 3/4`}</StepLabel>
                <Title>{t`Оберіть, яким продуктом бажаєте користуватись`}</Title>
                <div className={css.radioGroup}>
                    {projects.map(renderOption)}
                </div>
                <div className={css.button}>
                    <Button
                        theme="cta"
                        width="full"
                        onClick={handleSubmit}
                        dataQa="qa_submit_form"
                        className={activeOption.themeClassName}
                    >
                        {t`Далі`}
                    </Button>
                </div>
            </div>
            <div className={css.picture} data-option={activeOption.value}>
                {activeOption.picture && (
                    <img src={activeOption.picture} alt={activeOption.alt} />
                )}
                {activeOption.logoLarge && (
                    <img src={activeOption.logoLarge} alt={activeOption.alt} />
                )}
                <div>{activeOption.label}</div>
            </div>
        </div>
    );
};

export default ChooseProjectForm;
