import { useMemo } from 'react';
import { useDispatch } from 'react-redux';

import { CsatType } from 'services/scat';
import { openCsatModal } from 'store/csat';

interface CsatSurveyShowHandlerOptions {
    delaySec?: number;
}

export const useCsatSurveyShowHandlers = () => {
    const dispatch = useDispatch();
    const makeByType = (type: CsatType) => ({
        delaySec,
    }: CsatSurveyShowHandlerOptions = {}) => {
        if (delaySec && delaySec > 0) {
            setTimeout(() => {
                dispatch(openCsatModal({ type }));
            }, delaySec * 1000);
        } else {
            dispatch(openCsatModal({ type }));
        }
    };

    return useMemo(
        () => ({
            documentScanMetaRecognition: makeByType(
                'document_scans_meta_recognition_by_ai',
            ),
            uploadDocWithoutSigning: makeByType('upload_doc_without_signing'),
            uploadDocWithSigning: makeByType('upload_doc_with_signing'),
        }),
        [dispatch],
    );
};
