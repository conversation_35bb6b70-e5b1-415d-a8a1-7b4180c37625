import { useMutation } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { sendCsat } from 'services/scat';
import { t } from 'ttag';

export const useSendCsat = () => {
    return useMutation({
        mutationFn: (params: [...Parameters<typeof sendCsat>]) => {
            return sendCsat(...params);
        },
        onError: () => {
            snackbarToast.error(t`Не вдалося відправити данні опитування`);
        },
    });
};
