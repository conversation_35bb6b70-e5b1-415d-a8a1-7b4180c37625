import React, { Dispatch, FC, SetStateAction } from 'react';

import { FlexBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import IconStar from 'icons/star.svg';
import Icon from 'ui/icon';

import css from './StarsRate.css';

interface StarsRateProps {
    rate: number;
    setRate: Dispatch<SetStateAction<number>>;
}

export const StarsRate: FC<StarsRateProps> = ({ rate, setRate }) => {
    return (
        <>
            <FlexBox
                tagName="ul"
                justify="center"
                gap={0}
                className={css.stars}
                direction="row-reverse"
            >
                <li
                    className={cn(css.starWrap, {
                        [css.active]: rate >= 5,
                    })}
                    onClick={() => {
                        setRate(5);
                    }}
                >
                    <Icon glyph={IconStar} className={css.iconStar} />
                </li>
                <li
                    className={cn(css.starWrap, {
                        [css.active]: rate >= 4,
                    })}
                    onClick={() => {
                        setRate(4);
                    }}
                >
                    <Icon glyph={IconStar} className={css.iconStar} />
                </li>
                <li
                    className={cn(css.starWrap, {
                        [css.active]: rate >= 3,
                    })}
                    onClick={() => {
                        setRate(3);
                    }}
                >
                    <Icon glyph={IconStar} className={css.iconStar} />
                </li>
                <li
                    className={cn(css.starWrap, {
                        [css.active]: rate >= 2,
                    })}
                    onClick={() => {
                        setRate(2);
                    }}
                >
                    <Icon glyph={IconStar} className={css.iconStar} />
                </li>
                <li
                    className={cn(css.starWrap, {
                        [css.active]: rate >= 1,
                    })}
                    onClick={() => {
                        setRate(1);
                    }}
                >
                    <Icon glyph={IconStar} className={css.iconStar} />
                </li>
            </FlexBox>
        </>
    );
};
