import React, { useState } from 'react';

import { Button, Text, TextAreaInput, Title } from '@vchasno/ui-kit';

import { StarsRate } from 'components/CsatPopup/StarsRate';
import { mapRate, mapTitle } from 'components/CsatPopup/constants';
import { useSendCsat } from 'components/CsatPopup/useSendCsat';
import { CsatEstimateLevel, CsatType } from 'services/scat';
import { t } from 'ttag';
import Popup from 'ui/popup/popup';

import sentImg from './images/send.gif';

import css from './CsatPopup.css';

interface CsatPopupProps {
    type: CsatType;
    onSend?: (rate: CsatEstimateLevel, feedback: string) => void;
    onClose?: () => void;
}

export const CsatPopup: React.FC<CsatPopupProps> = ({
    type,
    onClose,
    onSend,
}) => {
    const [feedback, setFeedback] = useState('');
    const [rate, setRate] = useState<CsatEstimateLevel>(0);
    const [isSendSuccess, setIsSendSuccess] = useState(false);

    const { mutateAsync, isLoading } = useSendCsat();

    return (
        <Popup
            active
            onClose={async () => {
                await mutateAsync([type, null, '']);
                onClose?.();
            }}
            isCloseByButtonOnly
            className={css.rootPopup}
        >
            <div className={css.root}>
                {isSendSuccess ? (
                    <>
                        <div className={css.imageRate}>
                            <img
                                src={sentImg}
                                alt={'sent'}
                                className={css.rateImg}
                            />
                        </div>

                        <Title level={2}>{t`Дякуємо за ваш відгук!`}</Title>

                        <Text type="secondary" className={css.description}>
                            {t`Ми дуже цінуємо вашу думку — саме завдяки вам ми можемо ставати кращими.`}
                        </Text>
                    </>
                ) : (
                    <>
                        <div className={css.imageRate}>
                            {rate === 0 ? (
                                '⭐'
                            ) : (
                                <img
                                    src={mapRate[rate].icon}
                                    alt={String(rate)}
                                    className={css.rateImg}
                                />
                            )}
                        </div>

                        <Title level={2}>
                            {rate === 0 ? mapTitle[type] : mapRate[rate].title}
                        </Title>

                        <Text type="secondary" className={css.description}>
                            {rate === 0
                                ? t`Ваші відгуки допомагають нам стати кращими`
                                : mapRate[rate].description}
                        </Text>

                        <StarsRate rate={rate} setRate={setRate} />

                        <TextAreaInput
                            value={feedback}
                            onChange={(event) => {
                                setFeedback(event.currentTarget.value);
                            }}
                            minRows={7}
                            maxRows={7}
                            placeholder={t`Залиште нам свій відгук`}
                            maxLength={1000}
                            className={css.textArea}
                        />

                        <div className={css.btnWrap}>
                            <Button
                                disabled={rate === 0}
                                loading={isLoading}
                                theme="primary"
                                className={css.button}
                                onClick={async () => {
                                    await mutateAsync([type, rate, feedback]);
                                    setIsSendSuccess(true);
                                    onSend?.(rate, feedback);
                                }}
                            >{t`Надіслати`}</Button>
                        </div>
                    </>
                )}
            </div>
        </Popup>
    );
};
