import React, { useEffect } from 'react';
import Helmet from 'react-helmet';
import { useDispatch, useSelector } from 'react-redux';

import { mapRate } from 'components/CsatPopup/constants';
import { useCsatSurveyShowHandlers } from 'components/CsatPopup/useCsatSurveyShowHandlers';
import actionCreators from 'components/app/appActionCreators';
import { getCurrentUser } from 'selectors/app.selectors';
import { closeCsatModal } from 'store/csat';
import { getCsat } from 'store/csat/csatSelectors';

import { CsatSurvey } from '../../types/user';

import { CsatPopup } from './CsatPopup';

interface CsatPopupContainerProps {
    type: CsatSurvey['type'];
}

const CsatPopupContainer: React.FC<CsatPopupContainerProps> = ({ type }) => {
    const dispatch = useDispatch();
    const { isReadyForNextCsat, csatSurveys } = useSelector(getCurrentUser);
    const csatSlice = useSelector(getCsat);
    const currentTypeResults = csatSurveys.filter(
        (survey) => survey.type === type,
    );
    const csatHandlers = useCsatSurveyShowHandlers();

    useEffect(() => {
        if (config.DEBUG) {
            // @ts-ignore
            window['__csatHandlers'] = csatHandlers;
        }
    }, []);

    const handleClose = () => {
        dispatch(closeCsatModal());
    };

    const handleSuccessSend = () => {
        // оновлюємо користувача через 3 секунди, щоб встиг записатися в базу новий КСАТ
        setTimeout(() => {
            // це оновить дані про ксат і має підвантажити і закрити попап
            dispatch(actionCreators.onRefreshCurrentUser());
            // або закриваємо вручну щоб не блокувати користувача у разі проблем з оновленням користувача
            dispatch(closeCsatModal());
        }, 3_000);
    };

    const surveyJSX = (
        <>
            <Helmet>
                {Object.values(mapRate).map((rate) => (
                    <link
                        key={rate.title}
                        rel="prefetch"
                        href={rate.icon}
                        as="image"
                    />
                ))}
            </Helmet>
            <CsatPopup
                type={type}
                onSend={handleSuccessSend}
                onClose={handleClose}
            />
        </>
    );

    // користувач пройшов опитування або закрив попап з опитуванням нещодавно (14 днів - логіка на бекенді)
    if (!isReadyForNextCsat) {
        return null;
    }

    // якщо маємо показати кокретний тип опитування - тільки один попап опитування за раз
    if (csatSlice.type !== type) {
        return null;
    }

    // Користувач пройшов відповідний КСАТ
    if (
        currentTypeResults.length > 0 &&
        currentTypeResults.some((res) => res.estimate > 0)
    ) {
        return null;
    }

    // це місце коли в нас користувач вже закривав КСАТ вдруге (estimate = null) - не показуємо більше
    if (currentTypeResults.filter((res) => res.estimate === null).length > 1) {
        return null;
    }

    // Користувач закрив КСАТ один раз (в базу записався estimate = null) - резолвер isReadyForNextCsat повертає true та csatSurveys повертає цей КСАТ
    if (
        currentTypeResults.length === 1 &&
        currentTypeResults[0].estimate === null
    ) {
        // це місце коли в нас перший раз користувач закрив КСАТ але прийшов час показати вдруге
        return surveyJSX;
    }

    return surveyJSX;
};

export default CsatPopupContainer;
