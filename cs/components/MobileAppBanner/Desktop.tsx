import React, { FC, useEffect } from 'react';

import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';
import CloseButton from 'ui/closeButton/closeButton';

import mobileApp from './images/mobile_app.png';
import qr from './images/qr.jpg';

import css from './Desktop.css';

interface DesktopMobileAppBannerProps {
    setCloseBanner: () => void;
}

export const DesktopMobileAppBanner: FC<DesktopMobileAppBannerProps> = ({
    setCloseBanner,
}) => {
    useEffect(() => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'shown',
            campaign: 'app_sidebar',
        });
    }, []);

    const handleClose = () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'closed_manual',
            campaign: 'app_sidebar',
        });
        setCloseBanner();
    };

    return (
        <div className={css.borderWrapper}>
            <CloseButton className={css.closeBtn} onClose={handleClose} />
            <div className={css.wrapper}>
                <div className={css.title}>
                    {t`Встановлюйте застосунок Вчасно.ЕДО`}
                </div>
                <a
                    href="https://service.vchasno.ua/vchasno-edo-app"
                    onClick={() => {
                        eventTracking.sendPromoBannerToGTMV4({
                            action: 'click',
                            campaign: 'app_sidebar',
                        });
                    }}
                    target="_blank"
                    className={css.qrCode}
                >
                    <img className={css.qrCodeIMG} src={qr} alt="QR Code" />
                </a>
                <img
                    className={css.appScreen}
                    src={mobileApp}
                    alt="Mobile App Screen"
                />
            </div>
        </div>
    );
};
