import { useState } from 'react';

import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';

import { Nullable } from '../../../types/general';

import { isCloseBannerKey } from '../MobileAppBanner.constants';

export const useCloseBanner = () => {
    const [isCloseBanner, setStateCloseBanner] = useState<Nullable<boolean>>(
        // Виключили в рамках задачі https://vchasno-group.atlassian.net/browse/EC-49
        // TODO: видалити, якщо за півроку виявиться непотрібним
        // () => getLocalStorageItem(isCloseBannerKey),
        () => true,
    );

    const setCloseBanner = () => {
        setLocalStorageItem(isCloseBannerKey, true);
        setStateCloseBanner(getLocalStorageItem(isCloseBannerKey));
    };

    return [isCloseBanner, setCloseBanner] as const;
};
