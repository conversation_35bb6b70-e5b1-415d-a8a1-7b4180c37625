import React, { FC, useEffect, useState } from 'react';
import MediaQuery from 'react-responsive';

import CloseButton from 'components/ui/closeButton/closeButton';
import { motion } from 'framer-motion';
import { MEDIA_WIDTH, MOBILE_APP_BANNER_KEY } from 'lib/constants';
import { setLocalStorageItem } from 'lib/webStorage';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import { useGetMobileAppDownloadLink } from './hooks/useGetMobileAppDownloadLink';

import SmartPhone from './images/smartphone.png';
import Stars from './images/stars.gif';

import css from './Mobile.css';

export const MobileAppBanner: FC = () => {
    const url = useGetMobileAppDownloadLink();
    const [bannerActive, setBannerActive] = useState<boolean>(false);

    // Виключили в рамках задачі https://vchasno-group.atlassian.net/browse/EC-49
    // TODO: видалити, якщо за півроку виявиться непотрібним
    // const mobileAppBannerHide = getLocalStorageItem(MOBILE_APP_BANNER_KEY);
    const mobileAppBannerHide = true;

    useEffect(() => {
        setBannerActive(!mobileAppBannerHide);
    }, [mobileAppBannerHide]);

    useEffect(() => {
        if (bannerActive) {
            eventTracking.sendPromoBannerToGTMV4({
                action: 'shown',
                campaign: 'app_responsive_top',
            });
        }
    }, [bannerActive]);

    const onCloseBanner = () => {
        setBannerActive(false);
        setLocalStorageItem(MOBILE_APP_BANNER_KEY, true);
    };

    const handleClickBanner = () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'click',
            campaign: 'app_responsive_top',
        });
        onCloseBanner();
    };
    const handleCloseBanner = () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'closed_manual',
            campaign: 'app_responsive_top',
        });
        onCloseBanner();
    };

    return (
        <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
            {bannerActive && (
                <div className={css.bannerContainer}>
                    <a
                        href={url}
                        target="_blank"
                        className={css.borderWrapper}
                        onClick={handleClickBanner}
                    >
                        <div className={css.wrapper}>
                            <div className={css.title}>
                                {t`Встановлюйте застосунок Вчасно.ЕДО`}
                            </div>
                        </div>
                        <img
                            src={SmartPhone}
                            alt="Human IMG"
                            className={css.image}
                        />
                        <motion.img
                            variants={{
                                hidden: { opacity: 0 },
                                visible: {
                                    opacity: 1,
                                    transition: { duration: 1 },
                                },
                            }}
                            initial="hidden"
                            animate="visible"
                            exit="hidden"
                            className={css.stars}
                            src={Stars}
                            alt={t`Зірки`}
                            width={35}
                            height={35}
                        />
                    </a>
                    <CloseButton
                        withHover
                        className={css.closeButton}
                        position="absolute"
                        onClose={handleCloseBanner}
                    />
                </div>
            )}
        </MediaQuery>
    );
};
