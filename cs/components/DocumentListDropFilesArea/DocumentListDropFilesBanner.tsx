import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { BlackTooltip, FlexBox, SvgBorder, Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import { useUploadActions } from 'components/uploader/useUploadActions';
import { useDocumentUploadDropzone } from 'hooks/useDocumentUploadDropzone';
import { getOnboardingIsShow } from 'selectors/onboarding.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';
import Icon from 'ui/icon';
import PseudoLink from 'ui/pseudolink/pseudolink';

import { useDocumentUploadMaxSize } from './useDocumentUploadMaxSize';

import CloseSVG from '../../icons/cross.svg';
import UploadSVG from '../../icons/upload.svg';

import css from './DocumentListDropFilesBanner.css';

interface DocumentListDropFilesBannerProps {
    onClose?: VoidFunction;
}

const DocumentListDropFilesBanner: React.FC<DocumentListDropFilesBannerProps> = ({
    onClose,
}) => {
    const onboardingIsShow = useSelector(getOnboardingIsShow);
    const uploadActions = useUploadActions();
    const {
        getRootProps,
        getInputProps,
        isDragActive,
        open: openFileBrowser,
    } = useDocumentUploadDropzone({
        onDrop: (acceptedFiles) => {
            eventTracking.sendToGTM({
                event: 'doc_upload_add',
                category: 'drag_and_drop',
            });
            eventTracking.sendPromoBannerToGTMV4({
                action: 'click',
                campaign: 'drop_files',
            });
            uploadActions.setSelectedFilesAC(acceptedFiles);
            uploadActions.onShowPopup('documents-drop-area');
        },
        noClick: true,
        noKeyboard: true,
    });
    const maxFileSizeMB = useDocumentUploadMaxSize();

    const handleOpenFileBrowser = () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'click',
            campaign: 'drop_files',
        });
        openFileBrowser();
    };

    useEffect(() => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'shown',
            campaign: 'drop_files',
        });
    }, []);

    const handleClose = () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'closed_manual',
            campaign: 'drop_files',
        });
        onClose?.();
    };

    if (onboardingIsShow) {
        return null;
    }

    return (
        <SvgBorder
            {...getRootProps()}
            color={isDragActive ? 'var(--link-color)' : 'var(--default-border)'}
            animation="border-offset"
            animationDurationSec={1.2}
            dashoffset={16}
            animationPlay={isDragActive}
            className={cn(css.root, {
                [css.dropBoxActive]: isDragActive,
            })}
        >
            <input {...getInputProps()} />

            <BlackTooltip
                disableInteractive
                title={t`Закрийте банер та додавайте документи просто перетягнувши їх на сторінку`}
            >
                <span
                    onClick={handleClose}
                    role="button"
                    className={css.closeIcon}
                >
                    <Icon glyph={CloseSVG} />
                </span>
            </BlackTooltip>

            <FlexBox direction="column" align="center" justify="center">
                <span className={css.icon}>
                    <Icon glyph={UploadSVG} />
                </span>
                <Text className={css.title}>
                    {t`Перетягніть файли сюди або натисніть`}{' '}
                    <PseudoLink
                        onClick={handleOpenFileBrowser}
                    >{t`завантажити`}</PseudoLink>
                </Text>
                <Text
                    type="secondary"
                    className={css.subTitle}
                >{t`Максимальний розмір файлу ${maxFileSizeMB} МБ`}</Text>
            </FlexBox>
        </SvgBorder>
    );
};

export default DocumentListDropFilesBanner;
