import React from 'react';

import { formatDate } from 'lib/date';
import PropTypes from 'prop-types';
import { XmlDocument } from 'records/document';
import { XmlData } from 'records/xml';

import SignLine from '../../ui/signLine/signLine';

import FooterRenderer from '../../../footerRenderer/footerRenderer';
import Company from '../../company/company';
import { DATE_FORMAT_FULL } from '../constants';

import css from './deedWrapper.css';

const DeedWrapper = ({
    data,
    doc,
    children,
    renderSignatures,
    renderReviews,
    renderServicesToOrderNotation = true,
}) => {
    const { date, number, owner, partner, services, templateAttribute } = data;

    const operatorTitle = templateAttribute === '9' ? 'Оператор' : 'Виконавець';
    const customerTitle = templateAttribute === '9' ? 'Клієнт' : 'Замовник';
    const signatureOperatorTitle =
        templateAttribute === '9' ? 'Оператора' : 'Виконавця';
    const signatureCustomerTitle =
        templateAttribute === '9' ? 'Клієнта' : 'Замовника';

    return (
        <div>
            <h1 className={css.title}>
                АКТ № {number} <br /> здачі-прийняття робіт (надання послуг)
            </h1>
            <div className={css.header}>
                <div className={css.row}>
                    <div className={css.cellMiddle}>
                        <div className={css.logo}>
                            <img
                                src={`${config.STATIC_HOST}/images/logos/nova_poshta.png`}
                                alt=""
                            />
                        </div>
                    </div>
                    <div className={css.cell}>
                        <div className={css.table}>
                            <div className={css.row}>
                                <div className={css.cell}>
                                    <b className={css.textNoWrap}>
                                        від {formatDate(date, DATE_FORMAT_FULL)}
                                        р.
                                    </b>
                                </div>
                                <div className={css.cell}>
                                    <div className={css.textRight}>
                                        <b>
                                            Місце складання:{' '}
                                            {data.agreementPlace}
                                        </b>
                                    </div>
                                </div>
                            </div>
                            <div className={css.row}>
                                <div className={css.cell}>
                                    <b>{operatorTitle}</b>
                                </div>
                                <div className={css.cell}>
                                    <b>{owner.fullName}</b>
                                </div>
                            </div>
                            <div className={css.row}>
                                <div className={css.cell}>
                                    <b>{customerTitle}</b>
                                </div>
                                <div className={css.cell}>
                                    <b>{partner.fullName}</b>
                                </div>
                            </div>
                            {data.agreementName && (
                                <div className={css.row}>
                                    <div className={css.cell}>
                                        <b>Підстава</b>
                                    </div>
                                    <div className={css.cell}>
                                        <b>{data.agreementName}</b>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div>
                            1. Загальна вартість виконаних робіт (послуг) з ПДВ:
                        </div>
                        <div className={css.totalSumString}>
                            {services.totalSumStr}
                        </div>
                        {renderServicesToOrderNotation && (
                            <div>
                                2. Послуги за замовленням надані згідно Е/Н в
                                повному обсязі.
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {children}

            <div className={css.documentTrivia}>
                Сторони претензій одна до одної не мають.
            </div>

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від {signatureOperatorTitle}
                            <br />
                            {owner.representativePosition}
                        </th>
                        <th>
                            Від {signatureCustomerTitle}
                            <br />
                            {partner.representativePosition}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <SignLine typeInlined />
                            <b>{owner.representative}</b>
                        </td>
                        <td>
                            <SignLine typeInlined />
                            <b>{partner.representative}</b>
                        </td>
                    </tr>
                </tbody>
            </table>
            <table className={css.companyTable}>
                <tbody>
                    <tr>
                        <td className={css.requisites}>
                            <Company data={owner} />
                        </td>
                        <td className={css.requisites}>
                            <Company data={partner} />
                        </td>
                    </tr>
                </tbody>
            </table>
            {(renderSignatures || renderReviews) && (
                <FooterRenderer
                    renderSignatures={renderSignatures}
                    renderReviews={renderReviews}
                    doc={doc}
                />
            )}
        </div>
    );
};

DeedWrapper.propTypes = {
    renderSignatures: PropTypes.bool,
    renderReviews: PropTypes.bool,
    data: PropTypes.instanceOf(XmlData).isRequired,
    doc: PropTypes.instanceOf(XmlDocument).isRequired,
    renderServicesToOrderNotation: PropTypes.bool,
};

export default DeedWrapper;
