import React from 'react';

import { BayerNakladna } from 'components/txtXmlViewer/bayer/BayerNakladna';
import xmlBayerParser from 'components/txtXmlViewer/bayer/xmlBayerParser';
import { AcceptanceTransferCertificate } from 'components/txtXmlViewer/rozetka/AcceptanceTransferCertificate/AcceptanceTransferCertificate';
import {
    ROZETKA_ACCEPTANCE_TRANSFER_CERTIFICATE_DOCUMENT_FOP_TYPE,
    ROZETKA_ACCEPTANCE_TRANSFER_CERTIFICATE_DOCUMENT_TOV_TYPE,
} from 'components/txtXmlViewer/rozetka/AcceptanceTransferCertificate/constants';
import { parserAcceptanceTransferCertificate } from 'components/txtXmlViewer/rozetka/AcceptanceTransferCertificate/parserAcceptanceTransferCertificate';
import { AVRORA_EDRPOUS } from 'components/txtXmlViewer/rozetka/Avrora/constants';
import { parseXml } from 'services/xml';

import { textContent } from './utils';

import ANCDeed from './ANC/DeedAvr';
import { ANC_DOCUMENT_TYPE } from './ANC/constants';
import { comdocResolver } from './Comdoc/comdocResolver';
import HusqvarnaDeliveryNote from './Husqvarna/DeliveryNote/HusqvarnaDeliveryNote';
import HusqvarnaDeliveryNoteParser from './Husqvarna/DeliveryNote/parser';
import HusqvarnaInvoice from './Husqvarna/Invoice/HusqvarnaInvoice';
import HusqvarnaInvoiceParser from './Husqvarna/Invoice/parser';
import MTIDeliveryNote from './MTI/deliveryNote';
import MTIParser from './MTI/parser';
import {
    AGROMAT_DOCUMENT_TYPE,
    DOCUMENT_TYPE_QUERY,
} from './agromat/constants';
import AgromatDeed from './agromat/deedRtu';
import agromatParser from './agromat/parser';
import ArricanoDeed from './arricano/deed';
import arricanoParser from './arricano/parser';
import { CarlsbergParserResolver } from './carlsbergUkraine/resolvers/parserResolver';
import { CarlsbergUkraineTemplateResolver } from './carlsbergUkraine/resolvers/templateResolver';
import DeliveryDeed from './delivery/deed';
import deliveryParser from './delivery/parser';
import DeliveryReconciliationDeed from './delivery/reconciliationDeed';
import elitUkraineParser from './elitUkraine/parser';
import ElitUkraineSalesIvnoice from './elitUkraine/salesInvoice';
import forpostParser from './forpost/parser';
import ForpostPaymentRegistry from './forpost/paymentRegistry/paymentRegistry';
import ForpostReceipt from './forpost/receipt/receipt';
import FozzyApplication from './fozzy/application';
import FozzyInnerInvoice from './fozzy/deed';
import fozzyParser from './fozzy/parser';
import { InterCarsParserResolver } from './interCars/resolvers/parserResolver';
import { InterCarsTemplateResolver } from './interCars/resolvers/templateResolver';
import IntertopInternalDisplacementInvoice from './intertop/internalDisplacementInvoice';
import intertopParser from './intertop/parser';
import LifecellDeedBMSv3 from './lifecell/DeedBMSv3';
import LifecellDeedCRPv3 from './lifecell/DeedCRPv3';
import LifecellDeedBMS from './lifecell/deedBMS';
import LifecellDeedBMSv2 from './lifecell/deedBMSv2';
import LifecellDeedCRP from './lifecell/deedCRP';
import LifecellDeedCRPv2 from './lifecell/deedCRPv2';
import lifecellParser from './lifecell/parser';
import metInvestParser from './metinvest/parser';
import { metinvestParserResolver } from './metinvest/resolvers/parserResolver';
import { metinvestTemplateResolver } from './metinvest/resolvers/templateResolver';
import MetInvestStatement from './metinvest/statement';
import MetroDeliveryNote from './metro/DeliveryNote/deliveryNote';
import MetroDeliveryNoteParser from './metro/DeliveryNote/parser';
import MichelinDeed from './michelin/deed';
import michelinParser from './michelin/parser';
import NovaPoshtaComplWorksCert from './novaPoshta/complWorksCert/complWorksCert';
import NovaPoshtaDeed from './novaPoshta/deed/deed';
import NovaPoshtaDeedRegular from './novaPoshta/deedRegular/deedRegular';
import NovaPoshtaDeedShort from './novaPoshta/deedShort/deedShort';
import NovaPoshtaInvoice from './novaPoshta/invoice/invoice';
import NovaPoshtaInvoiceWithDeed from './novaPoshta/invoiceWithDeed/invoiceWithDeed';
import NovaPoshtaInvoiceWithDeedRegular from './novaPoshta/invoiceWithDeedRegular/invoiceWithDeedRegular';
import NovaPoshtaInvoiceWithDeedRoyalty from './novaPoshta/invoiceWithDeedRoyalty/invoiceWithDeedRoyalty';
import NovaPoshtaInvoiceWithDeedShort from './novaPoshta/invoiceWithDeedShort/invoiceWithDeedShort';
import novaPoshtaParser from './novaPoshta/parser';
import novaPoshtaParserComplWorksCert from './novaPoshta/parserComplWorksCert';
import NovaPoshtaParserRequest from './novaPoshta/parserRequest';
import NovaPoshtaRequest from './novaPoshta/request/request';
import NovaPoshtaTemplateV8 from './novaPoshta/templateV8/templateV8';
import NovusZvit, { novusZvitParser } from './novus/NovusZvit';
import OstinDeliveryNote from './ostin/deliveryNote';
import OstinParser from './ostin/parser';
import PZTDeed from './prycarpatzahidtrance/deed';
import PZTParser from './prycarpatzahidtrance/parser';
import AvroraComdoc006 from './rozetka/Avrora/Comdoc006/Comdoc006';
import DeedAnp from './rozetka/DeedANP/DeedAnp';
import {
    ROZETKA_DEED_RTU_DOCUMENT_TYPE,
    ROZETKA_DEED__ANP_DOCUMENT_TYPE,
} from './rozetka/DeedANP/constants.ts';
import PozetkaDeed from './rozetka/PZTDeed';
import RozetkaSentRegister from './rozetka/SentRegister/SentRegister';
import rozetkaSentRegisterParser from './rozetka/SentRegister/parser';
import {
    ROZETKA_TRANSFER_OF_CERTIFICATES_DOCUMENT_FOP_TYPE,
    ROZETKA_TRANSFER_OF_CERTIFICATES_DOCUMENT_TOV_TYPE,
    ROZETKA_TRANSFER_OF_CERTIFICATES_DOCUMENT_TYPE,
    TransferOfCertificates,
    TransferOfCertificatesTOVFOP,
    transferOfCertificatesParser,
} from './rozetka/TransferOfCertificates';
import RozetkaDeliveryNote from './rozetka/deliveryNote';
import {
    CIFROTEH_EDRPOUS,
    CifrotehDeliveryNote,
} from './rozetka/layouts/index';
import rozetkaParser from './rozetka/parser';
import Invoice from './samsung/Invoice/Invoice';
import invoiceParcer from './samsung/Invoice/invoiceParcer';
import TaxInvoice from './samsung/TaxInvoice/TaxInvoice';
import taxInvoiceParcer from './samsung/TaxInvoice/taxInvoiceParcer';
import TravelVisionDeed from './travelVision/deed/deed';
import TravelVisionInvoice from './travelVision/invoice/invoice';
import travelVisionParser from './travelVision/parser';
import ttnParser from './ttn/parser';
import Ttn from './ttn/ttn';
import Txt from './txt/txt';
import VchasnoInvoiceWrapper from './vchasno/invoiceWrapper';
import ZKDeed from './zk/deed';
import ZKInvoice from './zk/invoice';
import zkParser from './zk/parser';

export const DATE_FORMAT = 'DD.MM.YYYY';
export const XML_TO_JSON_MAX_LIMIT = 10000;
export const XML_TO_JSON_URL_REGEXP = new RegExp(/\/xml-to-json/g);

export const TXT_DOCUMENT_MAPPING = {
    def: [Txt, false, false],
};

const zkDeedResolver = (data) => {
    const isAgromat = data.data.documentType === AGROMAT_DOCUMENT_TYPE;
    const isAnc = data.data.documentType === ANC_DOCUMENT_TYPE;
    if (isAgromat) {
        return React.createElement(AgromatDeed, data);
    } else if (isAnc) {
        return React.createElement(ANCDeed, data);
    }
    return React.createElement(ZKDeed, data);
};

const zkParserResolver = (data) => {
    const xml = parseXml(data.content);

    const isAgromat =
        textContent(xml, DOCUMENT_TYPE_QUERY) === AGROMAT_DOCUMENT_TYPE;
    const isAnc = textContent(xml, DOCUMENT_TYPE_QUERY) === ANC_DOCUMENT_TYPE;

    if (isAgromat) {
        return agromatParser(data);
    } else if (isAnc) {
        return arricanoParser(data);
    }
    return zkParser(data);
};

const rozetkaDeedResolver = (data) => {
    switch (data.data.documentType) {
        case ROZETKA_DEED__ANP_DOCUMENT_TYPE:
            return React.createElement(DeedAnp, data);
        case ROZETKA_TRANSFER_OF_CERTIFICATES_DOCUMENT_TYPE:
            return React.createElement(TransferOfCertificates, {
                ...data,
                data: transferOfCertificatesParser(data.doc.content),
            });
        case ROZETKA_TRANSFER_OF_CERTIFICATES_DOCUMENT_TOV_TYPE:
            return React.createElement(TransferOfCertificatesTOVFOP, {
                ...data,
                data: transferOfCertificatesParser(data.doc.content),
                variant: 'TOV',
            });
        case ROZETKA_TRANSFER_OF_CERTIFICATES_DOCUMENT_FOP_TYPE:
            return React.createElement(TransferOfCertificatesTOVFOP, {
                ...data,
                data: transferOfCertificatesParser(data.doc.content),
                variant: 'FOP',
            });
        case ROZETKA_ACCEPTANCE_TRANSFER_CERTIFICATE_DOCUMENT_TOV_TYPE:
            return React.createElement(AcceptanceTransferCertificate, {
                ...data,
                data: parserAcceptanceTransferCertificate(data.doc.content),
                variant: 'TOV',
            });
        case ROZETKA_ACCEPTANCE_TRANSFER_CERTIFICATE_DOCUMENT_FOP_TYPE:
            return React.createElement(AcceptanceTransferCertificate, {
                ...data,
                data: parserAcceptanceTransferCertificate(data.doc.content),
                variant: 'FOP',
            });
        case ROZETKA_DEED_RTU_DOCUMENT_TYPE:
            return React.createElement(PozetkaDeed, data);
        case ANC_DOCUMENT_TYPE:
            return React.createElement(ANCDeed, data);
        default:
            return React.createElement(ZKDeed, data);
    }
};

const isPagesLayout = true;

/* Configuration how to parse and render XML data:
 *
 * Keys usually looks like this { ROOT_XML_TAG: { OWNER_EDRPOU: ... }}. See `getTemplateKey` function for more details.
 * The `def` key is used as a fallback for unknown EDRPOU
 *
 * Value is an array with the following structure:
 * 1. component - react component to render XML data
 * 2. parser - function to parse XML data
 * 3. renderSignatures
 * 4. renderReviews
 * 5. renderWithPages
 */
export const XML_DOCUMENT_MAPPING = {
    elements: {
        42485293: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        34093721: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00191075': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        37732376: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '05393085': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00191023': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00190977': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00190905': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        35484610: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        35601339: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        39808111: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        39374955: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        24819472: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        31158623: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        39641616: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        13498562: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '01236070': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        36975983: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        40162326: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        37695759: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00179192': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        30939178: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        32729463: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        38626382: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00191230': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00191224': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '00191885': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        '05393079': [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        32036829: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        3140302500: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
        3316613919: [
            metinvestTemplateResolver,
            metinvestParserResolver,
            false,
            false,
            isPagesLayout,
        ],
    },
    NAKLADNA: {
        22911794: [BayerNakladna, xmlBayerParser, false, false],
        def: [BayerNakladna, xmlBayerParser, false, false],
    },
    'Документ.АктСверкиВзаиморасчетов': {
        31738765: [DeliveryReconciliationDeed, deliveryParser, true, true],
        def: [DeliveryReconciliationDeed, deliveryParser, true, true],
    },
    'Документ.ОказаниеУслуг': {
        40720198: [FozzyInnerInvoice, fozzyParser, false, false],
        def: [FozzyInnerInvoice, fozzyParser, false, false],
    },
    'Документ.ПоступлениеТоваровУслуг': {
        38324133: [ForpostReceipt, forpostParser, false, false],
        def: [ForpostReceipt, forpostParser, false, false],
    },
    'Документ.РеализацияСтраховыхУслуг': {
        31738765: [DeliveryDeed, deliveryParser, true, true],
        def: [DeliveryDeed, deliveryParser, true, true],
    },
    'Документ.РеализацияТоваровУслуг': {
        31316718: [NovaPoshtaDeed, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaDeed, novaPoshtaParser, true, true],
        31738765: [DeliveryDeed, deliveryParser, true, true],
        40283641: [ZKDeed, zkParser, false, false],
        41098152: [TravelVisionDeed, travelVisionParser, false, false],
        43204541: [TravelVisionDeed, travelVisionParser, false, false],
        39854367: [TravelVisionDeed, travelVisionParser, false, false],
        3088912807: [TravelVisionDeed, travelVisionParser, false, false],
        2264725702: [TravelVisionDeed, travelVisionParser, false, false],
        37817742: [ArricanoDeed, arricanoParser, true, true],
        34001452: [ArricanoDeed, arricanoParser, true, true],
        34530278: [ArricanoDeed, arricanoParser, true, true],
        34795907: [ArricanoDeed, arricanoParser, true, true],
        35391700: [ArricanoDeed, arricanoParser, true, true],
        36791032: [MichelinDeed, michelinParser, false, false],
        13990932: [PZTDeed, PZTParser, true, true],
        37193071: [rozetkaDeedResolver, arricanoParser, false, false],
        37568896: [rozetkaDeedResolver, arricanoParser, false, false],
        30217001: [rozetkaDeedResolver, arricanoParser, false, false],
        33584049: [rozetkaDeedResolver, arricanoParser, false, false],
        def: [zkDeedResolver, zkParserResolver, true, true],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_1': {
        31316718: [NovaPoshtaDeed, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaDeed, novaPoshtaParser, true, true],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_2': {
        31316718: [NovaPoshtaDeedShort, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaDeedShort, novaPoshtaParser, true, true],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_3': {
        31316718: [NovaPoshtaDeedRegular, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaDeedRegular, novaPoshtaParser, true, true],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_4': {
        31316718: [NovaPoshtaInvoiceWithDeed, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaInvoiceWithDeed, novaPoshtaParser, true, true],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_5': {
        31316718: [
            NovaPoshtaInvoiceWithDeedShort,
            novaPoshtaParser,
            true,
            true,
        ],
        99000101: [
            NovaPoshtaInvoiceWithDeedShort,
            novaPoshtaParser,
            true,
            true,
        ],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_6': {
        31316718: [
            NovaPoshtaInvoiceWithDeedRegular,
            novaPoshtaParser,
            true,
            true,
        ],
        99000101: [
            NovaPoshtaInvoiceWithDeedRegular,
            novaPoshtaParser,
            true,
            true,
        ],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_7': {
        31316718: [
            NovaPoshtaInvoiceWithDeedRoyalty,
            novaPoshtaParser,
            true,
            true,
        ],
        99000101: [
            NovaPoshtaInvoiceWithDeedRoyalty,
            novaPoshtaParser,
            true,
            true,
        ],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_8': {
        31316718: [NovaPoshtaTemplateV8, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaTemplateV8, novaPoshtaParser, true, true],
    },
    'Документ.РеализацияТоваровУслуг_Шаблон_9': {
        31316718: [NovaPoshtaTemplateV8, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaTemplateV8, novaPoshtaParser, true, true],
    },
    'Документ.РеестрПлатежейКонтрагентаСводный': {
        38324133: [ForpostPaymentRegistry, forpostParser, true, true],
    },
    'Документ.Счет': {
        40720198: [FozzyApplication, fozzyParser, false, false],
        def: [FozzyApplication, fozzyParser, false, false],
    },
    'Документ.СчетНаОплатуПокупателю': {
        31316718: [NovaPoshtaInvoice, novaPoshtaParser, true, true],
        99000101: [NovaPoshtaInvoice, novaPoshtaParser, true, true],
        40283641: [ZKInvoice, zkParser, false, false],
        41231992: [VchasnoInvoiceWrapper, zkParser, false, false],
        41098152: [TravelVisionInvoice, travelVisionParser, false, false],
        43204541: [TravelVisionInvoice, travelVisionParser, false, false],
        39854367: [TravelVisionInvoice, travelVisionParser, false, false],
        3088912807: [TravelVisionInvoice, travelVisionParser, false, false],
        2264725702: [TravelVisionInvoice, travelVisionParser, false, false],
        def: [ZKInvoice, zkParser, false, false],
    },
    ЕлектроннийДокумент: {
        20454393: [ElitUkraineSalesIvnoice, elitUkraineParser, false, false],
        37973863: [
            RozetkaSentRegister,
            rozetkaSentRegisterParser,
            false,
            false,
        ],
        30865632: [
            InterCarsTemplateResolver,
            InterCarsParserResolver,
            false,
            false,
        ],
        def: [comdocResolver, (data) => data, false, false],
    },
    Заявка: {
        32036829: [MetInvestStatement, metInvestParser, false, false],
        def: [MetInvestStatement, metInvestParser, false, false],
    },
    INVOICE: {
        36048094: [Invoice, invoiceParcer, false, false],
        def: [MetInvestStatement, metInvestParser, false, false],
    },
    Invoices: {
        35039974: [
            HusqvarnaDeliveryNote,
            HusqvarnaDeliveryNoteParser,
            false,
            false,
        ],
    },
    PrePayments: {
        35039974: [HusqvarnaInvoice, HusqvarnaInvoiceParser, false, false],
    },
    TAX_INVOICE: {
        36048094: [TaxInvoice, taxInvoiceParcer, false, false],
        def: [MetInvestStatement, metInvestParser, false, false],
    },
    Document_BMS: {
        22859846: [LifecellDeedBMS, lifecellParser, false, false],
    },
    Document_BMS2: {
        22859846: [LifecellDeedBMSv2, lifecellParser, false, false],
    },
    Document_BMS3: {
        22859846: [LifecellDeedBMSv3, lifecellParser, false, false],
    },
    Document_CRP: {
        22859846: [LifecellDeedCRP, lifecellParser, false, false],
    },
    Document_CRP2: {
        22859846: [LifecellDeedCRPv2, lifecellParser, false, false],
    },
    Document_CRP3: {
        22859846: [LifecellDeedCRPv3, lifecellParser, false, false],
    },
    'Document-Invoice': {
        13669756: [MTIDeliveryNote, MTIParser, false, false],
        34575827: [OstinDeliveryNote, OstinParser, false, false],
        32049199: [MetroDeliveryNote, MetroDeliveryNoteParser, false, false],
        ...CIFROTEH_EDRPOUS.reduce(
            (accum, item) => ({
                ...accum,
                [item]: [CifrotehDeliveryNote, rozetkaParser, false, false],
            }),
            {},
        ),
        ...AVRORA_EDRPOUS.reduce(
            (accum, item) => ({
                ...accum,
                [item]: [AvroraComdoc006, rozetkaParser, false, false],
            }),
            {},
        ),
        def: [RozetkaDeliveryNote, rozetkaParser, false, false],
    },
    MOVING: {
        41097426: [
            IntertopInternalDisplacementInvoice,
            intertopParser,
            false,
            false,
        ],
    },
    ComplWorksCert: {
        31316718: [
            NovaPoshtaComplWorksCert,
            novaPoshtaParserComplWorksCert,
            true,
            true,
        ],
        35868601: [
            NovaPoshtaComplWorksCert,
            novaPoshtaParserComplWorksCert,
            true,
            true,
        ],
        99000101: [
            NovaPoshtaComplWorksCert,
            novaPoshtaParserComplWorksCert,
            true,
            true,
        ],
        def: [
            NovaPoshtaComplWorksCert,
            novaPoshtaParserComplWorksCert,
            true,
            true,
        ],
    },
    DECLAR: {
        41097426: [
            IntertopInternalDisplacementInvoice,
            intertopParser,
            false,
            false,
        ],
        def: [Ttn, ttnParser, true, true],
    },
    Request: {
        def: [NovaPoshtaRequest, NovaPoshtaParserRequest, true, true],
    },
    ZVIT: {
        36003603: [NovusZvit, novusZvitParser, false, false],
        def: [
            CarlsbergUkraineTemplateResolver,
            CarlsbergParserResolver,
            false,
            false,
            isPagesLayout,
        ],
    },
};

export const RENDER_BY_RECIPIENT_EDRPOU = [
    '35625082', // Foxtrot
    '41130363', // Avrora
];

export const RENDER_BY_RECIPIENT_DOCTYPES = [
    'ЕлектроннийДокумент', // comdoc
    'Document-Invoice', // comdoc
];

export default {
    DATE_FORMAT,
    TXT_DOCUMENT_MAPPING,
    XML_DOCUMENT_MAPPING,
    XML_TO_JSON_MAX_LIMIT,
    XML_TO_JSON_URL_REGEXP,
    RENDER_BY_RECIPIENT_EDRPOU,
    RENDER_BY_RECIPIENT_DOCTYPES,
};
