import React from 'react';

import cn from 'classnames';
import { CodeGenerator } from 'components/CodeGenerator/CodeGenerator';
import { ROZETKA_EDRPOUS } from 'components/txtXmlViewer/rozetka/layouts';
import { ELKO_EDRPOU } from 'lib/constants';
import { formatDate } from 'lib/date';
import { capitalizeFirstLetter, extractNumbers } from 'lib/helpers';
import { formatPrice } from 'lib/numbers';
import PropTypes from 'prop-types';

import {
    generateUATextMoneyRepresentation,
    splitStringByLength,
} from '../utils';

import ExpandButtonRow from '../ui/expandButtonRow/expandButtonRow';
import SignLine from '../ui/signLine/signLine';

import Company from '../company/company';
import { DATE_FORMAT_FULL } from './constants';

// styles
import css from './deliveryNote.css';

const DeliveryNote = ({ data }) => {
    const {
        number,
        agreementName,
        agreementDate,
        deliveryAddress,
        date,
        place,
        partner,
        owner,
        paymentTerm,
        services,
        buyerOrder,
        buyerOrderDate,
        managerOrder,
        paymentTernOrder,
        extra,
        services: {
            items,
            totalSum,
            totalDocumentSum,
            totalDocumentSumStr,
            totalTaxes,
        },
    } = data;

    const isRozetkaPartOfDeal = ROZETKA_EDRPOUS.includes(partner.edrpou);
    const isElkoOwner = owner && owner.edrpou === ELKO_EDRPOU;
    const isNeedDeferredPaymentLine = isElkoOwner && paymentTerm;
    const totalTaxesStr =
        totalTaxes === 0
            ? undefined
            : capitalizeFirstLetter(
                  generateUATextMoneyRepresentation(totalTaxes),
              );

    const isSelfCode = items.some((service) => service.codeSelf);

    const isAdditionalNetAmountExist =
        (items?.filter((service) => !!service?.additionalNetAmount)).size > 0;
    const isAdditionalInvoiceQuantityExist =
        (items?.filter((service) => !!service?.additionalInvoiceQuantity))
            .size > 0;
    return (
        <div>
            {managerOrder && (
                <div className={css.managerRow}>
                    Відповідальний менеджер {managerOrder}
                </div>
            )}

            <h1 className={css.documentTitle}>
                Видаткова накладна № {number} від{' '}
                {formatDate(date, DATE_FORMAT_FULL)} р.
            </h1>

            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>Постачальник:</td>
                        <td>
                            <Company boldFullName data={owner} />
                        </td>

                        <td>
                            {isRozetkaPartOfDeal &&
                                extractNumbers(buyerOrder) && (
                                    <CodeGenerator
                                        text={extractNumbers(buyerOrder)}
                                        width="lg"
                                    />
                                )}
                        </td>
                    </tr>
                    <tr>
                        <td>Покупець:</td>
                        <td>
                            <Company boldFullName data={partner} />
                        </td>
                    </tr>
                    <tr>
                        <td>Договір:</td>
                        <td>
                            № {agreementName}
                            {agreementDate &&
                                ` від ${formatDate(
                                    agreementDate,
                                    DATE_FORMAT_FULL,
                                )}`}
                        </td>
                    </tr>
                    {buyerOrder && (
                        <tr>
                            <td>Замовлення:</td>
                            <td>
                                Замовлення покупця № {buyerOrder}{' '}
                                {buyerOrderDate && `від ${buyerOrderDate}`}
                            </td>
                        </tr>
                    )}
                    {deliveryAddress && (
                        <tr>
                            <td>Адреса доставки:</td>
                            <td>{deliveryAddress}</td>
                        </tr>
                    )}
                    {place && (
                        <tr>
                            <td>Місце складання:</td>
                            <td>{place}</td>
                        </tr>
                    )}
                    {isNeedDeferredPaymentLine && (
                        <tr>
                            <td>Відстрочка платежу:</td>
                            <td>{paymentTerm}</td>
                        </tr>
                    )}
                    {paymentTernOrder && (
                        <tr>
                            <td>Дата оплати:</td>
                            <td>{paymentTernOrder}</td>
                        </tr>
                    )}
                </tbody>
            </table>

            <table className={css.goodsTable}>
                <thead>
                    <tr>
                        <th className={css.number}>№</th>
                        <th
                            className={cn(css.code, {
                                [css.codeWithSelfCode]: isSelfCode,
                            })}
                        >
                            {isSelfCode ? 'Код покупця' : 'Артикул'}
                        </th>
                        {isSelfCode && (
                            <th
                                className={cn(css.code, {
                                    [css.codeWithSelfCode]: isSelfCode,
                                })}
                            >
                                Артикул
                            </th>
                        )}
                        <th
                            className={cn(css.name, {
                                [css.nameWithSelfCode]: isSelfCode,
                            })}
                        >
                            Товар
                        </th>
                        <th className={css.code}>Код УКТЗЕД</th>
                        <th className={css.code}>Код постачальника</th>
                        <th className={css.quantity}>Кіл-сть</th>
                        <th className={css.item}>Од.</th>
                        <th className={css.price}>Ціна без ПДВ</th>
                        <th className={css.sum}>Сума без ПДВ</th>

                        {isAdditionalInvoiceQuantityExist && (
                            <th className={css.quantity}>Кіл-сть в КГ</th>
                        )}
                        {isAdditionalNetAmountExist && (
                            <th className={css.price}>Ціна за КГ без ПДВ</th>
                        )}
                    </tr>
                </thead>
                <tbody>
                    {items.map((service, index) => (
                        <tr key={`product-${service.number}`}>
                            <td className={css.textCenter}>{index + 1}</td>
                            <td>{service.code}</td>
                            {isSelfCode && (
                                <td>{splitStringByLength(service.codeSelf)}</td>
                            )}
                            <td className={css.wb}>{service.name}</td>
                            <td className={css.textRight}>
                                {service.externalCode}
                            </td>
                            <td className={css.textRight}>
                                {service.supplierCode}
                            </td>
                            <td className={css.textRight}>
                                {isSelfCode
                                    ? splitStringByLength(service.quantity)
                                    : service.quantity}
                            </td>
                            <td className={css.textCenter}>{service.unit}</td>
                            <td className={css.textRight}>
                                {formatPrice(service.price)}
                            </td>
                            <td className={css.textRight}>
                                {formatPrice(service.sum)}
                            </td>
                            {isAdditionalInvoiceQuantityExist && (
                                <td className={css.textRight}>
                                    {service.additionalInvoiceQuantity}
                                </td>
                            )}
                            {isAdditionalNetAmountExist && (
                                <td className={css.textRight}>
                                    {formatPrice(service.additionalNetAmount)}
                                </td>
                            )}
                        </tr>
                    ))}
                    <ExpandButtonRow
                        colSpan={6}
                        last={items.last() && items.last().number}
                    />
                </tbody>
            </table>

            <table className={css.totalTable}>
                <tbody>
                    <tr>
                        <td className={css.totalName}>Всього:</td>
                        <td>{formatPrice(totalSum)}</td>
                    </tr>
                    <tr key="tax-key-2">
                        <td className={css.totalName}>Сума ПДВ:</td>
                        <td>{formatPrice(totalTaxes)}</td>
                    </tr>
                    <tr key="tax-key-3">
                        <td className={css.totalName}>Всього із ПДВ:</td>
                        <td>{formatPrice(totalDocumentSum)}</td>
                    </tr>
                </tbody>
            </table>

            <div className={css.documentTrivia}>
                Всього найменувань {items.size}, на суму{' '}
                {formatPrice(services.get('totalDocumentSum'))} грн.
            </div>

            {totalDocumentSumStr && (
                <div className={css.documentTriviaBold}>
                    {totalDocumentSumStr}
                </div>
            )}

            {totalTaxesStr && (
                <div className={css.documentTriviaBold}>
                    У т.ч. ПДВ: {totalTaxesStr}
                </div>
            )}

            <div className={css.documentTriviaBorder} />

            <table className={css.footerTable}>
                <tbody>
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td>
                            {extra?.addDocSigner && (
                                <div
                                    className={cn(
                                        css.documentTriviaBold,
                                        css.sizeDefault,
                                    )}
                                >
                                    {extra?.addDocSigner}
                                </div>
                            )}
                            {extra?.addDoc && (
                                <div>За довіреністю № {extra?.addDoc}</div>
                            )}
                        </td>
                    </tr>
                </tbody>
            </table>

            {extra?.compositionChecked && (
                <table className={css.footerTable}>
                    <thead>
                        <tr>
                            <th>
                                Перевірено склад: {extra?.compositionChecked}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <SignLine />
                            </td>
                        </tr>
                    </tbody>
                </table>
            )}

            {extra?.shipped && (
                <table className={css.footerTable}>
                    <thead>
                        <tr>
                            <th>Відвантажено: {extra?.shipped}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <SignLine />
                            </td>
                        </tr>
                    </tbody>
                </table>
            )}

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від Постачальника*
                            <br />
                        </th>
                        <th>
                            Отримав(ла)
                            <br />
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    );
};

DeliveryNote.propTypes = {
    data: PropTypes.object,
};

export default DeliveryNote;
