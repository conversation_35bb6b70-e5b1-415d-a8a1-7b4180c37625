import React, { FC, useEffect, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { useHistory } from 'react-router-dom';

import { useMutation } from '@tanstack/react-query';

import PartnerLogos from 'components/AuthLayout/PartnerLogos';
import FlexBox from 'components/FlexBox/FlexBox';
import { AUTH_ENTRYPOINT_PATH } from 'components/auth';
import PageTitle from 'components/pageTitle/pageTitle';
import BackButton from 'components/ui/BackButton/BackButton';
import PseudoLink from 'components/ui/pseudolink/pseudolink';
import { redirect } from 'lib/navigation';
import auth from 'services/auth';
import { t } from 'ttag';
import Alert from 'ui/Alert/Alert';
import Button from 'ui/button/button';
import Checkbox from 'ui/checkbox/checkbox';
import Icon from 'ui/icon/icon';
import OutlinedInput from 'ui/input/OutlinedInput/OutlinedInput';

import { Verify2FAFormFields } from './types';

import { verify2FAFormResolver } from './validation';

import LockIcon from './images/lock.svg';

import css from './VerifyEmail2FA.css';

/**
 * Email-based two-factor authentication component
 *
 * User enters their password to verify email access
 */
const VerifyEmail2FA: FC = () => {
    const [email, setEmail] = useState('***@***.com');
    const [commonErrorMessage, setCommonErrorMessage] = useState<string>('');
    const history = useHistory();

    const { control, handleSubmit, formState } = useForm<Verify2FAFormFields>({
        resolver: verify2FAFormResolver,
        defaultValues: {
            password: '',
            trusted: false,
        },
    });

    const {
        mutate: sendEmail2FALink,
        error: is2FALinkFailed,
        isSuccess: is2FALinkSent,
    } = useMutation({
        mutationFn: () => auth.sendEmail2FAToken(),
    });

    useEffect(() => {
        auth.getHiddenEmail().then((data) => setEmail(data.email));
    }, []);

    const onSubmit: SubmitHandler<Verify2FAFormFields> = async ({
        password,
        trusted,
    }) => {
        try {
            const response = await auth.verifyEmail2FA({
                password: password,
                trusted: trusted,
            });
            redirect(response.nextUrl);
        } catch (error) {
            const errorMessage = error.reason
                ? error.reason
                : t`Йой! Щось пішло не так.`;
            setCommonErrorMessage(errorMessage);
        }
    };

    const onBackHandler = () => {
        history.push({
            pathname: AUTH_ENTRYPOINT_PATH,
            search: location.search,
        });
    };

    const handleRemindPassword = () => {
        sendEmail2FALink();
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <PageTitle>{t`Двофакторна аутентифікація`}</PageTitle>
            <FlexBox className={css.container} justify="center" gap={0}>
                <div className={css.content}>
                    <BackButton
                        className={css.backBtn}
                        onClick={onBackHandler}
                    />
                    <PartnerLogos className={css.partnersLogos} />
                    <FlexBox
                        direction="column"
                        align="center"
                        justify="center"
                        gap={25}
                    >
                        <Icon className={css.lockIcon} glyph={LockIcon} />
                        <FlexBox
                            direction="column"
                            align="center"
                            justify="center"
                            gap={40}
                        >
                            <h1 className={css.title}>{t`Вкажіть пароль`}</h1>
                            <h4 className={css.description}>
                                {t`Вкажіть пароль для другого фактору автентифікації за поштою`}{' '}
                                <span className={css.email}>{email}</span>
                            </h4>
                        </FlexBox>
                    </FlexBox>
                    <FlexBox direction="column" gap={25}>
                        <Controller
                            control={control}
                            name="password"
                            render={({ field, fieldState }) => (
                                <OutlinedInput
                                    type="password"
                                    autoComplete="current-password"
                                    required
                                    label={t`Пароль`}
                                    value={field.value}
                                    onChange={field.onChange}
                                    autoFocus
                                    error={fieldState.error?.message}
                                />
                            )}
                        />
                        <div>
                            <PseudoLink
                                className={css.remindPasswordLink}
                                onClick={handleRemindPassword}
                            >
                                {t`Забули або немає паролю?`}
                            </PseudoLink>
                        </div>
                        {is2FALinkSent && (
                            <Alert theme="info" hideIcon>
                                {t`На вашу електронну пошту було надіслано листа з посиланням для підтвердження. Перейдіть по цьому посиланню, щоб підтвердити доступ до електронної пошти`}
                            </Alert>
                        )}
                        {is2FALinkFailed && (
                            <Alert theme="error" hideIcon>
                                {t`Не вдалося надіслати листа з посиланням для підтвердження. Спробуйте ще раз.`}
                            </Alert>
                        )}
                    </FlexBox>
                    {commonErrorMessage && (
                        <Alert theme="error" hideIcon>
                            {commonErrorMessage}
                        </Alert>
                    )}
                    <FlexBox gap={25} direction="column">
                        <Button
                            className={css.submitBtn}
                            type="submit"
                            theme="darkGray"
                            disabled={
                                formState.isSubmitting ||
                                formState.isSubmitSuccessful
                            }
                        >{t`Продовжити`}</Button>
                        <Controller
                            control={control}
                            name="trusted"
                            render={({ field }) => (
                                <Checkbox
                                    checked={field.value}
                                    onChange={field.onChange}
                                    color="deepSkyBlue"
                                    isOnlyInputClick={false}
                                    text={t`Це надійний пристрій. Підтверджувати паролем раз на місяць`}
                                />
                            )}
                        />
                    </FlexBox>
                </div>
            </FlexBox>
        </form>
    );
};

export default VerifyEmail2FA;
