.container {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 20px;
}

.content {
    display: flex;
    width: 100%;
    max-width: 400px;
    flex-direction: column;
    margin-top: 25%;
    gap: 40px;
}

.backBtn {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: var(--grey-bg);
}

.backBtn svg {
    color: var(--slate-grey-color);
}

.partnersLogos {
    display: none;
}

.lockIcon {
    width: 60px;
    height: 60px;
}

.title {
    color: var(--content-color);
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.description {
    color: var(--content-secondary-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.email {
    color: var(--content-color);
    font-weight: bold;
}

.remindPasswordLink {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.submitBtn {
    width: 180px;
    height: 50px;
    padding: 12px 30px;
    border-radius: var(--border-radius);
}

@media all and (max-width: 768px) {
    .container {
        padding: 0;
    }

    .backBtn {
        top: 0;
        left: 0;
    }

    .content {
        margin-top: 0;
        margin-bottom: 24px;
        gap: 24px;
    }

    .partnersLogos {
        display: flex;
        height: 36vh;
        align-self: center;
        margin-top: 22px;
        object-fit: contain;
    }

    .remindPasswordLink {
        align-self: flex-end;
    }

    .submitBtn {
        width: 100%;
    }
}
