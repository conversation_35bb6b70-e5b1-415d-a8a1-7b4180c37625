import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button, FlexBox, Paragraph, Title } from '@vchasno/ui-kit';

import Popup from 'components/ui/popup/popup';
import { ARCHIVE_SURVEY_POPUP_KEY } from 'lib/constants';
import { openInNewTab } from 'lib/navigation';
import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import { getCurrentUserSurveys } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import { ARCHIVE_SURVEY_GOOGLE_DOCS_LINK } from './constants';

import surveyMan from './images/surveyMan.png';

import css from './ArchiveSurveyPopup.css';

const ArchiveSurveyPopup: React.FC = () => {
    const [isOpen, setIsOpen] = useState(false);
    const surveys = useSelector(getCurrentUserSurveys);

    const mobileAppPopupHide = getLocalStorageItem(ARCHIVE_SURVEY_POPUP_KEY);

    useEffect(() => {
        if (surveys.includes('archive') && !mobileAppPopupHide) {
            setIsOpen(true);
            eventTracking.sendPromoPopupToGTMV4({
                action: 'shown',
                campaign: 'archive_survey',
            });
        }
    }, [mobileAppPopupHide]);

    const onClickSurvey = () => {
        openInNewTab(ARCHIVE_SURVEY_GOOGLE_DOCS_LINK);
        setLocalStorageItem(ARCHIVE_SURVEY_POPUP_KEY, true);
        setIsOpen(false);
        eventTracking.sendPromoPopupToGTMV4({
            action: 'click',
            campaign: 'archive_survey',
        });
    };

    const onClose = () => {
        setLocalStorageItem(ARCHIVE_SURVEY_POPUP_KEY, true);
        setIsOpen(false);
        eventTracking.sendPromoPopupToGTMV4({
            action: 'closed_manual',
            campaign: 'archive_survey',
        });
    };

    return (
        <Popup active={isOpen} onClose={onClose} className={css.root}>
            <FlexBox direction="column" gap={20}>
                <img
                    className={css.image}
                    src={surveyMan}
                    alt="archiveSurvey picture"
                />
                <Title
                    level={2}
                    className={css.confirmEmailTitle}
                >{t`Допоможіть покращити архів документів у Вчасно!`}</Title>
                <Paragraph>
                    {t`Ви вже скористалися нашим архівом, і нам важлива ваша думка! Поділіться своїм досвідом – це допоможе нам зробити архів ще зручнішим, усунути недоліки та впровадити корисні покращення.`}
                </Paragraph>
                <Button
                    className={css.button}
                    size="lg"
                    wide
                    onClick={onClickSurvey}
                >{t`Допомогти покращити архів`}</Button>
            </FlexBox>
        </Popup>
    );
};

export default ArchiveSurveyPopup;
