import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import {
    Redirect,
    Route,
    Switch,
    useHistory,
    useLocation,
} from 'react-router-dom';

import { PublicClientApplication } from '@azure/msal-browser';
import { Msal<PERSON>rovider } from '@azure/msal-react';
import { QueryClientProvider } from '@tanstack/react-query';

import LoginForm from 'components/LoginForm';
import { msalConfig } from 'components/MicrosoftAuthButton/msalConfig';
import RegistrationByInvite from 'components/RegistrationByInvite';
import RemindPassForm from 'components/RemindPassForm';
import RecoverPassForm from 'components/recoverPassForm';
import RegistrationForm from 'components/registration/components/userRegistrationForm';
import VerifyEmail2FA from 'components/verifyEmail2FA/VerifyEmail2FA';
import VerifyEmail2FAToken from 'components/verifyEmail2FAToken/VerifyEmail2FAToken';
import VerifyPhone2FA from 'components/verifyPhone2FA/VerifyPhone2FA';
import {
    ConnectedRouter,
    connectRouter,
    routerMiddleware,
} from 'connected-react-router';
import { createBrowserHistory } from 'history';
import { isClientRendering } from 'lib/helpers';
import { queryClient } from 'lib/queries';
import { applyMiddleware, combineReducers, createStore } from 'redux';
import thunk from 'redux-thunk';
import eventTracking from 'services/analytics/eventTracking';
import applyPolyfills from 'services/polyfill';

import { saveInviteEmailParamsToLocalStorage } from './utils';

import AuthCheckPhoneCode from '../AuthCheckPhoneCode/AuthCheckPhoneCode';
import AuthLayout from '../AuthLayout/AuthLayout';
import AuthStartForm from '../AuthStartForm/AuthStartForm';
// Auth container & error page
import AuthError from './authError';
import authFeatureFlagsReducer from './authFeatureFlags/authFeatureFlagsReducer';
import { useSetFeatureFlagsEffect } from './authFeatureFlags/hooks/useSetFeatureFlagsEffect';
import ErrorCard from './components/ErrorCard/ErrorCard';
import Loading from './components/Loading/Loading';

const browserHistory = isClientRendering() ? createBrowserHistory() : {};

const middleware = [routerMiddleware(browserHistory), thunk];

const store = createStore(
    combineReducers({
        router: connectRouter(browserHistory),
        authFeatureFlags: authFeatureFlagsReducer,
    }),
    applyMiddleware(...middleware),
);

if (isClientRendering()) {
    applyPolyfills();
}

const msalInstance = new PublicClientApplication(msalConfig);

export const AUTH_ENTRYPOINT_PATH = '/auth/start';

const AuthComponent = () => {
    const {
        requestState: authFeatureFlagsRequestState,
        errorMessage,
    } = useSetFeatureFlagsEffect();

    const history = useHistory();
    const location = useLocation();
    const { pathname, search } = location;

    useEffect(() => {
        saveInviteEmailParamsToLocalStorage(location);
    }, []);

    useEffect(() => {
        window.scrollTo(0, 0);
    }, [pathname, search]);

    useEffect(() => {
        function logEvent(loc) {
            if (loc) {
                eventTracking.pageview('', loc.pathname + loc.search);
            }
        }

        return history.listen(logEvent);
    }, [history]);

    if (
        authFeatureFlagsRequestState === 'uncalled' ||
        authFeatureFlagsRequestState === 'pending'
    ) {
        return <Loading />;
    }

    if (authFeatureFlagsRequestState === 'error') {
        return <ErrorCard errorMessage={errorMessage} />;
    }

    return (
        <AuthLayout>
            <Switch>
                <Route
                    exact
                    path={AUTH_ENTRYPOINT_PATH}
                    component={AuthStartForm}
                />
                <Route
                    exact
                    path="/auth/phone/verify"
                    component={AuthCheckPhoneCode}
                />
                <Route exact path="/auth/login" component={LoginForm} />
                <Route
                    exact
                    path="/auth/password/remind"
                    component={RemindPassForm}
                />
                <Route
                    exact
                    path="/auth/password/recover"
                    component={RecoverPassForm}
                />
                <Route
                    exact
                    // Pay attention, backend is responsible to redirect to this route
                    // if user need to complete 2FA verification. Do not change route
                    // name without backend changes
                    path="/auth/phone-2fa/verify"
                    component={VerifyPhone2FA}
                />
                <Route
                    exact
                    // Pay attention, backend is responsible to redirect to this route
                    // if user need to complete 2FA verification. Do not change route
                    // name without backend changes
                    path="/auth/email-2fa/verify"
                    component={VerifyEmail2FA}
                />
                <Route
                    exact
                    path="/auth/email-2fa/verify-token"
                    component={VerifyEmail2FAToken}
                />
                <Route
                    exact
                    path="/auth/registration/register-user"
                    component={RegistrationForm}
                />
                <Route
                    exact
                    path="/auth/registration/short"
                    component={RegistrationByInvite}
                />
                <Route path="/auth/not-found" component={AuthError} />
                <Redirect
                    from="/auth/registration"
                    to={{
                        pathname: '/auth/registration/register-user',
                        search,
                    }}
                />
                <Redirect
                    from="/auth/trial-registration"
                    to="/auth/registration/register-user?activeProTrial=true"
                />
                {/* temporary redirect */}
                <Redirect
                    from="/auth/registration-new"
                    to="/auth/registration"
                />
                <Redirect
                    strict
                    from="/auth"
                    to={{ pathname: AUTH_ENTRYPOINT_PATH, search }}
                />
                {/* redirect from old starting point to new */}
                <Redirect
                    strict
                    from="/auth/check-email"
                    to={{ pathname: AUTH_ENTRYPOINT_PATH, search }}
                />
                <Redirect to="/auth/not-found" />
            </Switch>
        </AuthLayout>
    );
};

const authWrapper = () => (
    <Provider store={store}>
        <QueryClientProvider client={queryClient}>
            <ConnectedRouter history={browserHistory}>
                <MsalProvider instance={msalInstance}>
                    <AuthComponent />
                </MsalProvider>
            </ConnectedRouter>
        </QueryClientProvider>
    </Provider>
);

export default authWrapper;
