import React, { useEffect } from 'react';
import { useMediaQuery } from 'react-responsive';

import { Button, FlexBox } from '@vchasno/ui-kit';

import { useDetectedInTimeBannerV2Context } from 'components/DetectedInTimeBannerV2/context';
import ArrowRightSvg from 'icons/arrow-right.svg';
import { MEDIA_WIDTH } from 'lib/constants';
import eventTracking from 'services/analytics/eventTracking';
import Icon from 'ui/icon';

import CrossSvg from '../../icons/cross.svg';
import LogoSvg from './images/logo.svg';
import WaveSvg from './images/wave.svg';

import css from './DetectedInTimeBannerV2.css';

const DetectedInTimeBannerV2: React.FC = () => {
    const isMobile = useMediaQuery({ maxWidth: MEDIA_WIDTH.tablet });
    const { setIsClose } = useDetectedInTimeBannerV2Context();

    useEffect(() => {
        // Track the banner shown event
        eventTracking.sendPromoBannerToGTMV4({
            action: 'shown',
            campaign: 'detected_in_time',
        });
    }, []);

    const handleClick = () => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'click',
            campaign: 'detected_in_time',
        });
    };

    const handleClose = (event: React.MouseEvent) => {
        eventTracking.sendPromoBannerToGTMV4({
            action: 'closed_manual',
            campaign: 'detected_in_time',
        });
        setIsClose();
        event.stopPropagation();
    };

    if (isMobile) {
        return (
            <a
                className={css.mobileContainer}
                href="https://send.monobank.ua/jar/5zqUFiMEPF"
                target="_blank"
                rel="noopener noreferrer"
                onClick={handleClick}
            >
                <div className={css.cross} onClick={handleClose}>
                    <Icon glyph={CrossSvg} />
                </div>
                <FlexBox justify="space-between" align="center">
                    <p>
                        <span style={{ color: '#F44F36' }}>
                            Задонать від 200 грн.
                        </span>{' '}
                        на комплекси <br /> спостереження та бери участь <br />{' '}
                        <b> у розіграші Iphone 16</b>
                    </p>
                    <div className={css.iconMobile}>
                        <Icon glyph={ArrowRightSvg} />
                    </div>
                </FlexBox>
            </a>
        );
    }

    return (
        <div className={css.container}>
            <div className={css.cross} onClick={handleClose}>
                <Icon glyph={CrossSvg} />
            </div>
            <div className={css.wave}>
                <Icon glyph={WaveSvg} />
            </div>
            <div className={css.technique} />
            <FlexBox
                justify="space-between"
                style={{ position: 'relative', zIndex: 3 }}
            >
                <FlexBox gap={20} direction="column">
                    <div className={css.title}>
                        <h5>Вчасно виявлено</h5>
                    </div>
                    <FlexBox
                        direction="column"
                        gap={20}
                        className={css.description}
                    >
                        <p>
                            <span style={{ color: '#F44F36' }}>
                                Задонать від 200 грн.
                            </span>{' '}
                            на <br /> комплекси спостереження та <br /> бери
                            участь <b> у розіграші Iphone 16</b>
                        </p>
                    </FlexBox>
                </FlexBox>
                <FlexBox gap={27} direction="column" align="center">
                    <div className={css.icon}>
                        <Icon glyph={LogoSvg} />
                    </div>
                    <a
                        className={css.buttonWrapper}
                        href="https://send.monobank.ua/jar/5zqUFiMEPF"
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={handleClick}
                    >
                        <Button wide>Задонатити</Button>
                    </a>
                </FlexBox>
            </FlexBox>
        </div>
    );
};

export default DetectedInTimeBannerV2;
