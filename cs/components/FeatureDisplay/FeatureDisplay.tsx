import React from 'react';
import { useSelector } from 'react-redux';

import { getAppFlags } from 'selectors/app.selectors';

export type Feature =
    | string
    | 'APPLE_AUTH_BUTTON'
    | 'DEMINING_BANNER'
    | 'PAPERLESS_BANNER'
    | 'CSAT_ARCHIVE_SCAN_RECOGNITION_BY_AI'
    | 'NEW_EDIT_DOCUMENT_UI'
    | 'ENABLE_INACTIVE_UI_EFFECT'
    | 'NEW_MULTI_EDIT_DOCUMENT_UI'
    | 'COLLABORA_MARKERS'
    | 'COLLABORA_VIEWER'
    // https://vchasno-group.atlassian.net/wiki/spaces/vchasno/pages/4954735
    | 'DOCUMENTS_PRIVATE_ACCESS'
    | 'POSTHOG_SR_ENABLED'
    | 'COLLABORA_XLS_TEMPLATE_CREATION'
    | 'ENABLE_NEW_UPLOAD_FLOW'
    | 'ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS'
    | 'ENABLE_AUTOMATION_TEMPLATE_FORM_UX_2025'
    | 'ENABLE_DOCUMENT_PAGE_UX_2025'
    | 'ENABLE_DIIA_ECDSA'
    | 'ENABLE_TTN_SERVICE_REGISTRATION'
    | 'AI_DOCUMENT_SUMMARY_BLOCK'
    | 'ENABLE_CONVERT_OFFICE_TO_PDF';

export interface FeatureProps {
    feature: Feature;
}

export const OnlyFocusWindow: React.FC = ({ children }) => {
    const [isFocused, setIsFocused] = React.useState(true);

    React.useEffect(() => {
        const onFocus = () => setIsFocused(true);
        const onBlur = () => setIsFocused(false);

        window.addEventListener('focus', onFocus);
        window.addEventListener('blur', onBlur);

        return () => {
            window.removeEventListener('focus', onFocus);
            window.removeEventListener('blur', onBlur);
        };
    }, []);

    if (!isFocused) {
        return null;
    }

    return <>{children}</>;
};

export const FeatureShow: React.FC<React.PropsWithChildren<FeatureProps>> = ({
    feature,
    children,
}) => {
    const featureOn = useSelector(getAppFlags)[feature];

    if (featureOn) {
        return <>{children}</>;
    }

    return null;
};

export const FeatureHide: React.FC<React.PropsWithChildren<FeatureProps>> = ({
    feature,
    children,
}) => {
    const featureOff = !useSelector(getAppFlags)[feature];

    if (featureOff) {
        return <>{children}</>;
    }

    return null;
};
