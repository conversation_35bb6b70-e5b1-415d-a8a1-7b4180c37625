import { formatDate } from 'lib/date';
import { formatFullName } from 'lib/helpers';
import { t } from 'ttag';
import { IRole, RoleActivationSource, roleActivationSource } from 'types/user';

import { OrderType } from './SortedTableHeaderItem/SortedTableHeaderItem';

export const getActivationSourceLabel = (
    activationSource: RoleActivationSource,
) => {
    switch (activationSource) {
        case roleActivationSource.signature:
            return t`Самостійна реєстрація`;
        case roleActivationSource.invite:
            return t`Запрошення від колеги`;
        case roleActivationSource.sync_role:
            return t`Синхронізація з ролями`;
        case roleActivationSource.restore:
            return t`Відновлення від колеги`;
        case roleActivationSource.super_admin:
            return t`Активація суперадміном`;
        default:
            return t`відсутні дані`;
    }
};

export const getDateLabel = (employee: IRole) => {
    if (
        employee.dateActivated &&
        employee.activationSource === roleActivationSource.restore
    ) {
        return `${formatDate(
            employee.dateCreated,
        )} (${t`відновлено`}: ${formatDate(employee.dateActivated)})`;
    }

    return formatDate(employee.dateCreated);
};

export const getActivatedByLabel = (employee: IRole, companyRoles: IRole[]) => {
    if (!employee?.activationSource) {
        return t`відсутні дані`;
    }

    const registrationTypeLabel = getActivationSourceLabel(
        employee.activationSource,
    );

    const activatedBy = companyRoles.find(
        (role) => role.id === employee.activatedBy,
    );

    if (activatedBy !== undefined) {
        const activatedByFullName = formatFullName(activatedBy.user);
        const activatedByEmail = activatedBy.user.email;

        return `${registrationTypeLabel} - ${activatedByFullName} (${activatedByEmail})`;
    }

    return registrationTypeLabel;
};

export const sortEmployeesByIsAdmin = (
    employees: IRole[],
    order: OrderType,
): IRole[] => {
    const adminUsers = employees.filter((user) => user?.isAdmin);
    const nonAdminUsers = employees.filter((user) => !user?.isAdmin);

    if (order === 'asc') {
        return [...adminUsers, ...nonAdminUsers];
    }
    if (order === 'desc') {
        return [...nonAdminUsers, ...adminUsers];
    }

    return employees;
};

export const sortEmployeesByDate = (
    employees: IRole[],
    order: OrderType,
): IRole[] => {
    return employees.toSorted((a, b) => {
        const _a = a.dateActivated || a.dateCreated;
        const _b = b.dateActivated || b.dateCreated;

        return (
            Number(new Date(order === 'asc' ? _b : _a)) -
            Number(new Date((order === 'asc' ? _a : _b)!))
        );
    });
};

export const sortEmployeesByFullName = (
    employees: IRole[],
    order: OrderType,
): IRole[] => {
    return employees.toSorted((a, b) => {
        const _a = formatFullName(a.user);
        const _b = formatFullName(b.user);

        return order === 'asc' ? _a.localeCompare(_b) : _b.localeCompare(_a);
    });
};

export const sortEmployeesByIdentifier = (
    employees: IRole[],
    order: OrderType,
): IRole[] => {
    return employees.toSorted((a, b) => {
        const _a = a.user?.email || a.user?.authPhone || '';
        const _b = b.user?.email || b.user?.authPhone || '';

        return order === 'asc' ? _a.localeCompare(_b) : _b.localeCompare(_a);
    });
};

export const sortEmployeesByRegistrationType = (
    employees: IRole[],
    order: OrderType,
): IRole[] => {
    return employees.toSorted((a, b) => {
        const _a = a.activationSource
            ? getActivationSourceLabel(a.activationSource)
            : '';
        const _b = b.activationSource
            ? getActivationSourceLabel(b.activationSource)
            : '';

        return order === 'asc' ? _a.localeCompare(_b) : _b.localeCompare(_a);
    });
};
