import React, { FC } from 'react';
import { useSelector } from 'react-redux';

import { formatDate } from 'lib/date';
import { formatUserIdentifier } from 'lib/ts/helpers';
import { getCurrentUserRoleId } from 'selectors/app.selectors';
import {
    getCompanyRoles,
    getEmployeesByStatusAndTags,
} from 'selectors/companyEmployees.selectors';
import { RoleStatuses } from 'services/enums';
import { t } from 'ttag';

import { StoreState } from '../../../../../../../types/store';

import Table from '../../../../../../ui/table/table';

import CollapsedText from '../../../../../../CollapsedText/CollapsedText';
import EmptyList from './EmptyList/EmptyList';
import SlicePagination from './SlicePagination';

import css from '../EmployeesList.css';

const COLUMNS_SIZES = ['33%', '33%', '33%'];

export interface PendingEmployeesListProps {
    isAdminPage?: boolean;
    companyId?: string;
    limit: number;
    page: number;
}

const PendingEmployeesList: FC<PendingEmployeesListProps> = ({
    isAdminPage,
    companyId,
    limit,
    page,
}) => {
    const companyRoles = Array.from(useSelector(getCompanyRoles));
    const invitedEmployees = useSelector((state: StoreState) =>
        getEmployeesByStatusAndTags(state, RoleStatuses.INVITED),
    );
    const currentRoleId = useSelector(getCurrentUserRoleId);

    const formattedFilteredActiveEmployees = invitedEmployees.map(
        (employee) => ({
            id: employee.id,
            link: isAdminPage
                ? `${location.pathname}/${employee.user?.id || ''}`
                : `/app/settings/companies/${currentRoleId}/employees/${employee.id}`,
            items: {
                identifier: (
                    <CollapsedText className={css.link}>
                        {formatUserIdentifier(employee?.user)}
                    </CollapsedText>
                ),
                invitedAt: employee?.dateInvited
                    ? formatDate(employee.dateInvited)
                    : t`відсутні дані`,
                invitedBy: employee?.invitedBy
                    ? formatUserIdentifier(
                          companyRoles.find(
                              (role) => role.id === employee.invitedBy,
                          )?.user,
                      )
                    : t`відсутні дані`,
            },
        }),
    );

    const total = Math.ceil(formattedFilteredActiveEmployees.length / limit);

    const slicedData =
        total > 1
            ? formattedFilteredActiveEmployees.slice(
                  (page - 1) * limit,
                  page * limit,
              )
            : formattedFilteredActiveEmployees;

    return (
        <>
            <Table
                headerItems={[
                    t`Email/Телефон`,
                    t`Дата запрошення`,
                    t`Ким запрошений`,
                ]}
                data={slicedData}
                sizes={COLUMNS_SIZES}
                EmptyList={<EmptyList companyId={companyId} />}
                isFullWidth
            />
            <SlicePagination total={total} />
        </>
    );
};

export default PendingEmployeesList;
