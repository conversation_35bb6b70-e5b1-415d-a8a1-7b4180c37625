import React, { FC, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { formatFullName } from 'lib/helpers';
import { formatUserIdentifier } from 'lib/ts/helpers';
import { getCurrentUserRoleId } from 'selectors/app.selectors';
import {
    getCanEditTagsAccess,
    getUnActiveEmployees,
} from 'selectors/companyEmployees.selectors';
import { RoleStatuses } from 'services/enums';
import { t } from 'ttag';

import { Nullable } from '../../../../../../../types/general';
import { StoreState } from '../../../../../../../types/store';
import { IRole } from '../../../../../../../types/user';

import actions from '../../../../../../companyEmployees/companyEmployeesActionCreators';

import Table from '../../../../../../ui/table/table';

import CollapsedText from '../../../../../../CollapsedText/CollapsedText';
import TagsAccessPopover from '../../../../../../tagsAccessPopover/tagsAccessPopover';
import EmptyList from './EmptyList/EmptyList';
import SlicePagination from './SlicePagination';
import SortedTableHeaderItem, {
    OrderType,
} from './SortedTableHeaderItem/SortedTableHeaderItem';
import UnActiveReason from './UnActiveReason';
import UnActiveRestoreBtn from './UnActiveRestoreBtn';

import css from '../EmployeesList.css';

const COLUMNS_SIZES = ['28%', '28%', '10%', '30%', '4%'];

export interface UnActiveEmployeesListProps {
    isAdminPage?: boolean;
    companyId?: string;
    limit: number;
    page: number;
}

const getStatus = (status: string) => {
    if (status === RoleStatuses.PENDING) {
        return t`Запрошений`;
    }
    if (status === RoleStatuses.DELETED) {
        return t`Видалений`;
    }
    if (status === RoleStatuses.BLOCKED_2FA) {
        return t`Заблокований`;
    }

    return '';
};

const UnActiveEmployeesList: FC<UnActiveEmployeesListProps> = ({
    isAdminPage,
    companyId,
    limit,
    page,
}) => {
    const dispatch = useDispatch();
    const [isStatusSortOrderType, setIsStatusSortOrderType] = useState<
        Nullable<OrderType>
    >(null);
    const [restoreEmployeeError, setRestoreEmployeeError] = useState('');
    const currentRoleId = useSelector(getCurrentUserRoleId);
    const unActiveEmployees = useSelector(getUnActiveEmployees);
    const canEditTagsAccess = useSelector((state: StoreState) =>
        getCanEditTagsAccess(state, companyId as never),
    );

    const onResetSort = () => setIsStatusSortOrderType(null);

    const onSortEmployeesByStatus = (employees: IRole[]) => {
        if (!isStatusSortOrderType) {
            return employees;
        }

        if (isStatusSortOrderType === 'desc') {
            return employees
                .slice()
                .sort((a, b) =>
                    getStatus(a.status).toLowerCase() >
                    getStatus(b.status).toLowerCase()
                        ? -1
                        : 1,
                );
        }

        return employees
            .slice()
            .sort((a, b) =>
                getStatus(a.status).toLowerCase() <
                getStatus(b.status).toLowerCase()
                    ? -1
                    : 1,
            );
    };

    const formattedFilteredPendingEmployees = onSortEmployeesByStatus(
        unActiveEmployees,
    ).map((employee) => ({
        id: employee.id,
        link: isAdminPage
            ? `${location.pathname}/${employee.user?.id || ''}`
            : `/app/settings/companies/${currentRoleId}/employees/${employee.id}`,
        items: {
            fullName: employee?.user ? formatFullName(employee.user) : '',
            identifier: (
                <CollapsedText className={css.link}>
                    {formatUserIdentifier(employee?.user)}
                </CollapsedText>
            ),
            status: getStatus(employee.status),
            reason: <UnActiveReason employee={employee} />,
            restoreBtn: (
                <UnActiveRestoreBtn
                    employee={employee}
                    companyId={companyId}
                    setRestoreEmployeeError={setRestoreEmployeeError}
                />
            ),
            tags:
                employee?.tags?.length && canEditTagsAccess ? (
                    <TagsAccessPopover
                        roleId={employee.id}
                        tags={employee.tags}
                        onOpenTagsAccessPopup={() =>
                            dispatch(actions.onOpenTagsAccessPopup(employee))
                        }
                        onDeleteTagFromList={(tagId, roleId) =>
                            dispatch(actions.onDeleteTagFromList(tagId, roleId))
                        }
                        tagTypeContour
                    />
                ) : null,
        },
    }));

    const total = Math.ceil(formattedFilteredPendingEmployees.length / limit);

    const slicedData =
        total > 1
            ? formattedFilteredPendingEmployees.slice(
                  (page - 1) * limit,
                  page * limit,
              )
            : formattedFilteredPendingEmployees;

    return (
        <>
            {restoreEmployeeError && (
                <div className={css.errorMessage}>{restoreEmployeeError}</div>
            )}
            <Table
                headerItems={[
                    t`Прізвище, Ім’я, По батькові`,
                    t`Email/Телефон`,
                    <SortedTableHeaderItem
                        title={t`Статус`}
                        orderType={isStatusSortOrderType}
                        onSort={setIsStatusSortOrderType}
                        onReset={onResetSort}
                    />,
                    t`Причина`,
                    '',
                ]}
                data={slicedData}
                sizes={COLUMNS_SIZES}
                EmptyList={<EmptyList companyId={companyId} />}
                isFullWidth
            />
            <SlicePagination total={total} />
        </>
    );
};

export default UnActiveEmployeesList;
