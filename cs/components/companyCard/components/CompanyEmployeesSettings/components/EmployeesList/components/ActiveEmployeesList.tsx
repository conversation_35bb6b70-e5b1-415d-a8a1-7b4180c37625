import React, { FC, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Text } from '@vchasno/ui-kit';

import { getEmployeeAdminTitle } from 'components/RoleAccessList/utils';
import { formatFullName } from 'lib/helpers';
import { formatUserIdentifier } from 'lib/ts/helpers';
import { getCurrentUserRoleId } from 'selectors/app.selectors';
import {
    getCanEditTagsAccess,
    getCompanyRoles,
    getEmployeesByStatusAndTags,
} from 'selectors/companyEmployees.selectors';
import { RoleStatuses } from 'services/enums';
import { t } from 'ttag';
import { StoreState } from 'types/store';
import { IRole } from 'types/user';

import actions from '../../../../../../companyEmployees/companyEmployeesActionCreators';

import {
    getActivatedByLabel,
    getDateLabel,
    sortEmployeesByDate,
    sortEmployeesByFullName,
    sortEmployeesByIdentifier,
    sortEmployeesByIsAdmin,
    sortEmployeesByRegistrationType,
} from './utils';

import Table from '../../../../../../ui/table/table';

import TagsAccessPopover from '../../../../../../tagsAccessPopover/tagsAccessPopover';
import EmptyList from './EmptyList/EmptyList';
import SlicePagination from './SlicePagination';
import SortedTableHeaderItem, {
    OrderType,
} from './SortedTableHeaderItem/SortedTableHeaderItem';

import css from '../EmployeesList.css';

const COLUMNS_SIZES = ['25%', '25%', '15%', '17%', '15%', '3%'];

export interface ActiveEmployeesListProps {
    isAdminPage?: boolean;
    companyId?: string;
    limit: number;
    page: number;
}

type SortedEmployeesColumn =
    | 'isAdmin'
    | 'date'
    | 'name'
    | 'identifier'
    | 'registrationType'
    | null;

interface SortedEmployees {
    column: SortedEmployeesColumn;
    order: OrderType;
}

const ActiveEmployeesList: FC<ActiveEmployeesListProps> = ({
    isAdminPage,
    companyId,
    limit,
    page,
}) => {
    const [sortedEmployees, setSortedEmployees] = useState<SortedEmployees>({
        column: null,
        order: 'asc',
    });

    const dispatch = useDispatch();

    const companyRoles = Array.from(useSelector(getCompanyRoles));
    const filteredActiveEmployees = useSelector((state: StoreState) =>
        getEmployeesByStatusAndTags(state, RoleStatuses.ACTIVE_REGISTERED),
    );
    const currentRoleId = useSelector(getCurrentUserRoleId);
    const canEditTagsAccess = useSelector((state: StoreState) =>
        getCanEditTagsAccess(state, companyId as never),
    );

    const onSortEmployees = (employees: IRole[]) => {
        switch (sortedEmployees.column) {
            case 'isAdmin':
                return sortEmployeesByIsAdmin(employees, sortedEmployees.order);
            case 'date':
                return sortEmployeesByDate(employees, sortedEmployees.order);
            case 'name':
                return sortEmployeesByFullName(
                    employees,
                    sortedEmployees.order,
                );
            case 'identifier':
                return sortEmployeesByIdentifier(
                    employees,
                    sortedEmployees.order,
                );
            case 'registrationType':
                return sortEmployeesByRegistrationType(
                    employees,
                    sortedEmployees.order,
                );
            default:
                return employees;
        }
    };

    const resetSortOrder = () => {
        setSortedEmployees({ column: null, order: 'asc' });
    };

    const formattedFilteredActiveEmployees = onSortEmployees(
        filteredActiveEmployees,
    ).map((employee) => ({
        id: employee.id,
        link: isAdminPage
            ? `${location.pathname}/${employee.user?.id || ''}`
            : `/app/settings/companies/${currentRoleId}/employees/${employee.id}`,
        items: {
            fullName: (
                <Text ellipsis>
                    {employee?.user ? formatFullName(employee.user) : ''}
                    {employee.id === currentRoleId && (
                        <span className={css.currentUserInTable}>
                            ({t`Ви`})
                        </span>
                    )}
                </Text>
            ),
            identifier: (
                <Text ellipsis type="likeLink">
                    {formatUserIdentifier(employee.user)}
                </Text>
            ),
            isAdmin: <Text ellipsis>{getEmployeeAdminTitle(employee)}</Text>,
            date: <Text>{getDateLabel(employee)}</Text>,
            registrationType: (
                <Text>{getActivatedByLabel(employee, companyRoles)}</Text>
            ),
            tags:
                employee?.tags?.length && canEditTagsAccess ? (
                    <TagsAccessPopover
                        roleId={employee.id}
                        tags={employee.tags}
                        onOpenTagsAccessPopup={() =>
                            dispatch(actions.onOpenTagsAccessPopup(employee))
                        }
                        onDeleteTagFromList={(tagId, roleId) =>
                            dispatch(actions.onDeleteTagFromList(tagId, roleId))
                        }
                        tagTypeContour
                    />
                ) : null,
        },
    }));

    const makeToggleSortOrder = (column: SortedEmployeesColumn) => () =>
        setSortedEmployees((prev) => {
            if (prev.column === column) {
                return { column, order: prev.order === 'asc' ? 'desc' : 'asc' };
            }

            return { column, order: 'asc' };
        });

    const makeGetOrderType = (column: SortedEmployeesColumn) =>
        sortedEmployees.column === column ? sortedEmployees.order : null;

    const total = Math.ceil(formattedFilteredActiveEmployees.length / limit);

    const slicedData =
        total > 1
            ? formattedFilteredActiveEmployees.slice(
                  (page - 1) * limit,
                  page * limit,
              )
            : formattedFilteredActiveEmployees;

    return (
        <>
            <Table
                headerItems={[
                    <SortedTableHeaderItem
                        title={t`Прізвище, Ім’я, По батькові`}
                        orderType={makeGetOrderType('name')}
                        onSort={makeToggleSortOrder('name')}
                        onReset={resetSortOrder}
                    />,
                    <SortedTableHeaderItem
                        title={t`Email/Телефон`}
                        orderType={makeGetOrderType('identifier')}
                        onSort={makeToggleSortOrder('identifier')}
                        onReset={resetSortOrder}
                    />,
                    <SortedTableHeaderItem
                        title={t`Статус`}
                        orderType={makeGetOrderType('isAdmin')}
                        onSort={makeToggleSortOrder('isAdmin')}
                        onReset={resetSortOrder}
                    />,
                    <SortedTableHeaderItem
                        title={t`Дата`}
                        orderType={makeGetOrderType('date')}
                        onSort={makeToggleSortOrder('date')}
                        onReset={resetSortOrder}
                    />,
                    <SortedTableHeaderItem
                        title={t`Тип реєстрації`}
                        orderType={makeGetOrderType('registrationType')}
                        onSort={makeToggleSortOrder('registrationType')}
                        onReset={resetSortOrder}
                    />,
                    '',
                ]}
                data={slicedData}
                sizes={COLUMNS_SIZES}
                EmptyList={<EmptyList companyId={companyId} />}
                isFullWidth
            />
            <SlicePagination total={total} />
        </>
    );
};

export default ActiveEmployeesList;
