import React, { FC, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import appActionCreators from 'components/app/appActionCreators';
import { formatDate } from 'lib/date';
import { formatFullName } from 'lib/helpers';
import { formatUserIdentifier } from 'lib/ts/helpers';
import {
    getCurrentCompany,
    getCurrentUserRoleId,
    getExpiresActivePayedWebRate,
    getIsActiveRateEqualOrGreaterExpiredMostExpensive,
} from 'selectors/app.selectors';
import {
    getCanEditTagsAccess,
    getEmployeesByStatusAndTags,
} from 'selectors/companyEmployees.selectors';
import { ACCOUNT_RATE_TITLE_MAP } from 'services/billing';
import { RoleStatuses } from 'services/enums';
import { getCompany, restoreRole } from 'services/user';
import { t } from 'ttag';
import PseudoLink from 'ui/pseudolink/pseudolink';
import Table from 'ui/table/table';

import { StoreState } from '../../../../../../../types/store';
import { IRole } from '../../../../../../../types/user';

import actions from '../../../../../../companyEmployees/companyEmployeesActionCreators';

import CollapsedText from '../../../../../../CollapsedText/CollapsedText';
import TagsAccessPopover from '../../../../../../tagsAccessPopover/tagsAccessPopover';
import EmptyList from './EmptyList/EmptyList';
import SlicePagination from './SlicePagination';
import SortedTableHeaderItem, {
    OrderType,
} from './SortedTableHeaderItem/SortedTableHeaderItem';

import css from '../EmployeesList.css';

const COLUMNS_SIZES = ['35%', '35%', '20%', '10%'];

export interface BlockedEmployeesListProps {
    isAdminPage?: boolean;
    companyId?: string;
    limit: number;
    page: number;
}

const BlockedEmployeesList: FC<BlockedEmployeesListProps> = ({
    isAdminPage,
    companyId,
    limit,
    page,
}) => {
    const dispatch = useDispatch();
    const [isAdminSortOrderType, setIsAdminSortOrderType] = useState<
        Nullable<OrderType>
    >(null);
    const [restoreEmployeeError, setRestoreEmployeeError] = useState('');

    const filteredBlockedEmployees = useSelector((state: StoreState) =>
        getEmployeesByStatusAndTags(state, RoleStatuses.RATE_DELETED),
    );
    const currentCompany = useSelector(getCurrentCompany);
    const expiresActivePayedWebRate = useSelector(getExpiresActivePayedWebRate);
    const isActiveRateEqualOrGreaterExpiredMostExpensive = useSelector(
        getIsActiveRateEqualOrGreaterExpiredMostExpensive,
    );
    const currentRoleId = useSelector(getCurrentUserRoleId);
    const canEditTagsAccess = useSelector((state: StoreState) =>
        getCanEditTagsAccess(state, companyId as never),
    );

    const onResetSort = () => setIsAdminSortOrderType(null);

    const onClickRestoreButton = async (
        employee: IRole,
        event: React.MouseEvent,
    ) => {
        event.preventDefault();

        try {
            await restoreRole(employee.id);
            const company = await getCompany(companyId);

            const roles = company.roles.toJS();

            dispatch(
                appActionCreators.updateCompanyState({
                    payload: {
                        roles: [...roles],
                    },
                }),
            );

            dispatch(actions.setEmployees(company.roles));
        } catch (error) {
            if (isActiveRateEqualOrGreaterExpiredMostExpensive) {
                setRestoreEmployeeError(
                    t`Досягнуто максимальної кількості співробітників згідно поточного тарифу`,
                );
                return;
            }

            if (!expiresActivePayedWebRate) {
                setRestoreEmployeeError(
                    t`Щоб відновити співробітників, сплатіть, будь ласка тариф. Після сплати тарифу співробітники автоматично повернуться в компанію.`,
                );
                return;
            }

            const formattedDate = formatDate(expiresActivePayedWebRate.endDate);
            const rateNameDisplay =
                ACCOUNT_RATE_TITLE_MAP[expiresActivePayedWebRate.rate as never];

            setRestoreEmployeeError(
                t`${formattedDate} спливає термін дії тарифу ${rateNameDisplay} в компанії ${currentCompany.name}. Щоб відновити співробітників, сплатіть, будь ласка тариф. Після сплати тарифу співробітники автоматично повернуться в компанію.`,
            );
        }
    };

    const onSortEmployeesByIsAdmin = (employees: IRole[]) => {
        if (!isAdminSortOrderType) {
            return employees;
        }

        const adminRoles = employees.filter((role) => role?.isAdmin);
        const nonAdminRoles = employees.filter((role) => !role?.isAdmin);

        if (isAdminSortOrderType === 'asc') {
            return [...adminRoles, ...nonAdminRoles];
        }
        if (isAdminSortOrderType === 'desc') {
            return [...nonAdminRoles, ...adminRoles];
        }

        return employees;
    };

    const formattedFilteredDeletedEmployees = onSortEmployeesByIsAdmin(
        filteredBlockedEmployees,
    ).map((employee) => ({
        id: employee.id,
        link: isAdminPage
            ? `${location.pathname}/${employee.user?.id || ''}`
            : `/app/settings/companies/${currentRoleId}/employees/${employee.id}`,
        items: {
            fullName: employee?.user ? formatFullName(employee.user) : '',
            identifier: (
                <CollapsedText className={css.link}>
                    {formatUserIdentifier(employee?.user)}
                </CollapsedText>
            ),
            status: employee.isAdmin && t`Адміністратор`,
            restoreBtn: (
                <PseudoLink
                    type="underlined"
                    className={css.rightPosition}
                    onClick={(event) => onClickRestoreButton(employee, event)}
                >
                    {t`Відновити`}
                </PseudoLink>
            ),
            tags:
                employee?.tags?.length && canEditTagsAccess ? (
                    <TagsAccessPopover
                        roleId={employee.id}
                        tags={employee.tags}
                        onOpenTagsAccessPopup={() =>
                            dispatch(actions.onOpenTagsAccessPopup(employee))
                        }
                        onDeleteTagFromList={(tagId, roleId) =>
                            dispatch(actions.onDeleteTagFromList(tagId, roleId))
                        }
                        tagTypeContour
                    />
                ) : null,
        },
    }));

    const total = Math.ceil(formattedFilteredDeletedEmployees.length / limit);

    const slicedData =
        total > 1
            ? formattedFilteredDeletedEmployees.slice(
                  (page - 1) * limit,
                  page * limit,
              )
            : formattedFilteredDeletedEmployees;

    return (
        <>
            {restoreEmployeeError && (
                <div className={css.errorMessage}>{restoreEmployeeError}</div>
            )}
            <Table
                headerItems={[
                    t`Прізвище, Ім’я, По батькові`,
                    t`Email/Телефон`,
                    <SortedTableHeaderItem
                        title={t`Статус`}
                        orderType={isAdminSortOrderType}
                        onSort={setIsAdminSortOrderType}
                        onReset={onResetSort}
                    />,
                    '',
                ]}
                data={slicedData}
                sizes={COLUMNS_SIZES}
                EmptyList={<EmptyList companyId={companyId} />}
                isFullWidth
            />
            <SlicePagination total={total} />
        </>
    );
};

export default BlockedEmployeesList;
