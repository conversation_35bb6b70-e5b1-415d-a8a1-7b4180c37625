import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import {
    getCurrentCompanyIsFop,
    getIsCompanyHasOnlyFreeRate,
} from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';

import { IS_CLOSE_KEY } from './constants';

export const useShow = () => {
    const isOnlyFreeRate = useSelector(getIsCompanyHasOnlyFreeRate);
    const isFOP = useSelector(getCurrentCompanyIsFop);
    const [isShow, setIsShow] = useState(() => {
        const isClose = getLocalStorageItem(IS_CLOSE_KEY);

        return isFOP && isOnlyFreeRate && !isClose;
    });

    useEffect(() => {
        if (isShow) {
            eventTracking.sendPromoPopupToGTMV4({
                action: 'shown',
                campaign: 'unlimit_sending_fop',
            });
        }
    }, [isShow]);

    const closePopUp = () => {
        eventTracking.sendPromoPopupToGTMV4({
            action: 'closed_manual',
            campaign: 'unlimit_sending_fop',
        });
        setIsShow(false);
    };

    useEffect(() => {
        setLocalStorageItem(IS_CLOSE_KEY, true);
    }, []);

    return { isShow, closePopUp };
};
