import React from 'react';

import { Button, Text, Title } from '@vchasno/ui-kit';

import { openInNewTab } from 'lib/navigation';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';
import Popup from 'ui/popup/popup';

import { useShow } from './useShow';

import css from './AlertUnlimitForFOP.css';

export const AlertUnlimitForFOP = () => {
    const { isShow, closePopUp } = useShow();

    return (
        <Popup active={isShow} onClose={closePopUp} className={css.rootPopup}>
            <div className={css.root}>
                <div className={css.image}>🎉</div>

                <Title
                    level={2}
                    className={css.title}
                >{t`Ми зняли обмеження на відправку документів`}</Title>

                <Text
                    style={{
                        fontSize: '14px',
                        fontWeight: 400,
                        lineHeight: '20px',
                        color: '#6B8091',
                    }}
                    className={css.text}
                >
                    {t`Відтепер тарифи «Старт» та «Професійний» для ФОП
без обмежень на відправку документів`}{' '}
                    😎
                </Text>

                <Button
                    onClick={() => {
                        eventTracking.sendPromoPopupToGTMV4({
                            action: 'click',
                            campaign: 'unlimit_sending_fop',
                        });
                        openInNewTab('/app/checkout-rates/web');
                    }}
                >{t`Переглянути тарифи`}</Button>
            </div>
        </Popup>
    );
};
