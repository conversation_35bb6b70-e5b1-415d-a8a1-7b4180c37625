import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { Datepicker, Input, TextInput } from '@vchasno/ui-kit';

import cn from 'classnames';
import DocCategorySelect from 'components/DocCategorySelect';
import { IDocCategorySelectOption } from 'components/DocCategorySelect/types';
import DocumentAmount from 'components/DocumentAmount/DocumentAmount';
import { useRecipientDisableSignCategoriesHandler } from 'components/DocumentsUploader/CounterpartySection/useRecipientDisableSignCategoriesHandler';
import { CounterpartyFields } from 'components/DocumentsUploader/types';
import { useCheckRequiredFieldsHandler } from 'components/DocumentsUploader/useCheckRequiredFieldsHandler';
import { useUploadDocumentsFormContext } from 'components/DocumentsUploader/useUploadDocumentsFormContext';
import { DocumentCategory } from 'gql-types';
import { usePrevious } from 'hooks/usePrevious';
import { getIsInternalDocCategory } from 'services/documentCategories/utils';
import { t } from 'ttag';

import { InfoForm } from './types';

import css from './DocumentInfo.css';

export interface DocumentInfoProps {
    disabledFields?: Partial<Record<keyof InfoForm, boolean>>;
    isForEdit?: boolean;
    preRequestCategoryDetails?: Nullable<DocumentCategory>;
}

const normalizeAmount = (amount?: string | number | null): string =>
    typeof amount === 'number' ? String(amount) : amount || '';

const DocumentInfo: React.FC<DocumentInfoProps> = ({
    disabledFields = {},
    isForEdit = false,
    preRequestCategoryDetails,
}) => {
    const formContext = useFormContext<
        InfoForm & Pick<CounterpartyFields, 'isInternal' | 'counterparties'>
    >();
    const uploadDocumentsFormContext = useUploadDocumentsFormContext();
    const recipientDisableSignCategoriesHandler = useRecipientDisableSignCategoriesHandler();
    const handleCheckRequiredFields = useCheckRequiredFieldsHandler();

    const isInternalDoc = formContext.watch('isInternal');
    const prevIsInternalDoc = usePrevious(isInternalDoc);
    const counterparties = formContext.watch('counterparties');

    const isOnlyPublicDocCategories = isForEdit
        ? !isInternalDoc
        : (prevIsInternalDoc && !isInternalDoc) || !!counterparties?.length;

    return (
        <div className={css.root}>
            <Controller
                control={formContext.control}
                name="title"
                render={({ field, fieldState }) => (
                    <TextInput
                        {...field}
                        disabled={disabledFields.title || field.disabled}
                        label={t`Назва`}
                        className={css.name}
                        error={fieldState.error?.message}
                    />
                )}
            />

            <Controller
                control={formContext.control}
                name="category"
                render={({ field, fieldState }) => (
                    <>
                        <DocCategorySelect
                            onlyPublic={isOnlyPublicDocCategories}
                            preRequestCategoryDetails={
                                preRequestCategoryDetails
                            }
                            isDisabled={
                                disabledFields.category || field.disabled
                            }
                            name={field.name}
                            onChange={(option: IDocCategorySelectOption) => {
                                if (
                                    !isForEdit &&
                                    getIsInternalDocCategory(option)
                                ) {
                                    uploadDocumentsFormContext.setValue(
                                        'isInternal',
                                        true,
                                    );
                                }
                                formContext.setValue(
                                    'category',
                                    option?.value || null,
                                    {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                    },
                                );

                                handleCheckRequiredFields();

                                recipientDisableSignCategoriesHandler();
                            }}
                            className={css.category}
                            error={fieldState.error?.message}
                            value={field.value as never}
                        />
                    </>
                )}
            />

            <Controller
                control={formContext.control}
                name="number"
                render={({ field, fieldState }) => (
                    <TextInput
                        {...field}
                        onChange={(event) => {
                            field.onChange(event.target.value);

                            handleCheckRequiredFields();
                        }}
                        disabled={disabledFields.number || field.disabled}
                        className={css.number}
                        label={t`Номер`}
                        startElement="№"
                        error={fieldState.error?.message}
                    />
                )}
            />

            <Controller
                control={formContext.control}
                name="date"
                render={({ field, fieldState }) => (
                    <Datepicker
                        isClearable
                        className={css.date}
                        disabled={disabledFields.date || field.disabled}
                        onChange={(date) => {
                            formContext.setValue('date', date, {
                                shouldValidate: true,
                                shouldDirty: true,
                            });

                            handleCheckRequiredFields();
                        }}
                        error={fieldState.error?.message}
                        selected={field.value}
                        label={t`Дата`}
                    />
                )}
            />

            <Controller
                control={formContext.control}
                name="amount"
                render={({ field, fieldState }) => (
                    <Input
                        disabled={disabledFields.amount || field.disabled}
                        className={cn('document-amount-input', css.amount)}
                        hideEmptyMeta
                        label={t`Сума`}
                        hint={t`Введіть параметр цифрами (100, 100.99)`}
                        error={fieldState.error?.message}
                    >
                        <DocumentAmount
                            disabled={disabledFields.amount || field.disabled}
                            value={normalizeAmount(field.value)}
                            placeholder=" "
                            onValueChange={(value) => {
                                field.onChange({
                                    target: {
                                        value: normalizeAmount(value) || null,
                                    },
                                });

                                handleCheckRequiredFields();
                            }}
                        />
                    </Input>
                )}
            />
        </div>
    );
};

export default DocumentInfo;
