import { t } from 'ttag';
import * as yup from 'yup';

import { Actor, Counterparty, CounterpartyFields } from '../types';

const counterpartySchema: yup.ObjectSchema<Counterparty> = yup.object({
    name: yup.string().trim().default('').optional(),
    edrpou: yup.string().trim().default('').required(),
    email: yup.string().trim().default('').optional().nullable(),
    shouldSign: yup.boolean().default(false).required(),
    isEmailHidden: yup.boolean().default(false).required(),
});

export const counterpartyFieldsSchema: yup.ObjectSchema<CounterpartyFields> = yup.object(
    {
        isVersioned: yup.boolean().default(false),
        isInternal: yup.boolean().default(false),
        isParallel: yup.boolean().default(false),
        counterparties: yup
            .array()
            .of<Counterparty>(counterpartySchema)
            .test(
                'requiredOneSigner',
                t`Серед учасників підписання має бути хоча б один, який підписує документ.`,
                (list) => {
                    if (!list || list.length === 0) {
                        return true;
                    }

                    return list.filter((item) => item.shouldSign).length > 0;
                },
            )
            .when((_, schema, options) => {
                const edrpou = options.parent?.companyEdrpou;
                const isParallel = options.parent?.isParallel;
                const isVersioned = options.parent?.isVersioned;
                const employeesSigners =
                    (options.parent?.employeesSigners as Actor[]) || [];
                const counterparties = (options.value as Counterparty[]) || [];
                const myCounterpartiesItems = counterparties.filter(
                    (item) => item.edrpou === edrpou,
                );

                if (
                    employeesSigners.length > 0 &&
                    myCounterpartiesItems.length > 0 &&
                    !myCounterpartiesItems.filter((item) => item.shouldSign)
                        .length
                ) {
                    return schema.test(
                        'requiredCompanyShouldSignWithEmployeeExists',
                        t`Ваша компанія має бути стороною підписання, якщо Ви вказали співробітників, що мають підписати документ`,
                        () => false,
                    );
                }

                if (
                    isVersioned &&
                    counterparties.length === 2 &&
                    counterparties.filter((item) => item.shouldSign).length !==
                        2
                ) {
                    return schema.test(
                        'requiredSignForVersioned',
                        t`Версійні документи обов'язково підписують обидві сторони`,
                        () => false,
                    );
                }

                if (
                    counterparties.length === 2 &&
                    !isParallel &&
                    counterparties[0].edrpou !== edrpou &&
                    !counterparties[0].shouldSign
                ) {
                    return schema.test(
                        'requiredFirstSignCounterparty',
                        t`Якщо документ першим опрацьовує отримувач - він має його підписувати`,
                        () => false,
                    );
                }

                return schema;
            })
            .default([])
            .required(),
    },
);
