import React from 'react';
import { useSelector } from 'react-redux';

import { BlackTooltip, FlexBox, Switch, Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import { useCheckRequiredFieldsHandler } from 'components/DocumentsUploader/useCheckRequiredFieldsHandler';
import { useUISectionsContext } from 'components/DocumentsUploader/useUISectionsContext';
import EditSVG from 'icons/pen.svg';
import { getCurrentCompany } from 'selectors/app.selectors';
import { t } from 'ttag';
import Icon from 'ui/icon';

import CloseIcon from '../CloseIcon';
import { useUploadDocumentsFormContext } from '../useUploadDocumentsFormContext';
import AddCounterpartyForm from './AddCounterpartyForm';
import { Badge } from './Badge';
import { useCounterpartyContext } from './CounterpartyContext';
import { useCounterpartyFieldArray } from './useCounerpartyFieldArray';

import ArrowDown from '../../../icons/arrow-down.svg';
import DragDotsDown from '../../../icons/drag-dots.svg';

import css from './CounterpartySection.css';

interface CounterpartyListItemProps {
    isSender: boolean;
    canClose: boolean;
    item: ReturnType<typeof useCounterpartyFieldArray>['fields'][0];
    index: number;
    counterpartiesFieldArray: ReturnType<typeof useCounterpartyFieldArray>;
    disabled?: boolean;
}

const CounterpartyListItem: React.FC<CounterpartyListItemProps> = ({
    isSender,
    item,
    index,
    counterpartiesFieldArray,
    canClose,
    disabled,
}) => {
    const [isEdit, setIsEdit] = React.useState(false);
    const counterpartyContext = useCounterpartyContext();
    const prevItem = counterpartiesFieldArray.fields[index - 1];
    const nextItem = counterpartiesFieldArray.fields[index + 1];
    const disabledActions = counterpartyContext.disabledActions(item);
    const isAlreadySigned = counterpartyContext.isSigned(item);
    const currentCompany = useSelector(getCurrentCompany);
    const methods = useUploadDocumentsFormContext();
    const [, { expandSection }] = useUISectionsContext();
    const handleCheckRequiredFields = useCheckRequiredFieldsHandler();
    const isVersioned = methods.watch('isVersioned');
    const isOrdered = !methods.watch('isParallel');
    const isDisabled = methods.formState.disabled || disabled;
    const allowChangeOrder =
        isOrdered && !isVersioned && !isDisabled && !disabledActions.order;
    const allowEditCounterparty =
        counterpartyContext.editable &&
        !isDisabled &&
        !isSender &&
        !isAlreadySigned;

    const isUpMoveDisabled =
        index === 0 ||
        (prevItem && counterpartyContext.disabledActions(prevItem).order);

    const isDownMoveDisabled =
        index === counterpartiesFieldArray.fields.length - 1 ||
        (nextItem && counterpartyContext.disabledActions(nextItem).order);

    if (isEdit) {
        return (
            <AddCounterpartyForm
                title={t`Редагувати контрагента`}
                autoFillEmail={false}
                submitButtonIcon="checked"
                defaultValues={{
                    edrpou: item.edrpou,
                    email: item.email ?? undefined,
                }}
                onClose={() => setIsEdit(false)}
                onAddCounterparty={(data) => {
                    if (data.edrpou !== methods.getValues('companyEdrpou')) {
                        counterpartiesFieldArray.update(index, {
                            ...item,
                            edrpou: data.edrpou,
                            email: data.email,
                            isEmailHidden: data.isHidden,
                            name: data.edrpou === item.edrpou ? item.name : '', // якщо змінився edrpou, то назву компанії стираємо
                        });
                    }
                    setIsEdit(false);
                }}
            />
        );
    }

    const removeCounterparty = async () => {
        if (
            !counterpartyContext.suppressAutoRemoveOwner &&
            // не залишаємо щоб у переліку компаній була тілки своя компанія
            counterpartiesFieldArray.fields
                .filter((_, i) => i !== index)
                .every(({ edrpou }) => edrpou === currentCompany.edrpou)
        ) {
            methods.setValue('counterparties', []);
        } else {
            counterpartiesFieldArray.remove(index);
        }

        const requiredFields = await handleCheckRequiredFields();
        // відкриваємо секцію з інформацією про документ,
        // якщо є обов'язкові поля в формі додавання документу
        if (requiredFields.length) {
            expandSection('documentInfo');
        }
    };

    return (
        <FlexBox
            className={cn(css.item, {
                [css.ordered]: allowChangeOrder,
                [css.sender]: isSender,
            })}
            gap={10}
            align="center"
        >
            <span className={css.dragIcon}>
                <Icon glyph={DragDotsDown} />
            </span>
            <span className={css.dragArea} data-movable-handle={true} />
            {allowEditCounterparty && (
                <BlackTooltip title={t`Змінити контрагента`}>
                    <button
                        type="button"
                        className={css.editIcon}
                        onClick={() => setIsEdit(true)}
                    >
                        <Icon glyph={EditSVG} />
                    </button>
                </BlackTooltip>
            )}
            {canClose && !isDisabled && !disabledActions.remove && (
                <BlackTooltip title={t`Видалити`}>
                    <CloseIcon
                        className={css.deleteIcon}
                        onClick={removeCounterparty}
                    />
                </BlackTooltip>
            )}

            <FlexBox
                direction="column"
                align="center"
                justify="center"
                gap={5}
                className={cn(css.orderContainer, {
                    [css.visible]: allowChangeOrder,
                })}
            >
                <button
                    disabled={isUpMoveDisabled}
                    type="button"
                    onClick={() => {
                        counterpartiesFieldArray.swap(index, index - 1);
                        methods.trigger('counterparties');
                    }}
                    className={cn(css.orderIcon, css.iconRotate, {
                        [css.disable]: isUpMoveDisabled,
                    })}
                >
                    <Icon glyph={ArrowDown} />
                </button>
                <span className={css.orderLabel}>{index + 1}</span>
                <button
                    disabled={isDownMoveDisabled}
                    type="button"
                    onClick={() => {
                        counterpartiesFieldArray.swap(index, index + 1);
                        methods.trigger('counterparties');
                    }}
                    className={cn(css.orderIcon, {
                        [css.disable]: isDownMoveDisabled,
                    })}
                >
                    <Icon glyph={ArrowDown} />
                </button>
            </FlexBox>
            <FlexBox wrap="wrap" grow={1} gap={10} align="center">
                <FlexBox direction="column" gap={5} grow={1}>
                    {isSender && (
                        <span
                            className={css.secondaryText}
                        >{t`Відправник`}</span>
                    )}
                    <FlexBox align="center">
                        <Text strong>{item.edrpou}</Text>
                        {item.name && <Text type="secondary">{item.name}</Text>}
                    </FlexBox>
                    <Text type="secondary" ellipsis>
                        {item.email || (isSender ? '' : t`Прихований email`)}
                    </Text>
                </FlexBox>
                <FlexBox shrink={0} className={css.switchContainer}>
                    {isAlreadySigned && (
                        <>
                            <Badge /> <Text>{t`Підписано`}</Text>
                        </>
                    )}
                    {!isAlreadySigned && (
                        <Switch
                            size="sm"
                            disabled={isDisabled || disabledActions.sign}
                            value={methods.getValues(
                                `counterparties.${index}.shouldSign` as const,
                            )}
                            onChange={(event) => {
                                methods.setValue(
                                    `counterparties.${index}.shouldSign` as const,
                                    event.target.checked,
                                    {
                                        shouldValidate: true,
                                        shouldDirty: true,
                                        shouldTouch: true,
                                    },
                                );
                                methods.trigger(`counterparties`);
                            }}
                            label={t`Підписує`}
                        />
                    )}
                </FlexBox>
            </FlexBox>
        </FlexBox>
    );
};

export default CounterpartyListItem;
