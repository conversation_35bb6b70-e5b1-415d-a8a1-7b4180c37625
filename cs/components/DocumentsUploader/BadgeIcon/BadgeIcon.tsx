import React from 'react';

import cn from 'classnames';
import Icon from 'ui/icon';

import AlarmIconSVG from '../../../icons/alarm.svg';
import BadgeIconSVG from '../../../icons/checked.svg';

import css from './BadgeIcon.css';

export interface BadgeIconProps {
    className?: string;
    type?: 'alarm';
    disabled?: boolean;
}

const iconByType = {
    alarm: AlarmIconSVG,
} as const;

const BadgeIcon: React.FC<BadgeIconProps> = ({ className, type, disabled }) => {
    return (
        <span
            className={cn(css.root, type && css[type], className, {
                [css.disabled]: disabled,
            })}
        >
            <Icon glyph={type ? iconByType[type] : BadgeIconSVG} />
        </span>
    );
};

export default BadgeIcon;
