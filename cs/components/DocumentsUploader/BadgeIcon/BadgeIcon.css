.root {
    display: inline-flex;
    width: 20px;
    height: 20px;
    align-items: center;
    justify-content: center;
    animation: showBounce 0.5s ease-in-out forwards;
    background-color: var(--green-color);
    border-radius: 50%;
}

.disabled {
    animation: none;
    background-color: var(--slate-grey-color);
}

.root svg {
    width: 90%;
    height: auto;
    color: var(--white-color);
}

.alarm {
    background-color: transparent;
}

.root .alarm svg {
    width: 100%;
    color: var(--primary-cta-color);
}

@keyframes showBounce {
    0% {
        transform: scale(0.5);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}
