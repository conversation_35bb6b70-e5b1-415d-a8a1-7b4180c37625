import { IS_CLOSED_ALERT } from 'components/AlertLimitationEmployeesBanner/constants';
import { getLocalStorageItem, setLocalStorageItem } from 'lib/webStorage';
import moment from 'moment';

import { ShowedStateItemProps, ShowedStateProps } from './types';

export const setDates = (endDateFreeRateString: string) => {
    const today = moment().set({
        hours: 0,
        minutes: 0,
        seconds: 0,
        milliseconds: 0,
    });
    const endDateTrial = moment(endDateFreeRateString)
        .add(1, 'day')
        .set({ hours: 0, minutes: 0, seconds: 0, milliseconds: 0 });

    return {
        today: today.toDate(),
        endDateFreeRate: endDateTrial.toDate(),
    };
};

export const setShowedStateToStorage = (
    edrpou: string,
    state: ShowedStateItemProps,
) => {
    const showedState = getLocalStorageItem(
        IS_CLOSED_ALERT,
    ) as ShowedStateProps;

    setLocalStorageItem(IS_CLOSED_ALERT, {
        ...showedState,
        [edrpou]: state,
    });
};
