import React, { FC } from 'react';
import { useSelector } from 'react-redux';
import MediaQuery from 'react-responsive';

import { Button, FlexBox, Text, Title } from '@vchasno/ui-kit';

import crossIcon from 'icons/cross.svg';
import { MEDIA_WIDTH } from 'lib/constants';
import { toMoment } from 'lib/date';
import { getLocale } from 'lib/i18n/utils';
import { openInNewTab } from 'lib/navigation';
import {
    getCompanyActiveFreeRate,
    isAdminSelector,
} from 'selectors/app.selectors';
import { jt, t } from 'ttag';
import Icon from 'ui/icon';

import { useShowAlertLimitationEmployeesBanner } from './hooks/useShowAlertLimitationEmployeesBanner';

import imageMan from './images/image_man.png';

import css from './AlertLimitationEmployeesBanner.css';

export const AlertLimitationEmployeesBanner: FC = () => {
    const activeFreeRate = useSelector(getCompanyActiveFreeRate);
    const isAdmin = useSelector(isAdminSelector);
    const {
        isShow,
        closeAlert,
        roleToRemove,
        isCurrentRoleMainRole,
    } = useShowAlertLimitationEmployeesBanner();

    if (!roleToRemove || !isShow) {
        return null;
    }

    const nameRemovePerson = roleToRemove ? (
        <strong>{roleToRemove.user.email}</strong>
    ) : (
        ''
    );

    const usersCountToDelete = <strong>{t`2 користувача`}</strong>;
    const rateName = <strong>{t`Базовий`}</strong>;
    const usersSave = <strong>{t`обох співробітників`}</strong>;
    const dateFormat = activeFreeRate ? (
        <strong>
            {toMoment(activeFreeRate.endDate)
                ?.locale(getLocale())
                .format('DD MMMM YYYY')}
        </strong>
    ) : (
        ''
    );
    const urlDoc = (
        <a
            href="https://help.vchasno.com.ua/yak-vydalyty-spivrobitnyka/"
            target="_blank"
        >
            {t`видалити зайвого співробітника`}
        </a>
    );
    const adminUrl = isAdmin
        ? jt`За потреби ви можете самостійно ${urlDoc}.`
        : '';
    const selfText = (
        <strong>
            {t`ваш доступ до цієї компанії буде
                    заблоковано`}
        </strong>
    );

    const alertText = isCurrentRoleMainRole
        ? jt`У вашій компанії додано ${usersCountToDelete}, тоді як ${rateName} тариф передбачає лише 1. Щоб зберегти доступ для ${usersSave}, необхідно перейти на платний тариф до ${dateFormat}. При несплаті тарифу, система автоматично вимкне доступ для останнього доданого користувача: ${nameRemovePerson}. ${adminUrl}`
        : jt`У вашій компанії додано 2 користувача, тоді як Базовий
            тариф передбачає лише 1. Щоб зберегти обидва облікові
            записи, пропонуємо перейти на платний тариф до ${dateFormat}. При несплаті
            тарифу, ${selfText}. ${adminUrl}`;

    const pickRateHandle = () => {
        openInNewTab(
            '/internal-api/free-rate-update/events?source=banner_free_update_click',
        );
    };

    return (
        <div className={css.wrapper}>
            <FlexBox gap={12} align="center">
                <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                    <div className={css.imageWrap}>
                        <img
                            src={imageMan}
                            alt="image man"
                            className={css.image}
                        />
                    </div>
                </MediaQuery>

                <div>
                    <FlexBox gap={12} align="center" className={css.titleWrap}>
                        <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                            <img
                                src={imageMan}
                                alt="image man"
                                className={css.image}
                            />
                        </MediaQuery>

                        <Title level={3} className={css.title}>
                            {t`Перевищено ліміт користувачів на вашому тарифі!`}
                        </Title>
                    </FlexBox>
                    <Text type="secondary">{alertText}</Text>
                </div>

                <MediaQuery minWidth={MEDIA_WIDTH.mobile + 1}>
                    <div className={css.buttonWrap}>
                        <Button
                            onClick={pickRateHandle}
                        >{t`Обрати тариф`}</Button>
                    </div>
                </MediaQuery>
            </FlexBox>

            <MediaQuery maxWidth={MEDIA_WIDTH.mobile}>
                <div className={css.mobileButtonWrap}>
                    <Button
                        wide
                        onClick={pickRateHandle}
                    >{t`Обрати тариф`}</Button>
                </div>
            </MediaQuery>

            <button className={css.crossIconBtn} onClick={closeAlert}>
                <Icon glyph={crossIcon} className={css.crossIcon} />
            </button>
        </div>
    );
};
