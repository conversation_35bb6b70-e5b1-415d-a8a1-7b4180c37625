import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useMutation, useQuery } from '@tanstack/react-query';

import actionCreators from 'components/app/appActionCreators';
import { getDifferenceInDays, isDateBefore } from 'lib/date';
import { CACHE_TIME_MS } from 'lib/queriesConstants';
import { getLocalStorageItem } from 'lib/webStorage';
import {
    getActiveEmployeesSelector,
    getCompanyActiveFreeRate,
    getCurrentCompany,
    getCurrentUserRole,
    getIsCompanyHasOnlyFreeRate,
} from 'selectors/app.selectors';
import { RoleStatuses } from 'services/enums';
import { sendFreeBannersEvents } from 'services/ts/user';
import { getCompanyRolesForConfigApi } from 'services/user';

import { IRole } from '../../../types/user';
import { ShowedStateItemProps } from '../types';

import { setDates, setShowedStateToStorage } from '../utils';

import {
    IS_CLOSED_ALERT,
    rememberDays,
    showedStateItemDefault,
} from '../constants';

export const useShowAlertLimitationEmployeesBanner = () => {
    const dispatch = useDispatch();

    const [isShow, setIsShow] = useState(false);
    const [isCurrentRoleMainRole, setIsCurrentRoleMainRole] = useState(false);
    const [roleToRemove, setRoleToRemove] = useState<IRole>();

    const isOnlyFreeRate = useSelector(getIsCompanyHasOnlyFreeRate);
    const currentCompany = useSelector(getCurrentCompany);
    const activeRoles = useSelector(getActiveEmployeesSelector);
    const currentRole = useSelector(getCurrentUserRole);
    const activeFreeRate = useSelector(getCompanyActiveFreeRate);

    const { today, endDateFreeRate } = setDates(activeFreeRate?.endDate);
    const daysLeft = getDifferenceInDays(endDateFreeRate, today);

    const { mutate: sendEvent } = useMutation({
        mutationFn: () => {
            return sendFreeBannersEvents('banner_free_update_view');
        },
        onSuccess: () => {
            dispatch(actionCreators.onRefreshCurrentUser());
        },
    });

    const { data: roles } = useQuery({
        queryKey: [currentCompany?.id],
        queryFn: async () => {
            return ((await getCompanyRolesForConfigApi(
                currentCompany?.id,
            )) as Array<IRole>).filter(
                (role) => role.status === RoleStatuses.ACTIVE,
            );
        },
        staleTime: CACHE_TIME_MS,
        enabled: isOnlyFreeRate && activeRoles.length > 1,
    });

    const closeAlert = () => {
        setIsShow(false);
        const isShowed =
            (getLocalStorageItem(IS_CLOSED_ALERT)?.[
                currentCompany?.edrpou
            ] as ShowedStateItemProps) || showedStateItemDefault;

        if (daysLeft <= rememberDays.fourth) {
            isShowed.isClosedOneDays = true;
            isShowed.isClosedSevenDays = true;
            isShowed.isClosedFourteenDays = true;
            isShowed.isClosedThirtyDays = true;
        } else if (daysLeft <= rememberDays.third) {
            isShowed.isClosedSevenDays = true;
            isShowed.isClosedFourteenDays = true;
            isShowed.isClosedThirtyDays = true;
        } else if (daysLeft <= rememberDays.second) {
            isShowed.isClosedFourteenDays = true;
            isShowed.isClosedThirtyDays = true;
        } else if (daysLeft <= rememberDays.first) {
            isShowed.isClosedThirtyDays = true;
        }

        setShowedStateToStorage(currentCompany.edrpou, isShowed);
    };

    useEffect(() => {
        setIsShow(false);
        setRoleToRemove(undefined);
    }, [currentRole.id]);

    useEffect(() => {
        if (!isOnlyFreeRate || activeRoles.length !== 2) {
            setIsShow(false);
            return;
        }

        const isShowed =
            (getLocalStorageItem(IS_CLOSED_ALERT)?.[
                currentCompany.edrpou
            ] as ShowedStateItemProps) || showedStateItemDefault;

        if (daysLeft <= rememberDays.fourth && !isShowed.isClosedOneDays) {
            isShowed.isClosedSevenDays = true;
            isShowed.isClosedFourteenDays = true;
            isShowed.isClosedThirtyDays = true;

            setIsShow(true);
        } else if (
            daysLeft <= rememberDays.third &&
            !isShowed.isClosedSevenDays
        ) {
            isShowed.isClosedFourteenDays = true;
            isShowed.isClosedThirtyDays = true;

            setIsShow(true);
        } else if (
            daysLeft <= rememberDays.second &&
            !isShowed.isClosedFourteenDays
        ) {
            isShowed.isClosedThirtyDays = true;

            setIsShow(true);
        } else if (
            daysLeft <= rememberDays.first &&
            !isShowed.isClosedThirtyDays
        ) {
            setIsShow(true);
        } else {
            setIsShow(false);
        }

        setShowedStateToStorage(currentCompany.edrpou, isShowed);
    }, [currentCompany?.edrpou]);

    useEffect(() => {
        if (roles?.length !== 2) {
            return;
        }

        const roleRemove = roles?.reduce(
            (previousRole: IRole, currentRoleIteration) => {
                if (currentRoleIteration.isAdmin && !previousRole.isAdmin) {
                    return previousRole;
                }

                if (!currentRoleIteration.isAdmin && previousRole.isAdmin) {
                    return currentRoleIteration;
                }

                if (
                    currentRoleIteration.isAdmin &&
                    previousRole.isAdmin &&
                    isDateBefore(
                        previousRole.dateCreated,
                        currentRoleIteration.dateCreated,
                    )
                ) {
                    return currentRoleIteration;
                }

                return previousRole;
            },
            roles[0],
        );

        setRoleToRemove(roleRemove);
        setIsCurrentRoleMainRole(roleRemove?.id !== currentRole?.id);
    }, [roles]);

    useEffect(() => {
        if (
            !currentRole.isFreeRateBannerShown &&
            roleToRemove &&
            isShow &&
            isOnlyFreeRate &&
            activeRoles.length === 2
        ) {
            sendEvent();
        }
    }, [isShow, roleToRemove?.id]);

    return {
        isShow,
        closeAlert,
        isCurrentRoleMainRole,
        roleToRemove,
    };
};
