.wrapper {
    position: relative;
    padding: 20px;
    margin: 20px 10px 10px 10px;
    background-color: #FFF6E4;
    border-radius: var(--border-radius);
}

.imageWrap {
    flex-shrink: 0;
}

.image {
    width: 71px;
}

.titleWrap {
    margin-bottom: 4px;
}

.title {
    color: #333;
}

.mobileButtonWrap {
    margin-top: 10px;
}

.buttonWrap {
    flex-shrink: 0;
}

.crossIconBtn {
    position: absolute;
    top: 0;
    right: 0;
    padding: 12px;
    border: 0;
    color: var(--grey-color);
    cursor: pointer;
    outline: 0;
}

.crossIcon {
    width: 20px;
    height: 20px;
}
