import React, { FC, useEffect, useState } from 'react';
import MediaQuery from 'react-responsive';

import { FlexBox, Paragraph, Title } from '@vchasno/ui-kit';

import Popup from 'components/ui/popup/popup';
import PseudoLink from 'components/ui/pseudolink/pseudolink';
import { motion } from 'framer-motion';
import { MEDIA_WIDTH, MOBILE_APP_POPUP_KEY } from 'lib/constants';
import { setSessionStorageItem } from 'lib/webStorage';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';

import MobileAppEDOLinks from './MobileAppEDOLinks';

import EDOlogo from './images/edo-logo.png';
import PhoneImage from './images/phone.png';
import Stars from './images/stars.gif';

import css from './MobileAppEDOPopup.css';

const MobileAppEDOPopup: FC = () => {
    const [popupActive, setPopupActive] = useState<boolean>(false);

    // Виключили в рамках задачі https://vchasno-group.atlassian.net/browse/EC-49
    // TODO: видалити, якщо за півроку виявиться непотрібним
    // const mobileAppPopupHide = getSessionStorageItem(MOBILE_APP_POPUP_KEY);
    const mobileAppPopupHide = true;

    useEffect(() => {
        setPopupActive(!mobileAppPopupHide);
        if (popupActive || !mobileAppPopupHide) {
            eventTracking.sendToGTMV4({
                event: 'ec_popup_mob_upload_app_show',
            });

            eventTracking.sendPromoPopupToGTMV4({
                action: 'shown',
                campaign: 'app_responsive',
            });
        }
    }, [mobileAppPopupHide]);

    const handleContinueClick = () => {
        setSessionStorageItem(MOBILE_APP_POPUP_KEY, true);
        setPopupActive(false);
        eventTracking.sendToGTMV4({
            event: 'ec_popup_mob_upload_browser_click',
        });

        eventTracking.sendPromoPopupToGTMV4({
            action: 'closed_manual',
            campaign: 'app_responsive',
        });
    };

    return (
        <MediaQuery maxWidth={MEDIA_WIDTH.mobile}>
            <Popup
                className={css.root}
                active={popupActive}
                onClose={handleContinueClick}
            >
                <div className={css.phoneContainer}>
                    <motion.img
                        variants={{
                            hidden: { opacity: 0 },
                            visible: {
                                opacity: 1,
                                transition: { duration: 1 },
                            },
                        }}
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                        className={css.stars}
                        src={Stars}
                        alt={t`Зірки`}
                        width={59}
                        height={59}
                    />
                    <img
                        src={PhoneImage}
                        className={css.phoneImage}
                        alt="SmartPhone"
                    />
                </div>
                <FlexBox
                    className={css.infoContainer}
                    direction="column"
                    align="center"
                    justify="space-between"
                >
                    <FlexBox direction="column" align="center">
                        <img
                            className={css.edoLogo}
                            src={EDOlogo}
                            alt="Vchasno EDO"
                        />
                        <Title
                            className={css.title}
                            textAlign="center"
                            level={3}
                        >{t`Встановлюйте застосунок Вчасно.ЕДО`}</Title>
                        <Paragraph
                            className={css.subtitle}
                            textAlign="center"
                        >{t`Підписуйте та надсилайте документи за декілька секунд`}</Paragraph>
                        <MobileAppEDOLinks />
                    </FlexBox>
                    <PseudoLink
                        onClick={handleContinueClick}
                    >{t`Продовжити в браузері`}</PseudoLink>
                </FlexBox>
            </Popup>
        </MediaQuery>
    );
};

export default MobileAppEDOPopup;
