import React from 'react';

import { MobileAppLinks } from '@vchasno/ui-kit';

import eventTracking from 'services/analytics/eventTracking';
import { GTMEventName } from 'services/analytics/gtm';

import { getBowserOS, getEventGTMName } from './helpers';

import { ANDROID_OS, IOS_OS } from './constants';

import css from './MobileAppEDOPopup.css';

const MobileAppEDOLinks: React.FC = () => {
    const eventClickName = getEventGTMName();

    const OS = getBowserOS();

    const sendEvent = () => {
        eventTracking.sendToGTMV4({
            event: eventClickName as GTMEventName<string>,
        });
        eventTracking.sendPromoPopupToGTMV4({
            action: 'click',
            campaign: 'app_responsive',
        });
    };

    switch (OS) {
        case ANDROID_OS:
            return (
                <div className={css.buttonWrapper}>
                    <MobileAppLinks
                        android
                        hideTitle
                        product="edo"
                        onClick={sendEvent}
                    />
                </div>
            );

        case IOS_OS:
            return (
                <div className={css.buttonWrapper}>
                    <MobileAppLinks
                        ios
                        hideTitle
                        product="edo"
                        onClick={sendEvent}
                    />
                </div>
            );

        default:
            return null;
    }
};

export default MobileAppEDOLinks;
