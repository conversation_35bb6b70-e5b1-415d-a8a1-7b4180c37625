import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import cn from 'classnames';
import { isAdminSelector } from 'selectors/app.selectors';
import { t } from 'ttag';

import Button from '../../ui/button/button';
import PseudoLink from '../../ui/pseudolink/pseudolink';

import eventTracking from '../../../services/analytics/eventTracking';

import css from './ImportContactsSuccesfulView.css';

type Props = {
    invalidRowNumbers: Array<unknown>;
    rowsTotal: number;
    rowsInvalid: number;
    toContacts: () => void;
    onInvite: () => void;
};

const ImportContactsSuccesfulView: React.FC<React.PropsWithChildren<Props>> = ({
    invalidRowNumbers,
    rowsInvalid,
    rowsTotal,
    toContacts,
    onInvite,
}) => {
    const [invalidContactsShown, setInvalidContactsShown] = useState(false);
    const isAdmin = useSelector(isAdminSelector);

    const invalidContacts =
        invalidRowNumbers.length <= 10
            ? invalidRowNumbers
            : [
                  ...invalidRowNumbers.slice(0, 9),
                  '...',
                  invalidRowNumbers[invalidRowNumbers.length - 1],
              ];

    const contactsSum = rowsTotal - rowsInvalid;

    const invalidContactsStr = invalidContacts.join(', ');

    useEffect(() => {
        eventTracking.sendEvent(
            'contact_import_results',
            'show',
            contactsSum.toString(),
        );
    }, [contactsSum]);

    const trackInviteClick = () => {
        eventTracking.sendEvent(
            'contact_import_results',
            'invite_contractor_click_btn',
            contactsSum.toString(),
        );
        onInvite();
    };

    const trackToContactsClick = () => {
        eventTracking.sendEvent('contact_import_results', 'contacts_click_btn');
        toContacts();
    };

    return (
        <div data-qa="qa_success_message_popup" className={css.layout}>
            <img
                className={css.img}
                src={`${config.STATIC_HOST}/images/onboarding/use_in_company_bare.svg`}
                alt=""
            />
            <div className={css.successMessage}>
                {t`Ви успішно завантажили контакти контрагентів`}
            </div>
            <table className={css.contentBlock}>
                <tr data-qa="qa_all_num">
                    <td
                        className={css.textCell}
                    >{t`Всього контактів у файлі:`}</td>
                    <td>{rowsTotal}</td>
                </tr>
                <tr data-qa="qa_all_num_download">
                    <td
                        className={css.textCell}
                    >{t`Всього контактів завантажено:`}</td>
                    <td>{contactsSum}</td>
                </tr>
                <tr data-qa="qa_all_fails">
                    <td
                        className={css.textCell}
                    >{t`Не завантажено контактів:`}</td>
                    <td>{rowsInvalid}</td>
                </tr>
                <tr>
                    {!invalidContactsShown && (
                        <td>
                            <PseudoLink
                                onClick={() => setInvalidContactsShown(true)}
                            >
                                {t`Перелік невалідних контактів`}
                            </PseudoLink>
                        </td>
                    )}
                    {invalidContactsShown && (
                        <>
                            <td
                                className={cn(
                                    css.invalidContactsLabel,
                                    css.textCell,
                                )}
                            >
                                {t`Невалідні контакти (номер строки у файлі)`}:
                            </td>
                            <td className={css.invalidContactsLabel}>
                                {invalidContactsStr}
                            </td>
                        </>
                    )}
                </tr>
            </table>

            <div className={css.buttons} data-qa="qa_return_to_list">
                {isAdmin && (
                    <Button
                        theme="blue"
                        onClick={trackInviteClick}
                        dataQa="qa_invite_contacts"
                    >
                        {t`Запросити незареєстрованих`}
                    </Button>
                )}
                <Button
                    typeContour
                    theme="blue"
                    onClick={trackToContactsClick}
                    dataQa="qa_go_to_contacts"
                >
                    {t`До контактів`}
                </Button>
            </div>
        </div>
    );
};

export default ImportContactsSuccesfulView;
