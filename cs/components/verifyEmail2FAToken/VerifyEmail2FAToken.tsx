import React, { FC, useEffect } from 'react';
import { useHistory } from 'react-router-dom';

import { useMutation } from '@tanstack/react-query';
import { Spinner } from '@vchasno/ui-kit';

import PartnerLogos from 'components/AuthLayout/PartnerLogos';
import FlexBox from 'components/FlexBox/FlexBox';
import { AUTH_ENTRYPOINT_PATH } from 'components/auth';
import PageTitle from 'components/pageTitle/pageTitle';
import BackButton from 'components/ui/BackButton/BackButton';
import { redirect } from 'lib/navigation';
import auth, { VerifyEmail2FATokenResponse } from 'services/auth';
import { t } from 'ttag';
import { ApiError } from 'types/request';
import Alert from 'ui/Alert/Alert';
import Icon from 'ui/icon/icon';

import LockIcon from './images/lock.svg';

import css from './VerifyEmail2FAToken.css';

/**
 * Email-based two-factor authentication component
 *
 * User enters their password to verify email access
 */
const VerifyEmail2FAToken: FC = () => {
    const history = useHistory();
    const query = new URLSearchParams(window.location.search);
    const token = query.get('token');

    const { error, isError, mutate } = useMutation<
        VerifyEmail2FATokenResponse,
        ApiError
    >({
        mutationFn: () => auth.verifyEmail2FAToken({ token: token ?? '' }),
        onSuccess: (response) => {
            redirect(response.nextUrl);
        },
    });

    useEffect(() => mutate(), []);

    if (!isError) {
        return (
            <FlexBox justify="center" align="center" className={css.spinner}>
                <Spinner width="50px" height="50px" />
            </FlexBox>
        );
    }

    const onBackHandler = () => {
        history.push({
            pathname: AUTH_ENTRYPOINT_PATH,
            search: location.search,
        });
    };
    return (
        <form onSubmit={() => {}} className={css.form}>
            <PageTitle>{t`Двофакторна аутентифікація`}</PageTitle>
            <FlexBox className={css.container} justify="center" gap={0}>
                <div className={css.content}>
                    <BackButton
                        className={css.backBtn}
                        onClick={onBackHandler}
                    />
                    <PartnerLogos className={css.partnersLogos} />
                    <FlexBox direction="column" justify="center" gap={25}>
                        <Icon className={css.lockIcon} glyph={LockIcon} />
                        <FlexBox direction="column" justify="center" gap={40}>
                            <h1 className={css.title}>{t`Перевірка пошти`}</h1>
                        </FlexBox>
                    </FlexBox>
                    {error && (
                        <Alert theme="error" hideIcon>
                            {error.reason}
                        </Alert>
                    )}
                </div>
            </FlexBox>
        </form>
    );
};

export default VerifyEmail2FAToken;
