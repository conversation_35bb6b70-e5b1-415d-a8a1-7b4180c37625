import React, { useEffect, useState } from 'react';

import Lazy<PERSON>ottie from 'components/ui/LazyLottie';
import Popup from 'components/ui/popup/popup';
import { motion } from 'framer-motion';
import { t } from 'ttag';

import SignatureDrawAnimation from './SignatureDrawAnimation';

import css from './SuccessPopupBasic.css';

interface SuccessPopupProps {
    onAnimationComplete: () => void;
    onClose: () => void;
    onMount?: () => void;
}

const SuccessPopupBasic: React.FC<SuccessPopupProps> = (props) => {
    const [view, setView] = useState<'signature' | 'checkmark'>('signature');

    const handleComplete = async () => {
        await new Promise((resolve) => setTimeout(resolve, 250));
        props.onAnimationComplete();
    };

    useEffect(() => {
        props.onMount?.();
    }, []);

    return (
        <Popup
            active
            onClose={props.onClose}
            className={css.root}
            overlayClassName={css.overlay}
        >
            <div className={css.content}>
                {view === 'signature' && (
                    <SignatureDrawAnimation
                        onComplete={() => setView('checkmark')}
                    />
                )}

                {view === 'checkmark' && (
                    <div className={css.checkmark}>
                        <LazyLottie
                            getAnimationData={() =>
                                import('./assets/checkmark-lottie.json').then(
                                    (m) => m.default,
                                )
                            }
                            loop={false}
                            onComplete={handleComplete}
                            style={{ width: 165, height: 136 }}
                            width={165}
                            height={136}
                        />
                    </div>
                )}

                <motion.span
                    className={css.label}
                    variants={{
                        hidden: { opacity: 0 },
                        visible: { opacity: 1, transition: { delay: 0.7 } },
                    }}
                    initial="hidden"
                    animate={view === 'checkmark' ? 'visible' : 'hidden'}
                >
                    {t`Підписано!`}
                </motion.span>
            </div>
        </Popup>
    );
};

export default SuccessPopupBasic;
