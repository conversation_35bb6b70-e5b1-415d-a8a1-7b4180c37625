import { filterUnsuccessfulResults } from 'components/SuccesfulSignView/utils';
import { SignPopupState } from 'components/signPopup/types';

/**
 * Чи потрібно показувати старий чи новий попап після успішного підписання
 */
export function shouldShowOldSignSuccessPopup(
    signPopup: SignPopupState,
): boolean {
    return (
        signPopup.isMultiSign &&
        filterUnsuccessfulResults(signPopup.multiSignResult).length > 0
    );
}
