import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useMutation, useQuery } from '@tanstack/react-query';

import signPopupActionCreators from 'components/signPopup/signPopupActionCreators';
import { queryClient } from 'lib/queries';
import { getCurrentUserRoleId } from 'selectors/app.selectors';
import { resolvePromoBanner, updatePromoBannerShown } from 'services/banners';
import { PromoBanner, PromoBannerCampaign } from 'types/banner';

type PromoBannerType = 'basic' | PromoBannerCampaign;

const GET_PROMO_BANNER_KEY = 'getPromoBanner';
const GET_PROMO_BANNER_STALE_TIME_MS = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Щоб в моменті, коли користувач підписав документ, ми могли відразу ж показати
 * промо-попап, ми завантажуємо дані з серверу завчасно на попередніх етапах підпису.
 */
export function prefetchPromoBanner(currentRoleId: string) {
    queryClient.prefetchQuery({
        queryKey: [GET_PROMO_BANNER_KEY, currentRoleId],
        queryFn: () => resolvePromoBanner(),
        retry: false,
    });
}

/**
 * Як тільки ми показали якийсь промо-попап, ми відмічаємо що він показаний,
 * щоб наступного разу не показувати його повторно і показати базовий попап.
 */
export function useUpdatePromoBannerShown(campaign: PromoBannerCampaign) {
    const currentRoleId = useSelector(getCurrentUserRoleId);

    const { mutate } = useMutation({
        mutationFn: () => updatePromoBannerShown({ campaign: campaign }),
        onSuccess: () => {
            const queryKey = [GET_PROMO_BANNER_KEY, currentRoleId];
            const banner = queryClient.getQueryData<PromoBanner>(queryKey);
            if (banner && !banner.shown) {
                queryClient.invalidateQueries({ queryKey: queryKey });
            }
        },
    });

    useEffect(() => {
        void mutate();
    }, []);
}

export function useSignSuccessPopup() {
    const dispatch = useDispatch();
    const currentRoleId = useSelector(getCurrentUserRoleId);

    const { data, isLoading } = useQuery({
        queryKey: [GET_PROMO_BANNER_KEY, currentRoleId],
        queryFn: () => resolvePromoBanner(),
        retry: false,
        staleTime: GET_PROMO_BANNER_STALE_TIME_MS,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
    });

    const typeRef = useRef<PromoBannerType | null>(null);

    const handleClose = () => {
        dispatch(signPopupActionCreators.hidePopup());
    };

    let type: PromoBannerType | null = null;
    if (isLoading) {
        type = null;
    } else if (data && data.campaign && !data.shown) {
        type = data.campaign;
    } else {
        // якщо помилка чи ми вже показали промо-попап, то показуємо базовий попап
        type = 'basic';
    }

    // Після першого показу попапа, ми зберігаємо його тип в реф, щоб не змінювати тип
    // попапа, якщо дані в кеші зміняться, але попап ще активний.
    if (type !== null && typeRef.current === null) {
        typeRef.current = type;
    }

    return {
        type: typeRef.current,
        handleClose,
    };
}
