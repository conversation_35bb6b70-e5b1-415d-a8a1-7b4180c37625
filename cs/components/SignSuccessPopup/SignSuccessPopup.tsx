import React from 'react';
import { useSelector } from 'react-redux';

import { SignPopupStatus } from 'services/enums';
import { StoreState } from 'types/store';

import { shouldShowOldSignSuccessPopup } from './utils';

import SignSuccessPopupPromoApp from './SignSuccessPopupPromoApp';
import SignSuccessPopupPromoKasa from './SignSuccessPopupPromoKasa';
import SuccessPopupBasic from './SuccessPopupBasic';
import { useSignSuccessPopup } from './hooks';

function SignSuccessPopup() {
    const signPopup = useSelector((s: StoreState) => s.signPopup);
    const popupStatus = signPopup.popupStatus;

    const isActive =
        // Хоча цей попап окремий від SignPopup, ми все одно використовуємо
        // статус з нього, щоб зрозуміти коли показувати попап
        popupStatus === SignPopupStatus.SIGNED_SUCCESSFUL &&
        !shouldShowOldSignSuccessPopup(signPopup);

    if (!isActive) {
        return null;
    }

    return <SignSuccessPopupBase />;
}

function SignSuccessPopupBase() {
    const { type, handleClose } = useSignSuccessPopup();

    if (type === 'kasa') {
        return <SignSuccessPopupPromoKasa onClose={handleClose} />;
    }
    if (type === 'mobile_app') {
        return <SignSuccessPopupPromoApp onClose={handleClose} />;
    }
    if (type === 'basic') {
        return (
            <SuccessPopupBasic
                onClose={handleClose}
                onAnimationComplete={handleClose}
            />
        );
    }
    return null;
}

export default SignSuccessPopup;
