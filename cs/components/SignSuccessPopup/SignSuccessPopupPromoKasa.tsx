import React, { useEffect, useState } from 'react';
import Helmet from 'react-helmet';
import { useMediaQuery } from 'react-responsive';

import { Button, FlexBox, Text, Title } from '@vchasno/ui-kit';

import { motion } from 'framer-motion';
import { MEDIA_WIDTH } from 'lib/constants';
import { openInNewTab } from 'lib/navigation';
import eventTracking from 'services/analytics/eventTracking';
import { t } from 'ttag';
import Icon from 'ui/icon';
import Popup from 'ui/popup/popup';

import SignatureDrawAnimation from './SignatureDrawAnimation';
import SignedTextAnimation from './SignedTextAnimation';
import { useUpdatePromoBannerShown } from './hooks';

import CheckSVG from './images/check.svg';
import KasaAppSVG from './images/kasa-app.svg';

import handPointer from './images/hand-pointer.png';
import toggleOff from './images/toggle-off.png';
import toggleOn from './images/toggle-on.png';

import css from './SignSuccessPopupPromoKasa.css';

const APP_LINK =
    'https://service.vchasno.ua/upgrade-prro?utm_source=edo&utm_medium=edo&utm_campaign=edo-banner-after-sign';

const BENEFITS = [
    t`Безкоштовно для тих, в кого 1 каса`,
    t`-50% для 2 і більше кас на 2 роки`,
    t`Безкоштовна допомога з переходом та інтеграцією`,
];

const PromoView: React.FC<{ onLearnMore(): void }> = (props) => {
    return (
        <FlexBox
            direction="column"
            gap={20}
            align="space-between"
            className={css.adMenu}
        >
            <FlexBox direction="column" gap={20} align="center">
                <Icon glyph={KasaAppSVG} className={css.appSvg} />
                <Title level={2} className={css.title}>
                    {t`Перемикай на краще ПРРО`}
                </Title>
                <ul className={css.benefitsList}>
                    {BENEFITS.map((b, i) => (
                        <li key={i} className={css.benefitsListItem}>
                            <Icon
                                glyph={CheckSVG}
                                className={css.benefitsIcon}
                            />
                            <Text className={css.benefitsText} type="secondary">
                                {b}
                            </Text>
                        </li>
                    ))}
                </ul>
                <Button
                    size="lg"
                    onClick={props.onLearnMore}
                    className={css.learnMoreButton}
                >
                    {t`Дізнатись більше`}
                </Button>
            </FlexBox>
            <FlexBox
                direction="column"
                justify="flex-end"
                gap={10}
                className={css.toggleContainer}
                onClick={props.onLearnMore}
            >
                <FlexBox direction="row" align="center" gap={10}>
                    <img
                        src={toggleOff}
                        alt=""
                        role="presentation"
                        className={css.toggleIcon}
                    />
                    <Text className={css.toggleOffText}>
                        {t`Звичайне ПРРО`}
                    </Text>
                </FlexBox>
                <FlexBox
                    direction="row"
                    align="center"
                    gap={10}
                    className={css.toggleOnImg}
                >
                    <img
                        src={toggleOn}
                        alt=""
                        role="presentation"
                        className={css.toggleIcon}
                    />
                    <div className={css.handWrapper}>
                        <img
                            src={handPointer}
                            alt=""
                            role="presentation"
                            className={css.handPointerImg}
                        />
                    </div>
                    <Text className={css.toggleOnText}>{t`«Вчасно.Каса»`}</Text>
                </FlexBox>
            </FlexBox>
        </FlexBox>
    );
};

const SignSuccessPopupMobile = (props: { onLearnMore: () => void }) => {
    const [view, setView] = useState<'signature' | 'promo'>('signature');

    return (
        <div className={css.root}>
            {/* On mobile we first show the signature animation and then the promo panel as two slides/pages */}
            {view === 'signature' ? (
                <div className={css.content}>
                    <SignatureDrawAnimation
                        onComplete={() => setView('promo')}
                    />
                    <SignedTextAnimation isVisible className={css.label} />
                </div>
            ) : view === 'promo' ? (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                    style={{ opacity: view === 'promo' ? 1 : 0, width: '100%' }}
                >
                    <div className={css.leftSection}>
                        <PromoView onLearnMore={props.onLearnMore} />
                    </div>
                </motion.div>
            ) : null}
        </div>
    );
};

const SignSuccessPopupDesktop = (props: { onLearnMore: () => void }) => {
    return (
        <div className={css.root}>
            <motion.div
                className={css.leftSection}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
            >
                <PromoView onLearnMore={props.onLearnMore} />
            </motion.div>

            <div className={css.content}>
                <SignatureDrawAnimation />
                <SignedTextAnimation isVisible className={css.label} />
            </div>
        </div>
    );
};

type Props = {
    onClose: () => void;
};

const SignSuccessPopupPromoKasa: React.FC<Props> = (props) => {
    const isMobile = useMediaQuery({ maxWidth: MEDIA_WIDTH.tablet });
    useUpdatePromoBannerShown('kasa');

    useEffect(() => {
        eventTracking.sendPromoPopupToGTMV4({
            action: 'shown',
            campaign: 'kasa_sign_success',
        });
    }, []);

    const handleClose = () => {
        eventTracking.sendToGTMV4({ event: 'sign-thankyoupopup-kasa-close' });
        eventTracking.sendPromoPopupToGTMV4({
            action: 'closed_manual',
            campaign: 'kasa_sign_success',
        });
        props.onClose();
    };

    const handleLearnMore = () => {
        eventTracking.sendToGTMV4({ event: 'sign-thankyoupopup-kasa-click' });

        eventTracking.sendPromoPopupToGTMV4({
            action: 'click',
            campaign: 'kasa_sign_success',
        });
        openInNewTab(APP_LINK);
        props.onClose();
    };

    return (
        <>
            <Helmet>
                <link rel="prefetch" href={handPointer} as="image" />
                <link rel="prefetch" href={toggleOff} as="image" />
                <link rel="prefetch" href={toggleOn} as="image" />
            </Helmet>

            <Popup
                fullContent
                className={css.popup}
                active
                inPortal
                onClose={handleClose}
            >
                {isMobile ? (
                    <SignSuccessPopupMobile onLearnMore={handleLearnMore} />
                ) : (
                    <SignSuccessPopupDesktop onLearnMore={handleLearnMore} />
                )}
            </Popup>
        </>
    );
};

export default SignSuccessPopupPromoKasa;
