import React from 'react';
import { useSelector } from 'react-redux';
import { useMediaQuery } from 'react-responsive';

import {
    BusinessPaperlessBanner,
    DonateBanner,
    DonateRatelMBanner,
    useBusinessPaperlessBannerContext,
    useDeminingBannerContext,
    useDonateRatelMBannerContext,
} from '@vchasno/shared-components';

import { useDetectedInTimeBannerV2Context } from 'components/DetectedInTimeBannerV2/context';
import DetectedInTimeSidebarV2 from 'components/DetectedInTimeSidebarV2';
import { FeatureShow } from 'components/FeatureDisplay';
import FlexBox from 'components/FlexBox';
import { HomeBanner } from 'components/HomeBanner';
import { useHomeBannerContext } from 'components/HomeBanner/HomeBanner.context';
import { useCloseBanner } from 'components/MobileAppBanner/hooks/useCloseBanner';
import OnboardingArchiveSidebar from 'components/OnboardingArchiveSidebar';
import TrialInfoProgressBar from 'components/TrialInfoProgressBar';
import { MEDIA_WIDTH } from 'lib/constants';
import {
    getActiveTrialProRate,
    getIsAnyUnlimitRateExists,
} from 'selectors/app.selectors';
import { getSidebarExpanded } from 'selectors/sidebar.selectors';

import { DesktopMobileAppBanner } from '../MobileAppBanner';
import Header from './Header';
import Layout from './Layout';
import MenuItem from './components/MenuItem';
import { useShowAppSidebar } from './hooks/useShowAppSidebar';
import { sidebarMenuNavLinkList } from './sidebarMenuNavLinkList';

import css from './AppSidebar.css';

const Uploader = React.lazy(
    () => import(/* webpackChunkName: "Uploader" */ '../uploader/uploader'),
);

const AppSidebar: React.FC<React.PropsWithChildren<unknown>> = () => {
    const { isClose } = useDeminingBannerContext();
    const { isClose: isClosePaperLess } = useBusinessPaperlessBannerContext();
    const { isClose: isCloseHome } = useHomeBannerContext();
    const [isCloseAppBanner, setCloseAppBanner] = useCloseBanner();
    const isExpanded = useSelector(getSidebarExpanded);
    const activeTrialRate = useSelector(getActiveTrialProRate);
    const isAnyUnlimitRateExists = useSelector(getIsAnyUnlimitRateExists);
    const showSidebar = useShowAppSidebar();
    const medialQueryToShow = useMediaQuery({ minWidth: MEDIA_WIDTH.normal });
    const {
        isClose: isCloseDonateRatelMBanner,
    } = useDonateRatelMBannerContext();
    const {
        isClose: isCloseDetectedInTimeBannerV2,
    } = useDetectedInTimeBannerV2Context();

    if (!showSidebar || !medialQueryToShow) {
        return null;
    }

    return (
        <>
            <Layout header={<Header />}>
                <ul className={css.itemContainer}>
                    {sidebarMenuNavLinkList.map((item) => (
                        <MenuItem key={item.title} item={item} />
                    ))}
                </ul>
                <FlexBox direction="column">
                    {isExpanded && !isAnyUnlimitRateExists && (
                        <TrialInfoProgressBar />
                    )}
                    {isExpanded && isCloseHome && (
                        <FeatureShow feature="HOME_BANNER">
                            <HomeBanner isSideBar slide={0} />
                        </FeatureShow>
                    )}
                    {isExpanded && isClosePaperLess && (
                        <FeatureShow feature="PAPERLESS_BANNER">
                            {/*
                             // @ts-expect-error TS2322 [FIXME] Comment is autogenerated */}
                            <BusinessPaperlessBanner sideBar slide={0} />
                        </FeatureShow>
                    )}
                    {isExpanded && isCloseAppBanner && isClose && (
                        <FeatureShow feature="DEMINING_BANNER">
                            <DonateBanner compact slider />
                        </FeatureShow>
                    )}
                    {isExpanded && isCloseDonateRatelMBanner && (
                        <FeatureShow feature="DONATE_RATEL_M_BANNER">
                            <DonateRatelMBanner compact />
                        </FeatureShow>
                    )}
                    {isExpanded && isCloseDetectedInTimeBannerV2 && (
                        <FeatureShow feature="DONATE_DETECTED_IN_TIME_BANNER_V2">
                            <DetectedInTimeSidebarV2 />
                        </FeatureShow>
                    )}
                    {isExpanded &&
                        !isCloseAppBanner &&
                        !isCloseDonateRatelMBanner &&
                        !isCloseDetectedInTimeBannerV2 &&
                        !activeTrialRate && (
                            <DesktopMobileAppBanner
                                setCloseBanner={setCloseAppBanner}
                            />
                        )}
                </FlexBox>
            </Layout>
            <React.Suspense fallback={null}>
                <Uploader popupOnly />
            </React.Suspense>
            <OnboardingArchiveSidebar />
        </>
    );
};

export default React.memo(AppSidebar);
