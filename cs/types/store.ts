import { Add2FAPopupState } from 'components/Add2FAPopup/add2FAPopupReducer';
import { AdditionalInfoPopupState } from 'components/AdditionalInfoPopup/types';
import { AntivirusPopupState } from 'components/AntivirusPopup/AntivirusPopupReducer';
import { CheckoutState } from 'components/Checkout/types';
import { ExpiresFreeRateBannerState } from 'components/ExpiresFreeRateBanner/types';
import { FeedbackPopupState } from 'components/FeedbackPopup/types';
import { State as kepMobileAppPopup } from 'components/KepMobileAppPopup/types';
import { NetPeakWidgetState } from 'components/NetPeackWidget/reducer';
import { StateType as OnboardingState } from 'components/Onboarding/onboardingReducer';
import { RequiredFieldsResolverState } from 'components/RequiredFieldsResolver/requiredFieldsResolverReducer';
import { SignWithKepFlowState } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import { SuccessVerifiedUserState } from 'components/SuccessVerifiedUser/types';
import { SyncRolesErrorBannerState } from 'components/SyncRolesErrorBanner/types';
import { AppState } from 'components/app/types';
import { State as ChangeDocumentPopupState } from 'components/changeDocumentPopup/changeDocumentPopupTypes';
import { State as CompanyAdminCardState } from 'components/companyAdminCard/types';
import { CompanyEmployeesState } from 'components/companyEmployees/types';
import { State as ContactsTagsEditFormState } from 'components/contactsTagsEditForm/contactsTagsEditFormTypes';
import { State as ContactsTagsEditPopupState } from 'components/contactsTagsEditPopup/contactsTagsEditPopupTypes';
import { State as CreateDeleteRequestPopupState } from 'components/createDeleteRequestPopup/createDeleteRequestPopupTypes';
import { State as CreateFlowFormState } from 'components/createFlowForm/createFlowFormTypes';
import { DocumentState } from 'components/document/types';
import { DocumentSettingsState } from 'components/documentSettings/documentSettingsReducer';
import { State as EmployeePageState } from 'components/employee/types';
import { FilterSlice } from 'components/filters/types';
import { GuideSidebarState } from 'components/guideSidebar/guideSidebarReducer';
import { State as InfoBannerState } from 'components/infoBanner/infoBannerTypes';
import { State as ProRateInfoPopup } from 'components/proRateInfoPopup/proRateInfoPopupTypes';
import { State as ProRateTrialPopup } from 'components/proRateTrialPopup/proRateTrialPopupTypes';
import { State as ResolveDeleteRequestPopupState } from 'components/resolveDeleteRequestPopup/resolveDeleteRequestPopupTypes';
import { State as ReviewHistoryPopupState } from 'components/reviewHistoryPopup/reviewHistoryPopupTypes';
import { State as ShareDocumentPopupState } from 'components/shareDocumentPopup/shareDocumentPopupTypes';
import { SidebarSlice } from 'components/sidebar/types';
import { SignPopupState } from 'components/signPopup/types';
import { AdditionalFieldsAccessPopupState } from 'components/tagsAccessPopup/AdditionalFieldsAccessPopup/AdditionalFieldsAccessPopupTypes';
import { State as TagsAccessPopupState } from 'components/tagsAccessPopup/tagsAccessPopupTypes';
import { State as TagsEditPopupState } from 'components/tagsEditPopup/tagsEditPopupTypes';
import { State as TriggerNotificationState } from 'components/triggerNotification/types';
import { RouterState } from 'connected-react-router';
import { AnnulmentActProps } from 'store/annulmentAct';
import { ArchiveBannerState } from 'store/archiveBannerSlice';
import { ArchiveInfoPopupState } from 'store/archiveInfoPopupSlice';
import { CompanyEmployeesNumberPopupState } from 'store/companyEmployeesNumberPopupSlice';
import { CompanyPickerPopupSliceInitialStateProps } from 'store/companyPickerPopupSlice';
import { CsatState } from 'store/csat';
import { DocumentCreationState } from 'store/documentCreationSlice';
import { ProfileFormEmailChange2FAVerificationPopupState } from 'store/profileFormEmailChange2FAverificationPopupSlice';
import { ProfileFormEmailChangePendingPopupState } from 'store/profileFormEmailChangePendingPopupSlice';
import { ProfileFormEmailChangePopupState } from 'store/profileFormEmailChangePopupSlice';
import { TrialInfoPopupState } from 'store/trialInfoPopup';
import { TrialRateSelectCompanyPopupState } from 'store/trialRateSelectCompanyPopupSlice';

export interface StoreState {
    app: AppState;
    add2FAPopup: Add2FAPopupState;
    additionalInfoPopup: AdditionalInfoPopupState;
    antivirusPopup: AntivirusPopupState;
    archiveBanner: ArchiveBannerState;
    bannerEditForm: any;
    bannersList: any;
    changeDocumentPopup: ChangeDocumentPopupState;
    checkKeyPopup: any;
    checkout: CheckoutState;
    companyAdminCard: CompanyAdminCardState;
    companyCard: any;
    companyEmployees: CompanyEmployeesState;
    companyBilling: any;
    contactList: any;
    createDeleteRequestPopup: CreateDeleteRequestPopupState;
    createFlowForm: CreateFlowFormState;
    document: DocumentState;
    documentList: any;
    documentSettings: DocumentSettingsState;
    employee: EmployeePageState;
    feedbackPopup: FeedbackPopupState;
    filters: FilterSlice;
    filtersPopup: any;
    guideSidebar: GuideSidebarState;
    header: any;
    importContacts: any;
    infoBanner: InfoBannerState;
    notificationCenter: any;
    onboarding: OnboardingState;
    proRateInfoPopup: ProRateInfoPopup;
    proRateTrialPopup: ProRateTrialPopup;
    profile: any;
    recipientForm: any;
    recipientsList: any;
    resolveDeleteRequestPopup: ResolveDeleteRequestPopupState;
    reviewHistoryPopup: ReviewHistoryPopupState;
    shareDocumentPopup: ShareDocumentPopupState;
    sidebar: SidebarSlice;
    sideMenu: any;
    signPopup: SignPopupState;
    specialAbilities: any;
    statistics: any;
    statisticsCompaniesList: any;
    statisticsUser: any;
    statisticsUsersList: any;
    successVerifiedUser: SuccessVerifiedUserState;
    syncRolesErrorBanner: SyncRolesErrorBannerState;
    tagsAccessPopup: TagsAccessPopupState;
    additionalFieldsAccessPopup: AdditionalFieldsAccessPopupState;
    tagsEditPopup: TagsEditPopupState;
    contactsTagsEditPopup: ContactsTagsEditPopupState;
    contactsTagsEditForm: ContactsTagsEditFormState;
    taxInvoice: any;
    taxInvoicesList: any;
    taxInvoicePopup: any;
    triggerNotification: TriggerNotificationState;
    uploader: any;
    netpeakPopup?: NetPeakWidgetState;
    router: RouterState;
    kepMobileAppPopup: kepMobileAppPopup;
    requiredFieldsResolver: RequiredFieldsResolverState;
    documentCreation: DocumentCreationState;
    trialInfoPopup: TrialInfoPopupState;
    trialRateSelectCompanyPopup: TrialRateSelectCompanyPopupState;
    companyPickerPopup: CompanyPickerPopupSliceInitialStateProps;
    profileFormEmailChangePopup: ProfileFormEmailChangePopupState;
    profileFormEmailChange2FAVerificationPopup: ProfileFormEmailChange2FAVerificationPopupState;
    profileFormEmailChangePendingPopup: ProfileFormEmailChangePendingPopupState;
    archiveInfoPopup: ArchiveInfoPopupState;
    companyEmployeesNumberPopupSlice: CompanyEmployeesNumberPopupState;
    signWithKepFlow: SignWithKepFlowState;
    expiresFreeRateBanner: ExpiresFreeRateBannerState;
    annulmentAct: AnnulmentActProps;
    csat: CsatState;
}
