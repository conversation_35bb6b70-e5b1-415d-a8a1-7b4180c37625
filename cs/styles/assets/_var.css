:root {
    --z-index-header: 100;
    --z-index-highest: 999;
    --page-width: 1200px;
    --header-height: 60px;
    --sidebar-width: 260px;
    --netpeak-widget-height: 140px;
    --mobile-header-height: 48px;
    --border-radius: 8px;
    --text-font-size: 14px;

    /* colors */
    --corporate-color: #fff200;
    --kasa-color: #e579ff;
    --pos-color: #ffb200;
    --kep-color: #ff5a5f;
    --edi-color: #00c5ff;
    --blue-color: #087dc1;
    --light-blue-color: #e1f8ff;
    --deep-blue-color: #234f9a;
    --ttn-color: #6b5fff;
    --green-color: #1cb800;
    --green-alt-color: #138000;
    --pigeon-color: #bfcede;
    --pigeon-color-rgb: 182, 202, 219;
    --dark-pigeon-color: #8aa3b7;
    --graphite-color: #c4c4c4;
    --dark-grey-color: #979797;
    --slate-grey-color: #5a6a81;
    --link-color: #087dc1;
    --link-hover-color: #179ae7;
    --content-color: #252d3d;
    --content-secondary-color: #5a6a81;
    --red-color: #a90000;
    --pink-color: #ff9498;
    --red-alt-color: #ef6562;
    --linen-color: #fbf4ea;
    --grey-color: #9aaabf;
    --black-color: #000;
    --white-color: #fff;

    /* border colors */

    /* --default-border: #dbe5ea; */
    --default-border: #e2e9f3;
    --orange-border: #ff9c01;
    --error-border: #f77c7c;
    --grey-border: #bfcede;
    --blue-border: #087dc1;


    /* box shadow */
    --box-shadow-color: rgba(182, 202, 219, 0.5);

    /* background colors */
    --grey-bg: #f3f8fb;
    --pigeon-bg: #e4ecf1;
    --orange-bg: #ff9c01;
    --light-orange-bg: #fbf4ea;
    --hover-bg: #f2f6f7;
    --hover-orange-bg: #F57A00;
    --white-bg: #fff;
    --blue-bg: #087dc1;
    --light-blue-bg: #ecf4ff;
    --pale-green-bg: #e7f3d9;
    --azure-mist-bg: #e6f9ff;

    /* CTA (Call to action) colors */
    --primary-cta-color: #ff9c01;
    --secondary-cta-color: #F57A00;

    /* Autumn color */
    --autumn-cta-color: #ff9c01;
    --autumn-cta-hover-color: #e66100;
    --autumn-content-color: #2c313d;
    --autumn-content-alt-color: #eef3df;
    --autumn-1-color: #5d7415;
    --autumn-2-color: #819f27;
    --autumn-3-color: #bdd27d;
    --autumn-4-color: #eef3df;

    /* Spring color */
    --spring-cta-color: #00c5ff;
    --spring-cta-hover-color: #148fb3;
    --spring-content-color: #2c313d;
    --spring-content-alt-color: #eef3df;
    --spring-1-color: #5d7415;
    --spring-2-color: #819f27;
    --spring-3-color: #bdd27d;
    --spring-4-color: #eef3df;

    /* Dark color */
    --dark-1-color: #1d1d1f;
    --dark-3-color: #353539;
    --dark-4-color: #3b4f70;
    --dark-blue-hover-color: #148fb3;

    /* Components */

    /* SelectedItem */
    --selected-item-border-radius: var(--border-radius);

    /* SelectedItemsList */
    --selected-items-list-margin: 10px 0 0;

    /*    Design system color */
    --Color-Gray-300: #6E809A;
    --Color-System-states-Error-300: #F54F4F;
}
