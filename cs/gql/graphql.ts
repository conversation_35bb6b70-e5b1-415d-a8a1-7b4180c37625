import { GraphQLClient } from 'graphql-request';
import { RequestInit } from 'graphql-request/dist/types.dom';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };

function fetcher<TData, TVariables extends { [key: string]: any }>(client: GraphQLClient, query: string, variables?: TVariables, requestHeaders?: RequestInit['headers']) {
  return async (): Promise<TData> => client.request({
    document: query,
    variables,
    requestHeaders
  });
}
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Any: { input: any; output: any; }
};

export type AntivirusCheck = {
  __typename?: 'AntivirusCheck';
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  documentVersionId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  provider: Scalars['String']['output'];
  status: Scalars['String']['output'];
};

export type ArchiveList = {
  __typename?: 'ArchiveList';
  count: Scalars['Int']['output'];
  directories: Array<DocumentDirectory>;
  directory_ids: Array<Scalars['String']['output']>;
  document_ids: Array<Scalars['String']['output']>;
  documents: Array<Document>;
};

export type AutomationConditionCompany = {
  __typename?: 'AutomationConditionCompany';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type Banner = {
  __typename?: 'Banner';
  activityPeriod?: Maybe<Scalars['String']['output']>;
  analyticsCategory: Scalars['String']['output'];
  audienceType?: Maybe<Scalars['String']['output']>;
  color: Scalars['String']['output'];
  dateFrom: Scalars['String']['output'];
  dateTo: Scalars['String']['output'];
  daysBeforeSignatureExpires?: Maybe<Scalars['Int']['output']>;
  employeesCount?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['String']['output'];
  incomingDocumentsSignCount?: Maybe<Array<Scalars['String']['output']>>;
  link: Scalars['String']['output'];
  linkEn: Scalars['String']['output'];
  linkText: Scalars['String']['output'];
  linkTextEn: Scalars['String']['output'];
  outgoingDocumentsCount?: Maybe<Array<Scalars['String']['output']>>;
  positions?: Maybe<Array<Scalars['String']['output']>>;
  rates?: Maybe<Array<Scalars['String']['output']>>;
  status: Scalars['String']['output'];
  text: Scalars['String']['output'];
  textEn: Scalars['String']['output'];
};

export type Bill = {
  __typename?: 'Bill';
  amount?: Maybe<Scalars['Int']['output']>;
  companyId: Scalars['String']['output'];
  company_id: Scalars['String']['output'];
  count_documents?: Maybe<Scalars['String']['output']>;
  dateCreated: Scalars['String']['output'];
  date_created: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  document_id: Scalars['String']['output'];
  email: Scalars['String']['output'];
  id: Scalars['String']['output'];
  max_employees_count?: Maybe<Scalars['Int']['output']>;
  number?: Maybe<Scalars['String']['output']>;
  paymentStatus: Scalars['String']['output'];
  payment_status: Scalars['String']['output'];
  rate: Scalars['String']['output'];
  seqnum: Scalars['String']['output'];
  services: Array<BillService>;
  servicesType: Scalars['String']['output'];
  source: Scalars['String']['output'];
  statusId: Scalars['String']['output'];
  status_id: Scalars['String']['output'];
};

export type BillService = {
  __typename?: 'BillService';
  dateFrom?: Maybe<Scalars['String']['output']>;
  extension?: Maybe<Scalars['String']['output']>;
  limitsEmployeesCount?: Maybe<Scalars['Int']['output']>;
  rate?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
  unitPrice: Scalars['Int']['output'];
  units: Scalars['Int']['output'];
};

export type BillingAccount = {
  __typename?: 'BillingAccount';
  amount: Scalars['Int']['output'];
  amountLeft: Scalars['Int']['output'];
  billingTransactionsFrom: Array<BillingTransaction>;
  billingTransactionsTo: Array<BillingTransaction>;
  companyId: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateDeleted: Scalars['String']['output'];
  dateExpired: Scalars['String']['output'];
  id: Scalars['String']['output'];
  initiatorId: Scalars['String']['output'];
  pricePerUser: Scalars['Int']['output'];
  rate: Scalars['String']['output'];
  source: Scalars['String']['output'];
  status: Scalars['String']['output'];
  type: Scalars['String']['output'];
  units: Scalars['Int']['output'];
  unitsLeft: Scalars['Int']['output'];
};

export type BillingCompanyConfig = {
  __typename?: 'BillingCompanyConfig';
  apiEnabled: Scalars['Boolean']['output'];
  canEnforce2FA: Scalars['Boolean']['output'];
  canManageEmployeeAccess: Scalars['Boolean']['output'];
  externalCommentsEnabled: Scalars['Boolean']['output'];
  internalCommentsEnabled: Scalars['Boolean']['output'];
  internalDocumentsEnabled: Scalars['Boolean']['output'];
  maxAdditionalFieldsCount?: Maybe<Scalars['Int']['output']>;
  maxArchiveDocumentsCount?: Maybe<Scalars['Int']['output']>;
  maxDocumentsCount?: Maybe<Scalars['Int']['output']>;
  maxEmployeesCount?: Maybe<Scalars['Int']['output']>;
  maxRequiredFieldsCount?: Maybe<Scalars['Int']['output']>;
  maxTagsCount?: Maybe<Scalars['Int']['output']>;
  maxTemplatesCount?: Maybe<Scalars['Int']['output']>;
  maxVersionsCount?: Maybe<Scalars['Int']['output']>;
  maxVisibleDocumentsCount?: Maybe<Scalars['Int']['output']>;
  reviewsEnabled: Scalars['Boolean']['output'];
};

export type BillingTransaction = {
  __typename?: 'BillingTransaction';
  amount: Scalars['Int']['output'];
  comment: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  from: Scalars['String']['output'];
  id: Scalars['String']['output'];
  initiatorId: Scalars['String']['output'];
  operatorId: Scalars['String']['output'];
  to: Scalars['String']['output'];
  type: Scalars['String']['output'];
  units: Scalars['Int']['output'];
};

export type Bonus = {
  __typename?: 'Bonus';
  createdBy: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateDeleted: Scalars['String']['output'];
  dateExpired: Scalars['String']['output'];
  description: Scalars['String']['output'];
  id: Scalars['String']['output'];
  key: Scalars['String']['output'];
  period: Scalars['Int']['output'];
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
  units: Scalars['Int']['output'];
};

export type CsatSurvey = {
  __typename?: 'CSATSurvey';
  dateCreated: Scalars['String']['output'];
  estimate?: Maybe<Scalars['Int']['output']>;
  id: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type CloudSigner = {
  __typename?: 'CloudSigner';
  documentId: Scalars['String']['output'];
  operationId: Scalars['String']['output'];
};

export type Comment = {
  __typename?: 'Comment';
  accessCompanyId: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateEdited: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  documentVersion: DocumentVersion;
  documentVersionId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isRejection: Scalars['Boolean']['output'];
  role: CommentRole;
  roleId: Scalars['String']['output'];
  statusId: Scalars['Int']['output'];
  text: Scalars['String']['output'];
  type: Scalars['String']['output'];
  userId: Scalars['String']['output'];
};

export type CommentCompany = {
  __typename?: 'CommentCompany';
  edrpou: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isLegal: Scalars['Boolean']['output'];
};

export type CommentRole = {
  __typename?: 'CommentRole';
  company: CommentCompany;
  companyId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  user: CommentUser;
  userId: Scalars['String']['output'];
};

export type CommentUser = {
  __typename?: 'CommentUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type Company = {
  __typename?: 'Company';
  activeRates: Array<Scalars['String']['output']>;
  activityField?: Maybe<Scalars['String']['output']>;
  allowUnregisteredDocumentView: Scalars['Boolean']['output'];
  allowedApiIps: Array<Scalars['String']['output']>;
  allowedIps: Array<Scalars['String']['output']>;
  billingAccounts: Array<BillingAccount>;
  billingCompanyConfig: BillingCompanyConfig;
  bills: Array<Bill>;
  config?: Maybe<Scalars['Any']['output']>;
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  edrpou: Scalars['String']['output'];
  emailDomains: Array<Scalars['String']['output']>;
  employeesNumber?: Maybe<Scalars['String']['output']>;
  fullName?: Maybe<Scalars['String']['output']>;
  hasInvalidSignedDocuments: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  inactivityTimeout?: Maybe<Scalars['Int']['output']>;
  ipn: Scalars['String']['output'];
  isFop: Scalars['Boolean']['output'];
  isLegal: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  rateExtensions: Array<RateExtension>;
  rates: Array<CompanyRate>;
  renderSignatureInInterface: Scalars['Boolean']['output'];
  renderSignatureOnPrintDocument: Scalars['Boolean']['output'];
  roles: Array<CoworkerRole>;
  trialRates: Array<CompanyRate>;
  usedDocumentCount: Scalars['Int']['output'];
};


export type CompanyRolesArgs = {
  hasInvalidSignatures?: InputMaybe<Scalars['Boolean']['input']>;
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CompanyDocumentOwner = {
  __typename?: 'CompanyDocumentOwner';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type CompanyDocumentRecipient = {
  __typename?: 'CompanyDocumentRecipient';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type CompanyRate = {
  __typename?: 'CompanyRate';
  amount: Scalars['Int']['output'];
  billNumber?: Maybe<Scalars['String']['output']>;
  bill_number?: Maybe<Scalars['String']['output']>;
  config?: Maybe<Scalars['Any']['output']>;
  endDate: Scalars['String']['output'];
  id: Scalars['String']['output'];
  rate: Scalars['String']['output'];
  source: Scalars['String']['output'];
  startDate: Scalars['String']['output'];
  status: Scalars['String']['output'];
  units: Scalars['Int']['output'];
  unitsLeft: Scalars['Int']['output'];
  units_left: Scalars['Int']['output'];
};

export type Contact = {
  __typename?: 'Contact';
  companyId: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  edrpou: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isRegistered: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  persons: Array<ContactPerson>;
  shortName?: Maybe<Scalars['String']['output']>;
  tags: Array<Tag>;
};

export type ContactPerson = {
  __typename?: 'ContactPerson';
  contact?: Maybe<Contact>;
  contactId: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isEmailHidden: Scalars['Boolean']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  mainRecipient: Scalars['Boolean']['output'];
  phones: Array<ContactPersonPhone>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type ContactPersonPhone = {
  __typename?: 'ContactPersonPhone';
  phone: Scalars['String']['output'];
};

export type ContactPersonsList = {
  __typename?: 'ContactPersonsList';
  contact_person_ids: Array<Scalars['String']['output']>;
  contact_persons: Array<ContactPerson>;
  count: Scalars['Int']['output'];
};

export type ContactRecipient = {
  __typename?: 'ContactRecipient';
  edrpou: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  isMainRecipient: Scalars['Boolean']['output'];
  name?: Maybe<Scalars['String']['output']>;
  userName?: Maybe<Scalars['String']['output']>;
};

export type ContactsList = {
  __typename?: 'ContactsList';
  contact_ids: Array<Scalars['String']['output']>;
  contacts: Array<Contact>;
  count: Scalars['Int']['output'];
};

export type CoworkerRole = {
  __typename?: 'CoworkerRole';
  activatedBy?: Maybe<Scalars['String']['output']>;
  activationSource?: Maybe<Scalars['String']['output']>;
  allowedApiIps?: Maybe<Array<Scalars['String']['output']>>;
  allowedIps: Array<Scalars['String']['output']>;
  canArchiveDocuments: Scalars['Boolean']['output'];
  canChangeDocumentSignersAndReviewers: Scalars['Boolean']['output'];
  canCommentDocument: Scalars['Boolean']['output'];
  canCreateTags: Scalars['Boolean']['output'];
  canDeleteArchivedDocuments: Scalars['Boolean']['output'];
  canDeleteDocument: Scalars['Boolean']['output'];
  canDeleteDocumentExtended: Scalars['Boolean']['output'];
  canDownloadActions: Scalars['Boolean']['output'];
  canDownloadDocument: Scalars['Boolean']['output'];
  canEditClientData: Scalars['Boolean']['output'];
  canEditCompany: Scalars['Boolean']['output'];
  canEditCompanyContact: Scalars['Boolean']['output'];
  canEditDirectories: Scalars['Boolean']['output'];
  canEditDocumentCategory: Scalars['Boolean']['output'];
  canEditDocumentFields: Scalars['Boolean']['output'];
  canEditDocumentStructuredData: Scalars['Boolean']['output'];
  canEditDocumentTemplates: Scalars['Boolean']['output'];
  canEditRequiredFields: Scalars['Boolean']['output'];
  canEditRoles: Scalars['Boolean']['output'];
  canEditSecurity: Scalars['Boolean']['output'];
  canEditSpecialFeatures: Scalars['Boolean']['output'];
  canEditTemplates: Scalars['Boolean']['output'];
  canExtractDocumentStructuredData: Scalars['Boolean']['output'];
  canInviteCoworkers: Scalars['Boolean']['output'];
  canPrintDocument: Scalars['Boolean']['output'];
  canReceiveAccessToDoc: Scalars['Boolean']['output'];
  canReceiveAdminRoleDeletion: Scalars['Boolean']['output'];
  canReceiveComments: Scalars['Boolean']['output'];
  canReceiveDeleteRequests: Scalars['Boolean']['output'];
  canReceiveEmailChange: Scalars['Boolean']['output'];
  canReceiveFinishedDocs: Scalars['Boolean']['output'];
  canReceiveInbox: Scalars['Boolean']['output'];
  canReceiveInboxAsDefault: Scalars['Boolean']['output'];
  canReceiveNewRoles: Scalars['Boolean']['output'];
  canReceiveNotifications: Scalars['Boolean']['output'];
  canReceiveRejects: Scalars['Boolean']['output'];
  canReceiveReminders: Scalars['Boolean']['output'];
  canReceiveReviewProcessFinished: Scalars['Boolean']['output'];
  canReceiveReviewProcessFinishedAssigner: Scalars['Boolean']['output'];
  canReceiveReviews: Scalars['Boolean']['output'];
  canReceiveSignProcessFinished: Scalars['Boolean']['output'];
  canReceiveSignProcessFinishedAssigner: Scalars['Boolean']['output'];
  canReceiveTokenExpiration: Scalars['Boolean']['output'];
  canRemoveItselfFromApproval: Scalars['Boolean']['output'];
  canSignAndRejectDocument: Scalars['Boolean']['output'];
  canSignAndRejectDocumentExternal: Scalars['Boolean']['output'];
  canSignAndRejectDocumentInternal: Scalars['Boolean']['output'];
  canUploadDocument: Scalars['Boolean']['output'];
  canViewClientData: Scalars['Boolean']['output'];
  canViewCoworkers: Scalars['Boolean']['output'];
  canViewDocument: Scalars['Boolean']['output'];
  canViewPrivateDocument: Scalars['Boolean']['output'];
  companyEdrpou: Scalars['String']['output'];
  companyId: Scalars['String']['output'];
  dateActivated?: Maybe<Scalars['String']['output']>;
  dateAgreed: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateDeleted?: Maybe<Scalars['String']['output']>;
  dateInvited?: Maybe<Scalars['String']['output']>;
  dateUpdated: Scalars['String']['output'];
  deletedBy?: Maybe<Scalars['String']['output']>;
  fields: Array<DocumentsField>;
  hasFewReviews: Scalars['Boolean']['output'];
  hasFewSignatures: Scalars['Boolean']['output'];
  hasSignedDocuments: Scalars['Boolean']['output'];
  hasToken: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  invitedBy?: Maybe<Scalars['String']['output']>;
  isAdmin: Scalars['Boolean']['output'];
  isDefaultRecipient: Scalars['Boolean']['output'];
  isFreeRateBannerShown: Scalars['Boolean']['output'];
  isFreeTrialEndBannerShown: Scalars['Boolean']['output'];
  isMasterAdmin: Scalars['Boolean']['output'];
  isSuperAdmin: Scalars['Boolean']['output'];
  position?: Maybe<Scalars['String']['output']>;
  registrationReferralUrl: Scalars['String']['output'];
  showChildDocuments: Scalars['Boolean']['output'];
  showInviteTooltip: Scalars['Boolean']['output'];
  sortDocuments: Scalars['String']['output'];
  status: Scalars['String']['output'];
  tags: Array<Tag>;
  user: CoworkerUser;
  userId: Scalars['String']['output'];
  userRole: Scalars['Int']['output'];
};

export type CoworkerUser = {
  __typename?: 'CoworkerUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isRegistered: Scalars['Boolean']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
  sessions: Array<LoginSession>;
};

export type DeleteRequest = {
  __typename?: 'DeleteRequest';
  currentRoleEmail: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  initiatorEdrpou: Scalars['String']['output'];
  initiatorRoleId: Scalars['String']['output'];
  isReceiver: Scalars['Boolean']['output'];
  message: Scalars['String']['output'];
  receiverEdrpou: Scalars['String']['output'];
  recipientsEmails: Scalars['String']['output'];
  rejectMessage: Scalars['String']['output'];
  status: Scalars['String']['output'];
};

export type DirectoriesList = {
  __typename?: 'DirectoriesList';
  count: Scalars['Int']['output'];
  directories: Array<DocumentDirectory>;
  directory_ids: Array<Scalars['Int']['output']>;
};

export type Document = {
  __typename?: 'Document';
  accessLevel: Scalars['String']['output'];
  accesses: Array<DocumentAccess>;
  amount?: Maybe<Scalars['Int']['output']>;
  antivirusChecks: Array<AntivirusCheck>;
  archiveName?: Maybe<Scalars['String']['output']>;
  canDelete: Scalars['Boolean']['output'];
  canSign: Scalars['Boolean']['output'];
  category?: Maybe<Scalars['Int']['output']>;
  categoryDetails: DocumentCategory;
  children: Array<DocumentLink>;
  comments: Array<Comment>;
  companyOwner?: Maybe<CompanyDocumentOwner>;
  companyRecipient?: Maybe<CompanyDocumentRecipient>;
  contactPersonRecipient?: Maybe<ContactPerson>;
  contactRecipient?: Maybe<Contact>;
  dateCreated: Scalars['String']['output'];
  dateDelivered?: Maybe<Scalars['String']['output']>;
  dateDocument?: Maybe<Scalars['String']['output']>;
  dateFinished?: Maybe<Scalars['String']['output']>;
  dateListing: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  deleteRequest?: Maybe<DeleteRequest>;
  directory?: Maybe<DocumentDirectory>;
  displayCompanyEdrpou: Scalars['String']['output'];
  displayCompanyEmail: Scalars['String']['output'];
  displayCompanyName: Scalars['String']['output'];
  displayStatusText: Scalars['String']['output'];
  drafts: Array<Draft>;
  edrpouOwner: Scalars['String']['output'];
  edrpouRecipient?: Maybe<Scalars['String']['output']>;
  emailRecipient?: Maybe<Scalars['String']['output']>;
  expectedOwnerSignatures: Scalars['Int']['output'];
  expectedRecipientSignatures: Scalars['Int']['output'];
  expectedSignatureFormat: Scalars['String']['output'];
  extension: Scalars['String']['output'];
  firstSignBy: Scalars['String']['output'];
  flows: Array<DocumentFlow>;
  hasEUSignatures: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  isArchived: Scalars['Boolean']['output'];
  isDeleteLocked: Scalars['Boolean']['output'];
  isImported: Scalars['Boolean']['output'];
  isInput: Scalars['Boolean']['output'];
  isInternal: Scalars['Boolean']['output'];
  isInvalidSigned?: Maybe<Scalars['Boolean']['output']>;
  isMultilateral: Scalars['Boolean']['output'];
  isOneSign: Scalars['Boolean']['output'];
  isProtected: Scalars['Boolean']['output'];
  isRecipientEmailHidden?: Maybe<Scalars['Boolean']['output']>;
  isViewable: Scalars['Boolean']['output'];
  metadata?: Maybe<DocumentMeta>;
  number?: Maybe<Scalars['String']['output']>;
  parameters: Array<DocumentParameter>;
  parent?: Maybe<DocumentLink>;
  recipients: Array<DocumentRecipient>;
  reviewRequests: Array<ReviewRequest>;
  reviewSetting?: Maybe<ReviewSetting>;
  reviewStatus?: Maybe<Scalars['String']['output']>;
  reviews: Array<Review>;
  revoke?: Maybe<DocumentRevoke>;
  s3XmlToPdfKey?: Maybe<Scalars['String']['output']>;
  seqnum: Scalars['Int']['output'];
  signatures: Array<Signature>;
  signers: Array<DocumentSigner>;
  source: Scalars['String']['output'];
  statusId: Scalars['Int']['output'];
  tags: Array<Tag>;
  title: Scalars['String']['output'];
  type?: Maybe<Scalars['String']['output']>;
  uploadedBy: Scalars['String']['output'];
  user: DocumentUser;
  userId: Scalars['String']['output'];
  versions: Array<DocumentVersion>;
  viewerGroups: Array<GroupDocumentAccess>;
};


export type DocumentAccessesArgs = {
  source: Scalars['Int']['input'];
};


export type DocumentReviewRequestsArgs = {
  is_all_requests?: InputMaybe<Scalars['Boolean']['input']>;
};


export type DocumentReviewsArgs = {
  add_is_last_condition?: InputMaybe<Scalars['Boolean']['input']>;
};

export type DocumentAccess = {
  __typename?: 'DocumentAccess';
  dateCreated: Scalars['String']['output'];
  id: Scalars['String']['output'];
  role: DocumentAccessRole;
  roleId: Scalars['String']['output'];
};

export type DocumentAccessRole = {
  __typename?: 'DocumentAccessRole';
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: DocumentAccessUser;
  userId: Scalars['String']['output'];
};

export type DocumentAccessUser = {
  __typename?: 'DocumentAccessUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type DocumentAutomationCondition = {
  __typename?: 'DocumentAutomationCondition';
  conditions?: Maybe<Scalars['Any']['output']>;
  id: Scalars['String']['output'];
  involvedCompanies: Array<AutomationConditionCompany>;
  status: Scalars['String']['output'];
};

export type DocumentAutomationTemplate = {
  __typename?: 'DocumentAutomationTemplate';
  assignedTo?: Maybe<CoworkerRole>;
  assignedToId?: Maybe<Scalars['String']['output']>;
  automation: Array<DocumentAutomationCondition>;
  fieldsSettings?: Maybe<Scalars['Any']['output']>;
  id: Scalars['String']['output'];
  isActive: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  reviewSettings?: Maybe<Scalars['Any']['output']>;
  reviewers: Array<DocumentAutomationTemplateEntity>;
  signers: Array<DocumentAutomationTemplateEntity>;
  signersSettings?: Maybe<Scalars['Any']['output']>;
  tags: Array<Tag>;
  tagsSettings?: Maybe<Scalars['Any']['output']>;
  viewerGroups: Array<Group>;
  viewerRoles: Array<DocumentAutomationTemplateRole>;
  viewersSettings?: Maybe<Scalars['Any']['output']>;
};

export type DocumentAutomationTemplateEntity = {
  __typename?: 'DocumentAutomationTemplateEntity';
  group?: Maybe<Group>;
  id: Scalars['String']['output'];
  role?: Maybe<DocumentAutomationTemplateRole>;
  type: Scalars['String']['output'];
};

export type DocumentAutomationTemplateRole = {
  __typename?: 'DocumentAutomationTemplateRole';
  canSignAndRejectDocument: Scalars['Boolean']['output'];
  canSignAndRejectDocumentExternal: Scalars['Boolean']['output'];
  canSignAndRejectDocumentInternal: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: DocumentAutomationTemplateUser;
  userId: Scalars['String']['output'];
};

export type DocumentAutomationTemplateUser = {
  __typename?: 'DocumentAutomationTemplateUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type DocumentAvailableCompany = {
  __typename?: 'DocumentAvailableCompany';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type DocumentAvailableRole = {
  __typename?: 'DocumentAvailableRole';
  company: DocumentAvailableCompany;
  companyId: Scalars['String']['output'];
  id: Scalars['String']['output'];
};

export type DocumentCategoriesList = {
  __typename?: 'DocumentCategoriesList';
  count: Scalars['Int']['output'];
  documentCategories: Array<DocumentCategory>;
  document_categories_ids: Array<Scalars['String']['output']>;
};

export type DocumentCategory = {
  __typename?: 'DocumentCategory';
  companyId?: Maybe<Scalars['String']['output']>;
  dateCreated: Scalars['String']['output'];
  dateDeleted?: Maybe<Scalars['String']['output']>;
  dateUpdated: Scalars['String']['output'];
  id: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type DocumentDirectory = {
  __typename?: 'DocumentDirectory';
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  parentId?: Maybe<Scalars['Int']['output']>;
  /** Do not use that field while getting list of directories. Try to use it only for node "directory" from graph_root */
  path: Array<SimpleDirectory>;
};

export type DocumentFlow = {
  __typename?: 'DocumentFlow';
  canSign: Scalars['Boolean']['output'];
  companyId: Scalars['String']['output'];
  dateSent: Scalars['String']['output'];
  displayCompanyName?: Maybe<Scalars['String']['output']>;
  edrpou: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isComplete: Scalars['Boolean']['output'];
  meta?: Maybe<Scalars['Any']['output']>;
  order: Scalars['Int']['output'];
  pendingSignaturesCount: Scalars['Int']['output'];
  receivers?: Maybe<Scalars['Any']['output']>;
  receiversId: Scalars['String']['output'];
  recipient: DocumentRecipient;
  recipientId: Scalars['String']['output'];
  signaturesCount: Scalars['Int']['output'];
};

export type DocumentLink = {
  __typename?: 'DocumentLink';
  creatorEdrpou: Scalars['String']['output'];
  document: Document;
  documentId: Scalars['String']['output'];
};

export type DocumentMeta = {
  __typename?: 'DocumentMeta';
  contentHash: Scalars['String']['output'];
  contentLength: Scalars['Int']['output'];
};

export type DocumentParameter = {
  __typename?: 'DocumentParameter';
  fieldId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isRequired: Scalars['Boolean']['output'];
  value: Scalars['String']['output'];
};

export type DocumentRecipient = {
  __typename?: 'DocumentRecipient';
  dateDelivered?: Maybe<Scalars['String']['output']>;
  dateReceived?: Maybe<Scalars['String']['output']>;
  dateSent?: Maybe<Scalars['String']['output']>;
  document_id: Scalars['String']['output'];
  edrpou: Scalars['String']['output'];
  emails?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['String']['output'];
  isEmailsHidden: Scalars['Boolean']['output'];
};

export type DocumentRequiredField = {
  __typename?: 'DocumentRequiredField';
  company: DocumentRequiredFieldCompany;
  companyId: Scalars['String']['output'];
  documentCategory: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isAmountRequired: Scalars['Boolean']['output'];
  isDateRequired: Scalars['Boolean']['output'];
  isNameRequired: Scalars['Boolean']['output'];
  isNumberRequired: Scalars['Boolean']['output'];
  isTypeRequired: Scalars['Boolean']['output'];
};

export type DocumentRequiredFieldCompany = {
  __typename?: 'DocumentRequiredFieldCompany';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type DocumentRevoke = {
  __typename?: 'DocumentRevoke';
  documentId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  initiatorCompany: DocumentRevokeCompany;
  initiatorCompanyId: Scalars['String']['output'];
  initiatorRole: RevokeUser;
  initiatorRoleId: Scalars['String']['output'];
  reason: Scalars['String']['output'];
  signatureFormat: Scalars['String']['output'];
  signatures: Array<DocumentRevokeSignature>;
  status: Scalars['String']['output'];
};

export type DocumentRevokeCompany = {
  __typename?: 'DocumentRevokeCompany';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type DocumentRevokeSignature = {
  __typename?: 'DocumentRevokeSignature';
  dateCreated: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isInternal: Scalars['Boolean']['output'];
  keyAcsk: Scalars['String']['output'];
  keyCompanyFullName?: Maybe<Scalars['String']['output']>;
  keyOwnerEdrpou: Scalars['String']['output'];
  keyOwnerFullName: Scalars['String']['output'];
  keyOwnerPosition?: Maybe<Scalars['String']['output']>;
  keySerialNumber: Scalars['String']['output'];
  keyTimeMark: Scalars['String']['output'];
  revokeId: Scalars['String']['output'];
  roleId: Scalars['String']['output'];
  stampAcsk?: Maybe<Scalars['String']['output']>;
  stampCompanyFullName?: Maybe<Scalars['String']['output']>;
  stampOwnerEdrpou?: Maybe<Scalars['String']['output']>;
  stampOwnerFullName?: Maybe<Scalars['String']['output']>;
  stampOwnerPosition?: Maybe<Scalars['String']['output']>;
  stampSerialNumber?: Maybe<Scalars['String']['output']>;
  stampTimeMark?: Maybe<Scalars['String']['output']>;
  user: SignatureUser;
  userEmail: Scalars['String']['output'];
};

export type DocumentSigner = {
  __typename?: 'DocumentSigner';
  assignerId?: Maybe<Scalars['String']['output']>;
  companyId: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateSigned?: Maybe<Scalars['String']['output']>;
  documentId: Scalars['String']['output'];
  group: Group;
  groupId?: Maybe<Scalars['String']['output']>;
  groupSignedBy: SignerRole;
  groupSignerId?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  order?: Maybe<Scalars['Int']['output']>;
  role: SignerRole;
  roleId?: Maybe<Scalars['String']['output']>;
};

export type DocumentUser = {
  __typename?: 'DocumentUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type DocumentVersion = {
  __typename?: 'DocumentVersion';
  antivirusChecks: Array<AntivirusCheck>;
  contentHash: Scalars['String']['output'];
  contentLength: Scalars['Int']['output'];
  dateCreated: Scalars['String']['output'];
  extension: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isSent: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  number: Scalars['String']['output'];
  reviewStatus?: Maybe<Scalars['String']['output']>;
  role: VersionRole;
  roleId: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type DocumentsField = {
  __typename?: 'DocumentsField';
  canEdit: Scalars['Boolean']['output'];
  enumOptions: Array<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isRequired: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  order?: Maybe<Scalars['Int']['output']>;
  roles: Array<CoworkerRole>;
  type: Scalars['String']['output'];
};

export type DocumentsList = {
  __typename?: 'DocumentsList';
  count: Scalars['Int']['output'];
  document_ids: Array<Scalars['String']['output']>;
  documents: Array<Document>;
};

export type Draft = {
  __typename?: 'Draft';
  antivirusCheck?: Maybe<DraftAntivirusCheck>;
  companyId: Scalars['String']['output'];
  creatorRole: DraftRole;
  creatorRoleId: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateScheduledDeletion?: Maybe<Scalars['String']['output']>;
  dateUpdated?: Maybe<Scalars['String']['output']>;
  documentId?: Maybe<Scalars['String']['output']>;
  documentVersionId?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  template?: Maybe<Template>;
  templateId?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type DraftAntivirusCheck = {
  __typename?: 'DraftAntivirusCheck';
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  draftId: Scalars['String']['output'];
  provider: Scalars['String']['output'];
  status: Scalars['String']['output'];
};

export type DraftCompany = {
  __typename?: 'DraftCompany';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type DraftRole = {
  __typename?: 'DraftRole';
  company: DraftCompany;
  companyId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: DraftUser;
  userId: Scalars['String']['output'];
};

export type DraftUser = {
  __typename?: 'DraftUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type DraftsList = {
  __typename?: 'DraftsList';
  count: Scalars['Int']['output'];
  draft_ids: Array<Scalars['String']['output']>;
  drafts: Array<Draft>;
};

export type Group = {
  __typename?: 'Group';
  createdBy: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  documentAutomationTemplates: Array<DocumentAutomationTemplate>;
  documentReviewers: Array<Document>;
  documentSigners: Array<Document>;
  id: Scalars['String']['output'];
  members: Array<GroupMember>;
  name: Scalars['String']['output'];
};

export type GroupDocumentAccess = {
  __typename?: 'GroupDocumentAccess';
  createdBy: Scalars['String']['output'];
  createdByRole: CoworkerRole;
  dateCreated: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  group: Group;
  groupId: Scalars['String']['output'];
  id: Scalars['String']['output'];
};

export type GroupMember = {
  __typename?: 'GroupMember';
  createdBy: Scalars['String']['output'];
  createdByRole: CoworkerRole;
  dateCreated: Scalars['String']['output'];
  groupId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  role: CoworkerRole;
  roleId: Scalars['String']['output'];
};

export type GroupsList = {
  __typename?: 'GroupsList';
  count: Scalars['Int']['output'];
  group_ids: Array<Scalars['String']['output']>;
  groups: Array<Group>;
};

export type LoginSession = {
  __typename?: 'LoginSession';
  accessedAt: Scalars['String']['output'];
  browser: Scalars['String']['output'];
  browserVersion?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  device: Scalars['String']['output'];
  id: Scalars['String']['output'];
  ip: Scalars['String']['output'];
  isCurrent: Scalars['Boolean']['output'];
  os: Scalars['String']['output'];
  osVersion?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  /** Select active banner */
  activeBanner?: Maybe<Banner>;
  /** All archive documents and directories accessible by current user. */
  allArchiveItems: ArchiveList;
  /** Get list of operation ids and its document ids for current user */
  allCloudSigners: Array<CloudSigner>;
  /** All persons added to contacts of current company. */
  allContactPersons: ContactPersonsList;
  allContactRecipients: Array<ContactRecipient>;
  /** All contacts added to current company. */
  allContacts: ContactsList;
  allDirectories: DirectoriesList;
  /** Select all documents categories available for company */
  allDocumentCategories: DocumentCategoriesList;
  /** Get all required fields for company. For current company if no companies_ids nor edrpous are passed */
  allDocumentRequiredFields: Array<DocumentRequiredField>;
  /** All documents accessible by current user. */
  allDocuments: DocumentsList;
  allDrafts: DraftsList;
  allGroups: GroupsList;
  /** All tags for current user. */
  allTags: Array<Tag>;
  /** All tags visible in document filter */
  allTagsForDocumentFilter: Array<Tag>;
  allTemplates: TemplatesList;
  /** Retrieve company info accessible by current user by its ID. */
  company?: Maybe<Company>;
  /** Count documents accessible by current user. */
  countDocuments: Scalars['Int']['output'];
  /** Return number of unregistered companies in contacts. */
  countUnregisteredContacts: Scalars['Int']['output'];
  /** All roles of current company. */
  currentCompanyRoles: Array<CoworkerRole>;
  /** Current logged in role. Role connected company & user. */
  currentRole?: Maybe<Role>;
  /** Current sign session if any. */
  currentSignSession?: Maybe<SignSession>;
  /** Current logged in user. */
  currentUser?: Maybe<User>;
  directory?: Maybe<DocumentDirectory>;
  /** Fetch document accessible by current user by its ID. */
  document?: Maybe<Document>;
  /** Select all document templates for given company */
  documentAutomationTemplates: Array<DocumentAutomationTemplate>;
  /** Get all roles from which document is available for given user */
  documentAvailableRoles: Array<DocumentAvailableRole>;
  /** Select all documents fields for given company */
  documentsFields: Array<DocumentsField>;
  group?: Maybe<Group>;
  /** All available companies. Only for super admin. */
  saAllCompanies: Array<Company>;
  /** All available users. Only for super admin. */
  saAllUsers: Array<User>;
  saBanner: Array<Banner>;
  /** Retrieve company info by its ID. Only for super admin. */
  saCompany?: Maybe<Company>;
  /** Retrieve user info by its ID. Only for super admin. */
  saUser?: Maybe<User>;
  /** Show notification for current role */
  triggerNotifications: Array<TriggerNotification>;
};


export type QueryAllArchiveItemsArgs = {
  accessLevel?: InputMaybe<Scalars['String']['input']>;
  amountEq?: InputMaybe<Scalars['Int']['input']>;
  amountGte?: InputMaybe<Scalars['Int']['input']>;
  amountLte?: InputMaybe<Scalars['Int']['input']>;
  categories?: InputMaybe<Array<Scalars['Int']['input']>>;
  condition?: InputMaybe<Scalars['String']['input']>;
  condition2?: InputMaybe<Scalars['String']['input']>;
  conditions?: InputMaybe<Array<Scalars['String']['input']>>;
  conditions2?: InputMaybe<Array<Scalars['String']['input']>>;
  direction?: InputMaybe<Scalars['String']['input']>;
  firstSignBy?: InputMaybe<Scalars['String']['input']>;
  folderId?: InputMaybe<Scalars['Int']['input']>;
  folderIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  gte?: InputMaybe<Scalars['String']['input']>;
  hasComments?: InputMaybe<Scalars['Boolean']['input']>;
  hasDateDelivered?: InputMaybe<Scalars['Boolean']['input']>;
  ids?: InputMaybe<Array<Scalars['String']['input']>>;
  invalidSignedRoles?: InputMaybe<Array<Scalars['String']['input']>>;
  isArchived?: InputMaybe<Scalars['Boolean']['input']>;
  isMyInvalidSigned?: InputMaybe<Scalars['Boolean']['input']>;
  isOneSign?: InputMaybe<Scalars['Boolean']['input']>;
  isPartnerInvalidSigned?: InputMaybe<Scalars['Boolean']['input']>;
  isWaitMySign?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Scalars['String']['input']>;
  orderField?: InputMaybe<Scalars['String']['input']>;
  parentDirectoryId?: InputMaybe<Scalars['Int']['input']>;
  reviewFolder?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  searchCompanyEdrpou?: InputMaybe<Array<Scalars['String']['input']>>;
  searchCompanyName?: InputMaybe<Array<Scalars['String']['input']>>;
  searchNumber?: InputMaybe<Array<Scalars['String']['input']>>;
  searchParameter?: InputMaybe<Array<Scalars['String']['input']>>;
  searchTag?: InputMaybe<Array<Scalars['String']['input']>>;
  searchTitle?: InputMaybe<Array<Scalars['String']['input']>>;
  searchUserEmail?: InputMaybe<Array<Scalars['String']['input']>>;
  seqnumOffset?: InputMaybe<Scalars['Int']['input']>;
  sortDate?: InputMaybe<Scalars['String']['input']>;
  statusId?: InputMaybe<Scalars['Int']['input']>;
  statusIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  tag?: InputMaybe<Scalars['String']['input']>;
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
  withoutTags?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryAllContactPersonsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAllContactRecipientsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAllContactsArgs = {
  isRegistered?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAllDirectoriesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  parentId?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAllDocumentCategoriesArgs = {
  ids?: InputMaybe<Array<Scalars['Int']['input']>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  onlyInternal?: InputMaybe<Scalars['Boolean']['input']>;
  onlyPublic?: InputMaybe<Scalars['Boolean']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAllDocumentRequiredFieldsArgs = {
  companies_ids?: InputMaybe<Array<Scalars['String']['input']>>;
  edrpous?: InputMaybe<Array<Scalars['String']['input']>>;
};


export type QueryAllDocumentsArgs = {
  accessLevel?: InputMaybe<Scalars['String']['input']>;
  amountEq?: InputMaybe<Scalars['Int']['input']>;
  amountGte?: InputMaybe<Scalars['Int']['input']>;
  amountLte?: InputMaybe<Scalars['Int']['input']>;
  categories?: InputMaybe<Array<Scalars['Int']['input']>>;
  condition?: InputMaybe<Scalars['String']['input']>;
  condition2?: InputMaybe<Scalars['String']['input']>;
  conditions?: InputMaybe<Array<Scalars['String']['input']>>;
  conditions2?: InputMaybe<Array<Scalars['String']['input']>>;
  direction?: InputMaybe<Scalars['String']['input']>;
  firstSignBy?: InputMaybe<Scalars['String']['input']>;
  folderId?: InputMaybe<Scalars['Int']['input']>;
  folderIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  gte?: InputMaybe<Scalars['String']['input']>;
  hasComments?: InputMaybe<Scalars['Boolean']['input']>;
  hasDateDelivered?: InputMaybe<Scalars['Boolean']['input']>;
  ids?: InputMaybe<Array<Scalars['String']['input']>>;
  invalidSignedRoles?: InputMaybe<Array<Scalars['String']['input']>>;
  isArchived?: InputMaybe<Scalars['Boolean']['input']>;
  isMyInvalidSigned?: InputMaybe<Scalars['Boolean']['input']>;
  isOneSign?: InputMaybe<Scalars['Boolean']['input']>;
  isPartnerInvalidSigned?: InputMaybe<Scalars['Boolean']['input']>;
  isWaitMySign?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order?: InputMaybe<Scalars['String']['input']>;
  orderField?: InputMaybe<Scalars['String']['input']>;
  parentDirectoryId?: InputMaybe<Scalars['Int']['input']>;
  reviewFolder?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  searchCompanyEdrpou?: InputMaybe<Array<Scalars['String']['input']>>;
  searchCompanyName?: InputMaybe<Array<Scalars['String']['input']>>;
  searchNumber?: InputMaybe<Array<Scalars['String']['input']>>;
  searchParameter?: InputMaybe<Array<Scalars['String']['input']>>;
  searchTag?: InputMaybe<Array<Scalars['String']['input']>>;
  searchTitle?: InputMaybe<Array<Scalars['String']['input']>>;
  searchUserEmail?: InputMaybe<Array<Scalars['String']['input']>>;
  seqnumOffset?: InputMaybe<Scalars['Int']['input']>;
  sortDate?: InputMaybe<Scalars['String']['input']>;
  statusId?: InputMaybe<Scalars['Int']['input']>;
  statusIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  tag?: InputMaybe<Scalars['String']['input']>;
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
  withoutTags?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryAllDraftsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  types?: InputMaybe<Array<Scalars['String']['input']>>;
};


export type QueryAllGroupsArgs = {
  limit?: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  offset?: Scalars['Int']['input'];
};


export type QueryAllTagsArgs = {
  hasRoles?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAllTemplatesArgs = {
  categories?: InputMaybe<Array<Scalars['Int']['input']>>;
  is_favorite?: InputMaybe<Scalars['Boolean']['input']>;
  is_public?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryCompanyArgs = {
  id: Scalars['String']['input'];
};


export type QueryCountDocumentsArgs = {
  accessLevel?: InputMaybe<Scalars['String']['input']>;
  amountEq?: InputMaybe<Scalars['Int']['input']>;
  amountGte?: InputMaybe<Scalars['Int']['input']>;
  amountLte?: InputMaybe<Scalars['Int']['input']>;
  categories?: InputMaybe<Array<Scalars['Int']['input']>>;
  condition?: InputMaybe<Scalars['String']['input']>;
  condition2?: InputMaybe<Scalars['String']['input']>;
  conditions?: InputMaybe<Array<Scalars['String']['input']>>;
  conditions2?: InputMaybe<Array<Scalars['String']['input']>>;
  firstSignBy?: InputMaybe<Scalars['String']['input']>;
  folderId?: InputMaybe<Scalars['Int']['input']>;
  folderIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  gte?: InputMaybe<Scalars['String']['input']>;
  hasComments?: InputMaybe<Scalars['Boolean']['input']>;
  hasDateDelivered?: InputMaybe<Scalars['Boolean']['input']>;
  ids?: InputMaybe<Array<Scalars['String']['input']>>;
  invalidSignedRoles?: InputMaybe<Array<Scalars['String']['input']>>;
  isArchived?: InputMaybe<Scalars['Boolean']['input']>;
  isMyInvalidSigned?: InputMaybe<Scalars['Boolean']['input']>;
  isOneSign?: InputMaybe<Scalars['Boolean']['input']>;
  isPartnerInvalidSigned?: InputMaybe<Scalars['Boolean']['input']>;
  isWaitMySign?: InputMaybe<Scalars['Boolean']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  parentDirectoryId?: InputMaybe<Scalars['Int']['input']>;
  reviewFolder?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
  searchCompanyEdrpou?: InputMaybe<Array<Scalars['String']['input']>>;
  searchCompanyName?: InputMaybe<Array<Scalars['String']['input']>>;
  searchNumber?: InputMaybe<Array<Scalars['String']['input']>>;
  searchParameter?: InputMaybe<Array<Scalars['String']['input']>>;
  searchTag?: InputMaybe<Array<Scalars['String']['input']>>;
  searchTitle?: InputMaybe<Array<Scalars['String']['input']>>;
  searchUserEmail?: InputMaybe<Array<Scalars['String']['input']>>;
  sortDate?: InputMaybe<Scalars['String']['input']>;
  statusId?: InputMaybe<Scalars['Int']['input']>;
  statusIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  tag?: InputMaybe<Scalars['String']['input']>;
  tags?: InputMaybe<Array<Scalars['String']['input']>>;
  withoutTags?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryCurrentCompanyRolesArgs = {
  canSignAndRejectDocument?: InputMaybe<Scalars['Boolean']['input']>;
  canSignAndRejectDocumentExternal?: InputMaybe<Scalars['Boolean']['input']>;
  canSignAndRejectDocumentInternal?: InputMaybe<Scalars['Boolean']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDirectoryArgs = {
  id: Scalars['Int']['input'];
};


export type QueryDocumentArgs = {
  id: Scalars['String']['input'];
  withError?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryDocumentAutomationTemplatesArgs = {
  is_active?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryDocumentAvailableRolesArgs = {
  documentId: Scalars['String']['input'];
};


export type QueryDocumentsFieldsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGroupArgs = {
  id: Scalars['String']['input'];
};


export type QuerySaAllCompaniesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySaAllUsersArgs = {
  gte?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  lte?: InputMaybe<Scalars['String']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySaBannerArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
};


export type QuerySaCompanyArgs = {
  id: Scalars['String']['input'];
};


export type QuerySaUserArgs = {
  id: Scalars['String']['input'];
};


export type QueryTriggerNotificationsArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type RateExtension = {
  __typename?: 'RateExtension';
  bill_document_id: Scalars['String']['output'];
  bill_id: Scalars['String']['output'];
  date_expiring?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  status: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type Review = {
  __typename?: 'Review';
  dateCreated: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  documentVersion?: Maybe<DocumentVersion>;
  documentVersionId?: Maybe<Scalars['String']['output']>;
  group: Group;
  groupId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  role: ReviewRole;
  roleId: Scalars['String']['output'];
  type: Scalars['String']['output'];
  userEmail: Scalars['String']['output'];
};

export type ReviewRequest = {
  __typename?: 'ReviewRequest';
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  documentVersion?: Maybe<DocumentVersion>;
  documentVersionId: Scalars['String']['output'];
  fromRole: ReviewRequestRole;
  fromRoleId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  order?: Maybe<Scalars['Int']['output']>;
  status: Scalars['String']['output'];
  toGroup: Group;
  toGroupId: Scalars['String']['output'];
  toRole: ReviewRequestRole;
  toRoleId: Scalars['String']['output'];
};

export type ReviewRequestRole = {
  __typename?: 'ReviewRequestRole';
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: ReviewRequestUser;
  userId: Scalars['String']['output'];
};

export type ReviewRequestUser = {
  __typename?: 'ReviewRequestUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type ReviewRole = {
  __typename?: 'ReviewRole';
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: ReviewUser;
  userId: Scalars['String']['output'];
};

export type ReviewSetting = {
  __typename?: 'ReviewSetting';
  companyId: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isParallel: Scalars['Boolean']['output'];
  isRequired: Scalars['Boolean']['output'];
};

export type ReviewUser = {
  __typename?: 'ReviewUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type RevokeUser = {
  __typename?: 'RevokeUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type Role = {
  __typename?: 'Role';
  activatedBy?: Maybe<Scalars['String']['output']>;
  activationSource?: Maybe<Scalars['String']['output']>;
  allowedApiIps?: Maybe<Array<Scalars['String']['output']>>;
  allowedIps: Array<Scalars['String']['output']>;
  canArchiveDocuments: Scalars['Boolean']['output'];
  canChangeDocumentSignersAndReviewers: Scalars['Boolean']['output'];
  canCommentDocument: Scalars['Boolean']['output'];
  canCreateTags: Scalars['Boolean']['output'];
  canDeleteArchivedDocuments: Scalars['Boolean']['output'];
  canDeleteDocument: Scalars['Boolean']['output'];
  canDeleteDocumentExtended: Scalars['Boolean']['output'];
  canDownloadActions: Scalars['Boolean']['output'];
  canDownloadDocument: Scalars['Boolean']['output'];
  canEditClientData: Scalars['Boolean']['output'];
  canEditCompany: Scalars['Boolean']['output'];
  canEditCompanyContact: Scalars['Boolean']['output'];
  canEditDirectories: Scalars['Boolean']['output'];
  canEditDocumentCategory: Scalars['Boolean']['output'];
  canEditDocumentFields: Scalars['Boolean']['output'];
  canEditDocumentStructuredData: Scalars['Boolean']['output'];
  canEditDocumentTemplates: Scalars['Boolean']['output'];
  canEditRequiredFields: Scalars['Boolean']['output'];
  canEditRoles: Scalars['Boolean']['output'];
  canEditSecurity: Scalars['Boolean']['output'];
  canEditSpecialFeatures: Scalars['Boolean']['output'];
  canEditTemplates: Scalars['Boolean']['output'];
  canExtractDocumentStructuredData: Scalars['Boolean']['output'];
  canInviteCoworkers: Scalars['Boolean']['output'];
  canPrintDocument: Scalars['Boolean']['output'];
  canReceiveAccessToDoc: Scalars['Boolean']['output'];
  canReceiveAdminRoleDeletion: Scalars['Boolean']['output'];
  canReceiveComments: Scalars['Boolean']['output'];
  canReceiveDeleteRequests: Scalars['Boolean']['output'];
  canReceiveEmailChange: Scalars['Boolean']['output'];
  canReceiveFinishedDocs: Scalars['Boolean']['output'];
  canReceiveInbox: Scalars['Boolean']['output'];
  canReceiveInboxAsDefault: Scalars['Boolean']['output'];
  canReceiveNewRoles: Scalars['Boolean']['output'];
  canReceiveNotifications: Scalars['Boolean']['output'];
  canReceiveRejects: Scalars['Boolean']['output'];
  canReceiveReminders: Scalars['Boolean']['output'];
  canReceiveReviewProcessFinished: Scalars['Boolean']['output'];
  canReceiveReviewProcessFinishedAssigner: Scalars['Boolean']['output'];
  canReceiveReviews: Scalars['Boolean']['output'];
  canReceiveSignProcessFinished: Scalars['Boolean']['output'];
  canReceiveSignProcessFinishedAssigner: Scalars['Boolean']['output'];
  canReceiveTokenExpiration: Scalars['Boolean']['output'];
  canRemoveItselfFromApproval: Scalars['Boolean']['output'];
  canSignAndRejectDocument: Scalars['Boolean']['output'];
  canSignAndRejectDocumentExternal: Scalars['Boolean']['output'];
  canSignAndRejectDocumentInternal: Scalars['Boolean']['output'];
  canUploadDocument: Scalars['Boolean']['output'];
  canViewClientData: Scalars['Boolean']['output'];
  canViewCoworkers: Scalars['Boolean']['output'];
  canViewDocument: Scalars['Boolean']['output'];
  canViewPrivateDocument: Scalars['Boolean']['output'];
  company: Company;
  companyEdrpou: Scalars['String']['output'];
  companyId: Scalars['String']['output'];
  dateActivated?: Maybe<Scalars['String']['output']>;
  dateAgreed: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateDeleted?: Maybe<Scalars['String']['output']>;
  dateInvited?: Maybe<Scalars['String']['output']>;
  dateUpdated: Scalars['String']['output'];
  deletedBy?: Maybe<Scalars['String']['output']>;
  hasFewReviews: Scalars['Boolean']['output'];
  hasFewSignatures: Scalars['Boolean']['output'];
  hasSignedDocuments: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  invitedBy?: Maybe<Scalars['String']['output']>;
  isAdmin: Scalars['Boolean']['output'];
  isDefaultRecipient: Scalars['Boolean']['output'];
  isFreeRateBannerShown: Scalars['Boolean']['output'];
  isFreeTrialEndBannerShown: Scalars['Boolean']['output'];
  isMasterAdmin: Scalars['Boolean']['output'];
  isSuperAdmin: Scalars['Boolean']['output'];
  position?: Maybe<Scalars['String']['output']>;
  registrationReferralUrl: Scalars['String']['output'];
  showChildDocuments: Scalars['Boolean']['output'];
  showInviteTooltip: Scalars['Boolean']['output'];
  sortDocuments: Scalars['String']['output'];
  status: Scalars['String']['output'];
  user: User;
  userId: Scalars['String']['output'];
  userRole: Scalars['Int']['output'];
};

export type SignSession = {
  __typename?: 'SignSession';
  cancelUrl: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  documentStatus: Scalars['String']['output'];
  edrpou?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  finishUrl: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isLegal?: Maybe<Scalars['Boolean']['output']>;
  role?: Maybe<SignSessionRole>;
  roleId?: Maybe<Scalars['String']['output']>;
  signParameters?: Maybe<Scalars['Any']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type SignSessionRole = {
  __typename?: 'SignSessionRole';
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  user: SignSessionUser;
  userId: Scalars['String']['output'];
};

export type SignSessionUser = {
  __typename?: 'SignSessionUser';
  id: Scalars['String']['output'];
  isPhoneVerified: Scalars['Boolean']['output'];
  phone: Scalars['String']['output'];
};

export type Signature = {
  __typename?: 'Signature';
  dateCreated: Scalars['String']['output'];
  documentId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isInternal: Scalars['Boolean']['output'];
  isValid: Scalars['Boolean']['output'];
  keyAcsk: Scalars['String']['output'];
  keyCompanyFullName?: Maybe<Scalars['String']['output']>;
  keyIsLegal: Scalars['Boolean']['output'];
  keyOwnerEdrpou: Scalars['String']['output'];
  keyOwnerFullName: Scalars['String']['output'];
  keyOwnerPosition?: Maybe<Scalars['String']['output']>;
  keySerialNumber: Scalars['String']['output'];
  keyTimeMark: Scalars['String']['output'];
  roleId: Scalars['String']['output'];
  stampAcsk?: Maybe<Scalars['String']['output']>;
  stampCompanyFullName?: Maybe<Scalars['String']['output']>;
  stampIsLegal?: Maybe<Scalars['Boolean']['output']>;
  stampOwnerEdrpou?: Maybe<Scalars['String']['output']>;
  stampOwnerFullName?: Maybe<Scalars['String']['output']>;
  stampOwnerPosition?: Maybe<Scalars['String']['output']>;
  stampSerialNumber?: Maybe<Scalars['String']['output']>;
  stampTimeMark?: Maybe<Scalars['String']['output']>;
  user: SignatureUser;
  userEmail: Scalars['String']['output'];
  userId: Scalars['String']['output'];
};

export type SignaturePlaceholderUser = {
  __typename?: 'SignaturePlaceholderUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type SignatureUser = {
  __typename?: 'SignatureUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type SignerRole = {
  __typename?: 'SignerRole';
  canSignAndRejectDocument: Scalars['Boolean']['output'];
  canSignAndRejectDocumentExternal: Scalars['Boolean']['output'];
  canSignAndRejectDocumentInternal: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: SignerUser;
  userId: Scalars['String']['output'];
};

export type SignerUser = {
  __typename?: 'SignerUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type SimpleDirectory = {
  __typename?: 'SimpleDirectory';
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type Tag = {
  __typename?: 'Tag';
  canAssign: Scalars['Boolean']['output'];
  companyId: Scalars['String']['output'];
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type Template = {
  __typename?: 'Template';
  category?: Maybe<Scalars['String']['output']>;
  companyId?: Maybe<Scalars['String']['output']>;
  creatorRole: TemplateRole;
  creatorRoleId?: Maybe<Scalars['String']['output']>;
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  extension: Scalars['String']['output'];
  id: Scalars['String']['output'];
  isFavorite: Scalars['Boolean']['output'];
  previewImgUrl: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type TemplateCompany = {
  __typename?: 'TemplateCompany';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type TemplateRole = {
  __typename?: 'TemplateRole';
  company: TemplateCompany;
  companyId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: TemplateUser;
  userId: Scalars['String']['output'];
};

export type TemplateUser = {
  __typename?: 'TemplateUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type TemplatesList = {
  __typename?: 'TemplatesList';
  count: Scalars['Int']['output'];
  template_ids: Array<Scalars['String']['output']>;
  templates: Array<Template>;
};

export type TriggerNotification = {
  __typename?: 'TriggerNotification';
  context?: Maybe<Scalars['Any']['output']>;
  description: Scalars['String']['output'];
  displayDate: Scalars['String']['output'];
  id: Scalars['String']['output'];
  status: Scalars['String']['output'];
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
  url?: Maybe<Scalars['String']['output']>;
};

export type User = {
  __typename?: 'User';
  activeSurveys: Array<Scalars['String']['output']>;
  authPhone?: Maybe<Scalars['String']['output']>;
  createdBy: Scalars['String']['output'];
  csatSurveys: Array<CsatSurvey>;
  dateCreated: Scalars['String']['output'];
  dateUpdated: Scalars['String']['output'];
  email?: Maybe<Scalars['String']['output']>;
  emailConfirmed: Scalars['Boolean']['output'];
  firstName?: Maybe<Scalars['String']['output']>;
  hasPassword: Scalars['Boolean']['output'];
  id: Scalars['String']['output'];
  is2FAEnabledByRule: Scalars['Boolean']['output'];
  is2FAEnabledInProfile: Scalars['Boolean']['output'];
  isAuthPhoneEnabled: Scalars['Boolean']['output'];
  isAutogeneratedPassword: Scalars['Boolean']['output'];
  isPhoneVerified: Scalars['Boolean']['output'];
  isReadyForNextCsat?: Maybe<Scalars['Boolean']['output']>;
  isSubscribedEsputnik: Scalars['Boolean']['output'];
  language?: Maybe<Scalars['String']['output']>;
  lastName?: Maybe<Scalars['String']['output']>;
  onboarding: UserOnboarding;
  phone?: Maybe<Scalars['String']['output']>;
  registrationCompleted: Scalars['Boolean']['output'];
  registrationMethod: Scalars['String']['output'];
  roles: Array<Role>;
  secondName?: Maybe<Scalars['String']['output']>;
  sessions: Array<LoginSession>;
  showKEPAppPopup: Scalars['Boolean']['output'];
  source: Scalars['String']['output'];
  trialAutoEnable: Scalars['Boolean']['output'];
  userMeta?: Maybe<UserMeta>;
};

export type UserMeta = {
  __typename?: 'UserMeta';
  hasActiveMobileApp: Scalars['Boolean']['output'];
  hasMobileApp: Scalars['Boolean']['output'];
  mobileUsage: Scalars['Boolean']['output'];
};

export type UserOnboarding = {
  __typename?: 'UserOnboarding';
  extra: Scalars['String']['output'];
  hasCheckedCompanies: Scalars['Boolean']['output'];
  hasInvitedCoworker: Scalars['Boolean']['output'];
  hasInvitedRecipient: Scalars['Boolean']['output'];
  hasSeenNewUploading: Scalars['Boolean']['output'];
  hasUploadedDocument: Scalars['Boolean']['output'];
  isSkipped: Scalars['Boolean']['output'];
};

export type VersionCompany = {
  __typename?: 'VersionCompany';
  edrpou: Scalars['String']['output'];
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type VersionRole = {
  __typename?: 'VersionRole';
  company: VersionCompany;
  companyId: Scalars['String']['output'];
  id: Scalars['String']['output'];
  position?: Maybe<Scalars['String']['output']>;
  user: VersionUser;
  userId: Scalars['String']['output'];
};

export type VersionUser = {
  __typename?: 'VersionUser';
  authPhone?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstName?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  lastName?: Maybe<Scalars['String']['output']>;
  secondName?: Maybe<Scalars['String']['output']>;
};

export type CurrentUserOnboardingExtraQueryVariables = Exact<{ [key: string]: never; }>;


export type CurrentUserOnboardingExtraQuery = { __typename?: 'Query', currentUser?: { __typename?: 'User', onboarding: { __typename?: 'UserOnboarding', extra: string } } | null };



export const CurrentUserOnboardingExtraDocument = `
    query CurrentUserOnboardingExtra {
  currentUser {
    onboarding {
      extra
    }
  }
}
    `;

export const useCurrentUserOnboardingExtraQuery = <
      TData = CurrentUserOnboardingExtraQuery,
      TError = unknown
    >(
      client: GraphQLClient,
      variables?: CurrentUserOnboardingExtraQueryVariables,
      options?: UseQueryOptions<CurrentUserOnboardingExtraQuery, TError, TData>,
      headers?: RequestInit['headers']
    ) => {
    
    return useQuery<CurrentUserOnboardingExtraQuery, TError, TData>(
      variables === undefined ? ['CurrentUserOnboardingExtra'] : ['CurrentUserOnboardingExtra', variables],
      fetcher<CurrentUserOnboardingExtraQuery, CurrentUserOnboardingExtraQueryVariables>(client, CurrentUserOnboardingExtraDocument, variables, headers),
      options
    )};
