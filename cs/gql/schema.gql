type AntivirusCheck {
  id: String!
  provider: String!
  status: String!
  dateCreated: String!
  dateUpdated: String!
  documentVersionId: String!
}

scalar Any

type ArchiveList {
  count: Int!
  document_ids: [String!]!
  directory_ids: [String!]!
  documents: [Document!]!
  directories: [DocumentDirectory!]!
}

type AutomationConditionCompany {
  id: String
  name: String
  edrpou: String!
}

type Banner {
  id: String!
  dateFrom: String!
  dateTo: String!
  color: String!
  status: String!
  positions: [String!]
  rates: [String!]
  outgoingDocumentsCount: [String!]
  incomingDocumentsSignCount: [String!]
  activityPeriod: String
  employeesCount: [String!]
  audienceType: String
  daysBeforeSignatureExpires: Int
  analyticsCategory: String!
  text: String!
  textEn: String!
  linkText: String!
  linkTextEn: String!
  link: String!
  linkEn: String!
}

type Bill {
  id: String!
  seqnum: String!
  email: String!
  source: String!
  documentId: String!
  companyId: String!
  statusId: String!
  dateCreated: String!
  paymentStatus: String!
  number: String
  amount: Int
  servicesType: String!
  services: [BillService!]!
  rate: String!
  count_documents: String
  max_employees_count: Int
  document_id: String!
  company_id: String!
  status_id: String!
  date_created: String!
  payment_status: String!
}

type BillingAccount {
  id: String!
  companyId: String!
  initiatorId: String!
  type: String!
  rate: String!
  status: String!
  amount: Int!
  source: String!
  amountLeft: Int!
  units: Int!
  unitsLeft: Int!
  pricePerUser: Int!
  dateCreated: String!
  dateExpired: String!
  dateDeleted: String!
  billingTransactionsFrom: [BillingTransaction!]!
  billingTransactionsTo: [BillingTransaction!]!
}

type BillingCompanyConfig {
  maxArchiveDocumentsCount: Int
  maxAdditionalFieldsCount: Int
  maxEmployeesCount: Int
  maxDocumentsCount: Int
  maxTagsCount: Int
  maxTemplatesCount: Int
  maxRequiredFieldsCount: Int
  maxVersionsCount: Int
  apiEnabled: Boolean!
  canEnforce2FA: Boolean!
  externalCommentsEnabled: Boolean!
  internalCommentsEnabled: Boolean!
  internalDocumentsEnabled: Boolean!
  canManageEmployeeAccess: Boolean!
  reviewsEnabled: Boolean!
  maxVisibleDocumentsCount: Int
}

type BillingTransaction {
  id: String!
  from: String!
  to: String!
  operatorId: String!
  initiatorId: String!
  type: String!
  amount: Int!
  units: Int!
  comment: String!
  dateCreated: String!
}

type BillService {
  type: String!
  units: Int!
  unitPrice: Int!
  rate: String
  dateFrom: String
  extension: String
  limitsEmployeesCount: Int
}

type Bonus {
  id: String!
  createdBy: String!
  key: String!
  title: String!
  description: String!
  type: String!
  units: Int!
  period: Int!
  dateCreated: String!
  dateExpired: String!
  dateDeleted: String!
}

type CloudSigner {
  operationId: String!
  documentId: String!
}

type Comment {
  id: String!
  documentId: String!
  documentVersionId: String!
  accessCompanyId: String!
  roleId: String!
  type: String!
  text: String!
  dateCreated: String!
  dateEdited: String!
  role: CommentRole!
  isRejection: Boolean!
  documentVersion: DocumentVersion!
  userId: String!
  statusId: Int!
}

type CommentCompany {
  id: String!
  edrpou: String!
  isLegal: Boolean!
}

type CommentRole {
  id: String!
  userId: String!
  companyId: String!
  user: CommentUser!
  company: CommentCompany!
}

type CommentUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type Company {
  id: String!
  edrpou: String!
  ipn: String!
  name: String
  fullName: String
  isLegal: Boolean!
  renderSignatureInInterface: Boolean!
  renderSignatureOnPrintDocument: Boolean!
  allowUnregisteredDocumentView: Boolean!
  activityField: String
  employeesNumber: String
  phone: String
  dateCreated: String!
  dateUpdated: String!
  config: Any
  activeRates: [String!]!
  usedDocumentCount: Int!
  hasInvalidSignedDocuments: Boolean!
  isFop: Boolean!
  emailDomains: [String!]!
  allowedIps: [String!]!
  allowedApiIps: [String!]!
  inactivityTimeout: Int
  billingAccounts: [BillingAccount!]!
  bills: [Bill!]!
  rates: [CompanyRate!]!
  rateExtensions: [RateExtension!]!
  trialRates: [CompanyRate!]!
  roles(tags: [String!] = null, hasInvalidSignatures: Boolean = null): [CoworkerRole!]!
  billingCompanyConfig: BillingCompanyConfig!
}

type CompanyDocumentOwner {
  id: String
  name: String
  edrpou: String!
}

type CompanyDocumentRecipient {
  id: String
  name: String
  edrpou: String!
}

type CompanyRate {
  id: String!
  rate: String!
  status: String!
  startDate: String!
  endDate: String!
  amount: Int!
  source: String!
  config: Any
  billNumber: String
  units: Int!
  unitsLeft: Int!
  bill_number: String
  units_left: Int!
}

type Contact {
  id: String!
  companyId: String!
  name: String
  shortName: String
  edrpou: String!
  isRegistered: Boolean!
  dateCreated: String!
  persons: [ContactPerson!]!
  tags: [Tag!]!
}

type ContactPerson {
  id: String!
  contactId: String!
  email: String
  isEmailHidden: Boolean!
  mainRecipient: Boolean!
  firstName: String
  secondName: String
  lastName: String
  contact: Contact
  phones: [ContactPersonPhone!]!
}

type ContactPersonPhone {
  phone: String!
}

type ContactPersonsList {
  count: Int!
  contact_person_ids: [String!]!
  contact_persons: [ContactPerson!]!
}

type ContactRecipient {
  edrpou: String!
  name: String
  email: String
  userName: String
  isMainRecipient: Boolean!
}

type ContactsList {
  count: Int!
  contact_ids: [String!]!
  contacts: [Contact!]!
}

type CoworkerRole {
  id: String!
  companyId: String!
  companyEdrpou: String!
  userId: String!
  userRole: Int!
  canViewDocument: Boolean!
  canCommentDocument: Boolean!
  canUploadDocument: Boolean!
  canDownloadDocument: Boolean!
  canPrintDocument: Boolean!
  canDeleteDocument: Boolean!
  canArchiveDocuments: Boolean!
  canEditTemplates: Boolean!
  canEditDirectories: Boolean!
  canRemoveItselfFromApproval: Boolean!
  canDeleteArchivedDocuments: Boolean!
  canSignAndRejectDocument: Boolean!
  canSignAndRejectDocumentExternal: Boolean!
  canSignAndRejectDocumentInternal: Boolean!
  canInviteCoworkers: Boolean!
  canChangeDocumentSignersAndReviewers: Boolean!
  canDeleteDocumentExtended: Boolean!
  canDownloadActions: Boolean!
  canEditCompanyContact: Boolean!
  canEditRequiredFields: Boolean!
  canEditSecurity: Boolean!
  canViewPrivateDocument: Boolean!
  hasSignedDocuments: Boolean!
  canEditCompany: Boolean!
  canEditRoles: Boolean!
  canCreateTags: Boolean!
  canEditDocumentTemplates: Boolean!
  canEditDocumentFields: Boolean!
  canEditDocumentCategory: Boolean!
  canExtractDocumentStructuredData: Boolean!
  canEditDocumentStructuredData: Boolean!
  canReceiveInbox: Boolean!
  canReceiveInboxAsDefault: Boolean!
  canReceiveComments: Boolean!
  canReceiveRejects: Boolean!
  canReceiveReminders: Boolean!
  canReceiveReviews: Boolean!
  canReceiveReviewProcessFinished: Boolean!
  canReceiveReviewProcessFinishedAssigner: Boolean!
  canReceiveSignProcessFinished: Boolean!
  canReceiveSignProcessFinishedAssigner: Boolean!
  canReceiveNotifications: Boolean!
  canReceiveAccessToDoc: Boolean!
  canReceiveDeleteRequests: Boolean!
  canReceiveFinishedDocs: Boolean!
  canReceiveNewRoles: Boolean!
  canReceiveTokenExpiration: Boolean!
  canReceiveEmailChange: Boolean!
  canViewCoworkers: Boolean!
  canReceiveAdminRoleDeletion: Boolean!
  sortDocuments: String!
  showInviteTooltip: Boolean!
  showChildDocuments: Boolean!
  position: String
  status: String!
  allowedIps: [String!]!
  allowedApiIps: [String!]
  dateCreated: String!
  dateUpdated: String!
  dateDeleted: String
  deletedBy: String
  invitedBy: String
  activatedBy: String
  activationSource: String
  dateAgreed: String!
  dateInvited: String
  dateActivated: String
  isDefaultRecipient: Boolean!
  isAdmin: Boolean!
  isMasterAdmin: Boolean!
  hasFewSignatures: Boolean!
  hasFewReviews: Boolean!
  isSuperAdmin: Boolean!
  canViewClientData: Boolean!
  canEditClientData: Boolean!
  canEditSpecialFeatures: Boolean!
  registrationReferralUrl: String!
  isFreeRateBannerShown: Boolean!
  isFreeTrialEndBannerShown: Boolean!
  hasToken: Boolean!
  tags: [Tag!]!
  fields: [DocumentsField!]!
  user: CoworkerUser!
}

type CoworkerUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
  phone: String
  isRegistered: Boolean!
  sessions: [LoginSession!]!
}

type CSATSurvey {
  id: String!
  type: String!
  estimate: Int
  dateCreated: String!
}

type DeleteRequest {
  id: String!
  documentId: String!
  initiatorRoleId: String!
  recipientsEmails: String!
  receiverEdrpou: String!
  initiatorEdrpou: String!
  status: String!
  message: String!
  rejectMessage: String!
  currentRoleEmail: String!
  isReceiver: Boolean!
}

type DirectoriesList {
  count: Int!
  directory_ids: [Int!]!
  directories: [DocumentDirectory!]!
}

type Document {
  id: String!
  seqnum: Int!
  userId: String!
  uploadedBy: String!
  edrpouOwner: String!
  edrpouRecipient: String
  emailRecipient: String
  isRecipientEmailHidden: Boolean
  title: String!
  extension: String!
  archiveName: String
  statusId: Int!
  dateDocument: String
  dateFinished: String
  amount: Int
  type: String
  category: Int
  number: String
  source: String!
  firstSignBy: String!
  isInternal: Boolean!
  isMultilateral: Boolean!
  expectedOwnerSignatures: Int!
  expectedRecipientSignatures: Int!
  isProtected: Boolean!
  s3XmlToPdfKey: String
  dateCreated: String!
  dateUpdated: String!
  dateDelivered: String
  dateListing: String!
  displayCompanyEdrpou: String!
  displayCompanyEmail: String!
  displayCompanyName: String!
  displayStatusText: String!
  isImported: Boolean!
  isInput: Boolean!
  isOneSign: Boolean!
  expectedSignatureFormat: String!
  reviewStatus: String
  isViewable: Boolean!
  accessLevel: String!
  hasEUSignatures: Boolean!
  isInvalidSigned: Boolean
  isDeleteLocked: Boolean!
  isArchived: Boolean!
  canDelete: Boolean!
  canSign: Boolean!
  comments: [Comment!]!
  accesses(source: Int!): [DocumentAccess!]!
  signatures: [Signature!]!
  reviews(add_is_last_condition: Boolean = false): [Review!]!
  reviewRequests(is_all_requests: Boolean = false): [ReviewRequest!]!
  signers: [DocumentSigner!]!
  children: [DocumentLink!]!
  flows: [DocumentFlow!]!
  antivirusChecks: [AntivirusCheck!]!
  viewerGroups: [GroupDocumentAccess!]!
  parent: DocumentLink
  reviewSetting: ReviewSetting
  companyOwner: CompanyDocumentOwner
  companyRecipient: CompanyDocumentRecipient
  contactRecipient: Contact
  contactPersonRecipient: ContactPerson
  deleteRequest: DeleteRequest
  recipients: [DocumentRecipient!]!
  user: DocumentUser!
  tags: [Tag!]!
  parameters: [DocumentParameter!]!
  versions: [DocumentVersion!]!
  metadata: DocumentMeta
  drafts: [Draft!]!
  categoryDetails: DocumentCategory!
  directory: DocumentDirectory
  revoke: DocumentRevoke
}

type DocumentAccess {
  id: String!
  roleId: String!
  dateCreated: String!
  role: DocumentAccessRole!
}

type DocumentAccessRole {
  id: String!
  userId: String!
  position: String
  user: DocumentAccessUser!
}

type DocumentAccessUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type DocumentAutomationCondition {
  id: String!
  conditions: Any
  status: String!
  involvedCompanies: [AutomationConditionCompany!]!
}

type DocumentAutomationTemplate {
  id: String!
  name: String!
  isActive: Boolean!
  reviewSettings: Any
  signersSettings: Any
  viewersSettings: Any
  fieldsSettings: Any
  tagsSettings: Any
  assignedToId: String
  assignedTo: CoworkerRole
  tags: [Tag!]!
  reviewers: [DocumentAutomationTemplateEntity!]!
  signers: [DocumentAutomationTemplateEntity!]!
  viewerRoles: [DocumentAutomationTemplateRole!]!
  viewerGroups: [Group!]!
  automation: [DocumentAutomationCondition!]!
}

type DocumentAutomationTemplateEntity {
  id: String!
  type: String!
  group: Group
  role: DocumentAutomationTemplateRole
}

type DocumentAutomationTemplateRole {
  canSignAndRejectDocument: Boolean!
  canSignAndRejectDocumentExternal: Boolean!
  canSignAndRejectDocumentInternal: Boolean!
  id: String!
  userId: String!
  position: String
  user: DocumentAutomationTemplateUser!
}

type DocumentAutomationTemplateUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type DocumentAvailableCompany {
  id: String
  name: String
  edrpou: String!
}

type DocumentAvailableRole {
  id: String!
  companyId: String!
  company: DocumentAvailableCompany!
}

type DocumentCategoriesList {
  count: Int!
  document_categories_ids: [String!]!
  documentCategories: [DocumentCategory!]!
}

type DocumentCategory {
  id: String!
  companyId: String
  title: String!
  dateCreated: String!
  dateUpdated: String!
  dateDeleted: String
}

type DocumentDirectory {
  id: Int!
  name: String!
  parentId: Int
  dateCreated: String!
  dateUpdated: String!

  """
  Do not use that field while getting list of directories. Try to use it only for node "directory" from graph_root
  """
  path: [SimpleDirectory!]!
}

type DocumentFlow {
  id: String!
  companyId: String!
  edrpou: String!
  displayCompanyName: String
  signaturesCount: Int!
  pendingSignaturesCount: Int!
  meta: Any
  order: Int!
  dateSent: String!
  isComplete: Boolean!
  recipientId: String!
  recipient: DocumentRecipient!
  canSign: Boolean!
  receiversId: String!
  receivers: Any
}

type DocumentLink {
  creatorEdrpou: String!
  documentId: String!
  document: Document!
}

type DocumentMeta {
  contentHash: String!
  contentLength: Int!
}

type DocumentParameter {
  id: String!
  fieldId: String!
  value: String!
  isRequired: Boolean!
}

type DocumentRecipient {
  id: String!
  edrpou: String!
  document_id: String!
  emails: [String!]
  isEmailsHidden: Boolean!
  dateSent: String
  dateReceived: String
  dateDelivered: String
}

type DocumentRequiredField {
  id: String!
  documentCategory: String!
  isNameRequired: Boolean!
  isTypeRequired: Boolean!
  isNumberRequired: Boolean!
  isDateRequired: Boolean!
  isAmountRequired: Boolean!
  companyId: String!
  company: DocumentRequiredFieldCompany!
}

type DocumentRequiredFieldCompany {
  id: String
  name: String
  edrpou: String!
}

type DocumentRevoke {
  id: String!
  initiatorRoleId: String!
  initiatorCompanyId: String!
  reason: String!
  status: String!
  documentId: String!
  signatureFormat: String!
  signatures: [DocumentRevokeSignature!]!
  initiatorCompany: DocumentRevokeCompany!
  initiatorRole: RevokeUser!
}

type DocumentRevokeCompany {
  id: String
  name: String
  edrpou: String!
}

type DocumentRevokeSignature {
  id: String!
  revokeId: String!
  roleId: String!
  userEmail: String!
  isInternal: Boolean!
  keyAcsk: String!
  keySerialNumber: String!
  keyTimeMark: String!
  keyCompanyFullName: String
  keyOwnerEdrpou: String!
  keyOwnerFullName: String!
  keyOwnerPosition: String
  stampAcsk: String
  stampSerialNumber: String
  stampTimeMark: String
  stampCompanyFullName: String
  stampOwnerEdrpou: String
  stampOwnerFullName: String
  stampOwnerPosition: String
  dateCreated: String!
  user: SignatureUser!
}

type DocumentsField {
  id: String!
  name: String!
  type: String!
  isRequired: Boolean!
  order: Int
  canEdit: Boolean!
  enumOptions: [String!]!
  roles: [CoworkerRole!]!
}

type DocumentSigner {
  id: String!
  documentId: String!
  companyId: String!
  roleId: String
  groupId: String
  groupSignerId: String
  order: Int
  dateCreated: String!
  dateSigned: String
  assignerId: String
  role: SignerRole!
  group: Group!
  groupSignedBy: SignerRole!
}

type DocumentsList {
  count: Int!
  document_ids: [String!]!
  documents: [Document!]!
}

type DocumentUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type DocumentVersion {
  id: String!
  name: String!
  type: String!
  roleId: String!
  isSent: Boolean!
  extension: String!
  contentHash: String!
  contentLength: Int!
  dateCreated: String!
  antivirusChecks: [AntivirusCheck!]!
  role: VersionRole!
  reviewStatus: String
  number: String!
}

type Draft {
  id: String!
  type: String!
  dateCreated: String!
  dateUpdated: String
  companyId: String!
  creatorRoleId: String!
  documentId: String
  documentVersionId: String
  templateId: String
  dateScheduledDeletion: String
  creatorRole: DraftRole!
  antivirusCheck: DraftAntivirusCheck
  template: Template
}

type DraftAntivirusCheck {
  draftId: String!
  provider: String!
  status: String!
  dateCreated: String!
  dateUpdated: String!
}

type DraftCompany {
  id: String
  name: String
  edrpou: String!
}

type DraftRole {
  companyId: String!
  company: DraftCompany!
  id: String!
  userId: String!
  position: String
  user: DraftUser!
}

type DraftsList {
  count: Int!
  draft_ids: [String!]!
  drafts: [Draft!]!
}

type DraftUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type Group {
  id: String!
  name: String!
  dateCreated: String!
  createdBy: String!
  members: [GroupMember!]!
  documentSigners: [Document!]!
  documentReviewers: [Document!]!
  documentAutomationTemplates: [DocumentAutomationTemplate!]!
}

type GroupDocumentAccess {
  id: String!
  groupId: String!
  documentId: String!
  createdBy: String!
  dateCreated: String!
  group: Group!
  createdByRole: CoworkerRole!
}

type GroupMember {
  id: String!
  groupId: String!
  roleId: String!
  dateCreated: String!
  createdBy: String!
  createdByRole: CoworkerRole!
  role: CoworkerRole!
}

type GroupsList {
  count: Int!
  group_ids: [String!]!
  groups: [Group!]!
}

type LoginSession {
  id: String!
  accessedAt: String!
  ip: String!
  browser: String!
  browserVersion: String
  os: String!
  osVersion: String
  device: String!
  country: String
  city: String
  isCurrent: Boolean!
}

type Query {
  """All contacts added to current company."""
  allContacts(search: String = null, limit: Int = null, offset: Int = null, isRegistered: Boolean = null): ContactsList!

  """All persons added to contacts of current company."""
  allContactPersons(search: String = null, limit: Int = 25, offset: Int = 0): ContactPersonsList!

  """All documents accessible by current user."""
  allDocuments(ids: [String!] = null, search: String = null, searchTitle: [String!] = null, searchNumber: [String!] = null, searchCompanyName: [String!] = null, searchCompanyEdrpou: [String!] = null, searchUserEmail: [String!] = null, searchParameter: [String!] = null, searchTag: [String!] = null, firstSignBy: String = null, folderId: Int = null, folderIds: [Int!] = null, reviewFolder: String = null, statusId: Int = null, statusIds: [Int!] = null, isOneSign: Boolean = null, hasDateDelivered: Boolean = null, sortDate: String = "date_listing", gte: String = null, lte: String = null, amountEq: Int = null, amountGte: Int = null, amountLte: Int = null, condition: String = null, conditions: [String!] = null, condition2: String = null, conditions2: [String!] = null, tag: String = null, tags: [String!] = null, withoutTags: Boolean = null, categories: [Int!] = null, hasComments: Boolean = null, isWaitMySign: Boolean = null, isMyInvalidSigned: Boolean = null, isPartnerInvalidSigned: Boolean = null, invalidSignedRoles: [String!] = null, isArchived: Boolean = false, parentDirectoryId: Int = null, accessLevel: String = null, order: String = "date", orderField: String = null, direction: String = "desc", limit: Int = 20, offset: Int = 0, seqnumOffset: Int = null): DocumentsList!

  """Count documents accessible by current user."""
  countDocuments(ids: [String!] = null, search: String = null, searchTitle: [String!] = null, searchNumber: [String!] = null, searchCompanyName: [String!] = null, searchCompanyEdrpou: [String!] = null, searchUserEmail: [String!] = null, searchParameter: [String!] = null, searchTag: [String!] = null, firstSignBy: String = null, folderId: Int = null, folderIds: [Int!] = null, reviewFolder: String = null, statusId: Int = null, statusIds: [Int!] = null, isOneSign: Boolean = null, hasDateDelivered: Boolean = null, sortDate: String = "date_listing", gte: String = null, lte: String = null, amountEq: Int = null, amountGte: Int = null, amountLte: Int = null, condition: String = null, conditions: [String!] = null, condition2: String = null, conditions2: [String!] = null, tag: String = null, tags: [String!] = null, withoutTags: Boolean = null, categories: [Int!] = null, hasComments: Boolean = null, isWaitMySign: Boolean = null, isMyInvalidSigned: Boolean = null, isPartnerInvalidSigned: Boolean = null, invalidSignedRoles: [String!] = null, isArchived: Boolean = false, parentDirectoryId: Int = null, accessLevel: String = null): Int!

  """All tags for current user."""
  allTags(search: String = null, limit: Int = null, offset: Int = 0, hasRoles: Boolean = null): [Tag!]!

  """All tags visible in document filter"""
  allTagsForDocumentFilter: [Tag!]!

  """Retrieve company info accessible by current user by its ID."""
  company(id: String!): Company

  """Current logged in role. Role connected company & user."""
  currentRole: Role

  """All roles of current company."""
  currentCompanyRoles(id: String = null, search: String = null, canSignAndRejectDocument: Boolean = null, canSignAndRejectDocumentExternal: Boolean = null, canSignAndRejectDocumentInternal: Boolean = null): [CoworkerRole!]!

  """Current sign session if any."""
  currentSignSession: SignSession

  """Current logged in user."""
  currentUser: User

  """Fetch document accessible by current user by its ID."""
  document(id: String!, withError: Boolean = false): Document

  """Get all roles from which document is available for given user"""
  documentAvailableRoles(documentId: String!): [DocumentAvailableRole!]!

  """Return number of unregistered companies in contacts."""
  countUnregisteredContacts: Int!

  """Show notification for current role"""
  triggerNotifications(status: String = null, limit: Int = 5, offset: Int = 0): [TriggerNotification!]!

  """Select all document templates for given company"""
  documentAutomationTemplates(is_active: Boolean = null, limit: Int = 1000, offset: Int = 0): [DocumentAutomationTemplate!]!

  """Select active banner"""
  activeBanner: Banner

  """Select all documents fields for given company"""
  documentsFields(limit: Int = null, offset: Int = null): [DocumentsField!]!

  """Select all documents categories available for company"""
  allDocumentCategories(ids: [Int!] = null, onlyPublic: Boolean = null, onlyInternal: Boolean = null, title: String = null, limit: Int = null, offset: Int = null): DocumentCategoriesList!

  """
  Get all required fields for company. For current company if no companies_ids nor edrpous are passed
  """
  allDocumentRequiredFields(companies_ids: [String!] = null, edrpous: [String!] = null): [DocumentRequiredField!]!

  """Get list of operation ids and its document ids for current user"""
  allCloudSigners: [CloudSigner!]!

  """All available companies. Only for super admin."""
  saAllCompanies(search: String = null, limit: Int = null, offset: Int = null): [Company!]!

  """All available users. Only for super admin."""
  saAllUsers(search: String = null, gte: String = null, lte: String = null, limit: Int = null, offset: Int = null): [User!]!

  """Retrieve company info by its ID. Only for super admin."""
  saCompany(id: String!): Company

  """Retrieve user info by its ID. Only for super admin."""
  saUser(id: String!): User
  saBanner(limit: Int = null, offset: Int = null): [Banner!]!
  allGroups(limit: Int! = 200, offset: Int! = 0, name: String = null): GroupsList!
  group(id: String!): Group
  allContactRecipients(search: String = null, limit: Int = 100, offset: Int = 0): [ContactRecipient!]!
  allDrafts(types: [String!] = null, limit: Int = 100, offset: Int = 0): DraftsList!
  allTemplates(limit: Int = 20, offset: Int = 0, categories: [Int!] = null, search: String = null, is_public: Boolean = null, is_favorite: Boolean = null): TemplatesList!
  directory(id: Int!): DocumentDirectory
  allDirectories(limit: Int = 20, offset: Int = 0, parentId: Int = null, search: String = null): DirectoriesList!

  """All archive documents and directories accessible by current user."""
  allArchiveItems(ids: [String!] = null, search: String = null, searchTitle: [String!] = null, searchNumber: [String!] = null, searchCompanyName: [String!] = null, searchCompanyEdrpou: [String!] = null, searchUserEmail: [String!] = null, searchParameter: [String!] = null, searchTag: [String!] = null, firstSignBy: String = null, folderId: Int = null, folderIds: [Int!] = null, reviewFolder: String = null, statusId: Int = null, statusIds: [Int!] = null, isOneSign: Boolean = null, hasDateDelivered: Boolean = null, sortDate: String = "date_listing", gte: String = null, lte: String = null, amountEq: Int = null, amountGte: Int = null, amountLte: Int = null, condition: String = null, conditions: [String!] = null, condition2: String = null, conditions2: [String!] = null, tag: String = null, tags: [String!] = null, withoutTags: Boolean = null, categories: [Int!] = null, hasComments: Boolean = null, isWaitMySign: Boolean = null, isMyInvalidSigned: Boolean = null, isPartnerInvalidSigned: Boolean = null, invalidSignedRoles: [String!] = null, isArchived: Boolean = false, parentDirectoryId: Int = null, accessLevel: String = null, order: String = "date", orderField: String = null, direction: String = "desc", limit: Int = 20, offset: Int = 0, seqnumOffset: Int = null): ArchiveList!
}

type RateExtension {
  id: String!
  type: String!
  status: String!
  bill_id: String!
  date_expiring: String
  bill_document_id: String!
}

type Review {
  id: String!
  documentId: String!
  roleId: String!
  groupId: String!
  documentVersionId: String
  type: String!
  userEmail: String!
  dateCreated: String!
  role: ReviewRole!
  group: Group!
  documentVersion: DocumentVersion
}

type ReviewRequest {
  id: String!
  documentId: String!
  documentVersionId: String!
  fromRoleId: String!
  toRoleId: String!
  toGroupId: String!
  status: String!
  order: Int
  dateCreated: String!
  dateUpdated: String!
  toGroup: Group!
  fromRole: ReviewRequestRole!
  toRole: ReviewRequestRole!
  documentVersion: DocumentVersion
}

type ReviewRequestRole {
  id: String!
  userId: String!
  position: String
  user: ReviewRequestUser!
}

type ReviewRequestUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type ReviewRole {
  id: String!
  userId: String!
  position: String
  user: ReviewUser!
}

type ReviewSetting {
  id: String!
  documentId: String!
  companyId: String!
  isRequired: Boolean!
  isParallel: Boolean!
  dateCreated: String!
  dateUpdated: String!
}

type ReviewUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type RevokeUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type Role {
  id: String!
  companyId: String!
  companyEdrpou: String!
  userId: String!
  userRole: Int!
  canViewDocument: Boolean!
  canCommentDocument: Boolean!
  canUploadDocument: Boolean!
  canDownloadDocument: Boolean!
  canPrintDocument: Boolean!
  canDeleteDocument: Boolean!
  canArchiveDocuments: Boolean!
  canEditTemplates: Boolean!
  canEditDirectories: Boolean!
  canRemoveItselfFromApproval: Boolean!
  canDeleteArchivedDocuments: Boolean!
  canSignAndRejectDocument: Boolean!
  canSignAndRejectDocumentExternal: Boolean!
  canSignAndRejectDocumentInternal: Boolean!
  canInviteCoworkers: Boolean!
  canChangeDocumentSignersAndReviewers: Boolean!
  canDeleteDocumentExtended: Boolean!
  canDownloadActions: Boolean!
  canEditCompanyContact: Boolean!
  canEditRequiredFields: Boolean!
  canEditSecurity: Boolean!
  canViewPrivateDocument: Boolean!
  hasSignedDocuments: Boolean!
  canEditCompany: Boolean!
  canEditRoles: Boolean!
  canCreateTags: Boolean!
  canEditDocumentTemplates: Boolean!
  canEditDocumentFields: Boolean!
  canEditDocumentCategory: Boolean!
  canExtractDocumentStructuredData: Boolean!
  canEditDocumentStructuredData: Boolean!
  canReceiveInbox: Boolean!
  canReceiveInboxAsDefault: Boolean!
  canReceiveComments: Boolean!
  canReceiveRejects: Boolean!
  canReceiveReminders: Boolean!
  canReceiveReviews: Boolean!
  canReceiveReviewProcessFinished: Boolean!
  canReceiveReviewProcessFinishedAssigner: Boolean!
  canReceiveSignProcessFinished: Boolean!
  canReceiveSignProcessFinishedAssigner: Boolean!
  canReceiveNotifications: Boolean!
  canReceiveAccessToDoc: Boolean!
  canReceiveDeleteRequests: Boolean!
  canReceiveFinishedDocs: Boolean!
  canReceiveNewRoles: Boolean!
  canReceiveTokenExpiration: Boolean!
  canReceiveEmailChange: Boolean!
  canViewCoworkers: Boolean!
  canReceiveAdminRoleDeletion: Boolean!
  sortDocuments: String!
  showInviteTooltip: Boolean!
  showChildDocuments: Boolean!
  position: String
  status: String!
  allowedIps: [String!]!
  allowedApiIps: [String!]
  dateCreated: String!
  dateUpdated: String!
  dateDeleted: String
  deletedBy: String
  invitedBy: String
  activatedBy: String
  activationSource: String
  dateAgreed: String!
  dateInvited: String
  dateActivated: String
  isDefaultRecipient: Boolean!
  isAdmin: Boolean!
  isMasterAdmin: Boolean!
  hasFewSignatures: Boolean!
  hasFewReviews: Boolean!
  isSuperAdmin: Boolean!
  canViewClientData: Boolean!
  canEditClientData: Boolean!
  canEditSpecialFeatures: Boolean!
  registrationReferralUrl: String!
  isFreeRateBannerShown: Boolean!
  isFreeTrialEndBannerShown: Boolean!
  company: Company!
  user: User!
}

type Signature {
  id: String!
  documentId: String!
  userId: String!
  roleId: String!
  userEmail: String!
  isInternal: Boolean!
  keyAcsk: String!
  keySerialNumber: String!
  keyTimeMark: String!
  keyCompanyFullName: String
  keyOwnerEdrpou: String!
  keyOwnerFullName: String!
  keyOwnerPosition: String
  keyIsLegal: Boolean!
  stampAcsk: String
  stampSerialNumber: String
  stampTimeMark: String
  stampCompanyFullName: String
  stampOwnerEdrpou: String
  stampOwnerFullName: String
  stampOwnerPosition: String
  stampIsLegal: Boolean
  dateCreated: String!
  isValid: Boolean!
  user: SignatureUser!
}

type SignaturePlaceholderUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type SignatureUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type SignerRole {
  canSignAndRejectDocument: Boolean!
  canSignAndRejectDocumentExternal: Boolean!
  canSignAndRejectDocumentInternal: Boolean!
  id: String!
  userId: String!
  position: String
  user: SignerUser!
}

type SignerUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type SignSession {
  id: String!
  documentId: String!
  roleId: String
  edrpou: String
  email: String
  isLegal: Boolean
  type: String!
  source: String
  status: String!
  documentStatus: String!
  finishUrl: String!
  cancelUrl: String!
  signParameters: Any
  role: SignSessionRole
}

type SignSessionRole {
  status: String!
  id: String!
  userId: String!
  position: String
  user: SignSessionUser!
}

type SignSessionUser {
  id: String!
  phone: String!
  isPhoneVerified: Boolean!
}

type SimpleDirectory {
  id: Int!
  name: String!
}

type Tag {
  id: String!
  name: String!
  dateCreated: String!
  dateUpdated: String!
  companyId: String!
  canAssign: Boolean!
}

type Template {
  id: String!
  title: String!
  extension: String!
  dateCreated: String!
  dateUpdated: String!
  companyId: String
  creatorRoleId: String
  category: String
  creatorRole: TemplateRole!
  isFavorite: Boolean!
  previewImgUrl: String!
}

type TemplateCompany {
  id: String
  name: String
  edrpou: String!
}

type TemplateRole {
  companyId: String!
  company: TemplateCompany!
  id: String!
  userId: String!
  position: String
  user: TemplateUser!
}

type TemplatesList {
  count: Int!
  template_ids: [String!]!
  templates: [Template!]!
}

type TemplateUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

type TriggerNotification {
  id: String!
  title: String!
  description: String!
  url: String
  type: String!
  status: String!
  displayDate: String!
  context: Any
}

type User {
  id: String!
  email: String
  phone: String
  firstName: String
  secondName: String
  lastName: String
  emailConfirmed: Boolean!
  registrationCompleted: Boolean!
  isAutogeneratedPassword: Boolean!
  isPhoneVerified: Boolean!
  is2FAEnabledInProfile: Boolean!
  isAuthPhoneEnabled: Boolean!
  authPhone: String
  trialAutoEnable: Boolean!
  registrationMethod: String!
  source: String!
  createdBy: String!
  is2FAEnabledByRule: Boolean!
  isSubscribedEsputnik: Boolean!
  dateCreated: String!
  dateUpdated: String!
  language: String
  hasPassword: Boolean!
  roles: [Role!]!
  onboarding: UserOnboarding!
  userMeta: UserMeta
  showKEPAppPopup: Boolean!
  sessions: [LoginSession!]!
  activeSurveys: [String!]!
  isReadyForNextCsat: Boolean
  csatSurveys: [CSATSurvey!]!
}

type UserMeta {
  mobileUsage: Boolean!
  hasActiveMobileApp: Boolean!
  hasMobileApp: Boolean!
}

type UserOnboarding {
  hasCheckedCompanies: Boolean!
  hasInvitedRecipient: Boolean!
  hasInvitedCoworker: Boolean!
  hasUploadedDocument: Boolean!
  isSkipped: Boolean!
  hasSeenNewUploading: Boolean!
  extra: String!
}

type VersionCompany {
  id: String
  name: String
  edrpou: String!
}

type VersionRole {
  companyId: String!
  company: VersionCompany!
  id: String!
  userId: String!
  position: String
  user: VersionUser!
}

type VersionUser {
  id: String!
  email: String
  authPhone: String
  firstName: String
  secondName: String
  lastName: String
}

