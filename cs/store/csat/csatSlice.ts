import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { CsatState } from 'store/csat/types';

const initialState: CsatState = {
    // значення відповідає про необхідність відкриття в якийсь момент конретного опитування по фічі
    type: null,
};

export const csatSlice = createSlice({
    name: 'csat',
    initialState: initialState,
    reducers: {
        openCsatModal: (
            state,
            {
                payload,
            }: PayloadAction<{
                type: CsatState['type'];
            }>,
        ) => {
            state.type = payload.type;
        },
        closeCsatModal: (state) => {
            state.type = null;
        },
    },
});

export const { openCsatModal, closeCsatModal } = csatSlice.actions;

export default csatSlice.reducer;
