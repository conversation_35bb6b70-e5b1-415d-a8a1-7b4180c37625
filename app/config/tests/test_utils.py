from pathlib import Path

import pytest

from app.config import (
    get_level,
    read_app_config,
)
from app.config.errors import ConfigError
from app.config.helpers import read_yaml
from app.config.tests.common import DEFAULT_TEST_ENV_VARS
from app.config.utils import _merge_configs, _validate_config_domains, _validate_config_tokens
from app.lib.enums import AppLevel
from app.tokens.utils import read_key

CONFIG_PATH = Path(__file__).parent / 'config'


def monkeypatch_secrets_tokens(monkeypatch):
    def read_key_mock(path: str) -> str:
        # Some secrets are not exists in tests and are mounted by kubernetes.
        # For tests, we pretend that they are exists and have some value.
        if path.startswith('/secrets/'):
            return 'test_key'

        return read_key(path)

    monkeypatch.setattr('app.tokens.utils.read_key', read_key_mock)


@pytest.mark.parametrize(
    'public_filename, secret_filename, envs',
    [
        ('config/vchasno/test/config.yaml', 'config/vchasno/test/secrets.yaml', {}),
        ('config/vchasno/local/config.yaml', None, {}),
        ('config/vchasno/local/docker/config.yaml', None, {}),
        # Dev config with envs
        (
            'config/vchasno/dev/config.yaml',
            None,
            DEFAULT_TEST_ENV_VARS,
        ),
    ],
)
def test_configs(
    public_filename,
    secret_filename,
    envs,
    monkeypatch,
):
    monkeypatch_secrets_tokens(monkeypatch)
    read_app_config(
        public_filename=public_filename,
        secrets_filename=secret_filename,
        variables=envs,
    )


@pytest.mark.parametrize(
    'config_file, envs, error_dict',
    [
        # Check without envs to fixate all variables in config
        (
            'config/vchasno/dev/config.yaml',
            {},
            {
                'FERNET_KEY': 'Environment variable is not set',
                'DB_URL': 'Environment variable is not set',
                'DB_READ_URL': 'Environment variable is not set',
                'EVENT_DB_URL': 'Environment variable is not set',
                'ES_USERNAME': 'Environment variable is not set',
                'ES_PASSWORD': 'Environment variable is not set',
                'REDIS_SENTINEL_USERNAME': 'Environment variable is not set',
                'REDIS_SENTINEL_PASSWORD': 'Environment variable is not set',
                'YOUCONTROL_PASSWORD': 'Environment variable is not set',
                'YOUCONTROL_YOUSCORE_APIKEY': 'Environment variable is not set',
                'SYNC_CONTACTS_API_PROMUA_SECRET_KEY': 'Environment variable is not set',
                'SYNC_CONTACTS_API_ZAKUPKI_SECRET_KEY': 'Environment variable is not set',
                'EDI_RPC_AUTH_TOKEN': 'Environment variable is not set',
                'ESPUTNIK_PASSWORD': 'Environment variable is not set',
                'FIREBASE_PROJECT_ID': 'Environment variable is not set',
                'FIREBASE_CONFIG_PATH': 'Environment variable is not set',
                'TTN_AUTH_TOKEN': 'Environment variable is not set',
                'KASSA_AUTH_TOKEN': 'Environment variable is not set',
                'DIIA_TOKEN': 'Environment variable is not set',
                'DIIA_OFFER_ID': 'Environment variable is not set',
                'DIIA_BRANCH_ID': 'Environment variable is not set',
                'DIIA_AUTH_ACQUIRER_TOKEN': 'Environment variable is not set',
                'SIGNER_WEB_SECRET': 'Environment variable is not set',
                'SIGNER_WEB_KEY_ID': 'Environment variable is not set',
                'EVOPAY_API_KEY': 'Environment variable is not set',
                'EVOPAY_API_SECRET': 'Environment variable is not set',
                'KAFKA_SASL_PASSWORD': 'Environment variable is not set',
                'KAFKA_SASL_USERNAME': 'Environment variable is not set',
                'KAFKA_BOOTSTRAP_SERVERS': 'Environment variable is not set',
                'KAFKA_SECURITY_PROTOCOL': 'Environment variable is not set',
                'PRIVATBANK_INTEGRATION_ID': 'Environment variable is not set',
                'PRIVATBANK_INTEGRATION_TOKEN': 'Environment variable is not set',
                'S3_NEW_ACCESS_KEY': 'Environment variable is not set',
                'S3_NEW_ENCRYPTION_KEY': 'Environment variable is not set',
                'S3_NEW_SECRET_KEY': 'Environment variable is not set',
                'SMTP_PASSWORD': 'Environment variable is not set',
                'SMTP_USER': 'Environment variable is not set',
                'BEDROCK_KEY_ID': 'Environment variable is not set',
                'BEDROCK_KEY_SECRET': 'Environment variable is not set',
                'TEXTRACT_KEY_ID': 'Environment variable is not set',
                'TEXTRACT_KEY_SECRET': 'Environment variable is not set',
                'CONCIERGE_TOKEN': 'Environment variable is not set',
                'FEATURE_FLAGS_URL': 'Environment variable is not set',
            },
        ),
        # partially check with envs
        (
            'config/vchasno/dev/config.yaml',
            {
                'FERNET_KEY': 'Environment variable is not set',
                'DB_URL': 'Environment variable is not set',
                'DB_READ_URL': 'Environment variable is not set',
                'EVENT_DB_URL': 'Environment variable is not set',
                'ES_USERNAME': 'test_es_username',
                'ES_PASSWORD': 'test_es_password',
                'KASSA_AUTH_TOKEN': 'Environment variable is not set',
                'REDIS_SENTINEL_USERNAME': 'test_redis_sentinel_username',
                'REDIS_SENTINEL_PASSWORD': 'test_redis_sentinel_password',
                'YOUCONTROL_PASSWORD': 'Environment variable is not set',
                'YOUCONTROL_YOUSCORE_APIKEY': 'Environment variable is not set',
                'SYNC_CONTACTS_API_PROMUA_SECRET_KEY': 'Environment variable is not set',
                'SYNC_CONTACTS_API_ZAKUPKI_SECRET_KEY': 'Environment variable is not set',
                'EDI_RPC_AUTH_TOKEN': 'Environment variable is not set',
                'BEDROCK_KEY_ID': 'test',
                'BEDROCK_KEY_SECRET': 'test',
                'TEXTRACT_KEY_ID': 'test',
                'TEXTRACT_KEY_SECRET': 'test',
            },
            {
                'ESPUTNIK_PASSWORD': 'Environment variable is not set',
                'DIIA_TOKEN': 'Environment variable is not set',
                'DIIA_OFFER_ID': 'Environment variable is not set',
                'DIIA_BRANCH_ID': 'Environment variable is not set',
                'DIIA_AUTH_ACQUIRER_TOKEN': 'Environment variable is not set',
                'SIGNER_WEB_SECRET': 'Environment variable is not set',
                'SIGNER_WEB_KEY_ID': 'Environment variable is not set',
                'EVOPAY_API_KEY': 'Environment variable is not set',
                'EVOPAY_API_SECRET': 'Environment variable is not set',
                'KAFKA_SASL_PASSWORD': 'Environment variable is not set',
                'KAFKA_SASL_USERNAME': 'Environment variable is not set',
                'FIREBASE_PROJECT_ID': 'Environment variable is not set',
                'FIREBASE_CONFIG_PATH': 'Environment variable is not set',
                'PRIVATBANK_INTEGRATION_ID': 'Environment variable is not set',
                'PRIVATBANK_INTEGRATION_TOKEN': 'Environment variable is not set',
                'KAFKA_BOOTSTRAP_SERVERS': 'Environment variable is not set',
                'KAFKA_SECURITY_PROTOCOL': 'Environment variable is not set',
                'S3_NEW_ACCESS_KEY': 'Environment variable is not set',
                'S3_NEW_ENCRYPTION_KEY': 'Environment variable is not set',
                'S3_NEW_SECRET_KEY': 'Environment variable is not set',
                'SMTP_PASSWORD': 'Environment variable is not set',
                'SMTP_USER': 'Environment variable is not set',
                'CONCIERGE_TOKEN': 'Environment variable is not set',
                'TTN_AUTH_TOKEN': 'Environment variable is not set',
                'FEATURE_FLAGS_URL': 'Environment variable is not set',
            },
        ),
    ],
)
def test_invalid_config(
    config_file,
    envs: dict[str, str],
    error_dict: dict[str, str],
):
    """
    Test that read_and_validate catch cases when some environment variables
    are not expanded
    """
    with pytest.raises(ConfigError) as e:
        read_app_config(
            public_filename=config_file,
            variables=envs,
        )

    assert e.value.errors == error_dict


def test_get_level():
    assert get_level() == AppLevel.test


@pytest.mark.parametrize(
    'original, extra, expected',
    [
        (
            {},
            {'key': 'value'},
            {'key': 'value'},
        ),
        (
            {'key': 'value'},
            {'key': 'new-value'},
            {'key': 'new-value'},
        ),
        (
            {'key': 'value'},
            {'new-key': 'new-value'},
            {'key': 'value', 'new-key': 'new-value'},
        ),
        (
            {'key': {'key': 'value'}},
            {'key': {'key': 'new-value'}},
            {'key': {'key': 'new-value'}},
        ),
        (
            {'key': {'key': 'value'}},
            {'key': {'new-key': 'new-value'}},
            {
                'key': {
                    'key': 'value',
                    'new-key': 'new-value',
                },
            },
        ),
        (
            {
                'key': {
                    'key': 'value',
                    'other-key': 'other-value',
                },
            },
            {
                'key': {
                    'key': 'new-value',
                    'new-key': 'new-value',
                },
            },
            {
                'key': {
                    'key': 'new-value',
                    'new-key': 'new-value',
                    'other-key': 'other-value',
                },
            },
        ),
    ],
)
def test_merge_secrets(original, extra, expected):
    assert _merge_configs(original, extra) == expected


@pytest.mark.parametrize(
    'config_file',
    [
        'config/vchasno/test/config.yaml',
        'config/vchasno/local/config.yaml',
        'config/vchasno/local/docker/config.yaml',
        'config/vchasno/dev/config.yaml',
    ],
)
def test_validate_tokens(config_file, monkeypatch):
    monkeypatch_secrets_tokens(monkeypatch)
    config_dict = read_yaml(filename=config_file)
    _validate_config_tokens(config_dict)


@pytest.mark.parametrize(
    'config_file',
    [
        'config/vchasno/test/config.yaml',
        'config/vchasno/local/config.yaml',
        'config/vchasno/local/docker/config.yaml',
        'config/vchasno/dev/config.yaml',
    ],
)
def test_validate_config_domains(config_file):
    config_dict = read_yaml(filename=config_file)
    _validate_config_domains(config_dict)
