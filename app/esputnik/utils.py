import datetime
import logging
from typing import Any

from aiohttp import BasicAuth, ClientSession

from app.config.schemas import EsputnikConfig
from app.esputnik import db
from app.esputnik.enums import (
    Event,
    FieldId,
    RegistrationGroup,
    SubscriptionSource,
    UserGroup,
)
from app.esputnik.types import (
    AddContactsResponse,
    Contact,
    SubscribeContactResponse,
)
from app.lib.database import DBConnection
from app.lib.enums import enum_values
from app.lib.helpers import namedtuple_to_dict, to_json
from app.lib.types import AnyDict, DataDict
from app.services import services

DATE_FORMAT = '%Y-%m-%d'
logger = logging.getLogger(__name__)


class Client:
    def __init__(self, client: ClientSession, config: EsputnikConfig) -> None:
        self.client = client
        self.auth = BasicAuth(config.login, config.password)
        self.url = config.url
        self.address_book_id = config.address_book_id

    def _get_url(self, path: str) -> str:
        return f'{self.url}{path}/'

    async def _send_request(
        self,
        method: str,
        url: str,
        data: DataDict | None = None,
        raise_for_status: bool = False,
    ) -> DataDict:
        async with self.client.request(
            method=method,
            url=url,
            json=data,
            auth=self.auth,
            headers={'Content-Type': 'application/json'},
        ) as response:
            if raise_for_status:
                response.raise_for_status()

            body = await response.text()
            response_status = response.status
            log_extra = {
                'method': method,
                'url': url,
                'data': data,
                'status': response_status,
                'response': body,
            }

            if response_status == 200:
                logger.info('Request to ESputnik completed', extra=log_extra)
                if body:
                    return await response.json()
                return {}

        logger.error('Request to ESputnik failed', extra=log_extra)
        return {}

    async def get_addressbooks(self) -> DataDict:
        return await self._send_request('GET', self._get_url('addressbooks'))

    async def get_balance(self) -> DataDict:
        return await self._send_request('GET', self._get_url('balance'))

    async def update_contact(self, contact: Contact, fields: AnyDict) -> AddContactsResponse | None:
        """
        Used to update specific contact fields
        :param fields: dict like {esputnik_field_id (int): new_value (str)}
        """
        prepared_fields = self.prepare_fields(fields)
        response = await self._send_request(
            'POST',
            self._get_url('contacts'),
            {
                'contacts': [
                    {
                        'firstName': contact.first_name,
                        'lastName': contact.last_name,
                        'channels': [
                            {'type': 'email', 'value': contact.email},
                            {'type': 'sms', 'value': contact.phone},
                        ],
                        'fields': prepared_fields,
                    }
                ],
                'dedupeOn': 'email',
                'contactFields': ['firstName', 'email', 'sms'],
                'customFieldsIDs': [field['id'] for field in prepared_fields],
                'groupNames': [UserGroup.updated.value],
                'restoreDeleted': False,
            },
        )

        try:
            return AddContactsResponse(**response)
        except TypeError:
            logger.warning(
                'Got invalid response after updating esputnik contact',
                extra={'email': contact.email, **response},
            )
            return None

    async def add_contact(self, contact: Contact) -> AddContactsResponse | None:
        response = await self._send_request(
            'POST',
            self._get_url('contacts'),
            {
                'contacts': [
                    {
                        'firstName': contact.first_name,
                        'lastName': contact.last_name,
                        'channels': [
                            {'type': 'email', 'value': contact.email},
                            {'type': 'sms', 'value': contact.phone},
                        ],
                    }
                ],
                'dedupeOn': 'email',
                'contactFields': ['firstName', 'email', 'sms'],
                'groupNames': [contact.registration_group],
                'restoreDeleted': False,
            },
        )

        try:
            return AddContactsResponse(**response)
        except TypeError:
            logger.warning(
                'Got invalid response after creating esputnik contact',
                extra={'email': contact.email, **response},
            )
            return None

    async def update_contacts(self, contacts: list[Contact]) -> AddContactsResponse | None:
        response = await self._send_request(
            'POST',
            self._get_url('contacts'),
            {
                'contacts': [self.prepare_contact(contact) for contact in contacts],
                'dedupeOn': 'email',
                'contactFields': ['firstName', 'lastName', 'email'],
                'customFieldsIDs': enum_values(FieldId),
                'restoreDeleted': False,
            },
        )
        try:
            return AddContactsResponse(**response)
        except TypeError:
            logger.warning(
                'Got invalid response after updating esputnik contacts',
                extra=response,
            )
            return None

    async def generate_event(
        self, contact: Contact, event: Event, **extra_params: DataDict
    ) -> DataDict:
        if event == Event.registration:
            return await self._generate_registration_event(contact)
        if event == Event.first_employee:
            return await self._generate_first_employee_event(contact)
        if event == Event.company_check:
            return await self._generate_company_check_event(contact)
        if event == Event.first_incoming_sign:
            return await self._generate_first_signing_event(contact)
        if event in {
            Event.company_first_document,
            Event.company_10_documents,
            Event.company_25_documents,
            Event.company_50_documents,
        }:
            return await self._generate_company_signing_event(contact, event)
        if event in {
            Event.rate_expires_30_days,
            Event.rate_expires_14_days,
            Event.rate_expires_5_days,
            Event.rate_added,
            Event.rate_expired,
            Event.company_registered_private_individual,
            Event.company_registered_tov,
            Event.company_registered_fop,
            Event.tov_trial_expires_1_days,
            Event.tov_trial_expires_3_days,
            Event.tov_trial_rate_is_over,
            Event.documents_expires_15p,
            Event.documents_run_out,
            Event.integration_expires_15p,
            Event.integration_run_out,
            Event.employees_over,
            Event.document_view_over_limit_146p,
            Event.document_view_over_limit_130p,
            Event.document_view_limit_full,
            Event.document_view_limit_90p,
            Event.tov_trial_enabled,
            Event.fop_trial_enabled,
            Event.new_position_accountant,
            Event.new_position_director_accountant,
            Event.new_position_lawyer,
            Event.new_position_director_lawyer,
            Event.new_position_hr,
            Event.new_position_director_hr,
            Event.new_position_it,
            Event.new_position_sales,
            Event.new_position_director_sales,
            Event.new_position_middle_manager,
            Event.new_position_top_manager_tov,
            Event.new_position_top_manager_fop,
            Event.tov_employees_limit_trial_expires_1_days,
            Event.tov_employees_limit_trial_expires_3_days,
            Event.tov_employees_limit_trial_rate_is_over,
        }:
            return await self._generate_event_with_extra(
                contact,
                event,
                {
                    'email': contact.email,
                    'first_name': contact.first_name,
                    'second_name': contact.second_name,
                    'last_name': contact.last_name,
                    **extra_params,
                },
            )

        raise ValueError('Invalid ESputnik event')

    async def subscribe(
        self, contact: Contact, source: SubscriptionSource
    ) -> SubscribeContactResponse | None:
        formtype = None
        if source == SubscriptionSource.footer:
            formtype = 'FooterEDO'
        elif source == SubscriptionSource.popup:
            formtype = 'WidgetEDO'

        response = await self._send_request(
            'POST',
            self._get_url('contact/subscribe'),
            {
                'contact': self.prepare_contact(contact),
                'formType': formtype,
                'groups': [formtype],
            },
        )
        try:
            return SubscribeContactResponse(**response)
        except TypeError:
            logger.warning(
                'Got invalid response after esputnik subscription event',
                extra={'email': contact.email, **response},
            )
            return None

    async def __generate_event(
        self, event: Event, event_value: Any, params: list[AnyDict]
    ) -> DataDict:
        return await self._send_request(
            method='POST',
            url=self._get_url('event'),
            data={'eventTypeKey': event.value, 'keyValue': event_value, 'params': params},
            raise_for_status=True,
        )

    async def _generate_company_check_event(self, contact: Contact) -> DataDict:
        return await self.__generate_event(
            event=Event.company_check,
            event_value=contact.email,
            params=[
                {'name': 'email', 'value': contact.email},
                {'name': 'firstname', 'value': contact.first_name},
                {'name': 'lastname', 'value': contact.last_name},
            ],
        )

    async def _generate_first_signing_event(self, contact: Contact) -> DataDict:
        return await self.__generate_event(
            event=Event.first_incoming_sign,
            event_value=contact.email,
            params=[
                {'name': 'email', 'value': contact.email},
                {'name': 'firstname', 'value': contact.first_name},
                {'name': 'lastname', 'value': contact.last_name},
            ],
        )

    async def _generate_company_signing_event(self, contact: Contact, event: Event) -> DataDict:
        return await self.__generate_event(
            event=event,
            event_value=contact.email,
            params=[
                {'name': 'email', 'value': contact.email},
                {'name': 'firstname', 'value': contact.first_name},
                {'name': 'lastname', 'value': contact.last_name},
            ],
        )

    async def _generate_registration_event(self, contact: Contact) -> DataDict:
        if contact.registration_group == RegistrationGroup.edi:
            service = 'EDI'
        elif contact.registration_group == RegistrationGroup.kasa:
            service = 'KASA'
        else:
            service = 'EDO'

        date_registered = contact.date_registered and contact.date_registered.strftime(DATE_FORMAT)
        return await self.__generate_event(
            event=Event.registration,
            event_value=contact.email,
            params=[
                {'name': 'email', 'value': contact.email},
                {'name': 'sms', 'value': contact.phone},
                {
                    'name': 'jsonString',
                    'value': to_json(
                        {
                            'profileInputs': [
                                {
                                    'profileInputId': FieldId.date_registered.value,
                                    'value': date_registered,
                                },
                            ],
                        }
                    ),
                },
                {
                    'name': 'json',
                    'value': to_json(
                        {
                            'firstname': contact.first_name,
                            'email': contact.email,
                            'service': service,
                        }
                    ),
                },
            ],
        )

    async def _generate_first_employee_event(self, contact: Contact) -> DataDict:
        return await self.__generate_event(
            event=Event.first_employee,
            event_value=contact.email,
            params=[
                {'name': 'email', 'value': contact.email},
                {'name': 'firstname', 'value': contact.first_name},
                {'name': 'lastname', 'value': contact.last_name},
            ],
        )

    async def _generate_event_with_extra(
        self, contact: Contact, event: Event, params: DataDict
    ) -> DataDict:
        return await self.__generate_event(
            event=event,
            event_value=contact.email,
            params=[{'name': k, 'value': v} for k, v in params.items()],
        )

    def prepare_contact(self, contact: Contact) -> DataDict:
        data = namedtuple_to_dict(contact)
        data.pop('registration_group', None)

        return {
            'firstName': data.pop('first_name', None),
            'lastName': data.pop('last_name', None),
            'channels': [
                {'type': 'email', 'value': data.pop('email')},
                {'type': 'sms', 'value': data.pop('phone', None)},
            ],
            'fields': self.prepare_fields(data),
            'addressBookId': self.address_book_id,
        }

    def prepare_fields(self, fields: DataDict) -> list[DataDict]:
        res: list[DataDict] = []
        for key, value in fields.items():
            if value is None:
                continue
            try:
                field: FieldId = FieldId(int(key))
            except ValueError:
                continue
            if type(value) in [datetime.date, datetime.datetime]:
                value = value.strftime(DATE_FORMAT)
            res.append({'id': field.value, 'value': value})

        return res


def get_client() -> Client | None:
    config = services.config.esputnik
    if not config:
        return None
    return Client(services.http_client, config)


async def create_user_esputnik_event(
    conn: DBConnection,
    user_id: str,
    event: Event,
) -> None:
    """Create user esputnik event in database"""
    await db.insert_user_esputnik_event(
        conn=conn,
        data={
            'user_id': user_id,
            'event': event,
        },
    )
