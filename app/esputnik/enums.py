from enum import Enum, unique


class RegistrationGroup(Enum):
    edo = 'RegistrationEDO'
    edi = 'RegistrationEDI'
    kasa = 'RegistrationKASA'


class UserGroup(Enum):
    updated = 'Updated'


@unique
class Event(Enum):
    """
    Don't forget to update esputnik.utils.Client.generate_event
    """

    registration = 'Registration'
    first_incoming_sign = 'EDO_First_incoming_document_signed'

    first_employee = 'EDO_First_employee_registration'
    company_check = 'EDO_Сompanies_checked'

    company_first_document = 'EDO_First_document_sent'
    company_10_documents = 'EDO_10_documents_sent'
    company_25_documents = 'EDO_25_documents_sent'
    company_50_documents = 'EDO_50_documents_sent'

    rate_expires_30_days = 'Tariff_expires_30_days'
    rate_expires_14_days = 'Tariff_expires_14_days'
    rate_expires_5_days = 'Tariff_expires_5_days'

    tov_trial_expires_1_days = 'EDO_Welcome_TOV_1_days_of_testing'
    tov_trial_expires_3_days = 'EDO_Welcome_TOV_3_days_of_testing'
    tov_trial_rate_is_over = 'EDO_Welcome_TOV_test_period_is_over'

    tov_employees_limit_trial_expires_1_days = 'EDO_Welcome_TOV_users_1_days_of_testing'
    tov_employees_limit_trial_expires_3_days = 'EDO_Welcome_TOV_users_3_days_of_testing'
    tov_employees_limit_trial_rate_is_over = 'EDO_Welcome_TOV_users_test_period_is_over'

    rate_added = 'Tariff_added'
    rate_expired = 'Tariff_expired'

    tov_trial_enabled = 'EDO_TOV_trial_vkliucheno'
    fop_trial_enabled = 'EDO_FOP_trial_vkliucheno'

    company_registered_tov = 'EDO_Welcome_TOV'
    company_registered_fop = 'EDO_Welcome_FOP'
    company_registered_private_individual = 'EDO_Welcome_Fiz_osoby_start'

    documents_expires_15p = 'Web_documents_15%'
    documents_run_out = 'Web_documents_0'

    integration_expires_15p = 'API_documents_15%'
    integration_run_out = 'API_documents_0'

    employees_over = 'Employees_are_over'

    document_view_over_limit_146p = 'EDO_Documents_view_146%'
    document_view_over_limit_130p = 'EDO_Documents_view_130%'
    document_view_limit_full = 'EDO_Documents_view_full_limit'
    document_view_limit_90p = 'EDO_Documents_view_90%'

    new_position_accountant = 'EDO_role_buhgalter'
    new_position_director_accountant = 'EDO_role_Dir_buhgalter'
    new_position_lawyer = 'EDO_role_yurist'
    new_position_director_lawyer = 'EDO_role_Dir_yurist'
    new_position_hr = 'EDO_role_kadrovyk'
    new_position_director_hr = 'EDO_role_Dir_kadrovyk'
    new_position_it = 'EDO_role_it'
    new_position_sales = 'EDO_role_prodazhі'
    new_position_director_sales = 'EDO_role_Dir_prodazhі'
    new_position_middle_manager = 'EDO_role_middle-manager'
    new_position_top_manager_tov = 'EDO_role_TOP-manager_TOV'
    new_position_top_manager_fop = 'EDO_role_FOP+TOP-manager_FOP'

    employees_limit_trial_enabled = 'EDO_TOV_trial_users_vkliucheno'

    @staticmethod
    def get_event_by_count(count: int) -> 'Event':
        try:
            return COUNT_EVENT_MAP[count]
        except KeyError:
            raise NotImplementedError(f'No event defined for sending {count} documents')


@unique
class SubscriptionSource(Enum):
    popup = 'popup'
    footer = 'footer'


@unique
class FieldId(Enum):
    """Additional contact fields and their IDs are managed manually (no API).

    URL: https://esputnik.com/settings/settings/index.html#/additional.
    """

    is_registered = 52656

    is_legal = 52116
    is_natural = 52117

    email_confirmed = 52120
    registration_completed = 52121

    date_registered = 170_095

    date_of_sign_first_doc = 120_066
    user_signed_first_doc = 120_067

    pro_rate_on = 151_865
    pro_rate_trial_on = 151_866

    company_checking = 193_327

    documents_sent = 82_545

    position = 155_681
    is_admin = 196_772


COUNT_EVENT_MAP = {
    1: Event.company_first_document,
    10: Event.company_10_documents,
    25: Event.company_25_documents,
    50: Event.company_50_documents,
}
