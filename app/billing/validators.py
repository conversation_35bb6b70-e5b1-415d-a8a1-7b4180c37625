import csv
import logging
import typing as t
import uuid
from datetime import datetime, timedelta
from decimal import Decimal

import pydantic
import sqlalchemy as sa
from aiohttp import web
from aiohttp.web_request import FileField

from api.errors import (
    FIELD_IS_REQUIRED,
    AlreadyExists,
    Code,
    DoesNotExist,
    Error,
    InvalidRequest,
    Object,
    ServerError,
)
from app.analytics.types import GoogleAnalyticsMeta
from app.auth.db import (
    exists_company_by_id,
    select_companies_by_edrpou,
    select_company_by_id,
    select_company_id,
)
from app.auth.types import BaseUser, User
from app.auth.utils import (
    get_companies_configs_by_edrpous,
    get_company_config,
    get_default_company_config,
    is_fop,
)
from app.auth.validators import validate_role_by_user_id_and_edrpou
from app.billing.constants import (
    APRIL_1_2025,
    APRIL_2_2025,
    CURRENCY_LIST,
    DAYS_ALLOWANCE_TO_EXTEND_RATE,
    FEATURE_FLAG_CHANGED_RATES,
    MAX_EMPLOYEE_LIMIT,
    OCTOBER_2024,
)
from app.billing.db import (
    exist_bonus_by_key,
    select_account_by_id,
    select_active_company_rates,
    select_active_ultimate_rate,
    select_bill_by_id,
    select_bill_by_seqnum,
    select_bonus_by_id,
    select_bonus_by_key,
    select_company_accounts,
    select_payment_status_transactions_by_bill_id,
    select_planned_company_rates,
    select_rate_by_id,
    update_bank_transaction,
)
from app.billing.enums import (
    BONUS_ACCOUNT_TYPES_SET,
    WEB_RATES_SET,
    AccountRate,
    AccountStatus,
    AccountType,
    BillingAccountSource,
    BillServicesType,
    BillServiceType,
    BillSource,
    BillStatus,
    BonusType,
    CompanyRateStatus,
    CreatioBillType,
    RateExtensionStatus,
    RateExtensionType,
    ResourceType,
    TransactionType,
)
from app.billing.integrations.evopay import EvopayCheckoutOrder, EvopayClient
from app.billing.sa_db import select_bills_to_download, select_companies_to_download
from app.billing.schemas import (
    CreateBillCreatioSchema,
    CreateBillSchema,
    DeleteRateValidator,
)
from app.billing.tables import billing_account_table
from app.billing.types import (
    Account,
    ActivateBonusCtx,
    ActivateCustomBonusesFromFileCtx,
    ActivateEmployeeExtensionCtx,
    AddBillFromWebCtx,
    AddBillOptions,
    AddBillServiceExtensionOptions,
    AddBillServiceOptions,
    AddBillServiceRateOptions,
    AddBillServiceUnitsOptions,
    BankTransaction,
    Bill,
    BillPaymentStatus,
    Bonus,
    CancelResourcesCtx,
    ChargeDocumentContext,
    CompanyRate,
    DownloadBillsOptions,
    RateExtension,
    UpdateCompanyRateCtx,
    UserAddBillOptions,
)
from app.billing.utils import (
    TRIALS_DAYS_MAP,
    calculate_active_rate_left_days,
    calculate_extend_rate_dates,
    get_bill_total,
    get_billing_company_config,
    get_billing_user_options,
    get_bonus_id,
    get_employee_price,
    get_highest_rate,
    get_latest_rate_by_bill_type,
    get_price_per_document,
    get_rate_price,
    is_employee_extension_service,
)
from app.documents.db import select_document_by_id
from app.documents.enums import DocumentSource
from app.documents.types import DocumentWithUploader
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import money, validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import (
    end_of_day,
    local_now,
    to_local_datetime,
    to_utc_datetime,
    utc_now,
)
from app.lib.helpers import (
    csv_define_delimiter,
    csv_detect_encoding,
    get_file_extension,
    is_vchasno_company,
    not_none,
)
from app.lib.money import (
    to_units_decimal,
)
from app.lib.types import (
    DataDict,
    DataMapping,
    StrDict,
)
from app.models import exists, select_all

logger = logging.getLogger(__name__)

PORTMONE_EDRPOU = '********'

BANK_AND_PAYMENT_SYSTEM_EDRPOUS = {
    '********',  # АТ СЕНС БАНК
    '********',  # АТ КБ "Приватбанк"
    '********',  # АТ ПУМБ
    '********',  # АТ А-БАНК
    '********',  # АТ РАЙФФАЙЗЕН БАНК
    '********',  # АТ ПРАВЕКС БАНК
    '********',  # АТ БАНК ГРАНТ
    '********',  # ПуАТ "КБ "АКОРДБАНК"
    '********',  # Державний ощадний банк України
    '********',  # ПАТ БАНК ВОСТОК
    # Пумб іноді передає такий ЄДРПОУ (мабуть при оплаті терміналом коли ЄДРПОУ невідомий)
    '**********',
    '********',  # Вчасно (є оплати де платник - Вчасно)
}

# Agreement at crm manager level that the trial will not be enabled for more than a month
MAX_TRIAL_PERIOD_DAYS = 31


class CSVActivateBonusesSchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU
    units: pv.PositiveInt
    period: pv.PositiveInt


class ActivateRateSchema(pydantic.BaseModel):
    company_id: pv.UUID
    rate: AccountRate
    start_date: pv.LeftDatetime | None = None
    end_date: pv.RightDatetime | None = None
    employee_amount: t.Annotated[int, pydantic.Field(gt=0, lt=32767)] | None = None
    extend_active_rate: pv.BoolNullToFalse = False


class EvopayPageGenerationSchema(pydantic.BaseModel):
    bill_id: pv.UUID
    # price in cents
    price: pv.PositiveInt
    currency: str
    description: str


class AmountRateSchema(pydantic.BaseModel):
    amount: pv.PositiveInt


class AmountTrialRateSchema(pydantic.BaseModel):
    amount: pv.NonNegativeInt


class ActivateTrialSchema(pydantic.BaseModel):
    rate: AccountRate
    source: BillingAccountSource | None = None


class UpdateRateSchema(pydantic.BaseModel):
    rate_id: pv.UUID
    start_date: pv.LeftDatetime | None = None
    end_date: pv.RightDatetime | None = None


class ActivateBonusSchema(pydantic.BaseModel):
    bonus_key: str
    company_id: pv.UUID


class ActivateCustomBonusSchema(pydantic.BaseModel):
    role_id: pv.UUID
    company_id: pv.UUID
    units: pv.PositiveInt
    period: pv.PositiveInt
    comment: str | None = None


class ActivateDebitSchema(pydantic.BaseModel):
    bill_id: str | None = None
    role_id: pv.UUID
    company_id: pv.UUID
    amount: pv.PositiveInt
    units: pv.PositiveInt
    comment: str | None = None


class AddAccountSchema(pydantic.BaseModel):
    company_id: pv.UUID
    bill_id: str | None = None
    initiator_id: str | None = None
    rate: AccountRate
    type: AccountType
    amount: pv.NonNegativeInt
    amount_left: pv.NonNegativeInt
    units: pv.NonNegativeInt
    units_left: pv.NonNegativeInt
    status: AccountStatus | None = None
    activation_date: datetime | None = None
    date_expired: datetime | None = None


class AddBonusSchema(pydantic.BaseModel):
    created_by: pv.UUID
    key: str
    title: str
    description: str | None = None
    type: BonusType
    units: pv.PositiveInt
    period: pv.PositiveInt
    date_expired: datetime | None = None


class AddTransactionSchema(pydantic.BaseModel):
    from_: pv.UUID = pydantic.Field(alias='from_')
    to_: pv.UUID = pydantic.Field(alias='to_')
    operator_id: pv.UUID | None = None
    initiator_id: str
    type: TransactionType
    amount: pv.NonNegativeInt | None = None
    units: pv.NonNegativeInt | None = None
    comment: str | None = None


class BillSchema(pydantic.BaseModel):
    bill_id: pv.UUID


class CancelResourcesSchema(pydantic.BaseModel):
    role_id: pv.UUID
    company_id: pv.UUID
    units: pv.PositiveInt
    comment: str


class DeleteAccountSchema(pydantic.BaseModel):
    account_id: pv.UUID
    role_id: pv.UUID | None = None


class DeleteBonusSchema(pydantic.BaseModel):
    bonus_id: pv.UUID


class DownloadBillsSchema(pydantic.BaseModel):
    date_from: pv.Date | None = None
    date_to: pv.Date | None = None


class UpdateBonusSchema(pydantic.BaseModel):
    bonus_id: pv.UUID
    key: str | None = None
    title: str | None = None
    units: pv.PositiveInt | None = None
    period: pv.PositiveInt | None = None
    description: str | None = None
    date_expired: datetime | None = None


class ActivateBillServiceSchema(pydantic.BaseModel):
    start_date: datetime
    end_date: datetime


async def validate_bill_price(
    bill: Bill,
    price: int,
    currency: str,
) -> None:
    """Validates price from bill payment request"""

    if currency not in CURRENCY_LIST:
        raise InvalidRequest(reason=_('Вказана валюта оплати не підтримується сервісом'))

    total = await get_bill_total(bill)
    if not total:
        raise InvalidRequest(reason=_('Не вдалось визначити суму рахунку'))

    expected_price = total * 100  # в копійках
    if price != expected_price:
        raise InvalidRequest(reason=_('Сума рахунку не збігається із тарифним множником'))


async def validate_bill_exists_by_id(conn: DBConnection, bill_id: str) -> Bill:
    """Validates and returns Bill if exists"""

    bill = await select_bill_by_id(conn, bill_id)
    if not bill:
        raise DoesNotExist(
            obj=Object.bill,
            reason=_('Рахунок не знайдено у БД'),
        )
    return bill


def _has_two_highest_priority_rates(active_web_rates: list[CompanyRate]) -> bool:
    # sort rates by priority (to process case when have 2 rate with the same priority)
    sorted_rates = sorted(active_web_rates, key=lambda x: x.rate.priority_rank, reverse=True)
    return (
        len(sorted_rates) > 1
        and sorted_rates[0].rate.priority_rank == sorted_rates[1].rate.priority_rank
    )


def _rate_is_trial_period(rate: CompanyRate) -> bool:
    # To avoid mypy error (in db exist rates without start/end date but it's old (2021))
    assert rate.end_date and rate.start_date
    return (rate.end_date - rate.start_date) < timedelta(days=MAX_TRIAL_PERIOD_DAYS)


def is_unlimited_trial(active_web_rates: list[CompanyRate]) -> bool:
    """Determine if there is an unlimited rate(activated as trial) among the active web rates."""
    active_web_rates = [r for r in active_web_rates if r.rate.is_web]
    highest_rate = get_highest_rate(active_web_rates)

    if highest_rate and highest_rate.rate == AccountRate.pro:
        logger.info(
            'Found unlimited rate with higher priority',
            extra={'highest_rate': highest_rate.to_db()},
        )

        if _has_two_highest_priority_rates(active_web_rates):
            logger.info('Exist other active rates with the same priority')
            return False

        return _rate_is_trial_period(highest_rate)
    return False


def validate_rate_priority(rate: AccountRate, active_rates: list[CompanyRate]) -> None:
    """Validates rate priority while requesting payment by current highest rate"""

    if rate.is_web:
        # Web rates have priority ranking, and we don't allow to buy a lower rate than the
        # current one before the current rate expires.
        web_rates = [rate for rate in active_rates if rate.rate.is_web]
        highest_rate = get_highest_rate(web_rates)
        if not highest_rate or highest_rate.rate == AccountRate.free:
            return

        # pro == "Безлімітний"
        if is_unlimited_trial(web_rates):
            logger.info(
                'Deactivate unlimited trial rate',
                extra={'highest_rate': highest_rate.to_db(), 'target_rate': rate},
            )
            return

        rank_new = rate.priority_rank
        rank_highest = highest_rate.rate.priority_rank
        if rank_new < rank_highest:
            raise InvalidRequest(
                obj=Object.bill,
                reason=_('Не можна оплачувати рахунок нижче за тарифом активного'),
            )

        # Rates on the same priority level can be extended only 45 days before the end date
        # of the current highest rate. We should give the customer a chance to extend the current
        # rate before it expires to avoid any service interruptions.
        if (
            highest_rate.end_date
            and rank_new == rank_highest
            and calculate_active_rate_left_days(highest_rate) > DAYS_ALLOWANCE_TO_EXTEND_RATE
        ):
            logger.info(
                'Can not extend  current tariff earlier than 45 days before',
                extra={'highest_rate': highest_rate.to_db(), 'target_rate': rate},
            )
            raise InvalidRequest(
                obj=Object.bill,
                reason=_('Не можна подовжувати поточний тариф раніше ніж за 45 днів до кінця'),
            )

    if rate.is_archive:
        archive_rates = [rate for rate in active_rates if rate.rate.is_archive]
        if not archive_rates:
            return

        # we allow buying multiple archive rates, but they should be different
        same_archive_rates = [r for r in archive_rates if r.rate == rate]
        if not same_archive_rates:
            return

        archive_rates = sorted(same_archive_rates, key=lambda r: r.end_date or datetime.max)
        latest_archive_rate = archive_rates[-1]

        if calculate_active_rate_left_days(latest_archive_rate) > DAYS_ALLOWANCE_TO_EXTEND_RATE:
            logger.info(
                'Can not extend  current tariff earlier than 45 days before',
                extra={'highest_rate': latest_archive_rate.to_db(), 'target_rate': rate},
            )
            raise InvalidRequest(
                obj=Object.bill,
                reason=_('Не можна подовжувати поточний тариф раніше ніж за 45 днів до кінця'),
            )

    if rate.is_integration:
        # Integration rates currently doesn't have any priority ranking,
        # so we're just checking if there is no any active rate that couldn't be extended
        integration_rates = [
            rate for rate in active_rates if rate.rate.is_integration and not rate.rate.is_trial
        ]
        if not integration_rates:
            return

        # All integration rates should have "end_date". Just in case, let's check it again
        if any(rate.end_date is None for rate in integration_rates):
            raise ServerError(reason=_('Знайдено безтерміновий інтеграційний тариф'))

        integration_rates = sorted(integration_rates, key=lambda r: r.end_date or datetime.max)
        highest_rate = integration_rates[-1]

        if calculate_active_rate_left_days(highest_rate) > DAYS_ALLOWANCE_TO_EXTEND_RATE:
            logger.info(
                'Can not extend  current tariff earlier than 45 days before',
                extra={'highest_rate': highest_rate.to_db(), 'target_rate': rate},
            )

            raise InvalidRequest(
                obj=Object.bill,
                reason=_(
                    'Не можна подовжувати поточний інтеграційний тариф '
                    'раніше ніж за 45 днів до кінця'
                ),
            )


def validate_rate_planning(*, rate: AccountRate, planned_rates: list[CompanyRate]) -> None:
    """Validates to plan not more than one same rate"""

    if (
        rate
        and rate in (company_rate.rate for company_rate in planned_rates)
        or rate
        in (
            company_rate.rate
            for company_rate in planned_rates
            if company_rate.rate in AccountRate.integration_rates()
        )
    ):
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Не можна запланувати більш ніж один той самий тариф'),
        )


def skip_bill_activation_old_price_oct_2024(bill: Bill) -> bool:
    """
    Skip activation for bills created before October 2024,
    to avoid activation rates using an old bill with old prices.
    Skip the following bill types (price for these bills changed) :
     - Start rates
     - Pro rates
     - Documents

    https://tabula-rasa.atlassian.net/browse/DOC-6861

    You can remove this function after April 2025, because older bills will be skipped by
    condition "Target bill is older than 6 months" in `validate_bill_activation` function.
    """
    if bill.date_created >= OCTOBER_2024:
        return False

    if bill.services_type == BillServicesType.documents:
        return True

    if bill.services_type == BillServicesType.rate and (
        bill.single_rate_service.rate.is_start or bill.single_rate_service.rate.is_pro
    ):
        return True

    return False


def skip_bill_activation_old_price_may_2025(bill: Bill) -> bool:
    """
    Skip activation for bills created before May 2025 to avoid activation rates using an old
    bill with old prices. This change affected integration rates and documents

    https://tabula-rasa.atlassian.net/browse/DOC-7422

    You can remove this function after November 2025, because older bills will be skipped by
    condition "Target bill is older than 6 months" in `validate_bill_activation` function.
    """

    return (
        # It's a bill created before 1 May 2025
        bill.date_created <= to_utc_datetime(datetime(day=1, month=5, year=2025))
        and (
            # It's documents bills
            bill.services_type == BillServicesType.documents
            or (
                # It's integration rates bills
                bill.services_type
                in {
                    BillServicesType.rate,
                    BillServicesType.integration_and_documents,
                }
                and bill.single_rate_service.rate.is_integration
            )
        )
    )


def should_skip_bill_activation_due_to_changed_rates(bill: Bill) -> bool:
    """
    Determines whether a bill activation should be skipped due to changed rates.

    A bill will be skipped if all conditions are met:
    - The feature flag ENABLE_NEW_RATES is enabled.
    - The current date > changed rates activation deadline.
    - at least one tariff in the bill are part of FEATURE_FLAG_CHANGED_RATES.
    - Additional conditions (e.g., the bill belongs to an FOP, other) are met.

    Returns:
        bool: True if bill activation should be skipped, False otherwise.
    """
    # If ff disable - allow to activate any bills
    if not get_flag(FeatureFlags.ENABLE_NEW_RATES):
        return False

    # If none of the rates in bill not changed
    # (not in FEATURE_FLAG_CHANGED_RATES) - allow to activate bill
    if all(rate not in FEATURE_FLAG_CHANGED_RATES for rate in bill.rates):
        return False

    """
    NOTE: Add/remove here additional conditions for skipping bill activation
        if it needed (related to changes in rates limits)
    """

    if not is_fop(bill.edrpou):
        return False

    # The last date when users can activate bills with old rates
    pro_fov_rate_activation_deadline = APRIL_2_2025
    # In start changed price for FOP companies,
    # so 1 april 2025 we disable posible to activate old rates
    start_fov_rate_activation_deadline = APRIL_1_2025

    for rate in bill.rates:
        if rate.is_pro and local_now() < pro_fov_rate_activation_deadline:
            return False
        if rate.is_start and local_now() < start_fov_rate_activation_deadline:
            return False

    return True


async def validate_bill_activation(conn: DBConnection, transaction: BankTransaction) -> Bill:
    from worker.billing.utils import validate_bill_exists_by_seqnum_from_transaction

    bill = await validate_bill_exists_by_seqnum_from_transaction(conn=conn, transaction=transaction)
    if not bill:
        raise DoesNotExist(
            obj=Object.bill,
            reason=_('Рахунок не знайдено у БД'),
        )

    await update_bank_transaction(conn, transaction.transaction_id, data={'bill_id': bill.id_})

    if bill.date_created <= utc_now() - timedelta(days=180):
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Target bill is older than 6 months'),
        )

    if bill.payment_status in (
        BillPaymentStatus.completed,
        BillPaymentStatus.canceled,
        BillPaymentStatus.refund,
    ):
        # There are two scenarios where a bill could have a completed status:
        #  - The accounting team has already activated the bill via the super-admin menu
        #       (logs are stored in the super_admin_actions table).
        #  - The user made a second transaction on the same bill.
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Target bill already processed (payment completed / cancelled / refunded)'),
        )

    if skip_bill_activation_old_price_oct_2024(bill):
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Bill has old price (created before October 2024)'),
        )

    if skip_bill_activation_old_price_may_2025(bill):
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Bill has old price (created before May 2025)'),
        )

    if should_skip_bill_activation_due_to_changed_rates(bill):
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Bill has old price (created before April 2025)'),
        )

    # Skip activating if payer is not bank/paid system and not bill owner
    sender_edrpou = transaction.sender_edrpou
    if sender_edrpou and sender_edrpou != bill.edrpou:
        if sender_edrpou in BANK_AND_PAYMENT_SYSTEM_EDRPOUS:
            logger.info(
                'Payment from bank or payment system or exclude edrpous',
                extra={
                    'transaction': transaction.to_log_extra(),
                    'bill_id': bill.id_,
                    'bill_edrpou': bill.edrpou,
                    'sender_edrpou': transaction.sender_edrpou,
                },
            )
        else:
            raise InvalidRequest(
                obj=Object.transaction,
                reason=_('Transaction sender is not bill owner'),
            )

    return bill


async def validate_transaction_payment(
    conn: DBConnection, bill: Bill, transaction: BankTransaction
) -> None:
    """
    Validates bank transfer for:
    - Bill already been processed
    - Paid amount is valid with entity price
    **All validations from `validate_bill_payment_conditions`
    """

    await validate_transaction_payment_status(conn, bill.id, transaction)

    await validate_bill_payment_amount(bill=bill, transaction=transaction)
    await validate_bill_payment_conditions(conn, bill)


async def validate_transaction_payment_status(
    conn: DBConnection,
    bill_id: str,
    transaction: BankTransaction | None = None,
) -> None:
    """
    Validate payment status of transactions to avoid double bill activation.
    * transaction can be None when it's evopay payment
    """
    p_transactions = await select_payment_status_transactions_by_bill_id(
        conn=conn,
        bill_id=bill_id,
    )
    if p_transactions and BillPaymentStatus.completed in (p.payment_status for p in p_transactions):
        logger.info(
            msg='Bill was already been successfully paid',
            extra={'bill_id': bill_id, 'transaction': transaction and transaction.to_log_extra()},
        )
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('По цьому рахунку вже була проведена успішна оплата'),
        )


async def validate_bill_payment_amount(bill: Bill, transaction: BankTransaction) -> None:
    """Check if paid amount valid with entity price"""

    logger.info(
        msg='Validating bill payment amount',
        extra={'bill_id': bill.id_, 'transaction': transaction.to_log_extra()},
    )

    # bill_total_price в копійкаx
    bill_total_price = money.to_subunits_decimal_from_units(await get_bill_total(bill=bill))

    if transaction.amount != bill_total_price:
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Неправильна сума оплати рахунку'),
        )


async def validate_bill_payment_conditions(conn: DBConnection, bill: Bill) -> None:
    """
    Validates bill payment request for next conditions:
    - Payment is not allowed more than 45 days before active rate ends
    - Payment is not allowed if customer planning more than one same rate
    - Payment is not allowed if customer trying to get lower rate
    """

    from app.profile.validators import validate_company_by_edrpou_exists

    logger.info(
        msg='Validating bill payment conditions',
        extra={'bill_id': bill.id_},
    )

    rate_services = bill.rate_services

    if any(service.rate.is_deprecated for service in rate_services):
        raise InvalidRequest(
            obj=Object.company_rate,
            reason=_('Тариф є застарілим.'),
        )

    company = await validate_company_by_edrpou_exists(conn, bill.edrpou)

    if not rate_services:
        return

    active_rates = await select_active_company_rates(conn, company.id)

    if not active_rates:
        return

    planned_rates = await select_planned_company_rates(conn, company.id)

    for service in rate_services:
        rate = service.rate
        validate_rate_priority(rate=rate, active_rates=active_rates)
        validate_rate_planning(rate=rate, planned_rates=planned_rates)


async def validate_order_request(
    conn: DBConnection,
    bill_id: str,
    user_id: str,
    price: int,
    currency: str,
    description: str,
) -> EvopayCheckoutOrder:
    """Validates evopay request from frontend"""

    bill = await validate_bill_exists_by_id(conn, bill_id)

    await validate_bill_payment_conditions(conn, bill)
    role = await validate_role_by_user_id_and_edrpou(conn=conn, edrpou=bill.edrpou, user_id=user_id)

    await validate_bill_price(
        bill=bill,
        price=price,
        currency=currency,
    )

    result_url = EvopayClient.generate_evopay_result_url(role.id_, bill.id_)
    webhook_url = EvopayClient.generate_evopay_webhook_url()

    return EvopayCheckoutOrder(
        bill_id=bill.id_,
        role_id=role.id_,
        price=price,
        currency=currency,
        description=description,
        result_url=result_url,
        webhook_url=webhook_url,
    )


def _prepare_rate_status(start_date: datetime) -> CompanyRateStatus:
    """Prepare rate status on insert/update rate based on start date"""
    # don't activate rate when date_from is from a future
    not_activate = start_date and start_date > local_now()
    return CompanyRateStatus.new if not_activate else CompanyRateStatus.active


async def _validate_rate_exists(conn: DBConnection, rate_id: str) -> CompanyRate:
    """Check that role with given id exists"""
    rate = await select_rate_by_id(conn, rate_id)
    if rate is None:
        raise DoesNotExist(Object.company_rate, rate_id=rate_id)
    return rate


async def _validate_rate_dates_overlap(
    conn: DBConnection,
    company_id: str | None,
    account_rate: AccountRate,
    start_date: datetime | None,
    end_date: datetime | None,
    ignore_rate_id: str | None = None,
) -> None:
    """
    Check that new/updated range dates doesn't overlap with
    another rates in company
    """
    filters = [
        billing_account_table.c.activation_date < end_date,
        billing_account_table.c.date_expired > start_date,
        billing_account_table.c.company_id == company_id,
        billing_account_table.c.rate == account_rate,
        billing_account_table.c.type == AccountType.client_rate,
        billing_account_table.c.status.in_([CompanyRateStatus.new, CompanyRateStatus.active]),
    ]
    if ignore_rate_id is not None:
        filters.append(billing_account_table.c.id != ignore_rate_id)
    if await exists(conn=conn, select_from=billing_account_table, clause=sa.and_(*filters)):
        raise InvalidRequest(reason=_('Вже існує тариф, що пересікається по часу'))


async def validate_can_enable_rate__integration_trial(
    conn: DBConnection,
    company_id: str,
    account_rate: AccountRate,
    source: BillingAccountSource | None,
) -> None:
    """
    Check if we can enable trial rate for integration
    """
    if source is not None:
        raise InvalidRequest(
            reason=_('Параметр source не підтримується для інтеграційного тарифу'),
        )

    if await exists(
        conn=conn,
        select_from=billing_account_table,
        clause=sa.and_(
            billing_account_table.c.company_id == company_id,
            billing_account_table.c.rate == account_rate,
        ),
    ):
        raise InvalidRequest(
            reason=_('Неможливо активувати тестовий період для інтеграційного тарифу повторно'),
        )

    if await exists(
        conn=conn,
        select_from=billing_account_table,
        clause=sa.and_(
            billing_account_table.c.company_id == company_id,
            billing_account_table.c.rate == AccountRate.integration,
        ),
    ):
        raise InvalidRequest(
            reason=_(
                'Неможливо активувати тестовий період для інтеграційного тарифу, '
                'якщо для компанії цей тариф вже було активовано'
            ),
        )


async def validate_can_enable_rate__pro_plus_trial(
    conn: DBConnection,
    company_id: str,
    account_rate: AccountRate,
) -> None:
    """
    Check if we can enable trial rate for PRO+ rate

    - we allow only one trial rate per company to be active at a time
    - we allow to activate trial rate for each web rate only once
    """
    web_rates = await select_active_company_rates(
        conn=conn,
        company_id=company_id,
        rates=list(WEB_RATES_SET),
    )
    web_rates = [rate for rate in web_rates if not rate.rate.is_trial]

    _is_company_trial = sa.and_(
        billing_account_table.c.company_id == company_id,
        billing_account_table.c.rate == account_rate,  # pro_plus trial
        billing_account_table.c.type == AccountType.client_rate,
    )

    # When a user purchases a paid web rate, we cancel all trial rates. In this case, we don't need
    # to count canceled rates because they are removed at the moment a new rate becomes active.
    # Note that we use "expired" status for trial rates that have reached their end date, and we
    # count them for active web rates, unlike canceled rates.
    _is_not_canceled = billing_account_table.c.status.notin_((CompanyRateStatus.canceled,))

    # We allow to be active only one trial rate per company
    if await exists(
        conn=conn,
        select_from=billing_account_table,
        clause=sa.and_(
            _is_company_trial,
            billing_account_table.c.status == CompanyRateStatus.active,
        ),
    ):
        raise InvalidRequest(
            reason=_('Тестовий період для тарифу вже активовано'),
        )

    # Usually, at least the free rate should be active for a company, but sometimes life is
    # tough, and we might encounter companies with no active rates at all. For example,
    # a job that creates the free rate might have failed, or somewhere in the code, we might have
    # simply forgotten to schedule it. For those cases, we assume that the company has a free
    # rate with date activation that somewhere far in the past
    if not web_rates:
        if await exists(
            conn=conn,
            select_from=billing_account_table,
            clause=sa.and_(
                _is_company_trial,
                _is_not_canceled,
                billing_account_table.c.activation_date > datetime.min,
            ),
        ):
            raise InvalidRequest(
                reason=_('Неможливо активувати тестовий період для тарифу повторно'),
            )
    else:
        # We checking if each web rate has trial rate associated with it. Technically, there is no
        # direct connection between active web rates and web trial rates, so we establish an
        # implicit connection between them by checking the date range.
        # Usually we have 1-2 active web rates, so we make a N+1 query to find all of them
        rate_trials: dict[str, list[str]] = {}
        for rate in web_rates:
            rows = await select_all(
                conn=conn,
                query=(
                    sa.select([billing_account_table.c.id])
                    .select_from(billing_account_table)
                    .where(
                        sa.and_(
                            _is_company_trial,
                            _is_not_canceled,
                            billing_account_table.c.activation_date >= rate.start_date,
                            billing_account_table.c.activation_date <= rate.end_date,
                        )
                    )
                ),
            )
            rate_trials[rate.id_] = [row.id for row in rows]

        all_trials_ids = {_id for _ids in rate_trials.values() for _id in _ids}

        # when each web rate has trial rate rate associated with it, we assume that trial is already
        # activate for all current web rates and deny request to activate it again. Also trials
        # can be associated with several web rates, so we check also amount of trials ids
        # associated with web rates
        if all(rate_trials.values()) and len(all_trials_ids) >= len(web_rates):
            raise InvalidRequest(
                reason=_('Неможливо активувати тестовий період для тарифу повторно'),
            )


async def validate_can_enable_trial_rate(
    conn: DBConnection,
    company_id: str,
    account_rate: AccountRate,
    source: BillingAccountSource | None,
) -> None:
    """
    Validate certain rate has never been activated before.

    Previous tasks:
     - https://tabula-rasa.atlassian.net/browse/DOC-6800
    """

    # only those two rates are allowed to be activated as trial:
    latest_pro_plus_trial = AccountRate.latest_pro_plus_trial()
    latest_integration_trial = AccountRate.latest_integration_trial()

    if account_rate == latest_integration_trial:
        await validate_can_enable_rate__integration_trial(
            conn=conn,
            company_id=company_id,
            account_rate=account_rate,
            source=source,
        )
    elif account_rate == latest_pro_plus_trial:
        await validate_can_enable_rate__pro_plus_trial(
            conn=conn,
            company_id=company_id,
            account_rate=account_rate,
        )
    else:
        logger.exception(
            msg='Invalid rate for trial',
            extra={
                'account_rate': account_rate,
                'company_id': company_id,
                'source': source,
            },
        )
        raise InvalidRequest(reason=_('Вказаний тариф не є тестовим'))


async def validate_add_trial_company_rate(
    conn: DBConnection,
    data: ActivateTrialSchema,
    user: User,
) -> CompanyRate:
    """
    Check that we can enable trial rate for given company
    """
    account_rate = data.rate
    source = data.source

    await validate_can_enable_trial_rate(
        conn=conn,
        company_id=user.company_id,
        account_rate=account_rate,
        source=source,
    )

    start_date = local_now()
    end_date = end_of_day(start_date + TRIALS_DAYS_MAP[account_rate])
    end_date = to_local_datetime(end_date.replace(tzinfo=None))
    return CompanyRate(
        id_=str(uuid.uuid4()),
        company_id=user.company_id,
        company_edrpou=user.company_edrpou,
        initiator_id=user.role_id,
        rate=account_rate,
        source=source,
        status=CompanyRateStatus.active,
        amount=0,
        start_date=start_date,
        end_date=end_date,
    )


async def validate_activate_bonus(
    conn: DBConnection,
    raw_data: DataDict,
    check_for_duplication: bool = False,
) -> ActivateBonusCtx:
    """Validator to activate bonus for a specific company.

    - Check for bonus existence.
    - Check for company existence.
    - For enabled ``check_for_duplication`` flag, need to check for rewarded
    bonus with same key, if this bonus existed then do nothing.
    """
    data = validators.validate_pydantic(ActivateBonusSchema, raw_data)
    bonus_key = data.bonus_key
    company_id = data.company_id

    if check_for_duplication:
        bonus_id = await get_bonus_id(conn, bonus_key)
        is_duplicated = await exists(
            conn,
            billing_account_table,
            sa.and_(
                billing_account_table.c.company_id == company_id,
                billing_account_table.c.type == AccountType.client_bonus,
                billing_account_table.c.initiator_id == bonus_id,
            ),
        )
        if is_duplicated:
            return ActivateBonusCtx(
                bonus_key=bonus_key,
                company_id=company_id,
                bonus=Bonus(
                    id='',
                    key=bonus_key,
                    title='',
                    type=BonusType.bonus,
                    units=0,
                    period=0,
                    date_created=utc_now(),
                ),
                is_duplicated=True,
            )

    bonus = await select_bonus_by_key(conn, bonus_key)
    if not bonus:
        raise DoesNotExist(Object.bonus, key=bonus_key)

    if not await exists_company_by_id(conn, company_id):
        raise DoesNotExist(Object.company, id=company_id)

    return ActivateBonusCtx(
        bonus_key=bonus_key,
        company_id=company_id,
        bonus=bonus,
    )


async def validate_activate_custom_bonus(
    conn: DBConnection, raw_data: DataDict
) -> ActivateCustomBonusSchema:
    """Validator to activate custom bonus.

    - Check for company existence.
    """
    data = validators.validate_pydantic(ActivateCustomBonusSchema, raw_data)
    company_id = data.company_id

    if not await exists_company_by_id(conn, company_id):
        raise DoesNotExist(Object.company, id=company_id)

    return data


async def validate_activate_custom_bonuses_from_file(
    conn: DBConnection, data: DataMapping
) -> ActivateCustomBonusesFromFileCtx:
    """Ensure input data is a file with allowed extension and validate CSV
    data.
    """
    allowed_extensions = ['.csv']
    file_item = data.get('file')

    if not (
        isinstance(file_item, FileField)
        and get_file_extension(file_item.filename) in allowed_extensions
    ):
        raise Error(
            Code.invalid_file_extension,
            details={'allowed_extensions': ', '.join(allowed_extensions)},
        )

    file_content = file_item.file.read()
    encoding = csv_detect_encoding(file_content)
    file_data = file_content.decode(encoding)
    delimiter = csv_define_delimiter(file_data)

    # Counters for feedback
    row_number = 0
    count_invalid_rows = 0
    empty_rows = 0
    duplicate_rows = 0
    invalid_row_numbers = []

    # Valid data
    bonuses: dict[str, StrDict] = {}

    csv_reader = csv.reader(file_data.splitlines(), delimiter=delimiter)
    for row_number, row in enumerate(csv_reader, 1):
        # Ignore empty lines, generated on Mac during Excel -> CSV conversion.
        if not any(row):
            empty_rows += 1
            continue

        if len(row) < 3:
            count_invalid_rows += 1
            invalid_row_numbers.append(row_number)
            continue

        try:
            validator = validators.validate_pydantic(
                CSVActivateBonusesSchema, {'edrpou': row[0], 'units': row[1], 'period': row[2]}
            )
        except InvalidRequest:
            count_invalid_rows += 1
            invalid_row_numbers.append(row_number)
            continue

        valid_invite = validator.model_dump(mode='json')
        edrpou = valid_invite['edrpou']

        # Ignore duplicates in file
        if edrpou not in bonuses:
            bonuses[edrpou] = valid_invite
        else:
            duplicate_rows += 1

    # Check for companies exist, if company do not exist then remove it
    # from bonuses dict
    edrpous = list(bonuses.keys())
    companies = await select_companies_by_edrpou(conn, edrpous)
    existed_edrpous = [c.edrpou for c in companies]
    non_existent_edrpous = list(set(edrpous) - set(existed_edrpous))
    if len(non_existent_edrpous):
        for e in non_existent_edrpous:
            bonuses.pop(e, None)

    if not bonuses:
        raise Error(Code.empty_upload_csv)

    # Get companies ID
    companies_edrpou_id_map: DataDict = {}
    for c in companies:
        companies_edrpou_id_map[c.edrpou] = c.id
    for edrpou, bonus in bonuses.items():
        bonus['company_id'] = companies_edrpou_id_map[edrpou]

    return ActivateCustomBonusesFromFileCtx(
        bonuses=list(bonuses.values()),
        counters={
            'total': row_number - empty_rows,
            'duplicated': duplicate_rows,
            'invalid': count_invalid_rows,
            'invalid_row_numbers': invalid_row_numbers,
            'non_existent_edrpous': non_existent_edrpous,
        },
    )


async def validate_activate_debit(conn: DBConnection, raw_data: DataDict) -> ActivateDebitSchema:
    """Validator to activate debit account.

    - Check for company existence.
    - Check for bill existence.

    Be aware: bill_id currently serves as bill seqnum on this endpoint
    TODO: Rename bill_id to bill_seqnum on back&front
    """

    from worker.billing.utils import get_bill_seqnum_from_line

    data = validators.validate_pydantic(ActivateDebitSchema, raw_data)

    company_id = data.company_id
    bill_id = data.bill_id

    if not await exists_company_by_id(conn, company_id):
        raise DoesNotExist(Object.company, id=company_id)

    if bill_id:
        try:
            uuid.UUID(bill_id, version=4)
        except ValueError:
            bill_seqnum = get_bill_seqnum_from_line(bill_id)
            if not bill_seqnum:
                raise DoesNotExist(Object.bill, number=bill_id)
            bill = await select_bill_by_seqnum(conn, bill_seqnum)
            if not bill:
                raise DoesNotExist(Object.bill, number=bill_id)
            # Create new instance with updated bill_id
            return ActivateDebitSchema(
                bill_id=bill.id_,
                role_id=data.role_id,
                company_id=data.company_id,
                amount=data.amount,
                units=data.units,
                comment=data.comment,
            )

    return data


def validate_add_account(raw_data: DataDict) -> AddAccountSchema:
    """Validator to add account.

    Allow to add only ``client`` type accounts.
    ``service`` type accounts can be added only by database migration.
    """
    data = validators.validate_pydantic(AddAccountSchema, raw_data)
    account_type = data.type

    if account_type.is_service:
        raise Error(Code.billing_account_type_not_allowed, details={'type': account_type})

    return data


async def validate_add_bonus(conn: DBConnection, raw_data: DataDict) -> AddBonusSchema:
    """Validator to add bonus.

    - Don't allow to add new bonus with type ``bonus``.
    - Don't allow to add new bonus with not unique key.
    """
    data = validators.validate_pydantic(AddBonusSchema, raw_data)
    bonus_key = data.key
    bonus_type = data.type

    if bonus_type == BonusType.bonus:
        raise Error(Code.billing_bonus_type_not_allowed, details={'type': bonus_type.value})

    if await exist_bonus_by_key(conn, bonus_key):
        raise AlreadyExists(Object.bonus, key=bonus_key)

    return data


def _validate_rate_is_not_deprecated(rate: AccountRate) -> None:
    """Check that rate is not deprecated"""
    if rate.is_deprecated:
        raise InvalidRequest(obj=Object.company_rate, reason=_('Тариф є застарілим.'))


async def validate_add_bill_service_rate(
    conn: DBConnection,
    user: UserAddBillOptions,
    rate: AccountRate,
    date_from: datetime | None,
) -> AddBillServiceRateOptions:
    if company_id := user.company_id:
        await validate_bill_dates(
            conn=conn,
            company_id=company_id,
            date_from=date_from,
            rate=rate,
        )

        active_rates = await select_active_company_rates(conn, company_id=company_id)
        if active_rates:
            validate_rate_priority(rate=rate, active_rates=active_rates)
            planned_rates = await select_planned_company_rates(conn, company_id=company_id)
            validate_rate_planning(rate=rate, planned_rates=planned_rates)

    _validate_rate_is_not_deprecated(rate=rate)

    rate_price = get_rate_price(rate=rate, edrpou=user.edrpou)  # в гривнях
    unit_price = money.to_subunits_decimal_from_units(rate_price)  # в копійках

    return AddBillServiceRateOptions(
        rate=rate,
        units=1,
        unit_price=unit_price,
        date_from=date_from,
        # From web interface, we don't allow creating a bill for Ultimate rate (only rate with
        # custom number of employees),
        # so we can safely set limits_employees_count and price_per_user to None here.
        limits_employees_count=None,
        price_per_user=None,
    )


async def validate_add_bill_service_extension(
    conn: DBConnection,
    user: UserAddBillOptions,
    units: int,
    extension: RateExtensionType,
) -> AddBillServiceExtensionOptions:
    from app.profile.validators import validate_company_by_edrpou_exists

    await validate_company_by_edrpou_exists(conn, edrpou=user.edrpou)
    company_rate = await validate_active_ultimate_rate(conn, edrpou=user.edrpou)

    price_per_user = company_rate.price_per_user  # в копійках

    if not price_per_user:
        raise InvalidRequest(
            obj=Object.company_rate,
            reason=_('Тариф не містить ціни за користувача. Зверніться до менеджера'),
        )

    unit_price = money.to_subunits_decimal(price_per_user)  # в копійках

    return AddBillServiceExtensionOptions(
        units=units,
        unit_price=unit_price,
        date_from=None,
        extension=extension,
        company_rate=company_rate,
    )


async def validate_add_bill_service_units(
    conn: DBConnection,
    user: UserAddBillOptions,
    units: int,
) -> AddBillServiceUnitsOptions:
    """
    Validate add bill service units balance. Currently, we support only documents units/credits.
    """

    # в гривнях, float, example: 100.25, 25.50
    document_price = await get_price_per_document(conn=conn, company_edrpou=user.edrpou)
    unit_price = money.to_subunits_decimal_from_units(document_price)  # в копійках

    return AddBillServiceUnitsOptions(
        units=units,
        unit_price=unit_price,
    )


async def validate_add_bill_services(
    conn: DBConnection,
    data: CreateBillSchema,
    user: UserAddBillOptions,
) -> list[AddBillServiceOptions]:
    validated_services: list[AddBillServiceOptions] = []
    for service in data.services:
        if service.type == BillServiceType.rate:
            _service_rate = await validate_add_bill_service_rate(
                conn=conn,
                user=user,
                rate=service.rate,
                date_from=service.date_from,
            )
            validated_services.append(_service_rate)
        elif service.type == BillServiceType.extension:
            _service_extension = await validate_add_bill_service_extension(
                conn=conn,
                user=user,
                units=service.units,
                extension=service.extension,
            )
            validated_services.append(_service_extension)

        elif service.type == BillServiceType.units:
            _service_units = await validate_add_bill_service_units(
                conn=conn,
                user=user,
                units=service.units,
            )
            validated_services.append(_service_units)
        else:
            t.assert_never(service.type)

    return validated_services


async def validate_bill_dates(
    conn: DBConnection,
    company_id: str,
    date_from: datetime | None,
    rate: AccountRate,
) -> None:
    if date_from and date_from.date() < local_now().date():
        raise InvalidRequest(reason=_('Дата початку дії тарифу не може бути меншою за сьогодні'))

    if date_from:
        date_to = date_from + timedelta(days=365)
        await _validate_rate_dates_overlap(
            conn=conn,
            company_id=company_id,
            account_rate=rate,
            start_date=date_from,
            end_date=date_to,
        )


def _validate_add_bill_type_by_services(services: list[AddBillServiceOptions]) -> BillServicesType:
    """
    Convert services to a bill type and raise an exception if an unknown combination of services
    is provided.
    """
    if len(services) == 0:
        raise InvalidRequest(reason=_('Не вказано жодного сервісу для рахунку'))

    if len(services) == 1:
        service = services[0]
        if service.type == BillServiceType.extension:
            return BillServicesType.add_employee
        if service.type == BillServiceType.rate:
            return BillServicesType.rate
        if service.type == BillServiceType.units:
            return BillServicesType.documents
        t.assert_never(service.type)

    if len(services) == 2:
        has_web_rate = any(s.rate.is_web for s in services if s.type == BillServiceType.rate)
        has_archive_rate = any(
            s.rate.is_archive for s in services if s.type == BillServiceType.rate
        )
        has_integration_rate = any(
            s.rate.is_integration for s in services if s.type == BillServiceType.rate
        )
        has_units_service = any(s.type == BillServiceType.units for s in services)

        if has_integration_rate and has_units_service:
            raise InvalidRequest(
                reason=_(
                    'Наразі не підтримується комбінація сервісів '
                    'для купівлі інтеграції і документів'
                ),
            )

        if has_web_rate and has_archive_rate:
            return BillServicesType.web_and_archive

        raise InvalidRequest(
            reason=_(
                'Неправильна комбінація сервісів для формування рахунку. Наразі можливо формувати '
                'рахунок для купівля тарифу інтеграція і поповнення балансу документів або купівля '
                'веб і архів тарифу'
            ),
        )

    raise InvalidRequest(
        reason=_('Наразі комбіновані рахунки не підтримують більше ніж два сервіси')
    )


async def validate_add_bill_from_web(
    conn: DBConnection,
    raw_data: DataDict,
    source: BillSource,
    user: BaseUser | User | None,
) -> AddBillFromWebCtx:
    """
    Validate the ability to create a bill by a user. It can work for authenticated and not
    authenticated users.

    It consists of several steps:
    - validate common data
    - validate services
    """
    data = validators.validate_pydantic(CreateBillSchema, raw_data)
    user_options = await get_billing_user_options(user=user, data=data)

    validated_services = await validate_add_bill_services(conn, data, user=user_options)

    # don't allow buying ultimate rate from web
    if any(s.rate.is_ultimate for s in validated_services if s.type == BillServiceType.rate):
        raise InvalidRequest(
            reason=_(
                'Неможливо купити тариф "Максимальний" через веб. Зверніться до менеджера з продажу'
            ),
        )
    custom_price = None

    # Detect a bill type by services
    services_type = _validate_add_bill_type_by_services(services=validated_services)

    if services_type == BillServicesType.add_employee:
        ultimate_rate = await validate_active_ultimate_rate(conn, edrpou=data.edrpou)

        if ultimate_rate.price_per_user is None or ultimate_rate.end_date is None:
            raise InvalidRequest(reason=_('Неможливо створити рахунок'))

        __, price_per_user_total = get_employee_price(
            price_per_user=ultimate_rate.price_per_user,
            rate_end_date=ultimate_rate.end_date,
        )

        # Store total price for bill in custom_price field to correct show price in pdf bill.
        service = next(
            service for service in validated_services if is_employee_extension_service(service)
        )
        custom_price = to_units_decimal(price_per_user_total * service.units)

    analytics_meta = None
    if data.session_id and data.client_id and data.session_number:
        analytics_meta = GoogleAnalyticsMeta(
            client_id=data.client_id,
            session_id=data.session_id,
            session_number=data.session_number,
        )

    options = AddBillOptions(
        name=data.name,
        edrpou=data.edrpou,
        email=data.email,
        role_id=user_options.role_id,
        company_id=user_options.company_id,
        user_id=user_options.user_id,
        source=source,
        agreement=data.agreement,
        contract_basis=None,
        payment_purpose=None,
        is_card_payment=data.is_card_payment,
        services_type=services_type,
        services=validated_services,
        # Used only in BillServicesType.add_employee, in other cases no custom price from web
        custom_price=custom_price,
        status=BillStatus.requested,
    )

    return AddBillFromWebCtx(options=options, analytics_meta=analytics_meta)


async def validate_add_bill_from_creatio(conn: DBConnection, raw_data: DataDict) -> AddBillOptions:
    data = validators.validate_pydantic(CreateBillCreatioSchema, raw_data)

    services_type: BillServicesType
    services: list[AddBillServiceOptions]

    # Custom total sum of the bill
    # May be different from the sum of all services
    custom_price: Decimal | None = None
    if data.custom_price is not None:
        custom_price = money.to_units_decimal(data.custom_price)  # в гривнях

    price_per_user: Decimal | None = None
    if data.price_per_user:
        # Decimal('4200.5') -> Decimal('420050')
        price_per_user = money.to_subunits_decimal_from_units(data.price_per_user)  # в копійках

    # WARN: do not use "data.rate" as "AccountRate" because it's CRM-specific enum
    bill_type = data.rate

    if bill_type == CreatioBillType.integration_with_documents:
        services_type = BillServicesType.integration_and_documents
        rate = AccountRate.integration

        rate_price = get_rate_price(rate=rate, edrpou=data.edrpou)  # в гривнях
        unit_rate_price = money.to_subunits_decimal_from_units(rate_price)  # в копійках

        # в гривнях, float
        document_price = await get_price_per_document(conn=conn, company_edrpou=data.edrpou)
        unit_document_price = money.to_subunits_decimal_from_units(document_price)  # в копійках

        count_documents = not_none(data.count_documents)
        services = [
            AddBillServiceRateOptions(
                rate=AccountRate.integration,
                units=1,
                unit_price=unit_rate_price,
                date_from=None,  # no date here
                limits_employees_count=None,
                price_per_user=None,
            ),
            AddBillServiceUnitsOptions(
                units=count_documents,
                unit_price=unit_document_price,
            ),
        ]

    elif bill_type == CreatioBillType.add_employee:
        services_type = BillServicesType.add_employee

        company_rate = await validate_active_ultimate_rate(conn, edrpou=data.edrpou)

        # This data is required for the "add_employee" bill type
        price_per_user = not_none(price_per_user)  # в копійках
        units = not_none(data.employees_count)

        services = [
            AddBillServiceExtensionOptions(
                units=units,
                unit_price=price_per_user,
                extension=RateExtensionType.employees,
                date_from=data.planned_activation_date,
                company_rate=company_rate,
            )
        ]

    elif bill_type is not None and data.extra_rate is not None:
        services_type = BillServicesType.web_and_archive
        web_rate = get_latest_rate_by_bill_type(bill_type=bill_type)
        archive_rate = get_latest_rate_by_bill_type(bill_type=data.extra_rate)

        web_price = get_rate_price(rate=web_rate, edrpou=data.edrpou)  # в гривнях
        archive_price = get_rate_price(rate=archive_rate, edrpou=data.edrpou)  # в гривнях

        web_unit_price = money.to_subunits_decimal_from_units(web_price)  # в копійках
        archive_unit_price = money.to_subunits_decimal_from_units(archive_price)  # в копійках

        services = [
            AddBillServiceRateOptions(
                rate=web_rate,
                units=1,
                unit_price=web_unit_price,
                date_from=None,
                limits_employees_count=data.employees_count,
                price_per_user=None,
            ),
            AddBillServiceRateOptions(
                rate=archive_rate,
                units=1,
                unit_price=archive_unit_price,
                date_from=None,
                limits_employees_count=None,
                price_per_user=None,
            ),
        ]

    elif data.count_documents or data.rate == CreatioBillType.documents:
        services_type = BillServicesType.documents

        count_documents = not_none(data.count_documents)

        # в гривнях, float
        document_price = await get_price_per_document(conn=conn, company_edrpou=data.edrpou)
        unit_price = money.to_subunits_decimal_from_units(document_price)  # в копійках
        services = [
            AddBillServiceUnitsOptions(
                units=count_documents,
                unit_price=unit_price,
            )
        ]

    elif bill_type is not None:
        services_type = BillServicesType.rate
        account_rate = get_latest_rate_by_bill_type(bill_type=bill_type)

        if account_rate.is_ultimate:
            # Ultimate rate has only custom price, rate price does not exist
            _custom_price = not_none(custom_price)
            price_per_user = not_none(price_per_user)

            unit_price = money.to_subunits_decimal_from_units(_custom_price)  # в копійках
        else:
            document_price = get_rate_price(rate=account_rate, edrpou=data.edrpou)  # в гривнях
            unit_price = money.to_subunits_decimal_from_units(document_price)  # в копійках

        services = [
            AddBillServiceRateOptions(
                rate=account_rate,
                units=1,
                unit_price=unit_price,
                date_from=None,
                limits_employees_count=data.employees_count,
                price_per_user=price_per_user,
            )
        ]

    else:
        logger.info('Unsupported bill type', extra={'data': str(data)})
        raise InvalidRequest(reason=_('Неправильний запит на формування рахунку'))

    return AddBillOptions(
        name=data.name,
        edrpou=data.edrpou,
        email=data.email,
        role_id=None,
        company_id=None,
        user_id=None,
        source=BillSource.creatio,
        agreement=None,
        contract_basis=data.contract_basis,
        payment_purpose=data.payment_purpose,
        is_card_payment=False,
        services_type=services_type,
        services=services,
        custom_price=custom_price,
        # bills from creation doesn't have a status of bill generation
        status=None,
    )


def validate_add_transaction(raw_data: DataDict) -> AddTransactionSchema:
    """Validator to add transaction.

    - ``amount`` or ``units`` must exist
    - Check ``operator_id`` for some transaction types.
    - Check ``amount`` for some transaction types.
    - Check ``units`` for some transaction types.
    """
    data = validators.validate_pydantic(AddTransactionSchema, raw_data)
    type_ = data.type
    operator_id = data.operator_id
    amount = data.amount
    units = data.units

    if (
        (amount is None and units is None)
        or (TransactionType(type_).with_operator and operator_id is None)
        or (TransactionType(type_).with_amount and amount is None)
        or (TransactionType(type_).with_units and units is None)
    ):
        raise InvalidRequest(
            reason=FIELD_IS_REQUIRED,
            details={
                'type': type_,
                'operator_id': operator_id,
                'amount': amount,
                'units': units,
            },
        )

    # Convert None values to 0 for database compatibility
    return AddTransactionSchema(
        from_=data.from_,
        to_=data.to_,
        operator_id=data.operator_id,
        initiator_id=data.initiator_id,
        type=data.type,
        amount=data.amount if data.amount is not None else 0,
        units=data.units if data.units is not None else 0,
        comment=data.comment,
    )


async def validate_cancel_resources(
    conn: DBConnection, resource_type: ResourceType, raw_data: DataDict
) -> CancelResourcesCtx:
    """Validate to cancel resources.

    - Check that company have enough resources to cancel.
    """
    data = validators.validate_pydantic(CancelResourcesSchema, raw_data)
    accounts: list[Account] = []

    if resource_type == ResourceType.bonus:
        accounts = await select_company_accounts(conn, data.company_id, only_bonus=True)
    elif resource_type == ResourceType.debit:
        accounts = await select_company_accounts(conn, data.company_id, only_debit=True)

    available_resources = 0
    for account in accounts:
        available_resources = available_resources + account.units_left
    if data.units > available_resources:
        raise Error(Code.billing_not_enough_resources)

    # accounts with old rate must be canceled first
    accounts = sorted(accounts, key=lambda a: a.rate != AccountRate.old)

    return CancelResourcesCtx(
        role_id=data.role_id,
        company_id=data.company_id,
        units=data.units,
        comment=data.comment,
        accounts=accounts,
    )


async def validate_charge_document(
    conn: DBConnection,
    role_id: str,
    company_id: str,
    document_id: str,
    document_vendor: str | None,
    document_source: DocumentSource | None,
    document_is_internal: bool,
    document_is_internal_bill: bool,
) -> ChargeDocumentContext | None:
    """
    Check if web can charge a document from the company-account balance.

    This function doesn't have logic about the document status itself, it should be checked
    before calling this function. This function has a responsibility to check only the type of
    the document and company rates and accounts.

    - IF a document is free, then we return None.
    - IF a document can be charged, then we return an object with all necessary data to charge it.
    - IF a document can't be charged, then we raise an error.

    NOTE: The parameter "company_id" is not always a current user company ID, sometimes another
    company can pay for the document. For example, a parent company can pay for the child company.
    Use "validate_document_payer" or "validate_company_payer" for determining the payer company ID.

    Works in a pair with "charge_document" function.
    """

    log_extra = {
        'document_id': document_id,
        'company_id': company_id,
        'document_is_internal_bill': document_is_internal_bill,
        'document_is_internal': document_is_internal,
        'document_source': document_source,
        'document_vendor': document_vendor,
    }

    document = await select_document_by_id(conn, document_id)

    # Ignore EDI documents, billing for such documents is handled on the EDI side
    is_from_edi = document_source and document_source.is_from_edi
    if is_from_edi:
        logger.info('Skip charging EDI document', extra=log_extra)
        return None

    # Ignore HRS documents, billing for such documents is handled on the HRS side
    is_from_hrs = document_source and document_source.is_from_hrs
    if is_from_hrs:
        logger.info('Skip charging HRS document', extra=log_extra)
        return None

    # Don't charge documents from ourselves
    if await is_vchasno_company(conn, company_id):
        logger.info('Skip charging document from Vchasno', extra=log_extra)
        return None

    # When we send bills from "ТОВ Вчасно" to the clients, we don't charge ourselves.
    # This is not merged with the previous condition because in dev/local environment we use
    # "Тестова компанія (55555555)", instead of "TOV Vchasno" for sending bills.
    if document_is_internal_bill:
        logger.info('Skip charging bill document', extra=log_extra)
        return None

    rates = await select_active_company_rates(conn, company_id=company_id)

    # Rules by document source:
    # - API documents is always charged
    # - WEB documents is charged when a company doesn't have a chargeless rate
    is_from_api = document_vendor is not None

    # Portmone case scenario
    # When document was uploaded by Portmone, but document owner is other company,
    # don't consider this document as from API and charge as web document instead
    if (
        is_from_api
        and document
        and document.uploaded_by_edrpou == PORTMONE_EDRPOU
        and document.edrpou_owner != PORTMONE_EDRPOU
    ):
        is_from_api = False

    company = await select_company_by_id(conn, company_id)
    has_chargeless_rate = any(r.rate.is_chargeless(is_fop(company.edrpou)) for r in rates)  # type: ignore
    if not is_from_api and has_chargeless_rate:
        logger.info('Skip charging document with chargeless rate', extra=log_extra)
        return None

    # Ignore an internal document for Integration and PRO rates because we make little money
    # from them. See: https://tabula-rasa.atlassian.net/browse/DOC-6486
    if document_is_internal:
        # Sometimes we can manually enable charging for internal documents for some companies,
        # but by default, internal documents are free.
        company_config = await get_company_config(conn, company_id=company_id)
        is_free_internal = not company_config.paid_internal_documents

        has_api = any(r.rate.is_integration and not r.rate.is_integration_trial for r in rates)
        has_web_pro = any(r.rate.is_pro and not r.rate.is_web_trial for r in rates)

        if is_free_internal and ((is_from_api and has_api) or (not is_from_api and has_web_pro)):
            logger.info('Skip charging internal document', extra=log_extra)
            return None

    # In other cases, like API document or user doesn't have a chargeless rate,
    # we need to charge the document from the company's account balance.
    accounts = await select_company_accounts(conn, company_id, ignore_new_rates=True)
    api_accounts = [acc for acc in accounts if acc.rate.is_integration]
    web_accounts = [acc for acc in accounts if acc.rate.is_web]

    has_account = len(api_accounts) > 0 if is_from_api else len(web_accounts) > 0
    if not has_account:
        logger.info(
            'No accounts found for charging document',
            extra={
                **log_extra,
                'api_accounts': api_accounts,
                'web_accounts': web_accounts,
            },
        )
        raise Error(Code.billing_overlimit)

    return ChargeDocumentContext(
        payer_id=company_id,
        role_id=role_id,
        document_id=document_id,
        is_api=is_from_api,
        accounts=accounts,
    )


async def validate_delete_account(conn: DBConnection, raw_data: DataDict) -> DeleteAccountSchema:
    """Validator to delete account.

    - Check for account existence.
    - Check for account type.

    Allow to delete only bonus and debit type accounts.
    """
    data = validators.validate_pydantic(DeleteAccountSchema, raw_data)
    account_id = data.account_id

    account = await select_account_by_id(conn, account_id)
    if not account or account.date_deleted:
        raise DoesNotExist(Object.billing_account, id=account_id)

    valid_types = [*BONUS_ACCOUNT_TYPES_SET, AccountType.client_debit.value]
    if account.type.value not in valid_types:
        raise Error(
            Code.billing_reject_delete_account,
            reason=_('Неможливо видалити рахунок данного типу.'),
            details={'type': account.type.value},
        )

    return data


async def validate_delete_bonus(conn: DBConnection, raw_data: DataDict) -> DeleteBonusSchema:
    """Validator to delete bonus.

    - Check for bonus existence.
    - Don't allow to delete bonus with type ``bonus``.
    """
    data = validators.validate_pydantic(DeleteBonusSchema, raw_data)
    bonus_id = data.bonus_id

    bonus = await select_bonus_by_id(conn, bonus_id)
    if not bonus or bonus.date_deleted:
        raise DoesNotExist(Object.bonus, id=bonus_id)
    if bonus.type == BonusType.bonus:
        raise Error(Code.billing_bonus_type_not_allowed, details={'type': bonus.type.value})

    return data


async def validate_download_bills(conn: DBConnection, raw_data: DataDict) -> list[DBRow]:
    data = validators.validate_pydantic(DownloadBillsSchema, raw_data)
    options = DownloadBillsOptions(**data.model_dump())
    bills = await select_bills_to_download(conn, options)
    return bills


async def validate_download_companies(conn: DBConnection, raw_data: DataDict) -> list[DBRow]:
    data = validators.validate_pydantic(DownloadBillsSchema, raw_data)
    options = DownloadBillsOptions(**data.model_dump())
    companies = await select_companies_to_download(conn, options)
    return companies


async def validate_company_payer(conn: DBConnection, *, company_id: str) -> str:
    """
    Validate who pays for the given company and return the payer company ID.
    """
    config = await get_company_config(conn, company_id=company_id)

    parent_company = config.parent_company  # EDRPOU
    parent_pay = config.allow_parent_company_pay_for_documents

    if parent_company and parent_pay:
        parent_id = await select_company_id(conn, edrpou=parent_company, is_legal=True)
        if parent_id:
            return parent_id

        logger.warning(
            'Unable to find uploader parent company ID in database',
            extra={'edrpou': parent_company},
        )

    return company_id


async def validate_document_payer(
    conn: DBConnection, document: DocumentWithUploader, recipient_edrpou: str | None = None
) -> str:
    """
    Determine and validate payer for document.

    - database company config is prioritized over file config
    - allow to pay by parent companies only for API documents
    """
    recipient_edrpou = recipient_edrpou or document.edrpou_recipient
    uploader_edrpou = document.uploaded_by_edrpou
    uploader_id = document.uploaded_by_company_id
    is_from_api = document.vendor is not None

    # Portmone case scenario
    # Payer is the company which is the owner of the document, regardless of an uploader
    if uploader_edrpou in (PORTMONE_EDRPOU,):
        document_owner_company_id = await select_company_id(conn, document.edrpou_owner)
        if document_owner_company_id:
            return document_owner_company_id
        logger.warning(
            'Unable to find document owner company ID in database',
            extra={'edrpou': document.edrpou_owner},
        )

    configs = await get_companies_configs_by_edrpous(
        conn=conn,
        companies_edrpous=list(filter(None, [recipient_edrpou, uploader_edrpou])),
    )
    default_config = get_default_company_config()
    recipient_config = configs.get((recipient_edrpou or ''), default_config)
    uploader_config = configs.get(uploader_edrpou, default_config)

    to_pay_by_recipient = recipient_config.allow_pay_as_recipient
    recipient_parent = recipient_config.parent_company
    uploader_parent = uploader_config.parent_company
    recipient_parent_pay = recipient_config.allow_parent_company_pay_for_documents
    uploader_parent_pay = uploader_config.allow_parent_company_pay_for_documents

    # Pay by recipient parent company
    if to_pay_by_recipient and recipient_parent and recipient_parent_pay and is_from_api:
        recipient_parent_id = await select_company_id(conn, recipient_parent)
        if recipient_parent_id:
            return recipient_parent_id
        logger.warning(
            'Unable to find recipient parent company ID in database',
            extra={'edrpou': recipient_parent},
        )

    # Pay by uploader parent company
    if uploader_parent and uploader_parent_pay and is_from_api:
        uploader_parent_id = await select_company_id(
            conn, uploader_parent, document.uploaded_by_is_legal
        )
        if uploader_parent_id:
            return uploader_parent_id
        logger.warning(
            'Unable to find uploader parent company ID in database',
            extra={'edrpou': uploader_parent},
        )

    # Pay by recipient
    if to_pay_by_recipient and recipient_edrpou is not None:
        recipient_id = await select_company_id(conn, recipient_edrpou)
        if recipient_id:
            return recipient_id
        logger.warning(
            'Unable to find recipient company ID in database',
            extra={'edrpou': recipient_edrpou},
        )

    return uploader_id


async def validate_retrieve_bill(conn: DBConnection, bill_id: str) -> Bill:
    valid_data = validators.validate_pydantic(BillSchema, {'bill_id': bill_id})
    valid_bill_id = valid_data.bill_id

    bill = await select_bill_by_id(conn, valid_bill_id)
    if not bill:
        raise DoesNotExist(Object.bill, bill_id=valid_bill_id)

    return bill


async def validate_update_bonus(conn: DBConnection, raw_data: DataDict) -> UpdateBonusSchema:
    """Validator to update bonus.

    - Check for bonus existence.
    - Don't allow to update bonus with type ``bonus``.
    - Don't allow to update if new key value is existed.
    """
    data = validators.validate_pydantic(UpdateBonusSchema, raw_data)
    bonus_id = data.bonus_id
    bonus_key = data.key

    bonus = await select_bonus_by_id(conn, bonus_id)
    if not bonus or bonus.date_deleted:
        raise DoesNotExist(Object.bonus, id=bonus_id)

    if bonus.type == BonusType.bonus:
        raise Error(Code.billing_bonus_type_not_allowed, details={'type': bonus.type.value})

    if bonus_key and await exist_bonus_by_key(conn, bonus_key):
        raise AlreadyExists(Object.bonus, key=bonus_key)

    return data


def validate_rate_end_date(end_date: datetime) -> None:
    if end_date.date() < local_now().date():
        raise InvalidRequest(reason=_('Дата завершення тарифу не може бути меншою за сьогодні'))


async def validate_rate_days(
    conn: DBConnection, company_id: str, data: DataDict
) -> tuple[datetime, datetime]:
    if not data['extend_active_rate']:
        if not data['start_date'] or not data['end_date']:
            raise InvalidRequest(
                reason=_('Вкажіть дати активації тарифу або оберіть подовження поточного.'),
            )
        return data['start_date'], data['end_date']

    start_date, end_date = await calculate_extend_rate_dates(
        conn=conn,
        company_id=company_id,
        data=data,
    )

    return start_date, end_date


async def validate_add_company_rate(conn: DBConnection, user: User, data: DataDict) -> CompanyRate:
    """
    Validator for adding rate for given company

    - Check that company already doesn't have active rate
    """

    from app.profile.validators import validate_company_exists

    company_id: str = data['company_id']
    rate = AccountRate(data['rate'])
    start_date, end_date = await validate_rate_days(
        conn=conn,
        company_id=company_id,
        data=data,
    )

    company = await validate_company_exists(conn, company_id=company_id)

    # Validate amount based on rate type
    if rate in AccountRate.trials():
        trial_validated = validators.validate_pydantic(AmountTrialRateSchema, data)
        data['amount'] = trial_validated.amount
    else:
        regular_validated = validators.validate_pydantic(AmountRateSchema, data)
        data['amount'] = regular_validated.amount

    validate_rate_end_date(end_date)
    await _validate_rate_dates_overlap(
        conn=conn,
        company_id=company_id,
        account_rate=rate,
        start_date=start_date,
        end_date=end_date,
    )

    status = _prepare_rate_status(start_date)

    return CompanyRate(
        id_=str(uuid.uuid4()),
        company_id=company_id,
        company_edrpou=company.edrpou,
        rate=rate,
        status=status,
        amount=data['amount'],
        start_date=start_date,
        end_date=end_date,
        initiator_id=user.role_id,
        employee_amount=data['employee_amount'],
    )


async def validate_update_company_rate(
    conn: DBConnection, request: web.Request
) -> UpdateCompanyRateCtx:
    """
    Validate ability to update company rate. Note that possible to update to update
    only amount and range dates
    """
    raw_data = await validators.validate_json_request(request)
    raw_data['rate_id'] = request.match_info['rate_id']
    data = validators.validate_pydantic(UpdateRateSchema, raw_data)
    rate_id = data.rate_id

    rate = await _validate_rate_exists(conn, rate_id)

    start_date = data.start_date
    end_date = data.end_date

    if end_date:
        validate_rate_end_date(end_date)
    # On dates update validate date overlap and prepare new status
    if start_date or end_date:
        await _validate_rate_dates_overlap(
            conn=conn,
            company_id=rate.company_id,
            account_rate=rate.rate,
            start_date=start_date or rate.start_date,
            end_date=end_date or rate.end_date,
            ignore_rate_id=rate.id_,
        )

    status = _prepare_rate_status(start_date) if start_date else rate.status
    return UpdateCompanyRateCtx(
        rate=rate,
        new_status=status,
        start_date=start_date,
        end_date=end_date,
    )


async def validate_delete_company_rate(conn: DBConnection, request: web.Request) -> CompanyRate:
    raw_data = {'rate_id': request.match_info['rate_id']}
    data = validators.validate_pydantic(DeleteRateValidator, raw_data)
    return await _validate_rate_exists(conn, data.rate_id)


async def validate_company_max_employees_count(conn: DBConnection, edrpou: str) -> int:
    company_config = await get_billing_company_config(conn, company_edrpou=edrpou)

    if not company_config.max_employees_count:
        raise InvalidRequest(
            obj=Object.company_rate,
            reason=_('В компанії відсутній ліміт користувачів'),
            details={'company_edrpou': edrpou},
        )

    return company_config.max_employees_count


async def validate_active_ultimate_rate(
    conn: DBConnection, edrpou: str | None = None, account_id: str | None = None
) -> CompanyRate:
    """
    Return billing account (rate) for company or raise DoesNotExist exception
    """
    rate = await select_active_ultimate_rate(conn, edrpou=edrpou, account_id=account_id)

    if not rate:
        if edrpou:
            details = {
                'reason': _('У компанії немає активного Максимального тарифу'),
                'edrpou': edrpou,
            }
        else:
            details = {
                'reason': _(
                    'Тариф "Максимальний" для якого сформовано рахунок '
                    'на поповнення користувачів не активний'
                ),
                'account_id': account_id,
            }

        raise DoesNotExist(
            obj=Object.company_rate,
            **details,
        )

    return rate


async def validate_activate_employee_extension(
    conn: DBConnection, rate_extension: RateExtension, bill: Bill
) -> ActivateEmployeeExtensionCtx:
    if rate_extension.status == RateExtensionStatus.active:
        raise InvalidRequest(
            reason=_('Розширення вже активовано'),
        )
    company_rate = await validate_active_ultimate_rate(conn, account_id=rate_extension.account_id)
    old_max_employees_count = await validate_company_max_employees_count(conn, bill.edrpou)
    return ActivateEmployeeExtensionCtx(
        company_rate=company_rate,
        old_max_employees_count=old_max_employees_count,
    )


def validate_employee_max_limit(new_max_employees_limit: int) -> None:
    if new_max_employees_limit > MAX_EMPLOYEE_LIMIT:
        raise InvalidRequest(
            reason=_('Неможливо додати більше 10 000 співробітників у компанію'),
        )


async def validate_cancel_bill(conn: DBConnection, bill_id: str) -> Bill:
    bill = await validate_bill_exists_by_id(conn, bill_id)

    if bill.payment_status in (BillPaymentStatus.canceled, BillPaymentStatus.refund):
        raise InvalidRequest(reason=_('Bill is already canceled or refunded'))

    return bill
