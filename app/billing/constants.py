import dataclasses
import datetime
from typing import Self

from app.billing.enums import (
    AccountRate,
    AccountType,
    BillPaymentStatus,
    CompanyLimit,
    CompanyPermission,
)
from app.i18n import _
from app.lib.datetime_utils import to_local_datetime, to_utc_datetime
from app.lib.helpers import immutable_dict
from app.lib.types import DataDict

ACCOUNT_TYPES_REDIS_KEY_MAPPING = immutable_dict(
    {
        AccountType.service_credit_external.value: 'billing_service_credit_external_account_id',
        AccountType.service_credit_bonus.value: 'billing_service_credit_bonus_account_id',
        AccountType.service_debit_external.value: 'billing_service_debit_external_account_id',
        AccountType.service_debit_bonus.value: 'billing_service_debit_bonus_account_id',
        AccountType.service_debt.value: 'billing_service_debt_account_id',
    }
)

RESTRICTED_EDRPOUS_TO_PROCESS_PAYMENT_STATUS = {
    '********',
    '********',
    '********',
    '********',
    '********',
}

DAYS_ALLOWANCE_TO_EXTEND_RATE = 45

RATE_EXTENSION_TRIAL_DAYS = 5

MAX_EMPLOYEE_LIMIT = 10_000

STANDARD_PRICE_PER_DOCUMENT = 90

UPLOAD_RATE = 5

OCTOBER_2024 = to_utc_datetime(datetime.datetime(day=1, month=10, year=2024))

NOVEMBER_20 = to_utc_datetime(datetime.datetime(day=20, month=11, year=2024))

APRIL_1_2025 = to_local_datetime(datetime.datetime(day=1, month=4, year=2025))

APRIL_2_2025 = to_local_datetime(datetime.datetime(day=2, month=4, year=2025))

CRM_COMPANY_CREATOR_FAKE_EMAIL = '<EMAIL>'

PUMB_IBAN = '*****************************'
PUMB_BANK = 'АТ "ПУМБ"'


@dataclasses.dataclass(frozen=True)
class BillRequisites:
    address: str
    city: str
    edrpou: str
    ipn: str
    mfo: str
    name: str
    short_name: str
    zip: str
    iban: str
    bank: str

    def to_dict(self) -> DataDict:
        return dataclasses.asdict(self)

    @classmethod
    def to_vchasno(cls) -> Self:
        return cls(
            address='вул. Харківське шосе, буд. 201-203, корпус 1А, літ. Ф, кім. 604',
            city='м. Київ',
            edrpou='********',
            ipn='************',
            mfo='380805',
            name='Товариство з обмеженою відповідальністю “ВЧАСНО СЕРВІС”',
            short_name='ТОВ “ВЧАСНО СЕРВІС”',
            zip='02121',
            iban=PUMB_IBAN,
            bank=PUMB_BANK,
        )


BILL_REQUISITES = BillRequisites.to_vchasno()

ROLE_COUNT_FREE_PRO = 5


EXTENSION_ADD_EMPLOYEE_SERVICE_NAME = 'Поповнення користувачів (тариф Максимальний)'

RATES_NAME_MAP = {
    AccountRate.old: 'Застарілий',
    AccountRate.enterprise: 'Корпоративний',
    AccountRate.free: 'Базовий',
    AccountRate.start: 'Старт',
    AccountRate.start_2022_01: 'Старт',
    AccountRate.start_2022_04: 'Старт',
    AccountRate.start_2022_08: 'Старт',
    AccountRate.start_2023_10: 'Старт',
    AccountRate.start_2024_10: 'Старт',
    AccountRate.start_2025_04: 'Старт',
    AccountRate.pro_2021: 'Професійний',
    AccountRate.pro_2022: 'Професійний',
    AccountRate.pro_2022_01: 'Професійний',
    AccountRate.pro_2022_04: 'Професійний',
    AccountRate.pro_2022_12: 'Професійний',
    AccountRate.pro_2023_07: 'Професійний',
    AccountRate.pro_2023_10: 'Професійний',
    AccountRate.pro_2024_10: 'Професійний',
    AccountRate.pro_2025_04: 'Професійний',
    AccountRate.pro_plus: 'ПРО+',
    AccountRate.pro_plus_2022_04: 'ПРО+',
    AccountRate.pro_plus_2022_12: 'ПРО+',
    AccountRate.integration: 'Інтеграція',
    AccountRate.ultimate: 'Максимальний',
    AccountRate.ultimate_2022_12: 'Максимальний',
    AccountRate.pro: 'Безлімітний',
    AccountRate.pro_free: 'Професійний (безкоштовний)',
    AccountRate.pro_trial: 'Професійний (пробний)',
    AccountRate.pro_plus_trial_2022: 'Професійний (пробний)',
    AccountRate.pro_plus_trial_2022_12: 'Професійний (пробний)',
    AccountRate.pro_plus_trial: 'Професійний (пробний)',
    AccountRate.integration_trial: 'Інтеграція (пробний)',
    AccountRate.archive_small: 'Архів +1000',
    AccountRate.archive_big: 'Архів +5000',
}

# Do not use directly, use get_rate_price() instead
# Ціна за тариф в гривнях за рік. Приклад:
#   AccountRate.start_2023_10: 1800  # = 1800 грн/рік
# - RATES_PRICE_MAP → price that doesn't depend on the type of the company
# - RATES_PRICE_MAP_FOP → price for FOP ("ФОП")
# - RATES_PRICE_MAP_TOV → price for TOV ("ТОВ" або "Юридична особа")
RATES_PRICE_MAP = {
    #  Deprecated {{
    AccountRate.start: 1500,
    AccountRate.start_2022_01: 1500,
    AccountRate.start_2022_04: 1500,
    AccountRate.start_2022_08: 1500,
    AccountRate.start_2023_10: 1800,
    AccountRate.pro: 6000,
    AccountRate.pro_2021: 6000,
    AccountRate.pro_plus: 19200,
    AccountRate.pro_plus_trial_2022: 0,
    AccountRate.integration_trial: 0,
    AccountRate.pro_2022: 6000,
    AccountRate.pro_2022_04: 6000,
    AccountRate.pro_2022_12: 6000,
    AccountRate.pro_2023_07: 6000,
    AccountRate.pro_2023_10: 7200,
    #  }}
    #  Actual {{
    AccountRate.pro_plus_2022_12: 19200,
    AccountRate.archive_small: 480,
    AccountRate.archive_big: 1440,
    #  }}
}
RATES_PRICE_MAP_FOP = {
    AccountRate.integration: 1200,
    AccountRate.start_2025_04: 2400,
    AccountRate.pro_2025_04: 4800,
    #  Deprecated
    AccountRate.start_2024_10: 1800,
    AccountRate.pro_2024_10: 4800,
}
RATES_PRICE_MAP_TOV = {
    AccountRate.integration: 7200,
    AccountRate.start_2025_04: 2400,
    AccountRate.pro_2025_04: 9600,
    #  Deprecated
    AccountRate.pro_2024_10: 9600,
    AccountRate.start_2024_10: 2400,
}

DEPRECATED_RATES = {
    AccountRate.start,
    AccountRate.start_2022_01,
    AccountRate.start_2022_04,
    AccountRate.start_2022_08,
    AccountRate.start_2023_10,
    AccountRate.start_2024_10,
    AccountRate.pro,
    AccountRate.pro_2021,
    AccountRate.pro_plus,
    AccountRate.pro_plus_trial_2022,
    AccountRate.integration_trial,
    AccountRate.pro_2022,
    AccountRate.pro_2022_04,
    AccountRate.pro_2022_12,
    AccountRate.pro_2023_07,
    AccountRate.pro_2023_10,
    AccountRate.pro_2024_10,
}

# set for rates, which we changed
FEATURE_FLAG_CHANGED_RATES: set[AccountRate] = set()

LIMIT_NAMES = {
    CompanyLimit.additional_fields: 'додаткових параметрів',
    CompanyLimit.tags: 'ярликів',
    CompanyLimit.automation: 'сценаріїв',
    CompanyLimit.required_fields: "обов'язкових полів",
}

INTEGRATION_PRICE_PER_DOCUMENT = 7.2  # в гривнях

CURRENCY_LIST = ('UAH',)

EVOPAY_STATUS_TO_BILLING_MAP = {
    #  Success statuses
    'success': BillPaymentStatus.completed,
    #  Transition statuses
    'init': BillPaymentStatus.requested,
    'pending': BillPaymentStatus.pin_requested,
    #  Failure statuses
    'failure': BillPaymentStatus.rejected,
}

# API docs:
# https://cdn.rozetkapay.com/public-docs/index.html#tag/payments/operation/createPayment
# Status codes description:
# https://docs.google.com/spreadsheets/d/1bdF6Lugx091yTUbCmX_X6YtfGFG6a-vgaXGV5Ttxfs8
EVOPAY_CODE_TO_STATUS_CODE_MAP = {
    'transaction_successful': 1000,
    'reverse_successful': 1003,
    'subscription_successful': 1005,
    'unsubscribed_successfully': 1006,
    'pending': 2000,
    'wrong_pin': 2003,
    'wrong_amount': 2004,
    'wrong_authorization_code': 2005,
    'wrong_cavv': 2006,
    'wrong_cvv': 2007,
    'internal_error': 2008,
    'wrong_account_number': 2009,
    'capture_required': 2010,
    'anti_fraud_check': 2012,
    '3ds_required': 2100,
    'cvv_is_required': 2101,
    'otp_confirmation_required': 2102,
    'receiver_info_required': 2103,
    'sender_info_required': 2104,
    'missed_payout_method_data': 2105,
    'waiting_for_verification': 2106,
    'waiting_for_complete': 2114,
    'transaction_created': 2117,
    'authorization_required': 2120,
    'card_verification_required': 2121,
    'waiting_for_redirect': 2123,
    'waiting_for_clarification': 2201,
    'invalid_data': 4000,
    'card_expired': 4001,
    'incorrect_refund_sum_or_currency': 4002,
    'payment_card_has_invalid_status': 4003,
    'wrong_card_number': 4008,
    'insufficient_funds': 4009,
    'transaction_limit_exceeded': 4010,
    'pin_tries_exceeded': 4018,
    'payment_card_has_constraints': 4020,
    'user_not_found': 4100,
    'failed_to_send_sms': 4101,
    'wrong_sms_password': 4102,
    'card_not_found': 4103,
    'payment_system_not_supported': 4104,
    'invalid_card_type': 4105,
    'country_not_supported': 4106,
    'transaction_amount_limit': 4107,
    'no_discount_found': 4111,
    'failed_to_load_the_wallet': 4112,
    'invalid_verification_code': 4113,
    'additional_information_is_pending': 4114,
    'split_amount_is_not_equal_to_transaction_amount': 4115,
    'transaction_is_not_recurring': 4116,
    'invalid_currency': 4117,
    'capture_amount_cannot_be_more_than_the_transaction_amount': 4118,
    'parameter_is_empty': 4120,
    'invalid_phone_number': 4121,
    'invalid_card_number': 4126,
    'card_bin_not_found': 4127,
    'currency_rate_not_found': 4128,
    'invalid_recipient_name': 4129,
    'daily_card_usage_limit_reached': 4130,
    'transaction_for_this_country_are_forbidden': 4132,
    'card_not_supported': 4135,
    'invalid_transaction_amount': 4139,
    'transaction_declined': 4140,
    'otp_confirmation_timeout': 4141,
    'invalid_card_data': 4144,
    'confirmation_timeout': 4145,
    'timeout': 4148,
    'session_expired': 4149,
    'invalid_operation': 4150,
    'invalid_input_fields': 4151,
    'invalid_configuration': 4152,
    'transaction_rejected': 4153,
    'authorization_error': 4157,
    'failed_to_create_transaction': 4160,
    'operation_is_prohibited': 5001,
    'transaction_not_supported': 5004,
    'this_card_type_is_not_supported': 5022,
    'transaction_cache_data_timeout': 5102,
    'store_is_blocked': 5103,
    'store_is_not_active': 5104,
    'wrong_request_signature': 5105,
    'order_id_is_empty': 5106,
    'you_are_not_the_agent_of_the_specified_store': 5107,
    'invalid_request_url': 5109,
    'transaction_cannot_be_processed': 5110,
    'invalid_transaction_status': 5112,
    'public_key_is_not_found': 5113,
    'transaction_not_found': 5114,
    'access_error': 5115,
    'access_blocked': 5116,
    'terminal_not_found': 5117,
    'fee_not_found': 5118,
    'failed_to_verify_a_card': 5120,
    'currency_is_prohibited': 5121,
    'failed_to_finish_the_transaction': 5122,
    'failed_to_finish_transaction': 5122,
    'invalid_transaction_type': 5124,
    'invalid_signature': 5126,
    'action_parameter_is_not_sent_in_request': 5127,
    'callback_parameter_is_not_transferred': 5128,
    'restricted_ip': 5129,
    'card_does_not_support_3ds': 5130,
    'general_error': 5131,
    'invalid_token': 5132,
    'received_token_is_inactive': 5133,
    'token_reached_the_maximum_purchase_amount': 5134,
    'token_transactions_limit_exceeded': 5135,
    'merchant_is_not_allowed_preauth': 5137,
    '3ds_not_supported': 5138,
    'this_token_does_not_exist': 5139,
    'reached_the_limit_of_attempts_for_this_ip': 5140,
    'card_branch_is_blocked': 5142,
    'card_branch_daily_limit_reached': 5143,
    'temporarily_closed_the_p2p_transactions_from_pb_cards_to_foreign_banks_cards': 5144,
    'completion_limit_reached': 5145,
    'merchant_is_not_allowed_for_making_recurring_transactions': 5150,
    'transaction_is_canceled_by_payer': 5151,
    'payment_was_refunded': 5154,
    'acs_service_unavailable': 5156,
    'payment_card_is_lost_or_stolen': 6004,
}

EVOPAY_STATUS_CODE_TO_DESCRIPTION_MAP = {
    1000: _('Транзакція успішна'),
    1001: _('Транзакція успішна'),
    1002: _('Сума успішно заморожена'),
    1003: _('Кошти зарезервовані для проведення повернення'),
    1004: _('Операція повернення успішна'),
    1005: _('Підписку успішно оформлено'),
    1006: _('Підписку успішно деактивовано'),
    1007: _('Сума успішно заморожена на рахунку відправника'),
    1008: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    1009: _('Операція повернення успішна'),
    2000: _('Обробка операції'),
    2001: _('Обробка операції'),
    2002: _('Обробка операції'),
    2003: _('Неправильний PIN. Будь ласка, перевірте і спробуйте ще раз'),
    2004: _('Некоректна сума зарахування. Будь ласка, перевірте і спробуйте ще раз'),
    2005: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    2006: _('Дані карти вказані невірно. Будь ласка, спробуйте ще раз'),
    2007: _('Невірний код CVV (вказано на зворотній стороні вашої картки)'),
    2008: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    2009: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    2010: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    2012: _('Транзакція перебуває на перевірці на протидії шахрайству'),
    2100: _('Необхідно підтвердження карти'),
    2101: _('Будь ласка, введіть CVV-код карти'),
    2102: _(
        'Необхідно підтвердження клієнта. Одноразовий пароль відправлений на номер телефону клієнта'
    ),
    2103: _('Будь ласка, введіть дані одержувача'),
    2104: _('Будь ласка, введіть дані відправника'),
    2105: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    2106: _('Очікується підтвердження оплати'),
    2107: _('Чекайте підтвердження через телефонний дзвінок'),
    2108: _('Очікується підтвердження пароля додатка Приват24'),
    2109: _('Введіть номер телефону і спробуйте сплатити ще раз'),
    2110: _('Очікується підтвердження оплати'),
    2111: _('Очікується підтвердження в додатку SENDER'),
    2112: _('Проскануйте QR-коду для завершення транзакції'),
    2113: _('Очікується підтвердження оплати в додатку Privat24 / SENDER'),
    2114: _('Очікується завершення платежу в Приват24'),
    2115: _('Очікується завершення платежу в гаманці MasterPass'),
    2116: _('Очікується оплата готівкою. Будь ласка, скористайтеся терміналом самообслуговування'),
    2117: _('Очікується оплата'),
    2118: _('Очікується оплата'),
    2119: _('Платіж в обробці'),
    2120: _('Необхідно підтвердження карти'),
    2121: _('Необхідна перевірка даних карти'),
    2122: _('Будь-ласка, введіть код підтвердження з повідомлення від вашого банку'),
    2123: _('Успішне підтвердження операції'),
    2124: _('Помилка підтвердження оплати. Будь ласка, спробуйте сплатити ще раз'),
    2125: _('Перевищено число спроб введення PIN - коду. Будь ласка, повторіть пізніше'),
    2201: _('Очікування уточнень'),
    4000: _("Будь ласка, заповніть обов'язкові поля і спробуйте ще раз оплати"),
    4001: _('Закінчився термін дії картки.'),
    4002: _('Неправильна сума оплати або валюта. Будь ласка, перевірте і спробуйте ще раз'),
    4003: _('Проблеми з карткою. Перевірте стан картки у банку.'),
    4004: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4005: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4006: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4007: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4008: _(
        'Помилка при введенні номера карти. Будь ласка, перевірте номер карти і спробуйте ще раз'
    ),
    4009: _('На вашій карті недостатньо коштів'),
    4010: _(
        """),
        Упс. Щось пішло не так. Перевірте налаштування вашої картки або зв'яжіться з вашим банком.
        Можливо, по вашій карті:
        - перевищено ліміт на оплати в інтернеті
        - перевищено кредитний ліміт
        - закінчився строк дії
        - інша проблема
        """
    ),
    4011: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4012: _('Перевищено ліміт на оплати карткою. Скористайтесь іншою карткою.'),
    4013: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4014: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4015: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4016: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4017: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4018: _('Перевищено число спроб введення PIN - коду. Будь ласка, повторіть пізніше'),
    4019: _('Ваша карта прострочена. Будь ласка, скористайтеся іншою картою для оплати'),
    4020: _('У картки є обмеження. Скористайтесь іншою карткою.'),
    4021: _('Перевищена кількість невірно веденого ПІН коду.'),
    4100: _('Користувача даної картки не знайдено'),
    4101: _('Не вдалося відправити смс. Будь ласка, спробуйте ще раз'),
    4102: _('Пароль з смс вказано невірно. Будь ласка, перевірте і спробуйте ще раз'),
    4103: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4104: _('Платіжна система картки не підтримується. Будь ласка, скористайтеся іншою картою'),
    4105: _('Тип карти не підтримується. Будь ласка, скористайтеся іншою картою'),
    4106: _('Будь ласка, вкажіть іншу карту'),
    4107: _('Сума не відповідає доступному ліміту'),
    4108: _('Сума не відповідає доступному ліміту'),
    4109: _(
        'Досягнуто ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    4110: _('Помилка. Вкажіть іншу карту відправника'),
    4111: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4112: _('Не вдалося завершити операцію, нам дуже шкода. Будь ласка, спробуйте ще раз оплати'),
    4113: _('Невірний код підтвердження. Будь ласка, перевірте і спробуйте ще раз'),
    4114: _('Очікується додаткова інформація, спробуйте пізніше'),
    4115: _(
        'Суми розділення платежу не збігаються із загальною сумою. Будь ласка, перевірте значення'
    ),
    4116: _('Платіж не є регулярним'),
    4117: _('Оплата можлива тільки в гривні. Будь ласка, скористайтеся гривневою карткою'),
    4118: _('Помилка. Сума списання не може бути більше суми платежу'),
    4119: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4120: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4121: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4122: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4123: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4124: _('Оплата можлива тільки в гривні. Будь ласка, скористайтеся гривневою карткою'),
    4125: _('Невірний номер телефону. Перевірте його ще раз'),
    4126: _(
        'Помилка при введенні номера карти. Будь ласка, перевірте номер карти і спробуйте ще раз'
    ),
    4127: _(
        'Помилка при введенні номера карти. Будь ласка, перевірте номер карти і спробуйте ще раз'
    ),
    4128: _('Оплата можлива тільки в гривні. Будь ласка, скористайтеся гривневою карткою'),
    4129: _("Неправильне ім'я одержувача. Будь ласка, перевірте і повторіть оплату"),
    4130: _(
        'Досягнуто денний ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    4131: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4132: _('На жаль, оплати заблоковані для даної країни'),
    4133: _('Ваша карта прострочена. Будь ласка, скористайтеся іншою картою для оплати'),
    4134: _(
        'Помилка при введенні номера карти. Будь ласка, перевірте номер карти і спробуйте ще раз'
    ),
    4135: _(
        'Ваша карта не підтримує такий вид транзакції. '
        'Будь ласка, скористайтеся іншою картою для оплати'
    ),
    4136: _(
        'Ваша карта не підтримує такий вид транзакції. '
        'Будь ласка, скористайтеся іншою картою для оплати'
    ),
    4137: _('На картці недостатньо коштів. Будь ласка, скористайтеся іншою картою'),
    4138: _(
        'Досягнуто ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    4139: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    4140: _('Оплата скасована. Будь-ласка перевірте дані карти.'),
    4141: _('Закінчився час підтвердження оплати. Будь ласка, спробуйте ще раз оплати'),
    4142: _(
        'Досягнуто ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    4143: _(
        'Досягнуто ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    4144: _('Дані карти вказані невірно. Будь ласка, спробуйте ще раз'),
    4145: _('Закінчився час підтвердження оплати. Будь ласка, спробуйте ще раз оплати'),
    4146: _('Закінчився час підтвердження оплати. Будь ласка, спробуйте ще раз оплати'),
    4147: _('Закінчився час підтвердження оплати. Будь ласка, спробуйте ще раз оплати'),
    4148: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4149: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4150: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4151: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    4152: _('Операцію відхилено. Будь ласка, спробуйте сплатити ще раз'),
    4153: _('Операцію відхилено. Будь ласка, спробуйте сплатити ще раз'),
    4154: _("Операцію відхилено. Будь-ласка зв'яжіться з службою підтримки"),
    4155: _("Операцію відхилено. Будь-ласка зв'яжіться з службою підтримки"),
    4156: _('Операцію відхилено. Будь-ласка спробуйте оплату іншою карткою.'),
    4157: _('Операцію відхилено. Будь-ласка зверніться до свого банку.'),
    4158: _('Операцію відхилено. Будь-ласка зверніться до свого банку.'),
    4159: _('Закінчився час підтвердження оплати. Будь ласка, спробуйте ще раз оплати'),
    4160: _('Операцію відхилено. Будь ласка, спробуйте сплатити ще раз'),
    5000: _('Не вдалося завершити операцію, нам дуже шкода. Будь ласка, спробуйте ще раз оплати'),
    5001: _('Оплата заборонена. Будь-ласка зверніться до банку.'),
    5002: _("Платіж відхилений. Будь ласка, зв'яжіться зі службою підтримки"),
    5003: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5004: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5005: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5009: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5010: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5014: _('Помилка. Будь ласка, зверніться в технічну підтримку'),
    5015: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5019: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5020: _('Не вдалося завершити операцію, нам дуже шкода. Будь ласка, спробуйте ще раз оплати'),
    5021: _('Помилка при оплаті. Будь-ласка зверніться до банку.'),
    5022: _('Тип карти не підтримується. Будь ласка, скористайтеся іншою картою'),
    5023: _('Закінчився час очікування. Будь ласка, спробуйте сплатити ще раз'),
    5024: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5025: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5026: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5027: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5028: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5029: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5030: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5102: _('Закінчився час очікування. Будь ласка, спробуйте сплатити ще раз'),
    5103: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5104: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5105: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5106: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5107: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5108: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5109: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5110: _('Платіж не може бути проведений в цьому магазині'),
    5111: _('У одержувача не встановлена карта для прийому платежів'),
    5112: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5113: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5114: _('Платіж не знайдений, спробуйте ще раз'),
    5115: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5116: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5117: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5118: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5119: _('Не вдалося завершити операцію. Будь ласка, спробуйте ще раз'),
    5120: _('Не вдалося завершити операцію. Будь ласка, спробуйте ще раз'),
    5121: _('Оплата можлива тільки в гривні. Будь ласка, скористайтеся гривневою карткою'),
    5122: _('Не вдалося завершити операцію, нам дуже шкода. Будь ласка, спробуйте ще раз оплати'),
    5123: _('Не вдалося завершити операцію, нам дуже шкода. Будь ласка, спробуйте ще раз оплати'),
    5124: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5125: _('Оплата можлива тільки в гривні. Будь ласка, скористайтеся гривневою карткою'),
    5126: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5127: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5128: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5129: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5130: _('Ваша карта не підтримує 3DSecure. Будь ласка, скористайтеся іншою картою для оплати'),
    5131: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5132: _('Помилка. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5133: _('Ваша карта прострочена. Будь ласка, скористайтеся іншою картою для оплати'),
    5134: _(
        'Досягнуто ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    5135: _(
        'Досягнуто ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    5136: _('Ваша картка не підтримується. Будь ласка, скористайтеся іншою картою для оплати'),
    5137: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5138: _('Банк-еквайєр не підтримує 3D Secure. Спробуйте скористатися іншою картою'),
    5139: _('Помилка. Будь ласка, перевірте дані карти'),
    5140: _('Перевищено кількість спроб. Будь ласка, повторіть пізніше'),
    5141: _('Закінчився час очікування. Будь ласка, спробуйте ще раз оплати'),
    5142: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5143: _(
        'Досягнуто денний ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    5144: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5145: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5146: _('Платіж відхилений. Будь ласка, спробуйте пізніше'),
    5147: _('Оплата скасована. Банк не дозволив оплату. Будь-ласка зверніться до банку.'),
    5148: _('Банк не підтвердив операцію. Будь ласка, зверніться в банк для отримання подробиць'),
    5149: _('Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз'),
    5150: _('Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми'),
    5151: _('Платіж скасований платником'),
    5152: _('Будь-ласка, введіть код підтвердження з повідомлення від вашого банку'),
    5153: _('Помилка підтвердження оплати. Будь ласка, спробуйте сплатити ще раз'),
    5154: _("Операцію відхилено. Будь-ласка зв'яжіться з службою підтримки"),
    5156: _('Будь-ласка, введіть код підтвердження з повідомлення від вашого банку'),
    6000: _('Необхідна перевірка платежу. Будь ласка, очікуйте'),
    6001: _(
        'Досягнуто ліміт оплат по карті. '
        'Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку'
    ),
    6002: _('Банк відхилив операцію. Будь ласка, зверніться в банк для отримання подробиць'),
    6003: _('Банк отклонил операцию. Пожалуйста, обратитесь в банк для получения подробностей'),
    6004: _(
        'Ваша карта числиться як загублена або вкрадена. '
        'Будь ласка, зверніться до свого банку для отримання подробиць'
    ),
    6005: _('Оплата була відхилена. Будь ласка, скористайтесь іншою картою'),
}
UNLIMITED: dict[CompanyLimit, int | None] = {
    CompanyLimit.additional_fields: None,
    CompanyLimit.employees: None,
    CompanyLimit.documents: None,
    CompanyLimit.tags: None,
    CompanyLimit.automation: None,
    CompanyLimit.required_fields: None,
    CompanyLimit.max_versions_count: None,
    CompanyLimit.max_archive_documents_count: None,
}
ALL_PERMITTED: dict[CompanyPermission, bool] = {
    CompanyPermission.enforce_2fa: True,
    CompanyPermission.external_comments: True,
    CompanyPermission.internal_comments: True,
    CompanyPermission.internal_documents: True,
    CompanyPermission.manage_employee_access: True,
    CompanyPermission.reviews: True,
}
