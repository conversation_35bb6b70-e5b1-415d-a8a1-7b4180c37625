from __future__ import annotations

import base64
import hashlib
import json
import logging
from dataclasses import dataclass
from urllib.parse import urljoin

from aiohttp import ClientError

from api.errors import (
    AccessDenied,
    Code,
    DoesNotExist,
    EvoPayRequestException,
    InvalidRequest,
    Object,
    ServerError,
    Timeout,
)
from app.i18n import _
from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)


ENCODING = 'UTF-8'
EVOPAY_TRANSACTION_ALREADY_PAID_STATUS_CODE = 'transaction_already_paid'
EVOPAY_RESULT_URL = '/app/settings/companies/{role_id}/rates?bill_id={bill_id}'
EVOPAY_WEBHOOK_URL = '/api/evopay/webhook'
EVOPAY_REQUEST_URL = '/api/payments/v1/new'


@dataclass(frozen=True)
class EvopayCheckoutOrder:
    bill_id: str
    role_id: str
    price: int
    currency: str
    result_url: str
    webhook_url: str
    description: str


class EvopayClient:
    def __init__(self) -> None:
        config = services.config.evopay
        if not config:
            raise DoesNotExist(
                Object.config,
                reason=_('Конфіг для оплати карткою не знайдено.'),
            )

        self.mode = config.mode
        self.base_url = config.host
        self.api_key = config.api_key
        self.api_secret = config.api_secret
        self.client = services.http_client
        self.credentials = self._encode_credentials()

    def _encode_credentials(self) -> str:
        """
        Returns urlsafe base64 encoded api_key and api_secret joined with a single colon ":"
        """
        credentials = f'{self.api_key}:{self.api_secret}'.encode(ENCODING)
        return base64.urlsafe_b64encode(credentials).decode(ENCODING)

    async def _request(self, url: str, data: bytes) -> tuple[int, DataDict]:
        response = None
        try:
            async with self.client.post(
                url=url,
                data=data,
                headers={
                    'Authorization': f'Basic {self.credentials}',
                    'Content-Type': 'application/json',
                },
            ) as response:
                resp_data = await response.json()
                return response.status, resp_data

        except TimeoutError:
            logger.error(
                'Timeout during send request to EvoPay',
            )
            raise Timeout()

        except ClientError:
            logger.exception(
                'Exception during send request to EvoPay',
                extra={
                    'response_status': response.status if response else None,
                    'response_body': await response.text() if response else None,
                },
            )
            raise EvoPayRequestException(
                code=Code.external_resource_error,
                reason=_('Сталася помилка під час відправлення запиту в EvoPay'),
            )

    def _get_request_data(self, order: EvopayCheckoutOrder) -> DataDict:
        """Builds EvoPay request"""
        return {
            'external_id': order.bill_id,
            'description': order.description,
            'amount': str(order.price / 100),
            'currency': order.currency,
            'result_url': order.result_url,
            'callback_url': order.webhook_url,
            'mode': self.mode,
            'customer': {
                'external_id': order.role_id,
            },
        }

    async def initiate_payment_process(self, order: EvopayCheckoutOrder) -> str:
        """Returns order object and initiates payment process"""

        request_data = self._get_request_data(order)
        data_raw = json.dumps(request_data, separators=(',', ':'))

        response_status, response_data = await self._request(
            url=urljoin(self.base_url, EVOPAY_REQUEST_URL),
            data=data_raw.encode(ENCODING),
        )

        if response_status != 200:
            if response_data.get('code') == EVOPAY_TRANSACTION_ALREADY_PAID_STATUS_CODE:
                raise InvalidRequest(
                    obj=Object.bill,
                    reason=_('По цьому рахунку вже була проведена успішна оплата.'),
                )

            logger.warning(
                'Failed to initiate payment process',
                extra={
                    'response_data': str(response_data),
                    'response_status': response_status,
                },
            )
            raise EvoPayRequestException(
                code=Code.external_resource_error,
                reason=_('Сталася помилка під час відправлення запиту в EvoPay'),
            )

        action = response_data.get('action') or {}
        if action.get('type') != 'url' or not action.get('value'):
            logger.error(
                'Missing action url in EvoPay response',
                extra={
                    'response_data': str(response_data),
                    'response_status': response_status,
                },
            )
            raise ServerError(
                code=Code.unhandled_error,
                reason=_('У відповіді від сервісу EvoPay відсутній url'),
            )

        return response_data['action']['value']

    @staticmethod
    def generate_evopay_result_url(role_id: str, bill_id: str) -> str:
        """
        This is the URL to which EvoPay will redirect after payment is done
        """
        config = services.config.app
        base_url = config.domain
        return base_url + EVOPAY_RESULT_URL.format(role_id=role_id, bill_id=bill_id)

    @staticmethod
    def generate_evopay_webhook_url() -> str:
        """
        This is the URL to which EvoPay will send webhook request after payment is done
        """
        config = services.config
        base_url = config.app.domain

        # By default, on local environment, webhook will not work because EvoPay will not be able
        # to send a request to your localhost. To make it work, you need to expose your local server
        # to the internet. You can use "ngrok" or "localtunnel" for this purpose and then set
        # "evopay_webhook_domain" in your "config.override.yaml" to the URL provided by
        # "ngrok" or "localtunnel". For example,
        # ```
        # evopay:
        #   evopay_webhook_domain: https://your-url.ngrok.io
        # ```
        if config.evopay and config.evopay.evopay_webhook_domain:
            base_url = config.evopay.evopay_webhook_domain

        return base_url + EVOPAY_WEBHOOK_URL

    def _generate_signature(self, raw_body: bytes) -> str:
        """Generate signature for webhook request"""

        base64_data = base64.urlsafe_b64encode(raw_body).decode(ENCODING)
        signature_data = self.api_secret + base64_data + self.api_secret
        return base64.urlsafe_b64encode(
            hashlib.sha1(signature_data.encode(ENCODING)).digest()
        ).decode(ENCODING)

    def verify_signature(self, signature: str | None, raw_body: bytes) -> None:
        """Verify signature of webhook request"""

        if not signature or signature != self._generate_signature(raw_body):
            raise AccessDenied(reason=_('Неавторизований сервіс.'))
