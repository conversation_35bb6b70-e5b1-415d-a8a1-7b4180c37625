import logging

import sqlalchemy as sa

from api.errors import Code, Error
from api.private.super_admin.db import insert_super_admin_action
from app.actions.enums import SystemAction
from app.auth.db import (
    update_company_upload_documents_left,
)
from app.auth.types import User
from app.billing import utils
from app.billing.constants import STANDARD_PRICE_PER_DOCUMENT, UPLOAD_RATE
from app.billing.db import (
    _batch_update_company_rates,
    _delete_account,
    _insert_account,
    _insert_transaction,
    _update_company_rate,
    select_accounts,
    select_active_company_rates,
    select_company_credit_account,
    select_free_rate,
    update_account_counter,
    update_billing_company_config,
    update_rate_bonuses,
)
from app.billing.enums import (
    COMPANY_PERMISSIONS_OBSOLETE_KEYS_VALUES,
    AccountRate,
    AccountType,
    CompanyLimit,
    CompanyRateStatus,
    ResourceType,
    TransactionType,
)
from app.billing.tables import billing_account_table
from app.billing.types import (
    Account,
    CancelResourcesContext,
    ChargeBonusContext,
    ChargeCreditContext,
    ChargeDebitContext,
    ChargeDocumentContext,
    CompanyRate,
    DeactivateRateConfigUpdated,
    Transaction,
    UpdateBillingCompanyConfigDict,
    UpdateCompanyRateCtx,
)
from app.billing.utils import (
    activate_extra_roles,
    calculate_employee_extension_units,
    deactivate_company_users_extensions,
    deactivate_extra_roles,
    get_amount_to_charge,
    get_billing_company_config,
    get_date_expired_str,
    get_rate_config_units,
    get_rate_end_date_from_start_date,
    get_service_account_id,
)
from app.billing.validators import (
    validate_activate_bonus,
    validate_activate_custom_bonus,
    validate_activate_debit,
    validate_add_account,
    validate_add_transaction,
    validate_cancel_resources,
    validate_delete_account,
)
from app.crm.utils import send_rate_to_crm
from app.lib.database import DBConnection
from app.lib.datetime_utils import local_now, soft_isoformat
from app.lib.enums import (
    SuperAdminActionType,
)
from app.lib.helpers import (
    generate_uuid,
    is_vchasno_company,
)
from app.lib.types import DataDict
from app.services import services
from worker import topics

logger = logging.getLogger(__name__)

SERVICE_CREDIT_TYPE = AccountType.service_credit_external


async def activate_bonus(
    conn: DBConnection,
    data: DataDict,
    check_for_duplication: bool = False,
) -> str | None:
    """Activate bonus from ``bonuses`` table for company.

    When receiving bonus, need to:
    1. Add appropriate account with ``client_bonus`` type.
    2. Add transaction with ``bonus_income`` type.

    For enabled ``check_for_duplication`` flag, need to check for rewarded
    bonus with same key, if this bonus existed then do nothing.
    """
    valid_data = await validate_activate_bonus(conn, data, check_for_duplication)

    if valid_data.is_duplicated:
        return None

    bonus = valid_data.bonus
    account_data = {
        'company_id': valid_data.company_id,
        'initiator_id': bonus.id,
        'bill_id': None,
        'rate': AccountRate.integration.value,
        'type': AccountType.client_bonus.value,
        'amount': 0,
        'amount_left': 0,
        'units': bonus.units,
        'units_left': 0,  # will be filled by transaction
        'date_expired': get_date_expired_str(bonus.period),
    }

    async with conn.begin():
        # Add new bonus account
        account = await _add_account(conn, account_data)

        # Add bonus income transaction
        scb_account_id = await get_service_account_id(conn, AccountType.service_credit_bonus)
        transaction_data = {
            'from_': scb_account_id,
            'to_': account.id,
            'initiator_id': bonus.id,
            'type': TransactionType.bonus_income.value,
            'amount': 0,
            'units': bonus.units,
            'comment': bonus.title,
            'service_account_id': scb_account_id,
        }
        await _add_transaction(conn, transaction_data)

        return account.id


async def activate_custom_bonus(conn: DBConnection, data: DataDict) -> str:
    """Activate custom bonus for company.

    When receiving custom bonus, need to:
    1. Add account with ``client_bonus_custom`` type.
    2. Add transaction with ``bonus_income`` type.
    """
    valid_data = await validate_activate_custom_bonus(conn, data)
    role_id = valid_data.role_id
    units = valid_data.units
    account_data = {
        'company_id': valid_data.company_id,
        'initiator_id': role_id,
        'bill_id': None,
        'rate': AccountRate.integration.value,
        'type': AccountType.client_bonus_custom.value,
        'amount': 0,
        'amount_left': 0,
        'units': units,
        'units_left': 0,  # will be filled by transaction
        'date_expired': get_date_expired_str(valid_data.period),
    }

    async with conn.begin():
        # Add new custom bonus account
        account = await _add_account(conn, account_data)

        # Add bonus income transaction
        scb_account_id = await get_service_account_id(conn, AccountType.service_credit_bonus)
        transaction_data = {
            'from_': scb_account_id,
            'to_': account.id,
            'operator_id': role_id,
            'initiator_id': role_id,
            'type': TransactionType.bonus_income.value,
            'amount': 0,
            'units': units,
            'comment': valid_data.comment,
            'service_account_id': scb_account_id,
        }
        await _add_transaction(conn, transaction_data)

        return account.id


async def activate_debit(conn: DBConnection, data: DataDict) -> str:
    """Activate debit account for company.

    When activating debit account, need to:
    1. Add account with ``client_debit`` type.
    2. Add transaction with ``invoice_payment`` type.
    """
    valid_data = await validate_activate_debit(conn, data)
    bill_id = valid_data.bill_id
    amount = valid_data.amount
    units = valid_data.units
    account_data = {
        'company_id': valid_data.company_id,
        'bill_id': bill_id,
        'rate': AccountRate.integration.value,
        'type': AccountType.client_debit.value,
        'amount': amount,
        'amount_left': 0,  # will be filled by transaction
        'units': units,
        'units_left': 0,  # will be filled by transaction
    }

    async with conn.begin():
        # Add new debit account
        account = await _add_account(conn, account_data)

        # Add invoice payment transaction
        sce_account_id = await get_service_account_id(conn, AccountType.service_credit_external)
        transaction_data = {
            'from_': sce_account_id,
            'to_': account.id,
            'operator_id': valid_data.role_id,
            'initiator_id': valid_data.role_id,
            'type': TransactionType.invoice_payment.value,
            'amount': amount,
            'units': units,
            'comment': valid_data.comment,
            'service_account_id': sce_account_id,
        }
        await _add_transaction(conn, transaction_data)

        return account.id


async def cancel_bonus(conn: DBConnection, data: DataDict) -> None:
    """Cancel given bonuses from company's bonus accounts."""
    valid_data = await validate_cancel_resources(conn, ResourceType.bonus, data)
    context = CancelResourcesContext(
        service_account_id=await get_service_account_id(conn, AccountType.service_credit_bonus),
        transaction_type=TransactionType.bonus_cancel,
        role_id=valid_data.role_id,
        company_id=valid_data.company_id,
        units=valid_data.units,
        comment=valid_data.comment,
        accounts=valid_data.accounts,
    )
    await _cancel_resources(conn, context)


async def cancel_debit(conn: DBConnection, data: DataDict) -> None:
    """Cancel documents from company's debit accounts."""
    valid_data = await validate_cancel_resources(conn, ResourceType.debit, data)
    context = CancelResourcesContext(
        service_account_id=await get_service_account_id(conn, AccountType.service_credit_external),
        transaction_type=TransactionType.invoice_payment_cancel,
        role_id=valid_data.role_id,
        company_id=valid_data.company_id,
        units=valid_data.units,
        comment=valid_data.comment,
        accounts=valid_data.accounts,
    )
    await _cancel_resources(conn, context)


async def charge_document(conn: DBConnection, context: ChargeDocumentContext) -> None:
    """Charge one document from a company.

    Main function to use when need to charge a document from company
    (e.g., during first document sending).

    Charge order:
    1. From active bonus account with the earliest expiration date if any.
    2. From active debit account with oldest create date if any.
    3. From credit account (this logic used only for a Vchasno company).

    This function works in a pair with `validate_charge_document` function which validates and
    prepares context for this function. Here you don't need to validate context again.
    """
    is_from_api = context.is_api
    payer_id = context.payer_id
    api_accounts = [acc for acc in context.accounts if acc.rate.is_integration]
    web_accounts = [acc for acc in context.accounts if acc.rate.is_web]
    document_id = context.document_id
    role_id = context.role_id

    async with conn.begin():
        if is_from_api:
            account = await _charge_document_integration_rate(
                conn=conn,
                accounts=api_accounts,
                document_id=context.document_id,
                payer_id=payer_id,
                role_id=role_id,
            )

            # 14.5 = 15 - (500 documents min possible rate / 100 = 0,5% per doc)
            if account.documents_left_percentage is not None and (
                0 <= account.documents_left_percentage <= 0.5
                or 14.5 <= account.documents_left_percentage <= 15
            ):
                await services.kafka.send_record(
                    topics.ON_DOCUMENT_CHARGE_EVENT,
                    {
                        'charge_type': 'api',
                        'company_id': payer_id,
                        'billing_account_id': account.id,
                    },
                )
            return

        # Charge only from web bonus account
        bonus_accounts = [item for item in web_accounts if item.type.is_bonus]
        if len(bonus_accounts):
            bonus_account = bonus_accounts[0]
            await _charge_from_bonus(
                conn=conn,
                context=ChargeBonusContext(
                    account_id=bonus_account.id,
                    document_id=document_id,
                    role_id=role_id,
                ),
            )
            # 13 = 15 - (50 documents min possible rate / 100 = 2% per doc)
            if bonus_account.documents_left_percentage is not None and (
                0 <= bonus_account.documents_left_percentage <= 2
                or 13 <= bonus_account.documents_left_percentage <= 15
            ):
                await services.kafka.send_record(
                    topics.ON_DOCUMENT_CHARGE_EVENT,
                    {
                        'charge_type': 'web',
                        'company_id': payer_id,
                        'billing_account_id': bonus_account.id,
                    },
                )
            return

        # Charge only from web debit account
        debit_accounts = [item for item in web_accounts if item.type.is_debit]
        if len(debit_accounts):
            debit_account = debit_accounts[0]
            await _charge_from_debit(
                conn=conn,
                context=ChargeDebitContext(
                    account=debit_account,
                    document_id=document_id,
                    role_id=role_id,
                ),
            )

            # 13 = 15 - (50 documents min possible rate / 100 = 2% per doc)
            if debit_account.documents_left_percentage is not None and (
                0 <= debit_account.documents_left_percentage <= 2
                or 13 <= debit_account.documents_left_percentage <= 15
            ):
                await services.kafka.send_record(
                    topics.ON_DOCUMENT_CHARGE_EVENT,
                    {
                        'charge_type': 'web',
                        'company_id': payer_id,
                        'billing_account_id': debit_account.id,
                    },
                )
            return

        raise Error(Code.billing_overlimit)


async def delete_account(conn: DBConnection, data: DataDict) -> None:
    """Delete client account.

    If there are remaining resources in the deleted account, need to make
    transaction from this account to appropriate service account, to move back
    non used resources.

    - From bonus type accounts move resources to ``service_credit_bonus``.
    - From debit type accounts move resources to ``service_credit_external``.
    """
    valid_data = await validate_delete_account(conn, data)
    role_id = valid_data.role_id

    async with conn.begin():
        deleted_account = await _delete_account(conn, valid_data.account_id)

        # Don't make transaction if there are no remaining resources
        if deleted_account is None or not (
            deleted_account.amount_left > 0 or deleted_account.units_left > 0
        ):
            return

        transaction_data = {
            'from_': deleted_account.id,
            'initiator_id': deleted_account.initiator_id,
            'amount': max(deleted_account.amount_left, 0),
            'units': max(deleted_account.units_left, 0),
        }
        if role_id:
            transaction_data['operator_id'] = role_id

        if deleted_account.type.is_bonus:
            scb_account_id = await get_service_account_id(conn, AccountType.service_credit_bonus)
            transaction_data['type'] = TransactionType.bonus_cancel.value
            transaction_data['to_'] = scb_account_id
            transaction_data['service_account_id'] = scb_account_id
        elif deleted_account.type == AccountType.client_debit:
            sce_account_id = await get_service_account_id(conn, AccountType.service_credit_external)
            transaction_data['type'] = TransactionType.invoice_payment_cancel.value
            transaction_data['to_'] = sce_account_id
            transaction_data['service_account_id'] = sce_account_id

        await _add_transaction(conn, transaction_data)


async def _add_account(conn: DBConnection, data: DataDict) -> Account:
    """Add new account to company.

    Also need to update company ``upload_documents_left`` counter.
    """
    valid_data = validate_add_account(data)
    account = await _insert_account(conn, valid_data.model_dump())
    await update_company_upload_documents_left(
        conn, account.company_id, account.units * UPLOAD_RATE
    )
    return account


async def _add_transaction(conn: DBConnection, data: DataDict) -> Transaction:
    """Add new transaction.

    After adding new transaction record, need to charge resources (money,
    documents, etc.) from source account and transfer it to target account.
    """

    # We don't want to update system service_billing_account counters
    # due to high DB load by such queries,
    # so we check for account_id != service_account_id before counters update
    service_account_id = data.pop('service_account_id', None)

    valid_data = validate_add_transaction(data)
    amount = data.get('amount')
    units = data.get('units')

    # Add transaction
    transaction = await _insert_transaction(conn, valid_data.model_dump())

    if service_account_id != data['from_']:
        # Charge from source account
        await update_account_counter(
            conn=conn,
            account_id=data['from_'],
            amount=amount and -amount,
            units=units and -units,
        )

    if service_account_id != data['to_']:
        # Transfer to target account
        await update_account_counter(
            conn=conn,
            account_id=data['to_'],
            amount=amount,
            units=units,
        )

    return transaction


async def _cancel_resources(conn: DBConnection, context: CancelResourcesContext) -> None:
    """Iterate company's accounts and cancel resources making a transaction to
    service account.
    """
    accounts = context.accounts
    role_id = context.role_id

    units_to_cancel_left = context.units
    for account in accounts:
        if units_to_cancel_left <= 0:
            break

        units_left = account.units_left
        units_to_cancel = units_left if units_to_cancel_left > units_left else units_to_cancel_left
        price_per_unit = account.amount / account.units
        amount_to_cancel = units_to_cancel * price_per_unit
        transaction_data = {
            'from_': account.id,
            'to_': context.service_account_id,
            'operator_id': role_id,
            'initiator_id': role_id,
            'type': context.transaction_type.value,
            'amount': int(amount_to_cancel),
            'units': units_to_cancel,
            'comment': context.comment,
            'service_account_id': context.service_account_id,
        }
        await _add_transaction(conn, transaction_data)
        units_to_cancel_left = units_to_cancel_left - account.units_left


async def _charge_from_bonus(conn: DBConnection, context: ChargeBonusContext) -> None:
    """Charge one unit from bonus account and make transaction from bonus
    account to ``service_debit_bonus`` account."""
    sdb_account_id = await get_service_account_id(conn, AccountType.service_debit_bonus)
    transaction_data = {
        'from_': context.account_id,
        'to_': sdb_account_id,
        'operator_id': context.role_id,
        'initiator_id': context.document_id,
        'type': TransactionType.bonus_charge_off.value,
        'amount': 0,
        'units': 1,
        'service_account_id': sdb_account_id,
    }
    await _add_transaction(conn, transaction_data)


async def _charge_from_credit(conn: DBConnection, context: ChargeCreditContext) -> None:
    """Charge one unit and amount equal to standard price per document and
    make transaction from credit account to ``service_debt`` account."""
    sd_account_id = await get_service_account_id(conn, AccountType.service_debt)

    transaction_data = {
        'from_': context.account_id,
        'to_': sd_account_id,
        'operator_id': context.role_id,
        'initiator_id': context.document_id,
        'type': TransactionType.credit_use.value,
        'amount': STANDARD_PRICE_PER_DOCUMENT,
        'units': 1,
        'service_account_id': sd_account_id,
    }
    await _add_transaction(conn, transaction_data)


async def _charge_from_debit(conn: DBConnection, context: ChargeDebitContext) -> None:
    """Charge one unit and appropriate amount from debit account and make
    transaction from debit account to ``service_debit_external`` account."""
    sde_account_id = await get_service_account_id(conn, AccountType.service_debit_external)
    account = context.account
    amount = get_amount_to_charge(
        account.amount, account.amount_left, account.units, account.units_left
    )

    transaction_data = {
        'from_': account.id,
        'to_': sde_account_id,
        'operator_id': context.role_id,
        'initiator_id': context.document_id,
        'type': TransactionType.debit_charge_off.value,
        'amount': amount,
        'units': 1,
        'service_account_id': sde_account_id,
    }
    await _add_transaction(conn, transaction_data)


async def _charge_document_integration_rate(
    conn: DBConnection,
    *,
    payer_id: str,
    document_id: str,
    role_id: str,
    accounts: list[Account],
) -> Account:
    # Charge from bonus account
    bonus_accounts = [item for item in accounts if item.type.is_bonus]
    if len(bonus_accounts):
        bonus_ctx = ChargeBonusContext(
            account_id=bonus_accounts[0].id,
            document_id=document_id,
            role_id=role_id,
        )
        await _charge_from_bonus(conn, bonus_ctx)
        return bonus_accounts[0]

    # Charge from debit account
    debit_accounts = [item for item in accounts if item.type.is_debit]
    if len(debit_accounts):
        debit_ctx = ChargeDebitContext(
            account=debit_accounts[0],
            document_id=document_id,
            role_id=role_id,
        )
        await _charge_from_debit(conn, debit_ctx)
        return debit_accounts[0]

    # Charge from credit account (only for Vchasno company)
    if await is_vchasno_company(conn, payer_id):
        credit_account = await select_company_credit_account(conn, payer_id)
        if credit_account:
            await _charge_from_credit(
                conn,
                ChargeCreditContext(
                    account_id=credit_account.id,
                    document_id=document_id,
                    role_id=role_id,
                ),
            )
            return credit_account

    raise Error(Code.billing_overlimit)


async def _update_config_on_rate_activation(
    conn: DBConnection,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> None:
    """
    Update company config and billing company config on rate activation.
    """
    previous_billing_config = await get_billing_company_config(conn, company_id=rate.company_id)

    other_rates = [r for r in active_rates if r.id_ != rate.id_]
    permissions = utils.get_activation_permissions(rate=rate, active_rates=other_rates)
    limits = utils.get_activation_limits(rate=rate, active_rates=other_rates)

    # Change limits.max_employees_count
    await calculate_employee_extension_units(conn, other_rates, limits)

    config_update: UpdateBillingCompanyConfigDict = {}
    for permission_key, permission_value in permissions.items():
        config_update[permission_key.value] = permission_value

    for limit_key, limit_value in limits.items():
        config_update[limit_key.value] = limit_value

    # Remove obsolete keys from config on rate activation
    obsolete_keys: list[str] = []
    if rate.rate != AccountRate.pro:
        obsolete_keys = COMPANY_PERMISSIONS_OBSOLETE_KEYS_VALUES

    # The Ultimate rate has a different number of employees for each client,
    # and it depends on how many employees the client bought.
    if rate.rate == AccountRate.ultimate:
        config_update[CompanyLimit.employees.value] = rate.employee_amount

    await update_billing_company_config(
        conn=conn,
        company_id=rate.company_id,
        config=config_update,
        remove_keys=obsolete_keys,
    )

    logger.info(
        msg='Updating company config on rate activation',
        extra={
            'company_id': rate.company_id,
            'rate': rate.rate.value,
            'new_billing_config': str(config_update),
            'old_billing_config': str(previous_billing_config.to_update_dict()),
        },
    )


async def activate_rate(
    conn: DBConnection,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> None:
    if rate.status != CompanyRateStatus.active:
        rate_data = {'status': CompanyRateStatus.active, 'date_deleted': None}
        await _update_company_rate(conn, rate.id_, rate_data)

    await activate_rate_bonus_units(conn, rate)

    if not rate.rate.is_integration and not rate.rate.is_free:
        await deactivate_pro_trials(conn, active_rates=active_rates)
        # Get active_rates from database
        active_rates = await select_active_company_rates(conn, rate.company_id)

    await _update_config_on_rate_activation(
        conn=conn,
        rate=rate,
        active_rates=active_rates,
    )
    billing_config = await get_billing_company_config(conn, company_id=rate.company_id)

    await activate_extra_roles(
        conn=conn,
        company_id=rate.company_id,
        billing_config=billing_config,
    )

    if rate.rate.is_trial:
        # This function is usually called deep in transaction, so it might be a case that we've
        # already sent this event, while the transaction rolled back, but seems like we can tolerate
        # such cases.
        trial_type = rate.source.value if rate.source else None
        await services.kafka.send_record(
            topic=topics.ESPUTNIK_SEND_TRIAL_ENABLED_EVENT,
            value={
                'company_id': rate.company_id,
                'trial_type': trial_type,
            },
        )


async def _deactivate_rate_bonuses(
    conn: DBConnection,
    rate_id: str,
    data: DataDict,
) -> None:
    """If given rate had initiated bonuses, deactivate them too."""
    bonuses = await select_accounts(
        conn,
        sa.and_(
            billing_account_table.c.initiator_id == rate_id,
            billing_account_table.c.type == AccountType.client_bonus,
        ),
    )
    await _batch_update_company_rates(conn, [b.id for b in bonuses], data)


async def _update_config_on_rate_deactivation(
    conn: DBConnection,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> DeactivateRateConfigUpdated:
    """
    Update company config and billing company config on rate deactivation.
    """
    other_rates = [r for r in active_rates if r.id_ != rate.id_]

    permissions = utils.get_deactivation_permissions(rate=rate, active_rates=other_rates)
    limits = utils.get_deactivation_limits(rate=rate, active_rates=other_rates)

    # Change limits.max_employees_count
    await calculate_employee_extension_units(conn, active_rates, limits)

    # Clean up obsolete permissions that should be disabled
    obsolete_permissions: list[str] = [
        permission.value
        for permission, is_enabled in permissions.items()
        if not is_enabled and permission.is_obsolete
    ]

    config_update: UpdateBillingCompanyConfigDict = {}
    for permission_key, permission_value in permissions.items():
        config_update[permission_key.value] = permission_value

    for limit_key, limit_value in limits.items():
        config_update[limit_key.value] = limit_value

    async with conn.begin():
        previous_billing_config = await get_billing_company_config(conn, company_id=rate.company_id)

        await update_billing_company_config(
            conn=conn,
            company_id=rate.company_id,
            config=config_update,
        )

        logger.info(
            msg='Updating company config on rate deactivation',
            extra={
                'company_id': rate.company_id,
                'rate': rate.rate.value,
                'new_billing_config': str(config_update),
                'old_billing_config': str(previous_billing_config.to_update_dict()),
            },
        )

    return DeactivateRateConfigUpdated(
        company_config_update=config_update,
        company_config_remove=obsolete_permissions,
        billing_config_update=config_update,
    )


async def _create_free_rate_on_deactivation(
    conn: DBConnection,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> list[CompanyRate]:
    """
    If there are no active rates left beside coexisting ones (e.g. integration)
    create and activate free rate.
    """

    other_rates = [r for r in active_rates if r.id_ != rate.id_]

    should_create = utils.should_create_free_rate(active_rates=other_rates)
    if not should_create:
        return active_rates

    # Don't add documents from the "free" rate if the rate for deactivation is paid web rate.
    # This is necessary to prevent payment delays, and keep user at the same paid rate.
    # INFO: When "units" is None, it means that the number of units will be extracted from
    # the rate configuration. For example, the "free" rate includes 25 documents (June 2024).
    units: int | None = None
    if rate.rate.is_web and not (rate.rate.is_free or rate.rate.is_trial):
        units = 0

    await create_free_rate(
        conn=conn,
        company_id=rate.company_id,
        company_edrpou=rate.company_edrpou,
        units=units,
    )

    # fetch fresh rates in case if new active rate was created
    active_rates = await select_active_company_rates(conn, rate.company_id)
    return active_rates


async def _deactivate_rate(
    conn: DBConnection,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
    new_status: CompanyRateStatus,
    ignore_free_activation: bool = False,
) -> DeactivateRateConfigUpdated:
    data = (
        {'status': new_status}
        if not new_status.is_delete
        else {'status': new_status, 'date_deleted': sa.text('now()')}
    )
    await _update_company_rate(conn=conn, rate_id=rate.id_, data=data)
    await _deactivate_rate_bonuses(conn=conn, rate_id=rate.id_, data=data)
    await deactivate_company_users_extensions(conn=conn, account_id=rate.id_)

    if not ignore_free_activation:
        active_rates = await _create_free_rate_on_deactivation(
            conn=conn,
            rate=rate,
            active_rates=active_rates,
        )

    updated_configs = await _update_config_on_rate_deactivation(
        conn=conn,
        rate=rate,
        active_rates=active_rates,
    )
    billing_config = await get_billing_company_config(conn, company_id=rate.company_id)

    # Process extra roles
    await deactivate_extra_roles(
        conn=conn,
        company_id=rate.company_id,
        billing_config=billing_config,
    )

    return updated_configs


async def create_free_rate(
    conn: DBConnection,
    company_id: str,
    company_edrpou: str,
    units: int | None = None,
) -> None:
    active_rates = await select_active_company_rates(conn=conn, company_id=company_id)
    if existing_free_rate := await select_free_rate(conn, company_id):
        await activate_rate(
            conn=conn,
            rate=existing_free_rate,
            active_rates=active_rates,
        )
        return

    activation_date = local_now()
    end_date = get_rate_end_date_from_start_date(activation_date)

    rate = CompanyRate(
        id_=str(generate_uuid()),
        company_id=company_id,
        company_edrpou=company_edrpou,
        rate=AccountRate.free,
        status=CompanyRateStatus.active,
        amount=0,
        units=units,
        start_date=activation_date,
        end_date=end_date,
    )
    await add_system_company_rate(conn, rate=rate)


async def deactivate_pro_trials(
    conn: DBConnection,
    active_rates: list[CompanyRate],
) -> None:
    """
    TODO: investigate and add comment about why we do this.
    """
    # Update June 2024: the last pro-trial should expire on "2025-05-29".
    # Perhaps you can remove that logic after that date.
    pro_trials = [r for r in active_rates if r.rate.is_pro_trial]
    for rate in pro_trials:
        await _deactivate_rate(
            conn=conn,
            rate=rate,
            active_rates=active_rates,
            new_status=CompanyRateStatus.canceled,
            ignore_free_activation=True,
        )
        await send_rate_to_crm(account_id=rate.id_)


async def _cancel_rate_resources(
    conn: DBConnection, rate: CompanyRate, service_account_id: str
) -> None:
    transaction_data = {
        'to_': service_account_id,
        'from_': rate.id_,
        'initiator_id': rate.id_,
        'amount': rate.amount,
        'type': TransactionType.rate_payment_cancel.value,
        'service_account_id': service_account_id,
    }
    await _add_transaction(conn, transaction_data)


async def add_system_company_rate(
    conn: DBConnection,
    rate: CompanyRate,
) -> None:
    """
    Create new account for company rate, add transaction and
    activate rate if needed by system action
    """
    from app.actions.utils import add_system_action

    active_rates = await select_active_company_rates(conn, rate.company_id)
    service_account_id = await get_service_account_id(conn, SERVICE_CREDIT_TYPE)
    transaction_data = {
        'from_': service_account_id,
        'to_': rate.id_,
        'initiator_id': rate.company_id,
        'type': TransactionType.rate_payment.value,
        'amount': rate.amount,
        'service_account_id': service_account_id,
    }

    async with conn.begin():
        await add_rate_bonus_units(conn, rate=rate)
        await _insert_account(conn, rate.to_db())
        await _add_transaction(conn, transaction_data)

        # activate new rate if created already with active status
        if rate.status == CompanyRateStatus.active:
            await activate_rate(
                conn=conn,
                rate=rate,
                active_rates=active_rates,
            )

        await add_system_action(
            conn=conn,
            action=SystemAction.enable_system_rate,
            company_id=rate.company_id,
            rate=rate.to_serializable(),
        )


async def add_company_rate(
    conn: DBConnection,
    rate: CompanyRate,
    user: User,
) -> None:
    """
    Create new account for company rate, add transaction and
    activate rate if needed
    """

    active_rates = await select_active_company_rates(conn, rate.company_id)
    service_account_id = await get_service_account_id(conn, SERVICE_CREDIT_TYPE)
    transaction_data = {
        'from_': service_account_id,
        'to_': rate.id_,
        'operator_id': user.role_id,
        'initiator_id': user.role_id,
        'type': TransactionType.rate_payment.value,
        'amount': rate.amount,
        'service_account_id': service_account_id,
    }

    async with conn.begin():
        await add_rate_bonus_units(conn, rate=rate)
        await _insert_account(conn, rate.to_db())
        await _add_transaction(conn, transaction_data)

        # activate new rate if created already with active status
        if rate.status == CompanyRateStatus.active:
            await activate_rate(
                conn,
                rate=rate,
                active_rates=active_rates,
            )

    await insert_super_admin_action(
        conn,
        SuperAdminActionType.add_rate,
        user,
        [
            {
                'id': rate.id_,
                'company_id': rate.company_id,
                'initiator_id': rate.initiator_id,
                'rate': rate.rate.value,
            }
        ],
    )


async def update_company_rate_superadmin(
    conn: DBConnection, ctx: UpdateCompanyRateCtx, user: User
) -> None:
    """Update rate (change start/end dates, status(active, canceled) by super admin menu"""
    rates = await select_active_company_rates(conn, ctx.rate.company_id)
    async with conn.begin():
        await _update_company_rate(conn, ctx.rate.id_, ctx.to_db())
        await update_rate_bonuses(conn, ctx.rate.id_, ctx.to_db())

        # If status was changed, then activate/deactivate rate
        if ctx.should_activate:
            await activate_rate(
                conn=conn,
                rate=ctx.rate,
                active_rates=rates,
            )
        if ctx.should_deactivate:
            await _deactivate_rate(
                conn=conn,
                rate=ctx.rate,
                active_rates=rates,
                new_status=CompanyRateStatus.new,
            )

    await insert_super_admin_action(
        conn,
        SuperAdminActionType.update_rate,
        user,
        [{'rate_id': ctx.rate.id_}],
    )

    await send_rate_to_crm(account_id=ctx.rate.id_)


async def delete_company_rate(
    conn: DBConnection,
    rate: CompanyRate,
    initiator: User,
) -> None:
    """Deactivate and cancel rate resources"""
    service_account_id = await get_service_account_id(conn, SERVICE_CREDIT_TYPE)
    rates = await select_active_company_rates(conn, rate.company_id)
    async with conn.begin():
        await _deactivate_rate(
            conn=conn,
            rate=rate,
            active_rates=rates,
            new_status=CompanyRateStatus.canceled,
        )
        await _cancel_rate_resources(conn, rate, service_account_id)

    await insert_super_admin_action(
        conn,
        SuperAdminActionType.delete_rate,
        initiator,
        [{'rate_id': rate.id_}],
    )


async def expire_rate(conn: DBConnection, rate: CompanyRate) -> None:
    service_account_id = await get_service_account_id(conn, SERVICE_CREDIT_TYPE)
    rates = await select_active_company_rates(conn, rate.company_id)
    async with conn.begin():
        await _deactivate_rate(
            conn=conn,
            rate=rate,
            active_rates=rates,
            new_status=CompanyRateStatus.expired,
        )
        await _cancel_rate_resources(conn, rate, service_account_id)
    await send_rate_to_crm(account_id=rate.id_)


async def add_rate_bonus_units(conn: DBConnection, rate: CompanyRate) -> None:
    units = (
        rate.units
        if rate.units is not None
        else get_rate_config_units(rate=rate.rate, edrpou=rate.company_edrpou)
    )
    if units is None:
        return

    end_date = rate.end_date or get_rate_end_date_from_start_date(rate.start_date)
    bonus_rate = {
        'company_id': rate.company_id,
        # link between company rate and bonus account
        'initiator_id': rate.id_,
        'bill_id': rate.bill_id,
        'rate': rate.rate.value,
        'type': AccountType.client_bonus.value,
        'amount': 0,
        'amount_left': 0,
        'units': units,
        'units_left': 0,  # will be filled by transaction
        'status': rate.status.value,
        'activation_date': soft_isoformat(rate.start_date),
        'date_expired': soft_isoformat(end_date),
    }

    async with conn.begin():
        account = await _add_account(conn, bonus_rate)
        scb_account_id = await get_service_account_id(conn, AccountType.service_credit_bonus)
        transaction_data = {
            'from_': scb_account_id,
            'to_': account.id,
            'initiator_id': rate.id_,
            'type': TransactionType.bonus_income.value,
            'amount': 0,
            'units': units,
            'comment': 'Нарахування безкоштовних документів у рамках тарифу',
            'service_account_id': scb_account_id,
        }
        await _add_transaction(conn, transaction_data)


async def activate_rate_bonus_units(conn: DBConnection, rate: CompanyRate) -> None:
    await update_rate_bonuses(
        conn, rate.id_, {'status': CompanyRateStatus.active, 'date_deleted': None}
    )


async def activate_free_companies_free_rate(
    conn: DBConnection, *, company_id: str, company_edrpou: str
) -> None:
    """
    Used for filling free rates for companies without active rate
    or with active free pro rate.
    """

    rates = await select_active_company_rates(conn, company_id=company_id)

    # Deactivate PRO free rate, if the company has one. Function "_deactivate_rate"
    # also creates free rate account, if no active rates left after deactivation.
    if free_pro := next((r for r in rates if r.rate == AccountRate.pro_free), None):
        await _deactivate_rate(
            conn=conn,
            rate=free_pro,
            active_rates=rates,
            new_status=CompanyRateStatus.canceled,
        )
        await send_rate_to_crm(account_id=free_pro.id_)
        return

    # Activate free rate if the company doesn't have any active web rates
    if utils.should_create_free_rate(active_rates=rates):
        await create_free_rate(conn, company_id=company_id, company_edrpou=company_edrpou)
