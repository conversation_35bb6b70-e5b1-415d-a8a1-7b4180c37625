from __future__ import annotations

import datetime
import logging
import uuid
from collections import defaultdict
from collections.abc import Iterable
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from typing import (
    TypeGuard,
    assert_never,
)

from dateutil.relativedelta import relativedelta
from vchasno_crm.enums import CRMBillSourceInvoice, CRMBillStatus, CRMBillType

from api.errors import (
    AccessDenied,
    DoesNotExist,
    InvalidRequest,
)
from api.private.super_admin.db import insert_super_admin_action
from app.auth.db import (
    count_company_roles_for_billing,
    select_company,
    select_role_by,
    select_users_by_role_ids,
    update_roles,
    update_users_registration_completed,
)
from app.auth.enums import RoleStatus
from app.auth.types import BaseUser, User
from app.auth.utils import (
    get_company_config,
    is_fop,
    reset_role_to_default_hrs,
)
from app.billing.constants import (
    ACCOUNT_TYPES_REDIS_KEY_MAPPING,
    BILL_REQUISITES,
    DAYS_ALLOWANCE_TO_EXTEND_RATE,
    INTEGRATION_PRICE_PER_DOCUMENT,
    NOVEMBER_20,
    RATE_EXTENSION_TRIAL_DAYS,
    RATES_NAME_MAP,
    RATES_PRICE_MAP,
    RATES_PRICE_MAP_FOP,
    RATES_PRICE_MAP_TOV,
    RESTRICTED_EDRPOUS_TO_PROCESS_PAYMENT_STATUS,
)
from app.billing.db import (
    can_activate_rate_extension_trial,
    insert_payment_transaction,
    insert_rate_extension,
    select_active_company_rates,
    select_active_roles,
    select_billing_companies_configs,
    select_billing_company_config,
    select_bills_by_ids,
    select_bonus_by_key,
    select_company_accounts,
    select_company_active_bonuses,
    select_company_active_debits,
    select_latest_active_company_rate,
    select_latest_bank_transaction_by_bill_id,
    select_latest_payment_status_transaction_by_bill_id,
    select_rate_extension_by_bill_id,
    select_rate_extensions,
    select_rates_by_bill_id,
    select_service_account_id,
    select_users_for_activation,
    update_bill,
    update_billing_company_config,
    update_price_per_user_in_company_rate,
    update_rate_extension,
)
from app.billing.enums import (
    AccountRate,
    AccountType,
    BillActivationSource,
    BillingAccountSource,
    BillPaymentSource,
    BillPaymentStatus,
    BillServicesType,
    BillServiceType,
    BillSource,
    CompanyLimit,
    CompanyPermission,
    CompanyRateStatus,
    CreatioBillType,
    RateExtensionStatus,
    RateExtensionType,
)
from app.billing.rates import (
    ARCHIVE_DEFAULT,
    RATES_CONFIG_MAP,
    RATES_CONFIG_MAP_FOP,
    RATES_CONFIG_MAP_TOV,
)
from app.billing.schemas import CreateBillResponseSchema, CreateBillSchema
from app.billing.types import (
    AddBillOptions,
    AddBillServiceExtensionOptions,
    AddBillServiceOptions,
    Bill,
    BillingCompanyConfig,
    BillResources,
    BillService,
    BillServiceExtension,
    BillServiceRate,
    CheckoutArchiveRateProperties,
    CheckoutExtensionProperties,
    CheckoutIntegrationRateProperties,
    CheckoutProperties,
    CheckoutWebRateProperties,
    CompanyRate,
    PaymentTransaction,
    RateConfig,
    RateConfigLimits,
    RateConfigPermissions,
    RateExtension,
    RateExtensionConfig,
    UserAddBillOptions,
)
from app.contacts import utils as contacts
from app.crm.utils import send_bill_to_crm, send_rate_to_crm
from app.esputnik import Event
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import money, templates, url2pdf
from app.lib.database import DBConnection
from app.lib.datetime_utils import (
    DAY,
    end_of_day,
    local_now,
    midnight,
    naive_local_now,
    soft_isoformat,
    to_local_datetime,
    utc_now,
)
from app.lib.enums import SuperAdminActionType, UserRole
from app.lib.helpers import (
    not_none,
    quantize,
    to_decimal,
)
from app.lib.types import DataDict
from app.lib.url2pdf import URL2PDFWaitUnit
from app.lib.urls import build_url
from app.services import services
from worker import topics
from worker.billing.enums import ChargeType

logger = logging.getLogger(__name__)


TRIALS_DAYS_MAP: dict[AccountRate, datetime.timedelta] = {
    AccountRate.latest_pro_plus_trial(): 14 * DAY,
    AccountRate.latest_integration_trial(): 31 * DAY,
}


def generate_reason_bill_notification(bill: Bill) -> str | None:
    """Generates reason string for notification based on bill type"""

    reason = f'Оплата по рахунку {bill.number} пройшла успішно. '
    match bill.services_type:
        case BillServicesType.rate | BillServicesType.integration_and_documents:
            rate_service = bill.single_rate_service
            return reason + f'Тариф {RATES_NAME_MAP[rate_service.rate]} додано до акаунту'

        case BillServicesType.add_employee:
            extension_service = bill.single_extension_service
            return (
                reason + f'Додаткове розширення доступу для {extension_service.units}'
                f' користувачів до онлайн-сервісу електронного документообігу активовано'
            )
        case BillServicesType.documents:
            units_service = bill.single_units_service
            return reason + f'Баланс поповнено на {units_service.units} документів'
        case BillServicesType.web_and_archive:
            web_service = next(s for s in bill.rate_services if s.rate.is_web)
            archive_service = next(s for s in bill.rate_services if s.rate.is_archive)
            return (
                reason
                + f'Тариф {RATES_NAME_MAP[web_service.rate]} додано до акаунту. '
                + f'Тариф {RATES_NAME_MAP[archive_service.rate]} додано до акаунту.'
            )
        case _:
            assert_never(_)


async def bill_to_pdf(bill_id: str) -> bytes | None:
    """
    Create a PDF file from the bill using URL2PDF service.

    To render the PDF, we use URL2PDF service:

    1. EDO sends a request to URL2PDF service, with the URL to /internal-api/bills/{bill_id}/html
    2. URL2PDF opens that link in a headless browser.
    4. Browser fetch images and render the bill as HTML
    5. URL2PDF converts the HTML to PDF.
    6. URL2PDF responds to EDO with the PDF file.

    See also:
     - ./cs/components/txtXmlViewer/vchasno/invoiceWrapper.jsx — React component of the bill
     - ./app/templates/bills/bill.html.jinja2 — Jinja2 template of the bill HTML page

    To test how the page looks like locally, you can create a bill somehow and open
    the following link in your browser:
     - http://localhost:8000/internal-api/bills/{bill_id}/html
    """

    page_url = build_url(route='api.billing.retrieve_bill_html', bill_id=bill_id, absolute=True)

    # The page is static, so we don't need to wait for JS, wait only HTML and images to load
    wait_until = URL2PDFWaitUnit.LOAD
    # Page doesn't have any external styles, so we can skip them.
    # Fonts also won't be loaded. URL2PDF has Roboto font installed in the system
    skip_styles = True

    try:
        return await url2pdf.convert_url_to_pdf(
            convert_url=page_url,
            wait_until=wait_until,
            skip_styles=skip_styles,
        )
    except url2pdf.URL2PDFError:
        # Fail silently, because we use this function in the worker tasks
        logger.exception('Error on bill to PDF conversion', extra={'bill_id': bill_id})
        return None


async def bill_to_html(bill: Bill) -> str:
    """
    Render a bill to HTML using Jinja2 template.
    """

    total_sum = bill.total_sum

    # Example: '200.10'
    total_price_units = money.to_formatted_units_string(total_sum)
    total_tax_subunits = money.calculate_price_tax_amount(price_with_tax=total_sum)
    total_tax_units = money.to_formatted_units_string(total_tax_subunits)

    service_line: str | None = None
    services_table: list[DataDict] | None = None
    if bill.services_type in (
        BillServicesType.rate,
        BillServicesType.integration_and_documents,
        BillServicesType.documents,
        BillServicesType.add_employee,
    ):
        # Show single line with service description
        service_line = get_bill_service(bill)

    elif bill.services_type == BillServicesType.web_and_archive:
        # Show table with multiple services
        services_table = []
        for service in bill.rate_services:
            service_text = get_bill_service_description(service, agreement=bill.agreement)

            price_subunits = service.unit_price * service.units
            price_units = money.to_formatted_units_string(price_subunits)

            services_table.append({'text': service_text, 'price': price_units})

    # The line under "services" table or line with additional details
    services_details: str | None = get_bill_service_details(bill)

    html = await templates.render_template(
        template_name='bills/bill.html.jinja2',
        context={
            'bill_number': bill.number,
            'bill_date': bill.date_str_uk,
            'buyer_name': bill.name,
            'supplier_name': BILL_REQUISITES.name,
            'supplier_edrpou': BILL_REQUISITES.edrpou,
            'supplier_ipn': BILL_REQUISITES.ipn,
            'supplier_bank': BILL_REQUISITES.bank,
            'supplier_iban': BILL_REQUISITES.iban,
            'total_price': total_price_units,
            'total_tax': total_tax_units,
            'contract_basis': bill.contract_basis,
            'service_line': service_line,
            'services_table': services_table,
            'services_details': services_details,
        },
    )
    return html


def get_employee_price(
    price_per_user: int, rate_end_date: datetime.datetime
) -> tuple[float, float]:
    """
    Приклад алгоритму формування ціни:
    - Вартість за співробітника для моєї компанії: 1200 грн/рік.
    - До закінчення тарифу залишилось 56 днів.
    - Вартість одного співробітника за день: 1200/<кількість календарних днів у році>= 3.3 грн/день.
    - Вартість одного співробітника за 56 днів: 3.3 грн. * 56 = 184.10 грн.
    """
    today = utc_now()
    days_in_year = 365
    # +1 day because rate end after this date
    days_until_rate_finished: datetime.timedelta = (
        rate_end_date.date() - today.date() + timedelta(days=1)
    )
    price_per_user_total = (price_per_user / days_in_year) * days_until_rate_finished.days

    # divide by 100 because store price in database as integer. 10052 (in database) => 100.52 UAH
    price_per_user_base = round(price_per_user / 100, 2)
    # taking into account the number of days until the end of the tariff
    price_per_user_total = round(price_per_user_total / 100, 2)
    return price_per_user_base, price_per_user_total


def get_highest_web_rate(company_rates: list[CompanyRate]) -> CompanyRate | None:
    """Returns the highest web rate the user has from their active rates."""

    web_rates = [rate for rate in company_rates if rate.rate.is_web]
    if not web_rates:
        return None

    return max(web_rates, key=lambda x: x.rate.priority_rank)


def get_highest_rate(active_rates: list[CompanyRate]) -> CompanyRate | None:
    """Returns the highest rate the user has from their active rates."""

    if not active_rates:
        return None

    # TODO: What if we have multiple rates with the same priority rank?
    return max(active_rates, key=lambda x: x.rate.priority_rank)


def get_bill_service_employee_amount_limit(
    service: BillServiceRate, company_edrpou: str
) -> int | None:
    """
    Get employee amount limit for the bill service based on rate
    """
    rate = service.rate

    # Those rates don't have a predefined limit of employees count
    if rate.is_integration or rate.is_archive:
        return None

    # The Ultimate rate doesn't have a predefined limit of employees count,
    # and must be set manually for every new bill by the sales manager
    if rate.is_ultimate:
        return service.limits_employees_count

    return get_rate_config_limits(rate, edrpou=company_edrpou)[CompanyLimit.employees]


def calculate_active_rate_left_days(rate: CompanyRate) -> int:
    """Returns left days from CompanyRate"""

    dt_now = naive_local_now()
    if not rate.end_date:
        return 0
    return (rate.end_date.replace(tzinfo=None) - dt_now.replace(tzinfo=None)).days


async def get_left_documents_percentage_web_rate(company_id: str) -> float:
    """Get percentage of left documents on web balance"""

    async with services.db_readonly.acquire() as conn:
        active_rates = await select_active_company_rates(conn, company_id)
        highest_rate = get_highest_rate(active_rates)

        if not highest_rate or highest_rate.rate.is_ultimate:
            return 100

        _, percentage = await get_entity_expiring_event(
            conn=conn,
            charge_type=ChargeType.web,
            company_id=company_id,
        )

    return percentage


async def get_recommended_web_rate(company_id: str) -> AccountRate | None:
    """
    Get next recommended web rate for company or None if there is no recommendation
    Rate recommendation policy:
    1. If rate is free, always show recommended as start
    2. If rate is low on documents, but not expiring, show next rate
    3. If rate is expiring but not low on documents, show rate extendance
    4. If rate is not expiring and documents aren't low, but employees are - show next rate
    5. All other cases - don't show anything
    """

    async with services.db.acquire() as conn:
        active_rates = await select_active_company_rates(conn, company_id)
        config = await get_billing_company_config(conn, company_id=company_id)
        count = await count_company_roles_for_billing(conn, company_id)

    web_rates = [rate for rate in active_rates if rate.rate.is_web]

    highest_rate = get_highest_rate(web_rates)

    if not highest_rate or highest_rate.rate.is_free:
        return AccountRate.latest_start()

    rate_left_days = calculate_active_rate_left_days(highest_rate)
    is_rate_expiring = rate_left_days < DAYS_ALLOWANCE_TO_EXTEND_RATE

    percentage = await get_left_documents_percentage_web_rate(company_id)
    is_low_on_documents = False if highest_rate.rate.is_ultimate else percentage < 10

    next_rate = AccountRate.get_next_web_rate(rate=highest_rate.rate)
    current_rate = highest_rate.rate.latest

    employees_limit: int | None = get_rate_limit(config, CompanyLimit.employees)
    is_employees_limit = bool(count == employees_limit)

    if current_rate.is_free:
        return next_rate
    if is_low_on_documents and not is_rate_expiring:
        return next_rate
    if is_rate_expiring:
        return current_rate
    if is_employees_limit:
        return next_rate
    return None


def _get_units_bill_total(count_documents: int, price_per_document: Decimal) -> Decimal:
    count = to_decimal(count_documents)
    price_gross = to_decimal(price_per_document)
    total = quantize(count * price_gross)

    return total


async def get_price_per_document(
    conn: DBConnection,
    company_id: str | None = None,
    company_edrpou: str | None = None,
) -> float:
    """Returns default price per document OR custom that is defined in company config"""

    config = await get_company_config(
        conn=conn,
        company_id=company_id,
        company_edrpou=company_edrpou,
    )
    # В гривнях (float), example: 100.00, 100.52, 20.99
    return config.price_per_document or INTEGRATION_PRICE_PER_DOCUMENT


def format_bill_number(value: int) -> str:
    """1C setting for bill number length is 11 (with preceding zeros)."""
    prefix = 'ВЧ-'
    number = str(value).zfill(11)
    return f'{prefix}{number}'


def get_amount_to_charge(amount: int, amount_left: int, units: int, units_left: int) -> int:
    new_units_left = units_left - 1
    return amount_left - round(amount * (new_units_left / units))


async def get_bonus_id(conn: DBConnection, bonus_key: str) -> str | None:
    redis_key = f'billing_bonus_id_{bonus_key}'
    bonus_id = await services.redis.get(redis_key)
    if bonus_id:
        return bonus_id

    bonus = await select_bonus_by_key(conn, bonus_key)
    if bonus:
        bonus_id = bonus.id
        await services.redis.set(redis_key, bonus_id)
        return bonus_id

    return None


def get_date_expired(days: int) -> datetime.datetime:
    return end_of_day(utc_now() + datetime.timedelta(days=days))


def get_date_expired_str(days: int) -> str | None:
    return soft_isoformat(get_date_expired(days))


async def get_documents_balance(conn: DBConnection, company_id: str) -> tuple[int, int]:
    accounts = await select_company_accounts(conn, company_id)

    debit_left = sum(
        account.units_left for account in accounts if account.type == AccountType.client_debit
    )
    bonus_left = sum(account.units_left for account in accounts if account.type.is_bonus)
    return debit_left, bonus_left


async def get_service_account_id(conn: DBConnection, type_: AccountType) -> str:
    account_key = ACCOUNT_TYPES_REDIS_KEY_MAPPING[type_.value]
    if not account_key:
        raise Exception('Error on get_service_account_id, no service account')

    account_id = await services.redis.get(account_key)
    if account_id:
        return account_id

    account_id = await select_service_account_id(conn, type_)
    assert account_id, 'Expected account_id exists'

    await services.redis.set(account_key, account_id)

    return account_id


async def is_company_has_pro_or_higher_rate(conn: DBConnection, company_id: str) -> bool:
    """
    Checks if company has enough balance.
    Maybe you need something like `select_active_company_rates`.
    """

    accounts = await select_company_accounts(conn, company_id)
    return any(account.rate.is_pro or account.rate.is_ultimate for account in accounts)


async def get_companies_have_pro_or_higher_rate(
    conn: DBConnection, company_ids: list[str]
) -> dict[str, bool]:
    """
    Get companies accounts and verify they have pro/higher rate

    Example: {'company_id': bool, ...}
    """
    accounts = await select_company_accounts(conn, company_ids=company_ids)

    accounts_mapping = defaultdict(list)
    for account in accounts:
        accounts_mapping[account.company_id].append(account)

    result = {}
    for company_id, accounts in accounts_mapping.items():
        result[company_id] = any(acc.rate.is_pro or acc.rate.is_ultimate for acc in accounts)

    return result


def generate_trial_rate(
    *,
    company_id: str,
    company_edrpou: str,
    rate: AccountRate,
    role_id: str,
    source: BillingAccountSource | None = None,
) -> CompanyRate:
    start_date = local_now()
    end_date = end_of_day(start_date + TRIALS_DAYS_MAP[rate])
    end_date = to_local_datetime(end_date.replace(tzinfo=None))
    return CompanyRate(
        id_=str(uuid.uuid4()),
        company_id=company_id,
        company_edrpou=company_edrpou,
        initiator_id=role_id,
        rate=rate,
        source=source,
        status=CompanyRateStatus.active,
        amount=0,
        start_date=start_date,
        end_date=end_date,
    )


def is_allowed_by_rate(config: BillingCompanyConfig, permission: CompanyPermission) -> bool:
    return getattr(config, permission.value, False)


def get_rate_limit(config: BillingCompanyConfig, limit: CompanyLimit) -> int | None:
    return getattr(config, limit.value, None)


def get_rate_config(rate: AccountRate, *, edrpou: str) -> RateConfig | None:
    """
    Config for rate can be stored in different places:
    - RATES_MAP — rates that don't depend on company type
    - RATES_MAP_FOP — rates only for FOP companies
    - RATES_MAP_TOV — rates only for TOV companies
    """
    if rate in RATES_CONFIG_MAP:
        return RATES_CONFIG_MAP[rate]

    config = RATES_CONFIG_MAP_FOP if is_fop(edrpou) else RATES_CONFIG_MAP_TOV
    if rate in config:
        return config[rate]

    return None


def get_rate_config_permissions(rate: AccountRate, *, edrpou: str) -> RateConfigPermissions:
    config = get_rate_config(rate, edrpou=edrpou)
    if not config:
        return {}
    return config.get('permissions', {})


def get_rate_config_limits(rate: AccountRate, *, edrpou: str) -> RateConfigLimits:
    config = get_rate_config(rate, edrpou=edrpou)
    if not config:
        return {}
    return config.get('limits', {})


def get_rate_config_units(rate: AccountRate, *, edrpou: str) -> int | None:
    config = get_rate_config(rate, edrpou=edrpou)
    if not config:
        return None
    return config.get('units')


def get_activation_permissions(
    *,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> RateConfigPermissions:
    """
    Merge permissions from the new rate with currently active rates.

    The permission should be enabled if it is enabled for at least one of the rates.
    """
    rate_permissions = dict(get_rate_config_permissions(rate.rate, edrpou=rate.company_edrpou))
    for active_rate in active_rates:
        for permission, value in get_rate_config_permissions(
            rate=active_rate.rate,
            edrpou=active_rate.company_edrpou,
        ).items():
            rate_permissions[permission] = rate_permissions.get(permission) or value

    return rate_permissions


def get_deactivation_permissions(
    *,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> RateConfigPermissions:
    """
    Get actual snapshot of permissions from active rates after deactivation of the rate.
    """

    # All permissions from active rates (without the rate that is being deactivated).
    # The basic rule is that if permission is "True" at least for one rate, it should be enabled
    # for the company.
    permissions: RateConfigPermissions = {}
    for active_rate in active_rates:
        for permission, value in get_rate_config_permissions(
            rate=active_rate.rate,
            edrpou=active_rate.company_edrpou,
        ).items():
            permissions[permission] = permissions.get(permission, False) or value

    # If some permissions don't exist in active rates, we should disable them
    for permission in get_rate_config_permissions(rate=rate.rate, edrpou=rate.company_edrpou):
        if permission not in permissions:
            permissions[permission] = False

    return permissions


def get_activation_limits(
    *,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> RateConfigLimits:
    """Get max limit values from active rates + new rate."""
    return _get_rates_limits([*active_rates, rate])


def should_create_free_rate(active_rates: list[CompanyRate]) -> bool:
    """
    When the last web rate for a company is deactivated or a new company is created,
    we should create and activate the free web rate. This simplifies the logic of working with
    rates because every company should have at least one active web rate.

    Other rates sets, like "integrations" or "archives" don't have such requirements,
    and no active rate is needed for them.
    """
    web_rates = [rate for rate in active_rates if rate.rate.is_web]
    if not web_rates:
        return True

    # NOTE: When a company has only trial rates and no paid or free rate,
    # we consider it as a company without a rates
    if all(r.rate.is_trial for r in web_rates):
        return True

    return False


def get_deactivation_limits(
    *,
    rate: CompanyRate,
    active_rates: list[CompanyRate],
) -> RateConfigLimits:
    """
    Get max limit values from either remaining active rates or default limits if no active
    rates left.
    """

    # Default limits for different sets of rates
    if rate.rate.is_web and not any(r.rate.is_web for r in active_rates):
        return get_rate_config_limits(AccountRate.free, edrpou=rate.company_edrpou)

    if rate.rate.is_archive and not any(rate.rate.is_archive for rate in active_rates):
        return ARCHIVE_DEFAULT.get('limits', {})

    if rate.rate.is_integration and not any(rate.rate.is_integration for rate in active_rates):
        # Integration rate doesn't have any limits, so no limits to deactivate
        return {}

    # Here we get an actual snapshot of limits from rates that are left after deactivation
    return _get_rates_limits(active_rates)


def _get_rates_limits(rates: list[CompanyRate]) -> RateConfigLimits:
    """
    Returns limits mapping with max values from `rates`.
    """
    res: RateConfigLimits = {}
    # Don't change employee amount while deactivation
    # in case user already has an ultimate rate
    ultimate_rate = [r for r in rates if r.rate.is_ultimate]
    for rate in rates:
        if not (limits := get_rate_config_limits(rate.rate, edrpou=rate.company_edrpou)):
            continue
        for limit, value in limits.items():
            curr_max_value = res.get(limit, 0)
            if value is None:
                if ultimate_rate and limit == CompanyLimit.employees:
                    res[limit] = ultimate_rate[0].employee_amount
                else:
                    res[limit] = None
            elif curr_max_value is not None and curr_max_value <= value:
                res[limit] = value

    return res


def _get_obsolete_permissions(permissions: Iterable[CompanyPermission]) -> list[str]:
    """Get keys that are not used anymore and need to be wiped from configs."""
    return [perm.value for perm in permissions if perm.is_obsolete]


def get_bill_service_description(service: BillService, agreement: str | None) -> str:
    """
    Get a description of the service for single bill. Check get_bill_service to see how
    we generate service lien for different services combinations.
    """
    if service.type == BillServiceType.rate:
        if service.rate == AccountRate.integration:
            return (
                'Доступ до інтеграції для обміну електронними документами між '
                'Онлайн-сервісом електронного документообігу Вчасно '
                'та обліковою системою Користувача.'
            )

        if service.rate.is_archive:
            description = (
                'Додаткове розширення доступу до онлайн-сервісу електронного документообігу '
                f'для роботи з електронними документами згідно тарифу '
                f'"{RATES_NAME_MAP[service.rate]}" на 1 рік'
            )
        else:
            # web rates
            description = (
                'Додаткове розширення доступу до онлайн-сервісу електронного документообігу '
                f'з додатковим функціоналом згідно тарифу "{RATES_NAME_MAP[service.rate]}" на 1 рік'
            )

        if agreement:
            description += f' згідно рах. {agreement}'
        return description

    if service.type == BillServiceType.units:
        description = (
            'Надання доступу до онлайн-сервісу електронного '
            f'документообігу ({service.units} документів)'
        )
        if agreement:
            description += f' згідно рах. {agreement}'
        return description

    if service.type == BillServiceType.extension:
        return (
            'Додаткове розширення доступу до онлайн-сервісу електронного документообігу '
            'з додатковим функціоналом згідно тарифу "Максимальний"'
        )

    assert_never(service.type)


def get_bill_service(bill: Bill) -> str | None:
    """
    Generate a service line for the bill based on the bill type.
    """
    if bill.payment_purpose is not None:
        return bill.payment_purpose

    if bill.services_type == BillServicesType.add_employee:
        return get_bill_service_description(
            service=bill.single_extension_service,
            agreement=bill.agreement,
        )

    if bill.services_type == BillServicesType.documents:
        return get_bill_service_description(
            service=bill.single_units_service,
            agreement=bill.agreement,
        )

    if bill.services_type == BillServicesType.rate:
        return get_bill_service_description(
            service=bill.single_rate_service,
            agreement=bill.agreement,
        )

    if bill.services_type == BillServicesType.web_and_archive:
        web_service = next(s for s in bill.rate_services if s.rate.is_web)
        # This is not the best option here because web and archive are two separate sets of
        # rates, and we should represent both of them. But in cases when only a single service
        # line is expected, we at least will show the web rate, instead of failing.
        return get_bill_service_description(web_service, agreement=bill.agreement)

    if bill.services_type == BillServicesType.integration_and_documents:
        # The same as with "web_and_archive" case, we should represent two services
        # with a single line
        units_service = bill.single_units_service
        service = (
            'Доступ до інтеграції для обміну електронними документами між '
            'Онлайн-сервісом електронного документообігу Вчасно '
            f'та обліковою системою Користувача ({units_service.units}) документів'
        )
        if bill.agreement:
            service += f' згідно рах. {bill.agreement}'
        return service

    assert_never(bill.services_type)


def get_bill_service_details(bill: Bill) -> str | None:
    """
    Additional information about the service for the bill.

    In a bill visualization, it's a line right after the service line
    """
    if bill.services_type == BillServicesType.rate:
        employee_limit = bill.single_rate_service.limits_employees_count
        if employee_limit:
            return f'Кількість співробітників Замовника — {employee_limit}'
    if bill.services_type == BillServicesType.add_employee:
        employee_limit = bill.single_extension_service.units
        return f'Кількість співробітників Замовника — {employee_limit}'

    return None


async def get_bill_total(bill: Bill) -> float:
    """
    Calculate the total price of the bill.

    Returns in units (в гривнях)

    Examples: 100.0 = 100.0 UAH, 100.52 = 100.52 UAH
    """

    # Custom price set by manager
    if bill.custom_price is not None:
        return float(bill.custom_price)

    # Sum of all services
    return money.to_units_float_from_subunits(bill.services_total)


async def schedule_bill_processing(
    *,
    bill_id: str,
    is_card_payment: bool,
) -> None:
    """
    Schedule bill processing to be executed in the background.
    """
    await services.kafka.send_record(
        topic=topics.BILLS,
        value={
            'bill_id': bill_id,
            'is_card_payment': is_card_payment,
        },
    )


def get_latest_rate_by_bill_type(bill_type: CreatioBillType) -> AccountRate:
    match bill_type:
        case CreatioBillType.start:
            return AccountRate.latest_start()
        case CreatioBillType.start_fop:
            return AccountRate.latest_start()
        case CreatioBillType.start_tov:
            return AccountRate.latest_start()
        case CreatioBillType.pro:
            return AccountRate.latest_pro()
        case CreatioBillType.pro_fop:
            return AccountRate.latest_pro()
        case CreatioBillType.pro_tov:
            return AccountRate.latest_pro()
        case CreatioBillType.integration:
            return AccountRate.latest_integration()
        case CreatioBillType.ultimate:
            return AccountRate.latest_ultimate()
        case CreatioBillType.archive_small:
            return AccountRate.archive_small
        case CreatioBillType.archive_big:
            return AccountRate.archive_big
        case CreatioBillType.free:
            return AccountRate.latest_free()
        case (
            CreatioBillType.documents
            | CreatioBillType.add_employee
            | CreatioBillType.integration_with_documents
        ):
            # These CRM types are not real account rates, and it's not possible to get the
            # latest rate from them. Handle these cases separately by code that calls this
            # function.
            raise ValueError(f'Invalid crm bill type ({bill_type})')
        case _:
            # handle all cases explicitly, including invalid ones
            assert_never(bill_type)


async def get_crm_bill_payment_details(bill: Bill) -> str | None:
    async with services.db.acquire() as conn:
        last_bank_transaction = await select_latest_bank_transaction_by_bill_id(
            conn=conn,
            bill_id=bill.id_,
        )

    return last_bank_transaction.details if last_bank_transaction else None


async def get_crm_bill_payment_source(bill: Bill) -> CRMBillSourceInvoice | None:
    async with services.db.acquire() as conn:
        last_payment_entry = await select_latest_payment_status_transaction_by_bill_id(
            conn=conn,
            bill_id=bill.id_,
        )

    if last_payment_entry and last_payment_entry.source:
        if last_payment_entry.source.is_card:
            return CRMBillSourceInvoice.card
        if last_payment_entry.source.is_transfer:
            return CRMBillSourceInvoice.transfer
        if last_payment_entry.source.is_manual:
            return CRMBillSourceInvoice.manual

    return None


def get_crm_bill_employee_count(bill: Bill) -> int | None:
    if bill.services_type == BillServicesType.add_employee:
        return bill.single_extension_service.units
    return None


def get_crm_bill_status(bill: Bill) -> CRMBillStatus:
    # Don't change status for bill in test companies,
    # to avoid incorrect profit calculations on the CRM side
    if bill.edrpou in RESTRICTED_EDRPOUS_TO_PROCESS_PAYMENT_STATUS:
        return CRMBillStatus.new

    match bill.payment_status:
        case BillPaymentStatus.requested | BillPaymentStatus.pin_requested | None:
            return CRMBillStatus.new
        case BillPaymentStatus.completed:
            return CRMBillStatus.paid
        case BillPaymentStatus.rejected | BillPaymentStatus.canceled:
            return CRMBillStatus.cancelled
        case BillPaymentStatus.refund:
            return CRMBillStatus.refund
        case _:
            raise ValueError('BillPaymentStatus is not supported for CRM')


def get_crm_bill_type_by_rate(rate: AccountRate, edrpou: str) -> CRMBillType:
    match rate:
        # Trials.
        # This conditions should be first cause trials also have is_pro / is_integration flags
        case AccountRate.pro:
            return CRMBillType.unlimited
        case AccountRate.pro_plus_2022_12:
            return CRMBillType.pro_plus
        case AccountRate.integration_trial:
            return CRMBillType.trial_api
        case AccountRate.pro_plus_trial_2022_12:
            return CRMBillType.trial_web

        # INFO: we don't have different keys for rates for FOP and TOV companies in our system,
        # but CRM expect different keys depending on the company type
        case _ if rate.is_start:
            return CRMBillType.start_fop if is_fop(edrpou) else CRMBillType.start_tov
        case _ if rate.is_pro:
            return CRMBillType.pro_fop if is_fop(edrpou) else CRMBillType.pro_tov
        case _ if rate.is_integration:
            return CRMBillType.integration
        case _ if rate.is_ultimate:
            return CRMBillType.ultimate
        case AccountRate.archive_small:
            return CRMBillType.archive_small
        case AccountRate.archive_big:
            return CRMBillType.archive_big
        case _ if rate.is_free:
            return CRMBillType.free
        case _:
            raise ValueError(f'Invalid crm bill rate type ({rate.value})')


def get_crm_bill_type(bill: Bill) -> CRMBillType:
    match bill.services_type:
        case BillServicesType.rate:
            service = bill.single_rate_service
            return get_crm_bill_type_by_rate(rate=service.rate, edrpou=bill.edrpou)
        case BillServicesType.documents:
            return CRMBillType.documents
        case BillServicesType.add_employee:
            return CRMBillType.add_employee
        case BillServicesType.integration_and_documents:
            return CRMBillType.integration
        case BillServicesType.web_and_archive:
            service = next(s for s in bill.rate_services if s.rate.is_web)
            return get_crm_bill_type_by_rate(rate=service.rate, edrpou=bill.edrpou)
        case _:
            assert_never(bill.services_type)


async def deactivate_extra_roles(
    conn: DBConnection,
    company_id: str,
    billing_config: BillingCompanyConfig,
) -> None:
    from app.auth.utils import delete_role_base

    active_roles = await select_active_roles(conn, company_id)

    new_limit: int | None = get_rate_limit(billing_config, CompanyLimit.employees)

    # If new limit is None, it means that company has no limit, and
    # we should not deactivate any roles
    if new_limit is None:
        return

    # Get first N roles to deactivate
    roles_to_deactivate = active_roles[:-new_limit]

    for role in roles_to_deactivate:
        # If user has HRS role, don't delete him from company
        # Reset his permissions to the default HRS ones and
        # stop counting him towards the billing limit.
        if role.has_hrs_role and role.user_role != UserRole.admin.value:
            await reset_role_to_default_hrs(conn=conn, role=role)
        else:
            await delete_role_base(
                conn=conn,
                role_id=role.id,
                role_user_id=role.user_id,
                status=RoleStatus.rate_deleted,
            )
            logger.info(
                'Disable role due rate limitations',
                extra={
                    'role_id': role.id,
                    'new_limit': new_limit,
                    'role_count': len(active_roles),
                },
            )


async def update_registration_complete_for_extra_roles(
    conn: DBConnection,
    role_ids: list[str],
) -> None:
    """
    If user has only one role, and role has been deleted (status changed to any other than `active`)
    registration_completed property is set to false

    In order to cover this edge case, we need to update
    registration_completed field after role activation
    """

    users = await select_users_by_role_ids(
        conn=conn,
        ids=role_ids,
    )
    user_ids_with_registration_incomplete = [
        user.id for user in users if user.registration_completed is False
    ]

    if user_ids_with_registration_incomplete:
        logger.info(
            'Updating registration_completed fields for newly activated users',
            extra={'user_ids': user_ids_with_registration_incomplete},
        )
        await update_users_registration_completed(
            conn=conn,
            user_ids=user_ids_with_registration_incomplete,
            registration_completed=True,
        )


async def activate_extra_roles(
    conn: DBConnection,
    company_id: str,
    billing_config: BillingCompanyConfig,
) -> None:
    """
    Activate extra roles based on company config limit
    """
    active_roles = await select_active_roles(conn, company_id)

    new_limit: int | None = get_rate_limit(billing_config, CompanyLimit.employees)

    if new_limit == 0:
        logger.info(
            'New limit is not found in company config',
            extra={'company_id': company_id},
        )
        return

    disabled_roles = await select_users_for_activation(conn=conn, company_id=company_id)

    if new_limit is None:
        # Activate all disabled roles, activated by previous rate limit,
        # because None value means that company has no limit for those objects
        role_ids_to_activate = [role.id for role in disabled_roles]

    else:
        count_roles_to_activate = new_limit - len(active_roles)

        # Make sure that we are not activating more roles than new limit
        if count_roles_to_activate <= 0 or not disabled_roles:
            logger.info(
                msg='Extra roles activation skipped due to no users to activate',
                extra={
                    'company_id': company_id,
                    'active_roles': len(active_roles),
                    'new_limit': new_limit,
                    'disabled_roles': len(disabled_roles),
                },
            )
            return

        # Get last N roles to activate
        role_ids_to_activate = [role.id for role in disabled_roles[-count_roles_to_activate:]]

    logger.info(
        'Activating roles due to new config',
        extra={
            'company_id': company_id,
            'role_ids': role_ids_to_activate,
            'new_limit': new_limit,
        },
    )
    async with conn.begin():
        # TAG: role_activation
        await update_roles(
            conn=conn,
            role_ids=role_ids_to_activate,
            data={
                'status': RoleStatus.active,
                # NOTE: Here we don't need to update "activated_by", "date_activated" and
                # "activation_source" because the user was already activated before and just was
                # disabled automatically by the rate limit
            },
        )
        await update_registration_complete_for_extra_roles(conn=conn, role_ids=role_ids_to_activate)
        await contacts.add_role_to_contact_recipients_indexation(
            conn=conn,
            roles_ids=role_ids_to_activate,
        )


async def process_extra_roles(conn: DBConnection, company_id: str) -> None:
    """
    Disable extra roles if user has any.
    Use FILO, but admin has priority to stay. When all users are disabled,
    only after that admin will be disabled.
    """

    active_roles = await select_active_roles(conn, company_id)

    billing_config = await get_billing_company_config(conn, company_id=company_id)

    # If new limit is None, it means that company has no limit, and
    # we should activate all roles blocked by previous rate limit
    # In other case, activate or deactivate roles based on new limit
    new_limit: int | None = get_rate_limit(billing_config, CompanyLimit.employees)

    logger.info(
        msg='Processing extra roles due to new limit',
        extra={
            'company_id': company_id,
            'active_roles': len(active_roles),
            'new_limit': new_limit,
        },
    )

    if new_limit is None or len(active_roles) < new_limit:
        await activate_extra_roles(
            conn=conn,
            company_id=company_id,
            billing_config=billing_config,
        )
    else:
        await deactivate_extra_roles(
            conn=conn,
            company_id=company_id,
            billing_config=billing_config,
        )


async def bill_to_response(
    bill: Bill, extension_status: RateExtensionStatus | None = None
) -> CreateBillResponseSchema:
    """Convert bill to dictionary object for API response"""

    total = await get_bill_total(bill)
    requisites = BILL_REQUISITES
    return CreateBillResponseSchema(
        bill_id=bill.id_,
        number=bill.number,
        date=bill.date_iso,
        buyer_name=bill.name,
        supplier_name=requisites.short_name,
        service=get_bill_service(bill),
        supplier_edrpou=requisites.edrpou,
        supplier_ipn=requisites.ipn,
        supplier_bank=requisites.bank,
        supplier_iban=requisites.iban,
        amount=total,
        extension_status=extension_status,
    )


async def activate_bill_rate_service(
    conn: DBConnection,
    bill: Bill,
    service: BillServiceRate,
    start_date: datetime.datetime | None = None,
    end_date: datetime.datetime | None = None,
) -> list[CompanyRate]:
    """Rate activation/extending by payment"""
    from app.billing.api import _deactivate_rate, add_system_company_rate
    from app.billing.validators import is_unlimited_trial, validate_bill_dates
    from app.profile.validators import validate_company_by_edrpou_exists

    updated_rates = []
    price_per_user = None
    rate = service.rate

    company = await validate_company_by_edrpou_exists(conn, bill.edrpou)
    active_rates = await select_active_company_rates(conn, company.id)
    highest_rate = get_highest_rate([rate for rate in active_rates if rate.rate.is_web])

    if highest_rate and is_unlimited_trial(active_rates):
        logger.info(
            'Deactivating unlimited trial rate before activating new rate',
            extra={'bill_id': bill.id_, 'trial_rate_id': highest_rate.id_},
        )
        await _deactivate_rate(
            conn=conn,
            rate=highest_rate,
            active_rates=active_rates,
            new_status=CompanyRateStatus.canceled,
        )
        updated_rates.append(highest_rate)
        active_rates = [rate for rate in active_rates if rate != highest_rate]

    # Filter only rates with from the same rate set as the rate that is being activated
    # because we can extend only rates from the same set and with the same priority.
    rates_to_extend: list[CompanyRate] = []
    if rate.is_web:
        active_rates = [r for r in active_rates if r.rate.is_web]
        rates_to_extend = [r for r in active_rates if r.rate.priority_rank == rate.priority_rank]
    elif rate.is_integration:
        active_rates = [r for r in active_rates if r.rate.is_integration]
        rates_to_extend = [r for r in active_rates if r.rate.priority_rank == rate.priority_rank]
    elif rate.is_archive:
        active_rates = [r for r in active_rates if r.rate.is_archive]
        # Archive rates doesn't have a priority, so we can extend rates with the same type
        rates_to_extend = [r for r in active_rates if r.rate == rate]

    employee_amount = get_bill_service_employee_amount_limit(service, company_edrpou=bill.edrpou)

    if rates_to_extend:
        if not start_date:
            start_date = get_extended_rate_start_date(rates_to_extend)
        status = CompanyRateStatus.new

    else:
        if not start_date:
            start_date = local_now()
        status = CompanyRateStatus.new if start_date > local_now() else CompanyRateStatus.active

    await validate_bill_dates(
        conn=conn,
        company_id=company.id,
        date_from=start_date,
        rate=rate,
    )

    if end_date is None:
        # dd-mm-yyyy 23:59:59
        end_date = get_rate_end_date_from_start_date(start_date)

    if rate.is_ultimate:
        if not bill.custom_price:
            logger.info(
                'Cannot activate ultimate rate without bill custom price specified.',
                extra={'bill_id': bill.id_},
            )
            return updated_rates
        amount = int(bill.custom_price * 100)
        # TODO: Remove condition after June 2025
        if bill.date_created < NOVEMBER_20:
            _price_per_user = bill.single_rate_service.price_per_user
            price_per_user = int(_price_per_user) if _price_per_user else None
        else:
            price_per_user = int(not_none(bill.single_rate_service.price_per_user))

    else:
        amount = int(service.total_price)  # в копійках

    company_rate = CompanyRate(
        id_=str(uuid.uuid4()),
        company_id=company.id,
        company_edrpou=company.edrpou,
        rate=rate,
        status=status,
        bill_id=bill.id_,
        start_date=start_date,
        end_date=end_date,
        amount=amount,
        employee_amount=employee_amount,
        price_per_user=price_per_user,
    )

    await add_system_company_rate(conn, rate=company_rate)
    updated_rates.append(company_rate)

    if any(
        (
            company_rate.rate.is_start,
            company_rate.rate.is_pro,
            company_rate.rate.is_ultimate,
        ),
    ):
        await services.kafka.send_record(
            topics.ESPUTNIK_SEND_EVENT_TO_USERS,
            {
                'event': Event.rate_added,
                'company_id': company_rate.company_id,
                'extra_params': {
                    'rate': RATES_NAME_MAP.get(company_rate.rate, company_rate.rate.value),
                },
            },
        )

    logger.info(
        'Rate activated by user transaction',
        extra={'bill_id': bill.id_, 'rate': rate.value},
    )

    return updated_rates


async def activate_bill_units_service(
    conn: DBConnection,
    bill: Bill,
    service: BillService,
) -> str:
    """Adding documents to account by payment"""

    from api.public.validators import validate_user_exists
    from app.billing.api import activate_debit as _activate_debit

    user = await validate_user_exists(
        conn=conn,
        email=bill.email,
        company_edrpou=bill.edrpou,
        ensure_active_user=False,
    )

    assert service.type == 'units', 'Expected service type is "units"'

    count_documents = service.units

    price_per_document = await get_price_per_document(conn, user.company_id)

    data = {
        'amount': int(count_documents * price_per_document) * 100,
        'role_id': user.role_id,
        'bill_id': bill.id_,
        'company_id': user.company_id,
        'units': count_documents,
    }

    account_id = await _activate_debit(conn, data)

    logger.info(
        'Document count increased by user transaction',
        extra={'bill_id': bill.id_, 'count_documents': count_documents},
    )

    return account_id


async def activate_bill_extension_service(
    conn: DBConnection,
    bill: Bill,
    service: BillServiceExtension,
) -> CompanyRate:
    from app.billing.validators import (
        validate_activate_employee_extension,
        validate_employee_max_limit,
    )

    rate_extension = await select_rate_extension_by_bill_id(conn, bill_id=bill.id_)
    assert rate_extension, 'Expected rate_extension exist'

    result = await validate_activate_employee_extension(
        conn=conn,
        bill=bill,
        rate_extension=rate_extension,
    )
    company_rate = result.company_rate
    old_max_employees_count = result.old_max_employees_count
    validate_employee_max_limit(old_max_employees_count + service.units)

    # Change status for delayed activation
    if (
        rate_extension.planned_activation_date
        and rate_extension.planned_activation_date >= utc_now()
    ):
        logger.info(
            'Rate extension planned activation',
            extra={
                'rate_extension_id': rate_extension.id,
                'planned_activation_date': rate_extension.planned_activation_date,
                'current_max_employees_count': old_max_employees_count,
                'company_id': company_rate.company_id,
            },
        )
        await update_rate_extension(
            conn,
            rate_extension_ids=[rate_extension.id],
            update_data={'status': RateExtensionStatus.wait_activation_date},
        )
    else:
        # Activation
        await activate_company_employees_extension(
            conn=conn,
            bill=bill,
            company_id=company_rate.company_id,
            old_max_employees_count=old_max_employees_count,
            rate_extension=rate_extension,
        )

    # It's an ultimate rate, it wasn't created by this function, but we need to return it
    # to send updated data to CRM
    return company_rate


async def cancel_rate_extension_bill(
    conn: DBConnection, rate_extension: RateExtension, edrpou: str
) -> CompanyRate | None:
    """
    NOTE: Call this function in transaction -> async with conn.begin():
    """
    from app.billing.validators import (
        validate_active_ultimate_rate,
        validate_company_max_employees_count,
    )

    new_extension_status = RateExtensionStatus.canceled

    if rate_extension.status not in (RateExtensionStatus.active, RateExtensionStatus.active_trial):
        await update_rate_extension(
            conn,
            rate_extension_ids=[rate_extension.id],
            update_data={'status': new_extension_status},
        )
        return None

    ultimate = await validate_active_ultimate_rate(conn, edrpou=edrpou)
    if not ultimate.employee_amount:
        raise InvalidRequest(reason=_('Employee amount is required to deactivate rate extension'))

    company_id = ultimate.company_id
    active_rates = await select_active_company_rates(conn, company_id)

    if any(r.rate == AccountRate.pro_plus_trial_2022_12 for r in active_rates):
        raise InvalidRequest(reason=_('Cannot deactivate rate extension during active web trial'))
    if not rate_extension.config:
        raise InvalidRequest(reason=_('Rate extension config is not found'))

    company_config_employees_limit = await validate_company_max_employees_count(conn, edrpou)
    new_limit = company_config_employees_limit - rate_extension.config.units

    if new_limit < ultimate.employee_amount:
        raise InvalidRequest(
            reason=_('Кількість користувачів не може бути меншою за вказану у тарифі')
        )

    # Deactivation
    await update_billing_company_config(
        conn=conn,
        company_id=company_id,
        config={CompanyLimit.employees.value: new_limit},
    )
    await update_rate_extension(
        conn,
        rate_extension_ids=[rate_extension.id],
        update_data={'status': new_extension_status},
    )
    await process_extra_roles(conn, company_id=company_id)
    logger.info(
        'Change company max_employees_count',
        extra={
            'initiator_type': 'superadmin',
            'rate_extension_id': rate_extension.id,
            'new_extension_status': new_extension_status,
            'company_id': company_id,
            'old_max_employees_count': company_config_employees_limit,
            'new_max_employees_limit': new_limit,
        },
    )
    return ultimate


async def activate_company_employees_extension(
    conn: DBConnection,
    bill: Bill,
    company_id: str,
    old_max_employees_count: int,
    rate_extension: RateExtension,
) -> None:
    assert bill.services_type == BillServicesType.add_employee
    service = bill.single_extension_service
    add_employees_count = service.units

    # Just update status, because employees limit already increased
    if rate_extension.status == RateExtensionStatus.active_trial:
        await update_rate_extension(
            conn,
            rate_extension_ids=[rate_extension.id],
            update_data={'status': RateExtensionStatus.active},
        )
        logger.info(
            'Change rate extension status from trial to active',
            extra={
                'rate_extension_id': rate_extension.id,
                'new_extension_status': RateExtensionStatus.active,
                'company_id': company_id,
                'max_employees_count': old_max_employees_count,
            },
        )
        return

    new_max_employees_limit = old_max_employees_count + add_employees_count

    async with conn.begin():
        await update_billing_company_config(
            conn=conn,
            company_id=company_id,
            config={CompanyLimit.employees.value: new_max_employees_limit},
        )
        await update_rate_extension(
            conn,
            rate_extension_ids=[rate_extension.id],
            update_data={'status': RateExtensionStatus.active},
        )
        await process_extra_roles(conn, company_id=company_id)
        logger.info(
            'Change company max_employees_count',
            extra={
                'initiator_type': 'rate_extension',
                'rate_extension_id': rate_extension.id,
                'new_extension_status': RateExtensionStatus.active,
                'company_id': company_id,
                'old_max_employees_count': old_max_employees_count,
                'new_max_employees_limit': new_max_employees_limit,
            },
        )


async def deactivate_company_users_extensions(conn: DBConnection, account_id: str) -> None:
    await update_rate_extension(
        conn, account_id=account_id, update_data={'status': RateExtensionStatus.deactivated_rate}
    )


async def calculate_archive_start_date(
    conn: DBConnection, bill: Bill, updated_rates: list[CompanyRate]
) -> datetime.datetime | None:
    """
    Calculate start date for archive rate
    which should be activated together with web rate from same bill, if it possible.
    If case when active archive rate already exists -
        activate new archive after the end of the existing one.
    """
    if not bill.services_type.is_web_and_archive:
        return None
    web_rate = next(r for r in updated_rates if r.bill_id == bill.id_ and r.rate.is_web)
    if web_rate and web_rate.status == CompanyRateStatus.new:
        # skip type check because we expected service_type web_and_archive
        archive_rate = next(r.rate for r in bill.services if r.rate.is_archive)  # type: ignore
        active_archive = await select_latest_active_company_rate(
            conn=conn,
            company_id=web_rate.company_id,
            rates=[archive_rate],
        )

        if not active_archive:
            return web_rate.start_date
    return None


async def activate_service_by_bill_payment(
    conn: DBConnection,
    bill: Bill,
    source: BillActivationSource,
    start_date: datetime.datetime | None = None,
    end_date: datetime.datetime | None = None,
) -> None:
    """
    Activate services after bill payment is completed
    """

    updated_rates: list[CompanyRate] = []

    logger.info(
        'Activate services by bill payment start',
        extra={'bill_id': bill.id_, 'source': source.value, 'bill_services': str(bill.services)},
    )
    async with conn.begin():
        for service in bill.services:
            if service.type == BillServiceType.rate:
                # If we have a web and archive service,
                # we need to activate both rates at the same time (if it possible)
                if (
                    service.rate.is_archive
                    and bill.services_type.is_web_and_archive
                    and not start_date
                ):
                    # archive always goes after web rate in (bill.services),
                    # so we expect updated_rates list contains single item - web rate
                    start_date = await calculate_archive_start_date(conn, bill, updated_rates)

                updated_company_rates = await activate_bill_rate_service(
                    conn=conn,
                    bill=bill,
                    service=service,
                    start_date=start_date,
                    end_date=end_date,
                )
                if updated_company_rates:
                    updated_rates.extend(updated_company_rates)

            elif service.type == BillServiceType.units:
                await activate_bill_units_service(conn, bill=bill, service=service)
            elif service.type == BillServiceType.extension:
                company_rate = await activate_bill_extension_service(
                    conn=conn,
                    bill=bill,
                    service=service,
                )
                updated_rates.append(company_rate)
            else:
                assert_never(service.type)

        await update_bill(
            conn=conn,
            bill_ids=[bill.id_],
            data={'payment_status': BillPaymentStatus.completed.value},
        )

    # Send all updated rates to CRM.
    # Pay attention that we don't need to send bills to CRM that was used to create and activate
    # rates because we are sending all required information about a bill with those rates.
    for company_rate in updated_rates:
        await send_rate_to_crm(account_id=company_rate.id_)

    # In other cases when there is no updated rates send a bill manually.
    # Also, we have a special case for "add_employee" service type, because we don't have a direct
    # relation between ultimate rate and "add_employee" bill, so we need to send this bill
    # manually.
    if not updated_rates or bill.services_type == BillServicesType.add_employee:
        await send_bill_to_crm(bill_id=bill.id_)


async def process_evopay_payment(conn: DBConnection, bill_id: str) -> None:
    """Processing payment and enabling/adding rates/documents to account"""
    from app.billing.validators import validate_bill_exists_by_id

    logger.info('Payment received', extra={'bill_id': bill_id})

    bill = await validate_bill_exists_by_id(conn, bill_id)
    try:
        await activate_service_by_bill_payment(
            conn=conn, bill=bill, source=BillActivationSource.evopay
        )
    except (InvalidRequest, DoesNotExist) as error:
        # Add logging to investigate track evopay errors
        logger.warning(
            'Fail on evopay bill payment activation',
            extra={
                'reason': error.reason,
                'bill_id': bill_id,
            },
        )


async def calculate_extend_rate_dates(
    conn: DBConnection, company_id: str, data: DataDict
) -> tuple[datetime.datetime, datetime.datetime]:
    rate = AccountRate(data['rate'])
    active_rates = await select_active_company_rates(
        conn=conn,
        company_id=company_id,
        rate=rate,
    )
    if active_rates:
        try:
            start_date = max(
                [company_rate.end_date for company_rate in active_rates if company_rate.end_date]
            )
        except ValueError:
            raise InvalidRequest(
                reason=_(
                    'Неможливо подовжити поточний тариф через відсутність кінцевої дати.',
                ),
            )
    else:
        start_date = local_now()

    end_date = get_rate_end_date_from_start_date(start_date)

    return start_date, end_date


async def get_billing_user_options(
    user: BaseUser | User | None,
    data: CreateBillSchema,
) -> UserAddBillOptions:
    """
    Get user options for adding a bill. For authenticated users, we return also ID of a related
    company and role.
    """
    async with services.db_readonly.acquire() as conn:
        if not user:
            company = await select_company(conn=conn, edrpou=data.edrpou)
            return UserAddBillOptions(
                email=data.email,
                edrpou=data.edrpou,
                name=data.name,
                company_id=company.id if company else None,
            )

        role = await select_role_by(
            conn=conn,
            company_edrpou=data.edrpou,
            mixed=data.email,
        )

    if not role:
        raise AccessDenied(
            reason=_('Будь ласка, вкажіть ЄДРПОУ/ІПН компанії, до якої Ви маєте доступ')
        )

    return UserAddBillOptions(
        email=role.user_email,
        edrpou=role.company_edrpou,
        name=role.company_name,
        role_id=role.id_,
        user_id=role.user_id,
        company_id=role.company_id,
    )


async def create_payment_status_transaction(
    conn: DBConnection,
    bill_id: str,
    payment_status: BillPaymentStatus,
    source: BillPaymentSource,
    status_code: int | None = None,
) -> PaymentTransaction:
    from app.billing.validators import validate_bill_exists_by_id

    bill = await validate_bill_exists_by_id(conn, bill_id)

    data = {
        'bill_id': bill.id_,
        'company_id': bill.company_id,
        'role_id': bill.role_id,
        'payment_status': payment_status,
        'status_code': status_code,
        'source': source,
    }

    transaction = await insert_payment_transaction(
        conn=conn,
        data=data,
    )
    if payment_status != BillPaymentStatus.completed:
        # "completed" status set in end of transaction,
        # after success activate bill services
        await update_bill(
            conn=conn,
            bill_ids=[bill.id_],
            data={'payment_status': payment_status.value},
        )
    logger.info(
        'New entry for bill in payment_status_transactions',
        extra={
            'bill_id': bill_id,
            'payment_status': payment_status.value,
            'status_code': status_code,
            'transaction_id': transaction.id_,
            'company_id': transaction.company_id,
        },
    )

    return transaction


def calculate_amount_on_adding_employees(
    rate: CompanyRate, employee_amount: int, price_per_employee: int
) -> int:
    """
    Adds up old price from rate + new recalculated price for adding employees
    """
    price = int(rate.amount) + employee_amount * price_per_employee
    return price


async def get_entity_expiring_event(
    conn: DBConnection,
    charge_type: ChargeType,
    company_id: str,
) -> tuple[Event | None, float]:
    active_debits = await select_company_active_debits(conn, company_id)
    active_bonuses = await select_company_active_bonuses(conn, company_id)

    event = None
    percentage = 100.0

    if charge_type.is_web:
        web_balance = sum([bonus.units for bonus in active_bonuses])
        web_balance_left = sum([bonus.units_left for bonus in active_bonuses])

        # Company doesn't have any balance
        if web_balance == 0:
            return event, 0

        percentage = 100 * (web_balance_left / web_balance)

        if percentage == 0:
            event = Event.documents_run_out
        elif percentage <= 15:
            event = Event.documents_expires_15p

    elif charge_type.is_api:
        integration_balance = sum([debit.units for debit in active_debits])
        integration_balance_left = sum([debit.units_left for debit in active_debits])

        # Company doesn't have any balance
        if integration_balance == 0:
            return event, 0

        percentage = 100 * (integration_balance_left / integration_balance)

        if percentage == 0:
            event = Event.integration_run_out
        elif percentage <= 15:
            event = Event.integration_expires_15p

    return event, percentage


def get_rate_price(rate: AccountRate, *, edrpou: str) -> float:
    """
    Get rate price based on rate and company edrpou. There are three types of prices for rates:
     - common prices for FOP and TOV companies (RATES_PRICE_MAP)
     - prices for FOP companies (RATES_PRICE_MAP_FOP)
     - prices for TOV companies (RATES_PRICE_MAP_TOV)

    Returns in "гривнях". Examples: 1800.00 = 1800.00 UAH/рік
    """

    if rate in RATES_PRICE_MAP:
        return float(RATES_PRICE_MAP[rate])

    if is_fop(edrpou):
        return float(RATES_PRICE_MAP_FOP[rate])

    return float(RATES_PRICE_MAP_TOV[rate])


async def get_documents_price(count_documents: int, edrpou: str | None = None) -> float:
    """Get document price based on count_documents and company edrpou"""
    async with services.db.acquire() as conn:
        price_per_document = await get_price_per_document(conn=conn, company_edrpou=edrpou)

    # В гривнях. Examples: "100.12", "20.25", ...
    return float(to_decimal(count_documents * price_per_document))


def get_ultimate_rate_extensions_to_buy(
    company_rates: list[CompanyRate],
) -> list[CheckoutExtensionProperties] | None:
    """
    Get available employees rate extensions to buy for ultimate rate
    """

    # Find the first ultimate rate (company can't have 2 active ultimate rates)
    ultimate_rate = next((rate for rate in company_rates if rate.rate.is_ultimate), None)
    if not ultimate_rate:
        return None

    if not ultimate_rate.price_per_user:
        logger.warning('Ultimate rate has no price_per_user', extra={'rate_id': ultimate_rate.id_})
        return None

    # Calculate prices
    assert ultimate_rate.end_date  # To avoid mypy error
    price_per_user_base, price_per_user_total = get_employee_price(
        price_per_user=ultimate_rate.price_per_user,
        rate_end_date=ultimate_rate.end_date,
    )

    employee = CheckoutExtensionProperties(
        type=RateExtensionType.employees,
        price_per_unit_base=price_per_user_base,
        price_per_unit_total=price_per_user_total,
    )
    return [employee]


def get_web_rate_properties_for_checkout(
    active_rates: list[CompanyRate],
    company_edrpou: str,
) -> list[CheckoutWebRateProperties]:
    """
    Get web rate properties for checkout
    """
    web_rates = [rate for rate in active_rates if rate.rate.is_web]

    highest_web_rate = get_highest_rate(active_rates=web_rates)
    highest_web_rate_rank = (
        highest_web_rate.rate.priority_rank
        if highest_web_rate
        # The "free" rate is the lowest rate in a web rate set, so if the company doesn't
        # have any rates, we can offer to buy the next web rate after it.
        else AccountRate.free.priority_rank
    )

    rate_to_extend: AccountRate | None = None
    if (
        highest_web_rate
        and highest_web_rate.rate != AccountRate.free
        and calculate_active_rate_left_days(highest_web_rate) < DAYS_ALLOWANCE_TO_EXTEND_RATE
    ):
        rate_to_extend = highest_web_rate.rate

    free_rate = AccountRate.latest_free()
    start_rate = AccountRate.latest_start()
    pro_rate = AccountRate.latest_pro()
    ultimate_rate = AccountRate.latest_ultimate()

    can_buy_start = start_rate.priority_rank > highest_web_rate_rank
    can_buy_pro = pro_rate.priority_rank > highest_web_rate_rank
    can_buy_ultimate = ultimate_rate.priority_rank > highest_web_rate_rank

    can_extend_start = bool(rate_to_extend and rate_to_extend.is_start)
    can_extend_pro = bool(rate_to_extend and rate_to_extend.is_pro)
    can_extend_ultimate = bool(rate_to_extend and rate_to_extend.is_ultimate)

    free_limits = get_rate_config_limits(free_rate, edrpou=company_edrpou)
    start_limits = get_rate_config_limits(start_rate, edrpou=company_edrpou)
    pro_limits = get_rate_config_limits(pro_rate, edrpou=company_edrpou)
    ultimate_limits = get_rate_config_limits(ultimate_rate, edrpou=company_edrpou)

    documents_to_view_free = free_limits[CompanyLimit.documents]
    documents_to_view_start = start_limits[CompanyLimit.documents]
    documents_to_view_pro = pro_limits[CompanyLimit.documents]
    documents_to_view_ultimate = ultimate_limits[CompanyLimit.documents]

    extensions_ultimate = get_ultimate_rate_extensions_to_buy(company_rates=active_rates)

    documents_to_send_free = get_rate_config_units(free_rate, edrpou=company_edrpou)
    documents_to_send_start = get_rate_config_units(start_rate, edrpou=company_edrpou)
    documents_to_send_pro = get_rate_config_units(pro_rate, edrpou=company_edrpou)

    return [
        CheckoutWebRateProperties.build(
            rate=free_rate,
            can_buy=False,
            can_extend=False,
            price_per_year=None,
            price_per_month=None,
            documents_to_send=documents_to_send_free,
            kep_keys=0,
            antivirus=True,
            cloud_storage=True,
            documents_to_view=documents_to_view_free,
            extensions=None,
            company_edrpou=company_edrpou,
        ),
        CheckoutWebRateProperties.build(
            rate=start_rate,
            can_buy=can_buy_start,
            can_extend=can_extend_start,
            price_per_year=get_rate_price(start_rate, edrpou=company_edrpou),
            price_per_month=get_rate_price(start_rate, edrpou=company_edrpou) // 12,
            documents_to_send=documents_to_send_start,
            kep_keys=1,
            antivirus=True,
            cloud_storage=True,
            documents_to_view=documents_to_view_start,
            extensions=None,
            company_edrpou=company_edrpou,
        ),
        CheckoutWebRateProperties.build(
            rate=pro_rate,
            can_buy=can_buy_pro,
            can_extend=can_extend_pro,
            price_per_year=get_rate_price(pro_rate, edrpou=company_edrpou),
            price_per_month=get_rate_price(pro_rate, edrpou=company_edrpou) // 12,
            documents_to_send=documents_to_send_pro,
            kep_keys=2,
            antivirus=True,
            cloud_storage=True,
            documents_to_view=documents_to_view_pro,
            extensions=None,
            company_edrpou=company_edrpou,
        ),
        CheckoutWebRateProperties.build(
            rate=ultimate_rate,
            can_buy=can_buy_ultimate,
            can_extend=can_extend_ultimate,
            price_per_year=None,
            price_per_month=None,
            documents_to_send=None,
            kep_keys=3,
            antivirus=True,
            cloud_storage=True,
            documents_to_view=documents_to_view_ultimate,
            extensions=extensions_ultimate,
            company_edrpou=company_edrpou,
        ),
    ]


def get_integration_rate_properties_for_checkout(
    active_rates: list[CompanyRate],
    custom_price_per_document: float,
    company_edrpou: str,
) -> list[CheckoutIntegrationRateProperties]:
    """
    Get integration rate properties for checkout
    """
    integration_rate = AccountRate.latest_integration()

    integration_rates = [r for r in active_rates if r.rate.is_integration and not r.rate.is_trial]
    has_integration_rate = bool(integration_rates)

    can_buy = not has_integration_rate
    can_buy_documents = has_integration_rate
    can_extend = (
        has_integration_rate
        and calculate_active_rate_left_days(integration_rates[0]) < DAYS_ALLOWANCE_TO_EXTEND_RATE
    )

    return [
        CheckoutIntegrationRateProperties(
            rate=integration_rate,
            can_buy=can_buy,
            can_extend=can_extend,
            can_buy_documents=can_buy_documents,
            price_per_year=get_rate_price(integration_rate, edrpou=company_edrpou),
            price_per_document=custom_price_per_document,
            signing_documents=True,
            comments=True,
            tarification=True,
        )
    ]


def get_archive_rate_properties_for_checkout(
    active_rates: list[CompanyRate],
    company_edrpou: str,
) -> list[CheckoutArchiveRateProperties]:
    """
    Get archive rate properties for checkout
    """
    if not get_flag(FeatureFlags.ENABLE_ARCHIVE_RATES):
        return []

    archive_rates = [r for r in active_rates if r.rate.is_archive]

    can_buy = not archive_rates
    can_extend = False  # not implemented yet

    return [
        CheckoutArchiveRateProperties.build(
            rate=AccountRate.archive_small,
            can_buy=can_buy,
            can_extend=can_extend,
            company_edrpou=company_edrpou,
        ),
        CheckoutArchiveRateProperties.build(
            rate=AccountRate.archive_big,
            can_buy=can_buy,
            can_extend=can_extend,
            company_edrpou=company_edrpou,
        ),
    ]


def get_checkout_properties(
    active_rates: list[CompanyRate],
    custom_price_per_document: float,
    company_edrpou: str,
) -> CheckoutProperties:
    """
    Get rate properties for checkout for each rate set: web, integration, archive
    """
    rates_web = get_web_rate_properties_for_checkout(
        active_rates=active_rates,
        company_edrpou=company_edrpou,
    )
    rates_integration = get_integration_rate_properties_for_checkout(
        active_rates=active_rates,
        custom_price_per_document=custom_price_per_document,
        company_edrpou=company_edrpou,
    )
    rates_archive = get_archive_rate_properties_for_checkout(
        active_rates=active_rates,
        company_edrpou=company_edrpou,
    )

    return CheckoutProperties(
        rates_web=rates_web,
        rates_integration=rates_integration,
        rates_archive=rates_archive,
    )


async def get_billing_company_config(
    conn: DBConnection,
    *,
    company_id: str | None = None,
    company_edrpou: str | None = None,
) -> BillingCompanyConfig:
    """
    Get billing company config by selecting it from a database
    and temporary merging with a company config
    """

    if not company_id and not company_edrpou:
        return BillingCompanyConfig()

    billing_config_db = await select_billing_company_config(
        conn=conn,
        company_id=company_id,
        company_edrpou=company_edrpou,
    )

    return billing_config_db if billing_config_db else BillingCompanyConfig()


async def get_billing_companies_configs(
    conn: DBConnection,
    *,
    companies_ids: list[str],
) -> dict[str, BillingCompanyConfig]:
    """
    Get billing companies configs by selecting them from a database and merging with
    company configs
    """

    billing_configs = await select_billing_companies_configs(
        conn=conn,
        companies_ids=companies_ids,
    )

    configs: dict[str, BillingCompanyConfig] = {}
    for company_id in companies_ids:
        billing_config = billing_configs.get(company_id)
        if not billing_config:
            billing_config = BillingCompanyConfig()

        configs[company_id] = billing_config

    return configs


def is_employee_extension_service(
    service: AddBillServiceOptions,
) -> TypeGuard[AddBillServiceExtensionOptions]:
    return (
        service.type == BillServiceType.extension
        and service.extension == RateExtensionType.employees
    )


async def add_employees_rate_extension(
    conn: DBConnection,
    options: AddBillOptions,
    bill: Bill,
) -> RateExtensionStatus:
    """
    NOTE: The function is expected to be called in the same transaction
        as the bill creation (using con.begin())

    There are 2 ways to create bill:
        - Our manager In CRM
        - client in a personal cabinet

    The function process creating employee rate extension and performs the following steps:

    1. Validates if company has active ultimate rate.
    2. Validates if company has max_employees_count limit
    3. If the bill is created in CRM and billing_account_id and company_id are not provided,
        it get field from database
    4. Create rate extension and auto activate trial if it can, and update company limits
    5. If bill created in CRM, it updates price_per_user for the current Ultimate rate.
        (if bill created by the user, then we use existing price_per_user value
        if bill created in CRM, we overwrite price_per_user,
        cause manager and the client agree on a new price according
        to the number of purchased employee )
    """
    from app.billing.validators import (
        validate_company_max_employees_count,
    )

    assert bill.services_type == BillServicesType.add_employee, (
        'Function activate only add_employee bills'
    )
    service: AddBillServiceExtensionOptions = next(
        s for s in options.services if s.type == BillServiceType.extension
    )

    old_max_employees_count = await validate_company_max_employees_count(conn, options.edrpou)
    new_max_employees_limit = old_max_employees_count + service.units

    extension_status = RateExtensionStatus.pending

    if await can_activate_rate_extension_trial(
        conn, service.date_from, service.company_rate.id_, new_max_employees_limit
    ):
        extension_status = RateExtensionStatus.active_trial
        # Activate trial (update company config)
        await update_billing_company_config(
            conn=conn,
            company_id=service.company_rate.company_id,
            config={CompanyLimit.employees.value: new_max_employees_limit},
        )
        await process_extra_roles(conn, company_id=service.company_rate.company_id)
        logger.info(
            'Change company max_employees_count',
            extra={
                'initiator_type': 'rate_extension',
                'extension_status': extension_status,
                'company_id': service.company_rate.company_id,
                'edrpou': options.edrpou,
                'old_max_employees_count': old_max_employees_count,
                'new_max_employees_limit': new_max_employees_limit,
                'bill_id': bill.id_,
                'email': options.email,
            },
        )

    extension_data = {
        'account_id': service.company_rate.id_,
        'bill_id': bill.id,
        'status': extension_status,
        'type': RateExtensionType.employees,
        'planned_activation_date': service.date_from,
        'date_expiring': utc_now() + datetime.timedelta(days=RATE_EXTENSION_TRIAL_DAYS),
        'config': RateExtensionConfig(units=service.units).to_dict(),
    }
    await insert_rate_extension(conn, extension_data)

    if options.source == BillSource.creatio:
        # Update price per user for current Ultimate rate
        # Convert Decimal to int to store in DB
        await update_price_per_user_in_company_rate(
            conn, service.company_rate.id_, service.unit_price
        )

    return extension_status


async def get_rate_extension_employee_units(conn: DBConnection, rate_id: str) -> int:
    """Return summary units for all active employee rate extensions"""
    additional_employees_extensions_limit = 0
    extensions_ids_without_units = []

    rate_extensions = await select_rate_extensions(
        conn,
        account_id=rate_id,
        type=RateExtensionType.employees,
        statuses=[RateExtensionStatus.active_trial, RateExtensionStatus.active],
    )
    for ext in rate_extensions:
        # if ext.config - only for old add_employee extensions which created without config info,
        # and store units count in bill.
        # This bills can be activated no later than 6 months after creation
        # TODO: Remove this condition 6 month after release
        if ext.config:
            additional_employees_extensions_limit += ext.config.units
        else:
            extensions_ids_without_units.append(ext.bill_id)
    if extensions_ids_without_units:
        bills = await select_bills_by_ids(conn, bills_ids=extensions_ids_without_units)
        additional_employees_extensions_limit += sum(
            bill.single_extension_service.units for bill in bills
        )
    return additional_employees_extensions_limit


async def calculate_employee_extension_units(
    conn: DBConnection,
    active_rates: list[CompanyRate],
    limits: RateConfigLimits,
) -> None:
    ultimate_rate = next((r for r in active_rates if r.rate.is_ultimate), None)

    if not ultimate_rate:
        return

    additional_employees_extensions_limit = await get_rate_extension_employee_units(
        conn, ultimate_rate.id_
    )

    if limits.get(CompanyLimit.employees) and additional_employees_extensions_limit:
        limits[CompanyLimit.employees] += additional_employees_extensions_limit  # type: ignore


async def is_employee_limit_reached(conn: DBConnection, company_id: str) -> bool:
    """
    Check if the company has reached limit of employees
    """

    config = await get_billing_company_config(conn=conn, company_id=company_id)
    employees_limit = get_rate_limit(config, CompanyLimit.employees)

    # Ultimate
    if employees_limit is None:
        return False

    count = await count_company_roles_for_billing(conn, company_id)
    return count >= employees_limit


def get_rate_end_date_from_start_date(start_date: datetime.datetime) -> datetime.datetime:
    """
    Start date + 1 year - 1 day at 23:59:59 in local timezone
    """
    return to_local_datetime(
        (start_date + relativedelta(years=1) - relativedelta(days=1)).replace(
            hour=23, minute=59, second=59
        )
    )


def get_extended_rate_start_date(rates_to_extend: list[CompanyRate]) -> datetime.datetime:
    """
    Extended rates always begin at midnight on the day
    following the end of the previous rate.
    """
    date_max = max([rate.end_date for rate in rates_to_extend if rate.end_date])
    date_local = to_local_datetime(date_max)  # make sure that calculation in Kyiv TZ
    date_next = date_local + relativedelta(days=1)
    date_midnight = midnight(date_next)
    return date_midnight


async def get_bill_resources(bill: Bill) -> BillResources:
    rates = []
    document_units = None
    rate_extension = None

    async with services.db.acquire() as conn:
        if bill.payment_status == BillPaymentStatus.completed:
            if bill.services_type in (BillServicesType.rate, BillServicesType.web_and_archive):
                rates = await select_rates_by_bill_id(conn, bill.id_)

            elif bill.services_type == BillServicesType.integration_and_documents:
                rates = await select_rates_by_bill_id(conn, bill.id_)
                document_units = bill.units_services[0].units

            elif bill.services_type == BillServicesType.documents:
                document_units = bill.single_units_service.units

            elif bill.services_type == BillServicesType.add_employee:
                rate_extension = await select_rate_extension_by_bill_id(conn, bill.id_)

        # single case when need cancel resources when bill not completed - trial for rate extension
        elif bill.services_type == BillServicesType.add_employee:
            rate_extension = await select_rate_extension_by_bill_id(conn, bill.id_)

    logger.info(
        'Bill resources',
        extra={
            'bill_id': bill.id_,
            'rates': rates,
            'document_units': document_units,
            'rate_extension': rate_extension,
        },
    )

    return BillResources(
        rates=rates,
        document_units=document_units,
        rate_extension=rate_extension,
    )


async def cancel_bill_resources(
    conn: DBConnection,
    *,
    bill_resources: BillResources,
    edrpou: str,
    initiator: User,
) -> list[CompanyRate]:
    updated_rates = []

    from app.billing.api import cancel_debit, delete_company_rate
    from app.profile.validators import validate_company_by_edrpou_exists

    for rate in bill_resources.rates:
        assert initiator
        await delete_company_rate(conn, rate=rate, initiator=initiator)
        updated_rates.append(rate)

    if bill_resources.rate_extension:
        ultimate_rate = await cancel_rate_extension_bill(
            conn, bill_resources.rate_extension, edrpou
        )
        if ultimate_rate:
            updated_rates.append(ultimate_rate)

    if bill_resources.document_units:
        assert initiator
        company = await validate_company_by_edrpou_exists(conn, edrpou)
        await cancel_debit(
            conn,
            data={
                'role_id': initiator.role_id,
                'company_id': company.id,
                'units': bill_resources.document_units,
                'comment': 'Canceled bill with resources',
            },
        )

    return updated_rates


async def cancel_bill(
    conn: DBConnection,
    bill: Bill,
    payment_status: BillPaymentStatus,
    initiator: User,
) -> None:
    await update_bill(
        conn=conn,
        bill_ids=[bill.id_],
        data={'payment_status': payment_status.value},
    )

    await insert_super_admin_action(
        conn,
        SuperAdminActionType.cancel_bill,
        initiator,
        [{'bill_id': bill.id_, 'status': payment_status.value}],
    )
