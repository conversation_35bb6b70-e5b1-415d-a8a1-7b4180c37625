from aiohttp import web

from api.errors import TooManyRequests
from app.auth.db import (
    delete_telegram_id,
    exists_telegram_chat_id,
    select_base_user,
    set_telegram_chat_id_by_phone,
)
from app.lib import utm_params
from app.lib.sender.client import EvoSenderError
from app.lib.sender.enums import SenderMessageType
from app.lib.sender.utils import send_phone_otp_code, validate_send_phone_otp_rate_limit
from app.lib.types import DataDict
from app.lib.urls import build_static_url, build_url
from app.services import services
from app.telegram.enums import DispatcherStatus
from app.telegram.utils import (
    create_telegram_code,
    get_chat_id,
    get_contact_phone,
    get_telegram_code,
    get_text,
    make_inline_button,
    reset_telegram_code,
    send_message,
    send_photo,
)
from app.telegram.validators import (
    validate_code_verification,
    validate_phone_for_telegram,
)

INSTRUCTION_IMAGE = 'images/screenshots/telegram_add_phone_instruciton.png'


async def on_unsubscribe(
    app: web.Application,
    message: DataDict,
) -> web.Response:
    """/unsubscribe command for telegram bot
    It deletes any subscription to notification.
    """
    chat_id = get_chat_id(message)
    if chat_id is None:
        raise AttributeError('No chat_id in message')

    async with app['db'].acquire() as conn:
        await delete_telegram_id(conn, chat_id)

    msg = 'Ви більше не будете отримувати сповіщень!'
    return await send_message(chat_id=chat_id, text=msg)


async def on_contact(app: web.Application, message: DataDict) -> web.Response:
    """This command activates when user replies on keyboard
    contact request after /start command. It adds a subscription to
    notification in Telegram.
    """
    phone_number = get_contact_phone(message)

    if phone_number is None:
        raise AttributeError('No phone_number in contact')
    message['text'] = phone_number
    return await on_phone(app, message)


async def on_start(app: web.Application, message: DataDict) -> web.Response:
    """This function activate when user enter /start command.
    It sends greeting message and ask to share contact for subscription using
    ReplyButton.
    """
    chat_id = get_chat_id(message)
    if chat_id is None:
        raise AttributeError('No chat_id in message')

    # Greeting message
    await send_message(
        chat_id=chat_id,
        text=(
            'Вітаю! \nЯ бот, що повідомляє про події з вашими документами у '
            'сервісі <b>"Вчасно"</b>.'
        ),
    )

    return await on_other(message, subscription_check=False)


async def on_phone(app: web.Application, message: DataDict) -> web.Response:
    """
    This function activates when user send phone number just typing in input
    field.
    """
    chat_id = get_chat_id(message)
    phone = validate_phone_for_telegram(get_text(message))

    async with app['db'].acquire() as conn:
        has_subscription = await exists_telegram_chat_id(conn, chat_id)

    # Just ignore any messages when subscription exists
    if has_subscription:
        return await send_message(
            chat_id=chat_id,
            text=('Ви вже зареєстрували свій номер і вам більше не потрібно реєструватися.'),
            reply_markup={'remove_keyboard': True},
        )

    # Ask user again to provide phone number
    if not phone:
        return await on_other(message, subscription_check=False)

    async with app['db'].acquire() as conn:
        user = await select_base_user(conn, phone=phone)
    # If phone doesn't registered in vchasno
    if not user:
        msg = (
            'Йой, такого номеру не знайдено у "Вчасно". Щоб почати отримувати '
            'сповіщення про документи, додайте ваш телефон в обліковий запис '
            'Вчасно та підтвердьте його.'
        )
        utm = utm_params.TELEGRAM_ADD_PHONE
        url = build_url('app', tail='/settings', get=utm)
        button_text = 'Перейти до облікового запису у Вчасно'
        return await send_message(
            chat_id=chat_id,
            text=msg,
            reply_markup=make_inline_button(button_text, url),
        )

    try:
        await validate_send_phone_otp_rate_limit(user_id=user.id)
    except TooManyRequests:
        return await send_message(
            chat_id=chat_id,
            text=(
                'Ви перевищили ліміт запитів на надсилання коду підтвердження. '
                'Будь ласка, спробуйте пізніше.'
            ),
        )

    # Send sms with verification code
    code = await create_telegram_code(app, chat_id=chat_id, phone=phone)
    try:
        await send_phone_otp_code(
            phone=phone,
            otp=code,
            message_type=SenderMessageType.telegram_bot,
        )
    except EvoSenderError:
        return await send_message(
            chat_id,
            text=('Нам не вдалося надіслати СМС на ваш телефон, спробуйте ввести його ще раз.'),
        )

    # Save phone to Redis
    key = f'telegram-subscription-{chat_id}'
    valid_window = services.config.auth.totp_interval
    await app['redis'].setex(key, time=valid_window, value=phone)

    return await send_message(
        chat_id,
        text=(
            'На ваш телефон було надіслано СМС з кодом для підтвердження '
            'телефону. Введіть, будь ласка, цей код повідомленням нижче.'
        ),
    )


async def on_code(app: web.Application, message: DataDict) -> web.Response:
    """Function activates when code sent and user doesn't have subscription"""
    chat_id = get_chat_id(message)
    code = validate_code_verification(get_text(message))

    async with app['db'].acquire() as conn:
        has_subscription = await exists_telegram_chat_id(conn, chat_id)

    # Just ignore any messages when subscription exists
    if has_subscription:
        return await send_message(
            chat_id=chat_id,
            text=('Ви вже зареєстрували свій номер і вам більше не потрібно реєструватися.'),
            reply_markup={'remove_keyboard': True},
        )

    key = f'telegram-subscription-{chat_id}'
    phone = await app['redis'].get(key)
    if not phone:
        return await on_other(message, subscription_check=False)

    expected_code = await get_telegram_code(app, chat_id=chat_id, phone=phone)
    if not code or expected_code != code:
        return await send_message(
            chat_id,
            text=(
                'Не можемо підтвердити ваш телефон. Впишіть код ще раз '
                'або спробуйте надіслати номер телефону знову.'
            ),
        )
    await reset_telegram_code(app, chat_id=chat_id, phone=phone)

    async with app['db'].acquire() as conn:
        await set_telegram_chat_id_by_phone(conn, phone=phone, chat_id=chat_id)

    # Removing keyboard
    msg = 'Вітаємо! Тепер повідомлення про ваші документи будуть надходити сюди.'
    return await send_message(
        chat_id=chat_id,
        text=msg,
        reply_markup={'remove_keyboard': True},
    )


async def on_other(
    message: DataDict,
    subscription_check: bool = True,
) -> web.Response:
    """This function activates when any previous commands doesn't handle user
    message. Used for providing info about security policy when user try to
    type any info without registering telegram_chat_id in service.
    """
    chat_id = get_chat_id(message)

    if subscription_check:
        async with services.db.acquire() as conn:
            has_subscription = await exists_telegram_chat_id(conn, chat_id)

        # Just ignore any messages when subscription exists
        if has_subscription:
            return web.json_response({'status': DispatcherStatus.ignore.value})

    await send_message(
        chat_id=chat_id,
        text=(
            'Щоб знайти вас у Вчасно ми просимо передати нам номер, натиснувши'
            ' кнопку внизу екрана "Відправити телефон" або увести телефон у '
            'форматі +380ХХХХХХХХХ.'
        ),
        reply_markup={
            'keyboard': [
                [
                    {
                        'text': 'Відправити телефон',
                        'request_contact': True,
                    }
                ]
            ],
            'resize_keyboard': True,
            'one_time_keyboard': True,
        },
    )

    image_url = build_static_url(INSTRUCTION_IMAGE)
    return await send_photo(chat_id=chat_id, photo=image_url)
