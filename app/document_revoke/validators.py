import logging
from typing import cast

import sqlalchemy as sa
from vchasno_crm.types import DataDict

from api.errors import Code, Does<PERSON>otExist, Error, InvalidRequest, Object
from app.auth.db import select_expected_company
from app.auth.types import AuthUser, AuthUserExtended, User
from app.auth.validators import validate_edrpou, validate_user_permission
from app.document_revoke.db import (
    select_document_revoke_signatures,
    select_document_revokes,
    select_last_internal_document_revoke_signature,
)
from app.document_revoke.schemas import (
    CreateDocumentRevokeSchema,
    DiiaRevokeInitiateSchema,
    DocumentRevokeIdSchema,
    RejectDocumentRevokeSchema,
)
from app.document_revoke.types import (
    AddSignatureDocumentRevokeCtx,
    AddSignatureDocumentRevokeDiiaRequestCtx,
    CreateDocumentRevokeCtx,
    DocumentRevoke,
    DocumentRevokeSignature,
    GetXMLDocumentRevokeCtx,
    RejectDocumentRevokeCtx,
)
from app.document_revoke.utils import (
    download_wrapped_signature_document_revoke_content,
    get_document_revoke_asic_content,
    get_document_revoke_xml_content,
)
from app.documents.tables import company_listing_table
from app.documents.types import Document, DocumentWithUploader
from app.documents.validators import validate_document_access
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib.database import DBConnection
from app.lib.enums import DocumentStatus, SignatureFormat
from app.lib.helpers import normalize_to_composed, not_none, run_sync, safe_filename
from app.lib.validators import validate_pydantic
from app.models import select_all
from app.signatures.enums import ACSK, SignatureSource
from app.signatures.types import Signature
from app.signatures.utils import prepare_signature_container
from app.signatures.validators import (
    AddSignatureRequestSchema,
    validate_add_signature_data,
    validate_add_signature_type,
    validate_allowed_format_by_owner,
    validate_asic_internal,
    validate_first_sign_with_key,
    validate_sign_info,
    validate_sign_info_internal,
    validate_signature_json_data,
    validate_unique_serial_numbers,
    validate_user_edrpou,
)

logger = logging.getLogger(__name__)


async def _get_involved_companies_edrpou(
    conn: DBConnection,
    document_id: str,
) -> list[str]:
    """
    Get all involved companies edrpou for the given document ID.
    """
    involved_companies = await select_all(
        conn,
        (
            sa.select([company_listing_table.c.edrpou.label('edrpou')])
            .select_from(company_listing_table)
            .where(company_listing_table.c.document_id == document_id)
        ),
    )
    return [s.edrpou for s in involved_companies]


async def validate_document_revoke_exists(
    conn: DBConnection,
    *,
    id: str | None = None,
    document_id: str | None = None,
) -> DocumentRevoke:
    """
    Validate if document revokes exist for the given document IDs.
    """
    revokes = await select_document_revokes(
        conn=conn,
        ids=[id] if id else None,
        document_ids=[document_id] if document_id else None,
    )
    if not revokes:
        raise DoesNotExist(obj=Object.revoke)

    return revokes[0]


async def validate_get_xml_document_revoke_content(
    conn: DBConnection,
    user: User | AuthUserExtended,
    raw_data: DataDict,
) -> GetXMLDocumentRevokeCtx:
    valid_data = validate_pydantic(DocumentRevokeIdSchema, raw_data)
    revoke = await validate_document_revoke_exists(
        conn=conn,
        id=valid_data.revoke_id,
    )

    document = await validate_document_access(
        conn=conn,
        user=user,
        document_id=revoke.document_id,
    )

    return GetXMLDocumentRevokeCtx(
        document=document,
        revoke=revoke,
    )


def validate_user_permission_for_revoke(user: User | AuthUser) -> None:
    if get_flag(FeatureFlags.ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS):
        validate_user_permission(
            user, {'can_sign_and_reject_document_external', 'can_delete_document'}
        )
    else:
        validate_user_permission(user, {'can_sign_and_reject_document', 'can_delete_document'})


async def validate_create_document_revoke(
    conn: DBConnection,
    user: User | AuthUserExtended,
    raw_data: DataDict,
) -> CreateDocumentRevokeCtx:
    validate_user_permission_for_revoke(user)

    valid_data = validate_pydantic(CreateDocumentRevokeSchema, raw_data)
    document = await validate_document_access(conn, user, document_id=valid_data.document_id)

    # Only EDI documents can be revoked
    if not document.source.is_from_edi:
        raise InvalidRequest(reason=_('Тільки документи з EDI можуть бути відкликані'))

    # Document should be finished but not revoked already
    if not document.status.is_final or document.status == DocumentStatus.revoked:
        raise InvalidRequest(reason=_('Документ не може бути анулюваний'))

    # Revoke should not exist for the document
    # why not use just document.status == DocumentStatus.revoked?
    # because revoke may be only partially completed
    # so we need to prevent from creating new one
    revokes = await select_document_revokes(
        conn=conn,
        document_ids=[valid_data.document_id],
    )
    if revokes:
        raise InvalidRequest(reason=_('Документ вже має Акт анулювання'))

    return CreateDocumentRevokeCtx(
        document=document,
        reason=valid_data.reason,
        user=user,
    )


async def validate_document_revoke_reject(
    conn: DBConnection,
    user: User | AuthUserExtended,
    raw_data: DataDict,
    revoke: DocumentRevoke | None = None,
) -> RejectDocumentRevokeCtx:
    validate_user_permission_for_revoke(user)

    valid_data = validate_pydantic(RejectDocumentRevokeSchema, raw_data)

    # Make sure revoke exists
    if revoke is None:
        revoke = await validate_document_revoke_exists(
            conn=conn,
            id=valid_data.revoke_id,
        )

    # Make sure document exists and user has access to it
    document = await validate_document_access(
        conn=conn,
        user=user,
        document_id=revoke.document_id,
    )
    # When is not signed by initiator company
    # can be rejected only by initiator company
    # TODO: Or maybe we don't need this validation at all?
    #  and allow revoke rejection by any company?
    if not revoke.status.is_started and user.company_id != revoke.initiator_company_id:
        raise InvalidRequest(reason=_('Акт анулювання ще не підписано іншою стороною'))

    # Finished revoke can't be rejected
    if revoke.status.is_completed:
        raise InvalidRequest(reason=_('Анулювання завершено'))

    return RejectDocumentRevokeCtx(
        revoke=revoke,
        document=document,
        reason=valid_data.reason,
        user=user,
    )


async def _validate_signature_file_document_revoke(
    conn: DBConnection,
    document: DocumentWithUploader | Document,
    revoke_id: str,
    data: AddSignatureRequestSchema,
    signature_container: bytes | None,
    signatures: list[Signature] | list[DocumentRevokeSignature],
    user: User | AuthUser,
) -> DataDict:
    signature_format = not_none(data.format)
    acsk_param = data.acsk
    acsk = ACSK[acsk_param] if acsk_param is not None else ACSK.default

    if signature_format == SignatureFormat.external_separated:
        # Verify & read sign info data from external signature
        original = await get_document_revoke_xml_content(revoke_id)
        valid_data = await validate_sign_info(
            original=original,
            key=data.key,
            stamp=data.stamp,
            acsk=acsk,
        )

    elif signature_format in (
        SignatureFormat.internal_separated,
        SignatureFormat.internal_appended,
    ):
        # Verify & read sign info data from internal signature
        signature = cast(bytes, signature_container)
        original = await get_document_revoke_xml_content(revoke_id)
        valid_data = await validate_sign_info_internal(
            original=original,
            signature=signature,
            signatures=signatures,
        )

    elif signature_format == SignatureFormat.internal_wrapped:
        signature = cast(bytes, signature_container)
        content = await download_wrapped_signature_document_revoke_content(
            conn=conn,
            revoke_id=revoke_id,
        )
        valid_data = await validate_sign_info_internal(
            original=content,
            signature=signature,
            signatures=signatures,
        )

    elif signature_format == SignatureFormat.internal_asic:
        # All ECDSA signatures stored in one asic container
        signature = cast(bytes, signature_container)
        existed_container = await get_document_revoke_asic_content(revoke_id)
        original = await get_document_revoke_xml_content(revoke_id)
        normalized_filename = normalize_to_composed(f'Анулювання_{document.file_name}')
        valid_data = await validate_asic_internal(
            file_name=safe_filename(normalized_filename),
            original=original,
            signature=signature,
            existed_container=existed_container,
        )

    else:
        logger.error(
            msg='Requested signature format validation was not implemented',
            extra={
                'signature_format': signature_format.value,
                'document_id': document.id,
            },
        )
        raise Error(Code.not_implemented)

    await validate_allowed_format_by_owner(
        conn=conn,
        signature_format=signature_format,
        document=document,
    )

    validate_user_edrpou(user.company_edrpou, valid_data['key_owner_edrpou'])
    validate_user_edrpou(user.company_edrpou, valid_data.get('stamp_owner_edrpou'))

    return valid_data


async def validate_document_revoke_sign(
    conn: DBConnection,
    raw_data: DataDict,
    user: User | AuthUserExtended,
    source: SignatureSource,
    revoke: DocumentRevoke | None = None,
) -> AddSignatureDocumentRevokeCtx:
    validate_user_permission_for_revoke(user)

    # pass revoke_id as document_id to not copy-paste same validation code
    # essentially it has the same logic in terms of data validation
    schema = validate_add_signature_data(
        {
            'format': SignatureFormat.internal_wrapped.value,
            'document_id': raw_data.pop('revoke_id', None),
            'p7s': await run_sync(func=prepare_signature_container, p7s=raw_data.pop('p7s', None)),
            'acsk': raw_data.pop('acsk', None),
            'source': source.value,
            # regular signature data in json format
            **validate_signature_json_data({'data': raw_data.pop('data', None)}),
            # data from the diia (we could pass it inside data field but to avoid
            # extra json encoding/decoding we pass it separately)
            **raw_data,
        }
    )

    revoke_id = schema.document_id

    # Make sure revoke exists
    if revoke is None:
        revoke = await validate_document_revoke_exists(
            conn=conn,
            id=revoke_id,
        )

    # Make sure document exists and user has access to it
    document = await validate_document_access(
        conn=conn,
        user=user,
        document_id=revoke.document_id,
    )
    if revoke.status.is_completed:
        raise InvalidRequest(reason=_('Анулювання завершено'))

    # First signature should be from initiator company
    if not revoke.status.is_started and user.company_id != revoke.initiator_company_id:
        raise InvalidRequest(reason=_('Акт анулювання ще не підписано іншою стороною'))

    signature_container = schema.p7s
    signature_external = schema.key or schema.stamp

    signatures = await select_document_revoke_signatures(
        conn=conn,
        revoke_ids=[revoke.id],
    )
    signature_data = await _validate_signature_file_document_revoke(
        conn=conn,
        data=schema,
        signature_container=signature_container,
        signatures=signatures,
        user=user,
        document=document,
        revoke_id=revoke_id,
    )
    valid_data = {**schema.model_dump(), **signature_data}

    signature_edrpou = valid_data['key_owner_edrpou']
    stamp_edrpou: str | None = valid_data.get('stamp_owner_edrpou')
    if signature_edrpou is None:
        # validate stamp-only signing
        validate_first_sign_with_key(stamp_edrpou, signatures)
        signature_edrpou = stamp_edrpou

    valid_data['is_internal'] = bool(signature_container)
    valid_data['user_role_id'] = user.role_id
    valid_data['user_id'] = user.id
    valid_data['user_email'] = user.email
    if valid_data['archive'] and valid_data['is_internal']:
        valid_data['internal_file_name'] = f'p7s.{valid_data["archive"]}'

    await validate_add_signature_type(
        first_signature=signatures[0] if signatures else None,
        signature_container=signature_container,
        signature_external=signature_external,
        log_extra={'valid_data': valid_data},
    )

    validate_edrpou(signature_edrpou)
    validate_edrpou(stamp_edrpou)

    # Make sure that key/stamp serial number not used before by other coworker
    validate_unique_serial_numbers(
        signatures=signatures,
        key_serial_number=valid_data['key_serial_number'],
        stamp_serial_number=valid_data['stamp_serial_number'],
        signature_edrpou=signature_edrpou,
    )

    involved_companies = await _get_involved_companies_edrpou(
        conn,
        document_id=revoke.document_id,
    )

    return AddSignatureDocumentRevokeCtx(
        document=document,
        revoke=revoke,
        user=user,
        signatures=signatures,
        data=valid_data,
        involved_companies=involved_companies,
    )


async def validate_document_revoke_sign_initiate_diia(
    conn: DBConnection,
    raw_data: DataDict,
    user: AuthUser | User,
    source: SignatureSource,
) -> AddSignatureDocumentRevokeDiiaRequestCtx:
    validate_user_permission_for_revoke(user)

    valid_data = validate_pydantic(DiiaRevokeInitiateSchema, raw_data)

    # Make sure revoke exists
    revoke = await validate_document_revoke_exists(
        conn=conn,
        id=valid_data.revoke_id,
    )

    document = await validate_document_access(
        conn=conn,
        user=user,
        document_id=revoke.document_id,
    )

    # make sure only initiator company can sign the revoke first
    if not revoke.status.is_started:
        if user.company_id and user.company_id != revoke.initiator_company_id:
            raise InvalidRequest(reason=_('Акт анулювання ще не підписано іншою стороною'))
        if user.company_edrpou:
            company = await select_expected_company(conn, company_id=revoke.initiator_company_id)
            if company.edrpou != user.company_edrpou:
                raise InvalidRequest(reason=_('Акт анулювання ще не підписано іншою стороною'))

    return AddSignatureDocumentRevokeDiiaRequestCtx(
        document=document,
        revoke=revoke,
        sign_algo=valid_data.sign_algo,
        user=user,
        source=source,
    )


async def validate_last_internal_signature_document_revoke(
    conn: DBConnection,
    raw_data: DataDict,
    user: User | AuthUserExtended,
) -> DocumentRevokeSignature:
    valid_data = validate_pydantic(DocumentRevokeIdSchema, raw_data)
    revoke = await validate_document_revoke_exists(
        conn=conn,
        id=valid_data.revoke_id,
    )
    await validate_document_access(
        conn=conn,
        user=user,
        document_id=revoke.document_id,
    )
    signature = await select_last_internal_document_revoke_signature(
        conn=conn,
        revoke_id=revoke.id,
    )
    if not signature:
        raise DoesNotExist(obj=Object.signature, revoke_id=revoke.id)

    return signature
