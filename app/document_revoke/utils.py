import base64

from api.downloads.archives import prepare_p7s_file
from api.errors import TemporaryUnavailableError
from app.comments.enums import CommentType
from app.comments.utils import create_comment
from app.document_revoke.db import (
    delete_document_revokes,
    insert_document_revoke,
    insert_document_revoke_signature,
    select_last_internal_document_revoke_signature,
    update_document_revoke,
)
from app.document_revoke.enums import DocumentRevokeStatus
from app.document_revoke.types import (
    AddSignatureDocumentRevokeCtx,
    CreateDocumentRevokeCtx,
    DocumentRevoke,
    DocumentRevokeSignature,
    RejectDocumentRevokeCtx,
)
from app.documents.db import update_document
from app.es.utils import send_to_indexator
from app.events.document_actions import Action, DocumentAction, add_document_action
from app.lib import edi, s3_utils
from app.lib.database import DBConnection
from app.lib.edi import (
    EDIPayloadHandleDocumentRevokeCompleted,
    EDIPayloadHandleDocumentRevokeNew,
    EDIPayloadHandleDocumentRevokeReject,
    EDIPayloadHandleDocumentRevokeStarted,
)
from app.lib.enums import DocumentStatus, SignatureFormat
from app.lib.eusign_utils import build_container_with_multiple_signatures
from app.lib.helpers import not_none
from app.lib.types import DataDict
from app.lib.uuid import uuid7
from app.services import services
from app.signatures.enums import SignatureAlgo
from worker import topics


def get_document_revoke_s3_key(revoke_id: str) -> str:
    """
    Generate S3 key for document revoke.
    """
    return f'document_revoke/{revoke_id}'


def get_document_revoke_signatures_internal_s3_key(*, revoke_id: str, signature_id: str) -> str:
    return f'document_revoke/{revoke_id}/signatures/internal/{signature_id}'


def get_document_revoke_signatures_external_s3_key(*, revoke_id: str, signature_id: str) -> str:
    return f'document_revoke/{revoke_id}/signatures/external/{signature_id}'


def get_document_revoke_asic_s3_key(revoke_id: str) -> str:
    return f'document_revoke/{revoke_id}/asic'


def get_document_revoke_external_stamp_signature_s3_key(
    *, revoke_id: str, signature_id: str
) -> str:
    return f'document_revoke/{revoke_id}/signatures/stamp/{signature_id}'


async def get_document_revoke_xml_content(revoke_id: str) -> bytes:
    """
    Get the XML content of the document revoke from S3.
    """
    s3_key = get_document_revoke_s3_key(
        revoke_id=revoke_id,
    )
    content, _ = await s3_utils.download(s3_utils.DownloadFile(key=s3_key))
    return content


async def get_document_revoke_asic_content(revoke_id: str) -> bytes:
    """
    Get the ASiC content of the document revoke from S3.
    """
    s3_key = get_document_revoke_asic_s3_key(revoke_id=revoke_id)
    content, _ = await s3_utils.download(s3_utils.DownloadFile(key=s3_key))
    return content


async def get_document_revoke_signatures_internal_content(
    *, revoke_id: str, signature_id: str
) -> bytes:
    """
    Get the internal signature of the document revoke from S3.
    """
    s3_key = get_document_revoke_signatures_internal_s3_key(
        revoke_id=revoke_id,
        signature_id=signature_id,
    )
    content, _ = await s3_utils.download(s3_utils.DownloadFile(key=s3_key))
    return content


async def add_document_revoke(
    conn: DBConnection,
    ctx: CreateDocumentRevokeCtx,
) -> DocumentRevoke:
    revoke_id = str(uuid7())

    if ctx.is_from_edi:
        content = ctx.content
        signature_format = not_none(ctx.signature_format)
    else:
        client = edi.Client()
        data = await client.request(
            EDIPayloadHandleDocumentRevokeNew(
                document_id=ctx.document.id,
                reason=ctx.reason,
                initiator_edrpou=ctx.user.company_edrpou,
            )
        )
        # base64 encoded string
        xml_document_content: str = data['content']
        content = base64.b64decode(xml_document_content)
        signature_format = SignatureFormat(data['signature_format'])

    if not content or not signature_format:
        raise TemporaryUnavailableError()

    await s3_utils.upload(
        s3_utils.UploadFile(
            key=get_document_revoke_s3_key(revoke_id),
            body=content,
        ),
    )

    return await insert_document_revoke(
        conn=conn,
        id=revoke_id,
        document_id=ctx.document.id,
        initiator_role_id=ctx.user.role_id,
        initiator_company_id=ctx.user.company_id,
        reason=ctx.reason,
        status=DocumentRevokeStatus.created,
        signature_format=signature_format,
    )


async def reject_document_revoke(
    conn: DBConnection,
    ctx: RejectDocumentRevokeCtx,
) -> None:
    async with conn.begin():
        await delete_document_revokes(
            conn=conn,
            ids=[ctx.revoke.id],
        )
        await create_comment(
            conn=conn,
            document_id=ctx.document.id,
            document_version_id=None,
            role_id=ctx.user.role_id,
            type_=CommentType.document_revoke_rejection,
            text=ctx.reason,
            access_company_id=None,
        )
    keys_to_delete = await s3_utils.get_keys_with_prefix(
        f'{get_document_revoke_s3_key(revoke_id=ctx.revoke.id)}/',
    )
    keys_to_delete.append(get_document_revoke_s3_key(revoke_id=ctx.revoke.id))
    await s3_utils.delete_batch(keys_to_delete)
    await services.kafka.send_record(
        topic=topics.SEND_REVOKE_REJECTED_NOTIFICATIONS,
        value={
            'initiator_role_id': ctx.user.role_id,
            'message': ctx.reason,
            'document_ids': [ctx.document.id],
        },
    )
    await services.kafka.send_record(
        topic=topics.EDI_SEND_EDI_REQUEST,
        value=EDIPayloadHandleDocumentRevokeReject(
            document_id=ctx.document.id,
        ).to_dict(),
    )
    await add_document_action(
        DocumentAction(
            action=Action.revoke_reject,
            document_id=ctx.document.id,
            document_edrpou_owner=ctx.document.edrpou_owner,
            document_title=ctx.document.title,
            company_id=ctx.user.company_id,
            company_edrpou=ctx.user.company_edrpou,
            email=ctx.user.email,
            role_id=ctx.user.role_id,
        )
    )


async def _upload_signature_to_s3(
    key: str | None,
    stamp: str | None,
    container: bytes | None,
    revoke_id: str,
    signature: DocumentRevokeSignature,
    signature_algo: SignatureAlgo,
) -> None:
    """
    Upload signature to S3 storage

    NOTE: use this function before or inside a database transaction that inserts a signature.
    Never use it after the transaction is committed or you can end up with a signature in the
    database and no corresponding file in S3.
    """
    pending_uploads: list[s3_utils.UploadFile] = []

    if container is not None:
        # Internal signatures are passed as "container" parameter
        if signature_algo == SignatureAlgo.ECDSA:
            key = get_document_revoke_asic_s3_key(revoke_id)
        else:
            # Regular internal signature
            key = get_document_revoke_signatures_internal_s3_key(
                revoke_id=revoke_id,
                signature_id=signature.id,
            )

        file = s3_utils.UploadFile(
            key=key,
            body=container,
        )
        pending_uploads.append(file)

    else:
        # External signatures are passed as "key" and "stamp" parameters
        if signature.key_exists:
            assert key is not None, 'Key is required for signature that claims to have key'

            key_s3 = get_document_revoke_signatures_external_s3_key(
                revoke_id=revoke_id,
                signature_id=signature.id,
            )
            key_content = base64.b64decode(key)
            file = s3_utils.UploadFile(
                key=key_s3,
                body=key_content,
            )
            pending_uploads.append(file)

        if signature.stamp_exists:
            assert stamp is not None, 'Stamp is required for signature that claims to have stamp'
            stamp_s3 = get_document_revoke_external_stamp_signature_s3_key(
                revoke_id=revoke_id,
                signature_id=signature.id,
            )
            stamp_content = base64.b64decode(stamp)
            file = s3_utils.UploadFile(
                key=stamp_s3,
                body=stamp_content,
            )
            pending_uploads.append(file)

    await s3_utils.upload_batch(pending_uploads)


async def _process_signature_for_edi_document(
    ctx: AddSignatureDocumentRevokeCtx, data: DataDict
) -> DataDict:
    """
    Process first signature for Document from EDI.
    EDI documents always have document.signature_format = 'internal_wrapped' and
    we don't want to change that format on receiving first signature in
    format = 'external_separated'.
    """

    if not ctx.document.source.is_from_edi:
        return data

    # Currently covers case only for external_separated signatures
    if data['format'] != SignatureFormat.external_separated:
        return data

    if ctx.revoke.signature_format != SignatureFormat.internal_wrapped:
        return data

    # convert signature and set appropriate signature_format
    key = prepare_p7s_file(not_none(data.pop('key')))
    stamp_str: str | None = data.pop('stamp', None)
    stamp = prepare_p7s_file(stamp_str) if stamp_str is not None else None

    original = await get_document_revoke_xml_content(ctx.revoke.id)
    data['key'] = None
    data['stamp'] = None
    data['format'] = SignatureFormat.internal_wrapped
    data['is_internal'] = True
    data['p7s'] = build_container_with_multiple_signatures(
        original_file_content=original, signatures=list(filter(None, [key, stamp]))
    )
    return data


async def add_document_revoke_signature(
    conn: DBConnection,
    ctx: AddSignatureDocumentRevokeCtx,
) -> None:
    """
    Add signature to db, update all related entities and upload signature to S3
    NOTICE: this function IS NOT sending any notifications, consider calling
    `add_document_revoke_signature_async_jobs` after this function (outside of transaction)
    """

    data = ctx.data
    if data.get('key_serial_number'):
        data['key_exists'] = True

    if data.get('stamp_serial_number'):
        data['stamp_exists'] = True

    data = await _process_signature_for_edi_document(ctx, data)

    async with conn.begin():
        signature = await insert_document_revoke_signature(
            conn=conn,
            revoke_id=ctx.revoke.id,
            role_id=ctx.user.role_id,
            user_email=data['user_email'],
            key_acsk=data['key_acsk'],
            key_serial_number=data['key_serial_number'],
            key_timemark=data['key_timemark'],
            key_company_fullname=data['key_company_fullname'],
            key_owner_edrpou=data['key_owner_edrpou'],
            key_owner_fullname=data['key_owner_fullname'],
            key_owner_position=data['key_owner_position'],
            key_power_type=data.get('key_power_type') or None,
            key_certificate_power_type=data.get('key_certificate_power_type') or None,
            stamp_power_type=data.get('stamp_power_type') or None,
            stamp_certificate_power_type=data.get('stamp_certificate_power_type') or None,
            stamp_acsk=data['stamp_acsk'],
            stamp_serial_number=data['stamp_serial_number'],
            stamp_timemark=data['stamp_timemark'],
            stamp_company_fullname=data['stamp_company_fullname'],
            stamp_owner_edrpou=data['stamp_owner_edrpou'],
            stamp_owner_fullname=data['stamp_owner_fullname'],
            stamp_owner_position=data['stamp_owner_position'],
            is_internal=data['is_internal'],
            format=data['format'],
            algo=data['algo'],
            internal_file_name=data.get('internal_file_name'),
            source=data['source'],
            # For blackbox signatures that keys comes inside "data" field,
            # but for other signatures upload its updated after uploading
            # signatures to S3
            key_exists=data.get('key_exists', False),
            stamp_exists=data.get('stamp_exists', False),
        )

        await _upload_signature_to_s3(
            key=data['key'],
            stamp=data['stamp'],
            signature=signature,
            container=data.get('p7s'),
            signature_algo=signature.algo,
            revoke_id=ctx.revoke.id,
        )
        if ctx.is_first_sign:
            await create_comment(
                conn=conn,
                document_id=ctx.document.id,
                document_version_id=None,
                role_id=ctx.user.role_id,
                type_=CommentType.document_revoke_initiate,
                text=ctx.revoke.reason,
                access_company_id=None,
            )
            await update_document_revoke(
                conn,
                id=ctx.revoke.id,
                status=DocumentRevokeStatus.in_progress,
            )
        if ctx.is_last_sign:
            await update_document(
                conn,
                {
                    'status_id': DocumentStatus.revoked.value,
                    'document_id': ctx.document.id,
                },
            )
            await update_document_revoke(
                conn,
                id=ctx.revoke.id,
                status=DocumentRevokeStatus.completed,
            )


async def add_document_revoke_signature_async_jobs(ctx: AddSignatureDocumentRevokeCtx) -> None:
    """
    Send async jobs on signing document revoke
    """
    if ctx.is_first_sign:
        await services.kafka.send_record(
            topic=topics.SEND_REVOKE_INITIATED_NOTIFICATIONS,
            value={
                'initiator_role_id': ctx.user.role_id,
                'message': ctx.revoke.reason,
                'document_ids': [ctx.document.id],
            },
        )
        await services.kafka.send_record(
            topic=topics.EDI_SEND_EDI_REQUEST,
            value=EDIPayloadHandleDocumentRevokeStarted(document_id=ctx.document.id).to_dict(),
        )

    if ctx.is_last_sign:
        await send_to_indexator(document_ids=[ctx.document.id], to_slow_queue=True)
        await services.kafka.send_record(
            topic=topics.SEND_REVOKE_COMPLETED_NOTIFICATIONS,
            value={
                'initiator_role_id': ctx.user.role_id,
                'document_ids': [ctx.document.id],
            },
        )
        await services.kafka.send_record(
            topic=topics.EDI_SEND_EDI_REQUEST,
            value=EDIPayloadHandleDocumentRevokeCompleted(document_id=ctx.document.id).to_dict(),
        )


async def download_wrapped_signature_document_revoke_content(
    conn: DBConnection,
    revoke_id: str,
) -> bytes:
    signature = await select_last_internal_document_revoke_signature(conn, revoke_id)
    if signature is None:
        return await get_document_revoke_xml_content(revoke_id)

    return await get_document_revoke_signatures_internal_content(
        revoke_id=revoke_id,
        signature_id=signature.id,
    )
