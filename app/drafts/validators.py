from collections.abc import Sequence

from api.errors import Code, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror, Object
from app.auth.db import select_company
from app.auth.types import AuthUser, User
from app.document_antivirus.validators import validate_antivirus_check_download_draft
from app.document_categories.db import exists_document_category
from app.document_versions.db import select_document_version
from app.document_versions.types import VersionUploadCtx
from app.document_versions.utils import convert_document_version_marker
from app.document_versions.validators import validate_version_upload
from app.documents.types import Document
from app.documents.validators import validate_document_access
from app.drafts.constants import DRAFT_ALLOWED_EXTENSIONS, DRAFT_LIMIT
from app.drafts.db import count_drafts, select_draft
from app.drafts.enums import DraftType
from app.drafts.schemas import ConvertDraftToDocumentValidator, CreateDraftFromVersionedValidator
from app.drafts.types import (
    ConvertDraftToDocumentContext,
    CreateDraftFromTemplate<PERSON>ontext,
    CreateDraftFromVersioned<PERSON>ontext,
    Draft,
    DraftContentAccessCtx,
    DraftDeleteCtx,
)
from app.drafts.utils import get_draft_s3_key
from app.i18n import _
from app.lib import s3_utils
from app.lib.database import DBConnection
from app.lib.enums import DocumentStatus
from app.lib.helpers import not_none
from app.lib.s3_utils import DownloadFile
from app.lib.types import DataDict
from app.lib.validators import validate_pydantic
from app.templates.validators import validate_template_exists


async def validate_draft_exists(
    *,
    conn: DBConnection,
    draft_id: str,
    company_id: str | None = None,
    type: DraftType | None = None,
) -> Draft:
    draft = await select_draft(conn=conn, id=draft_id, company_id=company_id, type=type)
    if not draft:
        raise DoesNotExist(Object.draft, id=draft_id)
    return draft


async def validate_create_draft_from_versioned(
    conn: DBConnection,
    data: DataDict,
    user: User,
) -> CreateDraftFromVersionedContext:
    validated_data = validate_pydantic(CreateDraftFromVersionedValidator, data)

    document = await validate_document_access(conn, user, validated_data.document_id)

    # do not allow to create draft for already signed documents
    if document.status_id >= DocumentStatus.signed.value:
        raise Error(Code.invalid_document_status)

    document_version = await convert_document_version_marker(
        conn=conn,
        version_marker=validated_data.version,
        document_id=validated_data.document_id,
        company_edrpou=user.company_edrpou,
    )
    if document_version is None:
        raise DoesNotExist(Object.document_version, id=validated_data.version)

    if document_version.extension not in DRAFT_ALLOWED_EXTENSIONS:
        raise Error(
            Code.invalid_file_extension,
            details={'allowed_extensions': ', '.join(DRAFT_ALLOWED_EXTENSIONS)},
        )

    return CreateDraftFromVersionedContext(
        document=document,
        document_version=document_version,
        user=user,
    )


async def validate_create_draft_from_template(
    conn: DBConnection,
    template_id: str,
    user: User,
) -> CreateDraftFromTemplateContext:
    template = await validate_template_exists(
        conn=conn,
        template_id=template_id,
        company_ids=[user.company_id, None],
    )
    await validate_draft_create_counter(
        conn=conn,
        user=user,
        types=DraftType.document_creation_drafts(),
    )
    return CreateDraftFromTemplateContext(
        template=template,
        user=user,
    )


async def validate_convert_draft_to_document(
    conn: DBConnection,
    draft_id: str,
    user: User,
    raw_data: DataDict,
) -> ConvertDraftToDocumentContext:
    validated_data = validate_pydantic(
        ConvertDraftToDocumentValidator,
        {
            **raw_data,
            'draft_id': draft_id,
        },
    )

    draft = await validate_draft_exists(
        conn=conn,
        draft_id=draft_id,
        company_id=user.company_id,
    )

    assert draft.type in DraftType.document_creation_drafts(), (
        'Only some types of drafts can be converted to documents.'
    )

    template = None
    if draft.type.is_template:
        template = await validate_template_exists(
            conn=conn,
            template_id=not_none(draft.template_id),
            company_ids=[user.company_id, None],
        )

    if validated_data.category_id and not await exists_document_category(
        conn=conn,
        document_category_id=validated_data.category_id,
        company_id=user.company_id,
    ):
        raise DoesNotExist(obj=Object.document_category)

    return ConvertDraftToDocumentContext(
        draft=draft,
        template=template,
        user=user,
        validated_data=validated_data,
    )


async def validate_delete_draft(
    conn: DBConnection,
    draft_id: str,
    user: User,
) -> DraftDeleteCtx:
    draft = await validate_draft_exists(
        conn=conn,
        draft_id=draft_id,
        company_id=user.company_id,
    )
    document: Document | None = None
    if draft.document_id:
        document = await validate_document_access(
            conn=conn,
            user=user,
            document_id=draft.document_id,
        )
    # Creator can delete only drafts created by him
    # for some of the draft types
    if draft.type in DraftType.document_creation_drafts() and draft.creator_role_id != user.role_id:
        raise Error(Code.access_denied)

    return DraftDeleteCtx(
        draft=draft,
        document=document,
    )


async def validate_convert_draft_to_new_version(
    conn: DBConnection,
    draft_id: str,
    user: User,
) -> VersionUploadCtx:
    draft = await validate_draft_exists(
        conn=conn,
        draft_id=draft_id,
        company_id=user.company_id,
        type=DraftType.version,
    )
    assert draft.document_id is not None, 'Currently drafts can be only created from documents.'

    await validate_document_access(conn, user, draft.document_id)
    document_version = await select_document_version(
        conn=conn,
        version_id=draft.document_version_id,
    )
    if document_version is None:
        raise Exception('Document version not found')

    content, __ = await s3_utils.download(DownloadFile(key=get_draft_s3_key(draft_id=draft.id)))
    validated = await validate_version_upload(
        conn=conn,
        content=content,
        filename=f'document.{document_version.extension}',
        user=user,
        document_id=draft.document_id,
    )

    return validated


async def validate_download_draft_content(
    conn: DBConnection,
    draft_id: str,
    auth_user: AuthUser | User,
) -> Draft:
    draft = await validate_draft_exists(
        conn=conn,
        draft_id=draft_id,
        company_id=auth_user.company_id,
    )
    if draft.document_id:
        await validate_document_access(conn, auth_user, draft.document_id)

    await validate_antivirus_check_download_draft(
        conn=conn,
        draft_id=draft.id,
        company_id=auth_user.company_id,
    )

    return draft


async def validate_draft_create_counter(
    *,
    conn: DBConnection,
    user: User,
    types: Sequence[DraftType] | None = None,
) -> None:
    """
    Company can create not more than DRAFT_LIMIT drafts.
    """
    counter = await count_drafts(
        conn=conn,
        company_id=user.company_id,
        types=types,
    )
    if counter >= DRAFT_LIMIT:
        raise Error(Code.overdraft)


async def validate_draft_for_content_access(
    conn: DBConnection,
    *,
    user_company_id: str,
    draft_id: str | None = None,
    draft: Draft | None = None,
) -> DraftContentAccessCtx:
    from app.document_versions.validators import validate_document_version_exists

    if draft is None:
        assert draft_id is not None, 'Either draft or draft_id should be provided.'
        draft = await validate_draft_exists(
            conn=conn,
            draft_id=draft_id,
            company_id=user_company_id,
        )

    # Draft is encrypted with draft's company edrpou
    company = await select_company(conn=conn, company_id=draft.company_id)
    if company is None:
        raise DoesNotExist(Object.company, id=user_company_id)

    if draft.type.is_version:
        version = await validate_document_version_exists(
            conn=conn,
            document_id=not_none(draft.document_id),
            version_id=not_none(draft.document_version_id),
        )
        title = version.name
    elif draft.type.is_template:
        template = await validate_template_exists(
            conn=conn,
            template_id=not_none(draft.template_id),
            company_ids=[user_company_id, None],
        )
        title = template.title
    elif draft.type.standalone:
        title = str(_('Новий документ'))
    else:
        raise NotImplementedError(f'Unsupported draft type: {draft.type}')

    return DraftContentAccessCtx(
        title=title,
        draft=draft,
    )
