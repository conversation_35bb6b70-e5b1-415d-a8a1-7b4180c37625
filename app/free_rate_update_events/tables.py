import sqlalchemy as sa

from app.free_rate_update_events.enums import FreeRateUpdateEventType, FreeRateUserType
from app.models import columns, metadata

# Temporary table for storing click events
# related to buy rate from email notifications about changed free rate (2 employees => 1 employee)
free_rate_update_events_table = sa.Table(
    'free_rate_update_events',
    metadata,
    columns.UUID(),
    columns.ForeignKey('role_id', 'roles.id', ondelete='NO ACTION', index=True),
    columns.SoftEnum('type', FreeRateUpdateEventType, index=True),
    # used together with FreeRateUpdateEventType (open_email and click_email)
    columns.SoftEnum('user_type', FreeRateUserType, nullable=True),
    columns.DateCreated(),
)
