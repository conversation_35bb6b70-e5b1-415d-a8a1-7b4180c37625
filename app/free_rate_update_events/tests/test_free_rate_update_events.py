import datetime
from http import HTT<PERSON>tatus

import pytest

from app.free_rate_update_events.db import (
    insert_free_rate_update_event,
    select_free_rate_update_events,
)
from app.free_rate_update_events.enums import FreeRateUpdateEventType, FreeRateUserType
from app.lib.datetime_utils import utc_now
from app.lib.enums import UserRole
from app.tests.common import fetch_graphql, prepare_auth_headers, prepare_client, prepare_user_data


async def test_free_rate_add_update_event(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)
    graphql_query = '{ currentRole { id isFreeRateBannerShown isFreeTrialEndBannerShown } }'
    data = await fetch_graphql(client, graphql_query, headers)
    assert data['currentRole']['id'] == user.role_id
    assert data['currentRole']['isFreeRateBannerShown'] is False
    assert data['currentRole']['isFreeTrialEndBannerShown'] is False

    async with app['db'].acquire() as conn:
        await insert_free_rate_update_event(
            conn, {'role_id': user.role_id, 'type': FreeRateUpdateEventType.open_email_7}
        )
    # other events types not impact isFreeRateBannerShown field
    data = await fetch_graphql(client, graphql_query, headers)
    assert data['currentRole']['isFreeRateBannerShown'] is False
    assert data['currentRole']['isFreeTrialEndBannerShown'] is False

    resp = await client.post(
        path='/internal-api/view-free-rate-banner',
        json={'event_type': FreeRateUpdateEventType.banner_free_update_view.value},
        headers=headers,
    )
    assert resp.status == HTTPStatus.CREATED

    data = await fetch_graphql(client, graphql_query, headers)
    assert data['currentRole']['isFreeRateBannerShown'] is True
    assert data['currentRole']['isFreeTrialEndBannerShown'] is False

    resp = await client.post(
        path='/internal-api/view-free-rate-banner',
        json={'event_type': FreeRateUpdateEventType.banner_end_trial_view.value},
        headers=headers,
    )
    assert resp.status == HTTPStatus.CREATED

    data = await fetch_graphql(client, graphql_query, headers)
    assert data['currentRole']['isFreeRateBannerShown'] is True
    assert data['currentRole']['isFreeTrialEndBannerShown'] is True


@pytest.mark.parametrize('second_user_is_admin', [True, False])
async def test_free_rate_update_banner_redirect_event(aiohttp_client, second_user_is_admin):
    app, client, user = await prepare_client(aiohttp_client)
    user_role = UserRole.admin.value if second_user_is_admin else UserRole.user.value
    user2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        date_created=utc_now() + datetime.timedelta(days=5),
        user_role=user_role,
    )

    headers = prepare_auth_headers(user)
    headers2 = prepare_auth_headers(user2)

    resp = await client.get(
        path='/internal-api/free-rate-update/events?source=banner_free_update_click',
        headers=headers,
        allow_redirects=False,
    )
    assert resp.status == 302
    assert resp.headers['Location'] == 'http://localhost:8000/app/checkout-rates/web'

    resp = await client.get(
        path='/internal-api/free-rate-update/events?source=banner_end_trial_click',
        headers=headers2,
        allow_redirects=False,
    )

    assert resp.status == 302
    assert resp.headers['Location'] == 'http://localhost:8000/app/checkout-rates/web'

    async with app['db'].acquire() as conn:
        rows = await select_free_rate_update_events(conn, [user.role_id, user2.role_id])
        assert len(rows) == 2

        assert rows[0]['role_id'] == user2.role_id
        if second_user_is_admin:
            assert rows[0]['user_type'] == FreeRateUserType.retain_user
        else:
            assert rows[0]['user_type'] == FreeRateUserType.remove_user
        assert rows[0]['type'] == FreeRateUpdateEventType.banner_end_trial_click

        assert rows[1]['role_id'] == user.role_id
        if second_user_is_admin:
            assert rows[1]['user_type'] == FreeRateUserType.remove_user
        else:
            assert rows[1]['user_type'] == FreeRateUserType.retain_user
        assert rows[1]['type'] == FreeRateUpdateEventType.banner_free_update_click


async def test_free_rate_update_banner_negative(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    resp = await client.get(
        path='/internal-api/free-rate-update/events',
        headers=headers,
        allow_redirects=False,
    )
    assert resp.status == 500
