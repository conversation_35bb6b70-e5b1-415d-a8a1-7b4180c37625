import logging
from http import HTTPStatus

from aiohttp import web

from app.auth.constants import USER_APP_KEY
from app.auth.decorators import login_required
from app.auth.types import User
from app.free_rate_update_events.db import insert_free_rate_update_event
from app.free_rate_update_events.enums import (
    BannerViewSchema,
    FreeRateUpdateEventType,
)
from app.free_rate_update_events.units import get_event_user_type
from app.lib import validators
from app.services import services

logger = logging.getLogger(__name__)


async def new_free_rate_track_event(
    request: web.Request,
) -> web.Response:
    """
    Track events related to update new free rate
        (e.g. click on "Buy rate" button in banner / email, etc.)
        and redirect user to buy rates page.
    """
    user_type = None
    user: User | None = request.get(USER_APP_KEY)

    redirect_url = f'{services.config.app.domain}/app/checkout-rates/web'

    source = request.rel_url.query.get('source', None)

    match source:
        case FreeRateUpdateEventType.banner_free_update_click:
            event_type = FreeRateUpdateEventType.banner_free_update_click
            role_id = user and user.role_id

        case FreeRateUpdateEventType.banner_end_trial_click:
            event_type = FreeRateUpdateEventType.banner_end_trial_click
            role_id = user and user.role_id

        case FreeRateUpdateEventType.open_email_30.value:
            raise NotImplementedError

        case FreeRateUpdateEventType.open_email_14.value:
            raise NotImplementedError

        case FreeRateUpdateEventType.open_email_7.value:
            raise NotImplementedError

        case FreeRateUpdateEventType.open_email_1.value:
            raise NotImplementedError

        case FreeRateUpdateEventType.click_in_email_30.value:
            raise NotImplementedError

        case FreeRateUpdateEventType.click_in_email_14.value:
            raise NotImplementedError

        case FreeRateUpdateEventType.click_in_email_7.value:
            raise NotImplementedError

        case FreeRateUpdateEventType.click_in_email_1.value:
            raise NotImplementedError

        case _:
            # Log unknown event source
            logger.error('Unknown event source', extra={'source': source, 'user': user})
            raise NotImplementedError('Unknown event source')

    async with services.db.acquire() as conn:
        if user:
            user_type = await get_event_user_type(conn, user)

        data = {
            'role_id': role_id,
            'type': event_type,
            'user_type': user_type,
        }
        await insert_free_rate_update_event(conn, data)

    return web.HTTPFound(redirect_url)


@login_required()
async def view_free_rate_banner(request: web.Request, user: User) -> web.Response:
    """
    Track events related to new free rate updates sent manually from the frontend.
    """
    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(BannerViewSchema, data)

    async with services.db.acquire() as conn:
        user_type = await get_event_user_type(conn, user)

        data = {
            'role_id': user.role_id,
            'type': FreeRateUpdateEventType(valid_data.event_type),
            'user_type': user_type,
        }
        # TODO: Add logic to do not insert the same event type for the same role_id multiple times

        await insert_free_rate_update_event(conn, data)

    return web.json_response(status=HTTPStatus.CREATED)
