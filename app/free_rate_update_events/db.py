from sqlalchemy.dialects.postgresql import insert

from app.free_rate_update_events.enums import FreeRateUpdateEventType
from app.free_rate_update_events.tables import free_rate_update_events_table
from app.lib.database import DBConnection, DBRow
from app.lib.types import DataDict
from app.models import select_all


async def insert_free_rate_update_event(conn: DBConnection, data: DataDict) -> None:
    await conn.execute(insert(free_rate_update_events_table).values(data))


async def select_free_rate_update_events(
    conn: DBConnection,
    role_ids: list[str],
    event_types: list[FreeRateUpdateEventType] | None = None,
    limit: int | None = None,
) -> list[DBRow]:
    query = free_rate_update_events_table.select().distinct(free_rate_update_events_table.c.type)

    if role_ids:
        query = query.where(free_rate_update_events_table.c.role_id.in_(role_ids))

    if event_types:
        query = query.where(free_rate_update_events_table.c.type.in_(event_types))

    query = query.order_by(
        free_rate_update_events_table.c.type, free_rate_update_events_table.c.date_created.desc()
    )

    if limit:
        query = query.limit(limit)

    return await select_all(conn, query)
