from enum import auto
from typing import Literal

import pydantic

from app.lib.enums import NamedEnum


class FreeRateUserType(NamedEnum):
    remove_user = auto()  # user, which will be removed from the company after end free rate
    retain_user = auto()  # usually owner of the company


class BannerViewSchema(pydantic.BaseModel):
    # 2 values from FreeRateUpdateEventType (banner view events)
    event_type: Literal['banner_free_update_view', 'banner_end_trial_view']


class FreeRateUpdateEventType(NamedEnum):
    banner_free_update_click = auto()  # user click "Buy rate" on banner
    banner_free_update_view = auto()  # user view "Buy rate" banner (save only first event)

    banner_end_trial_click = auto()  # user click "Buy rate" on banner after end of free trial
    banner_end_trial_view = auto()  # user view "Buy rate" banner after end

    # user open email with free rate updates
    open_email_30 = auto()
    open_email_14 = auto()
    open_email_7 = auto()
    open_email_1 = auto()

    # user click "Buy rate" in email
    click_in_email_30 = auto()
    click_in_email_14 = auto()
    click_in_email_7 = auto()
    click_in_email_1 = auto()

    # user activate free_trial (invite coworker or new user added using key)
    activate_trial = auto()
