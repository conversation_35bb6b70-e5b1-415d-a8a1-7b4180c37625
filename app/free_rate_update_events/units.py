import logging

from app.auth.db import select_roles
from app.auth.tables import role_table
from app.auth.types import User
from app.free_rate_update_events.enums import FreeRateUserType
from app.lib.database import DBConnection

logger = logging.getLogger(__name__)


async def get_event_user_type(conn: DBConnection, user: User) -> FreeRateUserType | None:
    """
    Determine the user type based on the number of roles.
    """
    roles = await select_roles(
        conn=conn,
        company_id=user.company_id,
        order=[role_table.c.user_role, role_table.c.date_created.desc()],
        only_active=True,
    )

    if len(roles) > 2:
        logger.error(
            'Unexpected number of roles for user',
            extra={
                'user_id': user.id,
                'role_id': user.role_id,
                'role_count': len(roles),
                'company_id': user.company_id,
            },
        )

    if roles[-1].id == user.role_id:
        return FreeRateUserType.retain_user

    return FreeRateUserType.remove_user
