import enum


@enum.unique
class FeatureFlags(enum.Enum):
    ES_SEARCH = 'ES_SEARCH'
    ES_SEARCH_API = 'ES_SEARCH_API'
    ES_DISABLE_REINDEXATION_JOB = 'ES_DISABLE_REINDEXATION_JOB'

    DOCUMENT_VIEW_LOCK_ON = 'DOCUMENT_VIEW_LOCK_ON'
    ENABLE_DIIA_ECDSA = 'ENABLE_DIIA_ECDSA'
    ENABLE_TTN_SERVICE_REGISTRATION = 'ENABLE_TTN_SERVICE_REGISTRATION'
    DISABLE_DOCUMENT_COUNTER = 'DISABLE_DOCUMENT_COUNTER'

    ENABLE_KEP_SIGN_FOR_INTERNAL_SEPARATED_SIGNATURE_FORMAT = (
        'ENABLE_KEP_SIGN_FOR_INTERNAL_SEPARATED_SIGNATURE_FORMAT'
    )

    ENABLE_KEP_SUPER_MASS_SIGN = 'ENABLE_KEP_SUPER_MASS_SIGN'

    # Flags that can be disabled or enabled without any conditions and context
    # in service. # Must have `_BINARY` ending.
    USE_THREAD_POOL_EXECUTOR_FOR_PDF = 'USE_THREAD_POOL_EXECUTOR_FOR_PDF'

    REPORT_CSP = 'REPORT_CSP'
    ENFORCE_CSP = 'ENFORCE_CSP'

    USE_EXPIRABLE_URLS_FOR_MS_VIEWER = 'USE_EXPIRABLE_URLS_FOR_MS_VIEWER'

    CHARDET_FOR_DETECT_CONTENT_DECODING = 'CHARDET_FOR_DETECT_CONTENT_DECODING'

    ENABLE_BILL_SIGNING = 'ENABLE_BILL_SIGNING'
    ENABLE_EVOPAY_PAYMENT = 'ENABLE_EVOPAY_PAYMENT'
    ENABLE_PRIVATBANK_PAYMENT = 'ENABLE_PRIVATBANK_PAYMENT'
    ENABLE_PUMB_PAYMENT = 'ENABLE_PUMB_PAYMENT'
    ENABLE_PUMB_REQUISITES = 'ENABLE_PUMB_REQUISITES'

    # readonly db
    USE_READONLY_DB_FOR_ESPUTNIK = 'USE_READONLY_DB_FOR_ESPUTNIK'
    USE_READONLY_DB_FOR_LIST_OWNER_DOCUMENTS_API = 'USE_READONLY_DB_FOR_LIST_OWNER_DOCUMENTS_API'
    USE_READONLY_DB_FOR_LIST_INCOMING_DOCUMENTS = 'USE_READONLY_DB_FOR_LIST_INCOMING_DOCUMENTS'

    READONLY_DB_FOR_DELETE_REQUEST_API = 'READONLY_DB_FOR_DELETE_REQUEST_API'

    VCHASNO_KEP_VERIFICATION = 'VCHASNO_KEP_VERIFICATION'

    DISABLE_EVENT_COLLECTION = 'DISABLE_EVENT_COLLECTION'

    # temporary flags for AWS migration
    DISABLE_ACTIONS_WRITES = 'DISABLE_ACTIONS_WRITES'

    # Disable 2FA verification in case when if we have problem with sending SMS/viber messages
    # NOTE: use only in case of emergency
    DISABLE_2FA_VERIFICATION = 'DISABLE_2FA_VERIFICATION'

    DISABLE_PROXY_UA = 'DISABLE_PROXY_UA'

    TEMP_DISABLE_JOB_RETRIES = 'TEMP_DISABLE_JOB_RETRIES'

    INVALID_SIGNATURES = 'INVALID_SIGNATURES'

    WIDGET_IIT_FOZZY = 'WIDGET_IIT_FOZZY'

    # https://jira.evo/browse/AKRT-108
    USE_SIGN_SESSION_FOR_INVITE_EMAIL = 'USE_SIGN_SESSION_FOR_INVITE_EMAIL'

    # https://tabula-rasa.atlassian.net/browse/DOC-6248
    DOCUMENT_VERSIONS_UPLOAD_ANY = 'DOCUMENT_VERSIONS_UPLOAD_ANY'

    JSON_IS_DICT_VALIDATION = 'JSON_IS_DICT_VALIDATION'

    SIGN_SESSION_MS_OFFICE_PARAMETER = 'SIGN_SESSION_MS_OFFICE_PARAMETER'

    DISABLE_ESPUTNIK_GENERATE_TOV_TRIAL_EXPIRING_EVENT = (
        'DISABLE_ESPUTNIK_GENERATE_TOV_TRIAL_EXPIRING_EVENT'
    )
    DISABLE_ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT = (
        'DISABLE_ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT'
    )
    DISABLE_EVENT_REPORT_TASK = 'DISABLE_EVENT_REPORT_TASK'

    # https://tabula-rasa.atlassian.net/browse/DOC-6330
    APPLE_AUTH_BUTTON = 'APPLE_AUTH_BUTTON'

    # https://tabula-rasa.atlassian.net/browse/DOC-6191
    ENABLE_NEW_UPLOAD_FLOW = 'ENABLE_NEW_UPLOAD_FLOW'
    # https://vchasno-group.atlassian.net/browse/DOC-7494
    ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS = 'ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS'
    ENABLE_AUTOMATION_TEMPLATE_FORM_UX_2025 = 'ENABLE_AUTOMATION_TEMPLATE_FORM_UX_2025'
    ENABLE_DOCUMENT_PAGE_UX_2025 = 'ENABLE_DOCUMENT_PAGE_UX_2025'
    ENABLE_DOCUMENT_STRUCTURED_TEST_PAGE = 'ENABLE_DOCUMENT_STRUCTURED_TEST_PAGE'
    NEW_EDIT_DOCUMENT_UI = 'NEW_EDIT_DOCUMENT_UI'
    ENABLE_INACTIVE_UI_EFFECT = 'ENABLE_INACTIVE_UI_EFFECT'
    NEW_MULTI_EDIT_DOCUMENT_UI = 'NEW_MULTI_EDIT_DOCUMENT_UI'
    COLLABORA_MARKERS = 'COLLABORA_MARKERS'
    TEMPLATE_BUILDER = 'TEMPLATE_BUILDER'
    COLLABORA_VIEWER = 'COLLABORA_VIEWER'

    # https://vchasno-group.atlassian.net/browse/DOC-7261
    DOCUMENTS_PRIVATE_ACCESS = 'DOCUMENTS_PRIVATE_ACCESS'

    POSTHOG_SR_ENABLED = 'POSTHOG_SR_ENABLED'
    COLLABORA_XLS_TEMPLATE_CREATION = 'COLLABORA_XLS_TEMPLATE_CREATION'
    ENABLE_KEP_DEBUG_LOGS = 'ENABLE_KEP_DEBUG_LOGS'
    DEMINING_BANNER = 'DEMINING_BANNER'
    PAPERLESS_BANNER = 'PAPERLESS_BANNER'
    ARCHIVE_PURCHASE_IMPROVEMENT = 'ARCHIVE_PURCHASE_IMPROVEMENT'
    HOME_BANNER = 'HOME_BANNER'
    ALERT_UNLIMIT_FOR_FOP = 'ALERT_UNLIMIT_FOR_FOP'
    ENABLE_CONTACT_RECIPIENT_INDEX = 'ENABLE_CONTACT_RECIPIENT_INDEX'
    DISABLE_CONTACT_RECIPIENT_REINDEX = 'DISABLE_CONTACT_RECIPIENT_REINDEX'
    DISABLE_CONTACT_RECIPIENT_ES_SEARCH = 'DISABLE_CONTACT_RECIPIENT_ES_SEARCH'
    ENABLE_CONTACT_PERSON_ES_SEARCH = 'ENABLE_CONTACT_PERSON_ES_SEARCH'
    ENABLE_CONTACT_ES_SEARCH = 'ENABLE_CONTACT_ES_SEARCH'
    NEW_REGISTRATION_SYNC_ROLES = 'NEW_REGISTRATION_SYNC_ROLES'

    ENABLED_PUBLIC_API_RATE_LIMITS = 'ENABLED_PUBLIC_API_RATE_LIMITS'

    # https://tabula-rasa.atlassian.net/browse/DOC-6121
    # Do not show documents that in uploaded status.
    # The Situation is possible when a document just uploaded and updated in indexator(es)
    # but is not yet updated in readonly db.
    ENABLE_INCOMING_DOCUMENTS_DB_FILTERING_STATUS = 'ENABLE_INCOMING_DOCUMENTS_DB_FILTERING_STATUS'

    DISABLE_CONTACTS_INDEXATION = 'DISABLE_CONTACTS_INDEXATION'

    # https://tabula-rasa.atlassian.net/browse/DOC-6132
    ENABLE_ROLES_SYNC_ON_LOGIN = 'ENABLE_ROLES_SYNC_ON_LOGIN'

    # https://tabula-rasa.atlassian.net/browse/DOC-6090
    USE_ES_FOR_COMMENT_LIST = 'USE_ES_FOR_COMMENT_LIST'

    # https://tabula-rasa.atlassian.net/browse/DOC-6379
    ENABLE_REVIEW_REMINDER = 'ENABLE_REVIEW_REMINDER'

    # https://tabula-rasa.atlassian.net/browse/DOC-6120
    ENABLE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS = (
        'ENABLE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS'
    )

    # prod migration flags
    ENABLE_NEW_SIGNER_HOST = 'ENABLE_NEW_SIGNER_HOST'

    # Use new KEP API for getting user certificates
    # https://tabula-rasa.atlassian.net/browse/DOC-6329
    ENABLE_NEW_KEP_API_FOR_CERTIFICATES = 'ENABLE_NEW_KEP_API_FOR_CERTIFICATES'

    # Should we automatically send documents to the recipient after signing
    # https://tabula-rasa.atlassian.net/browse/DOC-6215
    AUTOSEND_DOCUMENTS = 'AUTOSEND_DOCUMENTS'

    # https://tabula-rasa.atlassian.net/browse/DOC-6619
    ENABLE_BILLING_CONFIG_MERGE = 'ENABLE_BILLING_CONFIG_MERGE'

    # new kep key for FOP https://tabula-rasa.atlassian.net/browse/DOC-6265
    ENABLE_FOP_NEW_KEP_KEY_REGISTRATION = 'ENABLE_FOP_NEW_KEP_KEY_REGISTRATION'

    # new kep key for TOV https://tabula-rasa.atlassian.net/browse/DOC-6826
    ENABLE_TOV_NEW_KEP_KEY_REGISTRATION = 'ENABLE_TOV_NEW_KEP_KEY_REGISTRATION'

    # https://tabula-rasa.atlassian.net/browse/DOC-6376
    ENABLE_CAN_SIGN_CHECK_ON_BACKEND = 'ENABLE_CAN_SIGN_CHECK_ON_BACKEND'

    # Temporary fully download file from S3 before streaming to client
    # https://tabula-rasa.atlassian.net/browse/DOC-6509
    ENABLE_FILES_FULL_DOWNLOAD = 'ENABLE_FILES_FULL_DOWNLOAD'

    ENABLE_ARCHIVE_RATES = 'ENABLE_ARCHIVE_RATES'

    # https://tabula-rasa.atlassian.net/browse/DOC-6041
    ENABLE_EXTRA_VALIDATION_FOR_FILENAME_ON_UPLOAD = (
        'ENABLE_EXTRA_VALIDATION_FOR_FILENAME_ON_UPLOAD'
    )

    # https://tabula-rasa.atlassian.net/browse/DOC-6694
    ENABLE_API_IP_WHITELIST = 'ENABLE_API_IP_WHITELIST'

    # TEMP
    ENABLE_SIGNATURE_CANCELLATION_CONFIRMATION = 'ENABLE_SIGNATURE_CANCELLATION_CONFIRMATION'

    ENABLE_MOBILE_PUSH_NOTIFICATIONS = 'ENABLE_MOBILE_PUSH_NOTIFICATIONS'

    DISABLE_ESPUTNIK_DOCUMENTS_SENT_EVENT = 'DISABLE_ESPUTNIK_DOCUMENTS_SENT_EVENT'

    USE_REAL_IP_HEADER = 'USE_REAL_IP_HEADER'

    ENABLE_HTTP_REQUESTS_LOGGING = 'ENABLE_HTTP_REQUESTS_LOGGING'

    ENABLE_REDIRECT_TO_AUTH_FOR_LANDING = 'ENABLE_REDIRECT_TO_AUTH_FOR_LANDING'

    # https://tabula-rasa.atlassian.net/browse/DOC-6696
    DISABLE_CACHE_USER_SESSION_TIMEOUT = 'DISABLE_CACHE_USER_SESSION_TIMEOUT'

    # https://tabula-rasa.atlassian.net/browse/DOC-6813
    ENABLE_BILL_GENERATION_WITHOUT_XML_VIEWER = 'ENABLE_BILL_GENERATION_WITHOUT_XML_VIEWER'

    STOP_SEND_CRM_SYNC_RATES = 'STOP_SEND_CRM_SYNC_RATES'

    ENABLE_CONVERT_OFFICE_TO_PDF = 'ENABLE_CONVERT_OFFICE_TO_PDF'

    ENABLE_NEW_UPLOAD_DOCS_CHECKING = 'ENABLE_NEW_UPLOAD_DOCS_CHECKING'

    # https://vchasno-group.atlassian.net/browse/EC-27
    DISABLE_MIGRATE_VCHASNO_USERS = 'DISABLE_MIGRATE_VCHASNO_USERS'

    TESTING_GA = 'TESTING_GA'

    # https://vchasno-group.atlassian.net/browse/DOC-7327
    SET_MIN_17_PDF_VERSION = 'SET_MIN_17_PDF_VERSION'

    DISABLE_REINDEX_REVIEWED_DOCS = 'DISABLE_REINDEX_REVIEWED_DOCS'

    ENABLE_PRO_ADVICE_BLOCK_IN_EMAILS = 'ENABLE_PRO_ADVICE_BLOCK_IN_EMAILS'

    ACTIVATE_TRIAL_AUTOMATICALLY_FOR_TOV_COMPANIES = (
        'ACTIVATE_TRIAL_AUTOMATICALLY_FOR_TOV_COMPANIES'
    )

    # https://tabula-rasa.atlassian.net/browse/DOC-6931
    ENABLE_CHANGE_PROFILE_FORM_EMAIL = 'ENABLE_CHANGE_PROFILE_FORM_EMAIL'

    # https://vchasno-group.atlassian.net/browse/EC-2
    ENABLE_PHONE_AUTH_TTN = 'ENABLE_PHONE_AUTH_TTN'

    # https://tabula-rasa.atlassian.net/browse/DOC-6918
    ENABLE_VCHASNO_PROFILE_SYNC_KEP = 'ENABLE_VCHASNO_PROFILE_SYNC_KEP'
    ENABLE_VCHASNO_PROFILE_SYNC_KASA = 'ENABLE_VCHASNO_PROFILE_SYNC_KASA'
    ENABLE_VCHASNO_PROFILE_SYNC_TTN = 'ENABLE_VCHASNO_PROFILE_SYNC_TTN'
    ENABLE_VCHASNO_PROFILE_SYNC_EDI = 'ENABLE_VCHASNO_PROFILE_SYNC_EDI'

    # Look at app.lib.logging.log_duration
    DISABLE_DURATION_LOGS_HELPER = 'DISABLE_DURATION_LOGS_HELPER'

    DONATE_RATEL_M_BANNER = 'DONATE_RATEL_M_BANNER'

    # https://vchasno-group.atlassian.net/browse/DOC-7187
    USE_LATEST_RECIPIENTS_TABLE = 'USE_LATEST_RECIPIENTS_TABLE'
    DISABLE_LATEST_RECIPIENTS_MIGRATION = 'DISABLE_LATEST_RECIPIENTS_MIGRATION'

    # https://vchasno-group.atlassian.net/browse/DOC-6740
    USE_CRM_LEADS_API = 'USE_CRM_LEADS_API'

    # Surveys
    ENABLE_ARCHIVE_SURVEY_POPUP = 'ENABLE_ARCHIVE_SURVEY_POPUP'

    # https://vchasno-group.atlassian.net/wiki/spaces/vchasno/pages/398819343/2025
    ENABLE_NEW_RATES = 'ENABLE_NEW_RATES'

    # https://vchasno-group.atlassian.net/browse/DOC-7423
    ENABLE_LISTING_DATE_FROM_ES = 'ENABLE_LISTING_DATE_FROM_ES'

    # https://vchasno-group.atlassian.net/browse/DOC-7374
    NEW_CLIENT_SESSION_ANTIVIRUS = 'NEW_CLIENT_SESSION_ANTIVIRUS'

    # https://vchasno-group.atlassian.net/browse/DOC-7363
    ENABLE_SYNC_RECIPIENTS_DATE_RECEIVED = 'ENABLE_SYNC_RECIPIENTS_DATE_RECEIVED'

    # https://vchasno-group.atlassian.net/browse/DOC-7401
    USE_LEGACY_DOCUMENT_PARAMETERS_FILTER = 'USE_LEGACY_DOCUMENT_PARAMETERS_FILTER'

    # new kep sign flow
    # https://vchasno-group.atlassian.net/wiki/spaces/vchasno/pages/406683649/2.0+.
    ENABLE_NEW_KEP_SIGN_FLOW = 'ENABLE_NEW_KEP_SIGN_FLOW'
    DISABLE_NEW_KEP_SIGN_FLOW = 'DISABLE_NEW_KEP_SIGN_FLOW'
    ENABLE_NEW_KEP_SIGN_FLOW_SIGN_WITH_APP = 'ENABLE_NEW_KEP_SIGN_FLOW_SIGN_WITH_APP'

    # https://vchasno-group.atlassian.net/browse/DOC-7254
    AI_DOCUMENT_SETTING_AUTO_COMPLETE = 'AI_DOCUMENT_SETTING_AUTO_COMPLETE'
    AI_DOCUMENT_SUMMARY_BLOCK = 'AI_DOCUMENT_SUMMARY_BLOCK'

    # https://vchasno-group.atlassian.net/browse/DOC-7300
    ENABLE_REVOKE_DOCUMENTS_FOR_EDI = 'ENABLE_REVOKE_DOCUMENTS_FOR_EDI'

    DISABLE_DELETE_REVIEW_REQUESTS_DUPLICATES = 'DISABLE_DELETE_REVIEW_REQUESTS_DUPLICATES'

    # https://vchasno-group.atlassian.net/browse/DOC-7452
    DISABLE_ALLOWED_SIGNATURE_FORMAT_RESTRICTIONS = 'DISABLE_ALLOWED_SIGNATURE_FORMAT_RESTRICTIONS'

    DONATE_DETECTED_IN_TIME_BANNER_V2 = 'DONATE_DETECTED_IN_TIME_BANNER_V2'

    # https://vchasno-group.atlassian.net/browse/DOC-7470
    DISABLE_SIGN_REJECT_PERMISSION_MIGRATION = 'DISABLE_SIGN_REJECT_PERMISSION_MIGRATION'

    # https://vchasno-group.atlassian.net/browse/DOC-7341
    ENABLE_AI_STRUCTURED_DATA_EXTRACTION = 'ENABLE_AI_STRUCTURED_DATA_EXTRACTION'

    # https://vchasno-group.atlassian.net/browse/DOC-7533
    ENABLE_UPLOAD_SCAN_DOCS = 'ENABLE_UPLOAD_SCAN_DOCS'
    IS_SHOW_SCAN_DOCS_ONBOARDING = 'IS_SHOW_SCAN_DOCS_ONBOARDING'

    DISABLE_BANNER_ES_FILTER_DOCUMENTS_COUNT = 'DISABLE_BANNER_ES_FILTER_DOCUMENTS_COUNT'

    DISABLE_PHONE_AUTH_RATE_LIMIT_BY_IP = 'DISABLE_PHONE_AUTH_RATE_LIMIT_BY_IP'

    CSAT_ARCHIVE_SCAN_RECOGNITION_BY_AI = 'CSAT_ARCHIVE_SCAN_RECOGNITION_BY_AI'

    DISABLE_LOGS_FOR_KAFKA_BATCH_SENDING = 'DISABLE_LOGS_FOR_KAFKA_BATCH_SENDING'
    ENABLE_UJSON_AS_DEFAULT_SERIALIZER = 'ENABLE_UJSON_AS_DEFAULT_SERIALIZER'

    DISABLE_DEACTIVATE_DOUBLE_ACTIVATED_ACCOUNTS = 'DISABLE_DEACTIVATE_DOUBLE_ACTIVATED_ACCOUNTS'

    # https://vchasno-group.atlassian.net/browse/DOC-7638
    DISABLE_SHORTENED_STRUCTURED_DATA_EXTRACTION = 'DISABLE_SHORTENED_STRUCTURED_DATA_EXTRACTION'
