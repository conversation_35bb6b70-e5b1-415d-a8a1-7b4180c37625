from aiohttp import web

from app.flags import FeatureFlags
from app.flags.utils import get_flag


async def get_flags(request: web.Request) -> web.Response:
    flags = [
        FeatureFlags.ES_SEARCH,
        FeatureFlags.DOCUMENT_VIEW_LOCK_ON,
        FeatureFlags.ENABLE_DIIA_ECDSA,
        FeatureFlags.ENABLE_TTN_SERVICE_REGISTRATION,
        FeatureFlags.ENABLE_KEP_SIGN_FOR_INTERNAL_SEPARATED_SIGNATURE_FORMAT,
        FeatureFlags.VCHASNO_KEP_VERIFICATION,
        FeatureFlags.DISABLE_EVENT_COLLECTION,
        FeatureFlags.ENABLE_BILL_SIGNING,
        FeatureFlags.ENABLE_KEP_SUPER_MASS_SIGN,
        FeatureFlags.ENABLE_EVOPAY_PAYMENT,
        FeatureFlags.ENABLE_PRIVATBANK_PAYMENT,
        FeatureFlags.INVALID_SIGNATURES,
        FeatureFlags.WIDGET_IIT_FOZZY,
        FeatureFlags.SIGN_SESSION_MS_OFFICE_PARAMETER,
        FeatureFlags.APPLE_AUTH_BUTTON,
        FeatureFlags.ENABLE_NEW_UPLOAD_FLOW,
        FeatureFlags.ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS,
        FeatureFlags.ENABLE_AUTOMATION_TEMPLATE_FORM_UX_2025,
        FeatureFlags.ENABLE_DOCUMENT_PAGE_UX_2025,
        FeatureFlags.ENABLE_DOCUMENT_STRUCTURED_TEST_PAGE,
        FeatureFlags.ENABLE_KEP_DEBUG_LOGS,
        FeatureFlags.ENABLE_INACTIVE_UI_EFFECT,
        FeatureFlags.NEW_EDIT_DOCUMENT_UI,
        FeatureFlags.ENABLE_CONTACT_ES_SEARCH,
        FeatureFlags.NEW_MULTI_EDIT_DOCUMENT_UI,
        FeatureFlags.COLLABORA_MARKERS,
        FeatureFlags.TEMPLATE_BUILDER,
        FeatureFlags.COLLABORA_VIEWER,
        FeatureFlags.DOCUMENTS_PRIVATE_ACCESS,
        FeatureFlags.POSTHOG_SR_ENABLED,
        FeatureFlags.COLLABORA_XLS_TEMPLATE_CREATION,
        FeatureFlags.DEMINING_BANNER,
        FeatureFlags.PAPERLESS_BANNER,
        FeatureFlags.CSAT_ARCHIVE_SCAN_RECOGNITION_BY_AI,
        FeatureFlags.ARCHIVE_PURCHASE_IMPROVEMENT,
        FeatureFlags.HOME_BANNER,
        FeatureFlags.NEW_REGISTRATION_SYNC_ROLES,
        FeatureFlags.ENABLE_FOP_NEW_KEP_KEY_REGISTRATION,
        FeatureFlags.ENABLE_TOV_NEW_KEP_KEY_REGISTRATION,
        FeatureFlags.ENABLE_CAN_SIGN_CHECK_ON_BACKEND,
        FeatureFlags.ENABLE_SIGNATURE_CANCELLATION_CONFIRMATION,
        FeatureFlags.ENABLE_MOBILE_PUSH_NOTIFICATIONS,
        FeatureFlags.ENABLE_ARCHIVE_RATES,
        FeatureFlags.ENABLE_CONVERT_OFFICE_TO_PDF,
        FeatureFlags.ENABLE_CHANGE_PROFILE_FORM_EMAIL,
        FeatureFlags.DONATE_RATEL_M_BANNER,
        FeatureFlags.ALERT_UNLIMIT_FOR_FOP,
        FeatureFlags.ENABLE_NEW_KEP_API_FOR_CERTIFICATES,
        FeatureFlags.ENABLE_ARCHIVE_SURVEY_POPUP,
        FeatureFlags.ENABLE_NEW_RATES,
        FeatureFlags.ENABLE_NEW_KEP_SIGN_FLOW,
        FeatureFlags.ENABLE_NEW_KEP_SIGN_FLOW_SIGN_WITH_APP,
        FeatureFlags.DISABLE_NEW_KEP_SIGN_FLOW,
        FeatureFlags.AI_DOCUMENT_SETTING_AUTO_COMPLETE,
        FeatureFlags.AI_DOCUMENT_SUMMARY_BLOCK,
        FeatureFlags.ENABLE_REVOKE_DOCUMENTS_FOR_EDI,
        FeatureFlags.ENABLE_PHONE_AUTH_TTN,
        FeatureFlags.DONATE_DETECTED_IN_TIME_BANNER_V2,
        FeatureFlags.ENABLE_UPLOAD_SCAN_DOCS,
        FeatureFlags.DISABLE_BANNER_ES_FILTER_DOCUMENTS_COUNT,
        FeatureFlags.IS_SHOW_SCAN_DOCS_ONBOARDING,
    ]
    values: dict[str, bool] = {flag.value: get_flag(flag) for flag in flags}

    return web.json_response(values)
