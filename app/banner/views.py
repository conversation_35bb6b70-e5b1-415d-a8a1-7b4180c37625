import logging
from http import HTTPStatus

from aiohttp import web

from api.private.super_admin.db import insert_super_admin_action
from api.public import decorators as public_decorators
from app.auth.decorators import login_required, super_admin_permission_required
from app.auth.types import User
from app.banner import db, utils
from app.banner.enums import SABannerPromoKasaAction
from app.banner.schemas import PromoBannerSchema, SABannerPromoKasaSchema
from app.banner.utils import (
    _banner_log_extra,
    create_banner_with_content,
    update_banner_with_content,
)
from app.banner.validators import (
    UpdatePromoBannerShownValidator,
    validate_create_banner,
    validate_update_banner,
)
from app.lib import validators
from app.lib.enums import SuperAdminActionType
from app.openapi.decorators import openapi_docs
from app.services import services

logger = logging.getLogger(__name__)


@super_admin_permission_required(required_permissions={'can_edit_special_features'})
async def update(request: web.Request, user: User) -> web.Response:
    app = request.app
    async with app['db'].acquire() as conn:
        banner = await validate_update_banner(conn, request)
        await update_banner_with_content(conn, banner)

    logger.info(msg='Banner was updated', extra={**_banner_log_extra(banner, user)})
    return web.Response(status=HTTPStatus.OK)


@super_admin_permission_required(required_permissions={'can_edit_special_features'})
async def create(request: web.Request, user: User) -> web.Response:
    app = request.app
    async with app['db'].acquire() as conn:
        banner = await validate_create_banner(conn, request, user)
        await create_banner_with_content(conn, banner)

    logger.info(msg='Banner was created', extra={**_banner_log_extra(banner, user)})
    return web.Response(status=HTTPStatus.CREATED)


@openapi_docs(
    summary='Редагувати список користувачів для промо банеру з Вчасно.Каса',
    request_json=SABannerPromoKasaSchema,
)
@public_decorators.api_super_admin_permission_required(
    required_permissions={'can_edit_special_features'}
)
async def update_banner_promo_kasa_list(request: web.Request, user: User) -> web.Response:
    """
    Upload email + edrpou pairs to which enable or disable promo banner for Kasa
    """

    raw_data = await validators.validate_json_request(request)
    ctx = validators.validate_pydantic(SABannerPromoKasaSchema, raw_data)

    logger.info(
        msg='Banner promo Kasa action',
        extra={
            'action': ctx.action.value,
            'items_len': len(ctx.items),
            'user_email': user.email,
        },
    )

    async with services.db.acquire() as conn:
        if ctx.action == SABannerPromoKasaAction.delete:
            await db.delete_banner_promo_kasa(conn, items=ctx.items)
        elif ctx.action == SABannerPromoKasaAction.update:
            await db.insert_banner_promo_kasa(conn, items=ctx.items)

        await insert_super_admin_action(
            conn=conn,
            user=user,
            action=SuperAdminActionType.update_banner_promo_kasa_list,
            extra_details=[{'len': len(ctx.items), 'action': ctx.action.value}],
        )

    return web.Response(status=HTTPStatus.OK)


@openapi_docs(
    summary='Дістати поточний промо банер для користувача',
    response=PromoBannerSchema,
)
@login_required()
async def resolve_promo_banner(request: web.Request, user: User) -> web.Response:
    """
    Get current promo banner for the user
    """
    async with services.db.acquire() as conn:
        banner = await utils.resolve_promo_banner(conn, user)

    return web.json_response(banner.to_api(), status=HTTPStatus.OK)


@openapi_docs(
    summary='Відмітити, що промо-банер був показаний користувачу',
    request_json=UpdatePromoBannerShownValidator,
)
@login_required()
async def update_promo_banner_shown(request: web.Request, user: User) -> web.Response:
    """
    Update promo banner shown status
    """

    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(UpdatePromoBannerShownValidator, raw_data)

    async with services.db.acquire() as conn:
        await utils.update_promo_banner_shown(conn, user=user, campaign=data.campaign)

    return web.Response(status=HTTPStatus.OK)
