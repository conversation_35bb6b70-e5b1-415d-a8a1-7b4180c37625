from datetime import datetime

from hiku.engine import Context, pass_context
from hiku.graph import Nothing

from api.graph.constants import DB_ENGINE_KEY, DB_READONLY_KEY
from api.graph.utils import check_sa_graph_user, get_graph_user
from app.banner import db
from app.banner.db import select_visible_banners
from app.banner.utils import BannerUserMatcher
from app.lib.datetime_utils import to_utc_datetime
from app.lib.types import DataDict, StrList


@pass_context
async def resolve_banners(ctx: Context, options: DataDict) -> StrList:
    user = check_sa_graph_user(ctx, required_permissions={'can_edit_special_features'})
    if not user:
        return []

    limit: int | None = options['limit']
    offset: int | None = options['offset']
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        banners = await select_visible_banners(conn, limit=limit, offset=offset)
    return [banner.id_ for banner in banners]


@pass_context
async def resolve_active_banner(ctx: Context) -> str | None:
    user = get_graph_user(ctx)
    if not user:
        return Nothing

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        banners = await db.select_active_banner_for_graph(conn)
        matcher = BannerUserMatcher(user=user)

        # Find a banner that best matches to given conditions.
        # If two or more banners have the same match score, then select the newest one
        banner_id: str | None = None
        max_banner_score: tuple[int, datetime] = (0, to_utc_datetime(datetime.min))
        for banner in banners:
            match = await matcher.match_score(
                conn=conn,
                positions=banner.positions,
                rates=banner.rates,
                outgoing_documents_count=banner.outgoing_documents_count,
                incoming_documents_sign_count=banner.incoming_documents_sign_count,
                activity_period=banner.activity_period,
                audience_type=banner.audience_type,
                days_before_signature_expires=banner.days_before_signature_expires,
                employees_count=banner.employees_count,
            )
            if match is None:
                continue

            banner_score: tuple[int, datetime] = (match, banner.date_created)
            if banner_score > max_banner_score:
                banner_id = banner.id
                max_banner_score = banner_score

    return banner_id or Nothing
