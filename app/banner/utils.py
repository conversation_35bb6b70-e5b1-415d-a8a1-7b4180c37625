import logging
from datetime import timedelta
from types import EllipsisType

from dateutil.relativedelta import relativedelta

from app.auth.db import select_company_by_id, select_raw_user_role_position
from app.auth.enums import EmployeePositions
from app.auth.types import User
from app.auth.utils import is_fop
from app.banner import db
from app.banner.enums import (
    BannerActivityPeriod,
    BannerAudienceType,
    BannerIncomingDocumentsSignCount,
    BannerOutgoingDocumentsCount,
    BannerPosition,
    BannerPositionFilter,
    BannerRate,
    BannerStatus,
    EmployeesCount,
    PromoBannerCampaign,
)
from app.banner.schemas import PromoBannerResponse, PromoBannerSchema
from app.banner.types import Banner
from app.billing.db import select_latest_active_company_rate
from app.billing.enums import ARCHIVE_RATES_SET, WEB_RATES_SET
from app.es.utils import (
    count_incoming_company_documents,
    count_sent_outgoing_documents_elastic,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib import datetime_utils
from app.lib.database import DBConnection
from app.lib.datetime_utils import midnight
from app.lib.types import DataDict
from app.mobile.auth.db import select_is_user_has_mobile_app
from app.signatures.db import select_certificates_by

logger = logging.getLogger(__name__)

_TransitionMapping = dict[BannerStatus, list[BannerStatus]]

ACTIVE = BannerStatus.active
DELETED = BannerStatus.deleted
HIDDEN = BannerStatus.hidden
NEW = BannerStatus.new


_BANNER_STATUS_TRANSITIONS: _TransitionMapping = {
    NEW: [ACTIVE, DELETED, HIDDEN],
    DELETED: [],
    ACTIVE: [HIDDEN, DELETED],
    HIDDEN: [ACTIVE, DELETED],
}


def _banner_log_extra(banner: Banner, user: User) -> DataDict:
    return {
        'banner': banner.to_db(),
        'content': banner.to_content_db(),
        'role_id': user.role_id,
        'email': user.email,
    }


PROMO_BANNER_ACTIVE_PERIOD = timedelta(days=7)


async def create_banner_with_content(conn: DBConnection, banner: Banner) -> None:
    async with conn.begin():
        await db.insert_banner(conn, banner.to_db())
        await db.insert_banner_content(conn, banner.to_content_db())


async def update_banner_status(conn: DBConnection, banner_id: str, status: BannerStatus) -> None:
    await db.update_banner(conn, {'id': banner_id, 'status': status})


async def update_banner_with_content(conn: DBConnection, banner: Banner) -> None:
    async with conn.begin():
        await db.update_banner(conn, banner.to_db())
        await db.update_banner_content(conn, banner.to_content_db(), banner.id_)


def can_change_status(current: BannerStatus, next_: BannerStatus) -> bool:
    transitions = _BANNER_STATUS_TRANSITIONS[current]
    return next_ in transitions


class BannerUserMatcher:
    """
    This class helps to calculate the match score between banner conditions and user data.

    User data is loaded lazily and cached after the first load. This optimization is useful in
    cases where there are no position- or rate-specific banners, so we don't need to load the
    user data at all.
    """

    def __init__(self, user: User | None) -> None:
        self.user = user
        self.role_id = user.role_id if user else None
        self.company_id = user.company_id if user else None
        self.company_edrpou = user.company_edrpou if user else None

        # cached data
        self.position: BannerPosition | None | EllipsisType = ...
        self.rate: BannerRate | None | EllipsisType = ...

    async def select_user_position(self, conn: DBConnection) -> BannerPosition | None:
        if not self.role_id:
            return None

        if self.position is not ...:
            return self.position  # return cached

        raw_position = await select_raw_user_role_position(conn, role_id=self.role_id)
        self.position = normalize_banner_position(raw_position)
        return self.position

    async def select_user_rate(self, conn: DBConnection) -> BannerRate | None:
        if not self.company_id:
            return None

        if self.rate is not ...:
            return self.rate  # return cached

        active_rate = await select_latest_active_company_rate(
            conn=conn,
            company_id=self.company_id,
            rates=[*WEB_RATES_SET, *ARCHIVE_RATES_SET],
        )
        if not active_rate:
            self.rate = None
            return None

        self.rate = BannerRate.from_account_rate(active_rate.rate)
        return self.rate

    async def is_signature_expiry_condition_met_by_user(
        self,
        conn: DBConnection,
        days_before_signature_expires: int,
    ) -> bool:
        """
        Define whether user has expiring signature certificate at given number of dates.

        Signature certificate must be issued by not Vchasno ACSK.
        """

        if not self.role_id:
            return False

        date_start = midnight()
        date_end = date_start + relativedelta(days=days_before_signature_expires)

        expiring_certificates = await select_certificates_by(
            conn=conn,
            role_ids=[self.role_id],
            date_end_from=date_start,
            date_end_to=date_end,
            no_renewed_certificate=True,
        )

        if not expiring_certificates:
            return False

        return not any(certificate.is_vchasno for certificate in expiring_certificates)

    async def match_score(  # noqa: C901
        self,
        conn: DBConnection,
        # filters
        positions: list[BannerPosition] | None,
        rates: list[BannerRate] | None,
        outgoing_documents_count: list[BannerOutgoingDocumentsCount] | None,
        incoming_documents_sign_count: list[BannerIncomingDocumentsSignCount] | None,
        activity_period: BannerActivityPeriod | None,
        employees_count: list[EmployeesCount] | None,
        audience_type: BannerAudienceType | None,
        days_before_signature_expires: int | None,
    ) -> int | None:
        """
        Calculate the match score between banner conditions and user data.

        General rules:
        - If at least one of the conditions is not met, we return None
            and consider the banner as not matched.
        - The score represents how many conditions the user has met.

        "None" means that the user did not meet the banner conditions.
        """
        score: int = 0

        if positions:
            # If the user position is known:
            #   - If the position is not in the list of allowed positions, return None.
            # If the user position is unknown:
            #   - If unknown positions are allowed (`include_unknown_positions` is True),
            #       increment the score.
            #   - If unknown positions are not allowed, return None.

            include_unknown_positions = BannerPositionFilter.other in positions
            user_position = await self.select_user_position(conn)

            if user_position:
                if user_position not in positions:
                    return None
            elif include_unknown_positions:
                score += 1
            else:
                return None

        if rates:
            user_rate = await self.select_user_rate(conn)
            if not user_rate or user_rate not in rates:
                return None

            score += 1

        if employees_count:
            if not self.company_id or not self.company_edrpou or is_fop(self.company_edrpou):
                return None
            company = await select_company_by_id(conn, company_id=self.company_id)

            assert company  # for mypy
            if not any(
                emp_range.in_range(company.employees_number) for emp_range in employees_count
            ):
                return None

            score += 1

        if audience_type:
            if not self.company_edrpou:
                return None

            user_audience_type = BannerAudienceType.from_edrpou(self.company_edrpou)
            if user_audience_type != audience_type:
                return None

            score += 1

        if days_before_signature_expires:
            if not await self.is_signature_expiry_condition_met_by_user(
                conn=conn,
                days_before_signature_expires=days_before_signature_expires,
            ):
                return None

            score += 1

        # Don't show banners with documents count filters when disabled by feature flag.
        if get_flag(FeatureFlags.DISABLE_BANNER_ES_FILTER_DOCUMENTS_COUNT) and (
            outgoing_documents_count or incoming_documents_sign_count
        ):
            return None

        if outgoing_documents_count:
            signed_count = await count_sent_outgoing_documents_elastic(
                self.company_edrpou, activity_period
            )
            if not any(doc_range.in_range(signed_count) for doc_range in outgoing_documents_count):
                return None
            score += 1

        if incoming_documents_sign_count:
            signed_count = await count_incoming_company_documents(
                is_signed=True,
                company_edrpou=self.company_edrpou,
                activity_period=activity_period,
            )
            unsigned_count = None
            if BannerIncomingDocumentsSignCount.has_unsigned in incoming_documents_sign_count:
                unsigned_count = await count_incoming_company_documents(
                    is_signed=False,
                    company_edrpou=self.company_edrpou,
                    activity_period=activity_period,
                )

            if not any(
                doc_range.in_range(incoming_count=signed_count, unsigned_count=unsigned_count)
                for doc_range in incoming_documents_sign_count
            ):
                return None
            score += 1

        return score


def normalize_banner_position(raw_position: str | None) -> BannerPosition | None:
    """
    Convert employee position names (in Ukrainian) to predefined enum values. If a position
    cannot be found, return None.

    Examples:
     - "Директор" -> EmployeePositions.director
     - "Бухгалтер" -> EmployeePositions.accountant
     - "Пес Протон" -> BannerPosition.other
     - None -> None

    Why do we need this? When a new role is created, we ask the user to choose a position from a
    predefined list of values, but we also allow the entry of custom values. These custom values
    are stored in the database as is. However, we sometimes need to answer questions like "Is
    this user a director?" or "Is this user an accountant?" In such cases, this function helps
    convert a wide range of values to predefine enum values.
    """

    if not raw_position:
        return None

    return EmployeePositions.from_role_position(raw_position) or BannerPositionFilter.other


BANNER_PROMO_CAMPAIGN_ORDER = [
    PromoBannerCampaign.mobile_app,
    PromoBannerCampaign.kasa,
]


async def is_promo_campaign_targeted(
    conn: DBConnection,
    user: User,
    campaign: PromoBannerCampaign | None = None,
) -> bool:
    """
    Check if the user is eligible for a promo banner based on the campaign.
    If no campaign is specified, check for all campaigns in order.
    """

    if campaign == PromoBannerCampaign.mobile_app:
        has_app = await select_is_user_has_mobile_app(conn, user_id=user.id)
        return not has_app

    if campaign == PromoBannerCampaign.kasa:
        if not user.email:
            return False
        return await db.select_is_kasa_promo_targeted(
            conn, email=user.email, edrpou=user.company_edrpou
        )

    return False


async def get_next_promo_campaign(
    conn: DBConnection,
    user: User,
    banner: PromoBannerSchema | None = None,
) -> PromoBannerCampaign | None:
    """
    Get the list of promo campaigns starting from the next one to this campaign again in
    circular order.

    WARNING: might be expensive because we make separate request to the database for each
    campaign. Try to cache results for some time.
    """
    campaigns = BANNER_PROMO_CAMPAIGN_ORDER.copy()

    campaigns_queue: list[PromoBannerCampaign]
    if banner and banner.campaign:
        idx = campaigns.index(banner.campaign)
        if banner.is_shown:
            # start from the next campaign after shown one
            campaigns_queue = campaigns[idx + 1 :] + campaigns[: idx + 1]
        else:
            # start from current banner campaign
            campaigns_queue = campaigns[idx:] + campaigns[:idx]
    else:
        # start from the first campaign
        campaigns_queue = campaigns

    for next_campaign in campaigns_queue:
        is_targeted = await is_promo_campaign_targeted(conn, user, campaign=next_campaign)

        if is_targeted:
            return next_campaign

    # No targeted campaign found for given user
    return None


async def resolve_promo_banner(conn: DBConnection, user: User) -> PromoBannerResponse:
    """
    Get the promo banner for the user to show after sign document
    """
    banner = await db.select_promo_banner(conn, user_id=user.id, edrpou=user.company_edrpou)

    if banner and datetime_utils.utc_now() < banner.date_expired:
        return PromoBannerResponse(campaign=banner.campaign, shown=banner.is_shown)

    next_campaign = await get_next_promo_campaign(conn, user=user, banner=banner)
    update_data: DataDict = {
        'user_id': user.id,
        'edrpou': user.company_edrpou,
        'campaign': next_campaign,
        'shown_count': 0,
        'first_shown': None,
        'last_shown': None,
        # Cache decision which banner to show for 1 day,
        'date_expired': datetime_utils.utc_now() + timedelta(days=1),
    }
    if banner is None:
        await db.insert_promo_banner(conn, data=update_data)
    else:
        await db.update_promo_banner(conn, banner_id=banner.id, data=update_data)

    return PromoBannerResponse(campaign=next_campaign, shown=False)


async def update_promo_banner_shown(
    conn: DBConnection,
    user: User,
    campaign: PromoBannerCampaign,
) -> None:
    """
    Update that promo banner was shown to the user
    """
    log_extra = {'user_id': user.id, 'edrpou': user.company_edrpou, 'campaign': campaign}

    async with conn.begin():
        banner = await db.select_promo_banner(
            conn,
            user_id=user.id,
            edrpou=user.company_edrpou,
            for_update=True,
        )
        if not banner:
            logger.warning('No promo banner found for user', extra=log_extra)
            return

        # Not expected case, but let's handle it gracefully to avoid unexpected behavior
        if banner.campaign != campaign:
            logger.warning(
                'Promo banner campaign mismatch',
                extra={**log_extra, 'actual_campaign': banner.campaign},
            )
            return

        update_data: DataDict = {
            'campaign': campaign,
            'last_shown': datetime_utils.utc_now(),
            'shown_count': banner.shown_count + 1,
        }

        # For now we show banner only once per N days. But even after shown banner we have to wait
        # to the end of that period. So banner might be considered as "active", but we don't
        # show it because we are already shown it once.
        if not banner.is_shown:
            update_data['first_shown'] = datetime_utils.utc_now()
            update_data['date_expired'] = datetime_utils.utc_now() + PROMO_BANNER_ACTIVE_PERIOD

        await db.update_promo_banner(conn, banner_id=banner.id, data=update_data)
