import sqlalchemy as sa

from app.banner.enums import (
    BannerActivityPeriod,
    BannerAudienceType,
    BannerColor,
    BannerIncomingDocumentsSignCount,
    BannerOutgoingDocumentsCount,
    BannerRate,
    BannerStatus,
    EmployeesCount,
    PromoBannerCampaign,
)
from app.lib.enums import Language
from app.models import columns, metadata
from app.models.types import SoftEnum

banner_table = sa.Table(
    'banners',
    metadata,
    columns.UUID(),
    columns.DateTime('start_date', nullable=True),
    columns.DateTime('end_date', nullable=True),
    columns.Text('analytics_category', nullable=True),
    columns.SoftEnum('color', BannerColor, nullable=False),
    columns.SoftEnum('status', BannerStatus, nullable=False),
    # Conditions:
    # To which position to show the banner. For example, show banner only to "accountant"
    columns.Array('positions', sa.Text(), nullable=True),
    # To companies with which rate to show the banner. For example, show banner only to companies
    # with "PRO" rate
    columns.Array('rates', SoftEnum(BannerRate), nullable=True),
    columns.SoftEnum('audience_type', BannerAudienceType, nullable=True),
    sa.Column('days_before_signature_expires', sa.Integer, nullable=True),
    columns.Array(
        'outgoing_documents_count', SoftEnum(BannerOutgoingDocumentsCount), nullable=True
    ),
    columns.Array(
        'incoming_documents_sign_count', SoftEnum(BannerIncomingDocumentsSignCount), nullable=True
    ),
    sa.Column('activity_period', SoftEnum(BannerActivityPeriod), nullable=True),
    columns.Array('employees_count', SoftEnum(EmployeesCount), nullable=True),
    columns.ForeignKey('created_by_role_id', 'roles.id', ondelete='NO ACTION'),
    columns.DateUpdated(),
    columns.DateCreated(),
)

# Localized content of banner
banner_content_table = sa.Table(
    'banners_content',
    metadata,
    columns.UUID(),
    columns.ForeignKey('banner_id', 'banners.id', ondelete='CASCADE'),
    columns.SoftEnum('language', Language, nullable=False),
    columns.Text('text'),
    columns.Text('link_text'),
    columns.Text('link_url'),
    sa.UniqueConstraint('banner_id', 'language', name='uix_banner_language'),
)


# List of users who should see the banner containing promotional content about Vchasno.Kasa
# https://vchasno-group.atlassian.net/browse/EC-7
banner_promo_kasa_table = sa.Table(
    'banner_promo_kasa',
    metadata,
    columns.UUID(),
    columns.EDRPOU('edrpou', nullable=False),
    columns.Email('email', nullable=False),
    columns.Counter('show_count'),
    sa.UniqueConstraint('edrpou', 'email', name='uix_banner_promo_kasa_edrpou_email'),
)


# Current promo banner for the user
promo_banner_table = sa.Table(
    'banner_promo',
    metadata,
    columns.UUID(),
    columns.ForeignKey('user_id', 'users.id', ondelete='CASCADE', nullable=False),
    columns.EDRPOU('edrpou', nullable=False),
    columns.SoftEnum('campaign', PromoBannerCampaign, nullable=True),
    # Track shown status of the banner
    columns.DateTime('first_shown', nullable=True),
    columns.DateTime('last_shown', nullable=True),
    columns.Counter('shown_count', default=0),
    # Date after which we need to recalculate current promo campaign for the user
    columns.DateTime('date_expired', nullable=False),
    sa.UniqueConstraint(
        'user_id',
        'edrpou',
        name='uix_banner_promo_user_id_edrpou',
    ),
)
