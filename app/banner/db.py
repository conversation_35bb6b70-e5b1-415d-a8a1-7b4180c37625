from datetime import datetime

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.banner.enums import BannerAudienceType, BannerPosition, BannerRate, BannerStatus
from app.banner.schemas import BannerPromoKasaItemSchema, PromoBannerSchema
from app.banner.tables import (
    banner_content_table,
    banner_promo_kasa_table,
    banner_table,
    promo_banner_table,
)
from app.banner.types import Banner, BannerContent, BannerForGraph
from app.lib.database import DBConnection
from app.lib.types import DataDict
from app.models import select_all, select_one


async def select_overlapping_banners(
    conn: DBConnection,
    start_date: datetime,
    end_date: datetime,
    positions: list[BannerPosition] | None,
    rates: list[BannerRate] | None,
    audience_type: BannerAudienceType | None,
    ignore_banner_id: str | None = None,
) -> list[Banner]:
    # based on stack overflow answer
    # https://stackoverflow.com/a/325964/7133756
    #
    # Ranges do not overlap when:
    # (StartA > EndB) OR (EndA < StartB)
    #
    # Ranges overlaps when:
    # (StartA <= EndB) AND (EndA >= StartB)

    filters = [
        banner_table.c.start_date < end_date,
        banner_table.c.end_date > start_date,
        banner_table.c.status != BannerStatus.deleted,
    ]

    if positions is None and rates is None and audience_type is None:
        # for banner without conditions, check that banner has no overlapping
        # with other banners without conditions
        filters.append(banner_table.c.positions.is_(None))
        filters.append(banner_table.c.rates.is_(None))
        filters.append(banner_table.c.audience_type.is_(None))
    else:
        # for banners with conditions, check that banner has no overlapping by at least one
        # condition
        conditions = []
        if positions is not None:
            conditions.append(banner_table.c.positions.overlap(positions))
        if rates is not None:
            conditions.append(banner_table.c.rates.overlap(rates))
        if audience_type is not None:
            conditions.append(banner_table.c.audience_type == audience_type)
        filters.append(sa.or_(*conditions))

    # Can be useful on updating date range
    if ignore_banner_id is not None:
        filters.append(banner_table.c.id != ignore_banner_id)

    rows = await select_all(
        conn=conn,
        query=(sa.select([banner_table.c.id]).select_from(banner_table).where(sa.and_(*filters))),
    )

    banners_ids = [row.id for row in rows]
    return await select_banners_with_content(
        conn=conn,
        banner_ids=banners_ids,
    )


async def select_banners_with_content(conn: DBConnection, banner_ids: list[str]) -> list[Banner]:
    if not banner_ids:
        return []
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    banner_table,
                    banner_content_table.c.language,
                    banner_content_table.c.text,
                    banner_content_table.c.link_text,
                    banner_content_table.c.link_url,
                ]
            )
            .select_from(banner_table.join(banner_content_table))
            .where(banner_table.c.id.in_(banner_ids))
        ),
    )
    banners = {}
    for row in rows:
        banner = Banner.from_db(row)
        if banner.id_ not in banners:
            banners[banner.id_] = banner
            banners[banner.id_].content = []

        banners[banner.id_].content.append(  # type: ignore
            BannerContent(
                language=row.language,
                text=row.text,
                link_text=row.link_text,
                link_url=row.link_url,
            )
        )

    return list(banners.values())


async def select_banner_by_id(conn: DBConnection, banner_id: str) -> Banner | None:
    row = await select_one(
        conn=conn, query=sa.select([banner_table]).where(banner_table.c.id == banner_id)
    )
    return Banner.from_db(row) if row else None


async def select_planned_banners(conn: DBConnection, time: datetime) -> list[Banner]:
    row = await select_all(
        conn=conn,
        query=(
            sa.select([banner_table]).where(
                sa.and_(
                    banner_table.c.start_date <= time,
                    banner_table.c.end_date > time,
                    banner_table.c.status == BannerStatus.new,
                )
            )
        ),
    )
    return [Banner.from_db(row) for row in row]


async def select_active_banners(conn: DBConnection) -> list[Banner]:
    rows = await select_all(
        conn=conn,
        query=(sa.select([banner_table]).where(banner_table.c.status == BannerStatus.active)),
    )
    return [Banner.from_db(row) for row in rows]


async def select_active_banner_for_graph(conn: DBConnection) -> list[BannerForGraph]:
    """
    Select ether-position-specific banner or banner for all positions
    """

    # Select all active banners, usually from 1 to 5 active banners
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    banner_table.c.id,
                    banner_table.c.positions,
                    banner_table.c.rates,
                    banner_table.c.audience_type,
                    banner_table.c.days_before_signature_expires,
                    banner_table.c.outgoing_documents_count,
                    banner_table.c.incoming_documents_sign_count,
                    banner_table.c.activity_period,
                    banner_table.c.employees_count,
                    banner_table.c.date_created,
                ]
            )
            .select_from(banner_table)
            .where(sa.and_(banner_table.c.status == BannerStatus.active))
        ),
    )

    return [BannerForGraph.from_db(row) for row in rows]


async def select_visible_banners(
    conn: DBConnection, offset: int | None = None, limit: int | None = None
) -> list[Banner]:
    query = (
        sa.select([banner_table])
        .select_from(banner_table)
        .where(banner_table.c.status != BannerStatus.deleted)
        .order_by(banner_table.c.date_created.desc())
    )
    if offset is not None:
        query = query.offset(offset)

    if limit is not None:
        query = query.limit(limit)

    rows = await select_all(conn=conn, query=query)
    return [Banner.from_db(row) for row in rows]


async def insert_banner(conn: DBConnection, data: DataDict) -> None:
    await conn.execute(conn=conn, query=banner_table.insert().values(data))


async def insert_banner_content(conn: DBConnection, data: list[DataDict]) -> None:
    await conn.execute(banner_content_table.insert().values(data))


async def update_banner(conn: DBConnection, data: DataDict) -> None:
    banner_id = data.pop('id')
    await conn.execute(banner_table.update().where(banner_table.c.id == banner_id).values(data))


async def update_banner_content(conn: DBConnection, data: list[DataDict], banner_id: str) -> None:
    if not data:
        return

    stmt = insert(banner_content_table).values(data)

    query = stmt.on_conflict_do_update(
        index_elements=[
            banner_content_table.c.banner_id,
            banner_content_table.c.language,
        ],
        set_={
            'language': stmt.excluded.language,
            'text': stmt.excluded.text,
            'link_text': stmt.excluded.link_text,
            'link_url': stmt.excluded.link_url,
        },
    ).returning(banner_content_table)

    updated_rows = await select_all(conn, query)

    # Delete not updated rows
    content_ids = [item.id for item in updated_rows]
    await conn.execute(
        query=(
            banner_content_table.delete().where(
                sa.and_(
                    banner_content_table.c.banner_id == banner_id,
                    banner_content_table.c.id.notin_(content_ids),
                )
            )
        )
    )


async def delete_banner_promo_kasa(
    conn: DBConnection,
    items: list[BannerPromoKasaItemSchema],
) -> None:
    await conn.execute(
        query=(
            banner_promo_kasa_table.delete().where(
                sa.tuple_(
                    banner_promo_kasa_table.c.edrpou,
                    banner_promo_kasa_table.c.email,
                ).in_(sa.tuple_(item.edrpou, item.email) for item in items),
            )
        )
    )


async def insert_banner_promo_kasa(
    conn: DBConnection,
    items: list[BannerPromoKasaItemSchema],
) -> None:
    await conn.execute(
        insert(banner_promo_kasa_table)
        .values([{'edrpou': item.edrpou, 'email': item.email} for item in items])
        .on_conflict_do_nothing(
            index_elements=[banner_promo_kasa_table.c.edrpou, banner_promo_kasa_table.c.email]
        )
    )


async def select_is_kasa_promo_targeted(
    conn: DBConnection,
    edrpou: str,
    email: str,
) -> bool:
    """
    Check if the user is targeted for Kasa promo banner. This list is manually uploaded by the
    super admin to the database.
    """
    row = await select_one(
        conn=conn,
        query=(
            sa.select([banner_promo_kasa_table.c.id])
            .select_from(banner_promo_kasa_table)
            .where(
                sa.and_(
                    banner_promo_kasa_table.c.edrpou == edrpou,
                    banner_promo_kasa_table.c.email == email,
                )
            )
        ),
    )
    return bool(row)


async def select_promo_banner(
    conn: DBConnection,
    *,
    user_id: str,
    edrpou: str,
    for_update: bool = False,
) -> PromoBannerSchema | None:
    query = (
        sa.select([promo_banner_table])
        .select_from(promo_banner_table)
        .where(
            sa.and_(
                promo_banner_table.c.user_id == user_id,
                promo_banner_table.c.edrpou == edrpou,
            )
        )
        .limit(1)
    )
    if for_update:
        query = query.with_for_update()

    row = await select_one(conn=conn, query=query)

    return PromoBannerSchema.from_db(row) if row else None


async def insert_promo_banner(
    conn: DBConnection,
    *,
    data: DataDict,
) -> None:
    """
    Insert a new promo banner into the database.
    """
    await conn.execute(promo_banner_table.insert().values(data))


async def update_promo_banner(
    conn: DBConnection,
    *,
    banner_id: str,
    data: DataDict,
) -> None:
    """
    Update promo banner in the database.
    """
    query = (
        promo_banner_table.update()
        .where(promo_banner_table.c.id == banner_id)
        .values(data)
        .returning(promo_banner_table)
    )
    await conn.execute(query)
