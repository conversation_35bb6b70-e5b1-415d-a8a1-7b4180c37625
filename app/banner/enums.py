from __future__ import annotations

from enum import Enum, StrEnum, auto, unique

from app.auth.enums import EmployeePositions
from app.billing.enums import AccountRate
from app.lib.enums import NamedEnum


class BannerColor(Enum):
    yellow = 'yellow'
    green = 'green'
    blue = 'blue'


class BannerStatus(Enum):
    # only created, but never shown to the user
    new = 'new'

    # active banner
    active = 'active'

    # hidden by end time or by admin
    hidden = 'hidden'

    # deleted from admin page of banners
    deleted = 'deleted'


class BannerRate(NamedEnum):
    """
    Enum for banners conditions by company rates.

    These rates are not a one-to-one match with "AccountRate" and should be considered
    only as conditions for banners
    """

    free = auto()
    start = auto()
    pro = auto()
    ultimate = auto()
    trial = auto()
    archive_small = auto()
    archive_big = auto()

    @staticmethod
    def from_account_rate(rate: AccountRate) -> BannerRate | None:
        """
        Convert from billing AccountRate to BannerRate
        """
        if rate.is_free:
            return BannerRate.free
        if rate.is_trial:
            return BannerRate.trial
        if rate.is_start:
            return BannerRate.start
        if rate.is_pro:
            return BannerRate.pro
        if rate.is_ultimate:
            return BannerRate.ultimate
        if rate.is_archive_small:
            return BannerRate.archive_small
        if rate.is_archive_big:
            return BannerRate.archive_big
        return None


class BannerAudienceType(StrEnum):
    TOV = 'TOV'
    FOP = 'FOP'

    @property
    def is_tov(self) -> bool:
        return self == BannerAudienceType.TOV

    @property
    def is_fop(self) -> bool:
        return self == BannerAudienceType.FOP

    @staticmethod
    def from_edrpou(edrpou: str) -> BannerAudienceType:
        from app.auth.utils import is_fop

        return BannerAudienceType.FOP if is_fop(edrpou) else BannerAudienceType.TOV


class BannerOutgoingDocumentsCount(Enum):
    zero = 'zero'
    range_1_10 = '1_10'
    range_11_24 = '11_24'
    range_25_50 = '25_50'
    range_51_100 = '51_100'
    range_100_plus = '100_plus'

    def in_range(self, count: int) -> bool:
        """
        Check if the count of outgoing documents is in the range of this enum value.
        """
        match self:
            case BannerOutgoingDocumentsCount.zero:
                return count == 0
            case BannerOutgoingDocumentsCount.range_1_10:
                return 1 <= count <= 10
            case BannerOutgoingDocumentsCount.range_11_24:
                return 11 <= count <= 24
            case BannerOutgoingDocumentsCount.range_25_50:
                return 25 <= count <= 50
            case BannerOutgoingDocumentsCount.range_51_100:
                return 51 <= count <= 100
            case BannerOutgoingDocumentsCount.range_100_plus:
                return count > 100
            case _:
                raise NotImplementedError


class BannerIncomingDocumentsSignCount(Enum):
    zero = 'zero'
    range_1_10 = '1_10'
    range_11_24 = '11_24'
    range_25_50 = '25_50'
    range_51_100 = '51_100'
    range_100_plus = '100_plus'
    has_unsigned = 'has_unsigned'  # has incoming unsigned documents

    def in_range(self, incoming_count: int, unsigned_count: int | None) -> bool:
        """
        Check if the count of signed incoming documents is in the range of this enum value.
        For 'has_unsigned', use the has_unsigned argument.
        """
        match self:
            case BannerIncomingDocumentsSignCount.zero:
                return incoming_count == 0
            case BannerIncomingDocumentsSignCount.range_1_10:
                return 1 <= incoming_count <= 10
            case BannerIncomingDocumentsSignCount.range_11_24:
                return 11 <= incoming_count <= 24
            case BannerIncomingDocumentsSignCount.range_25_50:
                return 25 <= incoming_count <= 50
            case BannerIncomingDocumentsSignCount.range_51_100:
                return 51 <= incoming_count <= 100
            case BannerIncomingDocumentsSignCount.range_100_plus:
                return incoming_count > 100
            case BannerIncomingDocumentsSignCount.has_unsigned:
                return bool(unsigned_count)


class BannerActivityPeriod(Enum):
    last_1_month = '1month'
    last_2_months = '2months'
    last_3_months = '3months'
    last_6_months = '6months'
    last_12_months = '12months'

    def days_in_period(self) -> int:
        """
        Return the number of days in the period.
        """
        match self:
            case BannerActivityPeriod.last_1_month:
                return 30
            case BannerActivityPeriod.last_2_months:
                return 60
            case BannerActivityPeriod.last_3_months:
                return 90
            case BannerActivityPeriod.last_6_months:
                return 180
            case BannerActivityPeriod.last_12_months:
                return 365


class EmployeesCount(Enum):
    range_1_10 = '1-10'
    range_11_50 = '11-50'
    range_51_100 = '51-100'
    range_101_500 = '101-500'
    range_500_1000 = '500-1000'
    range_1000_plus = 'більше 1000'
    unknown = 'unknown'

    def in_range(self, value: str | None) -> bool:
        if value is None:
            return self is EmployeesCount.unknown

        # If value is a digit, convert to int
        if value.isdigit():
            num = int(value)
            match self:
                case EmployeesCount.range_1_10:
                    return 1 <= num <= 10
                case EmployeesCount.range_11_50:
                    return 11 <= num <= 50
                case EmployeesCount.range_51_100:
                    return 51 <= num <= 100
                case EmployeesCount.range_101_500:
                    return 101 <= num <= 500
                case EmployeesCount.range_500_1000:
                    return 501 <= num <= 1000
                case EmployeesCount.range_1000_plus:
                    return num > 1000
        # If value is a range label, compare directly
        else:
            return value == self.value

        return False


@unique
class BannerPositionFilter(NamedEnum):
    other = 'other'

    @classmethod
    def from_role_position(cls, raw_position: str) -> BannerPosition | None:
        return EmployeePositions.from_role_position(raw_position) or BannerPositionFilter.other

    @property
    def name_ua(self) -> str:
        """
        Return Ukrainian name of the position
        """
        return BANNER_POSITION_NAMES_UK[self]


BANNER_POSITION_NAMES_UK = {
    BannerPositionFilter.other: 'Інше',
}

type BannerPosition = BannerPositionFilter | EmployeePositions


class SABannerPromoKasaAction(Enum):
    update = 'update'
    delete = 'delete'


class PromoBannerCampaign(Enum):
    mobile_app = 'mobile_app'
    kasa = 'kasa'
