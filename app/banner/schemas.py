from __future__ import annotations

from datetime import datetime

import pydantic

from app.banner.enums import PromoBannerCampaign, SABannerPromoKasaAction
from app.lib import validators_pydantic as pv
from app.lib.database.types import DBRow
from app.lib.types import DataDict


class BannerPromoKasaItemSchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU
    email: pv.Email


class SABannerPromoKasaSchema(pydantic.BaseModel):
    action: SABannerPromoKasaAction
    items: list[BannerPromoKasaItemSchema]


class PromoBannerSchema(pydantic.BaseModel):
    id: str
    user_id: str
    edrpou: str
    campaign: PromoBannerCampaign | None
    first_shown: datetime | None
    last_shown: datetime | None
    shown_count: int

    # Date after which we need to recalculate current promo campaign for the user
    date_expired: datetime

    @staticmethod
    def from_db(data: DBRow) -> PromoBannerSchema:
        return PromoBannerSchema.model_validate(data, from_attributes=True)

    @property
    def is_shown(self) -> bool:
        """
        Check if the banner has been shown at least once.
        """
        return self.shown_count > 0


class PromoBannerResponse(pydantic.BaseModel):
    campaign: PromoBannerCampaign | None
    shown: bool

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json')
