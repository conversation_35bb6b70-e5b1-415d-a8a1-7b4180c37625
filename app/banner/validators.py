from datetime import datetime
from textwrap import shorten

import pydantic
from aiohttp import web

from api.errors import DoesNotExist, InvalidRequest, Object
from app.auth.types import User
from app.banner.db import select_banner_by_id, select_overlapping_banners
from app.banner.enums import (
    BannerActivityPeriod,
    BannerAudienceType,
    BannerColor,
    BannerIncomingDocumentsSignCount,
    BannerOutgoingDocumentsCount,
    BannerPosition,
    BannerRate,
    BannerStatus,
    EmployeesCount,
    PromoBannerCampaign,
)
from app.banner.types import Banner, BannerContent
from app.banner.utils import can_change_status
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.datetime_utils import UTC_TZ
from app.lib.enums import Language
from app.lib.helpers import generate_uuid


class BannerContentSchema(pydantic.BaseModel):
    language: Language
    text: str = pydantic.Field(min_length=1, max_length=130)
    link_text: str = pydantic.Field(min_length=1, max_length=100)
    link_url: pv.URL


class _BaseBannerSchema(pydantic.BaseModel):
    color: BannerColor
    start_date: datetime | None
    end_date: datetime | None
    analytics_category: str | None
    audience_type: BannerAudienceType | None = None
    days_before_signature_expires: int | None = None
    content: list[BannerContentSchema]
    positions: list[BannerPosition] | None = pydantic.Field(default=None, min_length=1)
    rates: list[BannerRate] | None = pydantic.Field(default=None, min_length=1)
    outgoing_documents_count: list[BannerOutgoingDocumentsCount] | None = pydantic.Field(
        default=None, min_length=1
    )
    incoming_documents_sign_count: list[BannerIncomingDocumentsSignCount] | None = pydantic.Field(
        default=None, min_length=1
    )
    activity_period: BannerActivityPeriod | None = None
    employees_count: list[EmployeesCount] | None = pydantic.Field(default=None)


class CreateBannerValidator(_BaseBannerSchema):
    pass


class UpdateBannerValidator(_BaseBannerSchema):
    banner_id: pv.UUID
    status: BannerStatus


class UpdatePromoBannerShownValidator(pydantic.BaseModel):
    campaign: PromoBannerCampaign


def _to_banner_content(data: BannerContentSchema) -> BannerContent:
    return BannerContent(
        language=data.language,
        text=data.text,
        link_text=data.link_text,
        link_url=data.link_url,
    )


async def validate_banner_exists(conn: DBConnection, banner_id: str) -> Banner:
    banner = await select_banner_by_id(conn, banner_id)
    if not banner:
        raise DoesNotExist(Object.banner, banner_id=banner_id)
    return banner


def validate_banner_content(items: list[BannerContentSchema]) -> list[BannerContent]:
    content = [_to_banner_content(item) for item in items]
    languages = {item.language for item in content}
    if len(languages) != len(content):
        raise InvalidRequest(reason=_('Language must be unique'))
    return content


def validate_banner_status(current: BannerStatus, next_: BannerStatus) -> None:
    """Validate ability to change banner status"""

    if current == next_:
        return

    if not can_change_status(current=current, next_=next_):
        raise InvalidRequest(reason=_('Не можливо змінити статус банера'))


async def validate_banner_date(
    conn: DBConnection,
    start_date: datetime | None,
    end_date: datetime | None,
    positions: list[BannerPosition] | None,
    audience_type: BannerAudienceType | None,
    rates: list[BannerRate] | None,
    ignore_banner_id: str | None,
) -> None:
    """
    Validate that bot start and end date is None or is not None.
    """
    if not start_date and not end_date:
        return

    if not start_date or not end_date:
        raise InvalidRequest(
            reason=_('Початкова і кінцева дата мають бути або заповнені, або пусті')
        )

    end_date = end_date.replace(tzinfo=UTC_TZ)
    start_date = start_date.replace(tzinfo=UTC_TZ)
    if end_date < start_date:
        raise InvalidRequest(reason=_('Кінцева дата має бути більшою за початкову'))

    overlapping_banners = await select_overlapping_banners(
        conn=conn,
        start_date=start_date,
        end_date=end_date,
        ignore_banner_id=ignore_banner_id,
        audience_type=audience_type,
        positions=positions,
        rates=rates,
    )
    if overlapping_banners:
        banners_uk = ', '.join(
            f'"{shorten(content.text, width=20, placeholder="...")}"'
            for banner in overlapping_banners
            for content in banner.content or []
            if banner.content and content.language == Language.uk
        )
        raise InvalidRequest(
            reason=(
                _('Вже є банери, що пересікається по часу з вказаним: {banners}').bind(
                    banners=banners_uk
                )
            )
        )


async def validate_create_banner(conn: DBConnection, request: web.Request, user: User) -> Banner:
    """Validate ability to create banner"""
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(CreateBannerValidator, raw_data)

    await validate_banner_date(
        conn=conn,
        start_date=data.start_date,
        end_date=data.end_date,
        positions=data.positions,
        audience_type=data.audience_type,
        rates=data.rates,
        ignore_banner_id=None,
    )

    content = validate_banner_content(data.content)
    return Banner(
        id_=generate_uuid(),
        color=data.color,
        start_date=data.start_date,
        end_date=data.end_date,
        content=content,
        status=BannerStatus.new,
        created_by_role_id=user.role_id,
        analytics_category=data.analytics_category,
        positions=data.positions,
        rates=data.rates,
        audience_type=data.audience_type,
        days_before_signature_expires=data.days_before_signature_expires,
        outgoing_documents_count=data.outgoing_documents_count,
        incoming_documents_sign_count=data.incoming_documents_sign_count,
        activity_period=data.activity_period,
        employees_count=data.employees_count,
    )


async def validate_update_banner(conn: DBConnection, request: web.Request) -> Banner:
    """Validate ability to edit banner"""

    # Prepare data for validation
    raw_data = await validators.validate_json_request(request)
    raw_data = {**raw_data, 'banner_id': request.match_info['banner_id']}

    # Validate schema JSON schema
    data = validators.validate_pydantic(UpdateBannerValidator, raw_data)

    banner = await validate_banner_exists(conn, banner_id=data.banner_id)

    await validate_banner_date(
        conn=conn,
        start_date=data.start_date,
        end_date=data.end_date,
        positions=data.positions,
        audience_type=data.audience_type,
        rates=data.rates,
        ignore_banner_id=banner.id_,
    )

    validate_banner_status(current=banner.status, next_=data.status)

    content = validate_banner_content(data.content)

    return Banner(
        id_=banner.id_,
        color=data.color,
        status=data.status,
        start_date=data.start_date,
        end_date=data.end_date,
        positions=data.positions,
        rates=data.rates,
        created_by_role_id=banner.created_by_role_id,
        content=content,
        analytics_category=data.analytics_category,
        audience_type=data.audience_type,
        days_before_signature_expires=data.days_before_signature_expires,
        outgoing_documents_count=data.outgoing_documents_count,
        incoming_documents_sign_count=data.incoming_documents_sign_count,
        activity_period=data.activity_period,
        employees_count=data.employees_count,
    )
