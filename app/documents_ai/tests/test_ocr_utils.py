import pytest

from app.documents_ai.ocr_utils import OcrRawDataProcessor
from app.documents_ai.types import OCRDataRaw, OCRPageInfo, OCRProcessedDataItem


@pytest.mark.parametrize(
    ('header1', 'header2', 'expected'),
    [
        (['foo', 'bar'], ['foo', 'bar'], True),
        (['foo', 'bar'], ['bar', 'foo'], False),
        (['foo', 'bar'], ['foo', 'baz'], True),
        (['', ''], ['', ''], True),
        (['', ''], ['a', 'b'], False),
    ],
)
def test_is_tables_headers_are_similar(header1, header2, expected):
    assert OcrRawDataProcessor._is_tables_headers_are_similar(header1, header2) is expected


@pytest.mark.parametrize(
    ('row_data', 'expected'),
    [
        ([OCRProcessedDataItem(text='1', page=0, bbox=[])], True),
        ([OCRProcessedDataItem(text='1.0', page=0, bbox=[])], True),
        ([OCRProcessedDataItem(text='foo', page=0, bbox=[])], False),
        ([OCRProcessedDataItem(text='', page=0, bbox=[])], False),
        ([], False),
    ],
)
def test_is_data_row(row_data, expected):
    assert OcrRawDataProcessor._is_data_row(row_data) is expected


def test_process_final_tables():
    table1 = [
        [
            OCRProcessedDataItem(text='№', page=0, bbox=[]),
            OCRProcessedDataItem(text='foo', page=0, bbox=[]),
        ]
    ]
    table2 = [
        [
            OCRProcessedDataItem(text='bar', page=0, bbox=[]),
            OCRProcessedDataItem(text='baz', page=0, bbox=[]),
        ],
        [
            OCRProcessedDataItem(text='1', page=0, bbox=[]),
            OCRProcessedDataItem(text='2', page=0, bbox=[]),
        ],
    ]
    tables = [table1, table2]
    processed_tables = OcrRawDataProcessor._process_final_tables(tables)
    assert len(processed_tables[0][0]) == 1
    assert processed_tables[0][0][0].text == 'foo'
    assert len(processed_tables[1][0]) == 1


def test_reconstruct_table():
    table_block = {
        'BlockType': 'TABLE',
        'Id': 'table-id',
        'Relationships': [
            {
                'Type': 'CHILD',
                'Ids': ['cell1-id', 'cell2-id'],
            }
        ],
    }
    blocks_map = {
        'cell1-id': {
            'BlockType': 'CELL',
            'Id': 'cell1-id',
            'RowIndex': 1,
            'ColumnIndex': 1,
            'Relationships': [
                {
                    'Type': 'CHILD',
                    'Ids': ['word1-id'],
                }
            ],
            'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.1, 'Width': 0.1, 'Height': 0.1}},
        },
        'word1-id': {
            'BlockType': 'WORD',
            'Id': 'word1-id',
            'Text': 'hello',
        },
        'cell2-id': {
            'BlockType': 'CELL',
            'Id': 'cell2-id',
            'RowIndex': 1,
            'ColumnIndex': 2,
            'Relationships': [
                {
                    'Type': 'CHILD',
                    'Ids': ['word2-id'],
                }
            ],
            'Geometry': {'BoundingBox': {'Left': 0.2, 'Top': 0.1, 'Width': 0.1, 'Height': 0.1}},
        },
        'word2-id': {
            'BlockType': 'WORD',
            'Id': 'word2-id',
            'Text': 'world',
        },
    }
    processor = OcrRawDataProcessor(OCRDataRaw(pages_info=[], ocr_results=[]))
    table = processor._reconstruct_table(table_block, blocks_map, 0, 1000, 1000)
    assert len(table) == 1
    assert len(table[0]) == 2
    assert table[0][0].text == 'hello'
    assert table[0][1].text == 'world'


def test_process():
    ocr_results = [
        {
            'Blocks': [
                {
                    'BlockType': 'LINE',
                    'Id': 'line1-id',
                    'Text': 'hello',
                    'Geometry': {
                        'BoundingBox': {'Left': 0.1, 'Top': 0.1, 'Width': 0.1, 'Height': 0.1}
                    },
                },
                {
                    'BlockType': 'TABLE',
                    'Id': 'table-id',
                    'Relationships': [
                        {
                            'Type': 'CHILD',
                            'Ids': ['cell1-id'],
                        }
                    ],
                },
                {
                    'BlockType': 'CELL',
                    'Id': 'cell1-id',
                    'RowIndex': 1,
                    'ColumnIndex': 1,
                    'Relationships': [
                        {
                            'Type': 'CHILD',
                            'Ids': ['word1-id'],
                        }
                    ],
                    'Geometry': {
                        'BoundingBox': {'Left': 0.1, 'Top': 0.1, 'Width': 0.1, 'Height': 0.1}
                    },
                },
                {
                    'BlockType': 'WORD',
                    'Id': 'word1-id',
                    'Text': 'world',
                },
            ]
        }
    ]
    ocr_data_raw = OCRDataRaw(
        pages_info=[OCRPageInfo(number=0, width=1000, height=1000)], ocr_results=ocr_results
    )
    processor = OcrRawDataProcessor(ocr_data_raw)
    processed_data = processor.process()
    assert len(processed_data.text_lines) == 1
    assert processed_data.text_lines[0].text == 'hello'
    assert len(processed_data.tables) == 1
    assert len(processed_data.tables[0]) == 1
    assert len(processed_data.tables[0][0]) == 1
    assert processed_data.tables[0][0][0].text == 'world'
