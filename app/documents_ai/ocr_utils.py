import typing as t
from difflib import <PERSON><PERSON><PERSON>atch<PERSON>

import pymupdf

from app.documents_ai.types import OCRDataRaw, OCRProcessedData, OCRProcessedDataItem
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.types import DataDict
from app.services import services

if t.TYPE_CHECKING:
    from types_aiobotocore_textract.type_defs import (
        AnalyzeDocumentResponseTypeDef,
        DetectDocumentTextResponseTypeDef,
    )

# Default DPI for creating image from PDF page.
# default recommendations - use 300 dpi, but we got good test results with 250 dpi,
# so start with that value, it helps to reduce image size, which is important for OCR providers.
DEFAULT_DPI = 250


async def get_raw_ocr_data(document_content: bytes) -> OCRDataRaw:
    """
    Performing OCR on a document for further structured data extraction.
    Responsible for converting document pages to images
    and calling the OCR provider (e.g. AWS Textract).
    """

    if not services.textract_client:
        raise RuntimeError('OCR client not configured')

    pages_info = []
    images_bytes = []
    try:
        # Pymupdf supports different file types, not only PDF.
        # We use it to open any document from ALLOWED_EXTENSIONS__STRUCTURED_DATA
        # as a PDF for further processing.
        pdf_doc = pymupdf.open(stream=document_content)

        for i, page in enumerate(pdf_doc.pages()):
            pix = page.get_pixmap(dpi=DEFAULT_DPI)
            # TODO: it is possible to run some pre-processing for images to improve OCR quality,
            #  e.g. convert to grayscale, remove noise, etc.
            #  Leave it for future improvements if needed.
            images_bytes.append(pix.tobytes())
            pages_info.append(
                {
                    'number': i,
                    'width': pix.width,
                    'height': pix.height,
                }
            )
    finally:
        if 'pdf_doc' in locals():
            pdf_doc.close()

    if not images_bytes:
        raise RuntimeError('No images extracted from PDF')

    ocr_results: list[AnalyzeDocumentResponseTypeDef | DetectDocumentTextResponseTypeDef] = []

    # If the document has multiple pages, use OCR with the TABLES feature;
    # then LLM will process the result with a specific prompt.
    should_use_ocr_with_tables = bool(
        not get_flag(FeatureFlags.DISABLE_SHORTENED_STRUCTURED_DATA_EXTRACTION)
        and len(images_bytes) > 1
    )

    if should_use_ocr_with_tables:
        # TODO: Currently, AWS Textract has low rate-limits, so we avoid using asyncio.gather
        #  or TaskGroup, but consider such improvements in the future
        for img in images_bytes:
            ocr_results.append(
                await services.textract_client.analyze_document(
                    FeatureTypes=['TABLES'],
                    Document={'Bytes': img},
                )
            )
    else:
        for img in images_bytes:
            ocr_results.append(
                await services.textract_client.detect_document_text(Document={'Bytes': img})
            )

    return OCRDataRaw.from_dict(
        {
            'pages_info': pages_info,
            'ocr_results': ocr_results,
        }
    )


class OcrRawDataProcessor:
    """
    Processor for raw OCR data obtained from OCR service ASW Textract.
    It is responsible for parsing the raw OCR response and
    transforming it into a structured format for further using in
    document structured data extraction.

    Textract docs for resp structure etc:
    https://docs.aws.amazon.com/textract/latest/dg/API_AnalyzeDocument.html
    https://docs.aws.amazon.com/textract/latest/dg/API_DetectDocumentText.html
    """

    def __init__(self, ocr_data_raw: OCRDataRaw):
        self._ocr_data_raw = ocr_data_raw
        self._blocks_map_by_page: dict[int, dict[str, DataDict]] = {}
        self._table_content_ids_by_page: dict[int, set[str]] = {}

        for page_num, resp in enumerate(self._ocr_data_raw.ocr_results):
            blocks_map = {block['Id']: block for block in resp['Blocks']}
            self._blocks_map_by_page[page_num] = blocks_map
            table_blocks = [b for b in resp['Blocks'] if b['BlockType'] == 'TABLE']

            table_content_ids: set[str] = set()
            for table in table_blocks:
                table_content_ids.add(table['Id'])
                for child_id in self._get_all_child_ids(table, blocks_map):
                    table_content_ids.add(child_id)
            self._table_content_ids_by_page[page_num] = table_content_ids

    def process(self) -> OCRProcessedData:
        text_lines = self._extract_text_lines()
        tables = self._extract_tables()
        return OCRProcessedData(text_lines=text_lines, tables=tables)

    def _extract_text_lines(self) -> list[OCRProcessedDataItem]:
        """Extracts all lines of text that are not part of a tables."""
        ocr_text_lines: list[OCRProcessedDataItem] = []
        for page_num, resp in enumerate(self._ocr_data_raw.ocr_results):
            page_info = self._ocr_data_raw.pages_info[page_num]
            page_width = page_info.width
            page_height = page_info.height
            blocks_map = self._blocks_map_by_page[page_num]
            table_content_ids = self._table_content_ids_by_page[page_num]

            for block in resp['Blocks']:
                if block['BlockType'] != 'LINE':
                    continue

                line_child_ids = self._get_all_child_ids(block, blocks_map)
                if line_child_ids & table_content_ids:
                    continue

                text = block['Text']
                geometry = block['Geometry']['BoundingBox']
                bbox = self._calculate_bbox(geometry, page_width, page_height)

                ocr_text_lines.append(
                    OCRProcessedDataItem(
                        text=text,
                        page=page_num,
                        bbox=bbox,
                    )
                )
        return ocr_text_lines

    def _extract_tables(self) -> list[list[list[OCRProcessedDataItem]]]:
        """Extracts and reconstructs tables from the raw OCR data."""
        ocr_tables: list[list[list[OCRProcessedDataItem]]] = []
        for page_num, resp in enumerate(self._ocr_data_raw.ocr_results):
            page_info = self._ocr_data_raw.pages_info[page_num]
            page_width = page_info.width
            page_height = page_info.height
            blocks_map = self._blocks_map_by_page[page_num]
            table_blocks = [b for b in resp['Blocks'] if b['BlockType'] == 'TABLE']

            if not table_blocks:
                continue

            for table in table_blocks:
                table_data = self._reconstruct_table(
                    table_block=table,
                    blocks_map=blocks_map,
                    page_num=page_num,
                    page_width=page_width,
                    page_height=page_height,
                )
                if not table_data:
                    continue

                current_header = [cell.text for cell in table_data[0]]
                current_num_columns = len(current_header)

                if ocr_tables:
                    previous_table = ocr_tables[-1]
                    previous_header = [cell.text for cell in previous_table[0]]
                    previous_num_columns = len(previous_header)

                    is_headers_similar = self._is_tables_headers_are_similar(
                        current_header, previous_header
                    )
                    is_first_row_data = self._is_data_row(table_data[0])

                    if is_headers_similar or (
                        current_num_columns == previous_num_columns and is_first_row_data
                    ):
                        if is_first_row_data:
                            ocr_tables[-1].extend(table_data)
                        else:
                            ocr_tables[-1].extend(table_data[1:])
                    else:
                        ocr_tables.append(table_data)
                else:
                    ocr_tables.append(table_data)

        return self._process_final_tables(ocr_tables)

    def _get_all_child_ids(self, block: DataDict, blocks_map: dict[str, DataDict]) -> set[str]:
        """Recursively finds all descendant block IDs for a given block."""
        child_ids: set[str] = set()
        for rel in block.get('Relationships', []):
            if rel['Type'] == 'CHILD':
                for child_id in rel['Ids']:
                    child_ids.add(child_id)
                    child_block = blocks_map.get(child_id)
                    if child_block:
                        child_ids.update(self._get_all_child_ids(child_block, blocks_map))
        return child_ids

    def _reconstruct_table(
        self,
        table_block: DataDict,
        blocks_map: dict[str, DataDict],
        page_num: int,
        page_width: int,
        page_height: int,
    ) -> list[list[OCRProcessedDataItem]]:
        """Reconstructs a single table from Textract blocks to a simpler data structure."""
        rows: dict[int, dict[int, OCRProcessedDataItem]] = {}
        cell_ids = [
            rel['Ids'] for rel in table_block.get('Relationships', []) if rel['Type'] == 'CHILD'
        ]
        if not cell_ids:
            return []

        flat_cell_ids = [item for sublist in cell_ids for item in sublist]

        for cell_id in flat_cell_ids:
            cell = blocks_map.get(cell_id)
            if not cell:
                continue

            row_index = cell['RowIndex']
            col_index = cell['ColumnIndex']

            cell_text = ''
            if 'Relationships' in cell:
                for rel in cell['Relationships']:
                    if rel['Type'] == 'CHILD':
                        text_ids = rel['Ids']
                        words = [blocks_map.get(tid, {}).get('Text', '') for tid in text_ids]
                        cell_text = ' '.join(words)

            geometry = cell['Geometry']['BoundingBox']
            bbox = self._calculate_bbox(geometry, page_width, page_height)

            if row_index not in rows:
                rows[row_index] = {}
            rows[row_index][col_index] = OCRProcessedDataItem(
                text=cell_text,
                bbox=bbox,
                page=page_num,
            )

        if not rows:
            return []

        max_cols = max(max(r.keys()) for r in rows.values())
        table_list: list[list[OCRProcessedDataItem]] = []
        for row_idx in sorted(rows.keys()):
            row_list = [
                rows[row_idx].get(col_idx, OCRProcessedDataItem(text='', bbox=[], page=page_num))
                for col_idx in range(1, max_cols + 1)
            ]
            table_list.append(row_list)

        return table_list

    @staticmethod
    def _calculate_bbox(geometry: dict[str, float], page_width: int, page_height: int) -> list[int]:
        """
        Calculates the bounding box coordinates from the relative geometry provided by AWS Textract.
        Returns a list of integers representing the bounding box
        in the format [x_min, y_min, x_max, y_max].
        """
        x_min = int(geometry['Left'] * page_width)
        y_min = int(geometry['Top'] * page_height)
        x_max = int((geometry['Left'] + geometry['Width']) * page_width)
        y_max = int((geometry['Top'] + geometry['Height']) * page_height)
        return [x_min, y_min, x_max, y_max]

    @staticmethod
    def _is_tables_headers_are_similar(header1: list[str], header2: list[str]) -> bool:
        """
        Calculates the similarity between two table headers to determine if one table
        is a continuation of the other across a page break.

        The function calculates the similarity ratio for each pair of corresponding header cells
        using `SequenceMatcher`. The comparison is case-insensitive.
        The threshold is 0.7 and was chosen empirically.
        """
        threshold = 0.7
        if not header1 or not header2 or len(header1) != len(header2):
            return False
        ratios = [
            SequenceMatcher(None, str(h1).lower(), str(h2).lower()).ratio()
            for h1, h2 in zip(header1, header2)
        ]
        average_ratio = sum(ratios) / len(ratios) if ratios else 0
        return average_ratio >= threshold

    @staticmethod
    def _is_data_row(row_data: list[OCRProcessedDataItem]) -> bool:
        """Checks if a row appears to be a data row based on heuristics."""
        if not row_data:
            return False
        for cell in row_data:
            cell_text = cell.text.strip()
            if cell_text and cell_text.isdigit():
                return True
            try:
                float(cell_text)
                return True
            except ValueError:
                pass
        return False

    @staticmethod
    def _process_final_tables(
        final_tables: list[list[list[OCRProcessedDataItem]]],
    ) -> list[list[list[OCRProcessedDataItem]]]:
        """Removes the first column of each table if it is a row-enumeration."""
        enumerate_col_names = {'№', 'Номер', 'Номер п/п', 'No'}
        for table_list in final_tables:
            if not table_list or not table_list[0]:
                continue
            first_col_is_enum = False
            if any(n in table_list[0][0].text for n in enumerate_col_names):
                first_col_is_enum = True
            elif len(table_list) > 1:
                all_digits = True
                for row in table_list[1:]:
                    if not row or not row[0].text.strip().isdigit():
                        all_digits = False
                        break
                if all_digits:
                    first_col_is_enum = True

            if first_col_is_enum:
                for i in range(len(table_list)):
                    table_list[i] = table_list[i][1:]
        return final_tables
