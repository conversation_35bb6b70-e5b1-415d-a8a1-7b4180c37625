import sqlalchemy as sa

from app.documents_ai.enums import ModelName
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.models import columns, metadata

# A table for storing the document meta information suggested by AI model
# It is temporary table for analyzing data
#
# See the app.documents_ai.schemas.DocumentMetaSuggest for detailed fields information
documents_meta_suggestions_table = sa.Table(
    'document_meta_suggestions',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        name='document_id',
        mixed='documents.id',
        primary_key=False,
        ondelete='CASCADE',
        nullable=False,
        index=True,
    ),
    # begin: suggested data
    sa.Column('category', sa.SmallInteger(), nullable=True),
    columns.EDRPOU('edrpou_recipient', nullable=True),
    columns.DateTime('date_document', nullable=True),
    sa.Column('number', sa.String(512), nullable=True),
    sa.Column('amount', sa.BigInteger(), nullable=True),
    # end: suggested data
    sa.Column('chunk_length', sa.Integer(), nullable=False),
    sa.Column('tokens_used', sa.Integer(), nullable=False),
    sa.Column('request_duration', sa.Float(), nullable=False),
    columns.SoftEnum(
        'model', ModelName, nullable=False, server_default=ModelName.claude_sonnet_3_5
    ),
    columns.DateCreated(),
)


# Table for tracking structured data extraction jobs per (company, document)
# Stores status transitions and errors;
document_structured_data_table = sa.Table(
    'document_structured_data',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        name='document_id',
        mixed='documents.id',
        primary_key=False,
        ondelete='CASCADE',
        nullable=False,
        index=True,
    ),
    columns.ForeignKey(
        name='company_id',
        mixed='companies.id',
        primary_key=False,
        ondelete='CASCADE',
        nullable=False,
        index=True,
    ),
    columns.SoftEnum('status', SDAStatus, nullable=False, server_default=SDAStatus.PENDING),
    sa.Column('error_message', sa.Text(), nullable=True),
    columns.SoftEnum('llm_name', ModelName, nullable=True),
    sa.Column('request_duration', sa.Float(), nullable=True),
    sa.Column('input_tokens', sa.Integer(), nullable=True),
    sa.Column('output_tokens', sa.Integer(), nullable=True),
    columns.DateCreated(),
    columns.DateUpdated(),
    sa.Index(
        'ix_document_structured_data_document_id_company_id',
        'company_id',
        'document_id',
        unique=True,
    ),
)
