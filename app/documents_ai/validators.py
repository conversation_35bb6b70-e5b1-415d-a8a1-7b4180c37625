import typing as t

from aiohttp import web
from aiohttp.web_request import <PERSON><PERSON>ield

from api.errors import AccessDenied, InvalidRequest
from app.auth.types import User
from app.auth.utils import get_company_config
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.db import select_latest_document_versions
from app.documents.validators import validate_documents_access
from app.documents_ai.constants import (
    ALLOWED_AI_SUGGEST_EXTENSIONS,
    ALLOWED_EXTENSIONS__STRUCTURED_DATA,
)
from app.documents_ai.types import StartDocumentsExtractionCtx
from app.i18n import _
from app.lib.database import DBConnection
from app.lib.helpers import run_sync
from app.lib.validators import validate_post_request
from app.services import services
from app.uploads.utils import prepare_file_details_from_filename

# Акти, накладні, рахунки
ALLOWED_DOCUMENT_CATEGORIES__STRUCTURED_DATA = {
    PublicDocumentCategory.act.value,
    PublicDocumentCategory.bill.value,
    PublicDocumentCategory.sales_invoice.value,
    PublicDocumentCategory.act_of_acceptance_transfer.value,
    PublicDocumentCategory.act_of_return.value,
    PublicDocumentCategory.verification_act.value,
    PublicDocumentCategory.act_of_adjustment.value,
    PublicDocumentCategory.write_off_act.value,
    PublicDocumentCategory.inventory_act.value,
    PublicDocumentCategory.return_delivery_note.value,
}


class UploadedFile(t.NamedTuple):
    name: str
    title: str
    extension: str
    content: bytes


async def validate_start_extraction_request(
    conn: DBConnection,
    user: User,
    document_ids: list[str],
) -> StartDocumentsExtractionCtx:
    documents = await validate_documents_access(conn=conn, user=user, doc_ids=document_ids)
    versions = await select_latest_document_versions(
        conn=conn,
        document_ids=document_ids,
        uploader_edrpou=user.company_edrpou,
        is_sent=True,
    )
    version_map = {v.document_id: v for v in versions}

    # Validate allowed extensions
    docs_with_wrong_extension = []
    for doc in documents:
        doc_version = version_map.get(doc.id)
        extension = doc_version.extension if doc_version else doc.extension
        if extension not in ALLOWED_EXTENSIONS__STRUCTURED_DATA:
            docs_with_wrong_extension.append(doc.id)
    if docs_with_wrong_extension:
        raise InvalidRequest(
            reason=_(
                'Формат документів не підтримується. Доступні формати документів: {extensions}'
            ).format(extensions=', '.join(ALLOWED_EXTENSIONS__STRUCTURED_DATA)),
            details={'wrong_document_ids': docs_with_wrong_extension},
        )

    # Validate allowed document types
    docs_with_wrong_categories = []
    for doc in documents:
        if doc.category not in ALLOWED_DOCUMENT_CATEGORIES__STRUCTURED_DATA:
            docs_with_wrong_categories.append(doc.id)
    if docs_with_wrong_categories:
        raise InvalidRequest(
            reason=_(
                'Тип документів не підтримується. Доступні типи документів: акти, рахунки, накладні'
            ),
            details={'wrong_document_ids': docs_with_wrong_categories},
        )

    return StartDocumentsExtractionCtx(
        documents=[
            StartDocumentsExtractionCtx.Item(
                doc_id=doc.id,
                version_id=version_map[doc.id].id if version_map.get(doc.id) else None,
            )
            for doc in documents
        ],
    )


async def validate_document_meta_suggest_enabled(company_id: str) -> None:
    async with services.db.acquire() as conn:
        company_config = await get_company_config(conn, company_id=company_id)
    if not company_config.allow_suggesting_document_meta_with_ai:
        raise AccessDenied(reason=_('Доступ обмежено адміністратором компанії'))


async def validate_upload_file(
    request: web.Request,
    allowed_extensions: set[str] = ALLOWED_AI_SUGGEST_EXTENSIONS,
) -> UploadedFile:
    post_data = await validate_post_request(request)
    file_item = None
    for item in post_data.values():
        if not isinstance(item, FileField):
            continue
        file_item = item

    if file_item is None:
        raise InvalidRequest(details=_('Не знайдено файл у запиті'))

    file_details = prepare_file_details_from_filename(file_item.filename)
    if file_details.extension not in allowed_extensions:
        raise InvalidRequest(
            reason=_('Недопустимий формат файлу'),
            details={'allowed': allowed_extensions},
        )

    file_content = await run_sync(file_item.file.read)
    file_item.file.close()

    return UploadedFile(
        title=file_details.title,
        name=file_details.name,
        extension=file_details.extension,
        content=file_content,
    )
