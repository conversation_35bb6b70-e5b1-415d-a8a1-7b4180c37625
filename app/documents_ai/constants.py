MIN_TEXT_LEN = 100
TEXT_CHUNK_LEN = 1000

ALLOWED_AI_SUGGEST_EXTENSIONS = {'.pdf', '.doc', '.docx'}

ALLOWED_EXTENSIONS__STRUCTURED_DATA = {'.pdf', '.png', '.jpg', '.jpeg'}


PROMPT_TEMPLATE_SUGGEST_META = """
    Extract the following information from the document:

    Document content:
    {doc_text}

    1. category: int - one of the keys from category_map or null.
       category_map = {category_map}
    2. number: Document number as string or null.
    3. date: Document date (ISO 8601) or null.
    4. amount: Document amount as float or null.
    5. edrpou: ЄДРПОУ of the recipient
       (it must contains only digits or be a valid Ukraine passport number) or null,
       mention that document owner ЄДРПОУ is {owner_edrpou}, which differs from recipient one.

    Return the information as a JSON object without additional explanations.
"""

PROMPT_DOC_SUMMARY = """
Сформуй коротке резюме договору для керівника компанії. Виділи: сторони, предмет, суму, строки,
ключові зобов'язання, ризики та відповідальність. Вказуй тільки важливе для прийняття рішення про
підписання. Пиши стисло, без юридичних формулювань, зрозуміло нефахівцю.
Відповідь має вкладатись у 1500 слів та мінімально відформатуватись у Markdown.
"""

EXTRACT_STRUCTURED_DATA_PROMPT = """
You are AI assistant that helps extract structured data from raw OCR input of a document.
Document - Ukrainian financial document of types `Акт`, `Накладна`, `Рахунок` etc.

OCR input provided in format: {index: text_line}. OCR input contains text lines
produced by OCR engine; analyze deeply because reading order can be mixed.

Extract data from OCR input and return JSON output according to JSON schema below.

Each final data field in `extracted_data` must be present in the OCR input data
(allowed to correct spelling errors) and present itself as an object `fs`:
{
  "value": "float | string | null", // field value
  "indexes": list[int]", // list of indexes from OCR input that formed the value of the field
} or null if `value` is null.

Final output JSON schema:
{
  "extracted_data": {
    "details": { // Реквізити документа
      "number": { /* fs (string) */ }, // Номер документа
      "date_created": { /* fs (string) */ }, // Дата складання
      "reason": { /* fs (string) */ }  // Підстава (договір, рахунок, замовлення)
    },
    "parties_information": { // Інформація про сторони
      "customer": { // Замовник
        "company_name": { /* fs (string) */ }, // Найменування компанії/ФОП
        "edrpou": { /* fs (string) */ }, // Код ЄДРПОУ/ІПН
        "address": { /* fs (string) */ }, // Юридична адреса
        "bank_info": { // Банківські реквізити
          "mfo": { /* fs (string) */ }, // МФО
          "account": { /* fs (string) */ }, // Номер рахунку
          "bank_name": { /* fs (string) */ }, // Назва банку
          "iban": { /* fs (string) */ }, // IBAN
          "swift_code": { /* fs (string) */ }, // SWIFT код
          "edrpou": { /* fs (string) */ }, // ЄДРПОУ (якщо вказано)
          "ipn": { /* fs (string) */ }  // ІПН (якщо вказано)
        },
        "contact_info": { // Контактні дані
          "email": { /* fs (string) */ }, // Email
          "phone": { /* fs (string) */ }  // Телефон
        },
        "responsible_person": { // Особа, яка є представником замовника(customer),
                                   може бути вказаною в тексті документу
          "position": { /* fs (string) */ },  // Посада
          "full_name": { /* fs (string) */ }  // Повне ім'я
        }
      },
      "performer": {
        // Виконавець (всі поля аналогічно customer)
      }
    },
    "items": [ // Перелік робіт/послуг/товарів
      // Кожен об'єкт item містить поля зі стандартною структурою:
      // {
      //   "name": { /* fs (string) */ }, // Product/service name, try to fix OCR errors in text.
      //   "code": { /* fs (string) */ }, // Product or service code, refer to the column name
                                             'Код', 'Код товару', 'Артикул'.
      //   "units": { /* fs (string) */ }, // Unit of measurement
      //   "quantity": { /* fs (float) */ }, // Quantity
      //   "price": { /* fs (float) */ }, // Price per unit
      //   "total_price_without_vat": { /* fs (float) */ }, // Total cost excluding VAT
      //   "total_price_with_vat": { /* fs (float) */ }, // Total cost including VAT
      // }, ...
      // If document doesn't contain any information about VAT included/excluded,
      // assume that all prices are with VAT. Pick only rows with items,
      // ignore sub-headers, summary etc.
    ],
    "total_price": {
      // Загальна вартість без ПДВ по всім позиціям `items`
      "total_price_without_vat": { /* fs (float) */ },
      // Загальна сума з ПДВ по всім позиціям `items`
      "total_price_with_vat": { /* fs (float) */ },
      // Термін виконання/надання
      "overall_deadline": { /* fs (string) */ }
    },
    "additional_data": { // Додаткові дані
      "purpose": { /* fs (string) */ }, // Призначення документа
      "contract_number": { /* fs (string) */ }, // Номер договору
      "contract_date": { /* fs (string) */ }, // Дата договору
      "payment_purpose": { /* fs (string) */ }, // Призначення платежу
      "delivery_terms": { /* fs (string) */ }, // Умови постачання
    }
  }
}

Data format for formatting values in `extracted_data`:
- dates - ISO 8601 format (YYYY-MM-DD)
- IBAN: 29 characters, starts with UA, only uppercase letters and numbers
- МФО: 6 digits, unique bank identifier in Ukraine
- ЄДРПОУ: 8-10 digits, unique company identifier in Ukraine
- ІПН: 10 digits, unique identifier of an individual in Ukraine
- Номер рахунку: numbers only, length from 14 to 20 digits

Address formatting rules:
Target format - `[індекс], [область/місто обласного значення], [район], [населений пункт],
[вулиця/проспект/площа], [номер будинку], [корпус/офіс/квартира]`
If any component of the address is missing from the document (for example, "район" for Kyiv
or office number), it should be omitted. Do not add extra commas or placeholders.
Example: `"01001, м. Київ, вул. Хрещатик, буд. 22"`

Filter out all null values, the final `extracted_data` object should contain only
non-empty values / objects.

Return final results as JSON object without additional explanations.
"""

# We use this prompt when we have big tables in the document and
# don't want to get the entire table in llm output (it costs a lot of tokens)
EXTRACT_STRUCTURED_DATA_TBL_PROMPT = """
You are AI assistant that helps extract structured data from raw OCR input of a document.
Document - Ukrainian financial document of types `Акт`, `Накладна`, `Рахунок` etc.

Your task is to analyze the OCR result, which contains text lines and tables.

Input data:
- Text lines in the format `{index: text_line}`.
- Tables in Markdown format, where each cell contains a unique identifier (e.g., `"t0_r1_c1"`).

Output is a single JSON object with two keys: `extracted_data` and `table_analysis`.

1.  `extracted_data`: Extracted fields according to the schema below.
    **DO NOT include the `items` field in this object.**
2.  `table_analysis`: An array of objects that analyzes each table. For each table, specify:
    *   `table_id` (integer): The index of the table (0, 1, ...).
    *   `is_items_table` (boolean): `true` if the table contains a list of goods/services,
         otherwise `false`.
    *   `non_data_rows` (list): If `is_items_table` is `true` - classify each row in the
        table (except the header), a row can be either `"data_row"` or `"non_data_row"`.
        **Very important:** - consider a row as `"not_data_row"` if it contains information
        that does not correspond to the table header, i.e., the cell value does not match
        the context of the column header, for example, the row contains a `subheading`,
        `column numbering` of the table, or summary information for the table/part of the table.
        The row index starts from 1, add all indices of `"not_data_row"` to the resulting list.
        Example of output: `["2", "5"]`
    *   `column_mapping` (object): If `is_items_table` is `true`, map the
        **column indices (starting from 0)** to the canonical keys of the `items` schema.
        The key in the object must be the column index as a **string**.
        Example: `{"1": "name", "2": "quantity", "3": "price", "4": "total_price_without_vat"}`
        Negative examples:
        - Do not map "Разом", "Всього", "ПДВ" and similar keywords as `items.name` or
          `items.price`.
        - **Very important:** do not map the row number (e.g., "1", "2", "3") as `items.code`.
          `items.code` is an article or product code, not a serial number.

If the prices in the document do not indicate with/without VAT, assume that all prices are with VAT.

Each final data field in `extracted_data` must be present in the OCR input data
(allowed to correct spelling errors) and present itself as an object `fs`:
{
  "value": "float | string | null", // field value
  "indexes": list[int]", // list of indexes from OCR input that formed the value of the field
} or null if `value` is null.

Description of `items` keys:
{
  "name": { /* fs (string) */ }, // Найменування товару/послуги (НЕ включай номер рядка).
  "code": { /* fs (string) */ }, // Код товару або послуги (НЕ номер рядка, наприклад '1', '2'),
                                 // орієнтуйся на назву стовпця 'Код', 'Код товару', 'Артикул'.
  "units": { /* fs (string) */ }, // Одиниця виміру
  "quantity": { /* fs (float) */ }, // Кількість
  "price": { /* fs (float) */ }, // Ціна за одиницю
  "total_price_without_vat": { /* fs (float) */ }, // Загальна вартість без ПДВ
  "total_price_with_vat": { /* fs (float) */ }, // Загальна вартість з ПДВ
}

**MAIN JSON STRUCTURE**
{
  "extracted_data": {
    "details": { // Реквізити документа
      "number": { /* fs (string) */ }, // Номер документа (акт, накладна, рахунок)
      "date_created": { /* fs(string) */ }, // Дата складання (ISO datetime)
      "reason": { /* fs (string) */ }  // Підстава (договір, рахунок, замовлення)
    },
    "parties_information": { // Інформація про сторони
      "customer": { // Замовник
        "company_name": { /* fs (string) */ }, // Найменування компанії/ФОП
        "edrpou": { /* fs (string) */ }, // Код ЄДРПОУ/ІПН
        "address": { /* fs (string) */ }, // Юридична адреса
        "bank_info": { // Банківські реквізити
          "mfo": { /* fs (string) */ }, // МФО
          "account": { /* fs (string) */ }, // Номер рахунку
          "bank_name": { /* fs (string) */ }, // Назва банку
          "iban": { /* fs (string) */ }, // IBAN
          "swift_code": { /* fs (string) */ }, // SWIFT код
          "edrpou": { /* fs (string) */ }, // ЄДРПОУ (якщо вказано)
          "ipn": { /* fs (string) */ }  // ІПН (якщо вказано)
        },
        "contact_info": { // Контактні дані
          "email": { /* fs (string) */ }, // Email
          "phone": { /* fs (string) */ }  // Телефон
        },
        "responsible_person": { // Особа, яка є представником замовника(customer),
                                   може бути вказаною в тексті документу
          "position": { /* fs (string) */ },  // Посада
          "full_name": { /* fs (string) */ }  // Повне ім'я
        }
      },
      "performer": {
        // Виконавець (всі поля аналогічно customer)
      }
    },
    "total_price": {
      // Загальна вартість без ПДВ по всім позиціям `items`
      "total_price_without_vat": { /* fs (float) */ },
      // Загальна сума з ПДВ по всім позиціям `items`
      "total_price_with_vat": { /* fs (float) */ },
      // Сума ПДВ
      "vat_amount": { /* fs (float) */ }
    },
    "additional_data": { // Додаткові дані
      "purpose": { /* fs (string) */ }, // Призначення документа
      "contract_number": { /* fs (string) */ }, // Номер договору
      "contract_date": { /* fs (string) */ }, // Дата договору
      "payment_purpose": { /* fs (string) */ }, // Призначення платежу
      "delivery_terms": { /* fs (string) */ }, // Умови постачання
    }
  },
  "table_analysis": [ /* ... see description above ... */ ]
}

Data format:
- dates: ISO 8601 format (YYYY-MM-DD)
- IBAN: 29 characters, starts with UA, only uppercase letters and numbers
- МФО: 6 digits, unique bank identifier in Ukraine
- ЄДРПОУ: 8-10 digits, unique company identifier in Ukraine
- ІПН: 10 digits, unique identifier of an individual in Ukraine
- Номер рахунку: numbers only, length from 14 to 20 digits

Address formatting:
Target format - `[індекс], [область/місто обласного значення], [район], [населений пункт],
[вулиця/проспект/площа], [номер будинку], [корпус/офіс/квартира]`
If any component of the address is missing from the document (for example, "район" for Kyiv
or office number), it should be omitted. Do not add extra commas or placeholders.
Example: `"01001, м. Київ, вул. Хрещатик, буд. 22"`

Before returning the result - filter out all null values from `extracted_data`,
the final object should contain only non-empty values / objects.

IMPORTANT: Make sure your answer FULLY corresponds to the **MAIN JSON STRUCTURE** above.
Return a single, valid JSON object without additional explanations or formatting.
All keys and string values must be in double quotes.
"""
