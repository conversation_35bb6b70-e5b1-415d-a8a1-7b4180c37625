import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.documents_ai.enums import (
    StructuredDataErrorKey,
)
from app.documents_ai.enums import (
    StructuredDataExtractionStatus as SDAStatus,
)
from app.documents_ai.schemas import DocumentMetaSuggest, MetaInfo
from app.documents_ai.tables import document_structured_data_table, documents_meta_suggestions_table
from app.documents_ai.types import DocumentDataExtraction
from app.lib.database import DBConnection
from app.lib.types import DataDict
from app.models import select_all


async def insert_document_suggestion(conn: DBConnection, data: DocumentMetaSuggest) -> None:
    await conn.execute(documents_meta_suggestions_table.insert().values(data.to_db()))


async def select_document_suggestions(
    conn: DBConnection, document_id: str
) -> list[DocumentMetaSuggest]:
    rows = await select_all(
        conn,
        query=documents_meta_suggestions_table.select().where(
            documents_meta_suggestions_table.c.document_id == document_id
        ),
    )
    return [DocumentMetaSuggest.from_db(row) for row in rows]


async def insert_or_replace_extractions(
    conn: DBConnection,
    *,
    company_id: str,
    data: list[DocumentDataExtraction],
) -> set[str]:
    """
    If extraction is exists and its in ERROR status - allow to update it.
    """

    to_upsert = set()
    to_ignore = set()
    document_ids = {item.document_id for item in data}
    data_by_document_id = {item.document_id: item for item in data}

    existing_extractions = await select_extractions_for_documents(
        conn, document_ids=list(document_ids), company_id=company_id
    )
    for item in existing_extractions:
        if item.status == SDAStatus.ERROR:
            to_upsert.add(item.document_id)
        else:
            # just ignore documents which already have extractions without errors
            to_ignore.add(item.document_id)

    # process the rest of doc_ids without existing extractions
    for doc_id in document_ids:
        if doc_id not in to_ignore:
            to_upsert.add(doc_id)

    if to_upsert:
        insert_data = [data_by_document_id[doc_id].to_db() for doc_id in to_upsert]
        query = (
            insert(document_structured_data_table)
            .values(insert_data)
            .returning(document_structured_data_table.c.document_id)
        )

        query = query.on_conflict_do_update(
            index_elements=[
                document_structured_data_table.c.company_id,
                document_structured_data_table.c.document_id,
            ],
            set_={
                'date_updated': sa.text('now()'),
                'status': sa.text('EXCLUDED.status'),
                'error_message': sa.text('EXCLUDED.error_message'),
                'llm_name': None,
                'request_duration': None,
                'input_tokens': None,
                'output_tokens': None,
            },
        )
        res = await select_all(conn, query)
        return {row.document_id for row in res}

    return set()


async def update_extraction_status(
    conn: DBConnection,
    *,
    document_id: str,
    company_id: str,
    status: SDAStatus,
    error_message: StructuredDataErrorKey | None = None,
    meta_data: MetaInfo | None = None,
) -> None:
    values: DataDict = {
        'status': status,
        'error_message': error_message.value if error_message else None,
    }
    if meta_data:
        values.update(meta_data.to_db())
    await conn.execute(
        sa.update(document_structured_data_table)
        .where(
            sa.and_(
                document_structured_data_table.c.document_id == document_id,
                document_structured_data_table.c.company_id == company_id,
            )
        )
        .values(values)
    )


async def select_extractions_for_documents(
    conn: DBConnection, *, company_id: str, document_ids: list[str]
) -> list[DocumentDataExtraction]:
    rows = await select_all(
        conn,
        sa.select([document_structured_data_table]).where(
            sa.and_(
                document_structured_data_table.c.document_id.in_(document_ids),
                document_structured_data_table.c.company_id == company_id,
            )
        ),
    )
    return [DocumentDataExtraction.from_row(row) for row in rows]
