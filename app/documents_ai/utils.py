import asyncio
import logging
import re
import subprocess
import tempfile
import time
import typing as t
from decimal import ROUND_HALF_UP, Decimal
from functools import partial
from io import BytesIO, StringIO

import pymupdf
import uj<PERSON>
from docx import Document
from docx.table import Table
from docx.text.paragraph import Paragraph
from pydantic import ValidationError

from api.downloads.types import to_document
from api.downloads.utils import download_document_content
from app.auth.types import User
from app.document_categories.db import select_available_document_categories
from app.document_categories.types import DocumentCategory
from app.document_versions.utils import (
    get_latest_document_version_available_for_company,
)
from app.documents.db import select_document_by_id
from app.documents.types import DocumentWithUploader
from app.documents.utils import get_document_s3_key
from app.documents.validators import validate_document_access
from app.documents_ai.constants import (
    ALLOWED_AI_SUGGEST_EXTENSIONS,
    EXTRACT_STRUCTURED_DATA_PROMPT,
    EXTRACT_STRUCTURED_DATA_TBL_PROMPT,
    MIN_TEXT_LEN,
    PROMPT_DOC_SUMMARY,
    PROMPT_TEMPLATE_SUGGEST_META,
    TEXT_CHUNK_LEN,
)
from app.documents_ai.db import insert_document_suggestion
from app.documents_ai.enums import ModelName, ModelProvider
from app.documents_ai.errors import DocumentNoContentError
from app.documents_ai.ocr_utils import OcrRawDataProcessor, get_raw_ocr_data
from app.documents_ai.schemas import (
    DocumentMetaSuggest,
    DocumentSummaryResponse,
    ModelResponse,
    StructuredDataModel,
)
from app.documents_ai.types import OCRDataRaw, OCRProcessedData, OCRProcessedDataItem
from app.documents_required_fields.enums import DocumentCategoryFields
from app.lib import s3_utils
from app.lib.helpers import generate_uuid, run_sync_in_process_pool, to_json
from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)


DEFAULT_SUGGEST_MODEL_NAME = ModelName.claude_haiku_3
MODEL_ID_BY_NAME = {
    ModelName.claude_haiku_3: 'anthropic.claude-3-haiku-20240307-v1:0',
    ModelName.claude_sonnet_3_5: 'anthropic.claude-3-5-sonnet-20240620-v1:0',
    ModelName.claude_sonnet_3_7: 'eu.anthropic.claude-3-7-sonnet-20250219-v1:0',
}


# list of models for making Bedrock API calls (for test purposes)
AVAILABLE_MODELS = [
    {
        'provider': ModelProvider.anthropic,
        'id': 'eu.anthropic.claude-3-7-sonnet-20250219-v1:0',
        'label': 'Claude 3.7 Sonnet',
        'price': {
            'input': 0.003,
            'output': 0.015,
        },
    },
    {
        'provider': ModelProvider.anthropic,
        'id': 'anthropic.claude-3-haiku-20240307-v1:0',
        'label': 'Claude 3 Haiku',
        'price': {
            'input': 0.00025,
            'output': 0.00125,
        },
    },
    {
        'provider': ModelProvider.amazon,
        'id': 'eu.amazon.nova-micro-v1:0',
        'label': 'Amazon Nova Micro',
        'price': {
            'input': 0.000035,
            'output': 0.00000875,
        },
    },
]


def get_document_ocr_s3_key(document_id: str) -> str:
    """
    It is the raw OCR data for document_id,
    we use it as initial data for further processing by company
    """
    return f'ocr_data/{document_id}'


def get_document_structured_data_s3_key(document_id: str) -> str:
    """
    It is the raw structured_data for document_id,
    we use it as initial data for further processing by company
    """
    return f'structured_data/{document_id}'


def get_company_document_structured_data_s3_key(document_id: str, company_id: str) -> str:
    """
    Structured_data for document_id, managed by company_id
    """
    return f'company_structured_data/{company_id}_{document_id}'


async def download_document_bytes(*, document_id: str, version_id: str | None) -> bytes | None:
    s3_key = get_document_s3_key(document_id=document_id, version_id=version_id)
    content, _metadata = await s3_utils.download(s3_utils.DownloadFile(key=s3_key))
    return content


async def download_document_structured_data(
    document_id: str, user: User
) -> StructuredDataModel | None:
    """
    Loads structured data from S3 - company-specific structured data has priority
    """
    s3_key_company = get_company_document_structured_data_s3_key(
        document_id=document_id, company_id=user.company_id
    )
    s3_key_global = get_document_structured_data_s3_key(document_id)

    if await s3_utils.exists(s3_key_company):
        s3_key = s3_key_company
    elif await s3_utils.exists(s3_key_global):
        s3_key = s3_key_global
    else:
        return None

    content_bytes, _metadata = await s3_utils.download(s3_utils.DownloadFile(key=s3_key))

    structured_data_dict = ujson.loads(content_bytes.decode())

    return StructuredDataModel.model_validate(structured_data_dict)


async def save_extraction_outputs_to_s3(
    *,
    document_id: str,
    ocr_json: DataDict | None = None,
    structured_data_json: DataDict | None = None,
) -> None:
    if structured_data_json:
        await s3_utils.upload(
            s3_utils.UploadFile(
                key=get_document_structured_data_s3_key(document_id),
                body=to_json(structured_data_json).encode(),
            )
        )
    if ocr_json:
        await s3_utils.upload(
            s3_utils.UploadFile(
                key=get_document_ocr_s3_key(document_id), body=to_json(ocr_json).encode()
            )
        )


async def get_document_summary(document_id: str, user: User) -> list[DocumentSummaryResponse]:
    """
    Use different LLM models to generate a summary for a document
    """
    async with services.db.acquire() as conn:
        row = await select_document_by_id(conn, document_id=document_id)
        if not row:
            logger.info('Document not found', extra={'document_id': document_id})
            return []

        await validate_document_access(conn=conn, user=user, document_id=document_id)

        document = to_document(DocumentWithUploader.from_row(row))

        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document_id,
            company_edrpou=user.company_edrpou,
        )
        extension = document_version.extension if document_version else document.extension

        if extension not in ALLOWED_AI_SUGGEST_EXTENSIONS:
            logger.info(
                'Unsupported document extension',
                extra={
                    'document_id': document_id,
                    'extension': extension,
                    'supported_extensions': ', '.join(ALLOWED_AI_SUGGEST_EXTENSIONS),
                },
            )
            return []

        content = await download_document_content(
            conn, document, version_id=document_version.id if document_version else None
        )

    return await _get_document_summaries_with_bedrock(
        document_content=content,
        extension=extension,
    )


async def suggest_document_meta_by_content(
    content: bytes,
    extension: str,
    owner_edrpou: str,
) -> DocumentMetaSuggest | None:
    """
    Do AI suggest for meta-information based on document content,
    Persist data in DB (temp)
    """

    async with services.db_readonly.acquire() as conn:
        document_categories = await select_available_document_categories(
            conn=conn,
            company_edrpou=owner_edrpou,
        )

    return await _suggest_document_meta_with_bedrock(
        owner_edrpou=owner_edrpou,
        available_document_categories=document_categories,
        extension=extension,
        document_content=content,
        document_id=None,
    )


async def suggest_document_meta_by_document_id(
    document_id: str, user: User
) -> DocumentMetaSuggest | None:
    """
    Do AI suggest for meta-information based on document content,
    Persist data in DB (temp)
    """

    async with services.db.acquire() as conn:
        row = await select_document_by_id(conn, document_id=document_id)
        if not row:
            logger.info('Document not found', extra={'document_id': document_id})
            return None

        await validate_document_access(conn=conn, user=user, document_id=document_id)

        document = to_document(DocumentWithUploader.from_row(row))

        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document_id,
            company_edrpou=user.company_edrpou,
        )
        extension = document_version.extension if document_version else document.extension

        if extension not in ALLOWED_AI_SUGGEST_EXTENSIONS:
            logger.info(
                'Unsupported document extension',
                extra={
                    'document_id': document_id,
                    'extension': extension,
                    'supported_extensions': ', '.join(ALLOWED_AI_SUGGEST_EXTENSIONS),
                },
            )
            return None

        content = await download_document_content(
            conn, document, version_id=document_version.id if document_version else None
        )

        document_categories = await select_available_document_categories(
            conn=conn,
            company_edrpou=user.company_edrpou,
        )

    return await _suggest_document_meta_with_bedrock(
        owner_edrpou=document.owned_by_edrpou,
        available_document_categories=document_categories,
        document_content=content,
        document_id=document_id,
        extension=extension,
    )


async def process_document_with_ocr(
    document_id: str, version_id: str | None = None
) -> OCRProcessedData:
    """
    Process document content with OCR and return OCR results.
    Cache raw OCR results in S3 for possible future re-usage.
    """

    ocr_key = get_document_ocr_s3_key(document_id)
    if await s3_utils.exists(ocr_key):
        logger.info('OCR data found in S3, reusing it', extra={'document_id': document_id})
        ocr_bytes, _ = await s3_utils.download(s3_utils.DownloadFile(key=ocr_key))
        ocr_data_raw = OCRDataRaw.from_bytes(ocr_bytes)
    else:
        content = await download_document_bytes(document_id=document_id, version_id=version_id)
        if not content:
            raise DocumentNoContentError

        ocr_data_raw = await get_raw_ocr_data(content)

        await save_extraction_outputs_to_s3(
            document_id=document_id,
            ocr_json=ocr_data_raw.to_dict(),
        )

    return OcrRawDataProcessor(ocr_data_raw).process()


def _format_tables_to_markdown(tables: list[list[list[OCRProcessedDataItem]]]) -> str:
    """Formats a list of tables into a Markdown string for LLM input."""
    tables_string = ''
    for i, table in enumerate(tables):
        tables_string += f'Table {i}:\n'
        header = [cell.text for cell in table[0]]
        tables_string += f'| {" | ".join(header)} |\n'
        tables_string += f'|--{"|--".join(["-" * len(h) for h in header])}--|\n'
        for r, row in enumerate(table[1:]):
            row_with_ids = [f't{i}_r{r + 1}_c{c}: {cell.text}' for c, cell in enumerate(row)]
            tables_string += f'| {" | ".join(row_with_ids)} |\n'
    return tables_string


async def extract_structured_data_with_bedrock(
    ocr_data: OCRProcessedData,
    document_id: str,
) -> StructuredDataModel:
    """
    Extract structured data for document_id
    from prepared OCR results using Bedrock API.
    """

    if not services.bedrock_client:
        raise RuntimeError('Bedrock client not configured')

    model_name = ModelName.claude_sonnet_3_7

    if ocr_data.tables:
        prompt_parts = []
        text_lines_string = '\n'.join(
            [f'{idx}: {item.text}' for idx, item in enumerate(ocr_data.text_lines)]
        )
        prompt_parts.append(f'Text lines:\n{text_lines_string}')

        tables_string = _format_tables_to_markdown(ocr_data.tables)
        prompt_parts.append(f'Table data:\n{tables_string}')

        final_llm_input = '\n\n'.join(prompt_parts)
        prompt = f'{EXTRACT_STRUCTURED_DATA_TBL_PROMPT}\n{final_llm_input}'
    else:
        text_lines_string = '\n'.join(
            [f'{idx}: {item.text}' for idx, item in enumerate(ocr_data.text_lines)]
        )
        prompt = f'{EXTRACT_STRUCTURED_DATA_PROMPT}\nOCR data: {text_lines_string}'

    request_body = _prepare_body_for_provider(
        prompt=prompt,
        model_provider=model_name.provider,
        temperature=0,
        max_output_tokens=2000,
    )

    start_time = time.time()
    result = await services.bedrock_client.invoke_model(
        body=ujson.dumps(request_body),
        modelId=MODEL_ID_BY_NAME[model_name],
    )
    request_duration = time.time() - start_time
    async with result['body'] as stream:
        resp = await stream.read()

    model_response = _parse_model_response(resp, model_provider=model_name.provider)

    # fix unquoted table indexes, produced by llm sometimes
    raw_data = model_response.raw_data
    if ocr_data.tables:
        raw_data = re.sub(r'(?<!")\b(t\d+_r\d+_c\d+)\b(?!")', r'"\1"', model_response.raw_data)

    raw_llm_data = _parse_json_object_from_llm(raw_data)

    # Start with the non-table data from the LLM
    extracted_data = raw_llm_data.get('extracted_data', {})

    # If there are tables, process them and add them to the extracted_data
    if ocr_data.tables:
        table_analysis = raw_llm_data.get('table_analysis', [])
        table_items = _process_tables_data(
            tables_data=ocr_data.tables,
            table_analysis=table_analysis,
        )
        if table_items:
            extracted_data['items'] = table_items

    # Convert indexes to bboxes for all data
    processed_extracted_data = _prepare_structured_data_output(
        extracted_data=extracted_data,
        ocr_data=ocr_data.text_lines,
    )

    meta_data = {
        'llm_name': model_name,
        'request_duration': request_duration,
        'input_tokens': model_response.input_tokens,
        'output_tokens': model_response.output_tokens,
    }

    # Assemble the final model for validation
    final_model_data = {
        'extracted_data': processed_extracted_data,
        'meta': meta_data,
    }

    parsed = StructuredDataModel.model_validate(final_model_data)

    logger.info(
        'Structured data extracted',
        extra={
            'document_id': document_id,
            'request_llm_info': str(meta_data),
        },
    )

    pruned_data = _prune_empty_fields(parsed.to_dict())
    await save_extraction_outputs_to_s3(
        document_id=document_id,
        structured_data_json=pruned_data,
    )

    return parsed


def _process_tables_data(
    tables_data: list[list[list[OCRProcessedDataItem]]],
    table_analysis: list[DataDict],
) -> list[DataDict]:
    """
    Process tables data based on LLM analysis and build items list.
    """
    items = []
    for analysis in table_analysis:
        if not analysis.get('is_items_table'):
            continue

        table_id = analysis['table_id']
        column_mapping = analysis['column_mapping']
        non_data_rows_indexes = set(analysis.get('non_data_rows', []))
        table = tables_data[table_id]

        # Convert column_mapping keys to integers for direct indexing
        schema_to_col_index = {v: int(k) for k, v in column_mapping.items()}

        for r, row in enumerate(table[1:]):
            row_index = r + 1

            # Skip non-data rows
            if str(row_index) in non_data_rows_indexes:
                continue

            # Process as a regular item row
            item: DataDict = {}
            item_keys = {
                'name',
                'code',
                'price',
                'quantity',
                'total_price_without_vat',
                'total_price_with_vat',
                'units',
            }
            for schema_key, col_index in schema_to_col_index.items():
                if schema_key not in item_keys:
                    continue
                if col_index < len(row):
                    cell = row[col_index]
                    item[schema_key] = {
                        'value': cell.text,
                        'bboxes': [{'bbox': cell.bbox, 'page': cell.page}],
                    }
            if item:
                items.append(item)

    return items


def _clean_bboxes(bboxes: t.Any) -> list[dict[str, t.Any]] | None:
    """
    Clean and validate bboxes array, keeping only valid bbox objects.
    Returns None if no valid bboxes found.
    """
    if not isinstance(bboxes, list):
        return None

    cleaned_bboxes = []
    for bbox_obj in bboxes:
        if isinstance(bbox_obj, dict) and bbox_obj.get('bbox') and bbox_obj.get('page') is not None:
            cleaned_bboxes.append({'bbox': bbox_obj['bbox'], 'page': bbox_obj['page']})

    return cleaned_bboxes if cleaned_bboxes else None


def _prune_value_object(obj: dict[str, t.Any]) -> dict[str, t.Any] | None:
    """
    Handle special case for objects with 'value' and optional 'bboxes' keys.
    Returns None if value is None or empty string, otherwise returns cleaned object.
    """
    value = obj.get('value')

    # Consider None and empty strings as empty
    if value is None or (isinstance(value, str) and not value.strip()):
        return None

    result = {'value': value}

    # Clean bboxes if present
    bboxes = obj.get('bboxes')
    if bboxes is not None:
        cleaned_bboxes = _clean_bboxes(bboxes)
        if cleaned_bboxes:
            result['bboxes'] = cleaned_bboxes

    return result


def _prune_empty_fields(obj: t.Any) -> t.Any:
    """
    Recursively remove empty containers from parsed LLM structured_data output.
    Special handling for dicts: {'value': ..., 'bboxes': ...}.
    If value is None or empty string, drop the whole object.
    """
    if obj is None:
        return None

    if isinstance(obj, list):
        pruned_list: list[t.Any] = []
        for item in obj:
            pruned_item = _prune_empty_fields(item)
            if pruned_item is None:
                continue
            if isinstance(pruned_item, list | dict) and not pruned_item:
                continue
            pruned_list.append(pruned_item)
        return pruned_list if pruned_list else None

    if isinstance(obj, dict):
        # Special handling for value/bboxes objects
        if 'value' in obj and set(obj.keys()) <= {'value', 'bboxes'}:
            return _prune_value_object(obj)

        # Regular dict processing
        pruned_dict: dict[str, t.Any] = {}
        for key, value in obj.items():
            pruned_value = _prune_empty_fields(value)
            if pruned_value is None:
                continue
            if isinstance(pruned_value, list | dict) and not pruned_value:
                continue
            pruned_dict[key] = pruned_value
        return pruned_dict if pruned_dict else None

    return obj


def _parse_json_object_from_llm(s: str) -> DataDict:
    """
    Finds the first complete JSON object in a string from LLM response.
    Sometimes LLM response may contains extra text before and after the JSON object.
    """
    try:
        return ujson.loads(s)
    except ujson.JSONDecodeError:
        pass

    start_index = s.find('{')
    if start_index == -1:
        return {}

    brace_count = 1
    for i in range(start_index + 1, len(s)):
        if s[i] == '{':
            brace_count += 1
        elif s[i] == '}':
            brace_count -= 1

        if brace_count == 0:
            return ujson.loads(s[start_index : i + 1])

    return {}


def _prepare_structured_data_output(
    extracted_data: DataDict, ocr_data: list[OCRProcessedDataItem]
) -> DataDict:
    """
    Used for building a final data structure for structured data output.
    `extracted_data` contains values with indexes from ocr_data.
    We need to replace indexes with actual bounding boxes and page numbers.
    """
    ocr_index_map = {str(idx): item for idx, item in enumerate(ocr_data)}

    def _walk(obj: t.Any) -> t.Any:
        if obj is None:
            return None
        if isinstance(obj, list):
            return [_walk(x) for x in obj]
        if isinstance(obj, dict):
            # fs leaf
            if 'indexes' in obj:
                idxs = obj.get('indexes') or []
                bboxes = []
                for idx in idxs:
                    ocr_item = ocr_index_map.get(str(idx))
                    if ocr_item:
                        bboxes.append({'bbox': ocr_item.bbox, 'page': ocr_item.page})
                if bboxes:
                    obj['bboxes'] = bboxes
                obj.pop('indexes', None)
                return obj
            # recurse deeper
            return {k: _walk(v) for k, v in obj.items()}
        return obj

    return _walk(extracted_data) or {}


async def _get_document_summaries_with_bedrock(
    document_content: bytes,
    extension: str,
) -> list[DocumentSummaryResponse]:
    if not services.bedrock_client:
        logger.warning('Bedrock client not configured')
        return []

    document_text = await _extract_text_from_document(
        document_content=document_content,
        extension=extension,
    )
    if not document_text:
        logger.info('Got empty text from document')
        return []

    coroutines = []
    for model_data in AVAILABLE_MODELS:
        coroutines.append(
            _get_document_summary_with_bedrock(
                document_text=document_text,
                model_data=model_data,
            )
        )
    result = await asyncio.gather(*coroutines, return_exceptions=False)
    return list(filter(None, result))


async def _get_document_summary_with_bedrock(
    document_text: str,
    model_data: DataDict,
) -> DocumentSummaryResponse | None:
    assert services.bedrock_client, 'Expected bedrock client to be configured'

    prompt = '\n'.join([PROMPT_DOC_SUMMARY, document_text])
    body = _prepare_body_for_provider(
        prompt=prompt,
        model_provider=model_data['provider'],
    )

    time_start = time.time()
    try:
        result = await services.bedrock_client.invoke_model(
            body=ujson.dumps(body),
            modelId=model_data['id'],
        )
        request_duration = time.time() - time_start
        async with result['body'] as stream:
            resp = await stream.read()

        model_response = _parse_model_response(resp, model_provider=model_data['provider'])
        return DocumentSummaryResponse(
            summary=model_response.raw_data,
            model=model_data['label'],
            duration_seconds=round(request_duration, 1),
            input_tokens=model_response.input_tokens,
            output_tokens=model_response.output_tokens,
            total_price_usd=_calculate_total_price(
                input_tokens_price=model_data['price']['input'],
                output_tokens_price=model_data['price']['output'],
                input_tokens=model_response.input_tokens,
                output_tokens=model_response.output_tokens,
            ),
        )
    except Exception:
        logger.exception(
            'Exception during Bedrock API calls', extra={'model_data': str(model_data)}
        )
        return None


async def _suggest_document_meta_with_bedrock(
    owner_edrpou: str,
    available_document_categories: list[DocumentCategory],
    document_content: bytes,
    extension: str,
    document_id: str | None,
) -> DocumentMetaSuggest | None:
    if not services.bedrock_client:
        logger.warning('Bedrock client not configured')
        return None

    body = await _prepare_meta_suggest_bedrock_payload(
        content=document_content,
        available_document_categories=available_document_categories,
        owner_edrpou=owner_edrpou,
        extension=extension,
    )
    if not body:
        logger.info('Unable to extract text from document', extra={'document_id': document_id})
        return None

    used_model = DEFAULT_SUGGEST_MODEL_NAME
    time_start = time.time()
    try:
        result = await services.bedrock_client.invoke_model(
            body=ujson.dumps(body),
            modelId=MODEL_ID_BY_NAME[used_model],
        )
        request_duration = time.time() - time_start
        async with result['body'] as stream:
            resp = await stream.read()

        model_resp = _parse_model_response(resp, model_provider=ModelProvider.anthropic)
        raw_data = ujson.loads(model_resp.raw_data)

        raw_data['document_id'] = document_id or generate_uuid()
        raw_data['request_duration'] = request_duration
        raw_data['chunk_length'] = TEXT_CHUNK_LEN
        raw_data['tokens_used'] = model_resp.input_tokens + model_resp.output_tokens
        raw_data['model'] = used_model
    except Exception:
        logger.exception('Exception during Bedrock API calls', extra={'document_id': document_id})
        return None

    try:
        document_meta_suggest = DocumentMetaSuggest.model_validate(
            raw_data,
            context={'available_categories': {c.id for c in available_document_categories}},
        )
    except ValidationError:
        logger.warning(
            'Unexpected suggestion response from AI model', extra={'raw_data': str(raw_data)}
        )
        return None

    # Save results only for existing documents
    try:
        if document_id is not None:
            async with services.db.acquire() as conn:
                await insert_document_suggestion(conn, document_meta_suggest)
    except Exception:
        logger.exception('Exception during save document_meta analitics data')

    return document_meta_suggest


def _parse_model_response(
    resp: bytes,
    model_provider: ModelProvider,
) -> ModelResponse:
    resp_json = ujson.loads(resp.decode())
    if model_provider == ModelProvider.anthropic:
        usage = resp_json['usage']
        return ModelResponse(
            input_tokens=usage['input_tokens'],
            output_tokens=usage['output_tokens'],
            raw_data=resp_json['content'][0]['text'],
        )
    if model_provider == ModelProvider.meta:
        return ModelResponse(
            input_tokens=resp_json['prompt_token_count'],
            output_tokens=resp_json['generation_token_count'],
            raw_data=resp_json['generation'],
        )
    if model_provider == ModelProvider.amazon:
        usage = resp_json['usage']
        return ModelResponse(
            input_tokens=usage['inputTokens'],
            output_tokens=usage['outputTokens'],
            raw_data=resp_json['output']['message']['content'][0]['text'],
        )

    t.assert_never(model_provider)


async def _prepare_meta_suggest_bedrock_payload(
    *,
    content: bytes,
    owner_edrpou: str,
    available_document_categories: list[DocumentCategory],
    extension: str,
) -> DataDict | None:
    """
    Prepare a payload for a Bedrock API call for suggesting document content meta-information.
    """
    doc_txt = await _extract_text_from_document(
        document_content=content,
        extension=extension,
    )
    if not doc_txt:
        logger.info('Got empty text from document')
        return None

    category_map = {
        c.id: c.title
        for c in available_document_categories
        if c.id != DocumentCategoryFields.any.value
    }
    prompt = PROMPT_TEMPLATE_SUGGEST_META.format(
        doc_text=doc_txt,
        owner_edrpou=owner_edrpou,
        category_map=category_map,
    )
    return _prepare_body_for_provider(
        prompt=prompt,
        model_provider=ModelProvider.anthropic,
        temperature=0,
        max_output_tokens=300,
    )


def _prepare_body_for_provider(
    *,
    prompt: str,
    model_provider: ModelProvider,
    temperature: float | None = None,
    max_output_tokens: int | None = None,
) -> DataDict:
    """
    Prepares body for a specific model provider
    """

    # Max tokens in model response
    max_output_tokens = max_output_tokens or 2000

    # The amount of randomness injected into the response (from min=0 to max=1)
    temperature = temperature or 0.2

    if model_provider == ModelProvider.anthropic:
        return {
            'messages': [{'role': 'user', 'content': [{'type': 'text', 'text': prompt}]}],
            'anthropic_version': 'bedrock-2023-05-31',
            'max_tokens': max_output_tokens,
            'temperature': temperature,
            'stop_sequences': [],
        }
    if model_provider == ModelProvider.meta:
        return {
            'prompt': prompt,
            'max_gen_len': max_output_tokens,
            'temperature': temperature,
        }
    if model_provider == ModelProvider.amazon:
        return {
            'messages': [
                {
                    'role': 'user',
                    'content': [{'text': prompt}],
                }
            ],
            'inferenceConfig': {
                'max_new_tokens': max_output_tokens,
                'temperature': temperature,
            },
        }

    t.assert_never(model_provider)


async def _extract_text_from_document(
    *,
    document_content: bytes,
    extension: str,
    read_full_text: bool = False,
) -> str | None:
    try:
        text_extractor_func = partial(
            _extract_text_from_document_content_sync,
            content=document_content,
            extension=extension,
            read_full_text=read_full_text,
        )
        doc_txt = await run_sync_in_process_pool(text_extractor_func, timeout=3)
    except Exception:
        logger.exception('Failed to extract text from document', extra={'extension': extension})
        return None

    return doc_txt


def _extract_text_from_pdf_content(content: bytes, read_full_text: bool) -> str | None:
    """
    We assume that document requisites info placed only on first + last page

    WARNING! CPU bound, consider using in process_pool
    """
    try:
        doc_pdf = pymupdf.open(stream=content)

        if doc_pdf.page_count < 1:
            return None

        doc_text = ''

        if read_full_text:
            pages_iterator = doc_pdf
        else:
            pages_iterator = (
                doc_pdf
                if doc_pdf.page_count <= 4
                else [doc_pdf[0], doc_pdf[1], doc_pdf[-1], doc_pdf[-2]]
            )

        for page in pages_iterator:
            doc_text += page.get_text()

        return doc_text
    finally:
        if 'doc_pdf' in locals():
            doc_pdf.close()


def _extract_text_from_docx_content(content: bytes, read_full_text: bool) -> str:
    """
    Extract text from docx content
    """

    doc = Document(BytesIO(content))
    doc_text_buffer = StringIO()

    for item in doc.iter_inner_content():
        if isinstance(item, Paragraph):
            doc_text_buffer.write(item.text)
            doc_text_buffer.write(' ')

        elif isinstance(item, Table):
            for row in item.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        doc_text_buffer.write(paragraph.text)
                        doc_text_buffer.write(' ')

    return doc_text_buffer.getvalue()


def _extract_text_from_doc_content(content: bytes, read_full_text: bool) -> str:
    """
    Extract text from doc content.
    Using antiword. Because python-docx does not support
    doc format due to binary format.
    It is not a good solution, but it works. 🥲
    And seems lighter than using libreoffice.

    https://packages.debian.org/en/sid/antiword
    """

    with tempfile.NamedTemporaryFile(suffix='.doc') as temp_file:
        temp_file.write(content)
        return subprocess.run(
            ['antiword', temp_file.name],
            check=True,
            capture_output=True,
        ).stdout.decode()


EXTRACTORS = {
    '.pdf': _extract_text_from_pdf_content,
    '.docx': _extract_text_from_docx_content,
    '.doc': _extract_text_from_doc_content,
}


def _text_cleanup(text: str) -> str:
    # Remove extra new lines
    text = text.replace('\n', ' ')
    # Remove extra spaces before and after text
    text = text.strip()
    # remove | characters generated by antiword for tables
    text = text.replace('|', ' ')
    # Remove extra spaces
    text = ' '.join(text.split())
    return text


def _extract_text_from_document_content_sync(
    *,
    content: bytes,
    extension: str,
    read_full_text: bool = False,
) -> str | None:
    """
    Extract text from document content

    We read only chunk of data with length = TEXT_CHUNK_LEN
    from document start + from document end.
    It is initial assumptions, they may be corrected after testing.
    """
    if extension not in EXTRACTORS:
        logger.info('Unsupported document extension', extra={'extension': extension})
        return None

    extractor = EXTRACTORS[extension]
    raw = extractor(content=content, read_full_text=read_full_text)
    if not raw:
        return None

    doc_text = _text_cleanup(raw)

    # Do not process files with low text presence, it is possible for PDF with images
    if len(doc_text) < MIN_TEXT_LEN:
        return None

    if read_full_text:
        return doc_text

    if len(doc_text) > TEXT_CHUNK_LEN * 2:
        doc_text = ' '.join([doc_text[:TEXT_CHUNK_LEN], doc_text[-TEXT_CHUNK_LEN:]])

    return doc_text


def _calculate_total_price(
    input_tokens_price: float,
    output_tokens_price: float,
    input_tokens: int,
    output_tokens: int,
) -> float:
    input_price_decimal = Decimal(str(input_tokens_price))
    output_price_decimal = Decimal(str(output_tokens_price))

    total_price = (input_price_decimal * input_tokens / 1000) + (
        output_price_decimal * output_tokens / 1000
    )

    return float(total_price.quantize(Decimal('0.000000'), rounding=ROUND_HALF_UP))


def deep_update(mapping: DataDict, updating_mapping: DataDict) -> DataDict:
    for k, v in updating_mapping.items():
        if k == 'items' and isinstance(mapping.get(k), list) and isinstance(v, list):
            existing_items = {str(item['id']): item for item in mapping[k]}
            new_items_patch = []

            for item_patch in v:
                item_id = item_patch.get('id')
                if item_id and str(item_id) in existing_items:
                    deep_update(existing_items[str(item_id)], item_patch)
                else:
                    new_items_patch.append(item_patch)

            mapping[k] = list(existing_items.values()) + new_items_patch

        elif isinstance(mapping.get(k), dict) and isinstance(v, dict):
            mapping[k] = deep_update(mapping[k], v)
        else:
            mapping[k] = v
    return mapping


def is_fully_approved(data: t.Any) -> bool:
    if isinstance(data, dict):
        if data.get('is_deleted'):
            return True

        # FSValue check
        if 'value' in data:
            return data.get('is_approved') is True

        return all(is_fully_approved(v) for v in data.values())

    if isinstance(data, list):
        return all(is_fully_approved(v) for v in data)

    return True
