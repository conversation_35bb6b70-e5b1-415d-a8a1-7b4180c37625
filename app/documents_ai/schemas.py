import datetime
import logging
import typing as t
import uuid

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    ValidationError,
    ValidationInfo,
    WrapValidator,
    field_validator,
)

from app.documents_ai.enums import ModelName
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.lib import validators_pydantic as pv
from app.lib.database import DBRow
from app.lib.money import to_subunits_decimal_from_units, to_units_decimal_from_subunits
from app.lib.types import DataDict

logger = logging.getLogger(__name__)


def _invalid_to_none(v: t.Any, handler: t.Callable[[t.Any], t.Any]) -> t.Any:
    """
    Specific helper for DocumentMetaSuggest model.
    Helps to put None values for fields that don't pass validation by model"""
    try:
        return handler(v)
    except ValidationError as err:
        logger.info(
            'Unexpected field value from AI model response',
            extra={
                'error_message': str(err),
                'value': str(v),
            },
        )
        return None


class DocumentMetaSuggest(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    document_id: pv.UUID

    # data, suggested by model from bedrock
    category: int | None
    edrpou_recipient: t.Annotated[pv.EDRPOU | None, WrapValidator(_invalid_to_none)] = Field(
        None, alias='edrpou'
    )
    date_document: t.Annotated[datetime.date | None, WrapValidator(_invalid_to_none)] = Field(
        None, alias='date'
    )
    number: str | None = Field(None, max_length=512)
    amount: t.Annotated[pv.Price | None, WrapValidator(_invalid_to_none)]

    # time in seconds for invoking model from bedrock
    request_duration: float

    # length of text chunk extracted from begin/end of document
    chunk_length: int

    # number of tokens used during request a model from bedrock
    # it is used for calculating cost of service
    tokens_used: int

    # model name, which used for generating AI suggestions
    model: ModelName

    @field_validator('category', mode='after')
    @classmethod
    def validate_category_exists(cls, v: int | None, info: ValidationInfo) -> int | None:
        if isinstance(info.context, dict):
            available_categories: set[int] = info.context.get('available_categories', set())
            if v is not None and v not in available_categories:
                return None
        return v

    def to_db(self) -> DataDict:
        data = self.model_dump(mode='json')
        amount = data['amount']
        data['amount'] = to_subunits_decimal_from_units(amount) if amount else None
        data['model'] = self.model
        return data

    @classmethod
    def from_db(cls, row: DBRow) -> t.Self:
        result = cls.model_validate(row, from_attributes=True)
        if amount := result.amount:
            result.amount = to_units_decimal_from_subunits(amount)
        return result


class DocumentMetaSuggestResponse(BaseModel):
    title: str | None

    # data, suggested by model from bedrock
    category: int | None
    edrpou_recipient: pv.EDRPOU | None = Field(..., serialization_alias='companyEdrpou')
    date_document: datetime.date | None = Field(..., serialization_alias='date')
    number: str | None = Field(..., max_length=512)
    amount: pv.Price | None

    # recipient additional data
    recipient_name: str | None = Field(..., serialization_alias='companyName')
    recipient_email: str | None = Field(..., serialization_alias='companyEmail')

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', by_alias=True)

    @classmethod
    def empty(cls) -> t.Self:
        return cls(
            title=None,
            category=None,
            edrpou_recipient=None,
            date_document=None,
            number=None,
            amount=None,
            recipient_name=None,
            recipient_email=None,
        )


class DocumentSummaryResponse(BaseModel):
    model: str
    summary: str
    duration_seconds: float
    input_tokens: int
    output_tokens: int
    total_price_usd: float

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', by_alias=True)


class ModelResponse(BaseModel):
    input_tokens: int
    output_tokens: int
    raw_data: t.Any


class ExtractionRequest(BaseModel):
    document_ids: list[pv.UUID]


class GetStatusResponseItem(BaseModel):
    document_id: pv.UUID
    status: SDAStatus


class StartExtractionResponse(BaseModel):
    data: list[GetStatusResponseItem]


class GetStatusResponse(BaseModel):
    data: list[GetStatusResponseItem]


class FSBox(BaseModel):
    bbox: list[int] | None = None  # [x_min, y_min, x_max, y_max]
    page: int | None = None


class FSValue(BaseModel):
    """
    Field-Specific Value model for structured data extraction.

    Represents a single extracted field with its value, location information,
    and approval/deletion status for human validation workflow.

    Attributes:
        value: The extracted value (text or numeric)
        bboxes: List of bounding boxes indicating where this value was found in the document
        is_approved: Whether this value has been approved by a human reviewer
        is_deleted: Whether this value has been marked as deleted/invalid
                   Note: Deleted values are considered approved in the validation workflow
    """

    value: str | float | None = Field(None, description='Extracted field value')
    bboxes: list[FSBox] = Field(
        default_factory=list, description='Bounding boxes showing field location in document'
    )
    is_approved: bool | None = Field(False, description='Human approval status')
    is_deleted: bool | None = Field(
        False, description='Deletion status - deleted values are considered approved'
    )


class BankInfo(BaseModel):
    mfo: FSValue | None = None
    account: FSValue | None = None
    bank_name: FSValue | None = None
    iban: FSValue | None = None
    swift_code: FSValue | None = None
    edrpou: FSValue | None = None
    ipn: FSValue | None = None


class ContactInfo(BaseModel):
    email: FSValue | None = None
    phone: FSValue | None = None


class Person(BaseModel):
    position: FSValue | None = None
    full_name: FSValue | None = None


class Party(BaseModel):
    company_name: FSValue | None = None
    edrpou: FSValue | None = None
    address: FSValue | None = None
    bank_info: BankInfo | None = None
    contact_info: ContactInfo | None = None
    responsible_person: Person | None = None


class Item(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: FSValue | None = None
    code: FSValue | None = None
    units: FSValue | None = None
    quantity: FSValue | None = None
    price: FSValue | None = None
    total_price_without_vat: FSValue | None = None
    total_price_with_vat: FSValue | None = None
    deadline: FSValue | None = None
    is_deleted: bool | None = None


class TotalPrice(BaseModel):
    total_price_without_vat: FSValue | None = None
    total_price_with_vat: FSValue | None = None
    overall_deadline: FSValue | None = None


class AdditionalData(BaseModel):
    purpose: FSValue | None = None
    contract_number: FSValue | None = None
    contract_date: FSValue | None = None
    payment_purpose: FSValue | None = None
    delivery_terms: FSValue | None = None


class Details(BaseModel):
    number: FSValue | None = None
    date_created: FSValue | None = None
    reason: FSValue | None = None


class PartiesInformation(BaseModel):
    customer: Party | None = None
    performer: Party | None = None


class ExtractedData(BaseModel):
    details: Details | None = None
    parties_information: PartiesInformation | None = None
    items: list[Item] | None = None
    total_price: TotalPrice | None = None
    additional_data: AdditionalData | None = None


class MetaInfo(BaseModel):
    llm_name: ModelName
    request_duration: float
    input_tokens: int
    output_tokens: int

    def to_db(self) -> DataDict:
        return self.model_dump(exclude_unset=True)


class StructuredDataModel(BaseModel):
    """
    Main model for structured document data extraction.

    This model represents the complete structured data extracted from a document,
    including all fields, approval status, and metadata. It supports optimistic
    locking through versioning to prevent concurrent modification conflicts.

    Attributes:
        extracted_data: The main structured data content
        meta: Metadata about the extraction process (LLM used, tokens, etc.)
        version: Version number for optimistic locking (incremented on each update)

    Usage:
        # Create new structured data
        data = StructuredDataModel(extracted_data=extracted_data)

        # Update with optimistic locking
        updated = data.increment_version()

        # Save to S3 (compact format)
        s3_data = data.to_dict()

        # Return to API (full format)
        api_data = data.to_api()
    """

    model_config = ConfigDict(extra='forbid')

    extracted_data: ExtractedData = Field(description='Main structured data content')
    meta: MetaInfo | None = Field(None, description='Extraction process metadata')
    version: int = Field(default=1, description='Version for optimistic locking')

    def to_api(self) -> DataDict:
        """Convert to API response format with all fields"""
        return self.model_dump(mode='json')

    def to_dict(self) -> DataDict:
        """
        Convert to compact format for S3 storage.

        Excludes metadata and unset/default/None values to minimize storage size.
        """
        return self.model_dump(
            mode='json',
            exclude_unset=True,
            exclude_defaults=True,
            exclude_none=True,
            exclude={'meta'},
        )

    def increment_version(self) -> 'StructuredDataModel':
        """
        Create a new instance with incremented version for optimistic locking.

        Returns:
            New StructuredDataModel instance with version + 1
        """
        return self.model_copy(update={'version': self.version + 1})


# Patch Models for Type-Safe Updates
class FSValuePatch(BaseModel):
    """
    Patch model for FSValue updates.

    Used to partially update FSValue fields. Only non-None fields will be applied
    to the target FSValue during merge operations.

    Example:
        # Approve a field value
        patch = FSValuePatch(is_approved=True)

        # Delete a field value
        patch = FSValuePatch(is_deleted=True)

        # Update value and approve
        patch = FSValuePatch(value="new value", is_approved=True)
    """

    value: str | float | None = Field(None, description='New field value')
    bboxes: list[FSBox] | None = Field(None, description='New bounding boxes')
    is_approved: bool | None = Field(None, description='New approval status')
    is_deleted: bool | None = Field(None, description='New deletion status')


class BankInfoPatch(BaseModel):
    """Patch model for BankInfo updates"""

    mfo: FSValuePatch | None = None
    account: FSValuePatch | None = None
    bank_name: FSValuePatch | None = None
    iban: FSValuePatch | None = None
    swift_code: FSValuePatch | None = None
    edrpou: FSValuePatch | None = None
    ipn: FSValuePatch | None = None


class ContactInfoPatch(BaseModel):
    """Patch model for ContactInfo updates"""

    email: FSValuePatch | None = None
    phone: FSValuePatch | None = None


class PersonPatch(BaseModel):
    """Patch model for Person updates"""

    position: FSValuePatch | None = None
    full_name: FSValuePatch | None = None


class PartyPatch(BaseModel):
    """Patch model for Party updates"""

    company_name: FSValuePatch | None = None
    edrpou: FSValuePatch | None = None
    address: FSValuePatch | None = None
    bank_info: BankInfoPatch | None = None
    contact_info: ContactInfoPatch | None = None
    responsible_person: PersonPatch | None = None


class ItemPatch(BaseModel):
    """Patch model for Item updates"""

    id: str  # Required for identifying which item to update
    name: FSValuePatch | None = None
    code: FSValuePatch | None = None
    units: FSValuePatch | None = None
    quantity: FSValuePatch | None = None
    price: FSValuePatch | None = None
    total_price_without_vat: FSValuePatch | None = None
    total_price_with_vat: FSValuePatch | None = None
    deadline: FSValuePatch | None = None
    is_deleted: bool | None = None


class TotalPricePatch(BaseModel):
    """Patch model for TotalPrice updates"""

    total_price_without_vat: FSValuePatch | None = None
    total_price_with_vat: FSValuePatch | None = None
    overall_deadline: FSValuePatch | None = None


class AdditionalDataPatch(BaseModel):
    """Patch model for AdditionalData updates"""

    purpose: FSValuePatch | None = None
    contract_number: FSValuePatch | None = None
    contract_date: FSValuePatch | None = None
    payment_purpose: FSValuePatch | None = None
    delivery_terms: FSValuePatch | None = None


class DetailsPatch(BaseModel):
    """Patch model for Details updates"""

    number: FSValuePatch | None = None
    date_created: FSValuePatch | None = None
    reason: FSValuePatch | None = None


class PartiesInformationPatch(BaseModel):
    """Patch model for PartiesInformation updates"""

    customer: PartyPatch | None = None
    performer: PartyPatch | None = None


class ExtractedDataPatch(BaseModel):
    """Patch model for ExtractedData updates"""

    details: DetailsPatch | None = None
    parties_information: PartiesInformationPatch | None = None
    items: list[ItemPatch] | None = None
    total_price: TotalPricePatch | None = None
    additional_data: AdditionalDataPatch | None = None


class StructuredDataPatch(BaseModel):
    """
    Main patch model for structured data updates.

    This model represents a partial update to structured data. It supports
    optimistic locking through version checking and ensures type safety
    throughout the update process.

    Attributes:
        extracted_data: Partial updates to the main structured data
        version: Expected current version for optimistic locking

    Usage:
        # Create a patch to approve a field
        patch = StructuredDataPatch(
            extracted_data=ExtractedDataPatch(
                details=DetailsPatch(
                    number=FSValuePatch(is_approved=True)
                )
            ),
            version=1  # Expected current version
        )

        # Apply patch with merge_structured_data()
        updated = merge_structured_data(original, patch)
    """

    model_config = ConfigDict(extra='forbid')

    extracted_data: ExtractedDataPatch | None = Field(
        None, description='Partial updates to structured data content'
    )
    version: int | None = Field(None, description='Expected version for optimistic locking')
