import datetime
import logging
import typing as t
import uuid

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    ValidationError,
    ValidationInfo,
    WrapValidator,
    field_validator,
)

from app.documents_ai.enums import ModelName
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.lib import validators_pydantic as pv
from app.lib.database import DBRow
from app.lib.money import to_subunits_decimal_from_units, to_units_decimal_from_subunits
from app.lib.types import DataDict

logger = logging.getLogger(__name__)


def _invalid_to_none(v: t.Any, handler: t.Callable[[t.Any], t.Any]) -> t.Any:
    """
    Specific helper for DocumentMetaSuggest model.
    Helps to put None values for fields that don't pass validation by model"""
    try:
        return handler(v)
    except ValidationError as err:
        logger.info(
            'Unexpected field value from AI model response',
            extra={
                'error_message': str(err),
                'value': str(v),
            },
        )
        return None


class DocumentMetaSuggest(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    document_id: pv.UUID

    # data, suggested by model from bedrock
    category: int | None
    edrpou_recipient: t.Annotated[pv.EDRPOU | None, WrapValidator(_invalid_to_none)] = Field(
        None, alias='edrpou'
    )
    date_document: t.Annotated[datetime.date | None, WrapValidator(_invalid_to_none)] = Field(
        None, alias='date'
    )
    number: str | None = Field(None, max_length=512)
    amount: t.Annotated[pv.Price | None, WrapValidator(_invalid_to_none)]

    # time in seconds for invoking model from bedrock
    request_duration: float

    # length of text chunk extracted from begin/end of document
    chunk_length: int

    # number of tokens used during request a model from bedrock
    # it is used for calculating cost of service
    tokens_used: int

    # model name, which used for generating AI suggestions
    model: ModelName

    @field_validator('category', mode='after')
    @classmethod
    def validate_category_exists(cls, v: int | None, info: ValidationInfo) -> int | None:
        if isinstance(info.context, dict):
            available_categories: set[int] = info.context.get('available_categories', set())
            if v is not None and v not in available_categories:
                return None
        return v

    def to_db(self) -> DataDict:
        data = self.model_dump(mode='json')
        amount = data['amount']
        data['amount'] = to_subunits_decimal_from_units(amount) if amount else None
        data['model'] = self.model
        return data

    @classmethod
    def from_db(cls, row: DBRow) -> t.Self:
        result = cls.model_validate(row, from_attributes=True)
        if amount := result.amount:
            result.amount = to_units_decimal_from_subunits(amount)
        return result


class DocumentMetaSuggestResponse(BaseModel):
    title: str | None

    # data, suggested by model from bedrock
    category: int | None
    edrpou_recipient: pv.EDRPOU | None = Field(..., serialization_alias='companyEdrpou')
    date_document: datetime.date | None = Field(..., serialization_alias='date')
    number: str | None = Field(..., max_length=512)
    amount: pv.Price | None

    # recipient additional data
    recipient_name: str | None = Field(..., serialization_alias='companyName')
    recipient_email: str | None = Field(..., serialization_alias='companyEmail')

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', by_alias=True)

    @classmethod
    def empty(cls) -> t.Self:
        return cls(
            title=None,
            category=None,
            edrpou_recipient=None,
            date_document=None,
            number=None,
            amount=None,
            recipient_name=None,
            recipient_email=None,
        )


class DocumentSummaryResponse(BaseModel):
    model: str
    summary: str
    duration_seconds: float
    input_tokens: int
    output_tokens: int
    total_price_usd: float

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', by_alias=True)


class ModelResponse(BaseModel):
    input_tokens: int
    output_tokens: int
    raw_data: t.Any


class ExtractionRequest(BaseModel):
    document_ids: list[pv.UUID]


class GetStatusResponseItem(BaseModel):
    document_id: pv.UUID
    status: SDAStatus


class StartExtractionResponse(BaseModel):
    data: list[GetStatusResponseItem]


class GetStatusResponse(BaseModel):
    data: list[GetStatusResponseItem]


class FSBox(BaseModel):
    bbox: list[int] | None = None  # [x_min, y_min, x_max, y_max]
    page: int | None = None


class FSValue(BaseModel):
    value: str | float | None = None
    bboxes: list[FSBox] = Field(default_factory=list)
    is_approved: bool | None = False
    is_deleted: bool | None = False


class BankInfo(BaseModel):
    mfo: FSValue | None = None
    account: FSValue | None = None
    bank_name: FSValue | None = None
    iban: FSValue | None = None
    swift_code: FSValue | None = None
    edrpou: FSValue | None = None
    ipn: FSValue | None = None


class ContactInfo(BaseModel):
    email: FSValue | None = None
    phone: FSValue | None = None


class Person(BaseModel):
    position: FSValue | None = None
    full_name: FSValue | None = None


class Party(BaseModel):
    company_name: FSValue | None = None
    edrpou: FSValue | None = None
    address: FSValue | None = None
    bank_info: BankInfo | None = None
    contact_info: ContactInfo | None = None
    responsible_person: Person | None = None


class Item(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: FSValue | None = None
    code: FSValue | None = None
    units: FSValue | None = None
    quantity: FSValue | None = None
    price: FSValue | None = None
    total_price_without_vat: FSValue | None = None
    total_price_with_vat: FSValue | None = None
    deadline: FSValue | None = None
    is_deleted: bool | None = None


class TotalPrice(BaseModel):
    total_price_without_vat: FSValue | None = None
    total_price_with_vat: FSValue | None = None
    overall_deadline: FSValue | None = None


class AdditionalData(BaseModel):
    purpose: FSValue | None = None
    contract_number: FSValue | None = None
    contract_date: FSValue | None = None
    payment_purpose: FSValue | None = None
    delivery_terms: FSValue | None = None


class Details(BaseModel):
    number: FSValue | None = None
    date_created: FSValue | None = None
    reason: FSValue | None = None


class PartiesInformation(BaseModel):
    customer: Party | None = None
    performer: Party | None = None


class ExtractedData(BaseModel):
    details: Details | None = None
    parties_information: PartiesInformation | None = None
    items: list[Item] | None = None
    total_price: TotalPrice | None = None
    additional_data: AdditionalData | None = None


class MetaInfo(BaseModel):
    llm_name: ModelName
    request_duration: float
    input_tokens: int
    output_tokens: int

    def to_db(self) -> DataDict:
        return self.model_dump(exclude_unset=True)


class StructuredDataModel(BaseModel):
    model_config = ConfigDict(extra='forbid')

    extracted_data: ExtractedData
    meta: MetaInfo | None = None

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json')

    def to_dict(self) -> DataDict:
        """Used for saving compact data to S3"""
        return self.model_dump(
            mode='json',
            exclude_unset=True,
            exclude_defaults=True,
            exclude_none=True,
            exclude={'meta'},
        )
