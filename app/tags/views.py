import logging
from http import HTT<PERSON>tatus

from aiohttp import web

from api.public.decorators import api_handler
from api.utils import api_response
from app.actions.utils import get_source
from app.auth.db import select_roles_by_ids
from app.auth.types import User
from app.es.utils import send_to_indexator
from app.events.user_actions import types
from app.events.user_actions.utils import add_user_actions, get_event_source
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.openapi.decorators import openapi_docs
from app.openapi.types import OpenApiParam
from app.services import services
from app.tags import utils
from app.tags.db import (
    insert_tags_for_roles,
    safe_delete_role_tags,
    select_tag_roles_for_api,
    select_tags_by_ids,
    select_tags_for_api,
)
from app.tags.schemas import GetTagRolesResponse, GetTagsParams, GetTagsResponse
from app.tags.utils import (
    create_contact_tag,
    create_document_tags,
    create_new_tags_for_contacts,
    create_new_tags_for_documents,
    delete_contact_tags,
    get_tag_access_for_delete,
    insert_role_tags_batch,
    prepare_role_tag_data,
    prepare_tags_response,
    schedule_create_document_access_on_document_tag_job,
    schedule_create_document_access_on_role_tag_job,
    schedule_delete_tag_access_job,
)
from app.tags.validators import (
    validate_contacts_tags,
    validate_create_tags_for_contacts,
    validate_create_tags_for_documents,
    validate_create_tags_for_role,
    validate_document_tags,
    validate_get_tag_roles,
    validate_roles_tags,
)

logger = logging.getLogger(__name__)


async def create_tags_for_documents(
    request: web.Request,
    user: User,
) -> web.Response:
    """Creating and adding tags for document"""

    async with services.db.acquire() as conn:
        # Validate json, tag data, permissions and existing of documents
        options = await validate_create_tags_for_documents(conn, request, user)

        tags = await create_new_tags_for_documents(
            conn=conn,
            tags_names=options.tags_names,
            documents_ids=options.document_ids,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            assigner_role_id=user.role_id,
        )

    await send_to_indexator(services.redis, options.document_ids, to_slow_queue=False)

    return web.json_response(
        data=prepare_tags_response(tags),
        status=HTTPStatus.CREATED,
    )


async def connect_tags_and_documents(
    request: web.Request,
    user: User,
) -> web.Response:
    """Connect existing tag and existing documents"""

    async with services.db.acquire() as conn:
        options = await validate_document_tags(conn, request, user)

        # just connect existing document and tag
        await create_document_tags(
            conn=conn,
            documents_ids=options.documents_ids,
            tags_ids=options.tags_ids,
            company_id=user.company_id,
            assigner_role_id=user.role_id,
        )

    await schedule_create_document_access_on_document_tag_job(
        tags_ids=options.tags_ids,
        documents_ids=options.documents_ids,
        company_edrpou=user.company_edrpou,
        assigner_role_id=user.role_id,
        source=get_source(request),
    )

    await send_to_indexator(services.redis, options.documents_ids, to_slow_queue=False)

    return web.json_response(status=HTTPStatus.OK)


async def disconnect_documents_and_tags(
    request: web.Request,
    user: User,
) -> web.Response:
    """Mass disconnect tags and documents"""
    async with services.db.acquire() as conn:
        options = await validate_document_tags(conn, request, user)

        listings_ids = await utils.disconnect_documents_and_tags(
            conn=conn,
            documents_ids=options.documents_ids,
            tags_ids=options.tags_ids,
            user=user,
            request_source=get_source(request),
        )

    # send job that removes tag access from given listings
    await schedule_delete_tag_access_job(listings_ids=listings_ids)
    await send_to_indexator(document_ids=options.documents_ids, to_slow_queue=False)

    return web.json_response(status=HTTPStatus.OK)


async def create_tags_for_roles(
    request: web.Request,
    user: User,
) -> web.Response:
    """Create new tags and connect with existing roles"""
    async with services.db.acquire() as conn:
        options = await validate_create_tags_for_role(conn, request, user)

        tags = await insert_tags_for_roles(
            conn,
            names=options.tags_names,
            roles_ids=options.roles_ids,
            company_id=user.company_id,
            assigner_role_id=user.role_id,
        )

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            roles = await select_roles_by_ids(conn=conn, roles_ids=options.roles_ids)
            events = []

            for role in roles:
                for tag in tags:
                    if role and tag:
                        events.append(
                            types.UserAction(
                                action=types.Action.role_tag_create,
                                source=get_event_source(request),
                                email=user.email,
                                user_id=user.id,
                                phone=user.auth_phone,
                                company_id=user.company_id,
                                extra={
                                    'affected_user_email': role.user_email,
                                    'tag_name': tag.name,
                                },
                            )
                        )
            await add_user_actions(events)

    return web.json_response(
        data=prepare_tags_response(tags),
        status=HTTPStatus.CREATED,
    )


async def connect_tags_and_roles(
    request: web.Request,
    user: User,
) -> web.Response:
    """Connect existing tag and existing roles"""

    async with services.db.acquire() as conn:
        options = await validate_roles_tags(conn, request, user)

        await insert_role_tags_batch(
            conn=conn,
            tags_ids=options.tags_ids,
            roles_ids=options.roles_ids,
            assigner_role_id=user.role_id,
        )

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            roles = await select_roles_by_ids(conn=conn, roles_ids=options.roles_ids)
            tags = await select_tags_by_ids(conn, tags_ids=options.tags_ids)
            events = []

            for role in roles:
                for tag in tags:
                    if role and tag:
                        events.append(
                            types.UserAction(
                                action=types.Action.role_tag_create,
                                source=get_event_source(request),
                                email=user.email,
                                user_id=user.id,
                                phone=user.auth_phone,
                                company_id=user.company_id,
                                extra={
                                    'affected_user_email': role.user_email,
                                    'tag_name': tag.name,
                                },
                            )
                        )
            await add_user_actions(events)

    await schedule_create_document_access_on_role_tag_job(
        tags_ids=options.tags_ids,
        roles_ids=options.roles_ids,
        company_edrpou=user.company_edrpou,
    )
    return web.json_response(data={'status': 'processing'}, status=HTTPStatus.CREATED)


async def disconnect_tags_and_roles(
    request: web.Request,
    user: User,
) -> web.Response:
    """
    Mass disconnect tags and documents.
    Main functionality of this function are:
    * remove access to documents for roles that don't have any other access
      sources for given documents or remove access source in other cases.
    * disconnect tags and roles
    * remove tags that don't have any documents or roles connected with
      those tags
    """
    async with services.db.acquire() as conn:
        options = await validate_roles_tags(conn, request, user)

        logger.info(
            msg='Disconnect tags and roles',
            extra={
                'tags_ids': options.tags_ids,
                'roles_ids': options.roles_ids,
                'user_email': user.email,
                'tags': options.tags_dicts,
            },
        )

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            roles = await select_roles_by_ids(conn=conn, roles_ids=options.roles_ids)
            tags = await select_tags_by_ids(conn, tags_ids=options.tags_ids)
        else:
            roles = []
            tags = []

        # get listings ids that must lose tag access
        listings_ids = await get_tag_access_for_delete(
            conn=conn,
            tags_ids=options.tags_ids,
            access_edrpou=user.company_edrpou,
            roles_ids=options.roles_ids,
        )

        delete_data = prepare_role_tag_data(
            roles_ids=options.roles_ids,
            tags_ids=options.tags_ids,
            assigner_role_id=user.role_id,
        )
        await safe_delete_role_tags(conn, delete_data)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        events = []
        for role in roles:
            for tag in tags:
                if role and tag:
                    events.append(
                        types.UserAction(
                            action=types.Action.role_tag_delete,
                            source=get_event_source(request),
                            email=user.email,
                            user_id=user.id,
                            phone=user.auth_phone,
                            company_id=user.company_id,
                            extra={
                                'affected_user_email': role.user_email,
                                'tag_name': tag.name,
                            },
                        )
                    )
        if events:
            await add_user_actions(events)

    # send job that removes tag access from given listings
    await schedule_delete_tag_access_job(listings_ids=listings_ids)

    return web.json_response(status=HTTPStatus.OK)


async def create_tags_for_contacts(
    request: web.Request,
    user: User,
) -> web.Response:
    """Create new tags and connect with contacts"""
    async with services.db.acquire() as conn:
        options = await validate_create_tags_for_contacts(conn, request, user)

        # Create new tags and connect them with contacts and add those
        # tags to company role profile
        tags = await create_new_tags_for_contacts(
            conn=conn,
            tags_names=options.tags_names,
            contacts_ids=options.contacts_ids,
            company_id=user.company_id,
            assigner_role_id=user.role_id,
        )

    return web.json_response(
        data=prepare_tags_response(tags),
        status=HTTPStatus.CREATED,
    )


async def connect_tags_and_contacts(
    request: web.Request,
    user: User,
) -> web.Response:
    """Mass connect tags and contacts"""
    async with services.db.acquire() as conn:
        options = await validate_contacts_tags(conn, request, user)
        await create_contact_tag(
            conn=conn,
            tags_ids=options.tags_ids,
            contacts_ids=options.contacts_ids,
            assigner_role_id=user.role_id,
        )

    return web.json_response(status=HTTPStatus.CREATED)


async def disconnect_tags_and_contacts(
    request: web.Request,
    user: User,
) -> web.Response:
    """Mass disconnect tags and documents"""

    async with services.db.acquire() as conn:
        options = await validate_contacts_tags(conn, request, user)
        logger.info(
            msg='Disconnect tags and contacts',
            extra={
                'tags_ids': options.tags_ids,
                'contacts_ids': options.contacts_ids,
                'user_email': user.email,
                'tags': options.tags_dicts,
            },
        )

        await delete_contact_tags(
            conn=conn,
            contacts_ids=options.contacts_ids,
            tags_ids=options.tags_ids,
            assigner_id=user.role_id,
        )

    return web.json_response(status=HTTPStatus.OK)


@openapi_docs(
    summary=_('Отримати список тегів'),
    params_query=GetTagsParams,
    response=GetTagsResponse,
    tags=['tags'],
)
@api_handler
async def get_tags(request: web.Request, user: User) -> web.Response:
    """
    Get a list of all company tags that user has access to
    """

    params = validators.validate_pydantic(GetTagsParams, request.query)

    async with services.db_readonly.acquire() as conn:
        tags = await select_tags_for_api(
            conn=conn,
            user=user,
            limit=params.limit,
            offset=params.offset,
        )

    response = GetTagsResponse(tags=tags)
    return api_response(request, response.to_api())


@openapi_docs(
    summary=_("Отримати список ролей прив'язаних до тегу"),
    params_path={'tag_id': OpenApiParam(required=True, schema=pv.UUID)},
    response=GetTagRolesResponse,
    tags=['tags'],
)
@api_handler
async def get_tag_roles(request: web.Request, user: User) -> web.Response:
    """
    Get a list of roles that assigned to that tag
    """

    async with services.db_readonly.acquire() as conn:
        tag_id = await validate_get_tag_roles(
            conn=conn,
            user=user,
            request=request,
        )
        roles_tags = await select_tag_roles_for_api(
            conn=conn,
            tag_id=tag_id,
        )

    response = GetTagRolesResponse(roles=roles_tags)
    return api_response(request, response.to_api())
