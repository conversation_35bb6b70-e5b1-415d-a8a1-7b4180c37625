import logging
from http import HTTPStatus
from io import BytesIO
from urllib.parse import unquote

from aiohttp import web
from aiohttp.web_request import FileField
from botocore.exceptions import ClientError
from multidict import CIMultiDict, CIMultiDictProxy, MultiDict

from api.errors import DoesNotExist, InvalidRequest, Object
from app.actions.utils import get_source
from app.auth.types import User
from app.document_categories.types import PublicDocumentCategory
from app.documents.enums import DocumentSource
from app.es.utils import send_to_indexator
from app.events.user_actions import Action, Source, types
from app.events.user_actions.utils import add_user_action
from app.i18n import _
from app.lib import s3_utils
from app.lib.helpers import get_file_extension, get_filename_base, json_response
from app.lib.s3_utils import CopyFile, DownloadFile
from app.lib.types import DataDict, MultipartFormData
from app.lib.validators import validate_json_request, validate_post_request
from app.services import services
from app.templates.constants import (
    PREVIEW_FORMAT,
    PREVIEW_MAX_SIZE,
    TEMPLATE_PREVIEW_PREFIX,
)
from app.templates.db import (
    delete_favorites_template,
    insert_favorites_template,
    insert_template,
    update_template,
)
from app.templates.replace_text import replace_text_in_docx
from app.templates.utils import (
    add_template,
    copy_document_as_template,
    get_or_generate_template_preview,
    get_template_preview_s3_key,
    get_templates_s3_key,
)
from app.templates.validators import (
    validate_copy_document_as_template,
    validate_document_upload_from_template,
    validate_preview_content,
    validate_template_add,
    validate_template_delete,
    validate_template_duplicate,
    validate_template_exists,
    validate_template_update,
)
from app.uploads.utils import process_upload

logger = logging.getLogger(__name__)


async def create_template(request: web.Request, user: User) -> web.Response:
    """
    Create a new template
    """

    raw_data = await validate_json_request(request)

    async with services.db.acquire() as conn:
        async with conn.begin():
            validated_data = await validate_template_add(
                conn=conn,
                user=user,
                data_raw=raw_data,
            )
            template = await add_template(
                conn=conn,
                user=user,
                validated_data=validated_data,
            )

    await add_user_action(
        user_action=types.UserAction(
            action=Action.template_create,
            source=Source.internal,
            user_id=user.id,
            phone=user.auth_phone,
            email=user.email,
            company_id=user.company_id,
            extra={'template_id': template.id, 'title': template.title},
        )
    )

    return json_response(template.to_dict(), status=HTTPStatus.CREATED)


async def copy_document_as_template_view(request: web.Request, user: User) -> web.Response:
    """
    Create a new template from vhcanso's document
    """

    raw_data = await validate_json_request(request)

    async with services.db.acquire() as conn:
        async with conn.begin():
            ctx = await validate_copy_document_as_template(
                conn=conn,
                user=user,
                data_raw=raw_data,
            )
            template = await copy_document_as_template(
                conn=conn,
                user=user,
                ctx=ctx,
            )

    await add_user_action(
        user_action=types.UserAction(
            action=Action.template_create,
            source=Source.internal,
            user_id=user.id,
            phone=user.auth_phone,
            email=user.email,
            company_id=user.company_id,
            extra={
                'template_id': template.id,
                'title': template.title,
                'document_id': ctx.document.id,
            },
        )
    )

    return json_response(template.to_dict(), status=HTTPStatus.CREATED)


async def upload_template(request: web.Request, user: User) -> web.Response:
    """
    Create a new template from docx|xlsx file
    """

    data = dict(await validate_post_request(request))
    file = data.get('file')
    if not isinstance(file, FileField):
        raise InvalidRequest(reason=_('Use multipart/form-data content-type'))
    content = file.file.read()

    async with services.db.acquire() as conn:
        async with conn.begin():
            validated_data = await validate_template_add(
                conn=conn,
                user=user,
                data_raw={
                    'title': data.get('title') or get_filename_base(unquote(file.filename)),
                    'category_id': data.get('category_id'),
                    'extension': get_file_extension(file.filename),
                    'content': content,
                },
            )
            template = await add_template(
                conn=conn,
                user=user,
                validated_data=validated_data,
            )

    await add_user_action(
        user_action=types.UserAction(
            action=Action.template_create,
            source=Source.internal,
            user_id=user.id,
            phone=user.auth_phone,
            email=user.email,
            company_id=user.company_id,
            extra={'template_id': template.id, 'title': template.title},
        )
    )

    return json_response(template.to_dict(), status=HTTPStatus.CREATED)


async def template_delete(request: web.Request, user: User) -> web.Response:
    """
    Delete template from user's company
    """

    async with services.db.acquire() as conn:
        template = await validate_template_delete(
            conn=conn,
            user=user,
            template_id=request.match_info['template_id'],
        )
        await update_template(
            conn=conn,
            filter_company_id=user.company_id,
            filter_template_id=template.id,
            is_deleted=True,
        )

    # remove all related data
    template_preview_keys = await s3_utils.get_keys_with_prefix(
        f'{TEMPLATE_PREVIEW_PREFIX}/{template.id}/'
    )

    # remove all related data
    await s3_utils.delete_batch(
        [
            get_templates_s3_key(template.id),
            *template_preview_keys,
        ]
    )

    await add_user_action(
        user_action=types.UserAction(
            action=Action.template_delete,
            source=Source.internal,
            user_id=user.id,
            phone=user.auth_phone,
            email=user.email,
            company_id=user.company_id,
            extra={'template_id': template.id, 'title': template.title},
        )
    )

    return web.json_response(status=HTTPStatus.NO_CONTENT)


async def template_update(request: web.Request, user: User) -> web.Response:
    """
    Update template from user's company
    """

    raw_data = await validate_json_request(request)

    async with services.db.acquire() as conn:
        validated_data = await validate_template_update(
            template_id=request.match_info['template_id'],
            conn=conn,
            user=user,
            data_raw=raw_data,
        )
        template = await update_template(
            conn=conn,
            filter_template_id=validated_data.template_id,
            title=validated_data.title,
            extension=validated_data.extension,
            category_id=validated_data.category_id,
            filter_company_id=user.company_id,
        )

    # may be none if template was deleted between validation and update
    if not template:
        raise DoesNotExist(obj=Object.document_template)

    await add_user_action(
        user_action=types.UserAction(
            action=Action.template_update,
            source=Source.internal,
            user_id=user.id,
            phone=user.auth_phone,
            email=user.email,
            company_id=user.company_id,
            extra={'template_id': template.id, 'title': template.title},
        )
    )

    return json_response(template.to_dict())


async def template_duplicate(request: web.Request, user: User) -> web.Response:
    """
    It's like add but create from existing
    """

    async with services.db.acquire() as conn:
        async with conn.begin():
            template = await validate_template_duplicate(
                conn=conn,
                user=user,
                template_id=request.match_info['template_id'],
            )
            new_template = await insert_template(
                conn=conn,
                title=template.title,
                extension=template.extension,
                category_id=template.category,
                company_id=user.company_id,
                created_by_role_id=user.role_id,
            )
            await s3_utils.copy(
                CopyFile(
                    source_key=get_templates_s3_key(template.id),
                    destination_key=get_templates_s3_key(new_template.id),
                )
            )
            try:
                await s3_utils.copy(
                    CopyFile(
                        source_key=get_template_preview_s3_key(template.id),
                        destination_key=get_template_preview_s3_key(new_template.id),
                    )
                )
            except ClientError as e:
                logger.exception('Failed to copy template preview', exc_info=e)

    await add_user_action(
        user_action=types.UserAction(
            action=Action.template_create,
            source=Source.internal,
            user_id=user.id,
            phone=user.auth_phone,
            email=user.email,
            company_id=user.company_id,
            extra={
                'template_id': new_template.id,
                'title': template.title,
                'source_template_id': template.id,
            },
        )
    )

    return json_response(new_template.to_dict(), status=HTTPStatus.CREATED)


async def template_add_to_favorite(request: web.Request, user: User) -> web.Response:
    """
    Add given template to user's favorite
    """

    async with services.db.acquire() as conn:
        template = await validate_template_exists(
            conn=conn,
            template_id=request.match_info['template_id'],
            company_ids=[user.company_id, None],
        )
        await insert_favorites_template(
            conn=conn,
            role_id=user.role_id,
            template_id=template.id,
        )

    return web.json_response(status=HTTPStatus.NO_CONTENT)


async def template_delete_from_favorite(request: web.Request, user: User) -> web.Response:
    """
    Remove given template from user's favorite
    """

    async with services.db.acquire() as conn:
        template = await validate_template_exists(
            conn=conn,
            template_id=request.match_info['template_id'],
            company_ids=[user.company_id, None],
        )
        await delete_favorites_template(
            conn=conn,
            role_id=user.role_id,
            template_id=template.id,
        )

    return web.json_response(status=HTTPStatus.NO_CONTENT)


async def template_preview_content(request: web.Request, user_row: User) -> web.Response:
    """
    Get template preview content
    """

    async with services.db.acquire() as conn:
        ctx = await validate_preview_content(
            conn=conn,
            user=user_row,
            data={
                'template_id': request.match_info['template_id'],
                'size': request.query.get('size', PREVIEW_MAX_SIZE),
            },
        )
    try:
        content = await get_or_generate_template_preview(ctx)
    except TimeoutError as e:
        raise e
    except InvalidRequest as e:
        raise e
    except Exception as e:
        logger.exception('Failed to get template preview', exc_info=e)
        return web.Response(status=404)

    return web.Response(body=content, content_type=f'image/{PREVIEW_FORMAT}')


async def create_document_from_template(
    request: web.Request,
    user: User,
) -> web.Response:
    """
    Create a document from template.
    Replacing template variables with values from request.
    """

    async with services.db.acquire() as conn:
        ctx = await validate_document_upload_from_template(
            conn=conn,
            user=user,
            template_id=request.match_info['template_id'],
            data_raw=dict(await validate_json_request(request)),
        )

    template_content, _ = await s3_utils.download(
        DownloadFile(key=get_templates_s3_key(ctx.template.id))
    )
    document_content = replace_text_in_docx(
        content=template_content,
        replacements=ctx.replacements,
    )

    params: DataDict = {
        'edrpou': ctx.user.company_edrpou,
        'expected_recipient_signatures': 1,
        'expected_owner_signatures': 1,
        'recipient_emails': [],
        'title': ctx.title,
        'amount': ctx.amount,
        'date_document': ctx.date_document,
        'doc_number': ctx.number,
        'recipient_edrpou': None,
        'debug': services.config.app.debug,
        'source': DocumentSource.vchasno_template_standalone.value,
        'is_internal': ctx.category_id is not None
        and ctx.category_id not in PublicDocumentCategory.ids(),
    }

    if ctx.template.category is not None:
        params['category'] = ctx.category_id

    file = FileField(
        name='file.docx',
        filename=ctx.template.title + '.docx',
        file=BytesIO(document_content),  # type: ignore
        content_type='application/octet-stream',
        headers=CIMultiDictProxy(CIMultiDict()),
    )

    post_data: MultipartFormData = MultiDict({'file': file})
    documents, _ = await process_upload(
        user=ctx.user,
        params=params,
        post_data=post_data,
        company_edrpou=ctx.user.company_edrpou,
        request_source=get_source(request),
        header_vendor=None,
        ensure_vendor=False,
        auth_method=None,
    )
    await send_to_indexator(services.redis, [documents[0].id], to_slow_queue=False)
    return json_response(documents[0].to_api(), status=HTTPStatus.CREATED)
