import datetime
import functools
from collections.abc import Callable
from decimal import ROUND_HALF_UP, Decimal
from typing import Annotated, Any, Generic, ParamSpec, TypeVar

import pydantic
from pydantic_core import core_schema
from trafaret import DataError

from app.lib import regexp, validators
from app.lib.datetime_utils import soft_parse_raw_datetime
from app.lib.helpers import ensure_ascii, unique_list, unique_list_with_original_order

P = ParamSpec('P')
R = TypeVar('R')
T = TypeVar('T')

# Python doesn't have limits for integers, but to properly handle them in another system, like
# database, elastic search, etc., we need to provide some limits for integers.
MAX_INT_64 = 9223372036854775807  # 2**63 - 1


def _convert_trafaret_error(func: Callable[P, R]) -> Callable[P, R]:
    """
    Decorator to convert Trafaret DataError to ValueError to make it
    possible to reuse Trafaret validators with Pydantic.
    """

    @functools.wraps(func)
    def wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
        try:
            return func(*args, **kwargs)
        except DataError as e:
            # Pydantic expects ValueError or AssertionError
            raise ValueError(str(e))

    return wrapper


def _invalid_to_none(v: Any, handler: Callable[[Any], Any]) -> Any:
    """
    Handles the validation of a value through a given handler function and returns None if
    the validation fails with a pydantic.ValidationError.
    """
    try:
        return handler(v)
    except pydantic.ValidationError:
        return None


@_convert_trafaret_error
def _validate_edrpou(value: str) -> str:
    return validators.EDRPOU().check(value)


@_convert_trafaret_error
def _validate_email(value: str) -> str:
    return validators.email(nullable=False).check(value)


@_convert_trafaret_error
def _validate_uuid(value: str) -> str:
    return validators.UUID().check(value)


@_convert_trafaret_error
def _validate_ip(value: str) -> str:
    return validators.IP().check(value)


@_convert_trafaret_error
def _validate_url(value: str) -> str:
    """
    Validate string URL and return it as a string, unlike Pydantic's AnyHttpUrl
    """
    adapter = pydantic.TypeAdapter(pydantic.AnyHttpUrl)
    validated: pydantic.AnyHttpUrl = adapter.validate_python(value)
    return str(validated)


@_convert_trafaret_error
def _right_datetime(value: str) -> datetime.datetime:
    validated = validators.validate_right_datetime(value)
    if not validated:
        raise ValueError('Value is not a valid datetime')
    return validated


@_convert_trafaret_error
def _left_datetime(value: str) -> datetime.datetime:
    validated = validators.validate_left_datetime(value)
    if not validated:
        raise ValueError('Value is not a valid datetime')
    return validated


def _email_domain(value: str) -> str:
    if not value:
        raise ValueError(f'Domain is empty: {value}')

    if ensure_ascii(value) is None:
        raise ValueError(f'Domain is not a valid ASCII string: {value}')

    if not all(value.split('.')) or len(value.split('.')) == 1:
        raise ValueError(f"Has no country code after '.': {value}")

    return value


def process_price(value: Decimal) -> Decimal:
    return value.quantize(Decimal('.01'), rounding=ROUND_HALF_UP)


def preprocess_int_enum(value: Any) -> Any:
    """
    Convert str to int to support enum values in str format

    Example: "8001" -> 8001 -> UserRole.admin
    """

    if isinstance(value, str) and value.isdigit():
        return int(value)

    # Everything else should be validated by standard pydantic validation
    return value


@_convert_trafaret_error
def _datetime(value: str) -> datetime.datetime:
    return validators.validate_datetime(value)


def _soft_datetime(value: str) -> datetime.datetime | None:
    return soft_parse_raw_datetime(value)


@_convert_trafaret_error
def _date(value: str) -> datetime.date:
    return validators.validate_date(value)


@_convert_trafaret_error
def _validate_password(value: str) -> str:
    return validators.password().check(value)


@_convert_trafaret_error
def _validate_phone(value: str) -> str:
    return validators.phone(allow_blank=False).check(value)


@_convert_trafaret_error
def _validate_ipn(value: str) -> str:
    return validators.ipn().check(value)


# Custom types:
# More documentation: https://docs.pydantic.dev/latest/concepts/types/#custom-types
EDRPOU = Annotated[str, pydantic.AfterValidator(_validate_edrpou)]
Email = Annotated[str, pydantic.AfterValidator(_validate_email)]
UUID = Annotated[
    str,
    pydantic.AfterValidator(_validate_uuid),
    pydantic.WithJsonSchema({'format': 'uuid', 'type': 'string'}),
]
IP = Annotated[str, pydantic.AfterValidator(_validate_ip)]
URL = Annotated[str, pydantic.AfterValidator(_validate_url)]
RightDatetime = Annotated[datetime.datetime, pydantic.BeforeValidator(_right_datetime)]
LeftDatetime = Annotated[datetime.datetime, pydantic.BeforeValidator(_left_datetime)]
Datetime = Annotated[datetime.datetime, pydantic.BeforeValidator(_datetime)]
SoftDatetime = Annotated[datetime.datetime, pydantic.BeforeValidator(_soft_datetime)]
Date = Annotated[datetime.date, pydantic.BeforeValidator(_date)]
EmailDomain = Annotated[str, pydantic.AfterValidator(_email_domain)]
Price = Annotated[
    Decimal,
    pydantic.Field(
        ...,
        description='Price in UAH',
        examples=['100.50', '200.00'],
        ge=0,
    ),
    pydantic.AfterValidator(process_price),
]
IntID = Annotated[int, pydantic.Field(..., ge=0, lt=MAX_INT_64)]
Password = Annotated[str, pydantic.AfterValidator(_validate_password)]
# For old passwords that might not meet the new password requirements
InsecurePassword = Annotated[str, pydantic.Field(min_length=7, max_length=255)]
Phone = Annotated[str, pydantic.AfterValidator(_validate_phone)]
IPN = Annotated[str, pydantic.AfterValidator(_validate_ipn)]
PassportCode = Annotated[
    str,
    pydantic.Field(pattern=regexp.passport_re),
]

# Custom list types
UniqueList = Annotated[list[T], pydantic.BeforeValidator(unique_list)]
UniqueListWithOriginalOrder = Annotated[
    list[T], pydantic.BeforeValidator(unique_list_with_original_order)
]

# Convert None to False for boolean fields
BoolNullToFalse = Annotated[bool, pydantic.BeforeValidator(lambda v: v if v is not None else False)]
BoolNullToTrue = Annotated[bool, pydantic.BeforeValidator(lambda v: v if v is not None else True)]

# Validators that return None on validation error
EDRPOUOrNone = Annotated[EDRPOU | None, pydantic.WrapValidator(_invalid_to_none)]
PhoneOrNone = Annotated[Phone | None, pydantic.WrapValidator(_invalid_to_none)]
EmailOrNone = Annotated[Email | None, pydantic.WrapValidator(_invalid_to_none)]

# Billing-specific types
PositiveInt = Annotated[int, pydantic.Field(gt=0)]
NonNegativeInt = Annotated[int, pydantic.Field(ge=0)]

# Enum wrapper that can accept "str" values and convert them to "int" for Enum with int values
# Example: IntEnum[UserRole] == "8001" -> 8001 -> UserRole.admin
IntEnum = Annotated[T, pydantic.BeforeValidator(preprocess_int_enum)]

# Type adapters (for validating scalar types, without model):
# More documentation:https://docs.pydantic.dev/latest/concepts/type_adapter/
UUIDAdapter = pydantic.TypeAdapter(UUID)
EDRPOUAdapter = pydantic.TypeAdapter(EDRPOU)
PhoneAdapter = pydantic.TypeAdapter(Phone)
EmailAdapter = pydantic.TypeAdapter(Email)
DatetimeAdapter = pydantic.TypeAdapter(Datetime)
DateAdapter = pydantic.TypeAdapter(Date)
# Explicitly annotate adapters to pass mypy check
EDRPOUOrNoneAdapter: pydantic.TypeAdapter[EDRPOU | None] = pydantic.TypeAdapter(EDRPOUOrNone)
PhoneOrNoneAdapter: pydantic.TypeAdapter[Phone | None] = pydantic.TypeAdapter(PhoneOrNone)
EmailOrNoneAdapter: pydantic.TypeAdapter[Email | None] = pydantic.TypeAdapter(EmailOrNone)


class SoftList(list[T], Generic[T]):
    """A list that validates items but skips items with wrong type instead of failing"""

    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: type[Any], handler: Any
    ) -> core_schema.PlainValidatorFunctionSchema:
        # Get the inner type from the generic
        if hasattr(source_type, '__args__') and source_type.__args__:
            inner_type = source_type.__args__[0]
        else:
            inner_type = Any

        # Create a schema that validates each item but continues on error
        def soft_validate_list(items: Any) -> list[Any]:
            if not isinstance(items, list):
                raise ValueError('Expected a list')

            result = []
            for item in items:
                try:
                    adapter = pydantic.TypeAdapter(inner_type)
                    validated_item = adapter.validate_python(item)
                    result.append(validated_item)
                except Exception:
                    continue
            return result

        return core_schema.no_info_plain_validator_function(soft_validate_list)


class DocumentIdValidator(pydantic.BaseModel):
    document_id: UUID


def model_validate_no_explicit_nulls(
    model: pydantic.BaseModel,
    *,
    include: set[str] | None = None,
    exclude: set[str] | None = None,
) -> None:
    """
    Check that the frontend does not send "null" for most fields, as "None" when None is used
    as "not set" value and should not be allowed to be sent in the request.

    Use "ignore" to specify fields that should be allowed to be sent as "null" value. To distinguish
    between "not set" and "null" values for such fields, use "exclude_unset=True" during model
    serialization or use "model_fields_set" method to check if the field is set.
    """
    data = model.model_dump(exclude_unset=True)
    for key, value in data.items():
        if exclude is not None and key in exclude:
            continue

        if include is not None and key not in include:
            continue

        if value is None:
            raise ValueError(f'Field {key} is not allowed to be null')
