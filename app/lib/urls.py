from collections.abc import Mapping
from typing import Any

import yarl
from multidict import MultiDict

from app.services import services


def build_url(
    route: str | None = None,
    *,
    path: str | None = None,
    absolute: bool = True,
    get: Mapping[str, str | int | None] | None = None,
    **context: Any,
) -> str:
    """Build absolute or relative URL for given route name."""

    if route:
        url = services.app.router[route].url_for(**context)
    elif path:
        assert path.startswith('/'), 'Path must start with /'
        url = yarl.URL.build(path=path)
    else:
        raise ValueError('Either route or path must be provided')

    if get:
        # Remove None values from get params. Pay attention that "get" can be multidict
        # with multiple keys with the same name, so don't use dict comprehension here.
        get_params = MultiDict([(k, v) for k, v in get.items() if v is not None])
        url = url.with_query(get_params)

    if not absolute:
        return str(url)

    config = services.config.app
    domain = config.domain

    return f'{domain}{url}'


def build_static_url(filename: str) -> str:
    """Use app static_host building static urls."""
    config = services.config.app
    static_host = config.static_host

    static_host = static_host.removesuffix('/')
    filename = filename.removeprefix('/')
    return f'{static_host}/{filename}'
