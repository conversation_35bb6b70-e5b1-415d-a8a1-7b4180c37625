from __future__ import annotations

import asyncio
import base64
import csv
import datetime
import decimal
import functools
import hashlib
import io
import itertools
import json
import logging
import math
import os
import random
import re
import secrets
import string
import traceback
import unicodedata
import uuid
from collections import defaultdict
from collections.abc import Awaitable, Callable, Hashable, Iterable, Iterator, Sequence
from contextlib import contextmanager
from contextvars import ContextVar
from copy import deepcopy
from dataclasses import dataclass
from decimal import ROUND_HALF_UP, Decimal
from enum import Enum
from functools import lru_cache, wraps
from http import HTTPStatus
from mimetypes import guess_type
from re import Match
from types import MappingProxyType
from typing import (
    Any,
    NamedTuple,
    ParamSpec,
    TypeVar,
    cast,
)

import charset_normalizer
import ujson
from aiohttp import web
from inflection import camelize
from logevo.vars import get_current_rxid
from lxml import etree
from redis.exceptions import LockError, RedisError
from translitua import RussianInternationalPassport
from translitua import translit as translit_func
from typing_extensions import deprecated
from yarl import URL

from api.errors import Code, Error, TooManyRequests
from app.flags import FeatureFlags
from app.i18n.types import LazyI18nString
from app.lib import regexp
from app.lib.constants import VCHASNO_COMPANY_EDRPOU, VCHASNO_COMPANY_ID_KEY
from app.lib.database import DBConnection
from app.lib.datetime_utils import ONE_HOUR_DELTA
from app.lib.types import (
    AnyDict,
    DataDict,
    HandlerResponse,
    StrIterable,
    StrList,
    StrMapping,
)
from app.services import services

T = TypeVar('T')
G = TypeVar('G')
P = ParamSpec('P')
R = TypeVar('R')

EnumType = TypeVar('EnumType', bound=Enum)

DEFAULT_EXP = Decimal('0.01')

NT_ID_MAPPING = MappingProxyType({'id_': 'id'})
NT_ID_TYPE_MAPPING = MappingProxyType({'id_': 'id', 'type_': 'type'})

RP_ID_MAPPING = MappingProxyType({'id': 'id_'})
RP_ID_TYPE_MAPPING = MappingProxyType({'id': 'id_', 'type': 'type_'})

type ConvertFields = dict[str, Callable[[Any], Any]] | None
type IgnoreFields = set[str] | Sequence[str] | None
type IgnoreValues = set[Any] | Sequence[Any] | None
type RequestHandler = Callable[[web.Request], Awaitable[web.StreamResponse]]

ASCII_LETTERS_AND_DIGITS: str = string.ascii_letters + string.digits

ZAKUPKI_EDRPOU = '40283641'

# Rate limiter consts
DEFAULT_TTL = 60  # seconds
REDIS_RATE_LIMITER_LOCK_TIMEOUT = 5  # seconds


# Regexp to detect CSV injection to escape it with a single quote.
# Illegal characters are: =, +, -, @, \t, \r
CSV_EXCEL_INJECTION_REGEXP = re.compile(r'^\s*[=+\-@\t\r].*$')

EMAIL_SEPARATOR_REGEX = re.compile(r'[;,]+')

logger = logging.getLogger(__name__)


def add_quotes(value: str | None) -> str:
    """Quote given string."""
    if not isinstance(value, str):
        return ''
    return f'"{value}"'


@lru_cache
def read_csv_file(file_path: str) -> set[tuple[str, ...]]:
    csv_file = csv.reader(open(file_path))
    return {tuple(line) for line in csv_file}


def csv_define_delimiter(file_data: str) -> str:
    delimiter = ','
    if file_data.count(';') > file_data.count(','):
        delimiter = ';'
    return delimiter


def csv_detect_encoding(file_content: bytes) -> str:
    encoding = charset_normalizer.detect(file_content)['encoding']
    if not encoding:
        raise Error(Code.unknown_encoding)
    return cast(str, encoding)


def csv_excel_escape(value: str) -> str:
    """
    Escape value for MS Excel to prevent it from being interpreted as formula (CSV injection)

    https://owasp.org/www-community/attacks/CSV_Injection
    """
    if isinstance(value, str) and CSV_EXCEL_INJECTION_REGEXP.match(value):
        value = f"'{value}"
    return value


def csv_writer(headers: list[str], rows: list[dict[str, str]]) -> bytes:
    """
    CSV writer that gives "bytes" as output. Everything is handled in memory
    """
    buffer = io.StringIO()

    _headers = [csv_excel_escape(header) for header in headers]

    writer = csv.DictWriter(buffer, fieldnames=_headers)
    writer.writeheader()

    for row in rows:
        row = {csv_excel_escape(k): csv_excel_escape(v) for k, v in row.items()}
        writer.writerow(row)

    return buffer.getvalue().encode('utf-8')


def empty_string(value: str | None) -> str:
    """Return empty string in case of null value."""
    return '' if value is None else value


def ensure_ascii(value: str) -> str | None:
    try:
        value.encode('ascii')
    except UnicodeEncodeError:
        return None
    return value


def split_comma_separated_emails(email: str | None) -> list[str]:
    """
    This functions allow properly split emails from comma separated string into a list of emails

    Remember to validate emails after this function
    """
    if isinstance(email, list):
        logger.warning(  # type: ignore[unreachable]
            msg='Comma separated emails cannot be list',
            extra={'email': email},
            exc_info=True,
        )
        raise ValueError('email should be str or None')

    if not email:
        return []

    # NOTE: despite the name, this regex also splits by semicolon because there are some
    # integrations that expect this behavior
    emails = [item.strip() for item in EMAIL_SEPARATOR_REGEX.split(email)]
    return [item for item in emails if item]


def join_comma_separated_emails(emails: list[str] | None) -> str | None:
    """
    Combine a list of emails into a single string where emails are separated by comma
    """
    if isinstance(emails, str):
        logger.warning(  # type: ignore[unreachable]
            msg='Comma separated emails cannot be str',
            extra={'emails': emails},
            exc_info=True,
        )
        raise ValueError('emails should be list or None')

    if not emails:
        return None

    return ', '.join(emails)


def ensure_iterable(value: str | StrIterable) -> StrIterable:
    """Ensure value's type is list or other iterator."""
    return [value] if isinstance(value, str) else value


def ensure_mimetype(file_name: str) -> str:
    """Ensure that given file name has a mimetype."""
    guessed = guess_type(file_name)
    if guessed[0] is not None:
        return guessed[0]

    ext = os.path.splitext(file_name)[1].lstrip('.')
    logger.info(
        'Unable to guess mimetype from file name. Fallback to default one',
        extra={'ext': ext, 'file_name': file_name},
    )
    return f'application/x-{ext}'


def get_content_type(file_name: str, encoding: str | None = None) -> str:
    """
    Get content type for file.
    If encoding is provided, add it to content type.
    """
    mimetype = ensure_mimetype(file_name)
    need_to_add_charset = mimetype.startswith('text/') or 'xml' in mimetype or 'json' in mimetype
    if encoding and need_to_add_charset:
        return f'{mimetype}; charset={encoding}'
    return mimetype


def ensure_utf8(value: str) -> str:
    """Ensure that utf-8 string does not have surrogates.

    If it has surrogates - log the original value and ignore surrogates.
    """
    try:
        value.encode('utf-8')
    except UnicodeEncodeError:
        logger.warning('String contains surrogates', extra={'value': value})
        return value.encode('utf-8', 'replace').decode('utf-8')
    return value


def safe_enum_value(value: Enum | None) -> Any:
    """Return enum value or None if enum is None."""
    return getattr(value, 'value', value) if value else None


def get_list_or_none(data: DataDict, key: str) -> StrList | None:
    """Helper function for getting value of dict and create list with this
    value only if value is in dict and doesn't empty
    """
    return [data[key]] if key in data and data[key] else None


async def is_vchasno_company(conn: DBConnection, company_id: str) -> bool:
    from app.auth.db import select_company_id

    vchasno_company_id = await services.redis.get(VCHASNO_COMPANY_ID_KEY)
    if vchasno_company_id:
        return vchasno_company_id == company_id

    vchasno_company_id = await select_company_id(conn, VCHASNO_COMPANY_EDRPOU, True)
    if vchasno_company_id:
        await services.redis.set(VCHASNO_COMPANY_ID_KEY, vchasno_company_id)
    return vchasno_company_id == company_id


def soft_json_serializer(obj: Any) -> Any:
    """
    JSON serializer for objects not serializable by standard library.

    In case when an object is not serializable, it will silently return None.
    """
    if isinstance(obj, datetime.date):
        return obj.isoformat()
    if isinstance(obj, decimal.Decimal):
        return str(obj)
    if isinstance(obj, Enum):
        return str(obj.value)
    if isinstance(obj, LazyI18nString):
        return obj.value
    return None


def json_serializer(obj: Any) -> Any:
    """JSON serializer for objects not serializable by standard library."""
    if isinstance(obj, datetime.date):
        return obj.isoformat()
    if isinstance(obj, decimal.Decimal):
        return str(obj)
    if isinstance(obj, Enum):
        return obj.value
    if isinstance(obj, set):
        return list(obj)
    if isinstance(obj, LazyI18nString):
        return obj.value
    raise TypeError(f'Type {type(obj)} is not serializable')


def to_json(data: Any) -> str:
    from app.flags.utils import get_flag

    try:
        if get_flag(FeatureFlags.ENABLE_UJSON_AS_DEFAULT_SERIALIZER):
            # keep reject_bytes=False for compatibility with regular json.dumps
            # https://github.com/ultrajson/ultrajson/releases/tag/3.0.0
            return ujson.dumps(data, default=json_serializer, reject_bytes=False)  # type: ignore[call-arg]
    except LookupError:
        # FF is not available in some contexts,
        # so just fall back to standard json
        ...

    return json.dumps(data, default=json_serializer)


def json_response(data: Any, status: int = HTTPStatus.OK) -> web.Response:
    return web.json_response(data=data, dumps=to_json, status=status)


def match_encoding(value: str, codecs: list[tuple[str, str]]) -> str | None:
    for enc_codec, dec_codec in codecs:
        try:
            return value.encode(enc_codec).decode(dec_codec)
        except UnicodeError:
            continue
    return None


def namedtuple_to_dict(
    value: NamedTuple,
    *,
    ignore_fields: IgnoreFields | None = None,
    ignore_values: IgnoreValues | None = None,
    rename_fields: StrMapping | None = None,
    convert_fields: ConvertFields | None = None,
) -> DataDict:
    """Convert namedtuple to dict.

    It's is a replacement to ``._asdict()`` method as it also able to ignore
    some keys to be converted, and support renaming namedtuple keys to custom
    dict keys.
    """
    convert = convert_fields or {}
    rename = rename_fields or {}

    def iterate(value: Any) -> Iterator[tuple[str, Any]]:
        for idx, field in enumerate(value._fields):
            if ignore_fields and field in ignore_fields:
                continue

            converter = convert.get(field)
            idx_value = value[idx]
            idx_value = converter(idx_value) if converter else idx_value
            if ignore_values and idx_value in ignore_values:
                continue

            yield (rename.get(field) or field, idx_value)

    return dict(iterate(value))


def parse_invoice(value: str) -> str | None:
    """Invoice number is taken from the inner part of 1C document number.

    For instance: 'UA-00001241-51' or 'ZK-00001241' -> '00001241'.
    """
    if not value or not isinstance(value, str):
        return None

    items = value.split('-')
    if len(items) >= 2:
        return items[1]
    return items[0]


def nested_defaultict() -> defaultdict[str, Any]:
    """Create nested defaultdict."""
    return defaultdict(nested_defaultict)


def normalize_to_composed(value: str) -> str:
    """Normalize to composed form e.g. 'и' + ʼ\u0306ʼ -> 'й'."""
    return unicodedata.normalize('NFC', value)


def quantize(value: Decimal, exp: Decimal = DEFAULT_EXP, rounding: str = ROUND_HALF_UP) -> Decimal:
    return value.quantize(exp, rounding)


def safe_filename(value: str) -> str:
    """Naive version of ensuring safe file name value."""
    return value.replace('/', '-')


def safe_xml_text(root: etree._Element, xpath: str) -> str | None:
    """Read text for XML tag or xpath."""
    found = root.xpath(xpath)
    if not found:
        return None
    return found[0].text


@deprecated('Use helpers::str_to_bool instead.')
def string_to_bool(value: str | None) -> bool | None:
    """Convert string GET parameter to bool type."""
    if value is None:
        return value

    if value in {'True', 'true', '1'}:
        return True

    return False


def str_to_bool(s: str | None) -> bool | None:
    if s is None:
        return None
    if s.lower() in ('y', 'yes', 't', 'true', 'on', '1'):
        return True
    if s.lower() in ('n', 'no', 'f', 'false', 'off', '0'):
        return False
    raise ValueError(f'Invalid bool env value: {s}')


def null_to_true(value: bool | None) -> bool:
    """Convert None to `True` or return the value itself."""
    if value is None:
        return True

    return value


def blank_to_null(value: str | None) -> str | None:
    """Convert '' to None or return the value itself"""
    if value == '':
        return None

    return value


def null_to_false(value: bool | None) -> bool:
    """Convert None to `False` or return the value itself."""
    if value is None:
        return False

    return value


def to_camelcase(value: DataDict, *, uppercase_fisrt_letter: bool = False) -> DataDict:
    """Convert all inner keys in dict from snake to camel case."""
    return {
        camelize(key, uppercase_fisrt_letter): (
            to_camelcase(value, uppercase_fisrt_letter=uppercase_fisrt_letter)
            if isinstance(value, dict)
            else value
        )
        for key, value in value.items()
    }


def to_decimal(value: Decimal | int | float | str) -> Decimal:
    """
    Convert value to decimal with rounding to two decimal places.

    Example:
        1.234434 → Decimal('1.23')
    """
    return Decimal(value).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)


def translit(value: str) -> str:
    """Transliterate given string using UkrainianKMU table.

    If any non-latin characters left - use Russian transliteration table.
    """
    result = translit_func(value)

    if not ensure_ascii(result):
        return translit_func(result, RussianInternationalPassport)
    return result


def truncate_and_strip(value: Any, limit: int | None = None) -> str | None:
    """Only strip if no limit."""
    if isinstance(value, Match):
        value = value.string
    if not isinstance(value, str):
        # TODO: refactor, need to know all possible input types
        return value
    return value.strip()[:limit]


def trim_float_trailing_zero(num: float) -> float | int:
    if num % 1 == 0:
        return int(num)
    return num


def unique_list(original: list[T]) -> list[T]:
    """Return unique list of values"""
    return list(set(original))


def unique_list_with_original_order(original: list[T]) -> list[T]:
    checked = []
    for item in original:
        if item not in checked:
            checked.append(item)

    return checked


def update_path(url: URL, new_path: str) -> URL:
    return url.with_path(new_path).with_query(url.query).with_fragment(url.fragment)


def update_url(url: str, *, query: DataDict | None = None) -> str:
    # Do not attempt update URL with empty query
    if not query:
        return url
    return str(URL(url).update_query(query))


def update_urls(urls: DataDict, *, query: DataDict | None = None) -> DataDict:
    return {key: update_url(value, query=query) for key, value in urls.items()}


def generate_uuid() -> str:
    return str(uuid.uuid4())


@dataclass(frozen=True, slots=True)
class RateLimitState:
    allowed: bool
    lock_time: int | None


async def _is_rate_limit_within_grace(
    key: str,
    limit: int | None,
    period: datetime.timedelta | None,
) -> bool:
    """
    Simple implementation of rate limit based on Redis counter.

    WARNING: use "validate_rate_limit" instead of this function.
    """
    counter_key = f'rlb:{key}'

    if not limit and not period:
        return False

    assert limit is not None, 'Expected positive integer for limit'
    assert period is not None, 'Expected period to be positive'

    try:
        current = await services.redis.incr(counter_key)
        if current == 1:
            ttl_seconds = max(1, int(period.total_seconds()))
            await services.redis.expire(counter_key, ttl_seconds)
        if current <= limit:
            return True

    except RedisError:
        logger.warning('Rate limit counter redis-error', exc_info=True)
        return True

    return False


async def validate_rate_limit(
    *,
    key: str,
    limit: int,
    delta: datetime.timedelta,
    grace_period: datetime.timedelta | None = None,
    grace_limit: int | None = None,
) -> None:
    """
    Validate ability to perform request due to rate limits
    """

    # Allow some amount of fast requests before applying rate limit
    if await _is_rate_limit_within_grace(key, limit=grace_limit, period=grace_period):
        return

    key = f'rl:{key}'
    state = await check_rate_limit(key=key, limit=limit, period=delta)
    if state.allowed:
        return

    logger.warning('Rate limit error', extra={'key': key, 'limit': limit, 'delta': delta})
    raise TooManyRequests(details={'lock_time': state.lock_time})


async def reset_rate_limit(key: str) -> None:
    """
    Remove rate limit key from Redis store to reset rate limit state
    """
    await services.redis.delete(key)


async def check_rate_limit(
    key: str,
    limit: int,
    period: datetime.timedelta,
) -> RateLimitState:
    """
    Returns whether a request should be allowed due to rate limits.

    This function implements a rate limiter using the GCRA (Generic Cell Rate Algorithm) approach,
    which is a type of leaky-bucket algorithm. The primary goal of using this algorithm is to
    efficiently manage rate limits while minimizing memory usage in Redis.

    For more information on the GCRA algorithm, refer to:
    https://en.wikipedia.org/wiki/Generic_cell_rate_algorithm

    :param key: A unique identifier for the rate limiting operation.
    :param limit: The maximum number of requests allowed within the specified time period.
    :param period: The time period during which the rate limit is applied, represented as a
                   datetime.timedelta object.

    :return: it returns a RateLimitOutput object with two fields:
        - allowed: A boolean indicating whether the request is allowed (True) or denied (False).
        - retry_after: An integer representing the time in seconds after which the request can be
            retried. If the request is allowed, this will be None.

    ATTENTION! In case of changing logic inside that function - check test_case
    app.lib.tests.test_lib_helpers.test_rate_limiter_new
    """
    assert limit > 0, 'Expected positive integer for limit'
    period_seconds_float = period.total_seconds()
    assert period_seconds_float > 0, 'Expected period to be positive'

    redis = services.redis
    try:
        # Get the current time from Redis
        now_tuple = await redis.time()  # Returns (seconds: int, microseconds: int)
        now = now_tuple[0] + (now_tuple[1] / 1_000_000.0)

        # Emission interval (T in GCRA terminology) - time cost per request
        emission_interval = period_seconds_float / limit

        async with redis.lock(
            f'rate_limiter_lock:{key}',
            timeout=services.config.app.request_timeout,
            blocking_timeout=REDIS_RATE_LIMITER_LOCK_TIMEOUT,  # How long to wait to acquire
        ):
            stored_val = await redis.get(key)

            # Robust handling of stored TAT
            current_tat_from_storage = now
            if stored_val is not None:
                try:
                    current_tat_from_storage = float(stored_val)
                except ValueError:
                    logger.warning(
                        f"Invalid TAT value '{stored_val}' in Redis for key '{key}'. "
                        f'Resetting rate limit state for this key using current time.'
                    )

            # Effective Theoretical Arrival Time (TAT) for the current check.
            # If TAT is in the past, it means the "bucket" has capacity; start from 'now'.
            effective_tat = max(current_tat_from_storage, now)

            # GCRA allowance check:
            # Allow if (effective_tat + emission_interval <= now + period_seconds_float)
            # This is equivalent to: effective_tat - now <= period_seconds_float - emission_interval
            # Or: effective_tat <= now + period_seconds_float - emission_interval
            allowance_boundary = now + period_seconds_float - emission_interval

            if effective_tat <= allowance_boundary:
                # Request is allowed. Update the TAT for the next potential request.
                new_tat = effective_tat + emission_interval

                # TTL should ensure key lives for at least the current rate limit window.
                key_ttl_seconds = math.ceil(period_seconds_float)
                key_ttl_seconds = max(1, key_ttl_seconds)  # Ensure TTL is at least 1 sec for EX

                await redis.set(name=key, value=new_tat, ex=key_ttl_seconds)
                return RateLimitState(allowed=True, lock_time=None)

            # Request denied
            retry_after = math.ceil(effective_tat - allowance_boundary)
            return RateLimitState(allowed=False, lock_time=retry_after)

    except (RedisError, LockError):
        logger.warning('Rate limit redis-error', exc_info=True)
        # Do not lock a request in case of internal problems with redis
        return RateLimitState(allowed=True, lock_time=None)


def get_client_ip(request: web.Request) -> str | None:
    """Get IP for the current request"""

    # Custom header that contains client real-ip from CloudFlare Worker
    # For redirects from vchasno.ua/api -> edo.vchasno.ua/api etc.
    if worker_redirect_real_ip := request.headers.get('x-worker-real-ip'):
        return worker_redirect_real_ip

    return request.headers.get('X-Real-IP')


def client_rate_limit(
    *,
    limit: int,
    delta: datetime.timedelta = ONE_HOUR_DELTA,
) -> Callable[[RequestHandler], RequestHandler]:
    """Simple rate limiter that doesn't allow to make more than
    max allowed number of requests by current IP address
    """

    def decorator(func: RequestHandler) -> RequestHandler:
        @wraps(func)
        async def wrapped(request: web.Request) -> web.StreamResponse:
            client_ip = get_client_ip(request) or 'UKNOWN_IP'
            rate_key = f'{func.__name__}:{client_ip}'
            await validate_rate_limit(
                key=rate_key,
                limit=limit,
                delta=delta,
            )
            return await func(request)

        return wrapped

    return decorator


def generate_base64_str(content: bytes) -> str:
    """
    >>> generate_base64_str(b'test')
    'dGVzdA=='
    """
    return base64.b64encode(content).decode('utf-8')


def decode_base64_str(content: str) -> bytes:
    """
    >>> decode_base64_str('dGVzdA==')
    b'test'
    """
    return base64.b64decode(content)


def generate_base64_sha256(content: bytes) -> str:
    sha256 = hashlib.sha256()
    sha256.update(content)
    file_hash = sha256.digest()
    return generate_base64_str(file_hash)


def get_url2pdf_base_url() -> str:
    return services.config.url2pdf.url


async def run_sync(
    func: Callable[P, R],
    *args: P.args,
    **kwargs: P.kwargs,
) -> R:
    """Run sync code in thread pool executor"""

    # INFO: asyncio.to_thread also copies all context variables from the current context
    # to the new thread, so we don't need to pass them explicitly. Examples of context
    # variables in our app are: rxid/txid, locale, services

    _func = functools.partial(func, *args, **kwargs)
    return await asyncio.to_thread(_func)


async def run_sync_in_process_pool(
    func: Callable[..., R],
    timeout: int,
) -> R:
    """
    Run sync code in process_pool executor

    WARNING!
    Function will be executed in a pool of 'spawned' processes,
    which means that each process from the pool is 'fresh' and doesn't have context of the
    main process, therefore, all necessary context for function execution must be provided
    via parameters, and all parameters must be serializable.
    (data is transferring between processes using pickle).
    """
    loop = asyncio.get_event_loop()
    pool_executor = services.app['process_pool']

    # In case of running sync code inside offline_app, fallback to tread_pool
    if pool_executor is None:
        # temporary log call stack to detect handlers which uses this function
        tracked_stack = []
        start_track = False
        for line in traceback.format_stack()[:-1]:
            if 'concierge_middleware' in line:
                start_track = True
            if start_track:
                tracked_stack.append(line.strip())
        logger.warning(
            'Fallback to running sync code in thread pool executor',
            extra={
                'traceback': '\n'.join(tracked_stack) if tracked_stack else '',
            },
        )
        # Do not use "asyncio.to_thread" or "run_sync" here, to have always the same behavior
        # with a context passing, to be able to catch errors early in dev or test environments
        return await loop.run_in_executor(None, func)

    return await loop.run_in_executor(pool_executor, func, timeout)


@contextmanager
def set_contextvar(var: ContextVar[T], value: T) -> Iterator[None]:
    token = var.set(value)
    try:
        yield
    finally:
        var.reset(token)


def group_list(items: list[T], key_func: Callable[[T], G]) -> defaultdict[G, list[T]]:
    mapping: defaultdict[G, list[T]] = defaultdict(list)
    for item in items:
        mapping[key_func(item)].append(item)
    return mapping


def contains_duplicates(iterable: Iterable[Hashable]) -> bool:
    seen: set[Hashable] = set()
    for item in iterable:
        if item in seen:
            return True
        seen.add(item)
    return False


def remove_keys_from_dict(data: DataDict, keys: set[str]) -> DataDict:
    if not keys:
        return data.copy()
    return {key: value for key, value in data.items() if key not in keys}


def get_file_extension(filename: str) -> str:
    """Get extensions from filename with point prefixed
    >>> get_file_extension('signature.xml')
    '.xml'
    """
    return os.path.splitext(filename)[-1]


def get_filename_base(filename: str) -> str:
    """Get filename without extension
    >>> get_filename_base('signature.xml')
    'signature'

    >>> get_filename_base('signature.pdf.zip')
    'signature.pdf'

    """
    return os.path.splitext(filename)[0]


def build_full_user_name(first: str | None, second: str | None, last: str | None) -> str | None:
    parts = [first, second, last]
    name = ' '.join(part for part in parts if part).strip()
    return name if name else None


HashableT = TypeVar('HashableT', bound=Hashable)


def clear_duplicates(data: list[HashableT]) -> list[HashableT]:
    items = set()
    result = []
    for item in data:
        if item in items:
            continue
        result.append(item)
        items.add(item)
    return result


def get_random_digits(length: int) -> str:
    return str(secrets.randbelow(10**length)).zfill(length)


def get_random_letters_and_digits(length: int) -> str:
    return ''.join(random.choices(ASCII_LETTERS_AND_DIGITS, k=length))


def grouped(iterable: Iterable[Any], n: int, fillvalue: Any | None = None) -> Iterator[tuple[Any]]:
    """Collect data into non-overlapping fixed-length chunks or blocks

    grouped('ABCDEFG', 3, 'x') --> ABC DEF Gxx
    """
    args = [iter(iterable)] * n

    return itertools.zip_longest(*args, fillvalue=fillvalue)


def deep_merge_dicts(*dicts: DataDict) -> DataDict:
    """
    Merges dicts deeply.
    >>> deep_merge_dicts({'a': {'key1': [1, 2]}}, {'a': {'key2': [1, 2, 3]}})
    {'a': {'key1': [1, 2], 'key2': [1, 2, 3]}}
    """

    def merge_into(d1: DataDict, d2: DataDict) -> DataDict:
        for key in d2:
            if key not in d1 or not isinstance(d1[key], dict):
                d1[key] = deepcopy(d2[key])
            else:
                d1[key] = merge_into(d1[key], d2[key])
        return d1

    return functools.reduce(merge_into, dicts, {})


def get_request_canonical_path(request: web.Request) -> str:
    """
    Get canonical path from request. Example of canonical path:
    - /api/v2/documents/{document_id}/signatures
    - /internal-api/roles/{role_id}/permissions
    """
    resource = request.match_info.route.resource
    if resource is None:
        return ''
    return resource.canonical


def immutable_dict(data: AnyDict) -> MappingProxyType[Any, Any]:
    """Convert mutable dict to immutable one.

    And ensure that all inner dicts are also converted to immutable ones.
    """
    return MappingProxyType(
        {
            key: immutable_dict(value) if isinstance(value, dict) else value
            for key, value in data.items()
        }
    )


def attach_rxid_header(response: HandlerResponse) -> None:
    """Attach rxid header to response object"""

    header = 'X-Request-Id'
    rxid = get_current_rxid()

    response.headers[header] = rxid


def is_valid_edrpou(edrpou: str) -> bool:
    """
    Check if given string is a valid EDRPOU code
    """
    if not edrpou:
        return False

    if not regexp.company_code_re.match(edrpou):
        return False

    if regexp.zero_company_code_re.match(edrpou):
        return False

    return True


def to_base64str(data: bytes | str) -> str:
    """Convert bytes to base64 string"""

    if isinstance(data, str):
        data = data.encode('utf-8')

    return base64.b64encode(data).decode('utf-8')


def not_none(value: T | None) -> T:
    """
    Convert optional value to non-optional one. Raise ValueError if value is None

    Use it in cases when you are sure that value is not None as more type safe
    alternative to "typing.cast" function
    """
    if value is None:
        raise ValueError('Value is None')
    return value


def is_domain_trusted(url: URL) -> bool:
    """
    Check if given URL is trusted by the application via trusted_origins config option
    """
    trusted_origins = services.config.app.trusted_origins or []
    return url.is_absolute() and str(url.origin()) in trusted_origins


def find_remove_in_list(items: list[T], key: Callable[[T], bool]) -> T | None:
    """
    Finds and removes the first item in the list that matches the given condition.
    """
    for i, element in enumerate(items):
        if key(element):
            return items.pop(i)
    return None


def str_to_hash(text: str) -> str:
    return hashlib.blake2b(text.encode('UTF-8', 'surrogateescape')).hexdigest()
