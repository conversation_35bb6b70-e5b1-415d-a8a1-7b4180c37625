import asyncio
import json
import logging
import os
import random
from datetime import datetime

import aiokafka
import logevo
from aiokafka.helpers import create_ssl_context
from aiokafka.producer.message_accumulator import BatchBuilder

from app.config.schemas import KafkaConfig
from app.delayed_task.utils import add_delayed_task
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.database import DBConnection
from app.lib.helpers import json_serializer, to_json
from app.lib.types import AnyDict, DataDict
from worker.utils import (
    get_consumer_heartbeat_max_polling_interval,
)

logger = logging.getLogger(__name__)

HEALTH_CHECK_TIMEOUT_SECONDS = 5


class KafkaClient:
    # Default value from AIOKafkaProducer is 1048576
    _max_request_size = 1048576

    def __init__(self, config: KafkaConfig | None) -> None:
        if not config:
            self.is_configured = False
            return

        self.is_configured = True

        self.config: KafkaConfig = config

        self.bootstrap_servers = config.bootstrap_servers
        self.client_id = config.client_id

        # Worker type is provided when kafka client initialized in worker
        self.group_id = config.group_id
        if worker_type := os.getenv('WORKER_TYPE'):
            self.group_id = config.group_id + f'-{worker_type}'

        ssl_context = create_ssl_context() if config.security_protocol == 'SSL' else None

        max_poll_interval_ms = get_consumer_heartbeat_max_polling_interval()

        self.consumer = aiokafka.AIOKafkaConsumer(
            session_timeout_ms=self.config.consumer.session_timeout_ms,
            max_poll_interval_ms=max_poll_interval_ms,
            group_id=self.group_id,
            bootstrap_servers=self.bootstrap_servers,
            client_id=self.client_id,
            value_deserializer=self.deserializer,
            enable_auto_commit=True,
            security_protocol=config.security_protocol,
            ssl_context=ssl_context,
            sasl_mechanism=config.sasl_mechanism,
            sasl_plain_username=config.sasl_plain_username,
            sasl_plain_password=config.sasl_plain_password,
        )

        self.producer = aiokafka.AIOKafkaProducer(
            request_timeout_ms=self.config.producer.request_timeout_ms,
            bootstrap_servers=self.bootstrap_servers,
            client_id=self.client_id,
            value_serializer=self.serializer,
            security_protocol=config.security_protocol,
            ssl_context=ssl_context,
            sasl_mechanism=config.sasl_mechanism,
            sasl_plain_username=config.sasl_plain_username,
            sasl_plain_password=config.sasl_plain_password,
            max_request_size=self._max_request_size,
        )

    @staticmethod
    def deserializer(value: str) -> DataDict:
        return json.loads(value)

    @staticmethod
    def serializer(value: DataDict) -> bytes:
        data = json.dumps(value, default=json_serializer, separators=(',', ':'))
        return bytes(data, 'utf-8')

    async def update_cluster_metadata(self) -> None:
        await asyncio.wait_for(
            self.producer.client.force_metadata_update(), timeout=HEALTH_CHECK_TIMEOUT_SECONDS
        )

    async def add_task(
        self,
        *,
        topic: str,
        conn: DBConnection | None = None,  # is required for delay_min
        delay_min: int | None = None,
        execute_after: datetime | None = None,
        data: DataDict | None = None,
    ) -> None:
        """
        Decide whether to schedule a delayed message or send on execution directly to kafka
        """
        if delay_min:
            assert conn, 'Database connection is required for delayed task'

            await add_delayed_task(
                conn=conn,
                topic=topic,
                delay_min=delay_min,
                execute_after=execute_after,
                data=data or {},
            )
            return

        await self.send_record(topic, data)

    async def send_record(
        self,
        topic: str,
        value: AnyDict | None = None,
        key: DataDict | None = None,
    ) -> None:
        """
        Send record to Kafka topic.
        It's a low level method, use it only if you need to send record immediately.
        Consider using add_task as a high level method for delayed tasks.
        """
        if not self.is_configured:
            logger.error('Record not sent due to missing Kafka config')
            return

        value = value or {}

        # Providing rixd to worker for better logging
        value_copy = value.copy()
        value_copy.setdefault('rxid', logevo.get_current_rxid())

        message = await self.producer.send_and_wait(
            topic,
            key=key,
            value=value_copy,
        )

        logging.info(
            'Kafka record produced',
            extra={
                'topic': message.topic,
                'partition': message.partition,
                'offset': message.offset,
                'data': value_copy,
                'msg_timestamp': message.timestamp,
            },
        )

    async def send_records(
        self,
        topic: str,
        values: list[AnyDict],
    ) -> None:
        """Send batch of records"""
        if not self.is_configured:
            logger.error('Records not sent due to missing Kafka config')
            return

        if not values:
            return

        if not get_flag(FeatureFlags.DISABLE_LOGS_FOR_KAFKA_BATCH_SENDING):
            # use json.dumps to get more accurate size
            # because getsizeof doesn't count nested objects
            res = []
            for value in values:
                res.append(sum(to_json(value).encode('utf-8')))

            total_size = sum(res)
            if total_size > self._max_request_size * 0.9:
                logger.info(
                    'Payload is too large. Splitting into several batches',
                    extra={
                        'batch_topic': topic,
                        'batch_size': len(values),
                        'batch_total_size_bytes': total_size,
                        'batch_avg_size_bytes': total_size / len(values),
                        'batch_median_size_bytes': sorted(res)[len(res) // 2],
                        'batch_max_size_bytes': max(res),
                        'batch_min_size_bytes': min(res),
                    },
                )

        batch = self.producer.create_batch()
        while len(values) > 0:
            value = values.pop()
            value_copy = value.copy()
            value_copy.setdefault('rxid', logevo.get_current_rxid())
            metadata = batch.append(key=None, value=value_copy, timestamp=None)
            # Metadata is None if the batch is full,
            # but sometimes we can get MessageSizeTooLargeError
            # So try to fix it by having a lower threshold
            if metadata is None or batch.size() > self._max_request_size * 0.9:
                await self._send_batch(topic, batch)
                batch = self.producer.create_batch()
                values.append(value)

        if batch.record_count() > 0:
            await self._send_batch(topic, batch)

    async def _send_batch(self, topic: str, batch: BatchBuilder) -> None:
        partitions = await self.producer.partitions_for(topic)
        partition = random.choice(tuple(partitions))
        res = await self.producer.send_batch(batch, topic, partition=partition)
        await res
        logger.info(
            'Produced batch of kafka records',
            extra={
                'batch_topic': topic,
                'batch_size': batch.record_count(),
                'batch_partition': partition,
            },
        )
