import logging
from typing import Any
from urllib.parse import urljoin

import logevo
import uj<PERSON>
from aiohttp import ClientError, ClientSession

from app.lib.sender.enums import SenderMessageType, SenderProject

logger = logging.getLogger(__name__)


class EvoSenderError(Exception):
    pass


class EvoSender:
    def __init__(
        self,
        *,
        client_session: ClientSession,
        base_url: str,
        project_id: str,
    ) -> None:
        self._base_url = base_url
        self._project_id = project_id
        self._client_session = client_session

    async def send_sms(
        self,
        *,
        phone: str,
        message: str,
        message_type: SenderMessageType,
        billing_project: SenderProject | None = None,
    ) -> None:
        """
        Under the hood to send SMS and to send Viber/SMS cascade we use the same method
        """
        return await self.send_viber_or_sms(
            phone=phone,
            message=message,
            only_sms=True,
            message_type=message_type,
            billing_project=billing_project,
        )

    async def send_viber_or_sms(
        self,
        *,
        phone: str,
        message: str,
        message_type: SenderMessageType,
        only_sms: bool = False,
        billing_project: SenderProject | None = None,
    ) -> None:
        """
        Send notification via Evo Sender which will be delivered via
        either SMS or Viber.
        """

        url = urljoin(self._base_url, '/api/v1/sms')
        body = self._prepare_viber_sms_body(
            phone=phone,
            message=message,
            only_sms=only_sms,
            # TODO: Seems like this parameter is not used properly because Sender fallbacks to
            #   minimum TTL (40 seconds) if provided TTL is less than 40 seconds.
            viber_ttl_seconds=15,
            message_type=message_type,
            billing_project=billing_project,
        )
        request = self._client_session.post(
            url=url,
            json=body,
            headers=self._prepare_headers(),
        )
        json_response = None
        try:
            async with request as response:
                response.raise_for_status()
                json_response = await response.json(loads=ujson.loads)
                logger.info(
                    'Sent SMS via Evo Sender',
                    extra={
                        'phone': phone,
                        'message_id': json_response['request_id'],
                    },
                )
        except ClientError:
            logger.exception('Error when requesting Evo Sender', extra={'phone': phone})
            raise EvoSenderError()
        except Exception:
            logger.exception(
                'Unexpected response from Evo Sender',
                extra={'response': json_response, 'phone': phone},
            )
            raise EvoSenderError()

    def _prepare_viber_sms_body(
        self,
        *,
        phone: str,
        message: str,
        message_type: SenderMessageType,
        viber_ttl_seconds: int | None = None,
        only_sms: bool | None = None,
        billing_project: SenderProject | None = None,
    ) -> dict[str, Any]:
        params: dict[str, Any] = {
            'project': self._project_id,
            'phone': phone,
            'text': message,
            # Hide the message content in the logs for security reasons.
            # Because most of SMS that we send are OTP codes, this parameter is enabled by default.
            'hide_content': True,
            # Meta is free form dictionary that can be used to store any additional
            # information about the message.
            'meta': {
                'message_type': message_type.value,
            },
        }

        # In some cases we might send message on behalf of another project than EDO.
        # For this purposes we set meta field "billing_project" and then analytics can
        # correctly determine which project should be charged for the message.
        #
        # By default when this parameter is not set, the message will be charged to
        # the EDO project
        if billing_project is not None:
            params['meta']['billing_project'] = billing_project.value

        # We try to send the message via Viber first for N seconds and then via SMS.
        if viber_ttl_seconds is not None:
            params['viber_ttl'] = str(viber_ttl_seconds)

        if only_sms is not None:
            params['only_sms'] = only_sms

        return params

    def _prepare_headers(self) -> dict[str, str]:
        return {'X-Request-Id': logevo.get_current_rxid()}
