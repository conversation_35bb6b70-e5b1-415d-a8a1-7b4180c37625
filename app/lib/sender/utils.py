import logging
from typing import assert_never

from api.errors import TooManyRequests
from app.config import get_level
from app.flags.enums import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib.datetime_utils import ONE_DAY_DELTA, ONE_HOUR_DELTA, ONE_MINUTE_DELTA
from app.lib.enums import AppLevel
from app.lib.helpers import get_random_digits, validate_rate_limit
from app.lib.sender.enums import SenderMessageType, SenderMethod, SenderProject
from app.services import services

logger = logging.getLogger(__name__)


OTP_CODE_LENGTH = 6
DEFAULT_SMS_LIMIT = 10
DEFAULT_SMS_LIMIT_DELTA = ONE_HOUR_DELTA
SMS_DAILY_BAN_LIMIT = 50


def generate_otp_code() -> str:
    """
    Generate a random one-time password (OTP) code of six digits
    that can be used for phone verification or authentication
    """
    return get_random_digits(OTP_CODE_LENGTH)


async def send_phone_otp_code(
    *,
    phone: str,
    otp: str,
    message_type: SenderMessageType,
    method: SenderMethod = SenderMethod.cascade,
    billing_project: SenderProject | None = None,
) -> None:
    """
    Send an SMS with the specified OTP to the specified phone number to verify
    the user's ownership of the number
    """

    domain = services.config.app.domain
    message = _('{otp} - код підтвердження {domain}').format(domain=domain, otp=otp)

    if method == SenderMethod.cascade:
        await services.evo_sender.send_viber_or_sms(
            phone=phone,
            message=message,
            message_type=message_type,
            billing_project=billing_project,
        )
    elif method == SenderMethod.sms:
        await services.evo_sender.send_sms(
            phone=phone,
            message=message,
            message_type=message_type,
            billing_project=billing_project,
        )
    else:
        assert_never(method, f'Unknown sender method: {method}')

    if get_level() in {AppLevel.local, AppLevel.dev}:
        logger.info(f'Sent phone OTP code: {otp}', extra={'phone': phone, 'method': method})


async def validate_ban_otp_rate_limit(*, user_id: str) -> None:
    """
    Validate the rate limit for sending OTP codes to banned users.
    This is a special case where we allow sending OTP codes to users
    who are banned, but only once per hour.
    """
    from app.auth import utils as auth_utils

    try:
        await validate_rate_limit(
            key=f'ban_otp:send:{user_id}',
            limit=SMS_DAILY_BAN_LIMIT,
            delta=ONE_DAY_DELTA,
        )
    except TooManyRequests:
        async with services.db.acquire() as conn:
            await auth_utils.ban_user(
                conn=conn,
                user_id=user_id,
                is_banned=True,
                reason='Auto-banned for too many OTP requests',
            )
        raise


async def validate_send_phone_otp_rate_limit(*, user_id: str) -> None:
    """
    Validate the rate limit for sending phone verification codes.

    For 2FA or auth by phone use similar functions with separate key space.
    """

    await validate_rate_limit(
        key=f'phone_otp:send:{user_id}',
        limit=DEFAULT_SMS_LIMIT,
        delta=DEFAULT_SMS_LIMIT_DELTA,
    )
    await validate_ban_otp_rate_limit(user_id=user_id)


async def validate_send_phone_2fa_rate_limit(*, user_id: str) -> None:
    """
    Validate the rate limit for sending phone 2FA codes.

    NOTE: use only in context of 2FA, not for phone auth, registration or verification.
    """

    await validate_rate_limit(
        key=f'phone_2fa:send:{user_id}',
        limit=DEFAULT_SMS_LIMIT,
        delta=DEFAULT_SMS_LIMIT_DELTA,
    )
    await validate_ban_otp_rate_limit(user_id=user_id)


async def validate_send_phone_auth_rate_limit(
    *,
    auth_phone: str,
    client_ip: str | None,
) -> None:
    """
    Validate the rate limit for sending code for login or registration by phone number.
    """

    # Allow 5 fast requests, then 1 request per minute and not more than 20 requests per hour
    await validate_rate_limit(
        key=f'phone_auth:send:{auth_phone}',
        limit=1,
        delta=ONE_MINUTE_DELTA,
        grace_limit=5,
        grace_period=ONE_HOUR_DELTA,
    )
    await validate_rate_limit(
        key=f'phone_auth:send:spam:{auth_phone}',
        limit=20,
        delta=ONE_HOUR_DELTA,
    )

    # Prevent abuse by IP address with different phone numbers
    if client_ip:
        # Client IP is passed through several proxy layers, so this feature flag can be used
        # to disable rate limit by IP in case of infrastructure misconfiguration
        if not get_flag(FeatureFlags.DISABLE_PHONE_AUTH_RATE_LIMIT_BY_IP):
            await validate_rate_limit(
                key=f'phone_auth:send:ip:{client_ip}',
                limit=20,
                delta=ONE_HOUR_DELTA,
            )
    else:
        logger.warning(
            'Client IP is not provided for phone auth rate limit validation',
            extra={'auth_phone': auth_phone},
        )
