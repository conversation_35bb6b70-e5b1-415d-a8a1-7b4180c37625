import enum

from app.lib.enums import NamedEnum


class SenderMethod(enum.Enum):
    """
    Which method to use for sending OTP codes.
    """

    sms = 'sms'
    cascade = 'cascade'


class SenderProject(enum.StrEnum):
    """
    Projects names from Sender service config.

    WARNING: Do not change these values without changing the corresponding values in the Sender
    service config and code.
    """

    edo = 'vchasno.com.ua'
    ttn = 'ttn.vchasno.ua'


class SenderMessageType(NamedEnum):
    """
    Determines the type of message being sent by EDO service.

    This value we send to Sender service in meta.message_type field for analytics purposes.

    HINT: You are free to add new values, without any changes in the Sender service config
    """

    telegram_bot = enum.auto()
    inbox_document = enum.auto()
    phone_verification = enum.auto()
    phone_auth = enum.auto()
    phone_2fa = enum.auto()
