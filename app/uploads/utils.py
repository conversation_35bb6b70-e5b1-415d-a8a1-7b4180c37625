import asyncio
import dataclasses
import datetime
import logging
import os
import re
import uuid
from collections.abc import Iterable, Iterator
from contextlib import contextmanager
from typing import (
    Any,
    TypeVar,
    assert_never,
)

from aiohttp import (
    web,
)
from aiohttp.web_request import <PERSON><PERSON>ield
from lxml import etree
from multidict import MultiDict
from stream_unzip import UnzipError, async_stream_unzip
from yarl import URL

from api.downloads.constants import BUFFER_CHUNK_SIZE
from api.enums import Vendor
from api.errors import (
    Code,
    Error,
)
from api.uploads.types import MetaData
from api.uploads.utils import (
    handle_encoding,
    handle_encoding_bytes,
    is_one_sign_type,
    parse_title,
)
from api.uploads.xml_utils import parse_xml
from app.actions.utils import get_source
from app.archive.types import ArchiveDocumentsCtx
from app.archive.utils import archive_documents
from app.auth.constants import AUTH_METHOD_APP_KEY
from app.auth.db import (
    decrement_company_upload_documents_left,
    update_companies_ipn,
)
from app.auth.enums import AuthMethod
from app.auth.types import User
from app.comments import utils as comments
from app.comments.enums import CommentType
from app.comments.types import Comment
from app.contacts.db import select_contact_email_by_edrpou, upsert_contacts
from app.contacts.types import ContactBase
from app.document_antivirus.db import insert_document_antivirus_checks
from app.document_antivirus.utils import (
    add_document_antivirus_checks,
)
from app.document_versions.db import insert_document_versions_batch
from app.document_versions.types import InsertDocumentVersionParameters
from app.documents.db import (
    add_documents_links,
    add_documents_meta,
    insert_document_access_settings_private,
    insert_documents,
    insert_listings,
    insert_recipients,
    select_last_recipient_email,
)
from app.documents.enums import (
    AccessSource,
    DocumentAccessLevel,
    DocumentSource,
    FirstSignBy,
)
from app.documents.types import (
    Document,
    ListingDataAggregator,
    RecipientEmailsAggregator,
    UpsertDocumentRecipientDict,
)
from app.documents.utils import (
    find_recipient_emails,
    schedule_send_first_notification_to_signers_job,
    send_documents_status_callback_jobs,
    send_notification_about_document_access,
)
from app.documents_fields.utils import create_document_parameters_on_upload
from app.events import document_actions
from app.events.utils import get_signers_source
from app.flow.types import AddFlowOptions
from app.flow.utils import create_flows, send_multilateral_documents_to_recipients_job
from app.groups.db import insert_group_document_access
from app.lib import (
    error_reporting,
    eusign_utils,
    s3_utils,
    validators,
)
from app.lib.chunks import gather_chunks, iter_bytes_by_chunks, read_from_bytes_chunks
from app.lib.database import DBConnection, DBRow
from app.lib.enums import (
    DocumentStatus,
    SignatureFormat,
    SignatureType,
    Source,
)
from app.lib.helpers import (
    ensure_mimetype,
    ensure_utf8,
    generate_base64_str,
    is_valid_edrpou,
    join_comma_separated_emails,
    run_sync,
)
from app.lib.logging import log_duration
from app.lib.types import (
    DataDict,
    MultipartFormData,
    MultipartFormValue,
)
from app.lib.validators import is_valid_email
from app.onboarding import utils as onboarding_utils
from app.reviews import utils as reviews
from app.reviews.db import insert_review_settings
from app.reviews.enums import ReviewRequestSource, ReviewRequestStatus
from app.reviews.types import Recipient, ReviewerId, ReviewsInfoCtx
from app.services import services
from app.signatures.db import insert_document_signers
from app.signatures.enums import SignatureSource
from app.signatures.types import P7SContent
from app.signatures.utils import (
    add_signature,
    add_signature_schedule_async_jobs,
    is_internal_signature,
    unpack_wrapped_signature_container,
)
from app.tags.utils import add_tags, schedule_create_document_access_on_document_tag_job
from app.uploads import constants
from app.uploads.constants import (
    MB,
    PARSE_META_BY_RECIPIENT_EDRPOU,
    SIGNATURE_FILE_EXTENSION,
)
from app.uploads.types import (
    File,
    FileDetails,
    FlowsUploadSettings,
    InsertedDocumentsCtx,
    RawFile,
    ReviewRequestData,
    UploadCommentCtx,
    UploadedSignature,
    UploadOptions,
)
from app.uploads.validators import (
    validate_files,
    validate_upload_from_url,
    validate_upload_params,
)
from worker import topics

logger = logging.getLogger(__name__)

MetadataFieldT = TypeVar('MetadataFieldT', str, list[str], int, datetime.datetime)


async def download_from_url(url: URL) -> RawFile:
    async with services.http_client.get(url) as response:
        response.raise_for_status()
        content_type = response.headers['Content-Type']
        return RawFile(
            body=await response.read(),
            content_type=ensure_mimetype(content_type),
            filename=url.name,
        )


def _prepare_signature_format(
    options: UploadOptions,
    signature: UploadedSignature | None,
) -> SignatureFormat:
    """Prepare expected signature format on document uploading"""

    # If the signature format was provided in the download request, then use it
    if options.signature_format:
        return options.signature_format

    # If document uploaded with signature, then other signatures should
    # have the same format
    if signature:
        return signature.format_

    # Other documents by default will have external separated signature format
    return SignatureFormat.external_separated


def _prepare_file_flows_settings(options: UploadOptions) -> FlowsUploadSettings | None:
    """Prepare flows for document uploading"""

    # Flow passed in the request has higher priority
    if settings := options.flows_settings:
        updated_flows = []
        for flow in settings.flows:
            # If there are document signers parameters, then use them for calculating
            # the number of signatures required for the owner's flow
            sign_num = flow.flow_signatures_count
            if options.signers and flow.flow_edrpou == options.company_edrpou:
                sign_num = len(options.signers)

            updated_flow = dataclasses.replace(flow, flow_signatures_count=sign_num)

            updated_flows.append(updated_flow)

        return FlowsUploadSettings(
            flows=updated_flows,
            should_send=settings.should_send,
        )

    return None


def _merge_metadata_sources(
    meta_xml: MetaData | None,
    meta_title: MetaData | None,
) -> MetaData:
    """
    Merge metadata extracted from XML and title

    Metadata in title has higher priority than metadata from XML, because if we can parse
    metadata from title, then it means that the user consciously formed the title in our
    complex format. But metadata from XML can be extracted from the XML file by coincidence.
    """

    # If we have metadata only form one source, then return that metadata
    if meta_xml is None or meta_title is None:
        return meta_title or meta_xml or MetaData()

    # For most attributes, we use metadata from the title, except for amount
    # which
    return MetaData(
        owner_edrpou=meta_title.owner_edrpou or meta_xml.owner_edrpou,
        owner_ipn=meta_title.owner_ipn or meta_xml.owner_ipn,
        recipient_company_name=meta_title.recipient_company_name or meta_xml.recipient_company_name,
        recipient_short_company_name=meta_title.recipient_short_company_name
        or meta_xml.recipient_short_company_name,
        recipient_edrpou=meta_title.recipient_edrpou or meta_xml.recipient_edrpou,
        recipient_ipn=meta_title.recipient_ipn or meta_xml.recipient_ipn,
        recipient_emails=meta_title.recipient_emails or meta_xml.recipient_emails,
        recipient_names=meta_title.recipient_names or meta_xml.recipient_names,
        external_id=meta_title.external_id or meta_xml.external_id,
        vendor_id=meta_title.vendor_id or meta_xml.vendor_id,
        type_=meta_title.type_ or meta_xml.type_,
        number=meta_title.number or meta_xml.number,
        date=meta_title.date or meta_xml.date,
        amount=meta_title.amount if meta_title.amount is not None else meta_xml.amount,
        category=meta_title.category or meta_xml.category,
    )


def _strip_metadata_field(value: MetadataFieldT | None) -> MetadataFieldT | None:
    """
    Remove leading and trailing whitespaces from metadata fields and convert empty objects to None.
    """
    if value is None:
        return None
    if isinstance(value, str):
        return value.strip() or None
    if isinstance(value, list):
        items = [t for v in value if (t := v.strip())]
        return items or None
    if isinstance(value, int):
        return value  # don't strip or convert to None integer
    if isinstance(value, datetime.datetime):
        return value

    # If you try to preprocess a new type, add a new branch here and update MetadataFieldT
    assert_never(value)


def _soft_correct_metadata(meta: MetaData) -> MetaData:
    """
    Check that all metadata fields are valid and make corrections if necessary.

    This function should be "silent" and should avoid raising an exception. We do not expect the
    parsed metadata to always be correct; it is only used to automate uploading documents. If a
    field is incorrect or missing, we should simply replace it with `None`. The user will have a
    chance to correct it manually later.
    """

    updated = dataclasses.replace(meta)

    invalid_fields: dict[str, Any] = {}

    # First replace all empty strings '' with None
    updated.owner_edrpou = _strip_metadata_field(meta.owner_edrpou)
    updated.owner_ipn = _strip_metadata_field(meta.owner_ipn)
    updated.recipient_company_name = _strip_metadata_field(meta.recipient_company_name)
    updated.recipient_short_company_name = _strip_metadata_field(meta.recipient_short_company_name)
    updated.recipient_edrpou = _strip_metadata_field(meta.recipient_edrpou)
    updated.recipient_ipn = _strip_metadata_field(meta.recipient_ipn)
    updated.recipient_emails = _strip_metadata_field(meta.recipient_emails)
    updated.recipient_names = _strip_metadata_field(meta.recipient_names)
    updated.external_id = _strip_metadata_field(meta.external_id)
    updated.vendor_id = _strip_metadata_field(meta.vendor_id)
    updated.type_ = _strip_metadata_field(meta.type_)
    updated.number = _strip_metadata_field(meta.number)
    updated.date = _strip_metadata_field(meta.date)
    updated.amount = _strip_metadata_field(meta.amount)
    updated.category = _strip_metadata_field(meta.category)

    # Now validate separately check each field
    if (edrpou := updated.owner_edrpou) and not is_valid_edrpou(edrpou):
        invalid_fields['owner_edrpou'] = meta.owner_edrpou
        updated.owner_edrpou = None

    if (edrpou := updated.recipient_edrpou) and not is_valid_edrpou(edrpou):
        invalid_fields['recipient_edrpou'] = meta.recipient_edrpou
        updated.recipient_edrpou = None

    if emails := updated.recipient_emails:
        updated.recipient_emails = [e for e in emails if is_valid_email(e)]
        if set(meta.recipient_emails or []) != set(updated.recipient_emails or []):
            invalid_fields['recipient_emails'] = meta.recipient_emails

        updated.recipient_emails = updated.recipient_emails or None

    if invalid_fields:
        logger.info(
            msg='Invalid metadata fields',
            extra={
                'invalid_fields': str(invalid_fields),
                'original_metadata': str(meta),
                'updated_metadata': str(updated),
            },
        )

    return updated


def sync_get_meta_xml(file: RawFile, options: UploadOptions, from_archive: bool) -> MetaData | None:
    details = prepare_file_details(file)

    if details.extension != '.xml':
        return None

    try:
        return parse_xml(
            content=file.body,
            company_edrpou=options.company_edrpou,
        )
    except etree.Error:
        logger.warning(
            'Unable to parse XML from string. This may lead to inability to render XML on frontend',
            exc_info=True,
            extra={
                'company_edrpou': options.company_edrpou,
                'file_size': len(file.body),
                'file_title': details.title,
                'from_archive': from_archive,
                'user_id': options.user and options.user.id,
                'user_email': options.user and options.user.email,
                'vendor': options.vendor,
            },
        )
        return None


async def prepare_file(
    file: RawFile,
    options: UploadOptions,
    *,
    archive_details: FileDetails | None = None,
    signature: UploadedSignature | None = None,
    stamp: UploadedSignature | None = None,
) -> File:
    """Convert file field tuple from request into file dict.

    Right now the function only processing item filename, but going forward XML
    processing will be done here as well.
    """
    body = file.body
    company_edrpou = options.company_edrpou
    details = prepare_file_details(file)
    is_from_edi = options.source and DocumentSource(options.source).is_from_edi

    meta_xml = await run_sync(
        func=sync_get_meta_xml,
        file=file,
        options=options,
        from_archive=bool(archive_details),
    )

    meta_title: MetaData | None = None
    if meta_xml is None or (meta_xml.owner_edrpou is None and meta_xml.recipient_edrpou is None):
        meta_title = parse_title(details.title)

    # Merge metadata from different sources
    meta = _merge_metadata_sources(
        meta_xml=meta_xml,
        meta_title=meta_title,
    )

    if not meta.is_empty:
        logger.info(
            msg='Parsed metadata',
            extra={
                'meta_merged': str(meta),
                'meta_xml': str(meta_xml),
                'meta_title': str(meta_title),
            },
        )

    # Correct and remove invalid metadata fields
    meta = _soft_correct_metadata(meta)

    vendor = options.vendor

    if (
        not options.company_api_enabled
        and not is_from_edi
        and meta.recipient_edrpou not in PARSE_META_BY_RECIPIENT_EDRPOU
    ):
        logging.info(
            msg='Metadata parsing is disabled, ignore metadata from XML and title',
            extra={
                'is_company_api_enabled': options.company_api_enabled,
                'is_from_edi': is_from_edi,
                'recipient_edrpou': meta.recipient_edrpou,
            },
        )
        meta = MetaData()

    elif (
        options.company_api_enabled
        and not is_from_edi
        and not options.vendor  # == from WEB
        and meta.recipient_edrpou
    ):
        # Set a vendor to activate billing of a document
        vendor = Vendor.api.value

        logger.warning(
            'Upload document from WEB with metadata and API integration',
            extra={
                'company_edrpou': company_edrpou,
                'file_title': details.title,
                'user_id': options.user and options.user.id,
                'user_email': options.user and options.user.email,
            },
        )

    owner_edrpou = meta.owner_edrpou
    recipient_edrpou = meta.recipient_edrpou or options.recipient_edrpou
    first_sign_by = options.first_sign_by
    if (
        first_sign_by in (None, FirstSignBy.recipient.value)
        and recipient_edrpou == company_edrpou
        and recipient_edrpou != owner_edrpou
    ):
        owner_edrpou, recipient_edrpou = recipient_edrpou, owner_edrpou
        first_sign_by = FirstSignBy.recipient.value
    elif first_sign_by is None:
        first_sign_by = FirstSignBy.owner.value

    is_one_sign = is_one_sign_type(meta.type_)
    is_3p = first_sign_by == FirstSignBy.recipient.value
    expected_owner_signatures = options.expected_owner_signatures
    if expected_owner_signatures is None:
        expected_owner_signatures = 0 if is_one_sign and is_3p else 1

    expected_recipient_signatures = options.expected_recipient_signatures
    if expected_recipient_signatures is None:
        expected_recipient_signatures = 0 if is_one_sign and not is_3p else 1

    recipient_ipn = recipient_edrpou
    recipient_emails: list[str] = meta.recipient_emails or options.recipient_emails
    recipient_company_name = meta.recipient_company_name
    recipient_short_company_name = meta.recipient_company_name

    # Remove any recipient info for bad parameters
    if options.is_internal or (recipient_edrpou == owner_edrpou and recipient_edrpou is not None):
        logger.warning(
            msg='No need set recipient for internal documents',
            extra=meta.__dict__,
        )
        recipient_edrpou = None
        recipient_ipn = None
        recipient_emails = []
        recipient_company_name = None
        recipient_short_company_name = None
        expected_recipient_signatures = 0
        first_sign_by = FirstSignBy.owner.value

    if recipient_edrpou is None or options.is_multilateral:
        recipient_edrpou = None
        recipient_ipn = None
        recipient_emails = []
        recipient_company_name = None
        recipient_short_company_name = None

    signature_format = _prepare_signature_format(options, signature)

    flows_settings = _prepare_file_flows_settings(options)

    amount = options.amount
    if amount is None and meta.amount is not None:
        amount = meta.amount

    # Imported documents to the archive are histored by default.
    # No further actions available for this document.
    status_id = (
        DocumentStatus.uploaded.value
        if options.source != DocumentSource.archive_histored.value
        else DocumentStatus.histored.value
    )

    return File(
        id=str(uuid.uuid4()),
        uploaded_by=options.user.role_id if options.user else None,
        title=details.title,
        extension=details.extension,
        status_id=status_id,
        archive_name=archive_details.name if archive_details else None,
        edrpou_owner=owner_edrpou or company_edrpou,
        ipn_owner=meta.owner_ipn,
        edrpou_recipient=recipient_edrpou,
        ipn_recipient=recipient_ipn,
        recipient_emails=recipient_emails,
        hide_recipient_emails=options.hide_recipient_emails,
        company_name_recipient=recipient_company_name,
        company_short_name_recipient=recipient_short_company_name,
        date_document=options.date_document or meta.date,
        type_=meta.type_,
        category=options.category or meta.category,
        number=options.doc_number or meta.number,
        amount=amount,
        source=options.source or DocumentSource.vchasno.value,
        vendor=vendor,
        # Maybe needs renamed `meta.external_id` to `meta.vendor_id`
        vendor_id=options.vendor_id or meta.external_id,
        first_sign_by=first_sign_by,
        is_internal=options.is_internal,
        is_multilateral=options.is_multilateral,
        expected_owner_signatures=expected_owner_signatures,
        expected_recipient_signatures=expected_recipient_signatures,
        has_changed_for_public_api=True,
        signature_format=signature_format,
        # Internal values, which not be inserted into database
        body=body,
        content_type=file.content_type,
        file_name=file.filename,
        file_size=len(body),
        flows_settings=flows_settings,
        signature=signature,
        stamp=stamp,
        version_id=str(uuid.uuid4()) if options.is_versioned else None,
        content_hash=await eusign_utils.generate_hash_base64(body),
    )


def _prepare_filename(name: str) -> str:
    return os.path.basename(ensure_utf8(handle_encoding(name)))


def prepare_file_details(item: RawFile) -> FileDetails:
    return prepare_file_details_from_filename(item.filename)


def prepare_file_details_from_filename(file_name: str) -> FileDetails:
    """Handle file name details with proper encoding."""
    name = _prepare_filename(file_name)
    title, ext = os.path.splitext(name)
    return FileDetails(title=title, extension=ext.lower(), name=name)


def get_filename_title(name: str) -> str:
    """Remove all extension from file. Exaples:
    >>> get_filename_title('file.p7s.p7s')
    'file'
    >>> get_filename_title('file')
    'file'
    >>> get_filename_title('file.pdf')
    'file'
    """
    title, ext = os.path.splitext(_prepare_filename(name))
    if ext:
        return get_filename_title(title)
    return title


def get_filename_extension(name: str) -> str:
    details = prepare_file_details_from_filename(name)
    return details.extension


def remove_extension(value: str, ext: str) -> str:
    pattern = f'{re.escape(ext)}$'
    return re.sub(pattern, '', value, flags=re.IGNORECASE)


def guess_filename_from_signature_container(p7s: P7SContent, name: str) -> str:
    details = prepare_file_details_from_filename(name)
    for _ in range(p7s.depth):
        name = remove_extension(name, details.extension)

    details = prepare_file_details_from_filename(name)
    if details.extension:
        return name

    logger.info(
        msg='Signature container provided without original extension',
        extra={'file_name': name},
    )
    # fallback to default to .xml extension
    return f'{name}.xml'


async def prepare_raw_files(
    items: Iterable[MultipartFormValue],
    options: UploadOptions,
) -> list[RawFile]:
    """
    Read data from different sources and convert them to RawFile.
    Ignore all non-files from POST request.
    """

    raw_files = []
    urls_for_download = []

    for item in items:
        if isinstance(item, str):
            # Download file by URL if string is valid URL
            valid_url = await validate_upload_from_url(item, options)
            if not valid_url:
                continue
            urls_for_download.append(valid_url)

        if isinstance(item, FileField):
            # Download content of file if file was provided
            raw_files.append(
                RawFile(
                    body=await run_sync(item.file.read),
                    filename=item.filename,
                    content_type=item.content_type,
                )
            )

    # We use asyncio.TaskGroup instead of asyncio.gather to cancel all not finished tasks
    # if any task raised exception (except asyncio.CancelledError)
    download_tasks = []
    async with asyncio.TaskGroup() as tg:
        for url in urls_for_download:
            download_tasks.append(tg.create_task(download_from_url(url)))
    raw_files.extend([t.result() for t in download_tasks])

    return raw_files


async def prepare_files(
    data: MultipartFormData,
    options: UploadOptions,
) -> list[File]:
    """Convert user uploaded files to list of files metadata.

    Also unzip all archives.
    """
    archive_extensions = options.archive_extensions
    files: list[File] = []

    # Read content of different type of sources
    raw_files = await prepare_raw_files(items=data.values(), options=options)

    # Unpack all archives
    non_archives = []
    for raw_file in raw_files:
        details = prepare_file_details(raw_file)

        # Skip documents that are not archives
        if details.extension not in archive_extensions:
            non_archives.append(raw_file)
            continue

        # Unzip archive and prepare documents with signatures.
        items = await extract_files_from_archive(raw_file, options)
        files.extend(items)

    # For files, that are not archives, just prepare files.
    items = await prepare_files_with_signatures(non_archives, options)
    files.extend(items)

    return files


async def _send_notifications_on_upload_kafka(
    options: UploadOptions,
    files: list[File],
    source: Source,
) -> None:
    # Sending email for signers about this files
    share_to_all = list(options.share_to)

    for group_members in options.share_to_groups:
        share_to_all.extend(group_members.role_ids)

    if share_to_all:
        for file in files:
            await send_notification_about_document_access(
                user=options.user,
                document_id=file.id,
                document_title=file.title,
                roles_ids=list(share_to_all),
                source=source,
            )


async def process_upload(
    user: User | None,
    params: DataDict,
    post_data: MultipartFormData,
    request_source: Source,
    auth_method: AuthMethod | None,
    ensure_vendor: bool,
    header_vendor: str | None,
    company_edrpou: str,
    document_source: DocumentSource | None = None,
) -> tuple[list[File], UploadOptions]:
    """Handle whole process of validating and uploading files.

    Return list of uploaded files as result.
    """

    logger.info(
        msg='Process uploading',
        extra={
            'params': str(params),
            'request_source': request_source.value,
            'auth_method': auth_method and auth_method.value,
            'ensure_vendor': ensure_vendor,
            'company_edrpou': company_edrpou,
            'document_source': document_source and document_source.value,
        },
    )

    async with services.db.acquire() as conn:
        options = await validate_upload_params(
            conn=conn,
            data=params,
            company_edrpou=company_edrpou,
            auth_method=auth_method,
            ensure_vendor=ensure_vendor,
            header_vendor=header_vendor,
            user=user,
            document_source=document_source,
            request_source=request_source,
        )

    raw_files = await prepare_files(post_data, options)

    async with services.db.acquire() as conn:
        files = await validate_files(conn, raw_files, options)

    await upload_files(files=files, options=options, request_source=request_source)

    return files, options


async def prepare_upload_query_parameters(request: web.Request) -> dict[str, Any]:
    """
    Prepare URL parameters for an uploading document.
    Multi-parameter will be converted to one parameter with list of values.
    """

    params = MultiDict(request.rel_url.query)

    # Convert multidict to a dict.
    # WARNING: parameters that can be sent multiple times should be converted manually
    # to one key with a list of values. Example:
    # "?tags=1&tags=2&..." -> "tags=params.getall('tags', None)" == {'tags': [1,2,...]}
    data: DataDict = dict(
        params,
        recipient_emails=params.getall('recipient_emails', None),
        share_to=params.getall('share_to', None),
        share_to_groups=params.getall('share_to_groups', None),
        signer_roles=params.getall('signer_roles', None),
        signer_emails=params.getall('signer_emails', None),
        tags=params.getall('tags', None),
        new_tags=params.getall('new_tags', None),
        reviewers_ids=params.getall('reviewers_ids', None),
    )

    # Allow passing access settings in a simple query parameter for our integration clients.
    # Example:
    # {"access_settings": {"level": "private"}} -> ...?access_settings_level=private
    if 'access_settings_level' in data:
        data['access_settings'] = {'level': data['access_settings_level']}

    # For some nested objects, we require parameters to be encoded as JSON string to pass them
    # in the query parameters.
    #
    # Example:
    # -> {"parameters": {"key":"value"}} -> ...?parameters=%7B%22key%22%3A%22value%22%7D
    if raw_parameters := data.get('parameters'):
        data['parameters'] = validators.validate_json_query_param(raw_parameters)
    if raw_flows := data.get('flows'):
        data['flows'] = validators.validate_json_query_param(raw_flows)

    # remove None from dict
    data = {key: value for key, value in data.items() if value is not None}

    return data


def form_field_to_json(
    field: MultipartFormValue | None,
) -> DataDict:
    """
    Convert form data to JSON.
    """

    params: DataDict = {}
    if field is None:
        return params

    if isinstance(field, bytearray):
        param_content = bytes(field)
        params = validators.validate_json_string(value=param_content, allow_blank=True)
    if isinstance(field, str | bytes):
        params = validators.validate_json_string(value=field, allow_blank=True)
    elif isinstance(field, FileField):
        param_content = field.file.read()
        params = validators.validate_json_string(value=param_content, allow_blank=True)

    return params


async def prepare_upload_form_parameters(
    request: web.Request,
) -> tuple[DataDict, MultipartFormData]:
    """
    Validate multipart-form/data and separate parameters for uploading ("params" field)
    and files to upload (other form fields).
    """

    form_proxy = await validators.validate_post_request(request)
    # convert immutable ProxyMultiDict to mutable MultiDict
    form_data = form_proxy.copy()

    # remove "params" from form
    params_raw: MultipartFormValue | None
    params_raw = form_data.pop('params', None)

    # parse JSON in "params" field
    params = form_field_to_json(params_raw)

    return params, form_data


async def prepare_upload_parameters(
    request: web.Request,
) -> tuple[DataDict, MultipartFormData]:
    """
    Prepare parameters for document upload from web.

    Files for uploading should be sent as fields in multipart/form-data.
    Parameters for uploading can be sent as URL parameters (old way) or in the
    JSON-encoded "params" field of multipart/form-data.

    Example of request:
    ```
    POST /internal-api/documents?title=example&first_sign_by=recipient HTTP/1.1
    Content-Type: multipart/form-data; boundary=BOUNDARY

    --BOUNDARY
    Content-Disposition: form-data; name="params"
    Content-Type: application/json

    {"tags": [1,3,4], "doc_number": 1}
    --BOUNDARY
    Content-Disposition: form-data; name="doc.pdf"; filename="~/docs/doc.pdf"
    Content-Type: application/pdf

    <...PDF content...>
    --BOUNDARY--
    ```
    """

    # parse multipart/from-data and separate files to upload and parameters
    # for uploading.
    form_params, form_data = await prepare_upload_form_parameters(request)

    # convert URL query parameters to dict parameters for uploading
    query_params = await prepare_upload_query_parameters(request)

    # URL parameters take precedence over form parameters
    params = {
        **form_params,
        **query_params,
        'debug': services.config.app.debug,
    }

    return params, form_data


def should_check_vendor(
    request_source: Source,
    document_source: str | None,
) -> bool:
    # Do not check vendor for documents from EDI or HRS
    if document_source in (
        DocumentSource.edi.value,
        DocumentSource.edi_random_role.value,
        DocumentSource.hrs.value,
    ):
        return False

    # Check vendor, if it's not WEB documents
    return request_source not in (Source.api_internal, Source.api_mobile)


async def process_web_upload(
    request: web.Request,
    user: User,
    document_source: DocumentSource | None = None,
) -> tuple[list[File], UploadOptions]:
    """Handle whole process of validating and uploading files.

    Return list of uploaded files as result.
    """

    request_source = get_source(request)
    header_vendor = request.headers.get('Vendor')

    params, post_data = await prepare_upload_parameters(request)

    if document_source:
        params['source'] = document_source.value

    ensure_vendor = should_check_vendor(
        request_source=request_source,
        document_source=document_source and document_source.value,
    )

    return await process_upload(
        user=user,
        params=params,
        post_data=post_data,
        company_edrpou=user.company_edrpou,
        document_source=document_source,
        request_source=request_source,
        header_vendor=header_vendor,
        ensure_vendor=ensure_vendor,
        auth_method=request[AUTH_METHOD_APP_KEY],
    )


async def unzip_archive(body: bytes, allowed_extensions: set[str]) -> list[RawFile]:
    """Async function to read all files from ZIP archive."""

    items = []
    body_chunks = iter_bytes_by_chunks(body, chunk_size=BUFFER_CHUNK_SIZE)
    async for file_name, _, unzipped_chunks in async_stream_unzip(body_chunks):
        file_name = handle_encoding_bytes(file_name)
        details = prepare_file_details_from_filename(file_name)

        body = await read_from_bytes_chunks(unzipped_chunks)
        # Ignore unzipping non-supported or hidden files
        if details.extension not in allowed_extensions or details.is_hidden:
            continue

        file = RawFile(
            body=body,
            content_type=ensure_mimetype(file_name),
            filename=details.name,
        )

        items.append(file)

    return items


async def extract_files_from_archive(
    archive: RawFile,
    options: UploadOptions,
) -> list[File]:
    """Unzip archive and return list of file fields.

    We imitate here file uploads to easify working with files.
    """
    try:
        data = await unzip_archive(
            body=archive.body,
            allowed_extensions=options.allowed_extensions,
        )
    except UnzipError as err:
        raise Error(Code.bad_zip_file) from err

    archive_details = prepare_file_details(archive)
    return await prepare_files_with_signatures(data, options, archive_details)


async def prepare_files_with_signatures(
    data: list[RawFile],
    options: UploadOptions,
    archive_details: FileDetails | None = None,
) -> list[File]:
    """
    Split files into two categories: signatures and documents.
    We expect that external signed documents should have signatures with the
    same base title. Other signatures will be considered as internally signed
    containers.
    """

    # Split signatures and other documents
    signature_mapping = {}
    documents = []
    for item in data:
        ext = get_filename_extension(item.filename)
        if ext in SIGNATURE_FILE_EXTENSION:
            title = get_filename_title(item.filename)
            signature_mapping[title] = item
        else:
            documents.append(item)

    # Prepare file for all non signatures files
    files = []
    for document in documents:
        title = get_filename_title(document.filename)
        # We expect that, all signatures with the same name as document
        # has external format.
        signature_file = signature_mapping.pop(title, None)
        signature = None
        if signature_file:
            signature = UploadedSignature(
                file=signature_file,
                format_=SignatureFormat.external_separated,
            )

        file = await prepare_file(
            file=document,
            options=options,
            archive_details=archive_details,
            signature=signature,
        )
        files.append(file)

    # Expected that other signatures has internal format
    for signature_ in signature_mapping.values():
        if await is_internal_signature(signature_.body):
            file = await unpack_signature_container(signature_, options)
            files.append(file)

    return files


async def unpack_signature_container(file: RawFile, options: UploadOptions) -> File:
    # Unwrap signature container
    content = await unpack_wrapped_signature_container(file.body)

    # Prepare unwrapped filename
    filename = guess_filename_from_signature_container(content, file.filename)

    # Prepare signed file and signature
    original = RawFile(
        body=content.root,
        content_type=ensure_mimetype(filename),
        filename=filename,
    )

    signature: UploadedSignature | None = None
    stamp: UploadedSignature | None = None
    signatures = content.all_signatures
    for _signature in signatures:
        signature_type = eusign_utils.guess_type(_signature)
        if signature_type == SignatureType.stamp:
            stamp = UploadedSignature(
                file=file,
                format_=SignatureFormat.internal_wrapped,
                info=_signature,
            )
        else:
            signature = UploadedSignature(
                file=file,
                format_=SignatureFormat.internal_wrapped,
                info=_signature,
            )

    return await prepare_file(original, options, signature=signature, stamp=stamp)


def prepare_signature_source(request_source: Source) -> SignatureSource:
    if request_source == Source.api_internal:
        return SignatureSource.web
    return SignatureSource.api


def _prepare_add_signature_data(
    document: DBRow,
    file: File,
    options: UploadOptions,
    signature_source: SignatureSource,
) -> DataDict:
    """Prepare signature data on document uploading"""

    signature = file.signature
    if not signature:
        return {}

    internal_signature: bytes | None = None
    external_signature: str | None = None
    if signature.format_.is_internal:
        internal_signature = signature.file.body
    elif signature.format_.is_external:
        external_signature = generate_base64_str(signature.file.body)

    recipient_emails = file.recipient_emails
    signature_format = options.signature_format or signature.format_

    return {
        'document_id': document.id,
        'edrpou_recipient': file.edrpou_recipient,
        'emails_recipient': recipient_emails,
        'source': signature_source.value,
        'format': signature_format.value,
        'p7s': internal_signature,
        'key': external_signature,
        'stamp': None,  # Adding external stamp on uploading is not supported
    }


async def send_multilateral_documents_to_recipients_on_upload(
    *,
    files: list[File],
    request_source: Source,
) -> None:
    """Send multilateral documents to recipients on uploading."""

    documents_ids: list[str] = []
    for file in files:
        flows_settings = file.flows_settings
        if flows_settings and flows_settings.should_send:
            documents_ids.append(file.id)

    await send_multilateral_documents_to_recipients_job(
        documents_ids=documents_ids,
        source=request_source,
    )


async def send_upload_async_jobs(
    conn: DBConnection,
    files: list[File],
    options: UploadOptions,
    upload_ctx: InsertedDocumentsCtx,
    request_source: Source,
    user: User | None,
) -> None:
    """Send async jobs to kafka"""
    documents_ids = [doc.id for doc in upload_ctx.documents]

    callback_job_values = []
    for document in upload_ctx.documents:
        if document.source and document.source.is_from_edi:
            callback_job_values.append(
                {
                    'document_id': document.id,
                    'uploaded_by_edrpou': document.edrpou_owner,
                }
            )
    await send_documents_status_callback_jobs(callback_job_values)

    if options.reviewers and user:
        notifications_roles = []
        initiator_role_id = user.role_id

        for reviewer in options.reviewers:
            notifications_roles.append(
                Recipient(
                    reviewer=ReviewerId(
                        role_id=reviewer.role_id,
                        group_id=reviewer.group_id,
                    ),
                    initiator_role_id=initiator_role_id,
                )
            )
            # If parallel review add only first role or roles(if its group) and stop iteration
            if options.parallel_review is False:
                break

        # TODO: need to remove conn from that helper
        await reviews.send_document_review_request_notifications(
            conn=conn,
            initiator_role_id=user.role_id,
            documents_ids=documents_ids,
            request_source=request_source,
            recipients=notifications_roles,
            state_new=None,
        )

    if options.tags and options.user:
        await schedule_create_document_access_on_document_tag_job(
            options.tags,
            documents_ids=documents_ids,
            company_edrpou=options.user.company_edrpou,
            assigner_role_id=options.user.role_id,
            source=request_source,
        )

    await send_multilateral_documents_to_recipients_on_upload(
        files=files,
        request_source=request_source,
    )

    if options.comment and options.comment:
        # Function "comments.handle_add_comment" also creates an async job that sends
        # notifications about new comments to different sets of users.
        # Currently, I skip that logic because that logic should be rewritten to
        # support sending one grouped notification for all new comments.
        # NOTE: try keep in sync logic with parent function "comments.handle_add_comment"

        await comments.send_comments_for_indexation(comments_ids=upload_ctx.comments_ids)

    if not options.template:
        await services.kafka.send_records(
            topic=topics.START_DOCUMENT_AUTOMATION,
            values=[
                {
                    'document_id': file_.id,
                    'company_edrpou': file_.edrpou_owner,
                    'source': request_source,
                }
                for file_ in files
            ],
        )

    await _send_first_notification_to_signers_on_upload(options, files, user)

    await _send_notifications_on_upload_kafka(options, files, request_source)

    await add_document_antivirus_checks(upload_ctx.antivirus_checks)


async def _send_first_notification_to_signers_on_upload(
    options: UploadOptions,
    files: list[File],
    user: User | None,
) -> None:
    """
    Send the first notification to internal signers after uploading a document.
    """

    if not user or not options.signers:
        return

    # When the document has required review, signers will be able to sign
    # the document only after that review is completed.
    if options.is_required_review:
        return

    documents_ids = []
    for file in files:
        if file.is_internal:
            documents_ids.append(file.id)

        elif file.is_multilateral:
            settings = file.flows_settings
            if not settings:
                continue

            if settings.is_ordered:
                # For ordered flows, check if the current company can sign the document right
                # after uploading. If not, the document will be available for internal signers
                # only after the current company becomes the next signer in the flow.
                flows = sorted(settings.flows, key=lambda f: f.order or 0)
                if flows and flows[0].edrpou == user.company_edrpou:
                    documents_ids.append(file.id)
            else:
                documents_ids.append(file.id)

        else:
            # For bilateral we send notification only when "owner" is the first signer.
            # In case when "recipient" is the first signer, the document will be available
            # for internal signers only after the recipient signs the document.
            if file.first_sign_by in (FirstSignBy.owner.value, None):
                documents_ids.append(file.id)

    if documents_ids:
        await schedule_send_first_notification_to_signers_job(
            documents_ids=documents_ids,
            current_company_id=user.company_id,
            current_role_id=user.role_id,
        )


async def save_upload_actions(files: list[File], options: UploadOptions, user: User | None) -> None:
    if user and user.company_id is not None and user.role_id is not None:
        await document_actions.add_document_actions(
            document_actions=[
                document_actions.DocumentAction(
                    action=document_actions.Action.document_upload,
                    document_id=file.id,
                    document_edrpou_owner=file.edrpou_owner,
                    document_title=file.title,
                    company_id=user.company_id,
                    company_edrpou=user.company_edrpou,
                    email=user.email,
                    role_id=user.role_id,
                )
                for file in files
            ]
        )
        if options.template:
            await document_actions.add_document_actions(
                document_actions=[
                    document_actions.DocumentAction(
                        action=document_actions.Action.template_apply_manually,
                        extra={'template_name': options.template.name},
                        document_id=file.id,
                        document_edrpou_owner=file.edrpou_owner,
                        document_title=file.title,
                        email=user.email,
                        role_id=user.role_id,
                        company_edrpou=user.company_edrpou,
                        company_id=user.company_id,
                    )
                    for file in files
                ]
            )


async def upload_files(
    files: list[File],
    options: UploadOptions,
    request_source: Source,
) -> InsertedDocumentsCtx:
    """
    Upload files to S3 and insert their metadata to database.

    TODO: Cleanup documents from S3 if any error happened before committing
          database transaction.
    """

    # Before any database transaction, we upload files to S3 to avoid long transaction time and
    # losing files in case of transaction rollback.
    await upload_files_to_s3(files, options)

    async with services.db.acquire() as conn:
        # Here we insert documents, signatures, and another database object. This is the core
        # part of the upload process and considered as a critical logic that must be executed
        # [WARNING]: don't schedule any async jobs in this function
        upload_ctx = await insert_files_and_signatures(
            conn=conn,
            files=files,
            options=options,
            request_source=request_source,
        )

        # =========================================================
        # After we inserted documents to database, we can now execute all non-critical logic
        # like sending async jobs, sending notifications
        with supress_non_critical_upload_logic():
            await send_upload_async_jobs(
                conn=conn,
                files=files,
                options=options,
                upload_ctx=upload_ctx,
                request_source=request_source,
                user=options.user,
            )

        with supress_non_critical_upload_logic():
            for signature_ctx in upload_ctx.signatures:
                await add_signature_schedule_async_jobs(
                    conn=conn,
                    ctx=signature_ctx,
                    request_source=request_source,
                )

        with supress_non_critical_upload_logic():
            await save_upload_actions(files=files, options=options, user=options.user)

        with supress_non_critical_upload_logic():
            if user := options.user:
                await onboarding_utils.record_user_onboarding(
                    conn=conn,
                    user_id=user.id,
                    has_uploaded_document=True,
                )

    return upload_ctx


async def upload_files_to_s3(
    files: list[File],
    options: UploadOptions,
) -> None:
    """Prepare chunks with files and upload them to S3."""

    logger.info(
        msg='start upload_files_to_s3',
        extra={
            'files_count': len(files),
            'files_size_mb': sum([item.file_size for item in files]) / MB,
        },
    )
    seconds = services.config.app.upload_s3_timeout

    files_to_upload = [
        s3_utils.UploadFile(
            key=item.s3_key,
            body=item.body,
        )
        for item in files
    ]

    try:
        async with asyncio.timeout(seconds):
            await gather_chunks(
                s3_utils.upload,
                files_to_upload,
                constants.S3_CHUNK_SIZE,
            )
    except TimeoutError:
        logger.exception(
            'Timeout error on uploading files to S3',
            extra={
                'number_of_bytes': sum(item.file_size for item in files),
                'number_of_files': len(files),
                'seconds': seconds,
            },
        )
        raise Error(Code.s3_error)


async def add_reviewers_on_uploading(
    conn: DBConnection,
    user: User,
    company_id: str,
    docs_ids: list[str],
    reviewers: list[ReviewsInfoCtx],
    reviewers_source: ReviewRequestSource | None,
    is_required_review: bool,
    is_parallel: bool,
) -> None:
    setting_data: list[DataDict] = []
    if is_required_review or is_parallel:
        for doc_id in docs_ids:
            setting_data.append(
                {
                    'is_required': is_required_review,
                    'is_parallel': is_parallel,
                    'document_id': doc_id,
                    'company_id': company_id,
                }
            )

    if setting_data:
        await insert_review_settings(conn, setting_data)

    for doc_id in docs_ids:
        for idx, reviewer in enumerate(reviewers, start=1):
            data = ReviewRequestData(
                document_id=doc_id,
                document_version_id=None,
                from_role_id=user.role_id,
                to_role_id=reviewer.role_id,
                to_group_id=reviewer.group_id,
                order=None if is_parallel else idx,
                status=ReviewRequestStatus.active,
                source=reviewers_source,
            )

            if reviewer.is_group:
                await reviews.add_group_review_request(
                    conn,
                    user=user,
                    data=data,
                    group_role_ids=reviewer.group_role_ids,
                    is_parallel=is_parallel,
                )

            if reviewer.is_role:
                await reviews.add_review_request(conn, user, data=data, is_parallel=is_parallel)

        await reviews.update_review_statuses_in_db(
            conn=conn,
            documents_ids=[doc_id],
            company_edrpou=user.company_edrpou,
            company_id=company_id,
        )
        # We are sending notification to reviewers after long transaction
        # that insert all info about document


async def add_access_settings_on_uploading(
    conn: DBConnection,
    documents: list[Document],
    options: UploadOptions,
) -> None:
    settings = options.access_settings
    if not settings:
        return

    # For extended access level, we don't do anything because it is the default value
    if settings.level == DocumentAccessLevel.extended:
        return

    # For private access level, we have separated a table to mark documents as private
    await insert_document_access_settings_private(
        conn=conn,
        documents_ids=[document.id for document in documents],
        edrpou=options.company_edrpou,
    )


async def create_flows_on_uploading(
    conn: DBConnection,
    files: list[File],
    documents: list[Document],
    assigner_role_id: str | None,
) -> None:
    documents_map = {document.id: document for document in documents}
    for file in files:
        settings = file.flows_settings
        if not settings:
            continue

        document = documents_map[file.id]
        _options = AddFlowOptions(
            should_send=settings.should_send,
            should_update_document_status=True,
            should_count_signers=True,
            documents=[document],
            receivers=settings.flows,
            assigner_role_id=assigner_role_id,
        )
        await create_flows(conn, _options)
        error_reporting.set_breadcrumb(category='upload', message='flows created')


async def add_comments_on_uploading(
    conn: DBConnection,
    documents: list[Document],
    comment: UploadCommentCtx,
    user: User,
    request_source: Source,
    document_versions_map: dict[str, str],
) -> list[Comment]:
    """
    Add comment for uploaded documents

    Parameters:
     - document_versions_map: document_id -> document_version_id
    """

    _comments = await comments.create_comments_for_documents(
        conn=conn,
        documents_ids=[document.id for document in documents],
        text=comment.text,
        type_=CommentType.comment,
        role_id=user.role_id,
        access_company_id=user.company_id if comment.is_internal else None,
        document_versions_map=document_versions_map,
    )

    await document_actions.add_document_actions(
        document_actions=[
            document_actions.DocumentAction(
                action=document_actions.Action.comment_create,
                document_id=document.id,
                document_edrpou_owner=document.edrpou_owner,
                document_title=document.title,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                email=user.email,
                role_id=user.role_id,
                extra={'message': comment.text},
            )
            for document in documents
        ],
    )

    await comments.open_access_to_mentioned_coworkers_in_comment(
        conn=conn,
        comment_text=comment.text,
        company_id=user.company_id,
        documents_ids=[document.id for document in documents],
    )

    # WARNING: remember to start async actions outside of transaction context,
    # like "send_comment_notification_job" or "send_comment_for_indexation".
    # Otherwise, some part of business logic will be missed.

    return _comments


async def get_recipients_emails_on_uploading(
    conn: DBConnection,
    owner_company_id: str,
    owner_edrpou: str,
    files: list[File],
) -> RecipientEmailsAggregator:
    """
    For files without recipient emails, try to find them in contacts or in recipients table.
    """

    aggregator = RecipientEmailsAggregator()

    recipients_edrpous = [
        item.edrpou_recipient
        for item in files
        if (
            DocumentSource(item.source).is_internal
            and item.edrpou_recipient
            and not item.recipient_emails
        )
    ]

    if not recipients_edrpous:
        return aggregator

    async with log_duration('uploading, selecting contacts'):
        contacts = await select_contact_email_by_edrpou(
            conn=conn,
            edrpous=recipients_edrpous,
            company_id=owner_company_id,
        )

    for contact in contacts:
        aggregator.add(
            edrpou=contact.edrpou,
            email=contact.email,
            is_hidden=contact.is_email_hidden,
        )

    async with log_duration('uploading, selecting recipients'):
        recipients = await select_last_recipient_email(
            conn=conn,
            owner_edrpou=owner_edrpou,
            recipients_edrpous=list(set(recipients_edrpous) - aggregator.edrpous),
        )

    logger.info(
        msg='Selected last recipients emails',
        extra={
            'recipients_count': len(recipients),
            'owner_edrpou': owner_edrpou,
            'recipients_edrpous': recipients_edrpous,
        },
    )

    for recipient in recipients:
        if not recipient.emails:
            continue

        for email in recipient.emails:
            aggregator.add(
                edrpou=recipient.edrpou,
                email=email,
                # `is_hidden=False` because function
                # `select_last_recipient_email` doesn't select recipients
                # with hidden email.
                is_hidden=False,
            )

    return aggregator


def to_document_dict(
    item: File,
    options: UploadOptions,
    recipient_emails: list[str] | None,
) -> dict[str, Any]:
    """
    Create dict with document data for inserting into "documents" table.
    """

    data = item.to_document_data()

    if options.title:
        data['title'] = options.title

    if item.recipient_emails:
        # File uploaded with recipient emails in parameters or in parsed metadata
        data['email_recipient'] = join_comma_separated_emails(item.recipient_emails)
    elif options.allow_substitute_email_recipient and recipient_emails:
        # When a file uploaded without recipient emails, we might try to find in contacts
        # or recipient table. Argument "recipient_emails" represents emails that we found
        # previously
        data['email_recipient'] = join_comma_separated_emails(recipient_emails)
    else:
        data['email_recipient'] = None

    # Move document to next status if edrpou & email recipient filled in
    # or if it is internal document
    if (data['edrpou_recipient'] and data['email_recipient']) or data['is_internal']:
        data['status_id'] = DocumentStatus.ready_to_be_signed.value

    user = options.user

    # Log attempt to upload document with other EDRPOU
    edrpou = options.company_edrpou
    if item.edrpou_owner != edrpou and options.replace_owner_edrpou:
        logger.info(
            'EDRPOU owner from file name overwritten with current user EDRPOU',
            extra={'file_edrpou': item.edrpou_owner, 'user_edrpou': edrpou},
        )
        data['edrpou_owner'] = edrpou

    # Prepend user data
    data['role_id'] = user and user.role_id
    data['user_id'] = user and user.id
    return data


async def __insert_files(  # noqa: C901
    conn: DBConnection,
    files: list[File],
    options: UploadOptions,
    recipients: RecipientEmailsAggregator,
    request_source: Source,
) -> InsertedDocumentsCtx:
    """
    Insert list of files to database.

    This function should only have operations which are mission-critical
    for document upload process.
    If anything goes wrong, inserted documents would be reverted by transaction.
    """

    # Object in which we store all inserted documents and their related entities
    # (versions, comments, etc.) for further processing outside of transaction context
    inserted_ctx = InsertedDocumentsCtx()

    # ============================
    # Collecting data for inserting
    #
    # Try to avoid making DB queries here because this function
    # is already inside the transaction context, which can might put additional load on DB.
    # ============================

    documents_data = []
    listing_aggregator = ListingDataAggregator()
    ipn_data: dict[str, str] = {}  # edrpou: ipn
    document_signers_data = []
    document_recipients_data: list[UpsertDocumentRecipientDict] = []
    links = []
    versions = []
    documents_meta: list[DataDict] = []
    contacts: list[ContactBase] = []

    group_access_data = []
    for item in files:
        edrpou_recipient = item.edrpou_recipient
        recipient_emails: list[str] | None = None
        if edrpou_recipient:
            recipient_emails = item.recipient_emails or None
            is_emails_hidden = item.hide_recipient_emails or False

            # If document uploader did not provide recipient email, then
            # try to find recipient email from contacts
            if edrpou_recipient in recipients and not recipient_emails:
                recipient = recipients[edrpou_recipient]
                recipient_emails = recipient.emails
                is_emails_hidden = recipient.is_hidden

            document_recipients_data.append(
                UpsertDocumentRecipientDict(
                    document_id=item.id,
                    edrpou=edrpou_recipient,
                    emails=recipient_emails,
                    is_emails_hidden=is_emails_hidden,
                )
            )

        # Use validated owner EDRPOU (replaced for current company if needed)
        # for document/listing/ipn
        valid_item = to_document_dict(item, options, recipient_emails)
        documents_data.append(valid_item)

        listing_aggregator.add(
            document_id=item.id,
            access_edrpou=valid_item['edrpou_owner'],
            role_id=valid_item['role_id'],
            source=AccessSource.default,
        )
        for to_role_id in options.share_to:
            listing_aggregator.add(
                document_id=item.id,
                access_edrpou=valid_item['edrpou_owner'],
                role_id=to_role_id,
                source=AccessSource.viewer,
            )
        for group in options.share_to_groups:
            for role_id in group.role_ids:
                listing_aggregator.add(
                    document_id=item.id,
                    access_edrpou=valid_item['edrpou_owner'],
                    role_id=role_id,
                    source=AccessSource.group_viewer,
                )

        if options.share_to_groups:
            if not options.user:
                logger.warning('User is not provided for group sharing')
            else:
                group_access_data.extend(
                    [
                        {
                            'document_id': item.id,
                            'group_id': group.id,
                            'created_by': options.user.role_id,
                            'company_id': options.user.company_id,
                        }
                        for group in options.share_to_groups
                    ]
                )

        signers_source = get_signers_source(request_source)

        if options.user:
            for idx, sign_ctx in enumerate(options.signers, start=1):
                sign_data = {
                    'document_id': item.id,
                    'company_id': options.user.company_id,
                    'order': None if options.parallel_signing else idx,
                    'assigner': options.user.role_id,
                    'group_id': None,
                    'role_id': None,
                    'source': signers_source,
                }

                if sign_ctx.is_role:
                    sign_data = {**sign_data, 'role_id': sign_ctx.role_id}
                    listing_aggregator.add(
                        document_id=item.id,
                        access_edrpou=valid_item['edrpou_owner'],
                        role_id=sign_ctx.role_id,
                        source=AccessSource.signer,
                    )
                elif sign_ctx.is_group:
                    sign_data = {**sign_data, 'group_id': sign_ctx.group_id}
                    # open access for all group members
                    for role_id in sign_ctx.group_role_ids:
                        listing_aggregator.add(
                            document_id=item.id,
                            access_edrpou=valid_item['edrpou_owner'],
                            role_id=role_id,
                            source=AccessSource.signer,
                        )
                else:
                    raise NotImplementedError(f'Unknown entity type: {sign_ctx.entity}')

                document_signers_data.append(sign_data)

        # We might update IPN for owner and recipient companies if they don't have IPN.
        # NOTE: pay attention that valid_item['edrpou_owner'] might be changed in
        # "to_document_dict" in some edge cases. If it is changed, we just skip IPN update to
        # not set the wrong IPN for the company by mistake.
        if item.edrpou_owner == valid_item['edrpou_owner']:
            if item.edrpou_owner and item.ipn_owner:
                ipn_data[item.edrpou_owner] = item.ipn_owner
            if item.edrpou_recipient and item.ipn_recipient:
                ipn_data[item.edrpou_recipient] = item.ipn_recipient

        if options.parent_id:
            links.append(
                {
                    'company_id': options.user and options.user.company_id,
                    'company_edrpou': options.company_edrpou,
                    'parent_id': options.parent_id,
                    'child_id': item.id,
                }
            )

        if options.is_versioned and options.user:
            version_id = item.version_id
            assert version_id is not None

            params = InsertDocumentVersionParameters.prepare_upload_version(
                version_id=version_id,
                document_id=item.id,
                role_id=options.user.role_id,
                company_edrpou=options.user.company_edrpou,
                content_hash=item.content_hash,
                content_length=item.file_size,
                extension=item.extension,
            )
            versions.append(params)

        documents_meta.append(
            {
                'document_id': item.id,
                'content_hash': item.content_hash,
                'content_length': item.file_size,
            }
        )

        # Add recipient to a contact list for the current user on an uploading
        company_name_recipient = item.company_name_recipient
        edrpou_recipient = item.edrpou_recipient
        if company_name_recipient and edrpou_recipient and options.user:
            contacts.append(
                ContactBase(
                    company_id=options.user.company_id,
                    edrpou=edrpou_recipient,
                    name=company_name_recipient,
                    short_name=item.company_short_name_recipient,
                )
            )

    error_reporting.set_breadcrumb(
        category='upload',
        message='documents data prepared',
        data={
            'documents_count': len(documents_data),
            'listings_count': len(listing_aggregator),
            'group_access_count': len(group_access_data),
            'recipients_count': len(document_recipients_data),
            'ipn_count': len(ipn_data),
            'contacts_count': len(contacts),
            'links_count': len(links),
            'versions_count': len(versions),
            'tags_count': len(options.tags),
            'new_tags_count': len(options.new_tags),
            'reviewers_count': len(options.reviewers),
            'parameters_count': len(options.parameters or []),
        },
    )

    # ===========================
    # Start inserting objects collected by logic above
    #
    # Avoid for loop with DB queries, because the company might upload up to 500 files
    # ===========================

    documents = await insert_documents(
        conn=conn,
        data=documents_data,
    )
    error_reporting.set_breadcrumb(category='upload', message='documents inserted')

    inserted_ctx.add_documents(documents)

    await insert_listings(conn, listing_aggregator.as_db())
    error_reporting.set_breadcrumb(category='upload', message='listings inserted')

    if group_access_data:
        await insert_group_document_access(
            conn=conn,
            values_list=group_access_data,
        )
        error_reporting.set_breadcrumb(category='upload', message='group access inserted')

    if document_recipients_data:
        await insert_recipients(conn, recipients=document_recipients_data)
        error_reporting.set_breadcrumb(category='upload', message='recipients inserted')

    if document_signers_data:
        await insert_document_signers(conn, data=document_signers_data)
        error_reporting.set_breadcrumb(category='upload', message='signers inserted')

    if options.payer_id:
        await decrement_company_upload_documents_left(conn, options.payer_id, len(documents_data))
        error_reporting.set_breadcrumb(category='upload', message='decrement documents left')

    if ipn_data:
        await update_companies_ipn(conn, data=ipn_data)
        error_reporting.set_breadcrumb(category='upload', message='ipn data updated')

    if contacts:
        _contacts = await upsert_contacts(conn, contacts)
        inserted_ctx.add_contacts(_contacts)
        error_reporting.set_breadcrumb(category='upload', message='contacts inserted')

    if links:
        await add_documents_links(conn, links)
        error_reporting.set_breadcrumb(category='upload', message='links inserted')

    if versions:
        _version = await insert_document_versions_batch(conn=conn, params=versions)
        inserted_ctx.add_versions(_version)
        error_reporting.set_breadcrumb(category='upload', message='versions inserted')

    documents_ids = [document.id for document in documents]
    if (options.tags or options.new_tags) and options.user:
        await add_tags(
            conn=conn,
            company_id=options.user.company_id,
            company_edrpou=options.user.company_edrpou,
            documents_ids=documents_ids,
            tags_ids=options.tags,
            new_tags_names=options.new_tags,
            assigner_role_id=options.user.role_id,
        )
        error_reporting.set_breadcrumb(category='upload', message='tags inserted')

    if options.reviewers and options.user:
        await add_reviewers_on_uploading(
            conn=conn,
            user=options.user,
            company_id=options.user.company_id,
            docs_ids=documents_ids,
            reviewers=options.reviewers,
            reviewers_source=reviews.get_reviewers_source(request_source),
            is_required_review=options.is_required_review,
            is_parallel=options.parallel_review,
        )
        error_reporting.set_breadcrumb(category='upload', message='reviewers inserted')

    if options.parameters is not None and options.user:
        await create_document_parameters_on_upload(
            conn=conn,
            documents_ids=documents_ids,
            company_id=options.user.company_id,
            role_id=options.user.role_id,
            parameters=options.parameters,
        )
        error_reporting.set_breadcrumb(category='upload', message='parameters inserted')

    await create_flows_on_uploading(
        conn=conn,
        files=files,
        documents=documents,
        assigner_role_id=options.user.role_id if options.user else None,
    )

    if options.comment and options.user:
        _comments = await add_comments_on_uploading(
            conn=conn,
            documents=documents,
            comment=options.comment,
            user=options.user,
            request_source=request_source,
            document_versions_map=inserted_ctx.document_versions_map,
        )
        inserted_ctx.add_comments(_comments)
        error_reporting.set_breadcrumb(category='upload', message='comments inserted')

    inserted_ctx.add_antivirus_checks()
    await insert_document_antivirus_checks(conn=conn, checks=inserted_ctx.antivirus_checks)
    error_reporting.set_breadcrumb(category='upload', message='antivirus checks inserted')

    if documents_meta:
        await add_documents_meta(conn, data=documents_meta)
        error_reporting.set_breadcrumb(category='upload', message='documents meta inserted')

    if options.access_settings:
        await add_access_settings_on_uploading(conn, documents=documents, options=options)
        error_reporting.set_breadcrumb(category='upload', message='access settings inserted')

    if options.source == DocumentSource.archive_histored.value:
        assert options.user, 'User is expected here'
        user: User = options.user
        await archive_documents(
            conn=conn,
            # TODO: Potentially, add support for directories when uploading histored documents
            # in the future.
            ctx=ArchiveDocumentsCtx(documents=documents, directory=None),
            user=user,
        )

    return inserted_ctx


@contextmanager
def supress_non_critical_upload_logic() -> Iterator[None]:
    """
    Ignore non-mission-critical functionality on upload

    See https://vchasno-group.atlassian.net/browse/DOC-7113
    """

    try:
        yield
    except Exception as e:
        logger.info(
            msg='Non-critical error during documents upload',
            extra={'error_message': str(e)},
        )


async def insert_files_and_signatures(
    conn: DBConnection,
    files: list[File],
    options: UploadOptions,
    request_source: Source,
) -> InsertedDocumentsCtx:
    """
    Select additional context that we haven't selected during validation and insert all database
    objects related to uploaded files

    Async jobs and non-critical logic executed in parent function
    """
    user = options.user

    # Find recipients from contacts, if allowed by company config
    recipients = RecipientEmailsAggregator()
    if options.allow_substitute_email_recipient and user:
        recipients = await get_recipients_emails_on_uploading(
            conn=conn,
            owner_company_id=user.company_id,
            owner_edrpou=user.company_edrpou,
            files=files,
        )

    if options.source == DocumentSource.hrs.value and user and not recipients:
        recipients = await find_recipient_emails(
            conn=conn,
            recipients_edrpous=[
                _file.edrpou_recipient
                for _file in files
                if _file.edrpou_recipient and not _file.recipient_emails
            ],
            user=user,
        )

    # WARNING: this following function is a single big transaction that inserts all objects to
    # the database:
    #  - to select objects from a database, do it before that function call.
    #  - to schedule async jobs do it in parent function after that function call.
    return await __insert_files_and_signatures(
        conn=conn,
        files=files,
        options=options,
        recipients=recipients,
        request_source=request_source,
    )


async def __insert_files_and_signatures(
    conn: DBConnection,
    files: list[File],
    options: UploadOptions,
    recipients: RecipientEmailsAggregator,
    request_source: Source,
) -> InsertedDocumentsCtx:
    """
    Avoid direct calls - adding signatures has to trigger async jobs.
    Call `insert_files_and_signatures` instead.

    Open transaction and execute mission-critical methods for upload

    - Prepare and insert documents to database
    - Upload and insert signatures to a database
    """
    async with conn.begin():
        result = await __insert_files(
            conn=conn,
            files=files,
            options=options,
            recipients=recipients,
            request_source=request_source,
        )

        signature_source = prepare_signature_source(request_source)
        documents_map = {d.document_id: d.document for d in result.docs}

        # TODO: find a way to do it in one query without loop because company can upload
        #   up to 500 files, which might be slow to process in loop
        for file in files:
            if not file.signature:
                continue

            signature_ctx = await add_signature(
                conn=conn,
                user=options.user,
                data=_prepare_add_signature_data(
                    document=documents_map[file.id],  # type: ignore[arg-type]
                    file=file,
                    options=options,
                    signature_source=signature_source,
                ),
            )
            result.signatures.append(signature_ctx)

    return result
