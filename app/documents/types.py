from __future__ import annotations

import typing as t
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import auto
from functools import cached_property
from types import EllipsisType
from typing import Literal

from app.auth.types import AuthUser, Role, User
from app.billing.types import ChargeDocumentContext
from app.document_versions.types import DocumentVersion
from app.documents.enums import (
    AccessSource,
    DocumentAccessLevel,
    DocumentSource,
    FirstSignBy,
    RecipientRole,
    UpdateTagsAction,
    UpdateViewersAction,
)
from app.documents_fields.types import UpdateDocumentParametersCtx
from app.flow.types import CreateFlowCtx, FlowItem, FlowMeta
from app.lib.database import DBRow
from app.lib.enums import DocumentStatus, NamedEnum, SignatureFormat, SignersSource, Source
from app.lib.helpers import is_valid_edrpou
from app.lib.types import DataDict
from app.reviews.types import ReplaceReviewRequestsCtx

if t.TYPE_CHECKING:
    from app.documents.validators import RejectDocumentSchema
    from app.flow.utils import FlowsState
    from app.signatures.types import DocumentSigner, DocumentSignerWithEdrpou, Signature

ListingIndexTupleType = tuple[str, str, str | None]
AcceptDeleteReqData = dict[t.Literal['delete_request_ids'] | t.Literal['documents_ids'], list[str]]


class ListingDataAggregator(dict[ListingIndexTupleType, AccessSource]):
    """
    Dict that collect and combine access sources for listing items:
    (document_id, access_edrpou, role_id) -> AccessSource
    """

    def add(
        self,
        *,
        document_id: str,
        access_edrpou: str,
        role_id: str | None,
        source: AccessSource,
    ) -> None:
        # Check only for EDRPOU, because role_id and document_id should be validated
        # by the database as UUID + foreign keys
        if not is_valid_edrpou(access_edrpou):
            raise ValueError(f'Invalid EDRPOU: {access_edrpou}')

        key = (document_id, access_edrpou, role_id)
        # Combine access sources
        value = self.get(key, AccessSource._empty) | source
        self[key] = value

    def as_db(self) -> list[DataDict]:
        return [
            {
                'document_id': key[0],
                'access_edrpou': key[1],
                'role_id': key[2],
                'sources': value,
            }
            for key, value in self.items()
        ]

    def merge(self, other: ListingDataAggregator) -> None:
        """
        Append another aggregator to self by combining access sources
        """
        for key, source in other.items():
            self.add(
                document_id=key[0],
                access_edrpou=key[1],
                role_id=key[2],
                source=source,
            )


class RecipientsEmailsOptions(t.NamedTuple):
    edrpou: str
    emails: list[str]
    is_emails_hidden: bool = False

    def as_dict(self) -> DataDict:
        return {
            'edrpou': self.edrpou,
            'emails': self.emails,
            'is_emails_hidden': self.is_emails_hidden,
        }


@dataclass
class UpdateInfoData:
    """
    Basic document information for update.

    Think about it as a some metadata that has no effect on the signing or reviewing process.
    If you want to update reviews, signers, recipients, versions, tags, etc. you should use
    corresponding data classes, like UpdateSignersData, UpdateRecipientsExtendedCtx, etc.

    WARNING: None values overwrite existing values in the database
    """

    category: int | None
    date: datetime | None
    number: str | None
    title: str
    amount: int | None


class UpdateSignersDataSignerType(NamedEnum):
    role = auto()
    group = auto()


class UpdateSignersDataSignerEntity(t.NamedTuple):
    type: UpdateSignersDataSignerType
    id: str
    role_ids: tuple[str, ...] | None = None  # used for group and use tuple to be hashable


class UpdateSignersData(t.NamedTuple):
    signer_entities: list[UpdateSignersDataSignerEntity]
    is_parallel: bool
    signers_source: SignersSource

    @property
    def roles_ids(self) -> list[str]:
        """
        Flatten roles from signer_entities.
        """
        res = []
        for signer in self.signer_entities:
            if signer.type == UpdateSignersDataSignerType.role:
                res.append(signer.id)
            else:
                if signer.role_ids is not None:
                    res.extend(signer.role_ids)
                else:
                    raise ValueError('UpdateSignersData: role_ids should be set for group')

        return res


class UpdateViewersEntityType(NamedEnum):
    role = auto()
    group = auto()


class UpdateViewersEntity(t.NamedTuple):
    type: UpdateViewersEntityType
    id: str
    role_ids: tuple[str, ...] | None = None  # used for group and use tuple to be hashable


class UpdateViewersData(t.NamedTuple):
    entities: list[UpdateViewersEntity]
    action: UpdateViewersAction

    @property
    def viewer_role_ids(self) -> list[str]:
        return [
            entity.id for entity in self.entities if entity.type == UpdateViewersEntityType.role
        ]

    @property
    def group_ids(self) -> list[str]:
        return [
            entity.id for entity in self.entities if entity.type == UpdateViewersEntityType.group
        ]

    @property
    def group_viewer_role_ids(self) -> list[str]:
        res: list[str] = []
        for entity in self.entities:
            if entity.type == UpdateViewersEntityType.group and entity.role_ids is not None:
                res.extend(entity.role_ids)
        return res


@dataclass
class UpdateTagsSettings:
    tags_ids: list[str]
    new_tags: list[str]
    action: UpdateTagsAction


class UpdateVersionedCtx(t.NamedTuple):
    is_versioned: bool


@dataclass(frozen=True)
class UpdateDocumentAccessSettingsCtx:
    prev_level: DocumentAccessLevel
    new_level: DocumentAccessLevel


@dataclass(frozen=True)
class UploadDocumentAccessSettingsCtx:
    level: DocumentAccessLevel


class UpdateDocumentOptions(t.NamedTuple):
    document: DocumentWithUploader
    user: User

    recipients_settings: UpdateRecipientsExtendedCtx | None
    document_settings: UpdateInfoData | None
    access_settings: UpdateDocumentAccessSettingsCtx | None
    parameters_settings: UpdateDocumentParametersCtx | None
    signers_settings: UpdateSignersData | None
    reviews_settings: ReplaceReviewRequestsCtx | None
    viewers_settings: UpdateViewersData | None
    tags_settings: UpdateTagsSettings | None
    version_settings: UpdateVersionedCtx | None

    @property
    def is_document_owner(self) -> bool:
        """Is document updated by owner?"""
        return self.document.edrpou_owner == self.user.company_edrpou


class OpenAccessOptions(t.NamedTuple):
    document: Document
    user: User
    data: list[DataDict]
    roles: list[Role]
    comment: str | None = None


class SendDocumentOptions(t.NamedTuple):
    document: DocumentWithUploader
    next_status: DocumentStatus
    uploader_company_id: str
    recipient: RecipientsEmailsOptions | None
    is_owner_sending: bool

    is_one_sign: bool
    is_3p: bool
    is_sending_to_recipient: bool
    is_internal_bill_sending: bool

    # EDRPOU of sender company
    company_edrpou: str

    charge_context: ChargeDocumentContext | None

    @property
    def sender_edrpou(self) -> str:
        return self.company_edrpou


@dataclass
class RecipientAggregatedData:
    edrpou: str
    emails: list[str]
    is_hidden: bool

    def as_dict(self) -> DataDict:
        return {
            'edrpou': self.edrpou,
            'emails': self.emails,
            'is_emails_hidden': self.is_hidden,
        }


class FindRecipientEmailsOptions(t.NamedTuple):
    edrpous: list[str]
    recipients: list[RecipientsEmailsOptions]

    def recipients_data(self) -> list[DataDict]:
        return [value.as_dict() for value in self.recipients]


class RecipientEmailsAggregator:
    def __init__(self) -> None:
        self._mapping: dict[str, RecipientAggregatedData] = {}

    def add(
        self,
        *,
        edrpou: str,
        email: str | None,
        is_hidden: bool,
    ) -> None:
        if email is None:
            return

        if edrpou not in self._mapping:
            self._mapping[edrpou] = RecipientAggregatedData(
                edrpou=edrpou,
                emails=[email],
                is_hidden=is_hidden,
            )

        else:
            # Append new emails to existing
            recipient: RecipientAggregatedData = self._mapping[edrpou]
            recipient.is_hidden = is_hidden
            if email not in recipient.emails:
                recipient.emails = [*recipient.emails, email]

    @property
    def edrpous(self) -> set[str]:
        return set(self._mapping.keys())

    @property
    def recipients(self) -> list[RecipientAggregatedData]:
        return list(self._mapping.values())

    def recipients_data(self) -> list[DataDict]:
        return [value.as_dict() for value in self.recipients]

    def __getitem__(self, item: str) -> RecipientAggregatedData:
        return self._mapping[item]

    def __contains__(self, item: str) -> bool:
        return item in self._mapping

    def __bool__(self) -> bool:
        return bool(self._mapping)


@dataclass
class DocumentRecipient:
    """Document recipient model as it is stored in the database"""

    id: str
    edrpou: str
    emails: list[str]
    is_emails_hidden: bool
    document_id: str
    from_flow: bool
    external_meta: DataDict | None
    date_sent: datetime | None
    date_received: datetime | None
    date_delivered: datetime | None
    date_created: datetime
    assigner_role_id: str | None

    @staticmethod
    def from_row(row: DBRow) -> DocumentRecipient:
        return DocumentRecipient(
            id=row['id'],
            edrpou=row['edrpou'],
            emails=row['emails'] or [],
            is_emails_hidden=row['is_emails_hidden'],
            document_id=row['document_id'],
            from_flow=row['from_flow'],
            date_sent=row['date_sent'],
            date_received=row['date_received'],
            date_delivered=row['date_delivered'],
            external_meta=row['external_meta'],
            date_created=row['date_created'],
            assigner_role_id=row['assigner_role_id'],
        )

    def as_db(self) -> UpsertDocumentRecipientDict:
        return UpsertDocumentRecipientDict(
            id=self.id,
            edrpou=self.edrpou,
            emails=self.emails,
            is_emails_hidden=self.is_emails_hidden,
            document_id=self.document_id,
            from_flow=self.from_flow,
            external_meta=self.external_meta,
            date_sent=self.date_sent,
            date_received=self.date_received,
            date_delivered=self.date_delivered,
            date_created=self.date_created,
            assigner_role_id=self.assigner_role_id,
        )

    @property
    def is_sent(self) -> bool:
        return bool(self.date_sent)

    def is_equal_by(
        self,
        *,
        edrpou: str | EllipsisType = ...,
        emails: list[str] | EllipsisType = ...,
    ) -> bool:
        """
        Compare recipient by some fields and return True if all fields are equal
        """
        parts = []
        if edrpou is not ...:
            parts.append(self.edrpou == edrpou)

        if emails is not ...:
            email_1 = {email.lower() for email in self.emails}
            email_2 = {email.lower() for email in emails}
            parts.append(email_1 == email_2)

        return all(parts)

    def has_email(self, email: str | None) -> bool:
        """
        Check if the recipient has email
        """
        if email is None:
            return False
        return any(e.lower() == email.lower() for e in self.emails)


class UpsertDocumentRecipientDict(t.TypedDict):
    edrpou: str
    emails: list[str] | None
    document_id: str

    id: t.NotRequired[str]
    is_emails_hidden: t.NotRequired[bool]
    from_flow: t.NotRequired[bool]
    external_meta: t.NotRequired[DataDict | None]
    date_sent: t.NotRequired[datetime | None]
    date_received: t.NotRequired[datetime | None]
    date_delivered: t.NotRequired[datetime | None]
    date_created: t.NotRequired[datetime]
    assigner_role_id: t.NotRequired[str | None]


@dataclass
class DocumentRecipientWithFlow(DocumentRecipient):
    """
    Document recipient with attached flow item if it exists
    """

    # ... DocumentRecipient fields

    flow: FlowItem | None

    @property
    def sort_key(self) -> tuple[int, str, str]:
        """
        Return key for sorting recipients for updating recipients. The "order" is the first to
        preserve flows that can be signed first
        """
        order: int = 0
        flow_id: str = ''
        recipient_id: str = self.id
        if flow := self.flow:
            order = flow.order or 0
            flow_id = flow.id

        return order, flow_id, recipient_id

    @classmethod
    def from_objects(
        cls,
        recipient: DocumentRecipient,
        flow: FlowItem | None,
    ) -> DocumentRecipientWithFlow:
        return cls(
            id=recipient.id,
            edrpou=recipient.edrpou,
            emails=recipient.emails,
            is_emails_hidden=recipient.is_emails_hidden,
            document_id=recipient.document_id,
            from_flow=recipient.from_flow,
            date_sent=recipient.date_sent,
            date_received=recipient.date_received,
            date_delivered=recipient.date_delivered,
            external_meta=recipient.external_meta,
            date_created=recipient.date_created,
            assigner_role_id=recipient.assigner_role_id,
            flow=flow,
        )


class ListingItem(t.NamedTuple):
    edrpou: str
    role_id: str | None
    is_first: bool
    date_created: datetime


class RecipientItem(t.NamedTuple):
    id: str
    name: str | None
    edrpou: str
    email: str | None
    is_email_hidden: bool
    role_id: str | None


@dataclass(frozen=True)
class DeleteDocumentChildCtx:
    parent_id: str
    child_id: str
    company_edrpou: str


@dataclass(frozen=True)
class AddDocumentChildCtx(DeleteDocumentChildCtx):
    link: DBRow | None

    @property
    def link_exists(self) -> bool:
        return self.link is not None


@dataclass(frozen=True)
class AddDocumentChildrenCtx:
    parent_id: str
    children_ids: list[str]
    company_edrpou: str
    old_links_mapping: defaultdict[str, set[str]]


@dataclass
class DocumentMeta:
    document_id: str
    content_hash: str
    content_length: int

    def as_db(self) -> DataDict:
        return asdict(self)

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            document_id=row['document_id'],
            content_hash=row['content_hash'],
            content_length=row['content_length'],
        )


@dataclass(frozen=True)
class DocumentForEmailBase:
    id: str
    user_id: str | None
    edrpou_owner: str
    first_sign_by: FirstSignBy
    title: str
    expected_owner_signatures: int
    expected_recipient_signatures: int
    edrpou_recipient: str | None
    source: DocumentSource
    is_multilateral: bool
    # from companies table
    sender_company_edrpou: str
    sender_company_name: str
    # from users table
    owner_first_name: str

    # TODO: avoid using DBRow
    _row: DBRow


@dataclass(frozen=True)
class DocumentForRecipientEmail(DocumentForEmailBase):
    id: str
    user_id: str | None
    edrpou_owner: str
    first_sign_by: FirstSignBy
    title: str
    expected_owner_signatures: int
    expected_recipient_signatures: int
    edrpou_recipient: str | None
    source: DocumentSource
    # from companies table
    sender_company_edrpou: str
    sender_company_name: str
    # from users table
    sender_email: str
    sender_phone: str
    owner_first_name: str

    @staticmethod
    def from_row(row: DBRow) -> DocumentForRecipientEmail:
        return DocumentForRecipientEmail(
            id=row.id,
            user_id=row.user_id,
            edrpou_owner=row.edrpou_owner,
            first_sign_by=row.first_sign_by,
            title=row.doc_title,
            expected_owner_signatures=row.expected_owner_signatures,
            expected_recipient_signatures=row.expected_recipient_signatures,
            edrpou_recipient=row.edrpou_recipient,
            source=row.source,
            is_multilateral=row.is_multilateral,
            sender_company_edrpou=row.company_edrpou,
            sender_company_name=row.company_name,
            sender_email=row.email,
            sender_phone=row.phone,
            owner_first_name=row.first_name,
            _row=row,
        )


@dataclass(frozen=True)
class DocumentForOwnerEmail(DocumentForEmailBase):
    sender_email: str

    @staticmethod
    def from_row(row: DBRow) -> DocumentForOwnerEmail:
        return DocumentForOwnerEmail(
            id=row.id,
            user_id=row.user_id,
            edrpou_owner=row.edrpou_owner,
            first_sign_by=row.first_sign_by,
            title=row.doc_title,
            expected_owner_signatures=row.expected_owner_signatures,
            expected_recipient_signatures=row.expected_recipient_signatures,
            edrpou_recipient=row.edrpou_recipient,
            source=row.source,
            is_multilateral=row.is_multilateral,
            sender_company_edrpou=row.company_edrpou,
            sender_company_name=row.company_name,
            sender_email=row.email,
            owner_first_name=row.first_name,
            _row=row,
        )


@dataclass
class DocumentOwnerForZakupkiEmail:
    email: str | None
    first_name: str | None
    company_name: str | None
    company_edrpou: str | None

    @staticmethod
    def from_row(row: DBRow) -> DocumentOwnerForZakupkiEmail:
        return DocumentOwnerForZakupkiEmail(
            email=row.email,
            first_name=row.first_name,
            company_name=row.name,
            company_edrpou=row.edrpou,
        )


@dataclass
class Document:
    """Dataclass for row in documents table"""

    id: str
    edrpou_owner: str
    title: str
    status_id: int  # DocumentStatus enum
    source: DocumentSource
    first_sign_by: FirstSignBy
    signature_format: SignatureFormat
    date_created: datetime
    date_updated: datetime

    # Optional fields
    role_id: str | None  # ID of uploader role
    uploaded_by: str | None
    edrpou_recipient: str | None
    # # note: email can be comma separated email list of emails
    email_recipient: str | None
    # Extension of the document mostly in lowercase with a leading dot. Example ".pdf", ".docx".
    # INFO (October 2024): there few corner cases that you should be aware of:
    #  - ".xml.xz" — 850k documents
    #  - ".xz" — 1 document
    #  - ".ua" — 2 documents
    #  - ".PDF" - 50k documents
    #  - ".PDF.xz" — 50k documents
    #  - ".p7s" — 10 documents
    #  - "" (empty string) — 6 documents
    #  - None — 0 documents
    extension: str | None
    archive_name: str | None
    date_document: datetime | None
    type: str | None
    number: str | None
    amount: int | None
    vendor: str | None
    vendor_id: str | None
    is_internal: bool
    is_multilateral: bool
    signatures_to_finish: int  # DEFAULT_SIGNATURES_TO_FINISH
    expected_owner_signatures: int  # DEFAULT_EXPECTED_OWNER_SIGNATURES
    expected_recipient_signatures: int  # DEFAULT_EXPECTED_RECIPIENT_SIGNATURES
    category: int | None  # PublicDocumentCategory enum
    s3_archive_key: str | None
    s3_xml_to_pdf_key: str | None
    has_changed_for_public_api: bool
    date_delivered: datetime | None
    date_finished: datetime | None
    user_id: str | None
    is_invalid_signed: bool | None
    # to keep backward compatibility with old code, try to avoid using it
    _row: DBRow

    @property
    def is_bilateral(self) -> bool:
        """
        Only three states are possible: bilateral, multilateral, internal
        """
        return not self.is_multilateral and not self.is_internal

    def as_db(self) -> DataDict:
        result = asdict(self)
        result.pop('_row', None)
        return result

    @staticmethod
    def from_row(row: DBRow) -> Document:
        return Document(
            id=row.id,
            role_id=row.role_id,
            uploaded_by=row.uploaded_by,  # ID of uploader role
            edrpou_owner=row.edrpou_owner,
            edrpou_recipient=row.edrpou_recipient,
            email_recipient=row.email_recipient,
            title=row.title,
            extension=row.extension,
            archive_name=row.archive_name,
            status_id=row.status_id,
            date_document=row.date_document,
            type=row.type,
            number=row.number,
            amount=row.amount,
            source=row.source,
            vendor=row.vendor,
            vendor_id=row.vendor_id,
            first_sign_by=row.first_sign_by,
            is_internal=row.is_internal,
            is_multilateral=row.is_multilateral,
            signatures_to_finish=row.signatures_to_finish,
            expected_owner_signatures=row.expected_owner_signatures,
            expected_recipient_signatures=row.expected_recipient_signatures,
            signature_format=row.signature_format,
            category=row.category,
            s3_archive_key=row.s3_archive_key,
            s3_xml_to_pdf_key=row.s3_xml_to_pdf_key,
            has_changed_for_public_api=row.has_changed_for_public_api,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_delivered=row.date_delivered,
            date_finished=row.date_finished,
            user_id=row.user_id,
            is_invalid_signed=row.is_invalid_signed,
            _row=row,
        )

    @property
    def status(self) -> DocumentStatus:
        return DocumentStatus(self.status_id)

    @property
    def file_name(self) -> str:
        return self.title + (self.extension or '')


@dataclass
class DocumentWithUploader(Document):
    """
    Document with additional information about uploader

    It is the return value of `validate_document_exists` function that is quite frequently used
    in our codebase. You can convert DBRow to this dataclass and pass that object to functions
    that require either DocumentWithUploader or Document (due to inheritance).

    ```python
    def some_function(document: Document):
         pass

    document = DocumentWithUploader.from_row(row)

    # It OK to pass DocumentWithUploader to function that requires Document
    some_function(document)
    ```


    """

    # ... Document fields
    uploaded_by_company_id: str
    uploaded_by_edrpou: str
    uploaded_by_name: str | None
    uploaded_by_is_legal: bool

    @staticmethod
    def from_row(row: DBRow) -> DocumentWithUploader:
        return DocumentWithUploader(
            id=row.id,
            role_id=row.role_id,
            uploaded_by=row.uploaded_by,  # ID of uploader role
            edrpou_owner=row.edrpou_owner,
            edrpou_recipient=row.edrpou_recipient,
            email_recipient=row.email_recipient,
            title=row.title,
            extension=row.extension,
            archive_name=row.archive_name,
            status_id=row.status_id,
            date_document=row.date_document,
            type=row.type,
            number=row.number,
            amount=row.amount,
            source=row.source,
            vendor=row.vendor,
            vendor_id=row.vendor_id,
            first_sign_by=row.first_sign_by,
            is_internal=row.is_internal,
            is_multilateral=row.is_multilateral,
            signatures_to_finish=row.signatures_to_finish,
            expected_owner_signatures=row.expected_owner_signatures,
            expected_recipient_signatures=row.expected_recipient_signatures,
            signature_format=row.signature_format,
            category=row.category,
            s3_archive_key=row.s3_archive_key,
            s3_xml_to_pdf_key=row.s3_xml_to_pdf_key,
            has_changed_for_public_api=row.has_changed_for_public_api,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_delivered=row.date_delivered,
            date_finished=row.date_finished,
            user_id=row.user_id,
            is_invalid_signed=row.is_invalid_signed,
            uploaded_by_company_id=row.uploaded_by_company_id,
            uploaded_by_edrpou=row.uploaded_by_edrpou,
            uploaded_by_name=row.uploaded_by_name,
            uploaded_by_is_legal=row.uploaded_by_is_legal,
            _row=row,
        )

    def to_log_extra(self) -> DataDict:
        return {
            'document_id': self.id,
            'document_edrpou_owner': self.edrpou_owner,
            'document_uploader_edrpou': self.uploaded_by_edrpou,
            'document_title': self.title,
            'document_extension': self.extension,
            'document_status': self.status,
        }

    @property
    def is_third_party_uploader(self) -> bool:
        """
        Case scenario when document was uploaded by our partner
            (Agroyard, Portmone, Sovtes)
        and uploader is not considered as document owner
        """
        return self.uploaded_by_edrpou not in (self.edrpou_owner, self.edrpou_recipient)


class UpdateDocumentDict(t.TypedDict, total=False):
    document_id: t.Required[str]

    status_id: int
    date_updated: datetime
    date_finished: datetime | None
    role_id: str | None
    uploaded_by: str | None
    edrpou_owner: str
    title: str
    extension: str | None
    archive_name: str | None
    date_document: datetime | None
    type: str | None
    number: str | None
    amount: int | None
    source: DocumentSource
    vendor: str | None
    vendor_id: str | None
    category: int | None
    first_sign_by: FirstSignBy
    signature_format: SignatureFormat | None
    is_internal: bool
    is_multilateral: bool
    is_invalid_signed: bool | None
    has_changed_for_public_api: bool
    is_protected: bool
    signatures_to_finish: int
    expected_owner_signatures: int
    expected_recipient_signatures: int
    s3_archive_key: str | None
    s3_xml_to_pdf_key: str | None
    hermes_document_id: str | None
    date_delivered: datetime | None
    documents_seqnum: int
    user_id: str | None

    # Next fields are marked as unsafe to increase awareness that update of them should be
    # always reflected in the "document_recipients" table
    _unsafe_edrpou_recipient: str | None
    _unsafe_email_recipient: None


@dataclass
class DeleteDocumentFromDBIds:
    comment_ids: list[str]


@dataclass
class UpdateRecipient:
    """
    Single recipient for document update/upload
    """

    edrpou: str
    emails: list[str] | None
    is_email_hidden: bool
    role: RecipientRole

    @property
    def is_signer(self) -> bool:
        return self.role == RecipientRole.signer


@dataclass
class UpdateBilateralRecipientsCtx:
    recipient: UpdateRecipient
    owner: UpdateRecipient
    first_sign_by: FirstSignBy

    @property
    def expected_signatures_owner(self) -> int:
        return 1 if self.owner.is_signer else 0

    @property
    def expected_signatures_recipient(self) -> int:
        return 1 if self.recipient.is_signer else 0


@dataclass
class UpdateRecipientsCtx:
    is_ordered: bool
    recipients: list[UpdateRecipient]

    @property
    def recipients_edrpous(self) -> list[str]:
        return [recipient.edrpou for recipient in self.recipients]

    @property
    def is_empty(self) -> bool:
        return len(self.recipients) == 0

    @property
    def is_internal(self) -> bool:
        return len(self.recipients) == 1

    @property
    def is_bilateral(self) -> bool:
        return len(self.recipients) == 2

    @property
    def is_multilateral(self) -> bool:
        return len(self.recipients) > 2

    def get_bilateral_recipients(
        self,
        document_owner_edrpou: str,
    ) -> UpdateBilateralRecipientsCtx:
        """
        Unpack 2 recipients and detect who is the owner and who is the recipient
        and also who should sign first
        """

        # Detect who is the owner and who is the recipient
        recipient: UpdateRecipient
        owner: UpdateRecipient
        if self.recipients[0].edrpou == document_owner_edrpou:
            owner, recipient = self.recipients
            first_sign_by = FirstSignBy.owner
        else:
            recipient, owner = self.recipients
            first_sign_by = FirstSignBy.recipient

        # Switch to proper first_sign_by if owner or recipient is not a signer
        if first_sign_by == FirstSignBy.owner and not owner.is_signer:
            first_sign_by = FirstSignBy.recipient
        elif first_sign_by == FirstSignBy.recipient and not recipient.is_signer:
            first_sign_by = FirstSignBy.owner

        return UpdateBilateralRecipientsCtx(
            recipient=recipient,
            owner=owner,
            first_sign_by=first_sign_by,
        )

    def get_multilateral_flows(self) -> list[CreateFlowCtx]:
        flows = []
        for order, recipient in enumerate(self.recipients, start=0):
            item = CreateFlowCtx.from_params(
                edrpou=recipient.edrpou,
                emails=recipient.emails or [],
                order=order if self.is_ordered else None,
                sign_num=1 if recipient.is_signer else 0,
            )
            flows.append(item)

        return flows


@dataclass
class UpdateRecipientsExtendedCtx(UpdateRecipientsCtx):
    """
    Additional context for updating recipients to avoid selecting that data several times
    in different places
    """

    document_owner_edrpou: str
    prev_recipients: list[DocumentRecipient]
    prev_access: list[CompanyAccess]
    prev_flow_state: FlowsState
    prev_signatures: list[Signature]
    prev_document_signers: list[DocumentSignerWithEdrpou]

    @property
    def prev_document_signers_count(self) -> defaultdict[str, int]:
        """
        Count the number of signers for each company and return default dict
        { 'edrpou': count, ... }
        """
        result: defaultdict[str, int] = defaultdict(int)
        for signer in self.prev_document_signers:
            result[signer.company_edrpou] += 1
        return result

    @property
    def is_signature_exists(self) -> bool:
        return bool(self.prev_signatures)

    @property
    def prev_bilateral_recipient(self) -> DocumentRecipient | None:
        for recipient in self.prev_recipients:
            if recipient.edrpou != self.document_owner_edrpou:
                return recipient
        return None

    def had_access(self, *, edrpou: str) -> bool:
        return any(access.edrpou == edrpou for access in self.prev_access)

    @property
    def is_sent_to_recipients(self) -> bool:
        """
        Detect if a document has already been sent to some recipients
        """
        return any(
            self.had_access(edrpou=recipient.edrpou)
            for recipient in self.prev_recipients
            if recipient.edrpou != self.document_owner_edrpou
        )

    @property
    def should_autosend(self) -> bool:
        """
        Check if a document should be sent automatically to recipients.

        The main idea is to automatically send a document to recipients if it has already been
        sent to some recipients (we use access records to detect this). In this case, we assume
        that the user has already explicitly sent the document to some recipients and decided to
        make changes to the recipient list.

        In another case, we expect that the user will send the document manually to recipients by
        explicitly clicking the "Send" button in the UI.
        """

        if self.is_internal or self.is_empty:
            return False

        # Cases: multilateral, bilateral
        # The EDRPOU and email addresses to which the document should be sent should be determined
        # outside of this function based on the type of document and other conditions.
        return self.is_sent_to_recipients


@dataclass
class ListingRow:
    id: str
    document_id: str
    access_edrpou: str
    role_id: str | None
    sources: AccessSource
    date_created: datetime

    @staticmethod
    def from_row(row: DBRow) -> ListingRow:
        return ListingRow(
            id=row.id,
            document_id=row.document_id,
            access_edrpou=row.access_edrpou,
            role_id=row.role_id,
            sources=AccessSource(row.sources),
            date_created=row.date_created,
        )


@dataclass
class CompanyAccess:
    id: str
    edrpou: str
    document_id: str

    @staticmethod
    def from_row(row: DBRow) -> CompanyAccess:
        return CompanyAccess(
            id=row.id,
            edrpou=row.edrpou,
            document_id=row.document_id,
        )


@dataclass
class DeleteListingsCtx:
    listings: list[ListingRow] = field(default_factory=list)
    company_listing_ids: list[str] = field(default_factory=list)


@dataclass
class DeleteRecipientsResources:
    listings_ctx: DeleteListingsCtx = field(default_factory=DeleteListingsCtx)
    comments_ids: list[str] = field(default_factory=list)
    document_signers: list[DocumentSigner] = field(default_factory=list)

    @property
    def listings_edrpous(self) -> list[str]:
        return [listing.access_edrpou for listing in self.listings_ctx.listings]


@dataclass
class ChangeBilateralRecipientResult:
    deleted: DeleteRecipientsResources = field(default_factory=DeleteRecipientsResources)
    resend_to_recipient: bool = False


@dataclass
class SendVersionedDocumentCtx:
    """
    Context information required to send versioned document
    """

    is_versioned: bool
    versions_count: int
    has_signatures: bool

    @property
    def should_send_version(self) -> bool:
        # When:
        # - document is versioned (otherwise it's nothing to send)
        # - has no signatures, because after first signing it's
        #   not possible to add and send new version of the document
        return self.is_versioned and not self.has_signatures

    @property
    def is_first_version(self) -> bool:
        return self.versions_count == 0

    @property
    def should_sent_first_version(self) -> bool:
        return self.should_send_version and self.is_first_version

    @property
    def should_sent_next_version(self) -> bool:
        return self.should_send_version and not self.is_first_version


class DocumentDetails(t.NamedTuple):
    id_: str
    title: str

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            id_=row.id,
            title=row.title,
        )

    @classmethod
    def from_document(cls, document: Document | DBRow) -> t.Self:
        return cls(
            id_=document.id,
            title=document.title,
        )


@dataclass
class DocumentSignaturesCounter:
    """
    Number of signatures for document from owner and recipient side
    """

    owner_count: int
    recipient_count: int


@dataclass
class ReviewReminderDocumentRecipient:
    document_id: str
    company_edrpou: str
    company_name: str | None

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            document_id=row.document_id,
            company_edrpou=row.company_edrpou,
            company_name=row.company_name,
        )


@dataclass
class DeleteDocumentSettings:
    # delete_document_settings_table
    document_id: str
    is_delete_request_required: bool

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            document_id=row.document_id,
            is_delete_request_required=row.is_delete_request_required,
        )


@dataclass
class BillingDocumentsLimitCtx:
    # Documents that are visible to the user
    documents_ids: set[str]

    max_visible_documents_count: int
    max_documents_count: int
    max_archive_documents_count: int | None


@dataclass
class DocumentRecipientStateItem:
    """
    Unified model of document recipient for internal, bilateral and multilateral documents.

    See class DocumentRecipientsState to see how it is constructed for different types of documents.
    """

    recipient_id: str | None
    flow_id: str | None

    document_id: str

    edrpou: str
    emails: list[str]
    is_emails_hidden: bool

    order: int | None
    expected_signatures: int
    pending_signatures: int

    date_sent: datetime | None
    date_received: datetime | None
    date_delivered: datetime | None
    date_created: datetime | None
    date_updated: datetime | None

    assigner_role_id: str | None

    signed_roles_ids: list[str] | None  # flow.meta.role_ids

    # This attribute can be false when all signatures are collected from recipient
    # but the flow is not sent to the next recipient yet (waiting when user press "Send" button)
    is_finished: bool

    # These attributes are flow-specific
    send_jobs_executed: bool
    send_notifications_executed: bool

    external_meta: DataDict | None

    @property
    def is_signed_once(self) -> bool:
        """
        Is this recipient signed at least once?
        """
        if not self.expected_signatures:
            return False

        return self.pending_signatures < self.expected_signatures

    @property
    def sort_key(self) -> tuple[int, str, str]:
        """
        Return key for sorting in order they should sign the document.

        For recipients without an order, we sort by flow_id and recipient_id to have
        a stable order when choosing the next recipient.
        """
        order: int = self.order or 0
        flow_id: str = self.flow_id or ''
        recipient_id: str = self.recipient_id or ''

        return order, flow_id, recipient_id

    def is_equal_recipient(self, edrpou: str, emails: list[str]) -> bool:
        """
        Compare recipient by some fields and return True if all fields are equal
        """
        if self.edrpou != edrpou:
            return False

        # Compare emails case-insensitive
        if {email.lower() for email in self.emails} != {email.lower() for email in emails}:
            return False

        # It's by intention split into cascade of the "if statements" to simplify reading
        return True

    @property
    def flow_meta(self) -> FlowMeta:
        """
        Create flow meta from item.

        Try to avoid setting fields when they are not needed. Later we will skip unset fields in
        the final object that will be inserted into the database. See `FlowMeta.to_db` method.
        """
        meta = FlowMeta()

        # The "unfinished" field is used only for multilateral documents. It is set to True when
        # internal signing (document_signers) was in progress, and the company decided to remove
        # all internal signers who have not signed yet. In such a case, we set "unfinished" to
        # True for the current company flow and display the "Send" button, allowing the company
        # to send the document to the next recipient.
        #
        # In the next condition, it is important to check that the document was in the signing
        # process to replicate the same behavior as when internal signers are removed during an
        # ongoing signing process.
        if self.expected_signatures and not self.pending_signatures and not self.is_finished:
            meta.unfinished = True

        if self.signed_roles_ids:
            meta.role_ids = self.signed_roles_ids

        if self.send_jobs_executed:
            meta.send_jobs_executed = True

        if self.send_notifications_executed:
            meta.send_notifications_executed = True

        return meta


@dataclass
class DocumentBilateralSide:
    """
    This class should answer question where a bilateral document is in a bilateral
    process of signing
    """

    current_side: Literal['owner', 'recipient']

    is_received_by_recipient: bool

    is_finished_by_owner: bool
    is_finished_by_recipient: bool


@dataclass
class ConvertOfficeDocumentToPDFCtx:
    document: DocumentWithUploader
    latest_version: DocumentVersion
    source: Source


@dataclass
class UploaderInfo:
    role_id: str
    email: str

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            role_id=row.role_id,
            email=row.email,
        )


@dataclass
class ChangeBilateralRecipientCtx:
    # should we send the document to new recipient?
    should_send_to_recipient: bool

    # which companies should lose access to the document?
    edrpous_to_unsend: list[str]

    # which users should lose access to the document? (for companies that keep access)
    emails_to_remove_access: list[str]


@dataclass(frozen=True)
class RejectDocumentItemContext:
    document: DocumentWithUploader
    is_first_sign_by_recipient_document: bool
    owner_reject: bool
    recipient_reject: bool
    is_internal: bool


@dataclass(frozen=True)
class RejectDocumentContext:
    documents: list[RejectDocumentItemContext]
    validator: RejectDocumentSchema
    user: AuthUser | User

    @cached_property
    def document_ids(self) -> list[str]:
        return [document.document.id for document in self.documents]


@dataclass(frozen=True)
class PrivateDocumentIndexationItem:
    document_id: str
    edrpou: str

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            document_id=row.document_id,
            edrpou=row.edrpou,
        )


@dataclass(frozen=True)
class DocumentInvolvedUser:
    document_id: str
    user: User

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            document_id=row.document_id,
            user=User.from_row(row),
        )


@dataclass(frozen=True)
class DocumentAccessGraph:
    id: str
    dateCreated: datetime  # noqa: N815
    roleId: str | None = None  # noqa: N815
