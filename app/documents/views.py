import asyncio
import logging
from http import HTTPStatus

from aiohttp import web

from api.downloads.archives import get_archive_receipt_filename
from api.downloads.pdf.common import generate_signatures_details_file
from api.downloads.utils import stream_file_buffer
from api.downloads.validators import validate_download_signatures_details
from api.downloads.viewer import (
    get_revoke_with_viewer_signatures,
    get_viewer_signatures,
)
from app.actions.utils import get_source
from app.auth.decorators import login_required
from app.auth.types import AuthU<PERSON>, User
from app.auth.utils import get_sign_session_id_from_request
from app.auth.validators import validate_extended_auth_user_exists
from app.comments import utils as comments_utils
from app.comments.enums import CommentType
from app.config import is_test_environ
from app.documents import utils
from app.documents.db import (
    change_documents_for_public_api,
    insert_listings,
    select_document_recipients,
    update_delete_requests,
)
from app.documents.emailing import send_reject_documents_notifications
from app.documents.enums import DeleteRequestStatus
from app.documents.utils import (
    DocumentsDelete,
    accept_delete_request_util,
    cancel_delete_request_util,
    change_bilateral_document_recipient,
    create_delete_request,
    create_document_child,
    create_document_children,
    find_recipient_emails,
    handle_document_update,
    prepare_filled_emails_response,
    reject_delete_request_util,
    remove_document_child,
    save_hidden_emails,
    send_document_status_callback_job,
    send_documents_status_callback_jobs,
    send_notification_about_document_access,
)
from app.documents.utils import get_document_hash as get_document_hash_util
from app.documents.validators import (
    IndexStatusRetrieveSchema,
    validate_add_document_child_web,
    validate_add_document_children_web,
    validate_cancel_delete_vote,
    validate_convert_office_document_to_pdf,
    validate_delete_document,
    validate_delete_document_child_web,
    validate_find_recipients_emails,
    validate_open_access,
    validate_reject_documents,
)
from app.es.constants import (
    ALL_INDEXING_QUEUES,
    DOCUMENTS_LOCK_KEY,
)
from app.es.utils import send_to_indexator
from app.events import document_actions
from app.lib import validators
from app.lib.enums import DocumentStatus, SignersSource
from app.lib.helpers import not_none, safe_filename
from app.reviews.enums import ReviewRequestSource
from app.services import services
from app.sign_sessions.enums import SignSessionDocumentStatus
from app.sign_sessions.utils import update_sign_session_document_status
from app.signatures.utils import send_documents_rejected_event
from worker.topics import SEND_COMMENTS_TO_INDEX

logger = logging.getLogger(__name__)
READY_TO_BE_SIGNED = DocumentStatus.ready_to_be_signed


@login_required()
async def change_bilateral_recipient(request: web.Request, user: User) -> web.Response:
    async with services.db.acquire() as conn:
        raw_data = await validators.validate_json_request(request)
        raw_data['document_id'] = request.match_info['document_id']

        document = await change_bilateral_document_recipient(
            conn=conn,
            raw_data=raw_data,
            user=user,
            request_source=get_source(request),
        )

    await send_to_indexator(services.redis, [document.id], to_slow_queue=False)
    await services.kafka.send_record(
        topic=SEND_COMMENTS_TO_INDEX,
        value={
            'document_ids': [document.id],
        },
    )

    return web.json_response()


async def delete(
    request: web.Request,
    user: User,
    delete_from_edi: bool = True,
) -> web.Response:
    """Delete document from S3 and database."""

    document_id = request.match_info['document_id']
    data = {
        'document_id': document_id,
        'user_company_id': user.company_id,
        'user_id': user.id,
        'user_is_legal': user.is_legal,
        'user_edrpou': user.company_edrpou,
    }
    logger.info('Delete document', extra=data)

    async with services.db.acquire() as conn:
        async with conn.begin():
            document = await validate_delete_document(conn, data, user)
            documents_deleter = DocumentsDelete(
                documents=[document],
                delete_from_edi=delete_from_edi,
            )
            await documents_deleter.perform_main_transaction(conn)

    await documents_deleter.perform_async_actions()
    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.document_delete,
            document_id=document_id,
            document_edrpou_owner=document.edrpou_owner,
            document_title=document.title,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            email=user.email,
            role_id=user.role_id,
        )
    )

    return web.json_response(status=HTTPStatus.NO_CONTENT)


async def send_document(request: web.Request, raw_user: AuthUser | User) -> web.Response:
    """
    Send document after sign or ready document to recipient when first
    signature expected from recipient.
    """

    async with services.db.acquire() as conn:
        # TODO: use AuthUserExtended instead of User to send document to properly handle
        # permissions in sign session context
        _, user = await validate_extended_auth_user_exists(
            conn=conn,
            raw_user=raw_user,
            ensure_active_user=True,
        )

        json_data = await validators.validate_json_request(request, allow_blank=True)
        json_data = validators.validate_deserialized_json_type(json_data, dict)
        raw_data = {'document_id': request.match_info['document_id'], **json_data}

        await utils.send_document(
            conn=conn,
            user=user,
            company_edrpou=user.company_edrpou,
            raw_data=raw_data,
            request_source=get_source(request),
        )

    return web.json_response()


async def add_children(
    request: web.Request,
    user: User,
) -> web.Response:
    async with request.app['db'].acquire() as conn:
        ctx = await validate_add_document_children_web(conn, user, request)
        ids = await create_document_children(conn, ctx)

    await send_to_indexator(request.app['redis'], list(ids), to_slow_queue=False)
    return web.json_response(status=HTTPStatus.CREATED)


async def add_child(
    request: web.Request,
    user: User,
) -> web.Response:
    async with request.app['db'].acquire() as conn:
        ctx = await validate_add_document_child_web(conn, user, request)
        await create_document_child(conn, ctx)

    if ctx.link_exists:
        return web.Response(status=HTTPStatus.OK)

    await send_to_indexator(request.app['redis'], [ctx.child_id], to_slow_queue=False)
    return web.json_response(status=HTTPStatus.CREATED)


async def delete_child(
    request: web.Request,
    user: User,
) -> web.Response:
    async with request.app['db'].acquire() as conn:
        ctx = await validate_delete_document_child_web(conn, user, request)
        await remove_document_child(conn, ctx)

    await send_to_indexator(request.app['redis'], [ctx.child_id], to_slow_queue=False)
    return web.json_response(status=HTTPStatus.NO_CONTENT)


@login_required()
async def update(request: web.Request, user: User) -> web.Response:
    app = request.app
    document_id = request.match_info['document_id']
    data = await validators.validate_json_request(request)
    data = {**data, 'document_id': document_id}

    async with request.app['db'].acquire() as conn:
        await handle_document_update(
            conn=conn,
            user=user,
            data=data,
            request_source=get_source(request),
            signers_source=SignersSource.web,
            reviewers_source=ReviewRequestSource.web,
        )

    await send_to_indexator(app['redis'], [document_id], to_slow_queue=True)
    return web.json_response()


@login_required()
async def open_access(request: web.Request, user: User) -> web.Response:
    document_id = request.match_info['document_id']
    async with request.app['db'].acquire() as conn:
        data = await validators.validate_json_request(request)
        options = await validate_open_access(
            conn,
            user,
            {
                **data,
                'document_id': document_id,
            },
        )
        async with conn.begin():
            await insert_listings(conn, options.data)
            await change_documents_for_public_api(conn, [document_id])

    roles_ids = [role.id_ for role in options.roles]
    document = options.document
    await send_notification_about_document_access(
        user=user,
        document_id=document.id,
        document_title=document.title,
        comment=options.comment,
        roles_ids=roles_ids,
        source=get_source(request),
    )

    await send_to_indexator(request.app['redis'], [document_id], to_slow_queue=False)

    return web.json_response(status=HTTPStatus.OK)


@login_required()
async def create_delete_request_handler(request: web.Request, user: User) -> web.Response:
    """Creating document delete request"""
    data = await validators.validate_json_request(request)
    await create_delete_request(request, user, data)

    return web.json_response(data={'status': 'ok'})


@login_required()
async def cancel_delete_requests(
    request: web.Request,
    user: User,
) -> web.Response:
    data = await validators.validate_json_request(request)
    await cancel_delete_request_util(request, user, data)
    return web.json_response()


@login_required()
async def accept_delete_request(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)
    await accept_delete_request_util(request, user, data)

    return web.json_response(data={'status': 'ok'})


@login_required()
async def reject_delete_request(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)
    await reject_delete_request_util(request, user, data)
    return web.json_response(data={'status': 'ok'})


@login_required()
async def cancel_delete_request_vote(
    request: web.Request,
    user: User,
) -> web.Response:
    async with request.app['db'].acquire() as conn:
        data = await validators.validate_json_request(request)
        valid_data = await validate_cancel_delete_vote(conn, user, data)
        await update_delete_requests(
            conn=conn,
            ids=valid_data.delete_request_ids,
            data={
                'status': DeleteRequestStatus.new,
                'reject_message': None,
                'date_accepted': None,
                'date_rejected': None,
            },
        )
    return web.json_response(data={'status': 'ok'})


async def find_recipients_emails(request: web.Request, user: User) -> web.Response:
    """
    Automatically find email for given recipients EDRPOU in contacts or users tables.

    Also known as "Автопідбір пошти"
    """
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        options = await validate_find_recipients_emails(conn, user, data)

        recipients_aggregator = await find_recipient_emails(
            conn=conn,
            user=user,
            recipients_edrpous=options.edrpous,
        )

    await save_hidden_emails(
        redis=request.app['redis'],
        user=user,
        recipients=recipients_aggregator.recipients,
    )

    db_data = [
        *recipients_aggregator.recipients_data(),  # found recipients info
        *options.recipients_data(),  # recipients with emails before request
    ]
    response_data = prepare_filled_emails_response(db_data)
    return web.json_response(response_data)


async def get_document_hash(request: web.Request, user: AuthUser | User) -> web.Response:
    """Get base64 encoded DSTU hash of given document by ID

    TODO[AK]: Move to graph? If yes, to Document or some new node?
    """
    document_id = request.match_info['document_id']

    document_hash = await get_document_hash_util(user, document_id)

    return web.json_response(
        {
            'document_hash': document_hash,
            'document_id': document_id,
        }
    )


async def download_signatures_details(
    request: web.Request, user: AuthUser | User
) -> web.StreamResponse:
    """Download document signatures details"""

    async with request.app['db'].acquire() as conn:
        document = await validate_download_signatures_details(request, user, conn)
        recipients = await select_document_recipients(conn, document_id=document.id)
        signatures = await get_viewer_signatures(conn, document)
        revoke, revoke_signatures = await get_revoke_with_viewer_signatures(conn, document)

    receipt_filename = get_archive_receipt_filename()
    filename = safe_filename(f'{document.title}_{receipt_filename}')

    buffer = await generate_signatures_details_file(
        document=document,
        recipients=recipients,
        signatures=signatures,
        revoke=revoke,
        revoke_signatures=revoke_signatures,
        is_separate_file=True,
    )
    return await stream_file_buffer(request, filename, buffer)


async def reject_bulk(request: web.Request, user: User) -> web.Response:
    async with services.db.acquire() as conn:
        ctx = await validate_reject_documents(
            conn=conn,
            user=user,
            data={
                **(await validators.validate_json_request(request)),
            },
        )
        if not ctx.document_ids:
            return web.json_response()

        logger.info(
            'Rejecting documents',
            extra={
                'document_ids': ctx.document_ids,
                'role_id': ctx.user.role_id,
                'text': ctx.validator.text,
            },
        )

        async with conn.begin():
            await utils.reject_documents(conn, document_ids=ctx.document_ids)

            comments = None
            if ctx.user.role_id and ctx.validator.text:
                comments = await comments_utils.create_comments_for_documents(
                    conn=conn,
                    documents_ids=ctx.document_ids,
                    role_id=ctx.user.role_id,
                    type_=CommentType.rejection,
                    text=ctx.validator.text,
                    access_company_id=None,
                )

    if comments:
        await comments_utils.send_comments_for_indexation(
            comments_ids=[c.id for c in comments],
        )

    await send_reject_documents_notifications(
        initiator_role_id=ctx.user.role_id,
        document_ids=ctx.document_ids,
        comment=ctx.validator.text,
    )
    await send_documents_rejected_event(
        initiator_role_id=ctx.user.role_id,
        document_ids=ctx.document_ids,
        comment=ctx.validator.text,
    )

    if ctx.user.role_id and ctx.user.company_id:
        await document_actions.add_document_actions(
            document_actions=[
                document_actions.DocumentAction(
                    action=document_actions.Action.document_reject,
                    company_id=ctx.user.company_id,
                    company_edrpou=not_none(ctx.user.company_edrpou),
                    document_id=document_info.document.id,
                    document_edrpou_owner=document_info.document.edrpou_owner,
                    document_title=document_info.document.title,
                    email=ctx.user.email,
                    role_id=ctx.user.role_id,
                    extra={'comment': ctx.validator.text},
                )
                for document_info in ctx.documents
            ]
        )

    await send_documents_status_callback_jobs(
        data=[
            {
                'document_id': document_info.document.id,
                'uploaded_by_edrpou': document_info.document.uploaded_by_edrpou,
            }
            for document_info in ctx.documents
        ]
    )

    await send_to_indexator(services.redis, ctx.document_ids, to_slow_queue=False)

    return web.json_response()


async def reject(request: web.Request, user: AuthUser | User) -> web.Response:
    """Reject document by partner or by owner for internal documents."""

    # we are using AuthUser in that handler, because it should work in sign session
    # context, where user can't be exists in database at all

    async with services.db.acquire() as conn:
        ctx = await validate_reject_documents(
            conn,
            user,
            {
                **(await validators.validate_json_request(request)),
                'document_ids': [request.match_info['document_id']],
            },
        )
        if not ctx.document_ids:
            return web.json_response()

        document = ctx.documents[0].document
        logger.info(
            'Rejecting document',
            extra={
                'document_id': document.id,
                'role_id': ctx.user.role_id,
                'text': ctx.validator.text,
            },
        )

        async with conn.begin():
            await utils.reject_documents(conn, document_ids=[document.id])

            comments = None
            if ctx.user.role_id and ctx.validator.text:
                comments = await comments_utils.create_comments_for_documents(
                    conn=conn,
                    documents_ids=ctx.document_ids,
                    role_id=ctx.user.role_id,
                    type_=CommentType.rejection,
                    text=ctx.validator.text,
                    access_company_id=None,
                )

            await update_sign_session_document_status(
                conn=conn,
                sign_session_id=get_sign_session_id_from_request(request),
                next_status=SignSessionDocumentStatus.rejected,
            )

    if comments:
        await comments_utils.send_comments_for_indexation(
            comments_ids=[c.id for c in comments],
        )

    await send_reject_documents_notifications(
        initiator_role_id=ctx.user.role_id,
        document_ids=[document.id],
        comment=ctx.validator.text,
    )
    await send_documents_rejected_event(
        initiator_role_id=ctx.user.role_id,
        document_ids=[document.id],
        comment=ctx.validator.text,
    )

    if ctx.user.role_id and ctx.user.company_id:
        await document_actions.add_document_action(
            document_action=document_actions.DocumentAction(
                action=document_actions.Action.document_reject,
                company_id=ctx.user.company_id,
                company_edrpou=not_none(ctx.user.company_edrpou),
                document_id=document.id,
                document_edrpou_owner=document.edrpou_owner,
                document_title=document.title,
                email=ctx.user.email,
                role_id=ctx.user.role_id,
                extra={'comment': ctx.validator.text},
            )
        )

    await send_document_status_callback_job(
        document_id=document.id,
        uploaded_by_edrpou=document.uploaded_by_edrpou,
    )

    await send_to_indexator(services.redis, [document.id], to_slow_queue=False)

    return web.json_response()


async def indexing_status(request: web.Request, _: User) -> web.Response:
    """
    Get document indexing status by document ids.
    """

    data = validators.validate_pydantic(
        IndexStatusRetrieveSchema,
        await validators.validate_json_request(request),
    )

    async with services.redis.pipeline() as pipe:
        for queue in ALL_INDEXING_QUEUES:
            pipe.zmscore(queue, data.document_ids)

        pipe.hmget(DOCUMENTS_LOCK_KEY, data.document_ids)
        results = await pipe.execute()

    pending_document_ids = set()
    for result in results:
        for i, score in enumerate(result):
            if score is not None:
                pending_document_ids.add(data.document_ids[i])

    # Pause briefly to allow indexed documents to become searchable.
    # ---
    # Why is this pause necessary?
    # Elasticsearch updates its indexes every second.
    # So, after updating a document in ES, we need to wait
    # at least 1 second to ensure the document is searchable.
    # ---
    # Why not use refresh='wait_for' during indexing?
    # Because it would significantly slow down the indexing process.
    if not is_test_environ():
        await asyncio.sleep(1)

    return web.json_response(data=list(pending_document_ids))


@login_required()
async def convert_office_document_to_pdf(request: web.Request, user: User) -> web.Response:
    """
    Convert Office files (XLSX, DOCX, PPTX) to PDF and add a converted document as a new version
    of the original document.
    """
    async with services.db.acquire() as conn:
        ctx = await validate_convert_office_document_to_pdf(
            conn=conn,
            user=user,
            request=request,
        )
        await utils.convert_office_document_to_pdf(conn, ctx=ctx, user=user)

    return web.json_response()
