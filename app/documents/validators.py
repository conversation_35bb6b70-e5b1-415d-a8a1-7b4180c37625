from __future__ import annotations

import logging
import typing as t
from collections import defaultdict
from datetime import datetime
from itertools import chain, zip_longest

import pydantic
import sqlalchemy as sa
from aiohttp import web

from api.errors import (
    FIELD_IS_REQUIRED,
    AccessDenied,
    Code,
    DoesNotExist,
    Error,
    InvalidRequest,
    LoginRequired,
    Object,
)
from app.actions.utils import get_source
from app.archive.utils import is_document_archived
from app.auth.db import (
    select_company_by_edrpou,
    select_registered_companies_edrpous,
    select_roles_by_ids,
)
from app.auth.types import (
    AuthUser,
    Role,
    User,
)
from app.auth.utils import has_permission
from app.auth.validators import validate_coworkers_roles_ids, validate_user_permission
from app.billing.types import ChargeDocumentContext
from app.billing.validators import (
    validate_charge_document,
    validate_document_payer,
)
from app.comments.validators import CommentText
from app.document_categories.db import (
    select_document_category,
)
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.db import (
    count_document_versions,
    select_document_versions,
)
from app.document_versions.utils import get_version_count
from app.documents import db
from app.documents.constants import (
    CONVERT_TO_PDF_EXTENSIONS,
    DOCUMENT_AMOUNT_MAX_VALUE,
    DOCUMENT_NUMBER_MAX_LENGTH,
    DOCUMENT_TITLE_MAX_LENGTH,
    MAX_DOCUMENT_CHILDREN,
    TAGS_MAX_LENGTH,
)
from app.documents.db import (
    count_document_children,
    get_documents_pending_delete_requests_mapping,
    select_bilateral_document_recipient,
    select_delete_document_settings,
    select_delete_requests_by,
    select_delete_requests_by_docs_ids,
    select_delete_requests_by_ids,
    select_document_by_id,
    select_document_recipients,
    select_documents_access_companies_edrpou,
    select_documents_by_ids_with_company_info,
    select_documents_link,
    select_documents_links_simple,
    select_documents_recipients,
)
from app.documents.enums import (
    AccessSource,
    DeleteRequestStatus,
    DocumentAccessLevel,
    FirstSignBy,
    RecipientRole,
    UpdateTagsAction,
    UpdateViewersAction,
)
from app.documents.tables import (
    document_access_settings_private_table,
    document_link_table,
    document_listing_join,
    document_table,
    listing_table,
)
from app.documents.types import (
    AddDocumentChildCtx,
    AddDocumentChildrenCtx,
    ConvertOfficeDocumentToPDFCtx,
    DeleteDocumentChildCtx,
    Document,
    DocumentRecipientStateItem,
    DocumentWithUploader,
    FindRecipientEmailsOptions,
    ListingDataAggregator,
    OpenAccessOptions,
    RecipientsEmailsOptions,
    RejectDocumentContext,
    RejectDocumentItemContext,
    SendDocumentOptions,
    UpdateDocumentAccessSettingsCtx,
    UpdateDocumentOptions,
    UpdateInfoData,
    UpdateRecipient,
    UpdateRecipientsCtx,
    UpdateRecipientsExtendedCtx,
    UpdateSignersData,
    UpdateSignersDataSignerEntity,
    UpdateSignersDataSignerType,
    UpdateTagsSettings,
    UpdateVersionedCtx,
    UpdateViewersData,
    UpdateViewersEntity,
    UpdateViewersEntityType,
    UploadDocumentAccessSettingsCtx,
)
from app.documents.utils import (
    DocumentRecipientsState,
    can_delete_document,
    get_document_next_status_sending,
    get_documents_access_filters,
    get_hidden_email_key,
    is_bilateral_3p_not_sent_document,
    is_first_sign_by_recipient_document,
    is_one_sign_document,
    is_wait_owner_signature,
)
from app.documents_fields.types import UpdateDocumentParametersCtx
from app.documents_fields.validators import (
    UpdateDocumentParametersSchema,
    validate_delete_document_parameters,
    validate_document_parameters_on_update,
    validate_update_document_parameters,
)
from app.documents_required_fields.validators import validate_required_fields
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.groups.db import select_group_document_accesses
from app.groups.utils import get_group_members_by_group_ids
from app.groups.validators import validate_groups_exists
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import to_local_datetime
from app.lib.enums import (
    DocumentStatus,
    SignersSource,
    Source,
)
from app.lib.helpers import (
    group_list,
    split_comma_separated_emails,
    unique_list_with_original_order,
)
from app.lib.types import (
    DataDict,
    Redis,
    StrDict,
)
from app.lib.validators import is_malicious
from app.models import (
    exists,
    select_all,
    select_one,
)
from app.reviews.enums import ReviewRequestSource
from app.reviews.types import ReplaceReviewRequestsCtx, ReviewsInfoCtx
from app.services import services
from app.signatures.db import (
    select_document_signers_extended,
    select_signatures,
)
from app.signatures.types import DocumentSignerWithEdrpou, Signature
from app.tags.validators import TagName

logger = logging.getLogger(__name__)


APPROVED = DocumentStatus.approved.value
SIGNED = DocumentStatus.signed.value
SENT = DocumentStatus.sent.value
READY = DocumentStatus.ready_to_be_signed.value
UPLOADED = DocumentStatus.uploaded.value
SIGNED_AND_SENT = DocumentStatus.signed_and_sent.value
HISTORED = DocumentStatus.histored.value


class RejectDeleteRequestSchema(pydantic.BaseModel):
    delete_request_ids: list[pv.UUID] = pydantic.Field(..., min_length=1)
    reject_message: str | None = None


class CancelDeleteRequestSchema(pydantic.BaseModel):
    delete_request_ids: list[pv.UUID] = pydantic.Field(..., min_length=1)


class ViewerEntitySchema(pydantic.BaseModel):
    id: pv.UUID
    type: UpdateViewersEntityType


class ViewersSettingsSchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(extra='forbid')

    add_viewers: list[ViewerEntitySchema] | list[pv.UUID] | None = None
    remove_viewers: list[ViewerEntitySchema] | list[pv.UUID] | None = None
    replace_viewers: list[ViewerEntitySchema] | list[pv.UUID] | None = None


class DocumentSettingsSchema(pydantic.BaseModel):
    date_document: pv.Datetime | None = None
    number: str | None = pydantic.Field(default=None, max_length=DOCUMENT_NUMBER_MAX_LENGTH)
    title: str | None = pydantic.Field(default=None, max_length=DOCUMENT_TITLE_MAX_LENGTH)
    category: int | None = pydantic.Field(default=None, ge=0)
    amount: int | None = pydantic.Field(
        default=None,
        ge=-DOCUMENT_AMOUNT_MAX_VALUE,
        le=DOCUMENT_AMOUNT_MAX_VALUE,
    )


class RecipientSchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU
    emails: list[pv.Email] | None = None
    is_email_hidden: bool
    role: RecipientRole


class RecipientsSettingsSchema(pydantic.BaseModel):
    is_ordered: bool = True
    is_internal: bool = False
    recipients: list[RecipientSchema] | None = None


class DocumentAccessSettingsSchema(pydantic.BaseModel):
    level: DocumentAccessLevel


class UpdateDocumentInfoSchema(pydantic.BaseModel):
    """
    Schema to update basic document info about a document using public API
    """

    document_id: pv.UUID

    date: pv.Datetime | None = pydantic.Field(
        default=None,
        serialization_alias='date_document',  # for backward compatibility with WEB API
    )
    number: str | None = pydantic.Field(
        default=None,
        max_length=DOCUMENT_NUMBER_MAX_LENGTH,
        min_length=0,
    )
    title: str | None = pydantic.Field(
        default=None,
        max_length=DOCUMENT_TITLE_MAX_LENGTH,
        min_length=0,
    )
    category: int | None = pydantic.Field(default=None, ge=0, strict=True)
    amount: int | None = pydantic.Field(
        default=None,
        ge=-DOCUMENT_AMOUNT_MAX_VALUE,
        le=DOCUMENT_AMOUNT_MAX_VALUE,
        strict=True,
    )  # в копійках

    def to_update_dict(self) -> DataDict:
        # The value "none" is valid value for update, it means "clear that field". If you don't
        # want to update that field, just don't pass it in request.
        settings = self.model_dump(
            exclude_unset=True,
            by_alias=True,
            mode='json',
            include={'date', 'number', 'title', 'category', 'amount'},
        )

        return {
            'document_id': self.document_id,
            'document_settings': settings,
        }

    @pydantic.field_validator('date', mode='after')
    @classmethod
    def validate_date(cls, value: datetime | None) -> datetime | None:
        if value is None:
            return None
        if value.tzinfo is not None:
            return value

        # value without a timezone will be considered as in Kyiv timezone
        return to_local_datetime(value)


class UpdateDocumentAccessSettingsSchema(pydantic.BaseModel):
    """
    Schema to update document access settings using public API
    """

    document_id: pv.UUID
    level: DocumentAccessLevel

    def to_update_dict(self) -> DataDict:
        return {
            'document_id': self.document_id,
            'access_settings': {'level': self.level.value},
        }


class DocumentSignerEntitySchema(pydantic.BaseModel):
    id: pv.UUID
    type: t.Literal['role', 'group']


class DocumentSignersSettingsSchema(pydantic.BaseModel):
    parallel_signing: bool
    entities: list[DocumentSignerEntitySchema]


class TagsActionSchema(pydantic.BaseModel):
    tags_ids: pv.UniqueList[pv.UUID]
    new_tags: pv.UniqueList[TagName] = pydantic.Field(default_factory=list)


class TagsRemoveActionSchema(pydantic.BaseModel):
    tags_ids: pv.UniqueList[pv.UUID]


class TagsSettingsSchema(pydantic.BaseModel):
    # Deprecated: use add/replace/remove actions instead
    tags: list[str] | None = pydantic.Field(
        default=None,
        min_length=1,
        max_length=TAGS_MAX_LENGTH,
    )
    replace: TagsActionSchema | None = None
    add: TagsActionSchema | None = None
    remove: TagsRemoveActionSchema | None = None


class VersionSettingsSchema(pydantic.BaseModel):
    is_versioned: bool


class BaseDocumentSchema(pydantic.BaseModel):
    document_id: pv.UUID
    user_edrpou: pv.EDRPOU
    user_is_legal: bool


class UpdateDocumentSchema(pydantic.BaseModel):
    document_id: pv.UUID

    # === DEPRECATED (use signers_settings) ===
    update_sign_process: bool | None = False
    parallel_signing: bool | None = True
    signer_entities: list[DocumentSignerEntitySchema] | None = None
    signers: list[pv.UUID] | None = None  # deprecated
    # === DEPRECATED (use signers_settings) ===

    signers_settings: DocumentSignersSettingsSchema | None = None
    recipients_settings: RecipientsSettingsSchema | None = None
    document_settings: DocumentSettingsSchema | None = None
    access_settings: DocumentAccessSettingsSchema | None = None
    reviews_settings: ReviewsSettingsSchema | None = None
    viewers_settings: ViewersSettingsSchema | None = None
    parameters_settings: UpdateDocumentParametersSchema | None = None
    tags_settings: TagsSettingsSchema | None = None
    version_settings: VersionSettingsSchema | None = None


class OpenAccessSchema(pydantic.BaseModel):
    document_id: pv.UUID
    roles_ids: pv.UniqueList[pv.UUID] = pydantic.Field(min_length=1)
    comment: str | None = pydantic.Field(None, max_length=500)


class ChangeRecipientSchema(pydantic.BaseModel):
    document_id: pv.UUID
    edrpou: pv.EDRPOU
    emails: list[pv.Email] | None
    is_emails_hidden: bool


class DeleteDocumentSchema(BaseDocumentSchema):
    pass


class SendDocumentSchema(pydantic.BaseModel):
    document_id: pv.UUID

    # Recipient edrpou and emails
    edrpou: pv.EDRPOU | None = None
    emails: list[pv.Email] | None = pydantic.Field(default_factory=list)
    is_email_hidden: pv.BoolNullToFalse = False

    # Deprecated field
    email: list[pv.Email] | None = None

    is_internal_bill_sending: pv.BoolNullToFalse = False

    @pydantic.field_validator('email', mode='before')
    @classmethod
    def validate_email(cls, v: t.Any) -> list[str] | t.Any:
        if v is None:
            return v
        if isinstance(v, str):
            emails = split_comma_separated_emails(v.strip())
            return emails if emails else None
        return v


class ReviewerItemSchema(pydantic.BaseModel):
    id: pv.UUID
    type: ReviewsInfoCtx.EntityType | None = None

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)


class ReviewsSettingsSchema(pydantic.BaseModel):
    reviewers: list[ReviewerItemSchema]
    is_required: bool
    is_ordered: bool = False

    @pydantic.field_validator('reviewers', mode='before')
    @classmethod
    def prepare_reviewers(cls, v: t.Any) -> t.Any:
        # Only for backwards compatibility
        # Convert list of strings to list of ReviewerEntitySchema
        if not isinstance(v, list):
            return v

        if v and isinstance(v[0], str):
            role = ReviewsInfoCtx.EntityType.role
            return [{'id': id_, 'type': role.value} for id_ in v]

        return v


class DeleteRequestSchema(pydantic.BaseModel):
    document_ids: list[pv.UUID] = pydantic.Field(..., min_length=1)
    message: str


class AcceptDeleteRequestSchema(pydantic.BaseModel):
    delete_request_ids: list[pv.UUID] = pydantic.Field(min_length=1)


class FindRecipientsEmailsScheme(pydantic.BaseModel):
    document_id: pv.UUID | None
    edrpous: list[str]

    @pydantic.field_validator('edrpous', mode='before')
    @classmethod
    def filter_invalid_edrpous(cls, raw_edrpous: t.Any) -> list[str]:
        if not isinstance(raw_edrpous, list):
            return raw_edrpous

        edrpous: list[str] = []
        for raw in raw_edrpous:
            try:
                edrpous.append(pv.EDRPOUAdapter.validate_python(raw))
            except pydantic.ValidationError:
                continue
        return edrpous


class AddDocumentChildrenSchema(pydantic.BaseModel):
    children_ids: list[pv.UUID]
    parent_id: pv.UUID


class AddDocumentChildSchema(pydantic.BaseModel):
    child_id: pv.UUID
    parent_id: pv.UUID


class RejectDocumentSchema(pydantic.BaseModel):
    # from body JSON
    text: CommentText | None = None
    is_internal: bool | None = None
    version: str | None = pydantic.Field(None, max_length=36)
    # from path document_id or json body for list of documents
    document_ids: list[pv.UUID] = pydantic.Field(..., min_length=1, max_length=500)


class ResolveDocumentAccessesSchema(pydantic.BaseModel):
    source: AccessSource

    @pydantic.field_validator('source', mode='before')
    @classmethod
    def validate_source_only(cls, value: int) -> AccessSource:
        # Currently we allow only "viewer" source because we don't need other sources for now.
        # If you need to add another source, you can remove this check at all.
        if value != AccessSource.viewer.value:
            raise ValueError('Not allowed key: viewer')

        return AccessSource(value)


class IndexStatusRetrieveSchema(pydantic.BaseModel):
    document_ids: list[pv.UUID] = pydantic.Field(min_length=1, max_length=1000)


class ConvertOfficeDocumentToPdfSchema(pydantic.BaseModel):
    document_id: str


def validate_itself_link(parent_id: str, child_id: str) -> None:
    """Do not allow add itself as child document"""
    if parent_id == child_id:
        logger.info(
            msg='Can not create link to itself',
            extra={
                'parent_id': parent_id,
                'child_id': child_id,
            },
        )
        raise Error(
            code=Code.invalid_document_link,
            reason=_("Неможливо пов'язати документ з самим собою"),
        )


async def validate_one_parent_document(
    conn: DBConnection,
    *,
    parent_id: str,
    child_id: str,
    company_edrpou: str,
) -> None:
    """Validate that child have only one parent"""
    if await exists(
        conn=conn,
        select_from=document_link_table,
        clause=sa.and_(
            document_link_table.c.company_edrpou == company_edrpou,
            document_link_table.c.child_id == child_id,
            document_link_table.c.parent_id != parent_id,
        ),
    ):
        raise Error(
            code=Code.invalid_document_link,
            reason=_(
                'Дочірній документ може мати лише один головний документ. '
                "Видаліть наявні зв'язки та створіть нові."
            ),
        )


async def validate_links_not_private(
    conn: DBConnection,
    documents_ids: list[str],
    *,
    company_edrpou: str,
) -> None:
    """
    Check that we are not trying to connect a private document

    HISTORY: This is done to reduce the amount of work needed to implement private documents,
    so it might be subject to change in the future
    """

    if await exists(
        conn=conn,
        select_from=document_access_settings_private_table,
        clause=sa.and_(
            document_access_settings_private_table.c.edrpou == company_edrpou,
            document_access_settings_private_table.c.document_id.in_(documents_ids),
        ),
    ):
        raise Error(
            code=Code.invalid_document_link,
            reason=_(
                'Наразі не підтримується звʼязування для приватних документів. '
                'Змініть налаштування приватності документів та спробуйте ще раз'
            ),
        )


async def validate_child_documents_count(
    conn: DBConnection,
    parent_id: str,
    *,
    company_edrpou: str,
) -> None:
    count = await count_document_children(
        conn=conn,
        parent_id=parent_id,
        company_edrpou=company_edrpou,
    )
    _validate_children_count(count)


def _validate_children_count(count: int) -> None:
    """raise Error if children count outer limit"""

    if count >= MAX_DOCUMENT_CHILDREN:
        raise Error(
            code=Code.invalid_document_link,
            reason=(
                _(
                    'Неможливо створити більше ніж {limit} дочірніх документів для одного документу'
                ).bind(limit=MAX_DOCUMENT_CHILDREN)
            ),
        )


def _validate_links_parents(
    old_links_mapping: defaultdict[str, set[str]],
    parent_id: str,
    documents_ids: set[str],
) -> defaultdict[str, set[str]]:
    """raise Error if outer group parent_id exists"""

    _mapping: defaultdict[str, set[str]] = defaultdict(set)

    parent_ids = set(old_links_mapping.keys())

    # parents outside the current documents list [parent, *chilren]
    other_parent_ids: set[str] = parent_ids - documents_ids

    if other_parent_ids:
        # the current parent is a child to another parent
        # return empty mapping to keep this link
        if len(other_parent_ids) == 1 and parent_id in old_links_mapping[list(other_parent_ids)[0]]:
            return _mapping

        other_children_ids: list[str] = []
        for _id in other_parent_ids:
            other_children_ids.extend(old_links_mapping[_id])
        raise Error(
            code=Code.invalid_document_link,
            reason=_('Дочірні документи мають інші головні документи'),
            details={'document_ids': other_children_ids},
        )
    return old_links_mapping


async def validate_children_documents(
    conn: DBConnection,
    parent_id: str,
    *,
    company_edrpou: str,
    children_ids: list[str],
) -> defaultdict[str, set[str]]:
    documents_ids = {parent_id, *children_ids}
    documents_links = await select_documents_links_simple(
        conn=conn, company_edrpou=company_edrpou, documents_ids=documents_ids
    )
    mapping: defaultdict[str, set[str]] = defaultdict(set)
    for doc in documents_links:
        mapping[doc.parent_id].add(doc.child_id)

    _validate_children_count(count=len(children_ids))

    mapping = _validate_links_parents(
        old_links_mapping=mapping,
        parent_id=parent_id,
        documents_ids=documents_ids,
    )

    return mapping


def migrate_to_emails_list(data: SendDocumentSchema, user: User | None) -> SendDocumentSchema:
    """Helper function for log any attempting to use old deprecated email field"""
    logger_extra = {
        'user_email': user and user.email,
        'user_role_id': user and user.role_id,
        'user_company_edrpou': user and user.company_edrpou,
        **data.model_dump(mode='json'),
    }
    if not data.email:
        return data

    logger.warning('Email instead of emails was used', extra=logger_extra)
    emails = data.email or []
    data.emails = emails
    return data


async def validate_hidden_recipient(
    conn: DBConnection,
    *,
    role_id: str,
    recipient_edrpou: str,
    document_id: str,
) -> list[str]:
    """
    Validate hidden recipient emails for change recipient action.
    """
    mapping = await validate_hidden_recipients(
        conn=conn,
        role_id=role_id,
        recipient_edrpous=[recipient_edrpou],
        document_id=document_id,
    )
    return mapping[recipient_edrpou]


async def validate_hidden_recipients(
    conn: DBConnection,
    *,
    role_id: str,
    recipient_edrpous: list[str],
    document_id: str,
) -> dict[str, list[str]]:
    """
    Validate that hidden recipient exists and return mapping of recipient edrpou to emails
    """

    # Try to find hidden recipient in a database first
    recipients = await select_document_recipients(
        conn=conn,
        document_id=document_id,
    )
    mapping_db: dict[str, list[str]] = {
        recipient.edrpou: recipient.emails for recipient in recipients if recipient.emails
    }

    # Then try to find another hidden recipient in Redis
    missing_edrpous = list(set(recipient_edrpous) - set(mapping_db.keys()))
    mapping_redis = await validate_hidden_emails_in_redis(
        role_id=role_id,
        recipient_edrpous=missing_edrpous,
    )

    # Merge mapping from DB and Redis
    mapping: dict[str, list[str]] = {}
    for recipient_edrpou in recipient_edrpous:
        if recipient_edrpou in mapping_db:
            mapping[recipient_edrpou] = mapping_db[recipient_edrpou]
        else:
            mapping[recipient_edrpou] = mapping_redis[recipient_edrpou]

    return mapping


async def validate_hidden_emails_in_redis(
    *,
    role_id: str,
    recipient_edrpous: list[str],
) -> dict[str, list[str]]:
    """Try to find hidden emails in redis"""

    # Then try to find hidden recipient in Redis.
    mapping: dict[str, list[str]] = {}
    for recipient_edrpou in recipient_edrpous:
        emails_key = get_hidden_email_key(
            role_id=role_id,
            recipient_edrpou=recipient_edrpou,
        )
        emails = await services.redis.smembers(emails_key)
        if emails:
            mapping[recipient_edrpou] = list(set(emails))

    if set(mapping.keys()) != set(recipient_edrpous):
        logger.info(
            msg='Hidden emails not found in Redis',
            extra={
                'role_id': role_id,
                'recipient_edrpous': recipient_edrpous,
                'found_recipients': mapping.keys(),
            },
        )
        raise InvalidRequest(
            reason=_('Не можемо знайти підібраний email. Спробуйте підібрати його знову.'),
        )

    return mapping


def _validate_change_bilateral_recipient_status(document: Document) -> None:
    """
    Validate that recipient of a bilateral document can be changed in current status
    """

    # In 3p case (signature from one side) we allow to change recipient
    # in any status.
    # TODO: understand why, because it's not clear how to handle
    #  case when recipient has already signed the document
    if is_one_sign_document(document._row):
        return

    # Otherwise (sign from both sides) we allow changing recipient
    # only in non-finished status
    _validate_document_non_finished(document)


async def _validate_change_bilateral_recipient_hidden_emails(
    conn: DBConnection,
    *,
    recipient_edrpou: str,
    recipient_emails_hidden: bool,
    recipient_emails: list[str] | None,
    document: Document,
    user: User,
) -> list[str]:
    """
    Validate hidden recipient emails for change recipient action.
    """

    # Verify existence of hidden recipient emails
    if recipient_emails_hidden is True:
        recipient_emails = await validate_hidden_recipient(
            conn=conn,
            role_id=user.role_id,
            recipient_edrpou=recipient_edrpou,
            document_id=document.id,
        )
        return recipient_emails

    # Deny change recipients without emails
    if not recipient_emails:
        raise InvalidRequest(reason=_('Поле email не може бути пустим.'))

    return recipient_emails or []


async def validate_change_bilateral_recipient(
    conn: DBConnection,
    raw_data: DataDict,
    user: User,
) -> UpdateDocumentOptions:
    """
    Validate update bilateral document recipient email, edrpou or hidden status

    For more complex cases of updating document recipient use general update document API
    """

    from app.flow.utils import get_flows_state

    valid_data = validators.validate_pydantic(ChangeRecipientSchema, raw_data)

    recipient_emails: list[str] | None = valid_data.emails
    recipient_edrpou: str = valid_data.edrpou
    recipient_emails_hidden: bool = valid_data.is_emails_hidden

    document = await validate_document_exists(conn, {'document_id': valid_data.document_id})

    # Only the owner of a document can change recipient
    _validate_document_owner(document, company_edrpou=user.company_edrpou)

    # Deny changing recipient for internal and multilateral documents
    if not document.is_bilateral:
        raise Error(Code.invalid_action)

    _validate_change_bilateral_recipient_status(document)

    recipient_emails = await _validate_change_bilateral_recipient_hidden_emails(
        conn=conn,
        recipient_edrpou=recipient_edrpou,
        recipient_emails=recipient_emails,
        recipient_emails_hidden=recipient_emails_hidden,
        document=document,
        user=user,
    )

    # Do not allow setting self as recipient
    validate_self_recipient(
        user=user,
        recipient=RecipientsEmailsOptions(
            edrpou=recipient_edrpou,
            emails=recipient_emails,
        ),
    )

    validate_owner_not_recipient(
        edrpou_owner=document.edrpou_owner,
        edrpou_recipient=recipient_edrpou,
    )

    await validate_required_fields(
        conn=conn,
        edrpous=[recipient_edrpou],
        document_title=document.title,
        document_number=document.number,
        document_date=document.date_document,
        document_amount=document.amount,
        category=document.category,
    )

    # Select additional context and create a new context with it
    prev_recipients = await select_document_recipients(conn=conn, document_id=document.id)
    prev_access = await select_documents_access_companies_edrpou(conn, document_ids=[document.id])
    prev_flow_state = await get_flows_state(conn=conn, document_id=document.id)
    prev_signatures = await select_signatures(conn=conn, document_ids=[document.id])
    prev_document_signers = await select_document_signers_extended(conn, document_id=document.id)

    should_owner_sign = document.expected_owner_signatures > 0
    should_recipient_sign = document.expected_recipient_signatures > 0

    # Owner always should be presented in the list of recipients
    new_owner = UpdateRecipient(
        edrpou=document.edrpou_owner,
        emails=None,
        is_email_hidden=False,
        role=RecipientRole.signer if should_owner_sign else RecipientRole.viewer,
    )

    # We can update edrpou, emails, and hidden flag for the recipient, but not the role
    # using this API. Use general update document API for more flexibility.
    new_recipient = UpdateRecipient(
        edrpou=recipient_edrpou,
        emails=recipient_emails,
        is_email_hidden=recipient_emails_hidden,
        role=RecipientRole.signer if should_recipient_sign else RecipientRole.viewer,
    )

    new_recipients: list[UpdateRecipient]
    if document.first_sign_by == FirstSignBy.owner:
        new_recipients = [new_owner, new_recipient]
    elif document.first_sign_by == FirstSignBy.recipient:
        new_recipients = [new_recipient, new_owner]
    else:
        t.assert_never(document.first_sign_by)

    return UpdateDocumentOptions(
        user=user,
        document=document,
        recipients_settings=UpdateRecipientsExtendedCtx(
            # bilateral always ordered, order controlled by "first_sign_by" field
            is_ordered=True,
            recipients=new_recipients,
            prev_recipients=prev_recipients,
            document_owner_edrpou=document.edrpou_owner,
            prev_access=prev_access,
            prev_flow_state=prev_flow_state,
            prev_signatures=prev_signatures,
            prev_document_signers=prev_document_signers,
        ),
        access_settings=None,
        document_settings=None,
        tags_settings=None,
        viewers_settings=None,
        version_settings=None,
        reviews_settings=None,
        parameters_settings=None,
        signers_settings=None,
    )


async def _validate_can_company_update_signers(
    conn: DBConnection,
    document: Document,
    user: User,
) -> None:
    """
    Check if the current company can update signers of the document
    """

    if document.is_internal:
        return

    if document.is_multilateral:
        from app.flow.utils import get_flows_state

        flow_state = await get_flows_state(conn, document_id=document.id)
        should_company_sign = flow_state.should_company_sign(company_edrpou=user.company_edrpou)

        can_company_edit = should_company_sign or document.status == DocumentStatus.uploaded
        if not can_company_edit:
            raise InvalidRequest(
                reason=_(
                    'Неможливо редагувати підписантів документа, коли документ '
                    'знаходиться на підписанні у іншій компанії'
                ),
            )

    if document.is_bilateral:
        # We allow an owner to change signers even before the signing process for the owner has
        # started. This works only for documents where the recipient should sign first. This
        # feature was introduced by accident some time ago, but our clients started using it and
        # now expect it to work.
        if is_bilateral_3p_not_sent_document(document):
            return

        # Is the document is on the owner side or on the recipient side to sign?
        is_owner_side = is_wait_owner_signature(document=document)
        is_recipient_side = not is_owner_side

        # Is the current user an owner or a recipient of the document?
        is_document_owner = user.company_edrpou == document.edrpou_owner
        is_document_recipient = not is_document_owner

        can_company_edit = (
            # Is an owner should sign now?
            (is_document_owner and is_owner_side)
            or
            # Is a recipient should sign now?
            (is_document_recipient and is_recipient_side)
        )
        if not can_company_edit:
            raise InvalidRequest(
                reason=_(
                    'Неможливо редагувати підписантів документа, коли документ '
                    'знаходиться на підписанні у іншій компанії'
                ),
            )


async def validate_ability_edit_signers(
    conn: DBConnection,
    document: Document,
    user: User,
    previous_signers: list[DocumentSignerWithEdrpou],
) -> None:
    """
    Check if current company can edit document signers in three cases:
     - multilateral document
     - internal document
     - bilateral document
    """

    # No matter what type document is, we can't change signers for finished documents
    if document.status == DocumentStatus.finished:
        raise InvalidRequest(
            reason=_('Неможливо редагувати підписантів завершеного документа'),
        )

    # Check on which side the document is
    # to determine if the company can change signers
    await _validate_can_company_update_signers(
        conn=conn,
        document=document,
        user=user,
    )

    # Anyone with access to the document can change signers when:
    #  - no one added to signers yet
    #  - no one signed the document yet
    if not previous_signers:
        return

    is_signed = any(signer.is_signed for signer in previous_signers)
    if not is_signed:
        return

    # After the signing process has started, only the admin or user
    # that assigned signers can change signers
    if not has_permission(user, {'can_change_document_signers_and_reviewers'}) and not any(
        signer.assigner == user.role_id for signer in previous_signers
    ):
        raise InvalidRequest(
            reason=_(
                'Тільки адміністратор чи автор процесу підписання може редагувати '
                'підписантів документа',
            ),
        )


async def _validate_already_signed_signers_change(
    conn: DBConnection,
    user: User,
    signers: list[str],
    signatures: list[Signature],
) -> None:
    """
    Signer that already signed document can't be removed
    """

    role_ids = [s.role_id for s in signatures if s.role_id is not None]
    if not role_ids:
        return

    roles = await select_roles_by_ids(conn=conn, roles_ids=role_ids)
    roles_map: dict[str, Role] = {r.id: r for r in roles}

    already_signed = set()
    for signature in signatures:
        if not signature.role_id:
            continue

        # Remove inactive roles from already_signed set.
        # Role could be deleted from company when document signers are modified
        role = roles_map.get(signature.role_id)
        if role and not role.is_active:
            continue

        if signature.owner_edrpou == user.company_edrpou:
            already_signed.add(signature.role_id)

    if removed_signers := already_signed.difference(set(signers)):
        raise InvalidRequest(
            reason=_('Неможливо видалити підписанта документа, коли він вже підписав документ'),
            details={'signers': removed_signers},
        )


async def validate_document_category(
    conn: DBConnection,
    category_id: int,
    company_edrpou: str,
    is_document_internal: bool,
) -> None:
    """
    - Validate company exists
    - Validate document category exists

    If document category is internal - validate document is internal as well
    If document category is public - bypass validation
    """

    company = await select_company_by_edrpou(conn=conn, edrpou=company_edrpou)
    if not company:
        raise InvalidRequest(reason=_('Компанію не знайдено'))

    document_category = await select_document_category(
        conn=conn,
        document_category_id=category_id,
        company_id=company.id,
    )
    if not document_category:
        raise DoesNotExist(obj=Object.document_category)

    if document_category.is_internal and not is_document_internal:
        raise InvalidRequest(
            reason=_('Цей тип документа можна використовувати лише для внутрішніх документів')
        )


async def _validate_update_general_info(
    data: DocumentSettingsSchema | None,
    document: DocumentWithUploader,
    user: User,
    request_source: Source,
) -> UpdateInfoData | None:
    """
    Validate basic document fields like date, number, title, amount, category
    """

    if not data:
        return None

    if not document.status.is_document_update_allowed:
        raise Error(
            Code.invalid_document_status,
            details={'status': document.status_id},
        )

    prev_date = document.date_document
    prev_number = document.number or None
    prev_title = document.title
    prev_amount = document.amount
    prev_category = document.category or PublicDocumentCategory.other.value

    provided_raw_data = data.model_dump(exclude_unset=True)
    new_title = data.title or prev_title
    new_date = data.date_document if 'date_document' in provided_raw_data else prev_date
    new_amount = data.amount if 'amount' in provided_raw_data else prev_amount

    # Fronted can change none to empty string, even if nothing was changed
    new_number = (data.number if 'number' in provided_raw_data else prev_number) or None

    if 'category' in provided_raw_data:
        new_category = (
            data.category if data.category is not None else PublicDocumentCategory.other.value
        )
    else:
        new_category = prev_category

    async with services.db_readonly.acquire() as conn:
        await validate_document_category(
            conn=conn,
            category_id=new_category,
            company_edrpou=user.company_edrpou,
            is_document_internal=document.is_internal,
        )

    is_owner = user.company_edrpou == document.edrpou_owner
    is_signed = document.status_id >= SIGNED and document.status_id != HISTORED

    # Rules:
    # - only an owner can change general info
    # - document should not be signed, yet
    # With API we strictly check those rules to avoid confusion. But on the WEB and other sources,
    # we try to be more flexible and ignore changes if they are not allowed because frontend can
    # send unchanged data, even when it's not possible to change it.
    if request_source == Source.api_public:
        if is_signed:
            raise InvalidRequest(
                reason=_(
                    'Не можливо змінити загальні реквізити документа, якщо процес підписання '
                    'вже розпочато'
                ),
            )

        if not is_owner:
            raise InvalidRequest(
                reason=_(
                    'Змінити загальні реквізити документа може тільки компанія власник документа'
                ),
            )

    else:
        # WEB and other sources:

        # Do not update data if already set in a document and a signing process has started
        if is_signed:
            new_date = prev_date or new_date
            new_number = prev_number or new_number
            new_title = prev_title or new_title
            new_amount = prev_amount if prev_amount is not None else new_amount
            if prev_category != PublicDocumentCategory.other.value:
                new_category = prev_category

        # `data` has all objects even when some fields are not changed,
        # but we need to know if general info was changed
        has_changed = (
            new_date != prev_date
            or new_number != prev_number
            or new_title != prev_title
            or new_amount != prev_amount
            or new_category != prev_category
        )

        if not is_owner and has_changed:
            logger.info(
                'Validate general info',
                extra={
                    'document_status': document.status_id,
                    'document_uploaded_by_edrpou': document.uploaded_by_edrpou,
                    'document_edrpou_owner': document.edrpou_owner,
                    'user_edrpou': user.company_edrpou,
                    'document_id': document.id,
                    'prev_date': prev_date,
                    'prev_number': prev_number,
                    'prev_title': prev_title,
                    'prev_amount': prev_amount,
                    'prev_category': prev_category,
                    'new_date': new_date,
                    'new_number': new_number,
                    'new_title': new_title,
                    'new_amount': new_amount,
                    'new_category': new_category,
                },
            )

            reason = _('Тільки компанія власник документу може змінювати загальні реквізити')
            raise InvalidRequest(reason=reason)

    if new_title != prev_title:
        validate_title(new_title)

    return UpdateInfoData(
        # Set category to null if type is Other (0)
        category=new_category if new_category != PublicDocumentCategory.other.value else None,
        date=new_date,
        number=new_number,
        title=new_title,
        amount=new_amount,
    )


async def _validate_update_parameters(
    conn: DBConnection,
    user: User,
    data: UpdateDocumentParametersSchema | None,
    document: DocumentWithUploader,
) -> UpdateDocumentParametersCtx | None:
    settings = data
    if not settings:
        return None

    is_new_api = settings.add or settings.remove or settings.replace

    if add := settings.add:
        validated_update_parameters = await validate_update_document_parameters(
            conn=conn,
            user=user,
            items=add,
        )
        return UpdateDocumentParametersCtx(
            add=validated_update_parameters,
            delete=[],
            replace=None,
        )
    if remove := settings.remove:
        validated_delete_parameters = await validate_delete_document_parameters(
            conn=conn,
            user=user,
            fields_ids=remove,
        )
        return UpdateDocumentParametersCtx(
            add=[],
            delete=validated_delete_parameters,
            replace=None,
        )

    if settings.replace is not None:
        validated_replace_parameters = await validate_update_document_parameters(
            conn=conn,
            user=user,
            items=settings.replace,
        )
        return UpdateDocumentParametersCtx(
            add=[],
            delete=[],
            replace=validated_replace_parameters,
        )

    if not is_new_api:
        # Backward compatibility
        if settings is None or not (settings.update_parameters or settings.delete_parameters):
            return None

        return await validate_document_parameters_on_update(
            conn=conn,
            user=user,
            update_parameters=settings.update_parameters or [],
            delete_fields_ids=settings.delete_parameters or [],
            document_id=document.id,
        )

    return None


async def validate_recipient_params_for_internal(
    document_owner_edrpou: str,
    recipient: UpdateRecipient,
) -> None:
    """
    Make basic checks of recipients for internal documents and convert them to legacy options.
    """
    if document_owner_edrpou != recipient.edrpou:
        logger.info(
            msg='Internal document recipients do not contain the company itself',
            extra={
                'document_owner_edrpou': document_owner_edrpou,
                'recipient_edrpou': recipient.edrpou,
            },
        )
        raise InvalidRequest(
            reason=_('Тільки компанія відправник може бути одержувачем внутрішнього документа')
        )


async def _validate_update_recipients_emails(
    conn: DBConnection,
    ctx: UpdateRecipientsExtendedCtx,
    role_id: str,
    document_id: str,
    document_owner_edrpou: str,
) -> UpdateRecipientsExtendedCtx:
    """
    Fill and validate hidden recipient emails for update recipients action.

    WARNING: This function modifies ctx.recipients in place.
    """
    hidden_recipients = [
        recipient
        for recipient in ctx.recipients
        if recipient.is_email_hidden and recipient.edrpou != document_owner_edrpou
    ]
    hidden_edrpous = [recipient.edrpou for recipient in hidden_recipients]

    hidden_mapping = await validate_hidden_recipients(
        conn=conn,
        role_id=role_id,
        document_id=document_id,
        recipient_edrpous=hidden_edrpous,
    )

    for recipient in hidden_recipients:
        recipient.emails = hidden_mapping[recipient.edrpou]

    # Check that after filling all recipients, except the owner, have emails
    recipients_without_emails = [
        recipient
        for recipient in ctx.recipients
        if not recipient.emails and recipient.edrpou != document_owner_edrpou
    ]
    if recipients_without_emails:
        raise InvalidRequest(
            reason=_('Всі одержувачі, крім власника, повинні мати заповнений email')
        )

    return ctx


async def validate_recipients_params_for_bilateral_empty(document: Document) -> None:
    """
    Check if it is possible to make a bilateral document without recipients from current document.
    """
    if not document.is_internal:
        raise InvalidRequest(
            reason=_(
                'Зробити двосторонній документ без одержувачів можна '
                'тільки з внутрішнього документа'
            )
        )


async def validate_recipients_params_for_bilateral(
    document_owner_edrpou: str,
    recipients: list[UpdateRecipient],
) -> None:
    """
    Make basic checks of recipients for bilateral documents and convert them to legacy options.
    """

    recipients_edrpou = {r.edrpou for r in recipients}

    error_details = {
        'owner_edrpou': document_owner_edrpou,
        'recipients_edrpous': sorted(recipients_edrpou),
    }

    if len(recipients_edrpou) != 2:
        raise InvalidRequest(
            reason=_('Двосторонній документ повинен бути між двома різними компаніями'),
            details=error_details,
        )

    # check that the current company is one of the recipients
    if document_owner_edrpou not in recipients_edrpou:
        raise InvalidRequest(
            reason=_('Компанія відправник має бути однією з компаній-отримувачів документа'),
            details=error_details,
        )

    # check that there is at least one signer among recipients
    if not any(recipient.is_signer for recipient in recipients):
        raise InvalidRequest(
            reason=_('Документ повинен мати хоча б одного підписанта'),
            details=error_details,
        )


async def validate_recipients_params_for_multilateral(
    recipients: list[UpdateRecipient],
    document_owner_edrpou: str,
) -> None:
    """
    Make basic checks of recipients for multilateral documents and convert them to legacy options.
    """
    recipients_edrpou = {r.edrpou for r in recipients}
    error_details = {
        'owner_edrpou': document_owner_edrpou,
        'recipients_edrpous': sorted(recipients_edrpou),
    }

    # check that are more than companies involved
    if len(recipients_edrpou) < 2:
        raise InvalidRequest(
            reason=_('Багатосторонній документ повинен бути між двома або більше компаніями'),
            details=error_details,
        )

    # check that the current company is one of the recipients
    if document_owner_edrpou not in recipients_edrpou:
        raise InvalidRequest(
            reason=_('Компанія відправник має бути однією з компаній-отримувачів документа'),
            details=error_details,
        )

    # check that there is at least one signer among recipients
    if not any(recipient.is_signer for recipient in recipients):
        raise InvalidRequest(
            reason=_('Документ повинен мати хоча б одного підписанта'),
            details=error_details,
        )


async def _validate_update_recipients_document_state_bilateral(
    conn: DBConnection,
    document: Document,
    ctx: UpdateRecipientsExtendedCtx,
) -> None:
    """
    Check that for current bilateral document we can update recipients, like change order, make
    it internal or multilateral, etc.

    It's expected that the document is bilateral
    """

    _validate_document_non_finished(document)

    # Before a signing process has started, we allow changing a bilateral document to
    # multilateral or internal without any additional restrictions.
    signatures = await select_signatures(conn, document_ids=[document.id])
    company_signatures_map = group_list(signatures, key_func=lambda s: s.owner_edrpou)
    if not signatures:
        return

    # Any checks bellow assume that a signing process has started

    # We don't allow making the document empty (bilateral without recipients) due to missing logic
    # for handle such cases. Feel free to implement it and remove this restriction.
    if ctx.is_empty:
        raise InvalidRequest(
            reason=_(
                'Неможливо видалити контрагента двостороннього документа, коли вже розпочато '
                'підписання. Натомість ви можете змінити чи додати нового контрагента до такого '
                'документа',
            ),
        )

    # We don't allow making the document internal because internal documents should be billed
    # differently, and the document can be finished immediately after changing to internal.
    if ctx.is_internal:
        raise InvalidRequest(
            reason=_(
                'Наразі не можливо змінити двосторонній документ на внутрішній після '
                'того як розпочато підписання'
            ),
        )

    if ctx.is_bilateral:
        bilateral_ctx = ctx.get_bilateral_recipients(document_owner_edrpou=document.edrpou_owner)
        new_recipient = bilateral_ctx.recipient
        prev_recipient = ctx.prev_bilateral_recipient

        # Allow setting initial recipient for the document without additional checks
        if not prev_recipient:
            return

        has_recipient_signature = prev_recipient and prev_recipient.edrpou in company_signatures_map

        for _recipient in ctx.recipients:
            has_signature = _recipient.edrpou in company_signatures_map
            if has_signature and not _recipient.is_signer:
                raise InvalidRequest(
                    reason=_(
                        'Для компанії, що вже підписала документ, не можливо змінити налаштування '
                        'чи може вона підписувати чи ні'
                    ),
                )

        if has_recipient_signature and new_recipient.edrpou != prev_recipient.edrpou:
            raise InvalidRequest(
                reason=_(
                    'Оскільки контрагент вже підписав документ, то не можливо змінити його '
                    'на іншого контрагента'
                ),
            )

        if bilateral_ctx.first_sign_by != document.first_sign_by:
            raise InvalidRequest(
                reason=_(
                    'Неможливо змінити порядок підписання, якщо один з контрагентів вже підписав '
                    'документ'
                ),
            )

    # TODO: allow to change recipients from bilateral to multilateral
    #   when signing process has started
    if ctx.is_multilateral and signatures:
        raise InvalidRequest(
            reason=_(
                'Наразі не можливо змінити двосторонній документ на багатосторонній після '
                'того як розпочато підписання'
            ),
        )


def _validate_update_recipients_document_state(  # noqa: C901
    document: Document,
    ctx: UpdateRecipientsExtendedCtx,
) -> None:
    """
    Validate that we can change recipients for the document in the current state, like:
    who signed, which status, etc.
    """

    _validate_document_non_finished(document)

    # When a document is not signed, we allow changing recipients in any way
    if not ctx.is_signature_exists:
        return

    # Basic checks based on a type of document, from which we don't allow to change recipients
    # after the signing process has started
    if ctx.is_empty:
        raise InvalidRequest(
            reason=_(
                'Неможливо видалити контрагентів на документі, після того як розпочалося '
                'підписання. Натомість ви можете змінити чи додати нового контрагента до такого '
                'документа',
            ),
        )

    # Don't allow changing to and from an internal document after the signing process has started.
    # Maybe that will be allowed in the future, but for now, we don't have a clear understanding
    # on the product side of how to handle such cases.
    if document.is_internal:
        raise InvalidRequest(
            reason=_(
                'Не можливо додати контрагентів до внутрішнього документу після того як '
                'розпочалося підписання документу'
            )
        )
    if ctx.is_internal:
        raise InvalidRequest(
            reason=_(
                'Наразі не можливо змінити зовнішній документ на внутрішній після '
                'того як розпочато підписання'
            ),
        )

    # Next checks are actual only for bilateral and multilateral documents:

    # can't remove companies that already signed the document
    signed_edrpous = {s.owner_edrpou for s in ctx.prev_signatures}
    new_recipients_edrpous = {r.edrpou for r in ctx.recipients}

    removed_signed_edrpous = signed_edrpous - new_recipients_edrpous
    if removed_signed_edrpous:
        raise InvalidRequest(
            reason=_(
                'Неможливо видалити контрагенів, які вже підписали документ. ЄДРПОУ компаній, '
                'яких немає в оновленому списку контрагентів: {removed_edrpous}'
            ).bind(removed_edrpous=', '.join(removed_signed_edrpous))
        )

    # can't make companies that already signed as non-signers
    for signed_edrpou in signed_edrpous:
        if not any(r.edrpou == signed_edrpou and r.is_signer for r in ctx.recipients):
            raise InvalidRequest(
                reason=_(
                    'Неможливо змінити налаштування чи компанія підписувати чи ні для компаній, '
                    'що вже підписали документ. ЄДРПОУ компаній, що вже підписали: {edrpou_list}'
                ).bind(edrpou_list=', '.join(signed_edrpous))
            )

    if ctx.is_ordered:
        # Is a document ordered now?
        # INFO: bilateral and internal are only ordered, multilateral can be ordered or parallel
        is_ordered = ctx.prev_flow_state.is_ordered if document.is_multilateral else True
        is_parallel = not is_ordered

        state = DocumentRecipientsState(
            document=document,
            recipients=ctx.prev_recipients,
            flows=ctx.prev_flow_state.flows,
            signatures=ctx.prev_signatures,
            document_signers=ctx.prev_document_signers,
        )

        # The document is ordered now and new settings are ordered
        if is_ordered:
            # check that the order of signers is not changed
            new_recipient: UpdateRecipient | None
            prev_recipient: DocumentRecipientStateItem | None
            for new_recipient, prev_recipient in zip_longest(
                ctx.recipients,
                state.recipients_items,
                fillvalue=None,
            ):
                # user can add new recipient (1-st condition)
                # or replace previous recipient that is not signed yet (2-nd condition)
                if not prev_recipient or not prev_recipient.is_signed_once:
                    continue

                # in other cases, when prev recipient is signed, check that the new recipient is
                # basically the same
                if not new_recipient:
                    raise InvalidRequest(
                        reason=_('Неможливо видалити контрагента, який вже підписав документ'),
                    )
                if new_recipient.edrpou != prev_recipient.edrpou:
                    raise InvalidRequest(
                        reason=_(
                            'Неможливо змінити порядок підписання для контрагентів, що '
                            'вже підписали документ'
                        ),
                    )
                if not new_recipient.is_signer:
                    raise InvalidRequest(
                        reason=_(
                            'Для компанії, що вже підписала документ, не можливо змінити '
                            'налаштування чи може вона підписувати чи ні'
                        ),
                    )

        # The document is parallel now and new settings are ordered
        if is_parallel:
            # check that non-signed companies only after signed companies
            order_last_signed: None | int = None
            order_first_unsigned: None | int = None
            for order, new_recipient in enumerate(ctx.recipients, start=0):
                is_signed = new_recipient.edrpou in signed_edrpous
                if is_signed:
                    order_last_signed = order
                if not is_signed and not order_first_unsigned:
                    order_first_unsigned = order

            if (
                order_first_unsigned is not None
                and order_last_signed is not None
                and order_first_unsigned < order_last_signed
            ):
                raise InvalidRequest(
                    reason=_(
                        'Компанії, що не підписували документ мають бути після компаній, що '
                        'вже підписали документ'
                    ),
                )


async def validate_update_recipients_params(
    data: RecipientsSettingsSchema,
    company_edrpou: str,
) -> UpdateRecipientsCtx:
    """
    Validate data for update document recipients.

    To simplify the logic on fronted, we adopt input parameter to be as close as possible
    to how an input form looks like on the frontend. Parameter "recipients" is not for internal
    documents, we can infer it from the "is_internal" parameter, in other cases we require
    providing recipients.
    """

    recipients: list[UpdateRecipient] = []
    if data.recipients is not None:
        recipients = [
            UpdateRecipient(
                edrpou=recipient.edrpou,
                emails=recipient.emails,
                is_email_hidden=recipient.is_email_hidden,
                role=recipient.role,
            )
            for recipient in data.recipients
        ]

    is_internal = data.is_internal

    # Handle cases when "recipients" parameter is not provided:
    #
    # For all non-internal documents we requre to provide recipients:
    #  - bilateral, without recipient -> { recipients: [] }
    #  - bilateral, with recipient -> { recipients: [ {...}, {...} ] }
    #  - multilateral -> { recipients: [ {...}, {...}, ... ] }
    if not is_internal and data.recipients is None:
        raise InvalidRequest(
            reason=_('Документ повинен мати хоча б одного одержувача'),
        )

    # For internal documents, "recipients" parameter is optional, and we can
    # automatically add a document owner as a single recipient
    if is_internal and data.recipients is None:
        recipients = [
            UpdateRecipient(
                edrpou=company_edrpou,
                emails=[],
                is_email_hidden=False,
                role=RecipientRole.signer,
            ),
        ]

    return UpdateRecipientsCtx(
        is_ordered=data.is_ordered,
        recipients=recipients,
    )


async def _validate_required_fields_on_update(
    conn: DBConnection,
    document: Document,
    ctx: UpdateRecipientsExtendedCtx,
    document_settings: UpdateInfoData | None,
) -> None:
    """
    Validate required fields for update recipients during all-in-one document update.
    """

    if ctx.should_autosend:
        # All recipients except the document owner
        other_recipients = [r for r in ctx.recipients if r.edrpou != document.edrpou_owner]
        other_recipients_edrpous = [r.edrpou for r in other_recipients]

        # When new values are provided in edit form, we should use them to validate required
        # fields, otherwise we should use current values from the database.
        if document_settings:
            await validate_required_fields(
                conn=conn,
                edrpous=other_recipients_edrpous,
                document_title=document_settings.title,
                document_number=document_settings.number,
                document_date=document_settings.date,
                document_amount=document_settings.amount,
                category=document_settings.category,
            )
        else:
            await validate_required_fields(
                conn=conn,
                edrpous=other_recipients_edrpous,
                document_title=document.title,
                document_number=document.number,
                document_date=document.date_document,
                document_amount=document.amount,
                category=document.category,
            )


async def _validate_update_recipients(
    conn: DBConnection,
    data: RecipientsSettingsSchema | None,
    user: User,
    document: Document,
    document_settings: UpdateInfoData | None,
) -> UpdateRecipientsExtendedCtx | None:
    """
    Validate update recipients on all-in-one document update.
    """
    raw_settings = data
    if not raw_settings:
        return None

    from app.flow.utils import get_flows_state

    base_ctx = await validate_update_recipients_params(
        data=raw_settings,
        company_edrpou=user.company_edrpou,
    )

    # Select additional context and create a new context with it
    prev_recipients = await select_document_recipients(conn=conn, document_id=document.id)
    prev_access = await select_documents_access_companies_edrpou(conn, document_ids=[document.id])
    prev_flow_state = await get_flows_state(conn=conn, document_id=document.id)
    prev_signatures = await select_signatures(conn=conn, document_ids=[document.id])
    prev_document_signers = await select_document_signers_extended(conn, document_id=document.id)

    ctx = UpdateRecipientsExtendedCtx(
        recipients=base_ctx.recipients,
        is_ordered=base_ctx.is_ordered,
        document_owner_edrpou=document.edrpou_owner,
        prev_recipients=prev_recipients,
        prev_access=prev_access,
        prev_flow_state=prev_flow_state,
        prev_signatures=prev_signatures,
        prev_document_signers=prev_document_signers,
    )
    recipients = ctx.recipients

    # Only the owner of a document can change recipients
    _validate_document_owner(document, company_edrpou=user.company_edrpou)

    ctx = await _validate_update_recipients_emails(
        conn=conn,
        ctx=ctx,
        role_id=user.role_id,
        document_id=document.id,
        document_owner_edrpou=document.edrpou_owner,
    )

    _validate_update_recipients_document_state(document, ctx=ctx)

    await _validate_required_fields_on_update(
        conn=conn,
        document=document,
        ctx=ctx,
        document_settings=document_settings,
    )

    # Validate that input recipients params are correctly set
    if ctx.is_internal:
        await validate_recipient_params_for_internal(
            recipient=recipients[0],
            document_owner_edrpou=document.edrpou_owner,
        )

    elif ctx.is_empty:
        await validate_recipients_params_for_bilateral_empty(
            document=document,
        )

    elif ctx.is_bilateral:
        await validate_recipients_params_for_bilateral(
            recipients=recipients,
            document_owner_edrpou=document.edrpou_owner,
        )
    elif ctx.is_multilateral:
        if await get_version_count(conn=conn, document_id=document.id) > 1:
            raise InvalidRequest(
                reason=_('Версійний документ не може бути багатостороннім'),
            )
        await validate_recipients_params_for_multilateral(
            recipients=recipients,
            document_owner_edrpou=document.edrpou_owner,
        )

    return ctx


async def _validate_update_access_settings(
    conn: DBConnection,
    data: DocumentAccessSettingsSchema | None,
    user: User,
    document: Document,
) -> UpdateDocumentAccessSettingsCtx | None:
    """
    Validate update access settings that the company has for a document.
    """

    settings = data
    if not settings:
        return None

    new_level: DocumentAccessLevel = settings.level

    prev_level = await db.select_document_access_level(
        conn=conn,
        document_id=document.id,
        edrpou=user.company_edrpou,
    )
    if new_level == prev_level:
        return None

    recipients = await select_document_recipients(conn=conn, document_id=document.id)

    if not (
        user.is_admin
        # is owner/uploader of the document
        or user.role_id == document.role_id
        # is direct recipient of the document
        or any(r.edrpou != document.edrpou_owner and r.has_email(user.email) for r in recipients)
    ):
        raise InvalidRequest(
            reason=_(
                'Змінити налаштування доступу до документа може тільки власник документа, '
                'отримувач документа або адміністратор компанії'
            ),
        )

    # Don't allow changing a document to private if this document has linked documents
    if new_level == DocumentAccessLevel.private and await db.exist_linked_documents(
        conn=conn,
        document_id=document.id,
        company_edrpou=user.company_edrpou,
    ):
        raise Error(
            code=Code.invalid_document_link,
            reason=_(
                'Наразі не підтримується звʼязування для приватних документів. '
                'Змініть налаштування приватності документів та спробуйте ще раз'
            ),
        )

    return UpdateDocumentAccessSettingsCtx(
        prev_level=prev_level,
        new_level=new_level,
    )


async def validate_document_access_settings_on_upload(
    settings: DocumentAccessSettingsSchema,
) -> UploadDocumentAccessSettingsCtx:
    """
    Validate document access settings that the company has for a document
    """
    return UploadDocumentAccessSettingsCtx(level=settings.level)


async def _validate_update_signers(
    conn: DBConnection,
    user: User,
    data: UpdateDocumentSchema,
    document: Document,
    signers_source: SignersSource,
) -> UpdateSignersData | None:
    """
    Validate update signers for document on update.
    """
    entities: list[DocumentSignerEntitySchema]
    parallel_signing: bool
    if data.update_sign_process:
        # Old way to pass signer data
        parallel_signing = data.parallel_signing or True
        entities = data.signer_entities or []
    elif data.signers_settings:
        # New way to pass signer data
        settings = data.signers_settings
        parallel_signing = settings.parallel_signing
        entities = settings.entities
    else:
        return None

    from app.uploads.validators import validate_signer_roles

    previous_signers = await select_document_signers_extended(
        conn=conn,
        document_id=document.id,
        edrpou=user.company_edrpou,
    )

    # WARN: we are selecting signatures from all document recipients,
    # not only from the current user company.
    signatures = await select_signatures(conn=conn, document_ids=[document.id])

    await validate_ability_edit_signers(
        conn=conn,
        document=document,
        user=user,
        previous_signers=previous_signers,
    )

    signer_groups = []
    signers = []
    group_role_map: dict[str, list[str]] = {}

    for entity in entities:
        if entity.type == 'group':
            signer_groups.append(entity.id)
        elif entity.type == 'role':
            signers.append(entity.id)
        else:
            t.assert_never(entity.type)

    if signer_groups:
        group_role_map = await get_group_members_by_group_ids(
            conn=conn,
            group_ids=signer_groups,
            company_id=user.company_id,
        )
        if set(group_role_map.keys()) != set(signer_groups):
            raise InvalidRequest(
                reason=_('Неможливо додати підписантом групу без учасників'),
                details={'empty_groups': list(set(signer_groups) - set(group_role_map.keys()))},
            )
        for signer_role_ids in group_role_map.values():
            signers.extend(signer_role_ids)

    signers = await validate_signer_roles(conn, user, signers)
    signers_set = set(signers)

    await _validate_already_signed_signers_change(
        conn=conn,
        user=user,
        signers=signers,
        signatures=signatures,
    )

    signer_entities = []
    for entity in entities:
        if entity.type == 'group':
            group_id: str = entity.id
            roles_ids = group_role_map.get(group_id, [])
            roles_ids = [role_id for role_id in roles_ids if role_id in signers_set]

            signer_entity = UpdateSignersDataSignerEntity(
                id=entity.id,
                type=UpdateSignersDataSignerType.group,
                role_ids=tuple(roles_ids),
            )
            signer_entities.append(signer_entity)

        elif entity.type == 'role':
            role_id: str = entity.id

            # Ignore signers that are not coworkers of the current user
            if role_id not in signers_set:
                continue

            signer_entity = UpdateSignersDataSignerEntity(
                id=entity.id,
                type=UpdateSignersDataSignerType.role,
                role_ids=(),
            )
            signer_entities.append(signer_entity)

    return UpdateSignersData(
        signer_entities=signer_entities,
        is_parallel=parallel_signing,
        signers_source=signers_source,
    )


async def _validate_update_reviews(
    conn: DBConnection,
    user: User,
    data: ReviewsSettingsSchema | None,
    document_id: str,
    reviewers_source: ReviewRequestSource,
) -> ReplaceReviewRequestsCtx | None:
    from app.reviews.validators import validate_replace_review_requests

    if data is None:
        return None

    settings = data
    settings.reviewers = unique_list_with_original_order(settings.reviewers)

    return await validate_replace_review_requests(
        conn=conn,
        user=user,
        document_id=document_id,
        data=settings,
        reviewers_source=reviewers_source,
    )


async def _validate_update_viewers(
    conn: DBConnection,
    data: ViewersSettingsSchema | None,
    user: User,
    document_id: str,
) -> UpdateViewersData | None:
    """Validate update viewers for document on update.
    Note: Currently only adding new viewers is supported
    """

    settings = data
    if settings is None:
        return None

    add_reviewers = settings.add_viewers
    remove_reviewers = settings.remove_viewers
    replace_reviewers = settings.replace_viewers

    # Check if we have any viewers to process
    # Note: empty list for replace_viewers should be treated as "replace with empty list"
    if not any(
        [add_reviewers is not None, remove_reviewers is not None, replace_reviewers is not None]
    ):
        return None

    # list[str] is deprecated, use dict instead
    # but list[str] is still supported for backward compatibility
    # for external integrations and document automations
    is_old_format = True
    for items in chain(add_reviewers or [], remove_reviewers or [], replace_reviewers or []):
        if isinstance(items, ViewerEntitySchema):
            is_old_format = False
            break

    if is_old_format:
        action: UpdateViewersAction
        if add_reviewers is not None:
            roles_ids = t.cast(list[str], add_reviewers)
            action = UpdateViewersAction.add
        elif remove_reviewers is not None:
            roles_ids = t.cast(list[str], remove_reviewers)
            action = UpdateViewersAction.remove
        elif replace_reviewers is not None:
            roles_ids = t.cast(list[str], replace_reviewers)
            action = UpdateViewersAction.replace
        else:
            return None

        roles_ids = unique_list_with_original_order(roles_ids)
        roles = await validate_coworkers_roles_ids(
            conn=conn,
            roles_ids=roles_ids,
            company_edrpou=user.company_edrpou,
            exclude_deleted_roles=True,
        )

        return UpdateViewersData(
            action=action,
            entities=[
                UpdateViewersEntity(id=role.id, type=UpdateViewersEntityType.role) for role in roles
            ],
        )

    # New format
    if add_reviewers is not None:
        add_reviewers = t.cast(list[ViewerEntitySchema], add_reviewers)
        action = UpdateViewersAction.add
        roles_ids = [item.id for item in add_reviewers if item.type == UpdateViewersEntityType.role]
        groups_ids = [
            item.id for item in add_reviewers if item.type == UpdateViewersEntityType.group
        ]
    elif remove_reviewers is not None:
        remove_reviewers = t.cast(list[ViewerEntitySchema], remove_reviewers)
        action = UpdateViewersAction.remove
        roles_ids = [
            item.id for item in remove_reviewers if item.type == UpdateViewersEntityType.role
        ]
        groups_ids = [
            item.id for item in remove_reviewers if item.type == UpdateViewersEntityType.group
        ]
    elif replace_reviewers is not None:
        replace_reviewers = t.cast(list[ViewerEntitySchema], replace_reviewers)
        action = UpdateViewersAction.replace
        roles_ids = [
            item.id for item in replace_reviewers if item.type == UpdateViewersEntityType.role
        ]
        groups_ids = [
            item.id for item in replace_reviewers if item.type == UpdateViewersEntityType.group
        ]
    else:
        return None

    if groups_ids:
        await validate_groups_exists(
            conn=conn,
            group_ids=groups_ids,
            company_id=user.company_id,
        )

        # make sure that group is not added already
        if action == UpdateViewersAction.add:
            existing_document_accesses = await select_group_document_accesses(
                conn=conn,
                document_ids=[document_id],
            )
            if existing := (
                set(groups_ids) & {access.group_id for access in existing_document_accesses}
            ):
                raise InvalidRequest(
                    reason=_('Група вже має доступ до документу'),
                    details={'group_ids': list(existing)},
                )

        groups_members = await get_group_members_by_group_ids(
            conn=conn,
            group_ids=groups_ids,
        )

    return UpdateViewersData(
        action=action,
        entities=[
            UpdateViewersEntity(
                id=role_id,
                type=UpdateViewersEntityType.role,
            )
            for role_id in roles_ids
        ]
        + [
            UpdateViewersEntity(
                id=group_id,
                type=UpdateViewersEntityType.group,
                role_ids=tuple(groups_members[group_id]),
            )
            for group_id in groups_ids
        ],
    )


async def _validate_update_tags(
    conn: DBConnection,
    data: TagsSettingsSchema | None,
    user: User,
    document: Document,
) -> UpdateTagsSettings | None:
    """Validate ability to assign tags to document"""

    settings = data
    if settings is None:
        return None

    from app.tags.db import select_tags_by_documents
    from app.tags.validators import (
        soft_validate_create_new_tags,
        validate_tags_access,
    )

    # Backward compatible format where we can pass only tags ids,
    # and it should work as "add" action
    tags_ids_old = settings.tags
    if tags_ids_old is not None:
        # Check tags to add and skip already connected tags
        exists_tags = await select_tags_by_documents(
            conn=conn,
            documents_ids=[document.id],
            companies_ids=[user.company_id],
        )
        tags_to_add = set(tags_ids_old) - {tag.id for tag in exists_tags}
        await validate_tags_access(conn, tags_ids=list(tags_to_add), user=user)

        return UpdateTagsSettings(
            tags_ids=tags_ids_old,
            new_tags=[],
            action=UpdateTagsAction.add,
        )

    # New format: add, remove, replace actions.
    # Currently only one action at a time is supported for simplicity
    if add_settings := settings.add:
        new_tags_ids = list(add_settings.tags_ids)
        new_tags_names = list(add_settings.new_tags)

        create_ctx = await soft_validate_create_new_tags(
            conn=conn,
            tags_names=new_tags_names,
            user=user,
        )
        new_tags_ids.extend(create_ctx.tags_ids)
        new_tags_names = create_ctx.tags_names

        # Check tags to add and skip already connected tags
        existed_tags = await select_tags_by_documents(
            conn=conn,
            documents_ids=[document.id],
            companies_ids=[user.company_id],
        )
        tags_to_add = set(new_tags_ids) - {tag.id for tag in existed_tags}
        await validate_tags_access(conn, tags_ids=list(tags_to_add), user=user)

        return UpdateTagsSettings(
            tags_ids=new_tags_ids,
            new_tags=new_tags_names,
            action=UpdateTagsAction.add,
        )

    if remove_settings := settings.remove:
        new_tags_ids = list(remove_settings.tags_ids)
        await validate_tags_access(conn, tags_ids=new_tags_ids, user=user)

        return UpdateTagsSettings(
            tags_ids=new_tags_ids,
            new_tags=[],
            action=UpdateTagsAction.remove,
        )

    if replace_settings := settings.replace:
        new_tags_ids = list(replace_settings.tags_ids)
        new_tags_names = list(replace_settings.new_tags)

        create_ctx = await soft_validate_create_new_tags(
            conn=conn,
            tags_names=new_tags_names,
            user=user,
        )
        new_tags_ids.extend(create_ctx.tags_ids)
        new_tags_names = create_ctx.tags_names

        # Rules:
        #  - check tags that should be added
        #  - check tags that should be removed
        #  - skip tags that are already connected
        existed_tags = await select_tags_by_documents(
            conn=conn,
            documents_ids=[document.id],
            companies_ids=[user.company_id],
        )
        existed_ids = {tag.id for tag in existed_tags}
        tags_to_add = set(new_tags_ids) - existed_ids
        tags_to_delete = existed_ids - set(new_tags_ids)
        tags_to_check = list(tags_to_add | tags_to_delete)
        await validate_tags_access(conn, tags_ids=tags_to_check, user=user)

        return UpdateTagsSettings(
            tags_ids=new_tags_ids,
            new_tags=new_tags_names,
            action=UpdateTagsAction.replace,
        )

    logger.error('Invalid tags settings', extra={'settings': settings})
    raise InvalidRequest(reason=_('Невірний формат даних для тегів'))


async def _validate_update_versioned(
    conn: DBConnection,
    data: VersionSettingsSchema | None,
    document: Document,
    user: User,
) -> UpdateVersionedCtx | None:
    """
    Validate update document version settings. Currently, it supports only
    changing from versioned to non-versioned and vice versa.
    """

    settings = data
    if settings is None:
        return None

    is_versioned_expected = settings.is_versioned
    versions = await select_document_versions(conn, document_ids=[document.id])
    versions_count = len(versions)
    is_versioned_now = versions_count > 0

    # Ignore request when nothing to change
    if is_versioned_expected == is_versioned_now:
        return None

    _validate_document_owner(document, company_edrpou=user.company_edrpou)

    if document.status not in {DocumentStatus.uploaded, DocumentStatus.ready_to_be_signed}:
        raise InvalidRequest(
            reason=_('Неможливо змінити версійність документу, після початку процесу підписання')
        )

    if not is_versioned_expected:
        # Allow only when a document has only one version because we don't know what to do with
        # different versions of review and document content (remove? keep last? or something else?)
        if versions_count > 1:
            raise InvalidRequest(
                reason=_(
                    'Неможливо прибрати версійність документу, бо він має більше однієї версії'
                )
            )

        # Allow only when a document has no sent versions because in this case, we need to revert
        # sending a document to the recipient, and we don't know to do it properly.
        if any(version.is_sent for version in versions):
            raise InvalidRequest(
                reason=_(
                    'Неможливо прибрати версійність документу, бо версія документу вже '
                    'була відправлена контрагенту'
                )
            )

    return UpdateVersionedCtx(is_versioned=is_versioned_expected)


async def validate_update_document(
    conn: DBConnection,
    user: User,
    raw_data: DataDict,
    request_source: Source,
    signers_source: SignersSource,
    reviewers_source: ReviewRequestSource,
) -> UpdateDocumentOptions:
    """Validate change metadata action."""

    validated_data = validators.validate_pydantic(UpdateDocumentSchema, raw_data)

    document = await validate_document_exists(conn, document_id=validated_data.document_id)
    await validate_document_access(conn, user, document.id)

    document_settings = await _validate_update_general_info(
        data=validated_data.document_settings,
        document=document,
        user=user,
        request_source=request_source,
    )
    parameters_settings = await _validate_update_parameters(
        conn=conn,
        user=user,
        data=validated_data.parameters_settings,
        document=document,
    )
    recipients_settings = await _validate_update_recipients(
        conn=conn,
        data=validated_data.recipients_settings,
        user=user,
        document=document,
        document_settings=document_settings,
    )
    signers_settings = await _validate_update_signers(
        conn=conn,
        user=user,
        data=validated_data,
        document=document,
        signers_source=signers_source,
    )
    access_settings = await _validate_update_access_settings(
        conn=conn,
        data=validated_data.access_settings,
        user=user,
        document=document,
    )
    reviews_settings = await _validate_update_reviews(
        conn=conn,
        user=user,
        data=validated_data.reviews_settings,
        document_id=document.id,
        reviewers_source=reviewers_source,
    )
    viewers_settings = await _validate_update_viewers(
        conn, validated_data.viewers_settings, user, document.id
    )
    tags_settings = await _validate_update_tags(conn, validated_data.tags_settings, user, document)
    version_settings = await _validate_update_versioned(
        conn=conn,
        data=validated_data.version_settings,
        document=document,
        user=user,
    )

    return UpdateDocumentOptions(
        document=document,
        user=user,
        document_settings=document_settings,
        access_settings=access_settings,
        signers_settings=signers_settings,
        reviews_settings=reviews_settings,
        viewers_settings=viewers_settings,
        parameters_settings=parameters_settings,
        tags_settings=tags_settings,
        recipients_settings=recipients_settings,
        version_settings=version_settings,
    )


async def validate_open_access(
    conn: DBConnection,
    user: User,
    data: DataDict,
) -> OpenAccessOptions:
    valid_data = validators.validate_pydantic(OpenAccessSchema, data)
    document_id = valid_data.document_id
    roles_ids = valid_data.roles_ids
    comment = valid_data.comment

    document = await validate_document_access(conn, user, document_id)
    roles = await validate_coworkers_roles_ids(
        conn=conn,
        roles_ids=roles_ids,
        company_edrpou=user.company_edrpou,
        exclude_deleted_roles=True,
    )
    listing_data = ListingDataAggregator()
    for role in roles:
        listing_data.add(
            access_edrpou=role.company_edrpou,
            document_id=document_id,
            role_id=role.id_,
            source=AccessSource.viewer,
        )

    return OpenAccessOptions(
        user=user,
        comment=comment,
        document=document,
        roles=roles,
        data=listing_data.as_db(),
    )


async def validate_delete_document(
    conn: DBConnection, data: DataDict, user: User
) -> DocumentWithUploader:
    """Validate delete document action."""

    valid_data = validators.validate_pydantic(DeleteDocumentSchema, data)
    document = await validate_document_exists(conn, document_id=valid_data.document_id)
    check_delete_permission(user)
    await validate_document_access(conn, user, document_id=document.id)

    # Check if document can be deleted
    signatures = await select_signatures(conn, document_ids=[document.id])
    recipients = await select_document_recipients(conn, document_id=document.id)
    delete_document_settings = await select_delete_document_settings(
        conn=conn,
        document_ids=[document.id],
    )
    is_archived = await is_document_archived(
        conn=conn,
        document_id=document.id,
        company_id=user.company_id,
    )
    registered_companies = await select_registered_companies_edrpous(
        conn=conn,
        companies_edrpous=[r.edrpou for r in recipients],
    )

    has_unprocessed_delete_request = await get_documents_pending_delete_requests_mapping(
        conn=conn,
        documents_ids=[document.id],
    )

    is_signed_by_user = any(s.role_id == user.role_id for s in signatures)
    is_signed_by_recipient = any(s.owner_edrpou != document.edrpou_owner for s in signatures)

    result = can_delete_document(
        current_user=user,
        document=document,
        is_signed_by_user=is_signed_by_user,
        is_signed_by_recipient=is_signed_by_recipient,
        document_recipients=recipients,
        registered_recipients_edrpous=registered_companies,
        delete_document_setting=delete_document_settings[0] if delete_document_settings else None,
        has_unprocessed_delete_request=bool(has_unprocessed_delete_request.get(document.id)),
        is_archived=is_archived,
    )
    if not result.is_ok:
        if error := result.error_optional:
            raise error
        raise AccessDenied(reason=_('Неможливо видалити документу'))

    return document


async def validate_document_exists(
    conn: DBConnection, data: StrDict | None = None, document_id: str | None = None
) -> DocumentWithUploader:
    """
    Ensure that a document exists in the Database by given ID.

    Args:
        conn: Database connection instance
        data: Dict containing document_id (deprecated, use document_id parameter instead)
        document_id: UUID of the document to validate
    """
    if not document_id:
        assert data is not None, 'data expected'
        document_id = data['document_id']

    document = await select_document_by_id(conn, document_id)
    if not document:
        raise DoesNotExist(Object.document, id=document_id)
    return DocumentWithUploader.from_row(document)


def _validate_document_non_finished(document: Document) -> None:
    """Ensure that document non-finished yet."""
    if document.status_id == DocumentStatus.finished.value:
        raise Error(Code.invalid_document_status, details={'status': document.status_id})


def _validate_document_owner(document: Document, *, company_edrpou: str) -> None:
    """
    Ensure that a given company is an owner of the document.
    """
    if document.edrpou_owner != company_edrpou:
        logger.info(
            msg='Document owner is not the same as given company',
            extra={
                'document_owner_edrpou': document.edrpou_owner,
                'company_edrpou': company_edrpou,
            },
        )
        raise AccessDenied(
            reason=_(
                'Неможливо виконати дію над документом, оскільки компанія не є власником документа'
            )
        )


def validate_self_recipient(
    recipient: RecipientsEmailsOptions,
    user: AuthUser | User | None,
) -> None:
    """Do not allow self Email/EDRPOU as recipient."""
    if not user:
        return

    emails = [email.lower() for email in recipient.emails]

    if (
        recipient.emails
        and user.email
        and user.email.lower() in emails
        and user.company_edrpou == recipient.edrpou
    ):
        raise Error(Code.reject_self_recipient)
    return


def validate_owner_not_recipient(
    *,
    edrpou_owner: str,
    edrpou_recipient: str,
) -> None:
    """Ensure that document owner is not a recipient."""
    if edrpou_owner == edrpou_recipient:
        raise InvalidRequest(
            reason=_('Власник документу не може бути одержувачем'),
        )


def validate_document_status_on_send(document: Document, company_edrpou: str) -> bool:
    """
    Validate the ability to send a document by document status

    In case validation fails, log the reason and return False to silently ignore the sending of
    the document. We ignore sending the document to ensure compatibility with old frontend code
    and API integrations that manually send a document after signing. This is now performed
    automatically by the `autosend_document_on_sign` function on the backend side after the
    signing process, but we still need to not break the old code and integrations.
    """
    status_id = document.status_id

    # Histored documents can't be sent
    if status_id == DocumentStatus.histored.value:
        return False

    is_document_owner = company_edrpou == document.edrpou_owner
    is_one_sign = is_one_sign_document(document)
    is_3p = is_first_sign_by_recipient_document(document)

    # Validate document status
    not_approved = status_id != APPROVED
    not_signed = status_id != SIGNED

    log_extra = {
        'document_id': document.id,
        'document_status_id': status_id,
        'company_edrpou': company_edrpou,
        'document_owner_edrpou': document.edrpou_owner,
        'is_one_sign': is_one_sign,
        'is_3p': is_3p,
        'not_approved': not_approved,
        'not_signed': not_signed,
    }

    # Sending by owner
    if is_document_owner:
        # Owner able to send normal signed/approved document
        if not is_3p and ((is_one_sign and not_approved) or (not is_one_sign and not_signed)):
            logger.info('Sending document is ignored', extra={**log_extra, 'case': 1})
            return False

        # -//- 3P uploaded/ready/approved document
        if is_3p and (
            (is_one_sign and status_id > SENT)
            or (not is_one_sign and status_id > SENT and not_approved)
        ):
            logger.info('Sending document is ignored', extra={**log_extra, 'case': 2})
            return False

    # Sending by recipient
    else:
        # Recipient able to send normal approved document
        if not is_3p and not_approved:
            logger.info('Sending document is ignored', extra={**log_extra, 'case': 3})
            return False

        # -//- 3P signed/approved document
        if is_3p and ((is_one_sign and not_approved) or (not is_one_sign and not_signed)):
            logger.info('Sending document is ignored', extra={**log_extra, 'case': 4})
            return False

    return True


async def validate_required_recipients(
    conn: DBConnection,
    *,
    document: DocumentWithUploader,
    user: User | None,
    recipient_emails: list[str],
    recipient_edrpou: str | None,
    is_emails_hidden: bool,
) -> RecipientsEmailsOptions:
    """
    Validate required document recipients on document send. Document recipient can
    be passed from frontend, can be hidden (stored in redis), can be stored in
    document_recipients_table or deprecated fields document.edrpou_recipient and
    document.email_recipient
    """
    # If user pass recipient edrpou and email from fronted, than use this data
    if recipient_edrpou and recipient_emails:
        return RecipientsEmailsOptions(
            edrpou=recipient_edrpou,
            emails=recipient_emails,
            is_emails_hidden=False,
        )

    if is_emails_hidden and recipient_edrpou and user:
        # such as already have edrpou, find hidden recipient emails
        recipient_emails = await validate_hidden_recipient(
            conn=conn,
            role_id=user.role_id,
            document_id=document.id,
            recipient_edrpou=recipient_edrpou,
        )
    else:
        # Fill recipient edrpou and emails from database
        recipient = await select_bilateral_document_recipient(
            conn=conn,
            document_id=document.id,
            document_owner_edrpou=document.edrpou_owner,
        )
        if recipient and recipient.edrpou and recipient.emails:
            # firstly from document recipient table
            recipient_edrpou = recipient.edrpou
            recipient_emails = recipient.emails
            is_emails_hidden = recipient.is_emails_hidden
        else:
            # then from old fields document.edrpou_recipient and document.email_recipient
            recipient_edrpou = document.edrpou_recipient
            recipient_emails = split_comma_separated_emails(document.email_recipient)

    # Recipient EDRPOU & email should be filled
    if not recipient_edrpou or not recipient_emails:
        raise InvalidRequest(edrpou=FIELD_IS_REQUIRED, emails=FIELD_IS_REQUIRED)

    options = RecipientsEmailsOptions(
        edrpou=recipient_edrpou,
        emails=recipient_emails,
        is_emails_hidden=is_emails_hidden,
    )

    validate_self_recipient(options, user)
    return options


def _validate_edrpou_send_document(
    company_edrpou: str,
    user: User | None,
) -> None:
    if user and user.company_edrpou != company_edrpou:
        raise AccessDenied(reason=_('Користувач не може надсилати цей документ'))


async def get_document_recipient_options(
    conn: DBConnection,
    document: DocumentWithUploader,
) -> RecipientsEmailsOptions:
    recipient = await select_bilateral_document_recipient(
        conn=conn,
        document_id=document.id,
        document_owner_edrpou=document.edrpou_owner,
    )
    if not recipient:
        raise InvalidRequest(reason=_('Контаргент не призначено на документ'))

    return RecipientsEmailsOptions(
        edrpou=recipient.edrpou,
        emails=recipient.emails,
        is_emails_hidden=recipient.is_emails_hidden,
    )


async def validate_charge_document_on_send(
    conn: DBConnection,
    *,
    user: User | None,
    document: DocumentWithUploader,
    recipient_edrpou: str | None,
    is_internal_bill_sending: bool,
    is_sending_to_recipient: bool,
) -> ChargeDocumentContext | None:
    """
    Validate if you can charge a document on sending.

    On "send" action we charge only "bilateral" documents, "internal" documents are charged on
    "sign" action. See "validate_charge_document_on_sign" for more details about that.
    """

    if not user:
        # TODO: add charging for anonymous sending
        logger.info(
            msg='Charging is not supported for anonymous sending',
            extra={'document_id': document.id},
        )
        return None

    # Internal document is charged on sign
    if document.is_internal:
        return None

    # Multilateral documents are free for now
    if document.is_multilateral:
        return None

    is_owner_sending = user.company_edrpou == document.edrpou_owner

    # Sending can happen between coworkers or recipients (is_sending_to_recipient) and
    # owner or recipient (is_owner_sending). We charge only when an owner sends a document to
    # the recipient.
    if not (is_owner_sending and is_sending_to_recipient):
        return None

    # Do not charge a document in status `sent` to avoid double charging.
    # TODO: understand and document when such case can happen at all
    if document.status_id == DocumentStatus.sent.value:
        logger.info(
            msg='Attempt to charge document in status "sent"',
            extra={
                'document_id': document.id,
                'status_id': document.status_id,
                'company_edrpou': user.company_edrpou,
                'document_owner_edrpou': document.edrpou_owner,
                'recipient_edrpou': recipient_edrpou,
                'is_internal_bill_sending': is_internal_bill_sending,
                'is_sending_to_recipient': is_sending_to_recipient,
                'is_owner_sending': is_owner_sending,
            },
        )
        return None

    payer_id = await validate_document_payer(conn, document, recipient_edrpou=recipient_edrpou)
    return await validate_charge_document(
        conn=conn,
        role_id=user.role_id,
        company_id=payer_id,
        document_id=document.id,
        document_is_internal=document.is_internal,
        document_vendor=document.vendor,
        document_source=document.source,
        document_is_internal_bill=is_internal_bill_sending,
    )


async def validate_send_document(
    conn: DBConnection,
    redis: Redis,
    data: DataDict,
    company_edrpou: str,
    document: DocumentWithUploader,
    user: User | None,
) -> SendDocumentOptions | None:
    """Validate ability of sending document.

    Check valid statuses for sending document.
    Also for first sending check that further recipient is valid one.

    Document should has one of next statuses:
    - Uploaded
    - Ready to be signed or sent
    - Sent
    - Signed (by owner in standard flow / by recipient in 3P flow)
    - Approved (by owner in 3P or one sign document flow / by recipient in
      standard flow)

    NOTE: this function only for bilateral documents. Internal documents don't support
    sending to recipients, and sending between coworkers happens on sign action. Multilateral
    documents have separate branch in "send_document" function, which doesn't call this validator.
    """
    _validate_edrpou_send_document(company_edrpou, user)

    valid_data = validators.validate_pydantic(SendDocumentSchema, data)
    valid_data = migrate_to_emails_list(data=valid_data, user=user)

    # `unpack` parameters for explicit using
    document_id = valid_data.document_id
    recipient_edrpou = valid_data.edrpou
    recipient_emails = valid_data.emails or []
    is_emails_hidden = valid_data.is_email_hidden

    is_document_owner = company_edrpou == document.edrpou_owner
    is_3p = is_first_sign_by_recipient_document(document)
    status_id = document.status_id
    is_one_sign = is_one_sign_document(document)

    # Deny changing recipient for internal documents
    if document.is_internal:
        raise Error(Code.invalid_action)

    is_valid_status = validate_document_status_on_send(document, company_edrpou)
    if not is_valid_status:
        return None

    signatures = await select_signatures(conn, [document.id])
    signers = await select_document_signers_extended(conn, document_id=document.id)
    next_status = get_document_next_status_sending(
        document=document,
        signatures=signatures,
        document_signers=signers,
    )

    if next_status != DocumentStatus.sent and document.status_id == next_status.value:
        logger.warning(
            msg='Attempt to send document without changing status',
            extra={
                'document_id': document_id,
                'edrpou': recipient_edrpou,
                'emails': recipient_emails,
                'is_3p': is_3p,
                'is_one_sign': is_one_sign,
                'next_status_id': next_status.value,
                'status_id': document.status_id,
            },
        )
        return None

    # This variable represents the state when the bilateral document should be actually sent
    # to a next recipient (owner/recipient), not to coworker
    is_sending_to_recipient: bool = (
        # Standard flow
        (not is_3p and ((status_id == SIGNED) or (is_one_sign and status_id == APPROVED)))
        # 3P flow
        or (
            is_3p
            and (
                (is_document_owner and int(status_id) < SIGNED)
                or (not is_document_owner and status_id == SIGNED)
            )
        )
    )

    charge_context = await validate_charge_document_on_send(
        conn=conn,
        user=user,
        document=document,
        recipient_edrpou=recipient_edrpou,
        is_sending_to_recipient=is_sending_to_recipient,
        is_internal_bill_sending=valid_data.is_internal_bill_sending,
    )

    # for document owner recipient options is required for understanding who must
    # receive document
    recipient: RecipientsEmailsOptions | None = None
    if is_sending_to_recipient:
        if is_document_owner:
            recipient = await validate_required_recipients(
                conn=conn,
                user=user,
                document=document,
                recipient_emails=recipient_emails,
                recipient_edrpou=recipient_edrpou,
                is_emails_hidden=is_emails_hidden,
            )

            # Only the owner of the document should be responsible for filling all required fields.
            await validate_required_fields(
                conn=conn,
                edrpous=[recipient.edrpou],
                document_title=document.title,
                document_number=document.number,
                document_date=document.date_document,
                document_amount=document.amount,
                category=document.category,
            )

        else:
            recipient = await get_document_recipient_options(conn, document)

    return SendDocumentOptions(
        document=document,
        next_status=next_status,
        uploader_company_id=document.uploaded_by_company_id,
        charge_context=charge_context,
        recipient=recipient,
        is_one_sign=is_one_sign,
        is_3p=is_3p,
        is_sending_to_recipient=is_sending_to_recipient,
        is_owner_sending=is_document_owner,
        company_edrpou=company_edrpou,
        is_internal_bill_sending=valid_data.is_internal_bill_sending,
    )


async def validate_documents_exists(
    conn: DBConnection,
    document_ids: list[str],
    error_code: Code = Code.documents_requested_found_mismatch,
) -> list[DBRow]:
    """Ensure that documents exists in database by given IDs."""
    documents = await select_documents_by_ids_with_company_info(conn, document_ids)
    selected_ids = {doc_item.id for doc_item in documents}
    if not selected_ids == set(document_ids):
        raise Error(
            error_code,
            details={'mismatch_docs': list(set(document_ids) - selected_ids)},
        )
    return documents


async def validate_delete_requests_exists(
    conn: DBConnection,
    user: AuthUser | User,
    delete_request_ids: list[str] | None = None,
    documents_ids: list[str] | None = None,
    is_cancel: bool = False,
) -> list[DBRow]:
    receiver_edrpou = None if is_cancel else user.company_edrpou
    delete_requests: list[DBRow] = []
    if documents_ids:
        delete_requests = await select_delete_requests_by(
            conn,
            documents_ids=documents_ids,
            receiver_edrpou=receiver_edrpou,
        )
        docs_ids = {del_request.document_id for del_request in delete_requests}
        if set(documents_ids) != docs_ids:
            raise Error(
                Code.delete_requests_mismatch,
                details={'mismatch_delete_request_ids': list(set(documents_ids) - docs_ids)},
            )

    elif delete_request_ids:
        delete_requests = await select_delete_requests_by_ids(conn, delete_request_ids)
        ids = {delete_request_item.id for delete_request_item in delete_requests}
        if set(delete_request_ids) != ids:
            raise Error(
                Code.delete_requests_mismatch,
                details={'mismatch_delete_request_ids': list(set(delete_request_ids) - ids)},
            )

    return delete_requests


async def validate_documents_access(
    conn: DBConnection,
    user: AuthUser | User,
    doc_ids: list[str],
) -> list[Document]:
    documents = await select_all(
        conn,
        (
            sa.select([document_table])
            .select_from(document_listing_join)
            .where(
                sa.and_(
                    document_table.c.id.in_(doc_ids),
                    get_documents_access_filters(user),
                )
            )
            .group_by(document_table.c.id)
        ),
    )

    if len({item.id for item in documents}) != len(set(doc_ids)):
        logger.warning(
            'No access to one or more documents',
            extra={
                'email': user.email,
                'user_company_edrpou': user.company_edrpou,
                'role_id': user.role_id,
                'document_ids': doc_ids,
                'documents': documents,
            },
        )
        reason = _('У вас немає доступу до всіх або деяких документів')
        raise AccessDenied(reason=reason)

    return [Document.from_row(doc) for doc in documents]


async def validate_add_document_children_web(
    conn: DBConnection,
    user: User,
    request: web.Request,
) -> AddDocumentChildrenCtx:
    company_edrpou: str = user.company_edrpou

    raw_data = await validators.validate_json_request(request)
    raw_data['parent_id'] = request.match_info['document_id']
    data = validators.validate_pydantic(AddDocumentChildrenSchema, raw_data)

    return await validate_add_document_children(
        conn=conn,
        parent_id=data.parent_id,
        children_ids=data.children_ids,
        company_edrpou=company_edrpou,
    )


async def validate_add_document_child_web(
    conn: DBConnection,
    user: User,
    request: web.Request,
) -> AddDocumentChildCtx:
    company_edrpou: str = user.company_edrpou
    raw_data = {
        'parent_id': request.match_info['document_id'],
        'child_id': request.match_info['child_id'],
    }
    data = validators.validate_pydantic(AddDocumentChildSchema, raw_data)

    return await validate_add_document_child(
        conn=conn,
        parent_id=data.parent_id,
        child_id=data.child_id,
        company_edrpou=company_edrpou,
    )


async def validate_delete_document_child_web(
    conn: DBConnection,
    user: User,
    request: web.Request,
) -> DeleteDocumentChildCtx:
    company_edrpou: str = user.company_edrpou
    raw_data = {
        'parent_id': request.match_info['document_id'],
        'child_id': request.match_info['child_id'],
    }
    data = validators.validate_pydantic(AddDocumentChildSchema, raw_data)

    return await validate_delete_document_child(
        conn=conn,
        parent_id=data.parent_id,
        child_id=data.child_id,
        company_edrpou=company_edrpou,
    )


async def validate_add_document_children(
    conn: DBConnection,
    *,
    parent_id: str,
    children_ids: list[str],
    company_edrpou: str,
) -> AddDocumentChildrenCtx:
    for child_id in children_ids:
        validate_itself_link(parent_id, child_id)

    await validate_company_documents_access(
        conn=conn,
        company_edrpou=company_edrpou,
        documents_ids=[parent_id, *children_ids],
    )

    await validate_links_not_private(
        conn=conn,
        company_edrpou=company_edrpou,
        documents_ids=[parent_id, *children_ids],
    )

    old_links_mapping = await validate_children_documents(
        conn=conn,
        parent_id=parent_id,
        company_edrpou=company_edrpou,
        children_ids=children_ids,
    )
    return AddDocumentChildrenCtx(
        parent_id=parent_id,
        children_ids=children_ids,
        company_edrpou=company_edrpou,
        old_links_mapping=old_links_mapping,
    )


async def validate_add_document_child(
    conn: DBConnection,
    *,
    parent_id: str,
    child_id: str,
    company_edrpou: str,
) -> AddDocumentChildCtx:
    validate_itself_link(parent_id, child_id)

    await validate_company_documents_access(
        conn=conn,
        company_edrpou=company_edrpou,
        documents_ids=[parent_id, child_id],
    )

    await validate_links_not_private(
        conn=conn,
        company_edrpou=company_edrpou,
        documents_ids=[parent_id, child_id],
    )

    await validate_child_documents_count(conn, parent_id, company_edrpou=company_edrpou)

    link = await select_documents_link(
        conn=conn,
        company_edrpou=company_edrpou,
        parent_id=parent_id,
        child_id=child_id,
    )

    await validate_one_parent_document(
        conn=conn,
        company_edrpou=company_edrpou,
        parent_id=parent_id,
        child_id=child_id,
    )

    return AddDocumentChildCtx(
        parent_id=parent_id,
        child_id=child_id,
        company_edrpou=company_edrpou,
        link=link,
    )


async def validate_delete_document_child(
    conn: DBConnection,
    *,
    parent_id: str,
    child_id: str,
    company_edrpou: str,
) -> DeleteDocumentChildCtx:
    await validate_company_documents_access(
        conn=conn,
        company_edrpou=company_edrpou,
        documents_ids=[parent_id, child_id],
    )

    return DeleteDocumentChildCtx(
        parent_id=parent_id,
        child_id=child_id,
        company_edrpou=company_edrpou,
    )


async def validate_company_document_access(
    conn: DBConnection,
    document_id: str,
    company_edrpou: str,
) -> None:
    if not await exists(
        conn=conn,
        select_from=listing_table,
        clause=(
            sa.and_(
                listing_table.c.access_edrpou == company_edrpou,
                listing_table.c.document_id == document_id,
            )
        ),
    ):
        raise AccessDenied(reason=_('Доступ до документу заборонено'))


async def validate_company_documents_access(
    conn: DBConnection,
    documents_ids: list[str],
    company_edrpou: str,
) -> None:
    items = await select_all(
        conn=conn,
        query=(
            sa.select([listing_table.c.document_id])
            .select_from(listing_table)
            .where(
                sa.and_(
                    listing_table.c.access_edrpou == company_edrpou,
                    listing_table.c.document_id.in_(documents_ids),
                )
            )
        ),
    )

    existed_documents_ids = {item.document_id for item in items}
    documents_ids_set = set(documents_ids)

    if documents_ids_set.difference(existed_documents_ids):
        raise AccessDenied(reason=_('Доступ до деяких документів заборонено'))


async def validate_document_access(
    conn: DBConnection,
    user: AuthUser | User,
    document_id: str,
) -> Document:
    select_from = listing_table.join(
        document_table,
        sa.and_(
            document_table.c.id == document_id,
            listing_table.c.document_id == document_table.c.id,
        ),
    )

    clause = get_documents_access_filters(user)

    document = await select_one(
        conn=conn,
        query=(sa.select([document_table]).select_from(select_from).where(clause)),
    )
    if not document:
        raise AccessDenied(reason=_('Доступ до документу заборонено'))
    return Document.from_row(document)


async def validate_create_delete_request(
    conn: DBConnection, user: AuthUser | User, data: DataDict
) -> tuple[list[DBRow], str]:
    """
    Initiate delete request may be done by:
    - If document already signed by other company
    - If document is finished (except for edi document, see document_revoke)
    - If document is rejected
    - If document has flow
    - If document uploaded by recipient

    Details: https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/14975010/QA+checklist+2.0
    """
    valid_data = validators.validate_pydantic(DeleteRequestSchema, data)
    if valid_data.message.isspace():
        raise InvalidRequest(reason=_('Поле для вводу не містить жодних символів.'))
    check_delete_permission(user)

    document_ids = valid_data.document_ids
    documents_rows = await validate_documents_exists(
        conn, document_ids, error_code=Code.one_or_more_documents_not_found
    )

    delete_requests = await select_delete_requests_by_docs_ids(conn, documents_ids=document_ids)
    if delete_requests:
        new_requests = {
            del_req.document_id
            for del_req in delete_requests
            if del_req.status == DeleteRequestStatus.new
        }
        document_ids = list(set(document_ids) - new_requests)
        documents_rows = [doc for doc in documents_rows if doc.id in document_ids]

    versioned_map = await count_document_versions(
        conn,
        document_ids=document_ids,
        is_sent=True,
    )

    if documents_rows:
        documents = [Document.from_row(row) for row in documents_rows]
        await validate_documents_access(conn, user, list(document_ids))
        if not all(
            _is_allowed_for_delete_request(doc=doc, versioned_map=versioned_map)
            for doc in documents
        ):
            raise Error(Code.not_all_documents_in_finished_status)
    return documents_rows, valid_data.message


def _is_allowed_for_delete_request(doc: Document, versioned_map: dict[str, int]) -> bool:
    """
    Check ability to create delete-request

    Note: Before call this function you should validate user access to document

    Who can delete document (mandatory general condition):
    - the administrator or user with permission `can_delete_document`
    External documents:
      - status sent (7002) and signed_and_sent (7004) (when it's incoming document)
      - flow (7010)
      - signed (7003) and approved (7007)
      - finished (7008) (except for edi document)
      - rejected (7006)

    In other case we can't create delete-request
    Details: https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/27328529/QA+checklist+3.0
    """
    # versioned document is sent
    if versioned_map[doc.id]:
        return True
    if doc.status in (
        DocumentStatus.sent,
        DocumentStatus.signed_and_sent,
        DocumentStatus.signed,
        DocumentStatus.approved,
        DocumentStatus.reject,
        DocumentStatus.flow,
    ):
        return True
    if doc.status == DocumentStatus.finished:
        if doc.source.is_from_edi and get_flag(FeatureFlags.ENABLE_REVOKE_DOCUMENTS_FOR_EDI):
            return False
        return True

    return False


def validate_delete_request_accept(user: AuthUser | User, delete_requests: list[DBRow]) -> None:
    """Validate that the initiator of the deletion request cannot accept it."""
    for item in delete_requests:
        if user.company_edrpou == item.initiator_edrpou:
            raise Error(Code.not_all_delete_request_allowed)


async def base_delete_request_validation(
    conn: DBConnection,
    user: AuthUser | User,
    data: DataDict,
) -> list[DBRow]:
    check_delete_permission(user)

    delete_requests = await validate_delete_requests_exists(
        conn,
        user=user,
        delete_request_ids=data.get('delete_request_ids'),
        documents_ids=data.get('documents_ids'),
    )

    validate_delete_request_accept(user, delete_requests)

    await validate_documents_access(
        conn, user, [delete_request.document_id for delete_request in delete_requests]
    )
    return delete_requests


def check_delete_permission(user: AuthUser | User) -> None:
    """
    Check user permission to do any action with delete-requests (create, cancel, approve or reject)
    or delete document.
    """
    if not has_permission(
        user, {'can_delete_document', 'can_delete_document_extended'}, all_permissions=False
    ):
        raise AccessDenied(reason=_('Немає прав для видалення документу.'))


async def validate_cancel_delete_request(
    conn: DBConnection, user: AuthUser | User, data: DataDict
) -> list[DBRow]:
    """
    Cancel delete request can user with admin role / user with `can_delete_document` permission
    ** the user must have access to all documents
    """
    if not data:
        raise Error(Code.invalid_json_request)

    check_delete_permission(user)

    delete_requests = await validate_delete_requests_exists(
        conn,
        user=user,
        documents_ids=data['documents_ids'],
        is_cancel=True,
    )

    await validate_documents_access(
        conn, user, [delete_request.document_id for delete_request in delete_requests]
    )
    return delete_requests


async def validate_accept_delete_request(
    conn: DBConnection, user: AuthUser | User, data: dict[t.Any, t.Any], source: Source
) -> tuple[list[DBRow], list[str]]:
    if source == Source.api_public:
        # in public api we generate data ourselves, so it does not need to be validated
        documents_ids = data['documents_ids']
        delete_requests = await base_delete_request_validation(conn, user, data)
        docs_to_delete: set[str] = set()
        for del_request in delete_requests:
            if (
                del_request.status == DeleteRequestStatus.new
                and del_request.receiver_edrpou == user.company_edrpou
            ):
                docs_to_delete.add(del_request.document_id)
        if set(documents_ids) != docs_to_delete:
            raise Error(Code.delete_request_by_doc_does_not_exist, status=404)

    else:
        # in private api we expect data from frontend
        valid_data = validators.validate_pydantic(AcceptDeleteRequestSchema, data)
        delete_request_ids = valid_data.delete_request_ids
        delete_requests = await base_delete_request_validation(conn, user, data)
        if set(delete_request_ids) != {del_req.id for del_req in delete_requests}:
            raise Error(
                Code.delete_requests_mismatch,
                details={
                    'mismatch_delete_request_ids': list(
                        set(delete_request_ids) - {del_req.id for del_req in delete_requests}
                    )
                },
            )
        documents_ids = list({del_req.document_id for del_req in delete_requests})
    return delete_requests, documents_ids


async def validate_reject_delete_request(
    conn: DBConnection, user: AuthUser | User, data: DataDict, source: Source
) -> tuple[str | None, list[DBRow]]:
    if source == Source.api_public:
        # in public api we generate data ourselves, so it does not need to be validated
        delete_requests = await base_delete_request_validation(conn, user, data)
        docs_to_delete: set[str] = set()
        for del_request in delete_requests:
            if (
                del_request.status == DeleteRequestStatus.new
                and del_request.receiver_edrpou == user.company_edrpou
            ):
                docs_to_delete.add(del_request.document_id)
        if set(data['documents_ids']) != docs_to_delete:
            raise Error(Code.delete_request_by_doc_does_not_exist, status=404)
        reject_message = data.get('reject_message')

    else:
        # in private api we expect data from frontend
        valid_data = validators.validate_pydantic(RejectDeleteRequestSchema, data)
        delete_request_ids = set(valid_data.delete_request_ids)
        delete_requests = await base_delete_request_validation(conn, user, data)
        if delete_request_ids != {del_req.id for del_req in delete_requests}:
            raise Error(
                Code.delete_requests_mismatch,
                details={
                    'mismatch_delete_request_ids': list(
                        delete_request_ids - {del_req.id for del_req in delete_requests}
                    )
                },
            )
        reject_message = valid_data.reject_message

    return reject_message, delete_requests


async def validate_cancel_delete_vote(
    conn: DBConnection,
    user: AuthUser | User,
    data: DataDict,
) -> CancelDeleteRequestSchema:
    valid_data = validators.validate_pydantic(CancelDeleteRequestSchema, data)
    await base_delete_request_validation(conn, user, data)
    return valid_data


async def validate_find_recipients_emails(
    conn: DBConnection,
    user: User,
    data: DataDict,
) -> FindRecipientEmailsOptions:
    """Validate ability to find recipient emails."""
    valid_data = validators.validate_pydantic(FindRecipientsEmailsScheme, data)

    edrpous_with_emails: list[str] = []
    found_recipients: list[RecipientsEmailsOptions] = []

    # if request has document_id prepare already existed recipients.
    if valid_data.document_id:
        document = await validate_document_exists(conn, {'document_id': valid_data.document_id})
        await validate_document_access(conn, user, document.id)

        recipients = await select_documents_recipients(
            conn=conn,
            documents_ids=[document.id],
        )

        for recipient in recipients:
            if not recipient.emails:
                continue

            edrpous_with_emails.append(recipient.edrpou)
            found_recipients.append(
                RecipientsEmailsOptions(
                    edrpou=recipient.edrpou,
                    emails=recipient.emails,
                    is_emails_hidden=recipient.is_emails_hidden,
                )
            )

    missing_edrpous = [edrpou for edrpou in valid_data.edrpous if edrpou not in edrpous_with_emails]

    return FindRecipientEmailsOptions(
        edrpous=missing_edrpous,
        recipients=found_recipients,
    )


async def validate_get_document_hash(
    conn: DBConnection,
    user: AuthUser | User,
    document_id: str,
) -> DocumentWithUploader:
    document_id = validators.validate_pydantic_adapter(
        pv.UUIDAdapter,
        value=document_id,
    )
    document = await validate_document_exists(conn, document_id=document_id)
    await validate_document_access(conn, user, document.id)
    return document


def _validate_reject_documents_permissions(
    user: AuthUser | User,
    documents: list[DocumentWithUploader],
) -> None:
    if get_flag(FeatureFlags.ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS):
        can_reject_internal = has_permission(user, {'can_sign_and_reject_document_internal'})
        can_reject_external = has_permission(user, {'can_sign_and_reject_document_external'})
        documents_without_access = []
        for document in documents:
            is_internal = document.is_internal
            if is_internal and not can_reject_internal:
                documents_without_access.append(document.id)
            if not is_internal and not can_reject_external:
                documents_without_access.append(document.id)

        if documents_without_access:
            raise AccessDenied(
                reason=_('Неможливо відхилити документ, не достатньо прав'),
                details={'document_ids': documents_without_access},
            )
    else:
        validate_user_permission(user, {'can_sign_and_reject_document'})


async def validate_reject_documents(
    conn: DBConnection, user: AuthUser | User, data: DataDict
) -> RejectDocumentContext:
    """
    Validate ability to reject list of documents.

    Rules:
    - Document can be rejected by partner if it already signed by owner,
      but still not signed by partner.
    - Internal document can be rejected by owner.
    """

    if not user.company_edrpou:
        raise LoginRequired()

    # Validate form data
    valid_data = validators.validate_pydantic(RejectDocumentSchema, data)

    # Document must exist in database
    documents_rows = await select_documents_by_ids_with_company_info(conn, valid_data.document_ids)
    documents = [DocumentWithUploader.from_row(row) for row in documents_rows]
    _validate_reject_documents_permissions(
        user=user,
        documents=documents,
    )
    if len({item.id for item in documents}) != len(set(valid_data.document_ids)):
        raise DoesNotExist(
            Object.documents,
            document_ids=set(valid_data.document_ids) - {item.id for item in documents},
        )
    if await select_signatures(
        conn=conn,
        document_ids=valid_data.document_ids,
        role_id=user.role_id,
    ):
        raise Error(
            Code.invalid_action,
            reason=_('Неможливо відхилити документ після підписання'),
        )

    receivers_all = await select_documents_recipients(conn, documents_ids=valid_data.document_ids)
    receivers_map = defaultdict(list)
    for rec in receivers_all:
        receivers_map[rec.document_id].append(rec)

    res = []
    failed_documents = []
    for document in documents:
        receivers = receivers_map.get(document.id, [])
        # Recipient can reject both documents first signed by owner and first
        # signed by recipient. Owner can reject only documents first signed by
        # recipients. Other users cannot reject any documents.
        # Internal document can be rejected only by owner.
        is_3p = is_first_sign_by_recipient_document(document)
        is_owner_edrpou = document.edrpou_owner == user.company_edrpou
        is_internal = document.is_internal
        is_multilateral = document.is_multilateral

        receivers_edrpou = {rec.edrpou for rec in receivers if rec.edrpou != document.edrpou_owner}
        is_recipient_edrpou = user.company_edrpou in receivers_edrpou

        if is_internal:
            is_invalid_edrpou = is_recipient_edrpou
        else:
            is_invalid_edrpou = (not is_3p and not is_recipient_edrpou) or (
                is_3p and not is_owner_edrpou and not is_recipient_edrpou
            )

        if is_invalid_edrpou:
            logger.warning(
                'Unable to reject document by given EDRPOU',
                extra={
                    'company_edrpou': user.company_edrpou,
                    'is_first_sign_by_recipient_document': is_3p,
                    'is_owner_edrpou': is_owner_edrpou,
                    'is_recipient_edrpou': is_recipient_edrpou,
                    'is_internal': is_internal,
                    'owner_edrpou': document.edrpou_owner,
                    'recipient_edrpou': document.edrpou_recipient,
                    'status_id': document.status_id,
                },
            )
            failed_documents.append(document.id)

        if failed_documents:
            raise AccessDenied(
                reason=_(
                    'Неможливо відхилити документ, використовуючи ЄДРПОУ поточного користувача'
                ),
                details={'document_ids': failed_documents},
            )

        # Document must be signed by owner to be rejected by partner.
        # Also, it can be already signed by recipient company, but
        # rejected later by any signer unless sign process is not finished.
        # Internal document must be signed to be rejected by owner.
        recipient_can_reject = document.status_id in [
            DocumentStatus.sent.value,
            DocumentStatus.signed.value,
            DocumentStatus.signed_and_sent.value,
            DocumentStatus.approved.value,
        ]
        is_finished = document.status_id == DocumentStatus.finished.value
        is_not_sent = document.status_id == DocumentStatus.uploaded.value

        if is_internal:
            is_invalid_status = document.status_id not in [
                DocumentStatus.ready_to_be_signed.value,
                DocumentStatus.signed.value,
            ]
        elif is_multilateral:
            is_invalid_status = is_finished or is_not_sent
        elif is_3p:
            is_invalid_status = (
                document.status_id < DocumentStatus.signed_and_sent.value
                if is_owner_edrpou
                else not recipient_can_reject
            )
        else:
            is_invalid_status = is_owner_edrpou or not recipient_can_reject

        if is_invalid_status:
            raise Error(Code.invalid_document_status)

        res.append(
            RejectDocumentItemContext(
                document=document,
                is_first_sign_by_recipient_document=is_3p,
                owner_reject=is_owner_edrpou,
                recipient_reject=is_recipient_edrpou,
                is_internal=is_internal,
            )
        )

    return RejectDocumentContext(
        documents=res,
        validator=valid_data,
        user=user,
    )


def validate_title(title: str | None) -> str | None:
    """
    Validate title content.
    Prevents uploading files with malicious titles.
    """
    if not title:
        return title

    if not get_flag(FeatureFlags.ENABLE_EXTRA_VALIDATION_FOR_FILENAME_ON_UPLOAD):
        return title

    if is_malicious(title):
        raise InvalidRequest(title=_('Містить недопустимі символи'))

    return title


async def validate_convert_office_document_to_pdf(
    conn: DBConnection,
    request: web.Request,
    user: User,
) -> ConvertOfficeDocumentToPDFCtx:
    """
    Check that it's possible to convert an MS Office document (docx, xlsx, pptx, etc.) to PDF.
    """
    from app.document_versions.utils import get_latest_document_version, get_version_count
    from app.document_versions.validators import (
        validate_document_is_versioned,
        validate_document_versions_limit,
        validate_versioned_document_not_signed,
    )

    raw_data = {'document_id': request.match_info['document_id']}
    data = validators.validate_pydantic(ConvertOfficeDocumentToPdfSchema, raw_data)
    document_id = data.document_id

    document = await validate_document_exists(conn, document_id=document_id)
    await validate_document_access(conn, user, document_id=document_id)

    validate_versioned_document_not_signed(document=document)

    versions_count = await get_version_count(conn=conn, document_id=document.id)
    validate_document_is_versioned(versions_count=versions_count)
    validate_document_versions_limit(versions_count=versions_count)

    if document.is_internal:
        # Document version for internal documents isn't sent anywhere, so we don't care about
        # sent status for internal documents and just select any latest version.
        latest_version = await get_latest_document_version(conn=conn, document_id=document.id)
    else:
        # For both bilateral and multilateral documents, we need to retrieve the latest sent
        # version to identify which company should sign the document next. The company that sent
        # the latest version should neither be able to sign it nor convert it to PDF.
        latest_version = await get_latest_document_version(
            conn=conn,
            document_id=document.id,
            is_sent=True,
        )

        if latest_version and latest_version.company_edrpou == user.company_edrpou:
            raise InvalidRequest(
                reason=_(
                    'Наразі конвертація в PDF доступна тільки для тієї компанії, '
                    'що повинна підписати останню версію документа. Зазвичай має підписувати '
                    'та сторона, які надіслали версію документа'
                )
            )

    if not latest_version:
        raise InvalidRequest(reason=_('Не знайдено останньої надісланої версії документа'))

    if not latest_version.type.is_pdf_convertable:
        raise InvalidRequest(reason=_('Версія документа не підтримує конвертацію в PDF'))

    if latest_version.extension.lower() not in CONVERT_TO_PDF_EXTENSIONS:
        raise InvalidRequest(
            reason=_(
                'Для конвертації документа в PDF остання версія документа повинна мати '
                'розширення docx або xlsx'
            ),
            details={'version_extension': latest_version.extension},
        )

    return ConvertOfficeDocumentToPDFCtx(
        document=document,
        latest_version=latest_version,
        source=get_source(request),
    )
