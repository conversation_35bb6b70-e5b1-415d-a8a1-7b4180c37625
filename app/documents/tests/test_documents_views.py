import datetime
import io
import unittest
import uuid
from http import HTT<PERSON>tatus
from unittest import mock

import pytest
import sqlalchemy as sa
import ujson
from aiohttp import FormData

from api.public.tests.common import ROLE, add_signature
from api.public.tests.test_public_api_views_documents import USER_EMAIL_2
from app.auth.db import select_company_by_id, update_role
from app.auth.enums import RoleStatus
from app.billing.db import (
    select_company_accounts,
    select_company_transactions,
    update_account_counter,
)
from app.comments import utils as comments
from app.comments.db import select_comments
from app.comments.enums import CommentType
from app.comments.tables import comment_table
from app.contacts.db import select_contact_person
from app.contacts.sync.db import insert_contact, insert_contact_person
from app.document_antivirus.db import select_document_antivirus_check
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_antivirus.utils import add_document_antivirus_check
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.db import select_document_versions
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.test_document_versions_views import DELETE_DOCUMENT_VERSION_URL
from app.document_versions.tests.utils import prepare_document_version
from app.document_versions.utils import (
    get_document_version_key,
    get_latest_document_version_available_for_company,
)
from app.documents.db import (
    add_documents_meta,
    exists_listing,
    insert_listings,
    select_bilateral_document_recipient,
    select_delete_requests_by,
    select_delete_requests_by_ids,
    select_document,
    select_document_by_id,
    select_document_listings,
    select_document_meta,
    select_documents_by_ids_with_company_info,
    select_listings,
)
from app.documents.enums import (
    AccessSource,
    DeleteRequestStatus,
    FirstSignBy,
    RecipientRole,
)
from app.documents.tables import document_link_table, document_table
from app.documents.tests.utils import (
    check_document_status,
    get_company_listing,
    get_document_recipients,
    get_listings,
    prepare_document_recipient,
)
from app.documents.types import (
    Document,
    UpdateSignersDataSignerEntity,
    UpdateSignersDataSignerType,
)
from app.documents.utils import get_hidden_email_key
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.tables import (
    document_parameters_table,
    documents_fields_table,
)
from app.drafts.tables import draft_table
from app.drafts.tests.utils import prepare_draft_from_version
from app.drafts.utils import get_draft_s3_key
from app.events import document_actions
from app.events.document_actions.db import select_document_actions_for
from app.flow.db import select_flows_by
from app.flow.tests.utils import get_flow, get_flows
from app.flow.types import AddFlowOptions, CreateFlowCtx, FlowMeta
from app.flow.utils import create_flows
from app.groups.db import insert_group, insert_group_document_access, insert_group_member
from app.groups.utils import add_group
from app.lib import eusign_utils
from app.lib.datetime_utils import local_now, naive_local_now, soft_isoformat, utc_now
from app.lib.enums import DocumentStatus, UserRole
from app.lib.helpers import to_json
from app.lib.s3_utils import UploadFile
from app.lib.types import DataDict
from app.models import count, select_all, select_one
from app.reviews.db import (
    select_review,
    select_review_request,
    select_review_requests,
    select_review_statuses,
    select_reviews,
)
from app.reviews.enums import ReviewStatus, ReviewType
from app.reviews.tables import review_setting_table, review_table
from app.reviews.tests.utils import (
    prepare_review_db,
    prepare_review_request_db,
    prepare_review_settings_db,
    prepare_review_status_db,
)
from app.services import services
from app.signatures.db import insert_document_signers
from app.signatures.tables import signature_table
from app.signatures.tests.utils import get_document_signers
from app.signatures.utils import update_document_signers
from app.tags.db import insert_tags_for_roles, select_tags_by_documents
from app.tags.tests.common import get_company_tags, get_document_tags
from app.tags.utils import add_tags, create_new_tags_for_documents, insert_role_tags_batch
from app.tests.common import (
    AGROYARD_EDRPOU,
    API_V2_SEND_DOCUMENT_URL,
    TEST_BILLING_ACCOUNT,
    TEST_COMPANY_EDRPOU,
    TEST_CONTACT,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_UPLOAD_LEFT_COUNTER,
    UKRPOSHTA_EDRPOU,
    UKRPOSHTA_SUBSIDIARY_EDRPOU,
    UPLOAD_DOCUMENT_URL,
    VCHASNO_EDRPOU,
    ZAKUPKI_EDRPOU,
    change_recipient,
    cleanup_on_teardown,
    get_billing_accounts,
    get_document,
    get_document_meta,
    get_es_documents,
    insert_values,
    prepare_app_client,
    prepare_auth_headers,
    prepare_client,
    prepare_comment_data,
    prepare_company_data,
    prepare_contact,
    prepare_delete_document_request,
    prepare_document_data,
    prepare_document_data_for_email,
    prepare_document_recipients,
    prepare_document_reject,
    prepare_document_signer,
    prepare_document_upload,
    prepare_flow_item,
    prepare_flows_rows,
    prepare_listings_accesses,
    prepare_private_key,
    prepare_public_document_categories,
    prepare_review,
    prepare_review_requests,
    prepare_signature_data,
    prepare_signature_form_data,
    prepare_signature_row,
    prepare_user_data,
    request_create_tags_for_contacts,
    request_delete_document,
    request_document_update,
    request_find_recipients,
    send_document,
    set_billing_company_config,
    set_company_config,
    sign_and_send_document,
    sign_document,
    update_company_config,
    with_elastic,
)

TEST_DOCUMENT_NUMBER = 'ZK-00000000'
TEST_DOCUMENT_TITLE = 'test title'
TEST_DATE_DOCUMENT = '2000-01-01T00:00:00+00:00'
TEST_RECIPIENT_ANOTHER_EMAIL = '<EMAIL>'
TEST_RECIPIENT_ANOTHER_EMAIL2 = '<EMAIL>'
TEST_RECIPIENT_EDRPOU = '77777777'
TEST_RECIPIENT_EMAIL = '<EMAIL>'
TEST_RECIPIENT_EMAILS = f'{TEST_RECIPIENT_EMAIL}, {TEST_RECIPIENT_ANOTHER_EMAIL}'
TEST_UUID_1 = '10000000-0000-0000-0000-000000000001'
TEST_UUID_2 = '10000000-0000-0000-0000-000000000002'
TEST_UUID_3 = '10000000-0000-0000-0000-000000000003'
TEST_UUID_4 = '10000000-0000-0000-0000-000000000004'
TEST_UUID_5 = '10000000-0000-0000-0000-000000000005'
TEST_UUID_6 = '10000000-0000-0000-0000-000000000006'

USER_ID_1 = '*************-0000-0000-000000000001'
USER_ID_2 = '*************-0000-0000-000000000002'
USER_ID_3 = '*************-0000-0000-000000000003'
USER_ID_4 = '*************-0000-0000-000000000004'

ROLE_ID_1 = '*************-0000-0000-000000000001'
ROLE_ID_2 = '*************-0000-0000-000000000002'
ROLE_ID_3 = '*************-0000-0000-000000000003'
ROLE_ID_4 = '*************-0000-0000-000000000004'
ROLE_ID_5 = '*************-0000-0000-000000000005'

DOCUMENT_ID_1 = '*************-0000-0000-000000000001'
DOCUMENT_ID_2 = '*************-0000-0000-000000000002'

TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'
TEST_EMAIL_3 = '<EMAIL>'
TEST_EMAIL_4 = '<EMAIL>'
TEST_EMAIL_5 = '<EMAIL>'


COMPANY_ID_1 = '89887743-c4d9-4373-ade3-dfd3ad481c90'
COMPANY_ID_2 = 'f4491e28-84f2-415c-b5f2-2cdf03eca2ae'

COMPANY_EDRPOU_1 = '10000001'
COMPANY_EDRPOU_2 = '10000002'
COMPANY_EDRPOU_3 = '10000003'
COMPANY_EDRPOU_4 = '10000004'

DEFAULT_DOCUMENT_PARAMETERS = [
    {'field_id': TEST_UUID_1, 'value': 'HELLO'},
    {'field_id': TEST_UUID_2, 'value': 'BLABLA'},
]

TODAY = datetime.date.today()
YESTERDAY_DATETIME = local_now() - datetime.timedelta(days=1)


@pytest.mark.parametrize(
    'document_data, expected',
    [
        (
            {
                'status_id': DocumentStatus.signed.value,
                'first_sign_by': FirstSignBy.recipient.value,
                'by_owner': True,
            },
            (
                'Неможливо редагувати підписантів документа, '
                'коли документ знаходиться на підписанні у іншій компанії'
            ),
        ),
        (
            {
                'status_id': DocumentStatus.sent.value,
                'first_sign_by': FirstSignBy.recipient.value,
                'by_owner': True,
            },
            (
                'Неможливо редагувати підписантів документа, '
                'коли документ знаходиться на підписанні у іншій компанії'
            ),
        ),
        (
            {
                'status_id': DocumentStatus.finished.value,
                'first_sign_by': FirstSignBy.recipient.value,
                'by_owner': False,
            },
            'Неможливо редагувати підписантів завершеного документа',
        ),
        (
            {
                'status_id': DocumentStatus.finished.value,
                'first_sign_by': FirstSignBy.owner.value,
                'by_owner': False,
            },
            'Неможливо редагувати підписантів завершеного документа',
        ),
        (
            {
                'status_id': DocumentStatus.finished.value,
                'first_sign_by': FirstSignBy.recipient.value,
                'by_owner': True,
            },
            'Неможливо редагувати підписантів завершеного документа',
        ),
        (
            {
                'status_id': DocumentStatus.finished.value,
                'first_sign_by': FirstSignBy.owner.value,
                'by_owner': True,
            },
            'Неможливо редагувати підписантів завершеного документа',
        ),
    ],
)
async def test_update_bad_status(aiohttp_client, document_data, expected):
    app, client, user = await prepare_client(aiohttp_client)
    by_owner = document_data.pop('by_owner', True)
    document_data['create_document_access_for_recipients'] = True

    document_data['document_recipients'] = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document = await prepare_document_data(app, user, **document_data)

    author = user if by_owner else recipient
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'entities': [{'id': author.role_id, 'type': 'role'}],
                'parallel_signing': False,
            },
        },
        headers=prepare_auth_headers(author),
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    result = await response.json()
    assert result['reason'] == expected


async def test_update_signers_after_one_of_signer_signed_doc(aiohttp_client):
    """
    Given:
    - internal document with 3 signers
    - first signer signed document
    When:
    - other signers are removed
    Then:
    - document status is changed to finished
    """
    # Arrange
    app, client, user = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_admin=True,
    )

    coworker1 = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(
        app,
        user,
        is_internal=True,
    )
    await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'parallel_signing': True,
                'entities': [
                    {'id': user.role_id, 'type': 'role'},
                    {'id': coworker1.role_id, 'type': 'role'},
                    {'id': coworker2.role_id, 'type': 'role'},
                ],
            },
        },
        headers=prepare_auth_headers(user),
    )

    response = await client.post(
        f'/internal-api/documents/{document.id}/signatures',
        data=prepare_signature_form_data(user),
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED

    # Act
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'entities': [
                    {'id': user.role_id, 'type': 'role'},
                ],
                'parallel_signing': False,
            },
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK

    # Assert
    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)

    assert document.status_id == DocumentStatus.finished.value
    assert document.date_finished is not None


@pytest.mark.parametrize(
    'status_id, is_admin, signers, expected_status',
    [
        pytest.param(
            DocumentStatus.uploaded.value,
            False,
            [],
            HTTPStatus.OK,
            id='not_admin_can_update_not_signed_document',
        ),
        pytest.param(
            DocumentStatus.uploaded.value,
            True,
            [],
            HTTPStatus.OK,
            id='admin_can_update_not_signed_document',
        ),
        pytest.param(
            DocumentStatus.signed.value,
            False,
            [
                {
                    'role_id': ROLE_ID_1,
                    'assigner': ROLE_ID_2,
                    'date_signed': naive_local_now(),
                }
            ],
            HTTPStatus.BAD_REQUEST,
            id='not_admin_cant_update_signed_document',
        ),
        pytest.param(
            DocumentStatus.signed.value,
            False,
            [
                {
                    'role_id': ROLE_ID_2,
                    'assigner': ROLE_ID_1,
                    'date_signed': naive_local_now(),
                }
            ],
            HTTPStatus.OK,
            id='not_admin_but_assigner_can_update_signed_document',
        ),
        pytest.param(
            DocumentStatus.signed.value,
            True,
            [
                {
                    'role_id': ROLE_ID_1,
                    'assigner': ROLE_ID_2,
                    'date_signed': naive_local_now(),
                }
            ],
            HTTPStatus.OK,
            id='admin_can_update_signed_document',
        ),
        pytest.param(
            DocumentStatus.finished.value,
            True,
            [
                {
                    'role_id': ROLE_ID_1,
                    'assigner': ROLE_ID_2,
                    'date_signed': naive_local_now(),
                }
            ],
            HTTPStatus.BAD_REQUEST,
            id='admin_can_not_update_finished_document',
        ),
    ],
)
async def test_update_signers_for_internal(
    aiohttp_client,
    status_id: str,
    is_admin: bool,
    signers: list[dict],
    expected_status: int,
):
    app, client, user = await prepare_client(aiohttp_client, is_admin=is_admin, role_id=ROLE_ID_1)
    await prepare_user_data(app, email='<EMAIL>', role_id=ROLE_ID_2)
    await prepare_user_data(app, email='<EMAIL>', role_id=ROLE_ID_3)

    document_data = {
        'is_internal': True,
        'status_id': status_id,
    }
    document = await prepare_document_data(app, user, **document_data)

    for signer in signers:
        await prepare_document_signer(
            document_id=document.id,
            company_id=user.company_id,
            group_id=None,
            **signer,
        )

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'entities': [{'id': user.role_id, 'type': 'role'}],
                'parallel_signing': True,
            },
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == expected_status


@pytest.mark.parametrize(
    'doc_signers, request_data, expected_response, expected_signers, expected_receivers',
    [
        (  # Delete signer with signature
            {(None, TEST_UUID_1)},
            {'parallel_signing': False, 'entities': []},
            (
                HTTPStatus.BAD_REQUEST,
                'Неможливо видалити підписанта документа, коли він вже підписав документ',
            ),
            {(None, TEST_UUID_1)},
            set(),
        ),
        (  # Delete signer without signature
            {(1, TEST_UUID_1), (2, TEST_UUID_2)},
            {
                'parallel_signing': False,
                'entities': [
                    {'id': TEST_UUID_1, 'type': 'role'},
                ],
            },
            (HTTPStatus.OK, None),
            {(1, TEST_UUID_1)},
            set(),
        ),
        (  # Add more signers for ordered signing
            {(1, TEST_UUID_1)},
            {
                'parallel_signing': False,
                'entities': [
                    {'id': TEST_UUID_1, 'type': 'role'},
                    {'id': TEST_UUID_2, 'type': 'role'},
                    {'id': TEST_UUID_3, 'type': 'role'},
                ],
            },
            (HTTPStatus.OK, None),
            {(1, TEST_UUID_1), (2, TEST_UUID_2), (3, TEST_UUID_3)},
            {TEST_RECIPIENT_EMAIL},
        ),
        (  # Add more signers for parallel signing
            {(None, TEST_UUID_1)},
            {
                'parallel_signing': True,
                'entities': [
                    {'id': TEST_UUID_1, 'type': 'role'},
                    {'id': TEST_UUID_2, 'type': 'role'},
                    {'id': TEST_UUID_3, 'type': 'role'},
                ],
            },
            (HTTPStatus.OK, None),
            {(None, TEST_UUID_1), (None, TEST_UUID_2), (None, TEST_UUID_3)},
            {TEST_RECIPIENT_EMAIL, TEST_RECIPIENT_ANOTHER_EMAIL},
        ),
        (  # Change from parallel to ordered signing, already signed user is the first
            {(None, TEST_UUID_1), (None, TEST_UUID_2), (None, TEST_UUID_3)},
            {
                'parallel_signing': False,
                'entities': [
                    {'id': TEST_UUID_1, 'type': 'role'},
                    {'id': TEST_UUID_2, 'type': 'role'},
                    {'id': TEST_UUID_3, 'type': 'role'},
                ],
            },
            (HTTPStatus.OK, None),
            {(1, TEST_UUID_1), (2, TEST_UUID_2), (3, TEST_UUID_3)},
            {TEST_RECIPIENT_EMAIL},
        ),
        (  # Change from parallel to ordered signing, already signed user is the last
            {(None, TEST_UUID_1), (None, TEST_UUID_2), (None, TEST_UUID_3)},
            {
                'parallel_signing': False,
                'entities': [
                    {'id': TEST_UUID_2, 'type': 'role'},
                    {'id': TEST_UUID_3, 'type': 'role'},
                    {'id': TEST_UUID_1, 'type': 'role'},
                ],
            },
            (HTTPStatus.OK, None),
            {(1, TEST_UUID_2), (2, TEST_UUID_3), (3, TEST_UUID_1)},
            {TEST_RECIPIENT_EMAIL},
        ),
        (  # Change from ordered to parallel signing
            {(1, TEST_UUID_1), (2, TEST_UUID_2), (3, TEST_UUID_3)},
            {
                'parallel_signing': True,
                # 'signers': [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3],
                'entities': [
                    {'id': TEST_UUID_1, 'type': 'role'},
                    {'id': TEST_UUID_2, 'type': 'role'},
                    {'id': TEST_UUID_3, 'type': 'role'},
                ],
            },
            (HTTPStatus.OK, None),
            {(None, TEST_UUID_1), (None, TEST_UUID_2), (None, TEST_UUID_3)},
            {TEST_RECIPIENT_EMAIL, TEST_RECIPIENT_ANOTHER_EMAIL},
        ),
    ],
)
async def test_update_signers(
    aiohttp_client,
    mailbox,
    doc_signers,
    request_data,
    expected_response,
    expected_signers,
    expected_receivers,
):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    signer = await prepare_user_data(
        app,
        company_edrpou=TEST_COMPANY_EDRPOU,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        role_id=TEST_UUID_1,
    )
    company_id = signer.company_id
    # potential signer_2
    await prepare_user_data(
        app,
        company_edrpou=TEST_COMPANY_EDRPOU,
        company_id=company_id,
        email=TEST_RECIPIENT_EMAIL,
        role_id=TEST_UUID_2,
    )
    # potential signer 3
    await prepare_user_data(
        app,
        company_edrpou=TEST_COMPANY_EDRPOU,
        company_id=company_id,
        email=TEST_RECIPIENT_ANOTHER_EMAIL,
        role_id=TEST_UUID_3,
    )

    document_data = {
        'status_id': DocumentStatus.signed.value,
    }
    document = await prepare_document_data(app, user, **document_data)

    async with app['db'].acquire() as conn:
        signer_data = [
            {
                'document_id': document.id,
                'company_id': company_id,
                'role_id': role_id,
                'assigner': user.role_id,
                'order': i,
            }
            for i, role_id in doc_signers
        ]
        await insert_document_signers(conn, signer_data)

    await prepare_signature_data(
        app=app,
        owner=signer,
        document=document,
        update_access=False,
        next_status_id=DocumentStatus.signed.value,
    )

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={'signers_settings': request_data},
        headers=prepare_auth_headers(user),
    )

    expected_code, error_message = expected_response
    assert response.status == expected_code

    if error_message:
        result = await response.json()
        assert result['reason']

    document_signers = await get_document_signers(document_id=document.id)
    document_signers_mapping = {(signer.order, signer.role_id) for signer in document_signers}
    assert document_signers_mapping == expected_signers

    notification_receivers = {mail['to'] for mail in mailbox}
    # Check that already signed no not receive duplicated notifications
    assert signer.email not in notification_receivers
    assert expected_receivers == notification_receivers


@pytest.mark.parametrize(
    (
        'document_data',
        'update_data',
        'expected',
    ),
    [
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.other.value,
                'amount': 1000,
            },
            {
                'document_settings': {
                    'date_document': '2024-01-01T00:00:00',
                    'number': '20',
                    'title': 'new title',
                    'category': PublicDocumentCategory.act.value,
                    'amount': 200000,
                },
            },
            {
                'date_document': '2024-01-01T00:00:00+00:00',
                'number': '20',
                'title': 'new title',
                'category': PublicDocumentCategory.act.value,
                'amount': 200000,
            },
            id='update_all_fields_uploaded',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            {
                'document_settings': {
                    'date_document': None,
                    'number': None,
                    'title': 'new',  # can't be None or empty
                    'category': PublicDocumentCategory.other.value,  # can't be None
                    'amount': None,
                },
            },
            {
                'date_document': None,
                'number': None,
                'title': 'new',
                'category': PublicDocumentCategory.other.value,
                'amount': None,
            },
            id='reset_fields_uploaded',
        ),
        # when the signing process is started, the document can be updated, but only fields
        # that weren't set for a document will be updated
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            {
                'document_settings': {
                    'date_document': '2024-01-01T00:00:00',
                    'number': '20',
                    'title': 'new title',
                    'category': PublicDocumentCategory.appendix.value,
                    'amount': 200000,
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            id='update_all_fields_signed_keep_old',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'date_document': None,
                'number': None,
                'title': '',
                'category': PublicDocumentCategory.other.value,  # "other" means "not set"
                'amount': None,
            },
            {
                'document_settings': {
                    'date_document': '2024-01-01T00:00:00',
                    'number': '20',
                    'title': 'new title',
                    'category': PublicDocumentCategory.appendix.value,
                    'amount': 200000,
                },
            },
            {
                'date_document': '2024-01-01T00:00:00+00:00',
                'number': '20',
                'title': 'new title',
                'category': PublicDocumentCategory.appendix.value,
                'amount': 200000,
            },
            id='update_all_fields_signed_update_empty',
        ),
        # you can also update only few fields, other fields will be kept
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            {
                'document_settings': {
                    'title': 'new title',
                    'category': PublicDocumentCategory.appendix.value,
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': '1',
                'title': 'new title',
                'category': PublicDocumentCategory.appendix.value,
                'amount': 1000,
            },
            id='update_partial_uploaded',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            {
                'document_settings': {
                    'number': '20',
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': '1',  # was not updated
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            id='update_partial_signed_keep_old',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'date_document': None,
                'number': None,
                'title': '',
                'category': PublicDocumentCategory.other.value,  # # "other" means "not set"
                'amount': None,
            },
            {
                'document_settings': {
                    'amount': 200000,
                },
            },
            {
                'date_document': None,
                'number': None,
                'title': '',
                'category': PublicDocumentCategory.other.value,
                'amount': 200000,
            },
            id='update_partial_signed_update_empty',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': None,
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            {
                'document_settings': {
                    'number': '',
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': None,  # still none
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            id='update_number_passed_as_empty',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            {
                'document_settings': {
                    'number': '',
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': None,  # becomes None
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 1000,
            },
            id='update_number_passed_as_empty_become_none',
        ),
        # Add special checks to make sure that zero amounts are treated as valid value, not empty
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 10,
            },
            {
                'document_settings': {
                    'amount': 0,
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 0,
            },
            id='update_amount_zero_uploaded',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 0,
            },
            {
                'document_settings': {
                    'amount': 10,
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 10,
            },
            id='update_amount_zero_uploaded',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 0,  # value is set
            },
            {
                'document_settings': {
                    'amount': 10,
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 0,  # was not updated (signed status + 0 is valid value)
            },
            id='update_amount_zero_signed_keep_old',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'date_document': None,
                'number': None,
                'title': '',
                'category': PublicDocumentCategory.other.value,  # "other" means "not set"
                'amount': 10,  # is set
            },
            {
                'document_settings': {
                    'amount': 0,
                },
            },
            {
                'date_document': None,
                'number': None,
                'title': '',
                'category': PublicDocumentCategory.other.value,
                'amount': 10,
            },
            id='update_amount_zero_signed',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.signed.value,
                'date_document': None,
                'number': None,
                'title': '',
                'category': PublicDocumentCategory.other.value,  # # "other" means "not set"
                'amount': None,
            },
            {
                'document_settings': {
                    'amount': 0,
                },
            },
            {
                'date_document': None,
                'number': None,
                'title': '',
                'category': PublicDocumentCategory.other.value,
                'amount': 0,
            },
            id='update_amount_zero_signed_none',
        ),
        pytest.param(
            {
                'status_id': DocumentStatus.uploaded.value,
                'date_document': '2021-01-01T00:00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': None,
            },
            {
                'document_settings': {
                    'amount': 0,
                },
            },
            {
                'date_document': '2021-01-01T00:00:00+00:00',
                'number': '1',
                'title': 'test title',
                'category': PublicDocumentCategory.act.value,
                'amount': 0,
            },
            id='update_amount_zero_uploaded_none',
        ),
    ],
)
async def test_update_document_info(
    aiohttp_client,
    document_data: DataDict,
    update_data: DataDict,
    expected: DataDict,
):
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_public_document_categories(amount=15)
    document = await prepare_document_data(app, user, **document_data)

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json=update_data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        document_obj = Document.from_row(document)
        assert soft_isoformat(document_obj.date_document) == expected['date_document']
        assert document_obj.number == expected['number']
        assert document_obj.title == expected['title']
        assert document_obj.category == (int(expected['category']) or None)
        assert document_obj.amount == expected['amount']


async def test_update_not_by_owner(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(app, owner)

    try:
        response = await client.patch(
            f'/internal-api/documents/{document.id}',
            data=ujson.dumps(
                {
                    'date_document': TEST_DATE_DOCUMENT,
                    'number': TEST_DOCUMENT_NUMBER,
                    'title': TEST_DOCUMENT_TITLE,
                }
            ),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 403

        async with app['db'].acquire() as conn:
            document = await select_document_by_id(conn, document.id)
            assert document.date_document != TEST_DATE_DOCUMENT
            assert document.number != TEST_DOCUMENT_NUMBER
            assert document.title != TEST_DOCUMENT_TITLE
    finally:
        await cleanup_on_teardown(app)


async def test_update_not_existing_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document_id = str(uuid.uuid4())

    try:
        response = await client.patch(
            f'/internal-api/documents/{document_id}',
            data=ujson.dumps(
                {
                    'date_document': TEST_DATE_DOCUMENT,
                    'number': TEST_DOCUMENT_NUMBER,
                    'title': TEST_DOCUMENT_TITLE,
                }
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 404

    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'prepare, viewers_settings, expected',
    [
        # all provided roles receive viewer access
        pytest.param(
            {},
            {'add_viewers': [TEST_UUID_2, TEST_UUID_3]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    # default access of document granted by uploading document
                    (TEST_UUID_1, AccessSource.default),
                    # new access
                    (TEST_UUID_2, AccessSource.viewer),
                    (TEST_UUID_3, AccessSource.viewer),
                },
                'mailbox': [TEST_EMAIL_2, TEST_EMAIL_3],
            },
            id='add_action_to_new_roles',
        ),
        # all given roles receive viewer access, even who already have other access
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_2,
                        'sources': AccessSource.reviewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'add_viewers': [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default | AccessSource.viewer),
                    (TEST_UUID_2, AccessSource.reviewer | AccessSource.viewer),
                    (TEST_UUID_3, AccessSource.viewer),
                },
                'mailbox': [TEST_EMAIL_2, TEST_EMAIL_3],
            },
            id='add_action_to_existing_roles',
        ),
        # Any duplication of must be correctly handled
        pytest.param(
            {},
            {'add_viewers': [TEST_UUID_1, TEST_UUID_1, TEST_UUID_1, TEST_UUID_2, TEST_UUID_2]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default | AccessSource.viewer),
                    (TEST_UUID_2, AccessSource.viewer),
                },
                'mailbox': [TEST_EMAIL_2],
            },
            id='add_action_with_duplicates',
        ),
        # empty list of viewers is also OK
        pytest.param(
            {},
            {'add_viewers': []},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                },
                'mailbox': [],
            },
            id='add_action_empty_list',
        ),
        # not expected key
        pytest.param(
            {},
            {'viewers_ids': []},
            {
                'status': HTTPStatus.BAD_REQUEST,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                },
                'mailbox': [],
            },
            id='add_action_wrong_key',
        ),
        # TEST_UUID_4 is role from another company
        pytest.param(
            {},
            {'add_viewers': [TEST_UUID_4, TEST_UUID_3]},
            {
                'status': HTTPStatus.FORBIDDEN,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                },
                'mailbox': [],
            },
            id='add_action_another_company',
        ),
        # Adding new viewers should affect access in another company
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_4,
                        'sources': AccessSource.comment | AccessSource.reviewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_RECIPIENT_EDRPOU,
                    },
                ]
            },
            {'add_viewers': [TEST_UUID_2]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                    (TEST_UUID_2, AccessSource.viewer),
                    (TEST_UUID_4, AccessSource.comment | AccessSource.reviewer),
                },
                'mailbox': [TEST_EMAIL_2],
            },
            id='add_action_should_not_affect_another_company',
        ),
        # === Remove viewers ===
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_2,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                    {
                        'role_id': TEST_UUID_3,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'remove_viewers': [TEST_UUID_2, TEST_UUID_3]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                },
                'mailbox': [],
            },
            id='remove_action_all_existing',
        ),
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_2,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                    {
                        'role_id': TEST_UUID_3,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'remove_viewers': [TEST_UUID_2]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                    (TEST_UUID_3, AccessSource.viewer),
                },
                'mailbox': [],
            },
            id='remove_action_one_existing',
        ),
        # Remove viewer access when user does not have it
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_3,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'remove_viewers': [TEST_UUID_2]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                    (TEST_UUID_3, AccessSource.viewer),
                },
                'mailbox': [],
            },
            id='remove_action_not_existing',
        ),
        # Remove viewer access when user is from another company
        pytest.param(
            {},
            {'remove_viewers': [TEST_UUID_4]},
            {
                'status': HTTPStatus.FORBIDDEN,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                },
                'mailbox': [],
            },
            id='remove_action_another_company',
        ),
        # Remove viewer access should not affect access in another company
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_4,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_RECIPIENT_EDRPOU,
                    },
                    {
                        'role_id': TEST_UUID_2,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'remove_viewers': [TEST_UUID_2]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                    (TEST_UUID_4, AccessSource.viewer),
                },
                'mailbox': [],
            },
            id='remove_action_should_not_affect_another_company',
        ),
        # === Replace viewers ===
        # Replace all viewers
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_2,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                    {
                        'role_id': TEST_UUID_3,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'replace_viewers': [TEST_UUID_2, TEST_UUID_1]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.viewer | AccessSource.default),
                    (TEST_UUID_2, AccessSource.viewer),
                },
                'mailbox': [],  # TEST_UUID_1 performed the action, TEST_UUID_2 already had access
            },
            id='replace_action_all_existing',
        ),
        # Replace all viewers all new
        pytest.param(
            {},
            {'replace_viewers': [TEST_UUID_2, TEST_UUID_1]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.viewer | AccessSource.default),
                    (TEST_UUID_2, AccessSource.viewer),
                },
                'mailbox': [TEST_EMAIL_2],
            },
            id='replace_action_all_new',
        ),
        # Remove all viewers by replacement
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_2,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                    {
                        'role_id': TEST_UUID_3,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'replace_viewers': []},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                },
                'mailbox': [],
            },
            id='replace_action_remove_all',
        ),
        # Try to replace viewers with roles from another company
        pytest.param(
            {},
            {'replace_viewers': [TEST_UUID_4]},
            {
                'status': HTTPStatus.FORBIDDEN,
                'accesses': {
                    (TEST_UUID_1, AccessSource.default),
                },
                'mailbox': [],
            },
            id='replace_action_another_company',
        ),
        # Replace viewers should not affect access in another company
        pytest.param(
            {
                'listings': [
                    {
                        'role_id': TEST_UUID_4,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_RECIPIENT_EDRPOU,
                    },
                    {
                        'role_id': TEST_UUID_2,
                        'sources': AccessSource.viewer,
                        'document_id': DOCUMENT_ID_1,
                        'access_edrpou': TEST_COMPANY_EDRPOU,
                    },
                ]
            },
            {'replace_viewers': [TEST_UUID_3, TEST_UUID_1]},
            {
                'status': HTTPStatus.OK,
                'accesses': {
                    (TEST_UUID_1, AccessSource.viewer | AccessSource.default),
                    (TEST_UUID_3, AccessSource.viewer),
                    (TEST_UUID_4, AccessSource.viewer),
                },
                'mailbox': [TEST_EMAIL_3],
            },
            id='replace_action_should_not_affect_another_company',
        ),
    ],
)
async def test_update_viewers(
    aiohttp_client,
    prepare: DataDict,
    viewers_settings,
    expected: DataDict,
    mailbox,
):
    """
    - User TEST_UUID_1 is main actor of test case (requesting document update);
    - User TEST_UUID_2, TEST_UUID_3 is regular coworker  without direct access to
    document
    - User TEST_UUID_4 is from another company.
    """
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        role_id=TEST_UUID_1,
        email=TEST_EMAIL_1,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    document = await prepare_document_data(app, user, id=DOCUMENT_ID_1)

    # coworkers
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=TEST_UUID_2,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_3,
        role_id=TEST_UUID_3,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    # user from another company
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_4,
        role_id=TEST_UUID_4,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    if prepare_listings := prepare.get('listings'):
        async with app['db'].acquire() as conn:
            await insert_listings(conn, prepare_listings)

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'viewers_settings': viewers_settings,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == expected['status'], await response.json()

    mailbox_emails = [mail['to'] for mail in mailbox]
    assert sorted(mailbox_emails) == sorted(expected['mailbox'])

    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [document.id])
        result = {(item.role_id, item.sources) for item in listings}
        assert result == expected['accesses']


@pytest.mark.parametrize(
    'update_data,approve_review,status,expected_review_requests_roles,expected_emails',
    [
        (
            # both reviewers will receive notification and review
            # request will be created for both users
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_2, TEST_UUID_3],
                    'is_required': False,
                },
            },
            False,
            HTTPStatus.OK,
            [TEST_UUID_2, TEST_UUID_3],
            2,
        ),
        (
            # both reviewers will receive notification and review
            # request will be created for both users, but signer will not receive
            # any notifications, becouse review is required
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_2, TEST_UUID_3],
                    'is_required': True,
                },
                'signers_settings': {
                    'entities': [{'id': TEST_UUID_1, 'type': 'role'}],
                    'parallel_signing': False,
                },
            },
            False,
            HTTPStatus.OK,
            [TEST_UUID_2, TEST_UUID_3],
            2,
        ),
        (
            # review request will be created for TEST_UUID_1 and TEST_UUID_2, but
            # TEST_UUID_1 does not receive notification, such as user is request
            # document update
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_1, TEST_UUID_2],
                    'is_required': False,
                },
            },
            False,
            HTTPStatus.OK,
            [TEST_UUID_1, TEST_UUID_2],
            1,
        ),
        (
            # review request will be created for TEST_UUID_1 and TEST_UUID_2, but
            # nobody receive notification, such as review is ordered and document
            # was updated by TEST_UUID_1
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_1, TEST_UUID_2],
                    'is_required': False,
                    'is_ordered': True,
                },
            },
            False,
            HTTPStatus.OK,
            [TEST_UUID_1, TEST_UUID_2],
            0,
        ),
        (
            # review request will be created TEST_UUID_1 and TEST_UUID_2
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_1, TEST_UUID_2],
                    'is_required': False,
                },
            },
            True,
            HTTPStatus.OK,
            [TEST_UUID_1, TEST_UUID_2],
            0,
        ),
        (
            # both reviewers will receive notification and review
            # request will be created for both users
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_2, TEST_UUID_3],
                    'is_required': True,
                },
            },
            False,
            HTTPStatus.OK,
            [TEST_UUID_2, TEST_UUID_3],
            2,
        ),
        (
            # Ordered review: only first reviewer will receive notification,
            # but review request will be created for both users
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_2, TEST_UUID_3],
                    'is_required': True,
                    'is_ordered': True,
                },
            },
            False,
            HTTPStatus.OK,
            [TEST_UUID_2, TEST_UUID_3],
            1,
        ),
        (
            # duplicated reviewers must correctly handled
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_3, TEST_UUID_3],
                    'is_required': True,
                },
            },
            False,
            HTTPStatus.OK,
            [TEST_UUID_3],
            1,
        ),
        (
            # empty list of reviewers is also allowed
            {
                'reviews_settings': {
                    'reviewers': [],
                    'is_required': True,
                },
            },
            False,
            HTTPStatus.OK,
            [],
            0,
        ),
        (
            # empty list of reviewers allowed for ordered review
            {
                'reviews_settings': {
                    'reviewers': [],
                    'is_required': True,
                    'is_ordered': True,
                },
            },
            False,
            HTTPStatus.OK,
            [],
            0,
        ),
        (
            # key `is_required` is required
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_2, TEST_UUID_2],
                },
            },
            False,
            HTTPStatus.BAD_REQUEST,
            [],
            0,
        ),
        (
            # can not add to reviewer user from another company
            {
                'reviews_settings': {
                    'reviewers': [TEST_UUID_4],
                    'is_required': True,
                },
            },
            False,
            HTTPStatus.NOT_FOUND,
            [],
            0,
        ),
    ],
)
async def test_update_reviewers(
    aiohttp_client,
    update_data,
    approve_review,
    expected_emails,
    expected_review_requests_roles,
    status,
    mailbox,
):
    """
    - User TEST_UUID_1 is main actor of test case (requesting document update);
    - User TEST_UUID_2 is regular coworker  without direct access to document and,
    if approve_review is True, then is one of document reviewers before document
    update;
    - User TEST_UUID_3 just regular coworker without direct access to document.
    - User TEST_UUID_4 is from another company.
    """
    app, client, user = await prepare_client(aiohttp_client, role_id=TEST_UUID_1)

    # coworkers
    coworker = await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_2)
    await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_3)
    # user from another company
    await prepare_user_data(
        app=app,
        email='<EMAIL>',
        role_id=TEST_UUID_4,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(app, user)
    if approve_review:
        await prepare_review(
            client,
            document=document,
            user=coworker,
            review_type=ReviewType.approve,
            expected_status=HTTPStatus.CREATED,
        )

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={**update_data},
        headers=prepare_auth_headers(user),
    )
    assert response.status == status, await response.json()

    assert len(mailbox) == expected_emails
    async with app['db'].acquire() as conn:
        requests = await select_review_requests(
            conn=conn,
            company_id=user.company_id,
            document_ids=[document.id],
        )
        result = {item.to_role_id for item in requests}
        assert set(expected_review_requests_roles) == result

        # Check that order is preserved for ordered reviews
        review_settings = update_data.get('reviews_settings', {})
        if review_settings.get('is_ordered'):
            assert not [r for r in requests if r.order is None]

            result = [r.to_role_id for r in sorted(requests, key=lambda r: r.order)]
            assert expected_review_requests_roles == result
        else:
            assert not [r for r in requests if r.order is not None]

        if expected_review_requests_roles:
            settings = await select_one(conn, review_setting_table.select())
            reviews_settings = update_data['reviews_settings']
            assert settings.is_required == reviews_settings['is_required']


async def test_update_reviewers_listing_changed(
    aiohttp_client,
):
    """
    Given:
        - document with 2 reviewers
    When:
        - update reviewers to 1 reviewer
    Then:
        - second reviewer listing must be deleted
    """

    # Arrange
    app, client, user = await prepare_client(aiohttp_client, role_id=TEST_UUID_1)

    # coworkers
    await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_2)
    await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_3)

    document = await prepare_document_data(app, user)

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'reviews_settings': {
                'reviewers': [TEST_UUID_2, TEST_UUID_3],
                'is_required': True,
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200, await response.json()

    async with app['db'].acquire() as conn:
        listings = await select_document_listings(
            conn=conn,
            document_id=document.id,
            edrpou=document.edrpou_owner,
        )
    assert len(listings) == 3  # 2 reviewers + 1 owner

    # Act
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'reviews_settings': {
                'reviewers': [TEST_UUID_2],
                'is_required': True,
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200, await response.json()

    # Assert
    async with app['db'].acquire() as conn:
        listings = await select_document_listings(
            conn=conn,
            document_id=document.id,
            edrpou=document.edrpou_owner,
        )
    assert len(listings) == 2  # 1 reviewer + 1 owner


async def test_update_reviewers_with_deactivated_role(aiohttp_client):
    """
    Logic Changed in DOC-5909. Deleted role removes from reviewers list in documents update.
    """
    app, client, user = await prepare_client(aiohttp_client)

    # coworkers
    coworker1 = await prepare_user_data(app, email='c1@ua')
    coworker2 = await prepare_user_data(app, email='c2@ua')
    document = await prepare_document_data(app, user)

    await prepare_review_requests(client, document, user, reviewers=[coworker1, coworker2])

    # Deactivate user role
    async with app['db'].acquire() as conn:
        await update_role(
            conn=conn,
            role_id=coworker1.role_id,
            data={'status': RoleStatus.deleted},
        )

    await prepare_review_requests(
        client,
        document=document,
        initiator=user,
        reviewers=[coworker1, coworker2],
    )
    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(conn, user.company_id, [document.id])
    assert len(review_requests) == 1
    assert review_requests[0].to_role_id == coworker2.role_id


async def test_update_reviewers_with_deactivated_role_and_approve_review(aiohttp_client):
    """
    Logic Changed in DOC-5909
    Can update reviewers if one of roles deactivated (but already have review approve).
    Second case can update reviewers when one of roles deactivated and without reviewe
        (removed user from reviewers)
    """
    app, client, user = await prepare_client(aiohttp_client)

    # coworkers
    coworker1 = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, user)
    reviewers = [coworker1, coworker2]

    await prepare_review_requests(client, document, user, reviewers=reviewers, is_required=True)

    # Add approve review and delete user from company
    await prepare_review(
        client=client,
        document=document,
        user=coworker1,
        review_type=ReviewType.approve,
        expected_status=HTTPStatus.CREATED,
    )
    async with app['db'].acquire() as conn:
        await update_role(
            conn=conn,
            role_id=coworker1.role_id,
            data={'status': RoleStatus.deleted},
        )

    # Success update reviews
    await prepare_review_requests(client, document, user, reviewers=reviewers, is_required=True)
    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(conn, user.company_id, [document.id])
    assert len(review_requests) == 2

    # Case where we reject approve: can't update reviewers
    async with app['db'].acquire() as conn:
        await conn.execute(
            sa.update(review_table)
            .where(review_table.c.document_id == document.id)
            .values({'type': None})
        )

    await prepare_review_requests(
        client, document=document, initiator=user, reviewers=reviewers, is_required=True
    )
    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(conn, user.company_id, [document.id])
    assert len(review_requests) == 1


@pytest.mark.parametrize(
    'update_data_1, expected_1, update_data_2, expected_2',
    [
        (
            # Replace reviewer 1 by reviewer 2
            {
                'reviews_settings': {
                    'reviewers': [{'id': TEST_UUID_2, 'type': ROLE}],
                    'is_required': True,
                    'is_ordered': False,
                },
            },
            [TEST_UUID_2],
            {
                'reviews_settings': {
                    'reviewers': [{'id': TEST_UUID_3, 'type': ROLE}],
                    'is_required': True,
                    'is_ordered': False,
                },
            },
            [TEST_UUID_3],
        ),
        (
            # Delete old reviewers
            {
                'reviews_settings': {
                    'reviewers': [
                        {'id': TEST_UUID_2, 'type': ROLE},
                        {'id': TEST_UUID_3, 'type': ROLE},
                    ],
                    'is_required': True,
                    'is_ordered': False,
                },
            },
            [TEST_UUID_2, TEST_UUID_3],
            {
                'reviews_settings': {
                    'reviewers': [],
                    'is_required': False,
                    'is_ordered': False,
                },
            },
            [],
        ),
        (
            # Change reviewers order
            {
                'reviews_settings': {
                    'reviewers': [
                        {'id': TEST_UUID_2, 'type': ROLE},
                        {'id': TEST_UUID_3, 'type': ROLE},
                        {'id': TEST_UUID_4, 'type': ROLE},
                    ],
                    'is_required': False,
                    'is_ordered': True,
                },
            },
            [TEST_UUID_2, TEST_UUID_3, TEST_UUID_4],
            {
                'reviews_settings': {
                    'reviewers': [
                        {'id': TEST_UUID_4, 'type': ROLE},
                        {'id': TEST_UUID_3, 'type': ROLE},
                        {'id': TEST_UUID_2, 'type': ROLE},
                    ],
                    'is_required': False,
                    'is_ordered': True,
                },
            },
            [TEST_UUID_4, TEST_UUID_3, TEST_UUID_2],
        ),
        (
            # Change ordered review to parallel
            {
                'reviews_settings': {
                    'reviewers': [
                        {'id': TEST_UUID_2, 'type': ROLE},
                        {'id': TEST_UUID_3, 'type': ROLE},
                    ],
                    'is_required': True,
                    'is_ordered': True,
                },
            },
            [TEST_UUID_2, TEST_UUID_3],
            {
                'reviews_settings': {
                    'reviewers': [
                        {'id': TEST_UUID_3, 'type': ROLE},
                        {'id': TEST_UUID_2, 'type': ROLE},
                    ],
                    'is_required': True,
                    'is_ordered': False,
                },
            },
            [TEST_UUID_2, TEST_UUID_3],
        ),
    ],
)
async def test_update_replace_reviewers(
    aiohttp_client,
    update_data_1,
    expected_1,
    update_data_2,
    expected_2,
):
    app, client, user = await prepare_client(aiohttp_client, role_id=TEST_UUID_1)
    await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_2)
    await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_3)
    await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_4)

    document = await prepare_document_data(app, user)

    async def update_reviews(data: dict, expected: dict) -> None:
        response = await client.patch(
            f'/internal-api/documents/{document.id}',
            json={**data},
            headers=prepare_auth_headers(user),
        )
        assert response.status == HTTPStatus.OK, await response.json()

        async with services.db.acquire() as conn:
            requests = await select_review_requests(
                conn=conn,
                company_id=user.company_id,
                document_ids=[document.id],
                sort_by_order=True,
            )
            result = [item.to_role_id for item in requests]

            if data['reviews_settings']['is_ordered']:
                assert not [r for r in requests if r.order is None]

                result = [r.to_role_id for r in sorted(requests, key=lambda r: r.order)]
                assert expected == result
            else:
                assert not [r for r in requests if r.order is not None]
                assert sorted(expected) == sorted(result)

            if data['reviews_settings']['is_required']:
                settings = await select_one(conn, review_setting_table.select())
                reviews_settings = data['reviews_settings']
                assert settings.is_required == reviews_settings['is_required']

    await update_reviews(
        data=update_data_1,
        expected=expected_1,
    )
    await update_reviews(
        data=update_data_2,
        expected=expected_2,
    )


@pytest.mark.parametrize(
    'is_new_api',
    [
        True,
        False,
    ],
)
@pytest.mark.parametrize(
    'data, documents_fields, create_extra_before_request, status, expected',
    [
        # invalid data
        (
            'HELLO WORLD',
            [],
            False,
            HTTPStatus.BAD_REQUEST,
            set(),
        ),
        # empty parameters list is allowed
        (
            {'update_parameters': [], 'delete_parameters': []},
            [],
            False,
            HTTPStatus.OK,
            set(),
        ),
        # one value from two optional fields is OK.
        (
            {
                'update_parameters': [
                    {'field_id': TEST_UUID_1, 'value': 'HELLO', 'is_required': False},
                ],
                'delete_parameters': [],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.text,
                    'is_required': False,
                },
                {
                    'id': TEST_UUID_2,
                    'name': 'name2',
                    'type': DocumentFieldType.number,
                    'is_required': False,
                },
            ],
            False,
            HTTPStatus.OK,
            {(TEST_UUID_1, 'HELLO')},
        ),
        # two value from two optional and required field is also OK.
        (
            {
                'update_parameters': [
                    {'field_id': TEST_UUID_2, 'value': '1.2', 'is_required': False},
                ],
                'delete_parameters': [],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.text,
                    'is_required': False,
                },
                {
                    'id': TEST_UUID_2,
                    'name': 'name2',
                    'type': DocumentFieldType.number,
                    'is_required': True,
                },
            ],
            False,
            HTTPStatus.OK,
            {(TEST_UUID_2, '1.2')},
        ),
        # Invalid because value of optional field has invalid type
        (
            {
                'update_parameters': [
                    {'field_id': TEST_UUID_1, 'value': 'HELLO', 'is_required': False},
                ],
                'delete_parameters': [],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.number,
                    'is_required': False,
                },
            ],
            False,
            HTTPStatus.BAD_REQUEST,
            set(),
        ),
        # Not existed fields will be skipped
        (
            {
                'update_parameters': [
                    {'field_id': TEST_UUID_2, 'value': 'HELLO', 'is_required': False},
                ],
                'delete_parameters': [],
            },
            [],
            False,
            HTTPStatus.OK,
            set(),
        ),
        # Invalid because field is not owned by current company
        (
            {
                'update_parameters': [
                    {'field_id': TEST_UUID_5, 'value': 'HELLO', 'is_required': False},
                ],
                'delete_parameters': [],
            },
            [],
            False,
            HTTPStatus.OK,
            set(),
        ),
        # check value update
        (
            {
                'update_parameters': [
                    {'field_id': TEST_UUID_1, 'value': 'NEW', 'is_required': False},
                ],
                'delete_parameters': [],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.text,
                },
                {
                    'id': TEST_UUID_2,
                    'name': 'name2',
                    'type': DocumentFieldType.number,
                },
            ],
            True,
            HTTPStatus.OK,
            {(TEST_UUID_1, 'NEW'), (TEST_UUID_2, 'BLABLA')},
        ),
        # check that bad request doesn't override previous value
        (
            {
                'update_parameters': [
                    {'field_id': '1'},
                ],
                'delete_parameters': [],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.text,
                },
                {
                    'id': TEST_UUID_2,
                    'name': 'name2',
                    'type': DocumentFieldType.number,
                },
            ],
            True,
            HTTPStatus.BAD_REQUEST,
            {(TEST_UUID_1, 'HELLO'), (TEST_UUID_2, 'BLABLA')},
        ),
        # check delete parameter
        (
            {
                'update_parameters': [],
                'delete_parameters': [TEST_UUID_1],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.text,
                },
                {
                    'id': TEST_UUID_2,
                    'name': 'name2',
                    'type': DocumentFieldType.number,
                },
            ],
            True,
            HTTPStatus.OK,
            {(TEST_UUID_2, 'BLABLA')},
        ),
        # replace fields
        (
            {
                'add': [],
                'remove': [],
                'replace': [
                    {'field_id': TEST_UUID_1, 'value': 'NEW 42', 'is_required': False},
                ],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.text,
                },
                {
                    'id': TEST_UUID_2,
                    'name': 'name2',
                    'type': DocumentFieldType.number,
                },
            ],
            True,
            HTTPStatus.OK,
            {(TEST_UUID_1, 'NEW 42')},
        ),
        (
            {
                'add': [],
                'remove': [],
                'replace': [],
            },
            [
                {
                    'id': TEST_UUID_1,
                    'name': 'name1',
                    'type': DocumentFieldType.text,
                },
                {
                    'id': TEST_UUID_2,
                    'name': 'name2',
                    'type': DocumentFieldType.number,
                },
            ],
            True,
            HTTPStatus.OK,
            set(),
        ),
    ],
)
async def test_update_document_parameters(
    aiohttp_client,
    data,
    documents_fields,
    status,
    create_extra_before_request,
    expected,
    is_new_api,
):
    config = {'custom_documents_fields_enabled': True}
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    await set_billing_company_config(company_id=user.company_id, **config)

    alien = await prepare_user_data(app, email='a1@e.c', company_edrpou='99999999')
    await set_billing_company_config(company_id=alien.company_id, **config)

    await insert_values(
        app=app,
        table=documents_fields_table,
        company_id=alien.company_id,
        created_by=alien.role_id,
        id=TEST_UUID_5,
        name='name1',
        type=DocumentFieldType.number,
        is_required=False,
    )
    await insert_values(
        app=app,
        table=document_parameters_table,
        company_id=alien.company_id,
        document_id=document.id,
        field_id=TEST_UUID_5,
        updated_by=alien.role_id,
        value='123',
    )

    for document_field in documents_fields:
        await insert_values(
            app=app,
            table=documents_fields_table,
            company_id=user.company_id,
            created_by=user.role_id,
            **document_field,
        )

    if create_extra_before_request:
        for parameter in DEFAULT_DOCUMENT_PARAMETERS:
            await insert_values(
                app=app,
                table=document_parameters_table,
                company_id=user.company_id,
                document_id=document.id,
                updated_by=user.role_id,
                **parameter,
            )

    if is_new_api and 'update_parameters' in data:
        data = {
            'add': data['update_parameters'],
            'remove': data['delete_parameters'],
            'replace': [],
        }

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'parameters_settings': data,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == status, await response.json()
    async with app['db'].acquire() as conn:
        parameters = await select_all(conn, document_parameters_table.select())
        alien_parameters = [p for p in parameters if p.company_id == alien.company_id]
        assert len(alien_parameters) == 1
        assert alien_parameters[0].field_id == TEST_UUID_5
        assert alien_parameters[0].value == '123'

        user_parameters = [p for p in parameters if p.company_id == user.company_id]
        assert len(user_parameters) == len(expected)
        assert {(p.field_id, p.value) for p in user_parameters} == expected


@pytest.mark.parametrize(
    'recipient_email',
    [
        TEST_RECIPIENT_EMAIL,
        TEST_RECIPIENT_ANOTHER_EMAIL,
        TEST_RECIPIENT_EMAILS,
    ],
)
async def test_change_recipient(aiohttp_client, recipient_email):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    response = await change_recipient(
        client,
        user,
        document.id,
        edrpou=TEST_RECIPIENT_EDRPOU,
        emails=recipient_email.split(', '),
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.email_recipient == recipient_email
        recipient = await select_bilateral_document_recipient(
            conn=conn,
            document_id=document.id,
            document_owner_edrpou=document.edrpou_owner,
        )
        assert set(recipient.emails) == set(recipient_email.split(', '))
        listings = await select_document_listings(
            conn=conn,
            document_id=document.id,
            edrpou=TEST_RECIPIENT_EDRPOU,
        )
        assert len(listings) == 0


async def test_change_recipient_after_sign_multiple_signers(aiohttp_client, mailbox):
    """
    Context:
        Owner - expected 2 signatures, 1 signature already applied
        Recipient - expected 1 signature

    Owner tries to change recipient, recipient shouldn't get access to document
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    recipient1 = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        owner,
        recipient_edrpou=recipient1.company_edrpou,
        recipient_email=recipient1.email,
        expected_owner_signatures=2,
        first_sign_by=FirstSignBy.owner,
        create_document_access_for_recipients=False,
    )

    response = await sign_document(
        client,
        document_id=document.id,
        signer=owner,
    )
    assert response.status == HTTPStatus.CREATED

    # Try to change recipient to same
    resp = await change_recipient(
        client=client,
        user=owner,
        document_id=document.id,
        edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        emails=[TEST_DOCUMENT_EMAIL_RECIPIENT],
    )
    assert resp.status == HTTPStatus.OK

    # check that only owner has access to document
    async with services.db.acquire() as conn:
        listings = await select_listings(conn, [document.id])
        assert len(listings) == 1
        assert listings[0].access_edrpou == owner.company_edrpou
        updated_document = await get_document(document_id=document.id)
        assert updated_document.status_id == DocumentStatus.signed.value

    # Do not send emails to recipient before opening access
    assert len(mailbox) == 0

    # Try to change recipient to another
    resp = await change_recipient(
        client=client,
        user=owner,
        document_id=document.id,
        edrpou=COMPANY_EDRPOU_1,
        emails=[TEST_RECIPIENT_EMAIL],
    )
    assert resp.status == HTTPStatus.OK

    # check that only owner has access to document
    async with services.db.acquire() as conn:
        listings = await select_listings(conn, [document.id])
        assert len(listings) == 1
        assert listings[0].access_edrpou == owner.company_edrpou
        updated_document = await get_document(document_id=document.id)
        assert updated_document.status_id == DocumentStatus.signed.value
        assert COMPANY_EDRPOU_1 != TEST_DOCUMENT_EDRPOU_RECIPIENT
        assert updated_document.edrpou_recipient == COMPANY_EDRPOU_1
        assert updated_document.email_recipient == TEST_RECIPIENT_EMAIL

    # Do not send emails to recipient before opening access
    assert len(mailbox) == 0


async def test_change_recipient_lose_access_by_email(aiohttp_client, mailbox):
    """
    Check that recipient emails lose access to document after changing emails to another
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    recipient1 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_1, email=TEST_EMAIL_1)
    recipient2 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_1, email=TEST_EMAIL_2)
    recipient3 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_1, email=TEST_EMAIL_3)

    document = await prepare_document_data(
        app=app,
        owner=owner,
        document_recipients=[
            {'edrpou': COMPANY_EDRPOU_1, 'emails': [TEST_EMAIL_1, TEST_EMAIL_3]},
        ],
        expected_owner_signatures=1,
        first_sign_by=FirstSignBy.recipient,
        create_document_access_for_recipients=True,
    )

    listings = await get_listings(document_id=document.id)
    assert len(listings) == 3  # owner + recipient1 + recipient3
    recipient_listings = [item for item in listings if item.access_edrpou == COMPANY_EDRPOU_1]
    assert len(recipient_listings) == 2
    assert {item.role_id for item in recipient_listings} == {recipient1.role_id, recipient3.role_id}

    company_listings = await get_company_listing(document_id=document.id)
    company_recipient_listings_before = [
        cl for cl in company_listings if cl.edrpou == COMPANY_EDRPOU_1
    ][0]

    # Try to change the recipient to another email, but the same company
    response = await change_recipient(
        client=client,
        user=owner,
        document_id=document.id,
        edrpou=COMPANY_EDRPOU_1,
        emails=[TEST_EMAIL_2, TEST_EMAIL_3],
    )
    assert response.status == HTTPStatus.OK

    listings = await get_listings(document_id=document.id)
    assert len(listings) == 3  # owner + recipient2 + recipient3
    recipient_listings = [item for item in listings if item.access_edrpou == COMPANY_EDRPOU_1]
    assert len(recipient_listings) == 2
    # recipient1 should lose access to document
    assert {item.role_id for item in recipient_listings} == {recipient2.role_id, recipient3.role_id}

    # company listing should be the same
    company_listings = await get_company_listing(document_id=document.id)
    company_recipient_listings_after = [
        cl for cl in company_listings if cl.edrpou == COMPANY_EDRPOU_1
    ][0]
    assert company_recipient_listings_before.id == company_recipient_listings_after.id

    updated_document = await get_document(document_id=document.id)
    assert updated_document.status_id == DocumentStatus.sent.value
    assert updated_document.edrpou_recipient == COMPANY_EDRPOU_1
    assert updated_document.email_recipient == ', '.join([TEST_EMAIL_2, TEST_EMAIL_3])

    # Emails about a new document to recipient2 and recipient3 should be sent
    assert len(mailbox) == 2
    assert {item['To'] for item in mailbox} == {TEST_EMAIL_2, TEST_EMAIL_3}


async def test_change_recipient_after_sign(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    recipient1 = await prepare_user_data(
        app,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        email=TEST_RECIPIENT_EMAIL,
    )
    await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=True,
        another_recipients=[recipient1],
        expected_recipient_signatures=0,
        status_id=DocumentStatus.finished.value,
    )
    document_id = document.id

    async with with_elastic(app, [document.id]):
        # 2 different documents for 2 access_edrpou
        es_docs = await get_es_documents(documents_ids=[document.id])
        result_es_edrpou = [doc.access_edrpou for doc in es_docs]
        assert sorted(result_es_edrpou) == sorted([document.edrpou_owner, TEST_RECIPIENT_EDRPOU])

        response = await change_recipient(
            client,
            user,
            document_id,
            edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            emails=TEST_DOCUMENT_EMAIL_RECIPIENT.split(', '),
        )

        # Check that we correctly remove recipient1 document from ES,
        # because it loses his access
        es_docs = await get_es_documents(documents_ids=[document.id])
        result_es_edrpou = [doc.access_edrpou for doc in es_docs]
        assert TEST_RECIPIENT_EDRPOU not in result_es_edrpou

    assert response.status == 200

    async with app['db'].acquire() as conn:
        assert await exists_listing(
            conn,
            document_id,
            TEST_DOCUMENT_EDRPOU_RECIPIENT,
        )


async def test_change_recipient_for_internal_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, is_internal=True)

    response = await change_recipient(
        client,
        user,
        document.id,
        edrpou=TEST_RECIPIENT_EDRPOU,
        emails=TEST_RECIPIENT_EMAIL.split(', '),
    )
    assert response.status == 400

    data = await response.json()
    assert data['code'] == 'invalid_action'


@pytest.mark.parametrize(
    'recipient_email',
    [
        TEST_RECIPIENT_EMAIL,
        TEST_RECIPIENT_EMAILS,
    ],
)
async def test_change_recipient_invalid_email(aiohttp_client, recipient_email):
    app, client, owner = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner)
    await prepare_user_data(
        app, company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT, email=TEST_RECIPIENT_EMAIL
    )

    response = await change_recipient(
        client,
        owner,
        document.id,
        edrpou=TEST_RECIPIENT_EDRPOU,
        emails=recipient_email.split(', '),
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
    assert document.status_id == DocumentStatus.ready_to_be_signed.value
    assert document.email_recipient == recipient_email
    assert document.edrpou_recipient == TEST_RECIPIENT_EDRPOU


async def test_change_recipient_to_self(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    try:
        response = await client.post(
            f'/internal-api/documents/{document.id}/change-recipient',
            data=ujson.dumps(
                {
                    'edrpou': user.company_edrpou,
                    'emails': [user.email],
                    'is_emails_hidden': False,
                }
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
        data = await response.json()
        assert data['code'] == 'reject_self_recipient'
    finally:
        await cleanup_on_teardown(app)


async def test_change_recipient_delete_document_tags(aiohttp_client):
    """All document tags assigned by previous recipient must be deleted.

    Given:
        - user is able to change document recipient.
    When:
        - user successfully changed document recipient.
    Then:
        - document tags from previous recipient must be deleted.
    """
    app, client, owner = await prepare_client(aiohttp_client)
    first_recipient = await prepare_user_data(
        app, company_edrpou='22222222', email='<EMAIL>'
    )
    second_recipient = await prepare_user_data(
        app, company_edrpou='********', email='<EMAIL>'
    )

    document = await prepare_document_data(app, owner)
    resp = await change_recipient(
        client,
        owner,
        document.id,
        edrpou=first_recipient.company_edrpou,
        emails=[first_recipient.email],
    )
    assert resp.status == 200

    async with services.db.acquire() as conn:
        await create_new_tags_for_documents(
            conn=conn,
            tags_names=['initial tag'],
            documents_ids=[document.id],
            company_id=first_recipient.company_id,
            company_edrpou=first_recipient.company_edrpou,
            assigner_role_id=first_recipient.role_id,
        )
        document_tags = await select_tags_by_documents(conn, [document.id])
        assert len(document_tags) == 1

    resp = await change_recipient(
        client,
        owner,
        document.id,
        edrpou=second_recipient.company_edrpou,
        emails=[second_recipient.email],
    )
    assert resp.status == 200

    async with services.db.acquire() as conn:
        document_tags = await select_tags_by_documents(conn, [document.id])
        assert len(document_tags) == 0


@pytest.mark.parametrize(
    'is_internal_document, document_status_id, is_admin, is_document_uploader, with_recipient',
    [
        # Admin can delete any internal document in any status
        (True, DocumentStatus.uploaded.value, True, True, False),
        (True, DocumentStatus.finished.value, True, True, False),
        (True, DocumentStatus.ready_to_be_signed.value, True, True, False),
        (True, DocumentStatus.uploaded.value, True, False, False),
        (True, DocumentStatus.finished.value, True, False, False),
        (True, DocumentStatus.ready_to_be_signed.value, True, False, False),
        # Not admin can delete only own internal document in any status
        (True, DocumentStatus.uploaded.value, False, True, False),
        (True, DocumentStatus.finished.value, False, True, False),
        # Not internal document cases
        (False, DocumentStatus.uploaded.value, False, True, False),
        (False, DocumentStatus.uploaded.value, True, True, False),
        (False, DocumentStatus.uploaded.value, True, False, False),
        (False, DocumentStatus.uploaded.value, False, True, True),
        (False, DocumentStatus.uploaded.value, True, True, True),
        (False, DocumentStatus.uploaded.value, True, False, True),
    ],
)
@pytest.mark.parametrize('with_signature', [True, False])
@pytest.mark.parametrize('has_internal_signature', [True, False])
async def test_delete_document_successful_cases(
    aiohttp_client,
    with_recipient,
    has_internal_signature,
    with_signature,
    is_internal_document,
    document_status_id,
    is_admin,
    is_document_uploader,
    s3_emulation,
):
    app, client = await prepare_app_client(aiohttp_client)

    user_role = UserRole.admin.value if is_admin else UserRole.user.value
    if is_document_uploader:
        requester = uploader = await prepare_user_data(
            app=app, email='<EMAIL>', user_role=user_role
        )
    else:
        requester = await prepare_user_data(app=app, email='<EMAIL>', user_role=user_role)
        uploader = await prepare_user_data(app, email='<EMAIL>')

    if is_internal_document:
        # Internal document
        document = await prepare_document_data(
            app=app,
            owner=uploader,
            another_owners=[requester],
            is_internal=True,
            status_id=document_status_id,
        )
    elif with_recipient:
        # Bilateral document with recipient
        recipient = await prepare_user_data(
            app,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        )
        document = await prepare_document_data(
            app,
            uploader,
            another_recipients=[recipient],
            another_owners=[requester],
            status_id=document_status_id,
        )
    else:
        # Bilateral document without recipient
        document = await prepare_document_data(
            app, uploader, status_id=document_status_id, another_owners=[requester]
        )

    assert len(s3_emulation) == 1

    # Append document owner signature to document
    if with_signature:
        # External and internal signature will have 1 file on s3
        private_key = prepare_private_key(
            user=uploader,
            is_internal=has_internal_signature,
            with_stamp=False,
        )
        next_status = (
            DocumentStatus.signed.value
            if is_internal_document
            else DocumentStatus.signed_and_sent.value
        )
        await prepare_signature_data(
            app=app,
            owner=uploader,
            document=document,
            is_internal=has_internal_signature,
            update_access=False,
            private_key=private_key,
            next_status_id=next_status,
        )

        assert len(s3_emulation) == 2

    await request_delete_document(client=client, user=requester, document=document)

    async with app['db'].acquire() as conn:
        # Make sure that document and signatures were deleted from database and s3
        assert await count(conn, document_table) == 0
        assert await count(conn, signature_table) == 0
        assert len(s3_emulation) == 0

    actions = await select_document_actions_for(document_id=document.id)
    assert len(actions) == 1
    assert actions[0].action == document_actions.Action.document_delete
    assert actions[0].email == requester.email


@pytest.mark.parametrize(
    'prepare_recipients, update_recipients, owner_actions, expected',
    [
        pytest.param(
            [
                {'company_edrpou': COMPANY_EDRPOU_2, 'email': TEST_EMAIL_2},
                {'company_edrpou': COMPANY_EDRPOU_3, 'email': TEST_EMAIL_3},
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {'sign': False, 'send': False},
            {'status': HTTPStatus.NO_CONTENT},
            id='ordered_not_send_to_recipients',
        ),
        pytest.param(
            [
                {'company_edrpou': COMPANY_EDRPOU_2, 'email': TEST_EMAIL_2},
                {'company_edrpou': COMPANY_EDRPOU_3, 'email': TEST_EMAIL_3},
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {'sign': True, 'send': True},
            {
                'status': HTTPStatus.NO_CONTENT,
            },
            id='ordered_send_to_recipients',
        ),
        pytest.param(
            [
                {'company_edrpou': COMPANY_EDRPOU_3, 'email': TEST_EMAIL_3},
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,  # not register, but received
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,  # register, but not received
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {'sign': True, 'send': True},
            {
                # no registered recipients that received document
                'status': HTTPStatus.NO_CONTENT,
            },
            id='send_to_unregistered_recipient',
        ),
        pytest.param(
            [],
            {
                'is_ordered': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,  # not register, but received
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,  # register, but not received
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {'sign': True, 'send': True},
            {
                # no registered recipients that received document
                'status': HTTPStatus.NO_CONTENT,
            },
            id='send_to_all_unregistered_recipient',
        ),
        pytest.param(
            [
                {'company_edrpou': COMPANY_EDRPOU_3, 'email': TEST_EMAIL_3},
            ],
            {
                'is_ordered': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {'send': True},
            {
                'status': HTTPStatus.NO_CONTENT,
            },
            id='send_to_registered_and_unregistered_recipient',
        ),
        pytest.param(
            [
                {'company_edrpou': COMPANY_EDRPOU_3, 'email': TEST_EMAIL_3},
            ],
            {
                'is_ordered': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {'send': True},
            {
                'status': HTTPStatus.NO_CONTENT,
            },
            id='send_to_registered_and_unregistered_recipient',
        ),
    ],
)
async def test_delete_multilateral_document(
    aiohttp_client,
    prepare_recipients: list[dict],
    update_recipients: dict,
    owner_actions: dict[str, bool],
    expected: dict,
):
    app, client, owner = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=COMPANY_EDRPOU_1,
        email=TEST_EMAIL_1,
    )
    document = await prepare_document_data(
        app=app,
        owner=owner,
        status_id=DocumentStatus.flow.value,
        is_multilateral=True,
    )
    for recipient in prepare_recipients:
        await prepare_user_data(app, **recipient)

    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
    )
    assert response.status == HTTPStatus.OK, await response.json()

    if owner_actions.get('sign') and owner_actions.get('send'):
        await sign_and_send_document(client, document_id=document.id, signer=owner)
    elif owner_actions.get('sign'):
        await sign_document(client, document_id=document.id, signer=owner)
    elif owner_actions.get('send'):
        await send_document(client, document_id=document.id, sender=owner)

    response_json = await request_delete_document(
        client=client,
        user=owner,
        document=document,
        status=expected['status'],
    )

    if expected['status'] >= HTTPStatus.BAD_REQUEST:
        assert response_json['reason'] == expected['error_reason']
    elif expected['status'] == HTTPStatus.NO_CONTENT:
        document = await get_document(document_id=document.id)
        assert document is None


@pytest.mark.parametrize(
    'coworker_data, document_data, create_recipient, owner_actions, by_owner, expected',
    [
        # Not allow deleting document for recipient in any status
        pytest.param(
            {},
            {'status_id': DocumentStatus.uploaded.value},
            True,
            {'sign': False, 'send': False},
            False,
            {
                'status': HTTPStatus.FORBIDDEN,
                'error_reason': (
                    'Неможливо видалити документ, оскільки ваша компанія не є власником '
                    'цього документу'
                ),
            },
            id='not_sent_to_recipient',
        ),
        pytest.param(
            {},
            {'status_id': DocumentStatus.ready_to_be_signed.value},
            True,
            {'sign': True, 'send': True},
            False,
            {
                'status': HTTPStatus.FORBIDDEN,
                'error_reason': (
                    'Неможливо видалити документ, оскільки ваша компанія не є власником '
                    'цього документу'
                ),
            },
            id='signed_and_sent_to_recipient',
        ),
        pytest.param(
            {},
            {
                'status_id': DocumentStatus.ready_to_be_signed.value,
                'first_sign_by': FirstSignBy.recipient,
            },
            True,
            {'sign': False, 'send': True},
            False,
            {
                'status': HTTPStatus.FORBIDDEN,
                'error_reason': (
                    'Неможливо видалити документ, оскільки ваша компанія не є власником '
                    'цього документу'
                ),
            },
            id='sent_to_recipient_first_sign_by_recipient',
        ),
        pytest.param(
            {},
            {
                'status_id': DocumentStatus.ready_to_be_signed.value,
                'expected_recipient_signatures': 0,
            },
            True,
            {'sign': True, 'send': True},
            False,
            {
                'status': HTTPStatus.FORBIDDEN,
                'error_reason': (
                    'Неможливо видалити документ, оскільки ваша компанія не є власником '
                    'цього документу'
                ),
            },
            id='sent_to_recipient_no_expected_recipient_signatures',
        ),
        # Now check when coworker have permission to delete document
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.uploaded.value},
            True,
            {'sign': False, 'send': False},
            True,
            {
                'status': HTTPStatus.NO_CONTENT,
            },
            id='can_delete_uploaded_document',
        ),
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.ready_to_be_signed.value},
            True,
            {'sign': True, 'send': True},
            True,
            {
                'status': HTTPStatus.NO_CONTENT,
            },
            id='delete_signed_and_sent_document',
        ),
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {
                'status_id': DocumentStatus.ready_to_be_signed.value,
                'first_sign_by': FirstSignBy.recipient,
            },
            True,
            {'sign': False, 'send': True},
            True,
            {
                'status': HTTPStatus.NO_CONTENT,
            },
            id='delete_sent_to_recipient_first_sign_by_recipient',
        ),
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {
                'status_id': DocumentStatus.ready_to_be_signed.value,
                'expected_recipient_signatures': 0,
            },
            True,
            {'sign': True, 'send': True},
            True,
            {
                'status': HTTPStatus.FORBIDDEN,
                'error_reason': 'Неможливо видалити завершений документ',
            },
            id='delete_sent_to_recipient_no_expected_recipient_signatures_1',
        ),
        pytest.param(
            {'user_role': UserRole.admin.value, 'can_delete_document': False},
            {
                'status_id': DocumentStatus.ready_to_be_signed.value,
                'expected_recipient_signatures': 0,
            },
            True,
            {'sign': True, 'send': True},
            True,
            {
                'status': HTTPStatus.FORBIDDEN,
                'error_reason': 'Неможливо видалити завершений документ',
            },
            id='delete_sent_to_recipient_no_expected_recipient_signatures_2',
        ),
        pytest.param(
            {'user_role': UserRole.admin.value, 'can_delete_document': False},
            {
                'status_id': DocumentStatus.ready_to_be_signed.value,
                'first_sign_by': FirstSignBy.recipient,
            },
            True,
            {'sign': False, 'send': True},
            True,
            {
                'status': HTTPStatus.NO_CONTENT,
            },
            id='delete_sent_to_recipient_first_sign_by_recipient',
        ),
        # Recipient is not created
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {
                'status_id': DocumentStatus.ready_to_be_signed.value,
                'expected_recipient_signatures': 0,
            },
            True,
            {'sign': True, 'send': True},
            True,
            {
                'status': HTTPStatus.FORBIDDEN,
                'error_reason': 'Неможливо видалити завершений документ',
            },
            id='recipient_is_not_created_finished',
        ),
    ],
)
async def test_delete_document_bilateral(
    aiohttp_client,
    coworker_data: dict,
    document_data: dict,
    create_recipient: bool,
    owner_actions: dict[str, bool],
    by_owner: bool,
    expected: dict,
):
    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)

    coworker = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        **coworker_data,
    )

    if create_recipient:
        recipient = await prepare_user_data(
            app,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        )

    document = await prepare_document_data(
        app=app,
        owner=user,
        recipient_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        recipient_email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        **document_data,
    )
    if owner_actions.get('sign') and owner_actions.get('send'):
        await sign_and_send_document(client, document_id=document.id, signer=user)
    elif owner_actions.get('sign'):
        await sign_document(client, document_id=document.id, signer=user)
    elif owner_actions.get('send'):
        await send_document(client, document_id=document.id, sender=user)

    target_user = coworker if by_owner else recipient
    response_json = await request_delete_document(
        client=client,
        user=target_user,
        document=document,
        status=expected['status'],
    )

    if expected['status'] >= HTTPStatus.BAD_REQUEST:
        assert response_json['reason'] == expected['error_reason']
    else:
        document = await get_document(document_id=document.id)
        assert document is None


@pytest.mark.parametrize(
    'coworker_data, document_data, expected_status',
    # 'status_id, is_admin, can_delete_document, is_internal, by_owner, status',
    [
        # Not allow to delete internal document by not admin in any status (except is uploaded)
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': False},
            {'status_id': DocumentStatus.uploaded.value},
            HTTPStatus.FORBIDDEN,
            id='not_admin_uploaded_document',
        ),
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': False},
            {'status_id': DocumentStatus.finished.value},
            HTTPStatus.FORBIDDEN,
            id='not_admin_finished_document',
        ),
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': False},
            {'status_id': DocumentStatus.signed.value},
            HTTPStatus.FORBIDDEN,
            id='not_admin_sent_document',
        ),
        # Allow to delete internal document by not admin if have can_delete permission
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.uploaded.value},
            HTTPStatus.NO_CONTENT,
            id='not_admin_can_delete_uploaded_document',
        ),
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.signed.value},
            HTTPStatus.NO_CONTENT,
            id='not_admin_can_delete_signed_document',
        ),
        pytest.param(
            {'user_role': UserRole.user.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.finished.value},
            HTTPStatus.FORBIDDEN,
            id='not_admin_can_not_delete_finished_document',
        ),
        # Allow to delete internal document by admin
        pytest.param(
            {'user_role': UserRole.admin.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.finished.value},
            HTTPStatus.NO_CONTENT,
            id='admin_can_delete_finished_document',
        ),
        pytest.param(
            {'user_role': UserRole.admin.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.uploaded.value},
            HTTPStatus.NO_CONTENT,
            id='admin_can_delete_uploaded_document',
        ),
        pytest.param(
            {'user_role': UserRole.admin.value, 'can_delete_document': True},
            {'status_id': DocumentStatus.signed.value},
            HTTPStatus.NO_CONTENT,
            id='admin_can_delete_signed_document',
        ),
    ],
)
async def test_delete_document_internal(
    aiohttp_client,
    coworker_data: dict,
    document_data: dict,
    expected_status: HTTPStatus,
):
    app, client, user = await prepare_client(aiohttp_client)

    coworker = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        **coworker_data,
    )

    document = await prepare_document_data(
        app=app,
        owner=user,
        is_internal=True,
        **document_data,
    )

    await request_delete_document(
        client=client,
        user=coworker,
        document=document,
        status=expected_status,
    )


@pytest.mark.parametrize(
    'create_recipient,'
    'status,'
    'first_sign_by,'
    'expected_owner_signatures,'
    'expected_recipient_signatures,'
    'by_owner,'
    'expected_status,'
    'expected_mailbox_len,'
    'expected_mail_to_recipient,'
    'expected_charge,'
    'expected_tags,'
    'is_empty_date_sent,'
    'send_sms,',
    [
        (
            False,
            DocumentStatus.uploaded,
            FirstSignBy.recipient,
            1,
            1,
            True,
            DocumentStatus.sent,
            1,
            True,
            True,
            ['tag1'],
            False,
            False,
        ),
        (
            True,
            DocumentStatus.uploaded,
            FirstSignBy.recipient,
            1,
            1,
            True,
            DocumentStatus.sent,
            1,
            True,
            True,
            ['tag1'],
            False,
            False,
        ),
        (
            False,
            DocumentStatus.ready_to_be_signed,
            FirstSignBy.recipient,
            1,
            1,
            True,
            DocumentStatus.sent,
            1,
            True,
            True,
            ['tag1'],
            False,
            False,
        ),
        (
            True,
            DocumentStatus.ready_to_be_signed,
            FirstSignBy.recipient,
            1,
            1,
            True,
            DocumentStatus.sent,
            1,
            True,
            True,
            ['tag1'],
            False,
            False,
        ),
        (
            False,
            DocumentStatus.sent,
            FirstSignBy.recipient,
            1,
            1,
            True,
            DocumentStatus.sent,
            1,
            True,
            False,
            ['tag1'],
            False,
            False,
        ),
        (
            True,
            DocumentStatus.sent,
            FirstSignBy.recipient,
            1,
            1,
            True,
            DocumentStatus.sent,
            1,
            True,
            False,
            ['tag1'],
            False,
            False,
        ),
        (
            True,
            DocumentStatus.signed,
            FirstSignBy.owner,
            1,
            1,
            True,
            DocumentStatus.signed_and_sent,
            1,
            True,
            True,
            ['tag1'],
            False,
            False,
        ),
        (
            True,
            DocumentStatus.signed,
            FirstSignBy.recipient,
            1,
            1,
            False,
            DocumentStatus.signed_and_sent,
            0,  # todo: change to 1 after making workers jobs works in tests
            False,
            False,
            [],
            True,
            True,
        ),
        (
            True,
            DocumentStatus.approved,
            FirstSignBy.owner,
            1,
            1,
            False,
            DocumentStatus.finished,
            0,
            True,
            False,
            [],
            True,
            False,
        ),
        (
            True,
            DocumentStatus.approved,
            FirstSignBy.recipient,
            1,
            1,
            True,
            DocumentStatus.finished,
            0,
            True,
            False,
            [],
            True,
            False,
        ),
        (
            True,
            DocumentStatus.approved,
            FirstSignBy.owner,
            1,
            0,
            True,
            DocumentStatus.finished,
            1,
            True,
            True,
            ['tag1'],
            False,
            False,
        ),
    ],
)
async def test_send_document(
    evo_sender_mock,
    mailbox,
    aiohttp_client,
    create_recipient,
    status,
    first_sign_by,
    expected_owner_signatures,
    expected_recipient_signatures,
    by_owner,
    expected_status,
    expected_mailbox_len,
    expected_mail_to_recipient,
    expected_charge,
    telegrambox,
    expected_tags,
    is_empty_date_sent,
    send_sms,
):
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_phone_verified=True,
    )
    document_data = await prepare_document_data_for_email(
        app,
        owner=owner,
        create_recipient=create_recipient,
        first_sign_by=first_sign_by,
        status_id=status.value,
        expected_owner_signatures=expected_owner_signatures,
        expected_recipient_signatures=expected_recipient_signatures,
    )
    document = document_data.document
    document_id = document.id
    recipient = (
        document_data.recipient
        if create_recipient
        else await prepare_user_data(
            app, company_edrpou=TEST_RECIPIENT_EDRPOU, email=TEST_RECIPIENT_EMAIL
        )
    )
    recipient_edrpou = recipient.company_edrpou
    recipient_email = recipient.email

    if status.value > DocumentStatus.sent.value:
        for _ in range(expected_owner_signatures):
            await prepare_signature_data(app, owner, document, next_status_id=status.value)

        for _ in range(expected_recipient_signatures):
            await prepare_signature_data(
                app,
                recipient,
                document,
                is_owner_signature=False,
                next_status_id=status.value,
            )

    # Create contact and tags for before sending
    async with app['db'].acquire() as conn:
        contact1_id = await prepare_contact(
            conn=conn, user=recipient, contact={'edrpou': owner.company_edrpou}
        )
        contact2_id = await prepare_contact(
            conn=conn, user=recipient, contact={'edrpou': '99999999'}
        )

        await request_create_tags_for_contacts(
            client=client, user=recipient, names=['tag1'], contacts_ids=[contact1_id]
        )
        await request_create_tags_for_contacts(
            client=client, user=recipient, names=['tag2'], contacts_ids=[contact2_id]
        )

        if send_sms:
            await update_company_config(
                conn=conn,
                company_id=owner.company_id,
                send_sms_to_document_receivers=True,
            )

    try:
        assert len(mailbox) == 0
        assert len(telegrambox) == 0

        response = await client.post(
            f'/internal-api/documents/{document_id}/send',
            data=ujson.dumps(
                {
                    'edrpou': recipient_edrpou,
                    'email': recipient_email,
                }
            ),
            headers=prepare_auth_headers(owner if by_owner else recipient),
        )
        assert response.status == 200

        assert len(mailbox) == expected_mailbox_len
        assert len(telegrambox) == expected_mailbox_len
        if by_owner and send_sms:
            assert owner.company_edrpou in evo_sender_mock.message
        if expected_mailbox_len > 0:
            assert mailbox[0]['To'] == (
                recipient_email if expected_mail_to_recipient else document_data.owner.email
            )

        async with app['db'].acquire() as conn:
            document = await select_document_by_id(conn, document_id)
            assert document.status_id == expected_status.value
            assert document.email_recipient == recipient_email
            assert document.edrpou_recipient == recipient_edrpou

            # Check account
            accounts = await select_company_accounts(conn, owner.company_id)
            expected_units_left = (
                TEST_BILLING_ACCOUNT['units'] - 1
                if expected_charge
                else TEST_BILLING_ACCOUNT['units']
            )
            assert accounts[0].units_left == expected_units_left
            # Check upload counter
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
            # Check transaction
            transactions = await select_company_transactions(conn, owner.company_id)
            assert len(transactions) == (1 if expected_charge else 0)
            if expected_charge:
                assert transactions[0].initiator_id == document_id

            assert await exists_listing(conn, document_id, owner.company_edrpou)
            assert await exists_listing(conn, document_id, recipient_edrpou)

            tags = await select_tags_by_documents(conn, documents_ids=[document.id])
            assert {tag.name for tag in tags} == set(expected_tags)

            recipient = await select_bilateral_document_recipient(
                conn=conn,
                document_id=document_id,
                document_owner_edrpou=document.edrpou_owner,
            )
            if is_empty_date_sent:
                assert recipient.date_sent is None
            else:
                assert recipient.date_sent is not None
    finally:
        await cleanup_on_teardown(app)


async def test_send_document_to_restricted_recipient(aiohttp_client):
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_phone_verified=True,
    )

    recipient = await prepare_user_data(
        app=app,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        email=TEST_RECIPIENT_EMAIL,
    )

    document_data = await prepare_document_data_for_email(
        app,
        owner=owner,
        create_recipient=False,
        first_sign_by=FirstSignBy.recipient,
        status_id=DocumentStatus.uploaded.value,
        expected_owner_signatures=0,
        expected_recipient_signatures=1,
    )
    document = document_data.document
    document_id = document.id

    with mock.patch('app.documents.utils.RESTRICTED_RECEIVING_DOCUMENTS_EMAILS', [recipient.email]):
        response = await client.post(
            f'/internal-api/documents/{document_id}/send',
            data=ujson.dumps(
                {
                    'edrpou': recipient.company_edrpou,
                    'email': recipient.email,
                }
            ),
            headers=prepare_auth_headers(owner),
        )
    assert response.status == 400
    data = await response.json()
    assert data['reason'] == 'Даний емейл не зареєстрований на Вчасно, вкажіть іншу адресу'


@pytest.mark.parametrize(
    'create_review, review_status, is_review_required, expected_emails',
    [
        (False, ReviewType.approve.value, True, True),
        (True, ReviewType.approve.value, True, True),
        (True, ReviewType.approve.value, False, True),
        (True, ReviewType.reject.value, True, False),
        (True, ReviewType.reject.value, False, True),
        (True, None, True, False),
        (True, None, False, True),
    ],
)
@pytest.mark.parametrize('can_receive_inbox, ', [True, False])
async def test_update_document_sign_process(
    aiohttp_client,
    mailbox,
    can_receive_inbox,
    create_review,
    review_status,
    is_review_required,
    expected_emails,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        can_receive_inbox=can_receive_inbox,
    )
    coworker1 = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_receive_inbox=can_receive_inbox,
    )
    coworker2 = await prepare_user_data(
        app, email='<EMAIL>', can_receive_inbox=can_receive_inbox
    )
    document_data = {
        'create_document_access_for_recipients': True,
        'document_recipients': [
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
    }
    recipient1 = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email='<EMAIL>',
        create_billing_account=True,
        can_receive_inbox=can_receive_inbox,
    )
    recipient2 = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email='<EMAIL>',
        can_receive_inbox=can_receive_inbox,
    )
    recipient3 = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email='<EMAIL>',
        can_receive_inbox=can_receive_inbox,
    )
    document = await prepare_document_data(app, user, **document_data)

    if create_review:
        await prepare_review_requests(
            client=client,
            document=document,
            initiator=user,
            reviewers=[coworker1, coworker2],
            is_required=is_review_required,
        )
        await prepare_review(
            client, user=coworker1, document=document, review_type=ReviewType.approve
        )
        await prepare_review(client, user=coworker2, document=document, review_type=review_status)
        mailbox.clear()

    # Initial check
    assert len(mailbox) == 0

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'parallel_signing': True,
                'entities': [],
            },
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.expected_owner_signatures == 1

    mailbox.clear()
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'parallel_signing': True,
                'entities': [
                    {'id': coworker1.role_id, 'type': 'role'},
                    {'id': coworker2.role_id, 'type': 'role'},
                    {'id': recipient1.role_id, 'type': 'role'},
                ],
            },
        },
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    assert len(mailbox) == 2 * can_receive_inbox * expected_emails

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.expected_owner_signatures == 2

    if review_status != 'approved':
        return

    await sign_and_send_document(
        client,
        document.id,
        coworker1,
        sign_data=prepare_signature_form_data(
            coworker1,
            owner=user,
            recipient=recipient1,
        ),
    )

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'parallel_signing': True,
                'entities': [
                    {'id': coworker1.role_id, 'type': 'role'},
                    {'id': coworker2.role_id, 'type': 'role'},
                    {'id': recipient1.role_id, 'type': 'role'},
                ],
            },
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 400

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.status_id == DocumentStatus.signed.value

    await sign_and_send_document(
        client,
        document.id,
        coworker2,
        sign_data=prepare_signature_form_data(
            coworker2,
            owner=user,
            recipient=recipient1,
        ),
    )

    await check_document_status(document.id, DocumentStatus.signed_and_sent)

    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'parallel_signing': True,
                'entities': [
                    {'id': coworker1.role_id, 'type': 'role'},
                    {'id': coworker2.role_id, 'type': 'role'},
                    {'id': recipient1.role_id, 'type': 'role'},
                ],
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == 400

    mailbox.clear()
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'signers_settings': {
                'parallel_signing': True,
                'entities': [
                    {'id': coworker1.role_id, 'type': 'role'},
                    {'id': recipient1.role_id, 'type': 'role'},
                    {'id': recipient2.role_id, 'type': 'role'},
                    {'id': recipient3.role_id, 'type': 'role'},
                ],
            }
        },
        headers=prepare_auth_headers(recipient1),
    )
    assert response.status == 200

    assert len(mailbox) == (3 if can_receive_inbox else 0)
    mailbox.clear()

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.expected_recipient_signatures == 3

    for recipient in [recipient1, recipient2, recipient3]:
        response = await client.post(
            f'/internal-api/documents/{document.id}/signatures',
            data=prepare_signature_form_data(recipient),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 201
    response = await client.post(
        f'/internal-api/documents/{document.id}/send',
        json={},
        headers=prepare_auth_headers(recipient3),
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.status_id == DocumentStatus.finished.value
        assert document.date_finished is not None


async def test_update_sign_process_for_multilateral_document(
    aiohttp_client,
    mailbox,
    test_flags,
):
    """
    Check basic update sign process for multilateral document by document owner.
    """

    app, client, owner = await prepare_client(aiohttp_client)
    coworker1 = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(
        app=app,
        owner=owner,
        is_multilateral=True,
    )

    flow_1 = await prepare_flow_item(
        app=app,
        document_id=document.id,
        company_id=owner.company_id,
        order=1,
        signatures_count=1,
        pending_signatures_count=1,
    )
    flow_2 = await prepare_flow_item(
        app=app,
        document_id=document.id,
        company_id=owner.company_id,
        order=2,
        signatures_count=1,
        pending_signatures_count=1,
    )

    response = await request_document_update(
        client=client,
        document=document,
        user=owner,
        signers=[coworker1, coworker2],
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.OK, response_json

    assert len(mailbox) == 2
    assert {m['To'] for m in mailbox} == {coworker1.email, coworker2.email}

    document_signers = await get_document_signers(document_id=document.id)
    assert {s.role_id for s in document_signers} == {
        coworker1.role_id,
        coworker2.role_id,
    }

    flow_1 = await get_flow(flow_id=flow_1.id_)
    assert flow_1.pending_signatures_count == 2

    flow_2 = await get_flow(flow_id=flow_2.id_)
    assert flow_2.pending_signatures_count == 1


async def test_multilateral_change_recipients_with_unfinished_flow(aiohttp_client):
    """
    Test case for DOC-7468
    Mistakenly sending a multilateral document on recipients update,
     when current flow.meta.unfinished = True
    """
    app, client, owner = await prepare_client(aiohttp_client, company_edrpou='77777777')
    user2 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_2, email='<EMAIL>')
    coworker_2 = await prepare_user_data(
        app, company_edrpou=COMPANY_EDRPOU_2, email='<EMAIL>'
    )
    user3 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_3, email='<EMAIL>')
    user4 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_4, email='<EMAIL>')
    document = await prepare_document_data(app, owner)

    update_recipients = {
        'is_ordered': True,
        'recipients': [
            {
                'edrpou': owner.company_edrpou,
                'emails': [owner.email],
                'is_email_hidden': False,
                'role': RecipientRole.viewer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_2,
                'emails': [user2.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_3,
                'emails': [user3.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
        ],
    }

    await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
        excepted_status=HTTPStatus.OK,
    )

    await send_document(client, document.id, sender=owner)

    async with services.db.acquire() as conn:
        flows = await select_flows_by(conn, documents_ids=[document.id])
        assert len(flows) == 3

    await request_document_update(
        client=client,
        user=user2,
        document=document,
        signers_settings={
            'parallel_signing': True,
            'entities': [
                {'id': user2.role_id, 'type': 'role'},
                {'id': coworker_2.role_id, 'type': 'role'},
            ],
        },
        excepted_status=HTTPStatus.OK,
    )

    await add_signature(client, document.id, user2)

    await request_document_update(
        client=client,
        user=user2,
        document=document,
        signers_settings={
            'parallel_signing': True,
            'entities': [{'id': user2.role_id, 'type': 'role'}],
        },
        excepted_status=HTTPStatus.OK,
    )

    await add_signature(client, document.id, user3, expected_http_status=HTTPStatus.FORBIDDEN)

    # Add new recipient (initiate send process)
    update_recipients['recipients'].append(
        {
            'edrpou': COMPANY_EDRPOU_4,
            'emails': [user4.email],
            'is_email_hidden': False,
            'role': RecipientRole.signer.value,
        }
    )
    await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
        excepted_status=HTTPStatus.OK,
    )

    # As expected document don't send to user3, no access
    await add_signature(client, document.id, user3, expected_http_status=HTTPStatus.FORBIDDEN)

    # Manually send document from company2, and add signature from company3
    await send_document(client, document.id, sender=user2)
    await add_signature(client, document.id, user3)


async def test_multilateral_change_recipients_with_unfinished_flow_on_parallel_sign(aiohttp_client):
    """
    Test case for DOC-7468
    Sending a multilateral document to new recipients,
    when update recipients and current flow.meta.unfinished = True and is_ordered=False
    """
    app, client, owner = await prepare_client(aiohttp_client, company_edrpou='77777777')
    user2 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_2, email='<EMAIL>')
    coworker_2 = await prepare_user_data(
        app, company_edrpou=COMPANY_EDRPOU_2, email='<EMAIL>'
    )
    user3 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_3, email='<EMAIL>')
    user4 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_4, email='<EMAIL>')
    document = await prepare_document_data(app, owner)

    update_recipients = {
        'is_ordered': False,
        'recipients': [
            {
                'edrpou': owner.company_edrpou,
                'emails': [owner.email],
                'is_email_hidden': False,
                'role': RecipientRole.viewer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_2,
                'emails': [user2.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_3,
                'emails': [user3.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
        ],
    }

    await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
        excepted_status=HTTPStatus.OK,
    )

    await send_document(client, document.id, sender=owner)

    async with services.db.acquire() as conn:
        flows = await select_flows_by(conn, documents_ids=[document.id])
        assert len(flows) == 3

    await request_document_update(
        client=client,
        user=user2,
        document=document,
        signers_settings={
            'parallel_signing': True,
            'entities': [
                {'id': user2.role_id, 'type': 'role'},
                {'id': coworker_2.role_id, 'type': 'role'},
            ],
        },
        excepted_status=HTTPStatus.OK,
    )

    await add_signature(client, document.id, user2)

    await request_document_update(
        client=client,
        user=user2,
        document=document,
        signers_settings={
            'parallel_signing': True,
            'entities': [{'id': user2.role_id, 'type': 'role'}],
        },
        excepted_status=HTTPStatus.OK,
    )

    # Add new recipient (initiate send process)
    update_recipients['recipients'].append(
        {
            'edrpou': COMPANY_EDRPOU_4,
            'emails': [user4.email],
            'is_email_hidden': False,
            'role': RecipientRole.signer.value,
        }
    )
    await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
        excepted_status=HTTPStatus.OK,
    )

    # Expected document send to company4, and user4 can sign
    # (ignoring unsinished flow company 2)
    await add_signature(client, document.id, user4)


async def test_multilateral_change_ordered_to_parallel_when_owner_signs_last(aiohttp_client):
    """
    Test case for DOC-7717
    Test changing a multilateral document from ordered to parallel signing
    after external recipients have already signed the document.

    1. External recipients are positioned first, an owner is set to sign last
    2. External recipients sign document
    3. Owner changes signing type from ordered to parallel
    """
    app, client, owner = await prepare_client(aiohttp_client, company_edrpou=COMPANY_EDRPOU_1)
    user2 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_2, email=TEST_EMAIL_2)
    user3 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_3, email=TEST_EMAIL_3)
    document = await prepare_document_data(app, owner)

    recipients_settings = {
        'is_ordered': True,
        'recipients': [
            {
                'edrpou': COMPANY_EDRPOU_2,
                'emails': [user2.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_3,
                'emails': [user3.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
            {
                'edrpou': owner.company_edrpou,
                'emails': [owner.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
        ],
    }

    await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=recipients_settings,
        excepted_status=HTTPStatus.OK,
    )
    await send_document(client, document.id, sender=owner)

    async with services.db.acquire() as conn:
        flows = await select_flows_by(conn, documents_ids=[document.id])
        assert len(flows) == 3

    # External recipients sign document
    await add_signature(client, document.id, user2)
    await add_signature(client, document.id, user3)

    async with services.db.acquire() as conn:
        flows = await select_flows_by(conn, documents_ids=[document.id])
        assert len(flows) == 3

    document = await get_document(document_id=document.id)
    assert document.status == DocumentStatus.flow

    # Change from ordered to parallel signing
    recipients_settings['is_ordered'] = False
    await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=recipients_settings,
        excepted_status=HTTPStatus.OK,
    )

    # Expect document status to remain flow
    document = await get_document(document_id=document.id)
    assert document.status == DocumentStatus.flow

    await add_signature(client, document.id, owner)

    # Document should be finished
    document = await get_document(document_id=document.id)
    assert document.status == DocumentStatus.finished


async def test_multilateral_change_status_to_finished_on_parallel_signing_unfinished_flow(
    aiohttp_client,
):
    """
    Multilateral document with parallel signing.
    Company №2 has unfinished flow (flow.meta.unfinished=True),
    while Company №1 and Company №3 have already signed the document.
    It is expected that after updating the internal signers and sending the document,
     its status will change to "Finished".
    DOC-7247
    """
    app, client, owner = await prepare_client(aiohttp_client, company_edrpou='77777777')
    user2 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_2, email='<EMAIL>')
    coworker_2 = await prepare_user_data(
        app, company_edrpou=COMPANY_EDRPOU_2, email='<EMAIL>'
    )
    user3 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_3, email='<EMAIL>')
    document = await prepare_document_data(app, owner)

    update_recipients = {
        'is_ordered': False,
        'recipients': [
            {
                'edrpou': owner.company_edrpou,
                'emails': [owner.email],
                'is_email_hidden': False,
                'role': RecipientRole.viewer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_2,
                'emails': [user2.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_3,
                'emails': [user3.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
        ],
    }

    await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
        excepted_status=HTTPStatus.OK,
    )

    await send_document(client, document.id, sender=owner)

    async with services.db.acquire() as conn:
        flows = await select_flows_by(conn, documents_ids=[document.id])
        assert len(flows) == 3

    await request_document_update(
        client=client,
        user=user2,
        document=document,
        signers_settings={
            'parallel_signing': True,
            'entities': [
                {'id': user2.role_id, 'type': 'role'},
                {'id': coworker_2.role_id, 'type': 'role'},
            ],
        },
        excepted_status=HTTPStatus.OK,
    )

    await add_signature(client, document.id, user3)
    await add_signature(client, document.id, user2)

    await request_document_update(
        client=client,
        user=user2,
        document=document,
        signers_settings={
            'parallel_signing': True,
            'entities': [{'id': user2.role_id, 'type': 'role'}],
        },
        excepted_status=HTTPStatus.OK,
    )

    async with services.db.acquire() as conn:
        document_ = await select_document(conn=conn, document_id=document.id)
        assert document_.status == DocumentStatus.flow

        await send_document(client, document.id, sender=user2)

        document_ = await select_document(conn=conn, document_id=document.id)
        assert document_.status == DocumentStatus.finished


async def test_bilateral_to_multilateral_change_status_to_finished_on_unfinished_flow(
    aiohttp_client,
):
    """
    Test case DOC-7247
    """
    app, client, owner = await prepare_client(aiohttp_client, company_edrpou='77777777')
    user2 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_2, email='<EMAIL>')
    user3 = await prepare_user_data(app, company_edrpou=COMPANY_EDRPOU_3, email='<EMAIL>')
    document = await prepare_document_data(app, owner)

    update_recipients = {
        'is_ordered': False,
        'recipients': [
            {
                'edrpou': owner.company_edrpou,
                'emails': [owner.email],
                'is_email_hidden': False,
                'role': RecipientRole.signer.value,
            },
            {
                'edrpou': COMPANY_EDRPOU_2,
                'emails': [user2.email],
                'is_email_hidden': False,
                'role': RecipientRole.viewer.value,
            },
        ],
    }

    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        flows = await select_flows_by(conn, documents_ids=[document.id])
        assert len(flows) == 0

    # Add third recipient. document type bilateral => multilateral.
    update_recipients['recipients'].append(
        {
            'edrpou': COMPANY_EDRPOU_3,
            'emails': [user3.email],
            'is_email_hidden': False,
            'role': 'signer',
        }
    )
    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_recipients,
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        flows = await select_flows_by(conn, documents_ids=[document.id])
        assert len(flows) == 3

        await add_signature(client, document.id, owner)
        await add_signature(client, document.id, user3)

        document_ = await select_document(conn=conn, document_id=document.id)
        assert document_.status == DocumentStatus.finished


async def test_delete_sign_process_for_multilateral_document(
    aiohttp_client,
    mailbox,
    test_flags,
):
    """
    Check that we can delete sign process for multilateral document by document owner.
    """

    app, client, owner = await prepare_client(aiohttp_client)
    coworker1 = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(
        app=app,
        owner=owner,
        is_multilateral=True,
    )
    recipient_1 = await prepare_document_recipient(
        document_id=document.id,
        emails=['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        edrpou=owner.company_edrpou,
        from_flow=True,
    )
    flow_1 = await prepare_flow_item(
        app=app,
        document_id=document.id,
        company_id=owner.company_id,
        order=1,
        receivers_id=recipient_1.id,
        signatures_count=1,
        pending_signatures_count=1,
    )

    flow_2 = await prepare_flow_item(
        app=app,
        document_id=document.id,
        company_id=owner.company_id,
        order=2,
        signatures_count=1,
        pending_signatures_count=1,
    )
    # Create signers for multilateral document
    # This function doesn't include async jobs
    async with services.db.acquire() as conn:
        await update_document_signers(
            conn=conn,
            document=Document.from_row(document),
            company_id=owner.company_id,
            company_edrpou=owner.company_edrpou,
            signers=[
                UpdateSignersDataSignerEntity(
                    type=UpdateSignersDataSignerType.role,
                    id=role_id,
                )
                for role_id in [coworker1.role_id, coworker2.role_id]
            ],
            parallel_signing=True,
            is_document_owner=True,
            current_role_id=owner.role_id,
            signers_source=None,
        )
    # Check that pending signatures count is correct
    flow_1 = await get_flow(flow_id=flow_1.id_)
    assert flow_1.pending_signatures_count == 2

    # Delete signers for multilateral document
    response = await request_document_update(
        client=client,
        document=document,
        user=owner,
        signers=[],  # To delete we are sending empty list
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.OK, response_json

    assert len(mailbox) == 0

    document_signers = await get_document_signers(document_id=document.id)
    assert len(document_signers) == 0

    # Restored from
    flow_1 = await get_flow(flow_id=flow_1.id_)
    assert flow_1.pending_signatures_count == 1

    flow_2 = await get_flow(flow_id=flow_2.id_)
    assert flow_2.pending_signatures_count == 1


@pytest.mark.parametrize('is_required_review', [True, False])
@pytest.mark.parametrize('can_receive_inbox, ', [True, False])
async def test_updated_signers_emails_on_uploading_with_review(
    aiohttp_client,
    can_receive_inbox,
    is_required_review,
    mailbox,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        can_receive_inbox=can_receive_inbox,
    )
    coworker1 = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_receive_inbox=can_receive_inbox,
    )
    document = await prepare_document_upload(
        app,
        client,
        user=user,
        reviewers_ids=[user.role_id, coworker1.role_id],
        signer_roles=[user.role_id],
        expected_owner_signatures=1,
        is_required_review=is_required_review,
    )

    # Clear all emails on uploading
    mailbox.clear()

    resp = await request_document_update(
        client,
        user=user,
        document=document,
        signers=[user, coworker1],
    )
    assert resp.status == HTTPStatus.OK
    assert len(mailbox) == 1 * can_receive_inbox * (not is_required_review)


async def test_update_signers_first_sign_by_recipient(aiohttp_client, mailbox):
    """
    Check that owner can update signers even before we send a document to recipient in case when
    first sign is expected from the recipient.
    """
    app, client, owner = await prepare_client(aiohttp_client)
    coworker1 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=owner.company_edrpou,
    )
    coworker2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=owner.company_edrpou,
    )
    recipient = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        first_sign_by=FirstSignBy.recipient,
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=1,
        expected_recipient_signatures=1,
        another_recipients=[recipient],
    )

    response = await request_document_update(
        client=client,
        document=document,
        user=owner,
        signers=[coworker1, coworker2],
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.OK, response_json

    # We don't send a document to signers, because it's not available yet for signing
    assert len(mailbox) == 0


@pytest.mark.parametrize(
    'before, request_data, expected',
    [
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    }
                ],
            },
            {
                'document': {
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            id='internal_from_uploaded_none',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'recipients': [],
                'listings': [],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    }
                ],
            },
            {
                'document': {
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    # this is the actually initial status of the internal document
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            id='internal_from_internal_basic',
        ),
        # From bilateral to internal -> remove everything related to recipient and set
        # proper document status
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.recipient,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'recipient_email': TEST_EMAIL_2,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    # Owner still has access to the document
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    }
                ],
            },
            {
                'document': {
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [],
                'listings': [
                    # Owner still has access to the document
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            id='internal_from_bilateral_basic',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.recipient,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'recipient_email': TEST_EMAIL_2,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    # Owner still has access to the document
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            {
                'is_internal': True,
            },
            {
                'document': {
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [],
                'listings': [
                    # Owner still has access to the document
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            id='internal_from_bilateral_simplified_param',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                },
                'recipients': [
                    {
                        'id': TEST_UUID_1,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'id': TEST_UUID_2,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                    {
                        'id': TEST_UUID_3,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                    },
                ],
                'flows': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': 1,
                        'receivers_id': TEST_UUID_1,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': 2,
                        'receivers_id': TEST_UUID_2,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'order': 3,
                        'receivers_id': TEST_UUID_3,
                    },
                ],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    }
                ],
            },
            {
                'document': {
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [],
                'listings': [
                    # Owner still has access to the document
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            id='internal_from_multilateral_basic',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': COMPANY_EDRPOU_2,
                    'email_recipient': TEST_EMAIL_2,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [],
            },
            id='bilateral_from_uploaded_none',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                },
                'recipients': [],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': COMPANY_EDRPOU_2,
                    'email_recipient': TEST_EMAIL_2,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_internal_basic',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': COMPANY_EDRPOU_3,
                    'email_recipient': TEST_EMAIL_3,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_bilateral_basic',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_bilateral_no_change',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.signed_and_sent.value,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'date_sent': utc_now(),
                    },
                ],
                'signatures': [
                    {
                        'user_role_id': ROLE_ID_1,
                        'user_id': USER_ID_1,
                        'user_email': TEST_EMAIL_1,
                        'key_owner_edrpou': COMPANY_EDRPOU_1,
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_2,
                        'role_id': ROLE_ID_2,
                        'sources': AccessSource.default,
                    },
                ],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': COMPANY_EDRPOU_3,
                    'email_recipient': TEST_EMAIL_3,
                    # keep status as is
                    'status_id': DocumentStatus.signed_and_sent.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                    # resend a document for the new recipient
                    {
                        'access_edrpou': COMPANY_EDRPOU_3,
                        'role_id': ROLE_ID_3,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_bilateral_already_sent',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'recipient_email': TEST_EMAIL_2,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'recipient',
                    'edrpou_recipient': COMPANY_EDRPOU_2,
                    'email_recipient': TEST_EMAIL_2,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_bilateral_first_sign_by_recipient',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'recipient_email': TEST_EMAIL_2,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': COMPANY_EDRPOU_2,
                    'email_recipient': TEST_EMAIL_2,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_bilateral_sign_only_by_owner',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                },
                'recipients': [],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_internal': False,
                'recipients': [],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'recipients': [],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_empty_from_internal',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'recipient_email': TEST_EMAIL_2,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 0,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'recipient',
                    'edrpou_recipient': COMPANY_EDRPOU_2,
                    'email_recipient': TEST_EMAIL_2,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_bilateral_sign_only_by_recipient',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.flow.value,
                },
                'recipients': [
                    {
                        'id': TEST_UUID_1,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'id': TEST_UUID_2,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                    {
                        'id': TEST_UUID_3,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': 1,
                        'receivers_id': TEST_UUID_1,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': 2,
                        'receivers_id': TEST_UUID_2,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'order': 3,
                        'receivers_id': TEST_UUID_3,
                    },
                ],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                },
                'recipients': [],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            id='bilateral_from_multilateral_basic',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    }
                ],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'meta': {},
                    },
                    {
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'meta': {},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'meta': {},
                    },
                ],
            },
            id='multilateral_from_uploaded_none',
        ),
        # multilateral from internal
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': True,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                },
                'recipients': [],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'meta': {},
                    },
                    {
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'meta': {},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'meta': {},
                    },
                ],
            },
            id='multilateral_from_internal_basic',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 2,
                    'first_sign_by': FirstSignBy.recipient,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'recipient_email': TEST_EMAIL_2,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'meta': {},
                    },
                    {
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'meta': {},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'meta': {},
                    },
                ],
            },
            id='multilateral_from_bilateral_basic',
        ),
        pytest.param(
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 2,
                    'first_sign_by': FirstSignBy.recipient,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'recipient_email': TEST_EMAIL_2,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'recipients': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [],
            },
            {
                'is_ordered': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.uploaded.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
                'listings': [
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [
                    {
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'meta': {},
                    },
                    {
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'meta': {},
                    },
                    {
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'meta': {},
                    },
                ],
            },
            id='multilateral_from_bilateral_parallel',
        ),
        pytest.param(
            # from parallel to ordered (lose access, order is not none)
            {
                'document': {
                    'id': TEST_UUID_1,
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'recipient_edrpou': None,
                    'recipient_email': None,
                },
                'recipients': [
                    {
                        'id': TEST_UUID_1,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                    },
                    {
                        'id': TEST_UUID_2,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                    },
                    {
                        'id': TEST_UUID_3,
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                    },
                ],
                'listings': [
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_2,
                        'role_id': ROLE_ID_2,
                        'sources': AccessSource.default,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'access_edrpou': COMPANY_EDRPOU_3,
                        'role_id': ROLE_ID_3,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': 1,
                        'receivers_id': TEST_UUID_1,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': 2,
                        'receivers_id': TEST_UUID_2,
                    },
                    {
                        'document_id': TEST_UUID_1,
                        'edrpou': COMPANY_EDRPOU_3,
                        'order': 3,
                        'receivers_id': TEST_UUID_3,
                    },
                ],
            },
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_internal': False,
                    'is_multilateral': True,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': 'owner',
                    'edrpou_recipient': None,
                    'email_recipient': None,
                    'status_id': DocumentStatus.flow.value,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_email': None,
                    },
                ],
                'listings': [
                    # owner
                    {
                        'access_edrpou': COMPANY_EDRPOU_1,
                        'role_id': ROLE_ID_1,
                        'sources': AccessSource.default,
                    },
                    # first ordered signer
                    {
                        'access_edrpou': COMPANY_EDRPOU_2,
                        'role_id': ROLE_ID_2,
                        'sources': AccessSource.default,
                    },
                    # is viewer
                    {
                        'access_edrpou': COMPANY_EDRPOU_3,
                        'role_id': ROLE_ID_3,
                        'sources': AccessSource.default,
                    },
                ],
                'flows': [
                    {
                        'order': 0,
                        'signatures_count': 0,
                        'pending_signatures_count': 0,
                        'edrpou': COMPANY_EDRPOU_3,
                        'meta': {'send_jobs_executed': True, 'send_notifications_executed': True},
                    },
                    {
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_2,
                        'meta': {'send_jobs_executed': True, 'send_notifications_executed': True},
                    },
                    {
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_4,
                        'meta': {},
                    },
                    {
                        'order': 3,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'edrpou': COMPANY_EDRPOU_1,
                        'meta': {},
                    },
                ],
            },
            id='multilateral_from_multilateral_change_order',
        ),
    ],
)
async def test_update_recipients_not_signed(
    aiohttp_client,
    before: DataDict,
    request_data: DataDict,
    expected: DataDict,
):
    app, client, owner = await prepare_client(
        aiohttp_client,
        id=USER_ID_1,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
    )
    # recipient 1
    await prepare_user_data(
        app=app,
        id=USER_ID_2,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )
    # recipient 2
    await prepare_user_data(
        app=app,
        id=USER_ID_3,
        email=TEST_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=COMPANY_EDRPOU_3,
    )
    # recipient 3
    await prepare_user_data(
        app=app,
        id=USER_ID_4,
        email=TEST_EMAIL_4,
        role_id=ROLE_ID_4,
        company_edrpou=COMPANY_EDRPOU_4,
    )

    document = await prepare_document_data(
        app,
        owner=owner,
        create_document_access_for_recipients=False,
        **before['document'],
    )
    await prepare_document_recipients(data=before.get('recipients'))
    await prepare_listings_accesses(data=before.get('listings'))

    # Set common values for flows
    for raw_flow in before.get('flows') or []:
        raw_flow.setdefault('pending_signatures_count', 1)
        raw_flow.setdefault('signatures_count', 1)

    await prepare_flows_rows(data=before.get('flows'))

    for signature_data in before.get('signatures') or []:
        await prepare_signature_row(
            document_id=document.id,
            **signature_data,
        )

    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=request_data,
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        document = await get_document(document_id=document.id)
        recipients = await get_document_recipients(document_id=document.id)
        flows = await get_flows(document_id=document.id)
        listing = await select_listings(conn, document_ids=[document.id])

    document_expected = expected['document']
    document_actual = {
        'is_internal': document.is_internal,
        'is_multilateral': document.is_multilateral,
        'expected_owner_signatures': document.expected_owner_signatures,
        'expected_recipient_signatures': document.expected_recipient_signatures,
        'first_sign_by': document.first_sign_by.value,
        'edrpou_recipient': document.edrpou_recipient,
        'email_recipient': document.email_recipient,
        'status_id': document.status_id,
    }
    assert document_actual == document_expected

    assert len(flows) == len(expected['flows'])
    flows_actual = [
        {
            'order': f.order,
            'signatures_count': f.signatures_count,
            'pending_signatures_count': f.pending_signatures_count,
            'edrpou': f.edrpou,
            'meta': f.meta.to_db(),
        }
        for f in flows
    ]
    flows_actual = sorted(
        flows_actual,
        key=lambda x: (
            x['order'],
            x['edrpou'],
            x['meta'],
            x['signatures_count'],
            x['pending_signatures_count'],
        ),
    )
    flows_expected = sorted(
        expected['flows'],
        key=lambda x: (
            x['order'],
            x['edrpou'],
            x['meta'],
            x['signatures_count'],
            x['pending_signatures_count'],
        ),
    )
    assert flows_actual == flows_expected

    assert len(listing) == len(expected['listings'])
    listing_actual = [
        {
            'access_edrpou': item.access_edrpou,
            'role_id': item.role_id,
            'sources': AccessSource.default,
        }
        for item in listing
    ]
    listing_actual = sorted(listing_actual, key=lambda x: (x['access_edrpou'], x['role_id'] or ''))
    listing_expected = sorted(
        expected['listings'],
        key=lambda x: (x['access_edrpou'], x['role_id'] or ''),
    )
    assert listing_actual == listing_expected

    # One record in recipient table (for recipient)
    assert len(recipients) == len(expected['recipients'])
    recipients_actual = [
        {
            'edrpou': r.edrpou,
            'emails': r.emails,
            'is_email_hidden': r.is_emails_hidden,
            'from_flow': r.from_flow,
            'external_email': r.external_meta,
        }
        for r in recipients
    ]
    recipients_actual = sorted(recipients_actual, key=lambda x: (x['edrpou'], x['emails']))
    recipients_expected = sorted(expected['recipients'], key=lambda x: (x['edrpou'], x['emails']))
    assert recipients_actual == recipients_expected


@pytest.mark.parametrize(
    'prepare_flows, update_data, expected',
    [
        pytest.param(
            [
                CreateFlowCtx.from_params(
                    order=0,
                    sign_num=1,
                    emails=[TEST_EMAIL_1],
                    edrpou=COMPANY_EDRPOU_1,
                ),
                CreateFlowCtx.from_params(
                    order=1,
                    sign_num=1,
                    emails=[TEST_EMAIL_2],
                    edrpou=COMPANY_EDRPOU_2,
                ),
                CreateFlowCtx.from_params(
                    order=2,
                    sign_num=1,
                    emails=[TEST_EMAIL_3],
                    edrpou=COMPANY_EDRPOU_3,
                ),
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_multilateral': True,
                    'is_internal': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.flow,
                },
                'flows': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 0,
                        'meta': FlowMeta(
                            role_ids=['*************-0000-0000-000000000001'],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=False,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=False,
                            send_notifications_executed=False,
                        ),
                        'date_sent': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'order': 3,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=False,
                            send_notifications_executed=False,
                        ),
                        'date_sent': None,
                    },
                ],
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': None,
                    },
                ],
                'mailbox_emails': [],
            },
            id='ordered_add_new_flow',
        ),
        pytest.param(
            [
                CreateFlowCtx.from_params(
                    order=0,
                    sign_num=1,
                    emails=[TEST_EMAIL_1],
                    edrpou=COMPANY_EDRPOU_1,
                ),
                CreateFlowCtx.from_params(
                    order=1,
                    sign_num=1,
                    emails=[TEST_EMAIL_2],
                    edrpou=COMPANY_EDRPOU_2,
                ),
                CreateFlowCtx.from_params(
                    order=2,
                    sign_num=1,
                    emails=[TEST_EMAIL_3],
                    edrpou=COMPANY_EDRPOU_3,
                ),
                CreateFlowCtx.from_params(
                    order=4,
                    sign_num=1,
                    emails=[TEST_EMAIL_4],
                    edrpou=COMPANY_EDRPOU_4,
                ),
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_multilateral': True,
                    'is_internal': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.flow,
                },
                'flows': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 0,
                        'meta': FlowMeta(
                            role_ids=['*************-0000-0000-000000000001'],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=False,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=False,
                            send_notifications_executed=False,
                        ),
                        'date_sent': None,
                    },
                ],
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': None,
                    },
                ],
                'mailbox_emails': [],
            },
            id='ordered_remove_flow',
        ),
        pytest.param(
            [
                CreateFlowCtx.from_params(
                    order=0,
                    sign_num=1,
                    emails=[TEST_EMAIL_1],
                    edrpou=COMPANY_EDRPOU_1,
                ),
                CreateFlowCtx.from_params(
                    order=1,
                    sign_num=1,
                    emails=[TEST_EMAIL_2],
                    edrpou=COMPANY_EDRPOU_2,
                ),
                CreateFlowCtx.from_params(
                    order=2,
                    sign_num=1,
                    emails=[TEST_EMAIL_3],
                    edrpou=COMPANY_EDRPOU_3,
                ),
                CreateFlowCtx.from_params(
                    order=3,
                    sign_num=1,
                    emails=[TEST_EMAIL_4],
                    edrpou=COMPANY_EDRPOU_4,
                ),
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_multilateral': True,
                    'is_internal': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.flow,
                },
                'flows': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 0,
                        'meta': FlowMeta(
                            role_ids=['*************-0000-0000-000000000001'],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=False,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=False,
                            send_notifications_executed=False,
                        ),
                        'date_sent': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': 3,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                ],
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                ],
                'mailbox_emails': [TEST_EMAIL_4],
            },
            id='ordered_reorder',
        ),
        pytest.param(
            [
                CreateFlowCtx.from_params(
                    order=0,
                    sign_num=1,
                    emails=[TEST_EMAIL_1],
                    edrpou=COMPANY_EDRPOU_1,
                ),
                CreateFlowCtx.from_params(
                    order=1,
                    sign_num=1,
                    emails=[TEST_EMAIL_2],
                    edrpou=COMPANY_EDRPOU_2,
                ),
                CreateFlowCtx.from_params(
                    order=2,
                    sign_num=1,
                    emails=[TEST_EMAIL_3],
                    edrpou=COMPANY_EDRPOU_3,
                ),
                CreateFlowCtx.from_params(
                    order=3,
                    sign_num=1,
                    emails=[TEST_EMAIL_4],
                    edrpou=COMPANY_EDRPOU_4,
                ),
            ],
            {
                'is_ordered': False,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_multilateral': True,
                    'is_internal': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.flow,
                },
                'flows': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 0,
                        'meta': FlowMeta(
                            role_ids=['*************-0000-0000-000000000001'],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=False,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'order': None,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                ],
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_4,
                        'emails': [TEST_EMAIL_4],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                ],
                'mailbox_emails': [TEST_EMAIL_3, TEST_EMAIL_4],
            },
            id='from_ordered_to_parallel',
        ),
        pytest.param(
            [
                CreateFlowCtx.from_params(
                    order=None,
                    sign_num=1,
                    emails=[TEST_EMAIL_1],
                    edrpou=COMPANY_EDRPOU_1,
                ),
                CreateFlowCtx.from_params(
                    order=None,
                    sign_num=1,
                    emails=[TEST_EMAIL_2],
                    edrpou=COMPANY_EDRPOU_2,
                ),
                CreateFlowCtx.from_params(
                    order=None,
                    sign_num=1,
                    emails=[TEST_EMAIL_3],
                    edrpou=COMPANY_EDRPOU_3,
                ),
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_multilateral': True,
                    'is_internal': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.flow,
                },
                'flows': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'order': 0,
                        'signatures_count': 1,
                        'pending_signatures_count': 0,
                        'meta': FlowMeta(
                            role_ids=['*************-0000-0000-000000000001'],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=False,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'order': 1,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'order': 2,
                        'signatures_count': 1,
                        'pending_signatures_count': 1,
                        'meta': FlowMeta(
                            role_ids=[],
                            unfinished=False,
                            send_jobs_executed=True,
                            send_notifications_executed=True,
                        ),
                        'date_sent': TODAY,
                    },
                ],
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                ],
                'mailbox_emails': [],
            },
            id='from_parallel_to_ordered',
        ),
        pytest.param(
            [
                CreateFlowCtx.from_params(
                    order=0,
                    sign_num=1,
                    emails=[TEST_EMAIL_1],
                    edrpou=COMPANY_EDRPOU_1,
                ),
                CreateFlowCtx.from_params(
                    order=1,
                    sign_num=1,
                    emails=[TEST_EMAIL_2],
                    edrpou=COMPANY_EDRPOU_2,
                ),
                CreateFlowCtx.from_params(
                    order=2,
                    sign_num=1,
                    emails=[TEST_EMAIL_3],
                    edrpou=COMPANY_EDRPOU_3,
                ),
            ],
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'is_multilateral': False,
                    'is_internal': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.signed_and_sent,
                },
                'flows': [],
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                ],
                'mailbox_emails': [],
            },
            id='to_bilateral',
        ),
    ],
)
async def test_update_recipients_signing_in_progress_multilateral(
    aiohttp_client,
    mailbox,
    prepare_flows: list[CreateFlowCtx],
    update_data: dict,
    expected: dict,
):
    app, client, owner = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
    )
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=COMPANY_EDRPOU_3,
    )
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_4,
        role_id=ROLE_ID_4,
        company_edrpou=COMPANY_EDRPOU_4,
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        is_multilateral=True,
        id=DOCUMENT_ID_1,
    )
    _options = AddFlowOptions(
        should_send=True,
        should_update_document_status=True,
        should_count_signers=True,
        documents=[Document.from_row(document)],
        receivers=prepare_flows,
        assigner_role_id=ROLE_ID_1,
    )
    async with services.db.acquire() as conn:
        await create_flows(conn, _options)

    await sign_and_send_document(client=client, document_id=document.id, signer=owner)

    mailbox.clear()

    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings=update_data,
    )
    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.is_multilateral == expected['document']['is_multilateral']
    assert document.is_internal == expected['document']['is_internal']
    assert document.expected_owner_signatures == expected['document']['expected_owner_signatures']
    assert (
        document.expected_recipient_signatures
        == expected['document']['expected_recipient_signatures']
    )
    assert document.first_sign_by == expected['document']['first_sign_by']
    assert document.status == expected['document']['status_id']

    flows = await get_flows(document_id=document.id)

    flows = sorted(flows, key=lambda f: (f.edrpou, f.order))
    flows_actual = [
        {
            'edrpou': flow.edrpou,
            'order': flow.order,
            'signatures_count': flow.signatures_count,
            'pending_signatures_count': flow.pending_signatures_count,
            'meta': flow.meta,
            'date_sent': flow.date_sent and flow.date_sent.date(),
        }
        for flow in flows
    ]
    flows_expected = expected['flows']
    flows_expected = sorted(flows_expected, key=lambda f: (f['edrpou'], f['order']))
    assert flows_actual == flows_expected

    recipients = await get_document_recipients(document_id=document.id)
    recipients_actual = [
        {
            'edrpou': recipient.edrpou,
            'emails': recipient.emails,
            'is_email_hidden': recipient.is_emails_hidden,
            'from_flow': recipient.from_flow,
            'external_meta': recipient.external_meta,
            'date_sent': recipient.date_sent and recipient.date_sent.date(),
        }
        for recipient in recipients
    ]
    recipients_actual = sorted(recipients_actual, key=lambda r: (r['edrpou'], r['emails']))
    recipients_expected = expected['recipients']
    recipients_expected = sorted(recipients_expected, key=lambda r: (r['edrpou'], r['emails']))
    assert recipients_actual == recipients_expected

    assert len(mailbox) == len(expected['mailbox_emails'])
    assert {msg['to'] for msg in mailbox} == set(expected['mailbox_emails'])


async def test_update_recipients_signing_in_progress_from_bilateral_to_multilateral(
    aiohttp_client,
    mailbox,
):
    """
    Start signing as bilateral from the owner side, then update a document to multilateral.
    Expected that owner's signature won't be removed and the signing will continue as multilateral.
    """
    app, client, owner = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
        create_billing_account=True,
    )
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=COMPANY_EDRPOU_3,
    )
    await prepare_user_data(
        app=app,
        email=TEST_EMAIL_4,
        role_id=ROLE_ID_4,
        company_edrpou=COMPANY_EDRPOU_4,
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        id=DOCUMENT_ID_1,
        recipient_email=TEST_EMAIL_2,
        recipient_edrpou=COMPANY_EDRPOU_2,
    )

    await sign_and_send_document(client=client, document_id=document.id, signer=owner)

    mailbox.clear()

    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings={
            'is_ordered': True,
            'recipients': [
                {
                    'edrpou': COMPANY_EDRPOU_1,
                    'emails': [TEST_EMAIL_1],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': COMPANY_EDRPOU_2,
                    'emails': [TEST_EMAIL_2],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': COMPANY_EDRPOU_3,
                    'emails': [TEST_EMAIL_3],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
            ],
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.is_multilateral is True
    assert document.is_internal is False
    assert document.expected_owner_signatures == 1
    assert document.expected_recipient_signatures == 1
    assert document.first_sign_by == FirstSignBy.owner
    assert document.status == DocumentStatus.flow

    flows = await get_flows(document_id=document.id)

    flows = sorted(flows, key=lambda f: (f.edrpou, f.order))
    flows_actual = [
        {
            'edrpou': flow.edrpou,
            'order': flow.order,
            'signatures_count': flow.signatures_count,
            'pending_signatures_count': flow.pending_signatures_count,
            'meta': flow.meta,
            'date_sent': flow.date_sent and flow.date_sent.date(),
        }
        for flow in flows
    ]
    flows_expected = [
        {
            'edrpou': COMPANY_EDRPOU_1,
            'order': 0,
            'signatures_count': 1,
            'pending_signatures_count': 0,
            'date_sent': None,
            'meta': FlowMeta(
                role_ids=[ROLE_ID_1],
                unfinished=False,
                send_jobs_executed=False,
                send_notifications_executed=False,
            ),
        },
        {
            'edrpou': COMPANY_EDRPOU_2,
            'order': 1,
            'signatures_count': 1,
            'pending_signatures_count': 1,
            'date_sent': TODAY,
            'meta': FlowMeta(
                role_ids=[],
                unfinished=False,
                send_jobs_executed=True,
                send_notifications_executed=True,
            ),
        },
        {
            'edrpou': COMPANY_EDRPOU_3,
            'order': 2,
            'signatures_count': 1,
            'pending_signatures_count': 1,
            'date_sent': None,
            'meta': FlowMeta(
                role_ids=[],
                unfinished=False,
                send_jobs_executed=False,
                send_notifications_executed=False,
            ),
        },
    ]
    flows_expected = sorted(flows_expected, key=lambda f: (f['edrpou'], f['order']))
    assert flows_actual == flows_expected

    recipients = await get_document_recipients(document_id=document.id)
    recipients_actual = [
        {
            'edrpou': recipient.edrpou,
            'emails': recipient.emails,
            'is_email_hidden': recipient.is_emails_hidden,
            'from_flow': recipient.from_flow,
            'external_meta': recipient.external_meta,
            'date_sent': recipient.date_sent and recipient.date_sent.date(),
        }
        for recipient in recipients
    ]
    recipients_actual = sorted(recipients_actual, key=lambda r: (r['edrpou'], r['emails']))
    recipients_expected = [
        {
            'edrpou': COMPANY_EDRPOU_1,
            'emails': [TEST_EMAIL_1],
            'is_email_hidden': False,
            'from_flow': True,
            'external_meta': None,
            'date_sent': None,
        },
        {
            'edrpou': COMPANY_EDRPOU_2,
            'emails': [TEST_EMAIL_2],
            'is_email_hidden': False,
            'from_flow': True,
            'external_meta': None,
            'date_sent': TODAY,
        },
        {
            'edrpou': COMPANY_EDRPOU_3,
            'emails': [TEST_EMAIL_3],
            'is_email_hidden': False,
            'from_flow': True,
            'external_meta': None,
            'date_sent': None,
        },
    ]
    recipients_expected = sorted(recipients_expected, key=lambda r: (r['edrpou'], r['emails']))
    assert recipients_actual == recipients_expected

    # zero because we already sent the document to the recipient before udpate
    assert len(mailbox) == 0


@pytest.mark.parametrize(
    'prepare, signer_email, update_data, expected',
    [
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_1,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Неможливо змінити порядок підписання для контрагентів, '
                        'що вже підписали документ'
                    ),
                },
            },
            id='change_order_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                }
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Неможливо змінити порядок підписання для контрагентів, '
                        'що вже підписали документ'
                    ),
                },
            },
            id='change_order_recipient_first_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                }
            },
            TEST_EMAIL_1,
            {
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                ],
            },
            {
                'document': {
                    'status': DocumentStatus.finished,
                    'is_multilateral': False,
                    'is_internal': False,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_meta': None,
                        'date_sent': None,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                ],
                'mailbox_emails': [],
            },
            id='make_recipient_as_viewer',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'expected_owner_signatures': 2,  # to prevent it to become finished after sign
                    'expected_recipient_signatures': 0,
                }
            },
            TEST_EMAIL_1,
            {
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'status': DocumentStatus.signed,  # user should many send document after update
                    'is_multilateral': False,
                    'is_internal': False,
                    'expected_recipient_signatures': 1,
                    'expected_owner_signatures': 1,
                },
                'recipients': [
                    # Bilateral document, with first sign by owner can be without
                    # "owner" in recipients: { 'edrpou': COMPANY_EDRPOU_1, ... }
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_meta': None,
                        'date_sent': None,
                    },
                ],
                'mailbox_emails': [],
            },
            id='make_recipient_as_signer',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                }
            },
            TEST_EMAIL_1,
            {
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Наразі не можливо змінити зовнішній документ на внутрішній після '
                        'того як розпочато підписання'
                    ),
                },
            },
            id='to_internal_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                }
            },
            TEST_EMAIL_1,
            {
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'status': DocumentStatus.flow,
                    'is_multilateral': True,
                    'is_internal': False,
                    'expected_recipient_signatures': 1,
                    'expected_owner_signatures': 1,
                },
                'recipients': [
                    {
                        'edrpou': '10000001',
                        'emails': ['<EMAIL>'],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': None,
                    },
                    {
                        'edrpou': '10000002',
                        'emails': ['<EMAIL>'],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,  # already sent
                    },
                    {
                        'edrpou': '10000003',
                        'emails': ['<EMAIL>'],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': None,
                    },
                ],
                'mailbox_emails': [],
            },
            id='bilateral_to_multilateral',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.owner,
                    'status_id': DocumentStatus.ready_to_be_signed.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 0,
                }
            },
            TEST_EMAIL_1,
            {
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_document_status',
                    'reason': 'Невалідний статус документу',
                },
            },
            id='change_finished_not_allowed',
        ),
    ],
)
async def test_update_recipients_signing_in_progress_bilateral_owner_is_first(
    aiohttp_client,
    mailbox,
    prepare: dict,
    signer_email: str,
    update_data: dict,
    expected: dict,
):
    """
    Check how is allowed or not allowed to change recipient settings for the bilateral document
    when signing is already started
    """
    app, client, user1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
        create_billing_account=True,
        enable_pro_functionality=True,
    )
    user2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )
    user3 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=COMPANY_EDRPOU_3,
    )
    users_map = {
        TEST_EMAIL_1: user1,
        TEST_EMAIL_2: user2,
        TEST_EMAIL_3: user3,
    }

    document = await prepare_document_data(
        app=app,
        owner=user1,
        id=DOCUMENT_ID_1,
        **prepare['document'],
    )
    await sign_and_send_document(
        client=client,
        document_id=document.id,
        signer=users_map[signer_email],
    )

    mailbox.clear()

    response = await request_document_update(
        client=client,
        user=user1,
        document=document,
        recipients_settings=update_data,
    )
    if expected.get('error'):
        assert response.status == expected['error']['http_status']
        response_json = await response.json()
        assert response_json['code'] == expected['error']['code']
        assert response_json['reason'] == expected['error']['reason']
        return

    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.status == expected['document']['status']
    assert document.is_multilateral == expected['document']['is_multilateral']
    assert document.is_internal == expected['document']['is_internal']
    assert (
        document.expected_recipient_signatures
        == expected['document']['expected_recipient_signatures']
    )
    assert document.expected_owner_signatures == expected['document']['expected_owner_signatures']

    recipients = await get_document_recipients(document_id=document.id)
    recipients_actual = [
        {
            'edrpou': recipient.edrpou,
            'emails': recipient.emails,
            'is_email_hidden': recipient.is_emails_hidden,
            'from_flow': recipient.from_flow,
            'external_meta': recipient.external_meta,
            'date_sent': recipient.date_sent and recipient.date_sent.date(),
        }
        for recipient in recipients
    ]
    recipients_actual = sorted(recipients_actual, key=lambda r: (r['edrpou'], r['emails']))
    recipients_expected = expected['recipients']
    recipients_expected = sorted(recipients_expected, key=lambda r: (r['edrpou'], r['emails']))
    assert recipients_actual == recipients_expected

    assert len(mailbox) == len(expected['mailbox_emails'])
    assert {msg['to'] for msg in mailbox} == set(expected['mailbox_emails'])


@pytest.mark.parametrize(
    'prepare, signer_email, update_data, expected',
    [
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Неможливо змінити порядок підписання для контрагентів, '
                        'що вже підписали документ'
                    ),
                },
            },
            id='change_order_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,  # not owner
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Компанія відправник має бути однією з компаній-отримувачів документа'
                    ),
                },
            },
            id='change_owner_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_3,  # +1 recipient
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'document': {
                    'status': DocumentStatus.flow,
                    'is_multilateral': True,
                    'is_internal': False,
                    'expected_recipient_signatures': 1,
                    'expected_owner_signatures': 1,
                },
                'recipients': [
                    {
                        'edrpou': '10000001',
                        'emails': ['<EMAIL>'],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': '10000002',
                        'emails': ['<EMAIL>'],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': TODAY,  # already sent
                    },
                    {
                        'edrpou': '10000003',
                        'emails': ['<EMAIL>'],
                        'is_email_hidden': False,
                        'from_flow': True,
                        'external_meta': None,
                        'date_sent': None,
                    },
                ],
                'mailbox_emails': [],
            },
            id='bilateral_to_multilateral',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Наразі не можливо змінити зовнішній документ на внутрішній '
                        'після того як розпочато підписання'
                    ),
                },
            },
            id='to_internal_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_3,  # new recipient
                        'emails': [TEST_EMAIL_3],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Неможливо видалити контрагенів, які вже підписали документ. ЄДРПОУ '
                        'компаній, яких немає в оновленому списку контрагентів: 10000002'
                    ),
                },
            },
            id='change_recipient_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                ],
            },
            {
                'error': {
                    'http_status': HTTPStatus.BAD_REQUEST,
                    'code': 'invalid_request',
                    'reason': (
                        'Неможливо змінити налаштування чи компанія підписувати чи ні '
                        'для компаній, що вже підписали документ. ЄДРПОУ компаній, '
                        'що вже підписали: 10000002'
                    ),
                },
            },
            id='make_recipient_as_viewer_not_allowed',
        ),
        pytest.param(
            {
                'document': {
                    'recipient_email': TEST_EMAIL_2,
                    'recipient_edrpou': COMPANY_EDRPOU_2,
                    'first_sign_by': FirstSignBy.recipient,
                    'status_id': DocumentStatus.sent.value,
                    'expected_owner_signatures': 1,
                    'expected_recipient_signatures': 1,
                },
            },
            TEST_EMAIL_2,
            {
                'is_ordered': True,
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'role': 'signer',
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_1,  # owner
                        'emails': [TEST_EMAIL_1],
                        'is_email_hidden': False,
                        'role': 'viewer',
                    },
                ],
            },
            {
                'document': {
                    'status': DocumentStatus.finished,
                    'is_multilateral': False,
                    'is_internal': False,
                    'expected_recipient_signatures': 1,
                    'expected_owner_signatures': 0,
                },
                'recipients': [
                    {
                        'edrpou': COMPANY_EDRPOU_1,
                        'emails': [],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                    {
                        'edrpou': COMPANY_EDRPOU_2,
                        'emails': [TEST_EMAIL_2],
                        'is_email_hidden': False,
                        'from_flow': False,
                        'external_meta': None,
                        'date_sent': TODAY,
                    },
                ],
                'mailbox_emails': [],
            },
            id='make_owner_as_viewer',
        ),
    ],
)
async def test_update_recipients_signing_in_progress_bilateral_recipient_is_first(
    aiohttp_client,
    mailbox,
    prepare: dict,
    signer_email: str,
    update_data: dict,
    expected: dict,
):
    """
    Check how is allowed or not allowed to change recipient settings for the bilateral document
    when signing is already started by recipient
    """
    app, client, user1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
        create_billing_account=True,
        enable_pro_functionality=True,
    )
    user2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )
    user3 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_3,
        role_id=ROLE_ID_3,
        company_edrpou=COMPANY_EDRPOU_3,
    )
    users_map = {
        TEST_EMAIL_1: user1,
        TEST_EMAIL_2: user2,
        TEST_EMAIL_3: user3,
    }

    document = await prepare_document_data(
        app=app,
        owner=user1,
        id=DOCUMENT_ID_1,
        **prepare['document'],
    )
    # the owner is sending a document to the recipient
    await send_document(
        client=client,
        document_id=document.id,
        headers=prepare_auth_headers(user1),
    )

    # sign by recipient
    await sign_and_send_document(
        client=client,
        document_id=document.id,
        signer=users_map[signer_email],
    )

    mailbox.clear()

    response = await request_document_update(
        client=client,
        user=user1,
        document=document,
        recipients_settings=update_data,
    )
    if expected.get('error'):
        assert response.status == expected['error']['http_status']
        response_json = await response.json()
        assert response_json['code'] == expected['error']['code']
        assert response_json['reason'] == expected['error']['reason']
        return

    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.status == expected['document']['status']
    assert document.is_multilateral == expected['document']['is_multilateral']
    assert document.is_internal == expected['document']['is_internal']
    assert (
        document.expected_recipient_signatures
        == expected['document']['expected_recipient_signatures']
    )
    assert document.expected_owner_signatures == expected['document']['expected_owner_signatures']

    recipients = await get_document_recipients(document_id=document.id)
    recipients_actual = [
        {
            'edrpou': recipient.edrpou,
            'emails': recipient.emails,
            'is_email_hidden': recipient.is_emails_hidden,
            'from_flow': recipient.from_flow,
            'external_meta': recipient.external_meta,
            'date_sent': recipient.date_sent and recipient.date_sent.date(),
        }
        for recipient in recipients
    ]
    recipients_actual = sorted(recipients_actual, key=lambda r: (r['edrpou'], r['emails']))
    recipients_expected = expected['recipients']
    recipients_expected = sorted(recipients_expected, key=lambda r: (r['edrpou'], r['emails']))
    assert recipients_actual == recipients_expected

    assert len(mailbox) == len(expected['mailbox_emails'])
    assert {msg['to'] for msg in mailbox} == set(expected['mailbox_emails'])


async def test_update_recipients_order_recipient_is_first_sent(aiohttp_client, mailbox):
    """
    Check how a system behaves when we send a document to the first recipient, and then we
    decided to change the order of signing and make the owner the first signer.

    Expected that recipient will completely lose access to the document and will receive it only
    after the owner signs and sends it to the recipient
    """
    app, client, user1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
        create_billing_account=True,
        enable_pro_functionality=True,
    )

    document = await prepare_document_data(
        app=app,
        owner=user1,
        id=DOCUMENT_ID_1,
        first_sign_by=FirstSignBy.recipient,
        status_id=DocumentStatus.uploaded.value,
        recipient_email=TEST_EMAIL_2,
        recipient_edrpou=COMPANY_EDRPOU_2,
        expected_owner_signatures=1,
        expected_recipient_signatures=1,
    )

    # the owner is sending a document to the recipient
    await send_document(
        client=client,
        document_id=document.id,
        headers=prepare_auth_headers(user1),
    )
    mailbox.clear()

    # check that recipient and owner have access to the document
    async with services.db.acquire() as conn:
        listings = await select_listings(conn, document_ids=[document.id])
        assert {item.access_edrpou for item in listings} == {COMPANY_EDRPOU_1, COMPANY_EDRPOU_2}

        recipients = await get_document_recipients(document_id=document.id)
        assert len(recipients) == 2  # for first sign by recipient we have 2 recipients
        mapping = {item.edrpou: item for item in recipients}
        assert mapping[COMPANY_EDRPOU_1].is_sent is False  # because it's an owner
        assert mapping[COMPANY_EDRPOU_2].is_sent is True

    response = await request_document_update(
        client=client,
        user=user1,
        document=document,
        recipients_settings={
            'is_ordered': True,
            'recipients': [
                {
                    'edrpou': COMPANY_EDRPOU_1,  # this is an owner
                    'emails': [TEST_EMAIL_1],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': COMPANY_EDRPOU_2,
                    'emails': [TEST_EMAIL_2],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
            ],
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    # check that recipient lost access to the document
    async with services.db.acquire() as conn:
        listings = await select_listings(conn, document_ids=[document.id])
        assert {item.access_edrpou for item in listings} == {COMPANY_EDRPOU_1}

        recipients = await get_document_recipients(document_id=document.id)
        assert len(recipients) == 2
        mapping = {item.edrpou: item for item in recipients}
        assert mapping[COMPANY_EDRPOU_1].is_sent is False  # because it's an owner
        assert mapping[COMPANY_EDRPOU_2].is_sent is False  # document was unsent

    # sign by owner
    await sign_and_send_document(
        client=client,
        document_id=document.id,
        signer=user1,
    )

    assert len(mailbox) == 1
    assert mailbox[0]['to'] == TEST_EMAIL_2
    assert mailbox[0]['Subject'] == ('Компанія "Test Company" надіслала вам документи на підпис')

    # check that recipient now, after sending, has access to the document
    async with services.db.acquire() as conn:
        listings = await select_listings(conn, document_ids=[document.id])
        assert {item.access_edrpou for item in listings} == {COMPANY_EDRPOU_1, COMPANY_EDRPOU_2}

        recipients = await get_document_recipients(document_id=document.id)
        assert len(recipients) == 2
        mapping = {item.edrpou: item for item in recipients}
        assert mapping[COMPANY_EDRPOU_1].is_sent is False  # because it's an owner
        assert mapping[COMPANY_EDRPOU_2].is_sent is True  # received again


async def test_update_recipients_from_bilateral_to_internal_with_signers(aiohttp_client):
    """
    Check if expected owner signatures are correctly calculated when we change a bilateral document
    to an internal and when we have document signers.
    """
    app, client, user1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
        create_billing_account=True,
        enable_pro_functionality=True,
    )
    user2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_1,
    )

    document = await prepare_document_data(
        app=app,
        owner=user1,
        id=DOCUMENT_ID_1,
        first_sign_by=FirstSignBy.owner,
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=2,
        expected_recipient_signatures=1,
        recipient_edrpou=COMPANY_EDRPOU_3,
        recipient_email=TEST_EMAIL_3,
    )

    await prepare_document_signer(
        document_id=document.id,
        company_id=user1.company_id,
        role_id=user1.role_id,
        group_id=None,
    )
    await prepare_document_signer(
        document_id=document.id,
        company_id=user2.company_id,
        role_id=user2.role_id,
        group_id=None,
    )

    response = await request_document_update(
        client=client,
        user=user1,
        document=document,
        recipients_settings={'is_internal': True},
    )
    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.is_internal is True
    assert document.is_multilateral is False
    assert document.expected_owner_signatures == 2
    assert document.expected_recipient_signatures == 0
    assert document.status == DocumentStatus.ready_to_be_signed
    assert document.edrpou_recipient is None
    assert document.email_recipient is None

    recipients = await get_document_recipients(document_id=document.id)
    assert len(recipients) == 0


async def test_update_recipients_multilateral_with_signers(aiohttp_client):
    """
    Check that during update of recipients from bilateral to multilateral and then to multilateral,
    we set the correct number of expected signatures when we have document signers.
    """
    app, client, user1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
        create_billing_account=True,
        enable_pro_functionality=True,
    )
    user2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_1,
    )

    # bilateral document
    document = await prepare_document_data(
        app=app,
        owner=user1,
        id=DOCUMENT_ID_1,
        first_sign_by=FirstSignBy.owner,
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=1,
        expected_recipient_signatures=1,
        recipient_edrpou=COMPANY_EDRPOU_3,
        recipient_email=TEST_EMAIL_3,
    )

    # Set signers
    response = await request_document_update(
        client=client,
        user=user1,
        document=document,
        signers_settings={
            'parallel_signing': False,
            'entities': [
                {'id': user1.role_id, 'type': 'role'},
                {'id': user2.role_id, 'type': 'role'},
            ],
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    # Now change it to multilateral
    response = await request_document_update(
        client=client,
        user=user1,
        document=document,
        recipients_settings={
            'is_ordered': True,
            'recipients': [
                {
                    'edrpou': COMPANY_EDRPOU_2,
                    'emails': [TEST_EMAIL_2],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': COMPANY_EDRPOU_1,
                    'emails': [TEST_EMAIL_1],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': COMPANY_EDRPOU_3,
                    'emails': [TEST_EMAIL_3],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
            ],
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.is_multilateral is True
    assert document.expected_owner_signatures == 1
    assert document.expected_recipient_signatures == 1

    flows = await get_flows(document_id=document.id)
    assert len(flows) == 3
    flows_map = {flow.edrpou: flow for flow in flows}
    assert flows_map[COMPANY_EDRPOU_1].pending_signatures_count == 2
    assert flows_map[COMPANY_EDRPOU_1].signatures_count == 2

    assert flows_map[COMPANY_EDRPOU_2].pending_signatures_count == 1
    assert flows_map[COMPANY_EDRPOU_2].signatures_count == 1

    assert flows_map[COMPANY_EDRPOU_3].pending_signatures_count == 1
    assert flows_map[COMPANY_EDRPOU_3].signatures_count == 1

    # Now update from multilateral to multilateral by moving the owner to the end
    response = await request_document_update(
        client=client,
        user=user1,
        document=document,
        recipients_settings={
            'is_ordered': True,
            'recipients': [
                {
                    'edrpou': COMPANY_EDRPOU_2,
                    'emails': [TEST_EMAIL_2],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': COMPANY_EDRPOU_3,
                    'emails': [TEST_EMAIL_3],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': COMPANY_EDRPOU_1,
                    'emails': [TEST_EMAIL_1],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
            ],
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.is_multilateral is True
    assert document.expected_owner_signatures == 1
    assert document.expected_recipient_signatures == 1

    flows = await get_flows(document_id=document.id)
    assert len(flows) == 3
    flows_map = {flow.edrpou: flow for flow in flows}
    assert flows_map[COMPANY_EDRPOU_1].pending_signatures_count == 2
    assert flows_map[COMPANY_EDRPOU_1].signatures_count == 2

    assert flows_map[COMPANY_EDRPOU_2].pending_signatures_count == 1
    assert flows_map[COMPANY_EDRPOU_2].signatures_count == 1

    assert flows_map[COMPANY_EDRPOU_3].pending_signatures_count == 1
    assert flows_map[COMPANY_EDRPOU_3].signatures_count == 1


async def test_update_bilateral_recipient_disable_signing_with_signers(aiohttp_client):
    """
    Test that we can disable signing rights for a recipient even if they have signers.

    Given:
    - Bilateral document with signers
    - Recipient with signers
    When:
    - Change recipient's role to viewer
    Then:
    - Document is updated correctly
    """
    app, client, owner = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        role_id=ROLE_ID_1,
        company_edrpou=COMPANY_EDRPOU_1,
        create_billing_account=True,
    )
    recipient = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        id=DOCUMENT_ID_1,
        first_sign_by=FirstSignBy.owner,
        status_id=DocumentStatus.ready_to_be_signed.value,
        expected_owner_signatures=1,
        expected_recipient_signatures=1,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
    )

    await sign_and_send_document(client=client, document_id=document.id, signer=owner)

    await prepare_document_signer(
        document_id=document.id,
        company_id=recipient.company_id,
        role_id=recipient.role_id,
        group_id=None,
    )

    # Change recipient's role to viewer
    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings={
            'is_ordered': True,
            'recipients': [
                {
                    'edrpou': owner.company_edrpou,
                    'emails': [owner.email],
                    'is_email_hidden': False,
                    'role': 'signer',
                },
                {
                    'edrpou': recipient.company_edrpou,
                    'emails': [recipient.email],
                    'is_email_hidden': False,
                    'role': 'viewer',
                },
            ],
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    document = await get_document(document_id=document.id)
    assert document.is_internal is False
    assert document.is_multilateral is False
    assert document.expected_owner_signatures == 1
    assert document.expected_recipient_signatures == 0

    recipients = await get_document_recipients(document_id=document.id)
    recipient_row = next((r for r in recipients if r.edrpou == recipient.company_edrpou), None)
    assert recipient_row is not None
    assert recipient_row.from_flow is False


@pytest.mark.parametrize(
    'sign_only, status, first_sign_by, expected_owner_signatures, '
    'expected_recipient_signatures, owner_signatures_count, '
    'recipient_signatures_count, by_owner, expected_status, '
    'expected_mailbox_len',
    [
        (
            True,
            DocumentStatus.signed,
            FirstSignBy.owner,
            1,
            1,
            1,
            0,
            True,
            DocumentStatus.signed_and_sent,
            1,
        ),
        (
            True,
            DocumentStatus.approved,
            FirstSignBy.owner,
            1,
            0,
            1,
            0,
            True,
            DocumentStatus.finished,
            1,
        ),
        (
            True,
            DocumentStatus.approved,
            FirstSignBy.owner,
            2,
            0,
            1,
            0,
            True,
            DocumentStatus.approved,
            0,
        ),
    ],
)
async def test_send_document_multiple_signatures(
    telegrambox,
    mailbox,
    aiohttp_client,
    sign_only,
    status,
    first_sign_by,
    expected_owner_signatures,
    expected_recipient_signatures,
    owner_signatures_count,
    recipient_signatures_count,
    by_owner,
    expected_status,
    expected_mailbox_len,
):
    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        owner,
        another_recipients=[recipient],
        status_id=status.value,
        first_sign_by=first_sign_by,
        expected_owner_signatures=expected_owner_signatures,
        expected_recipient_signatures=expected_recipient_signatures,
    )
    document_id = document.id

    for _ in range(owner_signatures_count):
        await prepare_signature_data(app, owner, document, next_status_id=status.value)

    for _ in range(recipient_signatures_count):
        await prepare_signature_data(
            app,
            recipient,
            document,
            is_owner_signature=False,
            next_status_id=status.value,
        )

    if by_owner:
        user = owner
        data = {
            'edrpou': document.edrpou_recipient,
            'email': document.email_recipient,
        }
    else:
        user = recipient
        data = {}

    try:
        response = await client.post(
            f'/internal-api/documents/{document_id}/send',
            data=ujson.dumps(data),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200

        await check_document_status(document_id, expected_status)

        assert len(mailbox) == expected_mailbox_len
        assert len(telegrambox) == expected_mailbox_len
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('vendor', [None, 'API'])
async def test_send_document_paid_by_parent_company(aiohttp_client, vendor):
    app, client, parent = await prepare_client(
        aiohttp_client,
        company_edrpou=UKRPOSHTA_EDRPOU,
        create_api_account=True,
    )
    owner = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=UKRPOSHTA_SUBSIDIARY_EDRPOU,
        create_billing_account=True,
    )
    await set_company_config(
        app,
        company_id=owner.company_id,
        parent_company=UKRPOSHTA_EDRPOU,
        allow_parent_company_pay_for_documents=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=False,
        document_recipients=[
            {
                'edrpou': TEST_RECIPIENT_EDRPOU,
                'emails': [TEST_RECIPIENT_EMAIL],
            }
        ],
        first_sign_by=FirstSignBy.recipient,
        status_id=DocumentStatus.uploaded.value,
        vendor=vendor,
    )

    response = await client.post(
        f'/internal-api/documents/{document.id}/send',
        data=ujson.dumps(
            {
                'edrpou': TEST_RECIPIENT_EDRPOU,
                'email': TEST_RECIPIENT_EMAIL,
            }
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200, await response.text()

    units = TEST_BILLING_ACCOUNT['units']
    # recipient account is untouched for web documents
    parent_units_left = units - 1 if vendor is not None else units
    # charging web document from sender balance
    subsidiary_units_left = units if vendor is not None else units - 1

    async with app['db'].acquire() as conn:
        accounts = await select_company_accounts(conn, owner.company_id)
        assert accounts[0].units_left == subsidiary_units_left

        accounts = await select_company_accounts(conn, parent.company_id)
        assert accounts[0].units_left == parent_units_left


async def test_send_document_paid_by_recipient(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=ZAKUPKI_EDRPOU,
        create_billing_account=True,
    )
    await set_company_config(
        app,
        company_id=recipient.company_id,
        allow_pay_as_recipient=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=False,
        another_recipients=[recipient],
        first_sign_by=FirstSignBy.recipient,
        status_id=DocumentStatus.uploaded.value,
    )

    try:
        response = await client.post(
            f'/internal-api/documents/{document.id}/send',
            data=ujson.dumps(
                {
                    'edrpou': recipient.company_edrpou,
                    'email': recipient.email,
                }
            ),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 200, await response.text()

        async with app['db'].acquire() as conn:
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER

            company = await select_company_by_id(conn, recipient.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER
    finally:
        await cleanup_on_teardown(app)


async def test_send_document_rate_overlimit(aiohttp_client):
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    accounts = await get_billing_accounts(company_id=owner.company_id)
    account_id = accounts[0].id

    async with services.db.acquire() as conn:
        # Default test limit = 30, set available to 0
        await update_account_counter(conn, account_id, units=-30)

    # 31-st sending is not allowed by billing limit
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.recipient,
        status_id=DocumentStatus.uploaded.value,
        vendor='API',
    )
    response = await client.post(
        f'/internal-api/documents/{document.id}/send',
        data=ujson.dumps(
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'email': TEST_DOCUMENT_EMAIL_RECIPIENT,
            }
        ),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 400
    data = await response.json()
    assert data['code'] == 'billing_overlimit'


async def test_send_document_uploaded_by_third_party(aiohttp_client):
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    third_party = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=AGROYARD_EDRPOU,
        create_billing_account=True,
    )
    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        create_billing_account=True,
    )

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=False,
        another_recipients=[recipient],
        first_sign_by=FirstSignBy.recipient,
        status_id=DocumentStatus.uploaded.value,
        uploaded_by=third_party.role_id,
    )

    try:
        response = await client.post(
            f'/internal-api/documents/{document.id}/send',
            data=ujson.dumps(
                {
                    'edrpou': recipient.company_edrpou,
                    'email': recipient.email,
                }
            ),
            headers=prepare_auth_headers(owner),
        )
        assert response.status == 200, await response.text()

        async with app['db'].acquire() as conn:
            accounts = await select_company_accounts(conn, owner.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, owner.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER

            accounts = await select_company_accounts(conn, recipient.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units']
            company = await select_company_by_id(conn, recipient.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER

            accounts = await select_company_accounts(conn, third_party.company_id)
            assert accounts[0].units_left == TEST_BILLING_ACCOUNT['units'] - 1
            company = await select_company_by_id(conn, third_party.company_id)
            assert company.upload_documents_left == TEST_UPLOAD_LEFT_COUNTER - 1
    finally:
        await cleanup_on_teardown(app)


async def test_send_internal_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, is_internal=True)

    try:
        response = await client.post(
            f'/internal-api/documents/{document.id}/send',
            data=ujson.dumps(
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'email': TEST_RECIPIENT_EMAIL,
                }
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400

        data = await response.json()
        assert data['code'] == 'invalid_action'
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'request_data',
    [
        '[]',
        '[1, 2, 3]',
        '[true, false]',
        '[{}, {}]',
    ],
)
async def test_send_document_json_list_returns_bad_request(
    aiohttp_client,
    request_data: str,
) -> None:
    """Attempts to send a document with a JSON request that is a list should
    return a Bad Request error.

    Reported by [DOC-4408].

    Given:
        - A prepared document.
    When:
        - Passing a JSON list data when attempting to send a document.
    Then:
        - The application should return a Bad Request error.
        - And the error code should be 'invalid_json_request'.
    """
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    auth_headers = prepare_auth_headers(user)

    response = await client.post(
        API_V2_SEND_DOCUMENT_URL.format(document_id=document.id),
        data=request_data,
        headers=auth_headers,
    )

    assert response.status == HTTPStatus.BAD_REQUEST
    json_response = await response.json()
    assert json_response['code'] == 'invalid_json_request'


@pytest.mark.parametrize(
    ('existed_links', 'new_link', 'status', 'expected_links'),
    [
        # ## Success cases
        # 1) User can link two independent documents
        (
            [],
            (TEST_UUID_1, TEST_UUID_2),
            HTTPStatus.CREATED,
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
        ),
        # 2) The same as 1
        (
            [],
            (TEST_UUID_2, TEST_UUID_1),
            HTTPStatus.CREATED,
            [(TEST_UUID_2, TEST_UUID_1, COMPANY_EDRPOU_1)],
        ),
        # 3) Every company has own independent list of linked documents. And it's
        # possible to mark document as parent, even if this document is child in
        # alien company
        (
            [
                # shared document is child document for alien document
                (TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_2),
            ],
            (TEST_UUID_5, TEST_UUID_1),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_2),
                (TEST_UUID_5, TEST_UUID_1, COMPANY_EDRPOU_1),
            ],
        ),
        # 4) Parent document can have more than 1 child document
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_1, TEST_UUID_3),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_1, TEST_UUID_3, COMPANY_EDRPOU_1),
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
            ],
        ),
        # 5) Duplicated link is also OK, previous record will be used
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_1, TEST_UUID_2),
            HTTPStatus.OK,
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
        ),
        # 6) Can add child document to the document  that is already child
        # for another document.
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_2, TEST_UUID_3),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
        ),
        # 7) Can use document that is already parent for some document as
        # child document
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_3, TEST_UUID_1),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_3, TEST_UUID_1, COMPANY_EDRPOU_1),
            ],
        ),
        # 8) Can reverse links
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_2, TEST_UUID_1),
            HTTPStatus.CREATED,
            [(TEST_UUID_2, TEST_UUID_1, COMPANY_EDRPOU_1)],
        ),
        # ## Bad cases
        # 1) Can not link not existed document
        (
            [],
            (TEST_UUID_1, TEST_UUID_6),
            HTTPStatus.FORBIDDEN,
            [],
        ),
        # 2) Can not link alien document
        (
            [],
            (TEST_UUID_1, TEST_UUID_4),
            HTTPStatus.FORBIDDEN,
            [],
        ),
        # 3) Can not link document to itself
        (
            [],
            (TEST_UUID_1, TEST_UUID_1),
            HTTPStatus.BAD_REQUEST,
            [],
        ),
        # 4) Can not link document to itself
        (
            [],
            (TEST_UUID_1, TEST_UUID_1),
            HTTPStatus.BAD_REQUEST,
            [],
        ),
        # 5) Not more than one parent is allowed
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_3, TEST_UUID_2),
            HTTPStatus.BAD_REQUEST,
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
        ),
        # 6) Not more than one parent is allowed
        (
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_3, TEST_UUID_2),
            HTTPStatus.BAD_REQUEST,
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
        ),
    ],
)
async def test_create_document_links(
    aiohttp_client, new_link, existed_links, status, expected_links
):
    app, client = await prepare_app_client(aiohttp_client)

    # create company and 3 company documents
    await prepare_company_data(app, id=COMPANY_ID_1, edrpou=COMPANY_EDRPOU_1)
    user = await prepare_user_data(app, email='<EMAIL>', company_id=COMPANY_ID_1)
    await prepare_document_data(app, user, id=TEST_UUID_1)
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, user, id=TEST_UUID_3)

    # Create alien company, one alien document and one shared
    await prepare_company_data(app, id=COMPANY_ID_2, edrpou=COMPANY_EDRPOU_2)
    alien = await prepare_user_data(app, email='<EMAIL>', company_id=COMPANY_ID_2)
    await prepare_document_data(app, alien, id=TEST_UUID_4)
    await prepare_document_data(
        app=app,
        owner=alien,
        id=TEST_UUID_5,
        status_id=DocumentStatus.signed_and_sent.value,
        another_recipients=[user],
    )

    for parent_, child_, edrpou in existed_links:
        await insert_values(
            app=app,
            table=document_link_table,
            company_edrpou=edrpou,
            parent_id=parent_,
            child_id=child_,
        )

    parent_id, child_id = new_link
    path = f'/internal-api/documents/{parent_id}/child/{child_id}'
    response = await client.post(path, headers=prepare_auth_headers(user))
    assert response.status == status

    async with app['db'].acquire() as conn:
        links = await select_all(conn, document_link_table.select())

    actual_links = ((link.parent_id, link.child_id, link.company_edrpou) for link in links)
    assert sorted(actual_links) == sorted(expected_links)


@pytest.mark.parametrize(
    ('existed_links', 'new_link', 'status', 'expected_links'),
    [
        # ## Success cases
        # 1) User can link two independent documents
        (
            [],
            (TEST_UUID_1, [TEST_UUID_2]),
            HTTPStatus.CREATED,
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
        ),
        # 2) The same as 1
        (
            [],
            (TEST_UUID_2, [TEST_UUID_1]),
            HTTPStatus.CREATED,
            [(TEST_UUID_2, TEST_UUID_1, COMPANY_EDRPOU_1)],
        ),
        # 3) Every company has own independent list of linked documents. And it's
        # possible to mark document as parent, even if this document is child in
        # alien company
        (
            [
                # shared document is child document for alien document
                (TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_2),
            ],
            (TEST_UUID_5, [TEST_UUID_1]),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_2),
                (TEST_UUID_5, TEST_UUID_1, COMPANY_EDRPOU_1),
            ],
        ),
        # 4) Change group parent
        (
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_1, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_2, [TEST_UUID_1, TEST_UUID_3]),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_2, TEST_UUID_1, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
        ),
        # 5) Child has outer group parent
        (
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_1, TEST_UUID_3, COMPANY_EDRPOU_1),
                (TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_2, [TEST_UUID_1, TEST_UUID_3, TEST_UUID_5]),
            HTTPStatus.BAD_REQUEST,
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_1, TEST_UUID_3, COMPANY_EDRPOU_1),
                (TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_1),
            ],
        ),
        # 6) Duplicated link is also OK
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_1, [TEST_UUID_2]),
            HTTPStatus.CREATED,
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
        ),
        # 7) Can add child document to the document that is already child
        # for another document.
        (
            [(TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1)],
            (TEST_UUID_2, [TEST_UUID_3]),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
        ),
        # 8) Can add child document to the document that is already child
        # for another document, double level.
        (
            [
                (TEST_UUID_4, TEST_UUID_1, COMPANY_EDRPOU_1),
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_2, [TEST_UUID_3]),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_4, TEST_UUID_1, COMPANY_EDRPOU_1),
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
        ),
        # 9) Delete links
        (
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_1, []),
            HTTPStatus.CREATED,
            [
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
        ),
        # 10) Not more than one parent is allowed
        (
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_3, [TEST_UUID_2]),
            HTTPStatus.BAD_REQUEST,
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_2, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
        ),
    ],
)
async def test_create_document_children_group_links(
    aiohttp_client, new_link, existed_links, status, expected_links
):
    app, client = await prepare_app_client(aiohttp_client)

    # create company and 3 company documents
    await prepare_company_data(app, id=COMPANY_ID_1, edrpou=COMPANY_EDRPOU_1)
    user = await prepare_user_data(app, email='<EMAIL>', company_id=COMPANY_ID_1)
    await prepare_document_data(app, user, id=TEST_UUID_1)
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, user, id=TEST_UUID_3)

    # Create alien company, one alien document and one shared
    await prepare_company_data(app, id=COMPANY_ID_2, edrpou=COMPANY_EDRPOU_2)
    alien = await prepare_user_data(app, email='<EMAIL>', company_id=COMPANY_ID_2)
    await prepare_document_data(app, alien, id=TEST_UUID_4)
    await prepare_document_data(
        app=app,
        owner=alien,
        id=TEST_UUID_5,
        status_id=DocumentStatus.signed_and_sent.value,
        another_recipients=[user],
    )

    for parent_, child_, edrpou in existed_links:
        await insert_values(
            app=app,
            table=document_link_table,
            company_edrpou=edrpou,
            parent_id=parent_,
            child_id=child_,
        )

    parent_id, children_ids = new_link
    path = f'/internal-api/documents/{parent_id}/children'
    response = await client.post(
        path,
        data=ujson.dumps({'children_ids': children_ids}),
        headers=prepare_auth_headers(user),
    )
    assert response.status == status

    async with app['db'].acquire() as conn:
        links = await select_all(conn, document_link_table.select())

    actual_links = ((link.parent_id, link.child_id, link.company_edrpou) for link in links)
    assert sorted(actual_links) == sorted(expected_links)


@pytest.mark.parametrize(
    ('existed_links', 'new_link', 'status', 'expected_links'),
    [
        # ## Success cases
        # 1) Not linked documents is OK
        (
            [],
            (TEST_UUID_1, TEST_UUID_2),
            HTTPStatus.NO_CONTENT,
            [],
        ),
        # 2) Deleting of one link doesn't delete other links
        (
            [
                (TEST_UUID_1, TEST_UUID_2, COMPANY_EDRPOU_1),
                (TEST_UUID_1, TEST_UUID_3, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_1, TEST_UUID_2),
            HTTPStatus.NO_CONTENT,
            [(TEST_UUID_1, TEST_UUID_3, COMPANY_EDRPOU_1)],
        ),
        # 3) Deleting of one link doesn't delete alien links
        (
            [
                (TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_2),
                (TEST_UUID_5, TEST_UUID_1, COMPANY_EDRPOU_1),
            ],
            (TEST_UUID_5, TEST_UUID_1),
            HTTPStatus.NO_CONTENT,
            [(TEST_UUID_4, TEST_UUID_5, COMPANY_EDRPOU_2)],
        ),
        # ## Bad cases
        # 1) Doesn't have access to document
        (
            [],
            (TEST_UUID_4, TEST_UUID_1),
            HTTPStatus.FORBIDDEN,
            [],
        ),
        # 2) Document doesn't exists
        (
            [],
            (TEST_UUID_6, TEST_UUID_1),
            HTTPStatus.FORBIDDEN,
            [],
        ),
    ],
)
async def test_delete_document_links(
    aiohttp_client, new_link, existed_links, status, expected_links
):
    app, client = await prepare_app_client(aiohttp_client)

    # create company and 3 company documents
    await prepare_company_data(app, id=COMPANY_ID_1, edrpou=COMPANY_EDRPOU_1)
    user = await prepare_user_data(app, email='<EMAIL>', company_id=COMPANY_ID_1)
    await prepare_document_data(app, user, id=TEST_UUID_1)
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, user, id=TEST_UUID_3)

    # Create alien company, one alien document and one shared
    await prepare_company_data(app, id=COMPANY_ID_2, edrpou=COMPANY_EDRPOU_2)
    alien = await prepare_user_data(app, email='<EMAIL>', company_id=COMPANY_ID_2)
    await prepare_document_data(app, alien, id=TEST_UUID_4)
    await prepare_document_data(
        app=app,
        owner=alien,
        id=TEST_UUID_5,
        status_id=DocumentStatus.signed_and_sent.value,
        another_recipients=[user],
    )

    for parent_, child_, edrpou in existed_links:
        await insert_values(
            app=app,
            table=document_link_table,
            company_edrpou=edrpou,
            parent_id=parent_,
            child_id=child_,
        )

    parent_id, child_id = new_link
    path = f'/internal-api/documents/{parent_id}/child/{child_id}'
    response = await client.delete(path, headers=prepare_auth_headers(user))
    assert response.status == status

    async with app['db'].acquire() as conn:
        links = await select_all(conn, document_link_table.select())

    actual_links = ((link.parent_id, link.child_id, link.company_edrpou) for link in links)
    assert sorted(actual_links) == sorted(expected_links)


async def test_delete_request(mailbox, aiohttp_client, fcm_message_handler):
    app, client, user = await prepare_client(aiohttp_client, create_mobile_active_session=True)
    url = '/internal-api/documents/delete-request'
    try:
        recipient = await prepare_user_data(
            app,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
            create_mobile_active_session=True,
        )
        document_1 = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            status_id=DocumentStatus.finished.value,
        )
        document_2 = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            status_id=DocumentStatus.finished.value,
        )

        # invalid data
        response = await client.post(url, data=ujson.dumps({}), headers=prepare_auth_headers(user))
        assert response.status == 400
        response_json = await response.json()
        assert 'code' in response_json
        assert response_json['code'] == 'invalid_request'
        details = response_json['details']
        assert 'document_ids' in details
        assert 'message' in details

        # invalid document_id
        response = await client.post(
            url,
            data=ujson.dumps({'document_ids': [str(uuid.uuid1())], 'message': 'message'}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
        response_json = await response.json()
        assert 'code' in response_json
        assert response_json['code'] == 'one_or_more_documents_not_found'

        # delete document_1 request from user
        response = await client.post(
            url,
            data=ujson.dumps({'document_ids': [document_1.id], 'message': 'delete message'}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        response_json = await response.json()
        assert 'status' in response_json
        assert response_json['status'] == 'ok'
        assert len(mailbox) == 1
        email = mailbox[0]
        assert email['To'] == recipient.email
        assert email['Subject'] == 'Ви отримали запит на видалення документа'
        async with app['db'].acquire() as conn:
            delete_requests = await select_delete_requests_by(conn, document_id=document_1.id)
            assert delete_requests
            assert len(delete_requests) == 1
            delete_request = delete_requests[0]
            assert delete_request.document_id == document_1.id
            assert delete_request.status.value == 'new'
            assert delete_request.initiator_role_id == user.role_id

            actions = await select_document_actions_for(document_id=document_1.id)
            assert len(actions) == 1
            assert actions[0].action == document_actions.Action.delete_request_create
            assert actions[0].email == user.email

        # duplicate delete document_1 request from user
        response = await client.post(
            url,
            data=ujson.dumps({'document_ids': [document_1.id], 'message': 'delete message'}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        response_json = await response.json()
        assert 'status' in response_json
        assert response_json['status'] == 'ok'
        assert len(mailbox) == 1
        async with app['db'].acquire() as conn:
            delete_requests = await select_delete_requests_by(conn, document_id=document_1.id)
            assert delete_requests
            assert len(delete_requests) == 1
            delete_request = delete_requests[0]
            assert delete_request.document_id == document_1.id
            assert delete_request.status.value == 'new'
            assert delete_request.initiator_role_id == user.role_id

            actions = await select_document_actions_for(document_id=document_1.id)
            assert len(actions) == 1
            assert actions[0].action == document_actions.Action.delete_request_create
            assert actions[0].email == user.email

        # duplicate delete document_1 request from recipient
        response = await client.post(
            url,
            data=ujson.dumps({'document_ids': [document_1.id], 'message': 'delete message'}),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 200
        response_json = await response.json()
        assert 'status' in response_json
        assert response_json['status'] == 'ok'
        assert len(mailbox) == 1
        async with app['db'].acquire() as conn:
            delete_requests = await select_delete_requests_by(
                conn, initiator_role_id=recipient.role_id
            )
            assert not delete_requests

            delete_requests = await select_delete_requests_by(conn, document_id=document_1.id)
            assert delete_requests
            assert len(delete_requests) == 1

            actions = await select_document_actions_for(role_id=recipient.role_id)
            assert len(actions) == 0

        # delete document_1 (duplicate) & document_2 request from user
        response = await client.post(
            url,
            data=ujson.dumps(
                {
                    'document_ids': [document_1.id, document_2.id],
                    'message': 'delete message',
                }
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        response_json = await response.json()
        assert 'status' in response_json
        assert response_json['status'] == 'ok'
        assert len(mailbox) == 2
        email = mailbox[1]
        assert email['To'] == recipient.email
        assert email['Subject'] == 'Ви отримали запит на видалення документа'
        async with app['db'].acquire() as conn:
            delete_requests = await select_delete_requests_by(conn, initiator_role_id=user.role_id)
            assert delete_requests
            assert len(delete_requests) == 2
            assert {document_2.id, document_1.id} == {item.document_id for item in delete_requests}
            assert {'new'} == {item.status.value for item in delete_requests}
            assert {user.role_id} == {item.initiator_role_id for item in delete_requests}

        # DOC-6830 - Temporary disabled
        # assert len(fcm_message_handler) == 2
        # for message in fcm_message_handler:
        #     assert message['message']['notification']['title'] == 'Запит на видалення'
    finally:
        await cleanup_on_teardown(app)


async def test_accept_delete_request(mailbox, aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    url = '/internal-api/documents/accept-delete-request'
    try:
        recipient = await prepare_user_data(
            app,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        )
        document_1 = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            status_id=DocumentStatus.finished.value,
        )
        document_2 = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            status_id=DocumentStatus.finished.value,
        )
        delete_request_1 = await prepare_delete_document_request(
            app,
            user,
            document_1.id,
            message='message',
            receiver_edrpou=recipient.company_edrpou,
        )
        delete_request_2 = await prepare_delete_document_request(
            app,
            user,
            document_2.id,
            message='message',
            receiver_edrpou=recipient.company_edrpou,
        )
        response = await client.post(url, data=ujson.dumps({}), headers=prepare_auth_headers(user))
        assert response.status == 400
        response_json = await response.json()
        assert response_json['code'] == 'invalid_request'
        response = await client.post(
            url,
            data=ujson.dumps({'delete_request_ids': [str(uuid.uuid1())]}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
        response_json = await response.json()
        assert response_json['code'] == 'delete_requests_mismatch'
        response = await client.post(
            url,
            data=ujson.dumps({'delete_request_ids': [delete_request_1.id, delete_request_2.id]}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
        response_json = await response.json()
        assert response_json['code'] == 'not_all_delete_request_allowed'
        response = await client.post(
            url,
            data=ujson.dumps({'delete_request_ids': [delete_request_1.id, delete_request_2.id]}),
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 200
        response_json = await response.json()
        assert response_json['status'] == 'ok'
        assert len(mailbox) == 2
        assert {recipient.email, user.email} == {email['To'] for email in mailbox}
        assert '[ВАЖЛИВО] Контрагент погодив видалення документів з сервісу Вчасно' in [
            email['Subject'] for email in mailbox
        ]
        assert '[ВАЖЛИВО] Документи погоджено для видалення з сервісу Вчасно' in [
            email['Subject'] for email in mailbox
        ]
        async with app['db'].acquire() as conn:
            documents = await select_documents_by_ids_with_company_info(
                conn, [document_1.id, document_2.id]
            )
            assert not documents
            delete_requests = await select_delete_requests_by_ids(
                conn, [delete_request_1.id, delete_request_2.id]
            )
            assert delete_requests
            assert len(delete_requests) == 2
            assert {delete_request_1.id, delete_request_2.id} == {
                item.id for item in delete_requests
            }
            assert all(item.status.value == 'accepted' for item in delete_requests)
            assert all(item.document_id is not None for item in delete_requests)

            actions = await select_document_actions_for(role_id=recipient.role_id)
            assert len(actions) == 2
            assert actions[0].action == document_actions.Action.delete_request_accept
            assert actions[0].email == recipient.email
            assert actions[1].action == document_actions.Action.delete_request_accept
            assert actions[1].email == recipient.email

    finally:
        await cleanup_on_teardown(app)


async def test_reject_delete_request(mailbox, aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    url = '/internal-api/documents/reject-delete-request'
    try:
        recipient = await prepare_user_data(
            app,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        )
        document_1 = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            status_id=DocumentStatus.finished.value,
        )
        document_2 = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            status_id=DocumentStatus.finished.value,
        )
        delete_request_1 = await prepare_delete_document_request(
            app,
            user,
            document_1.id,
            recipients_emails=[recipient.email],
            message='message',
        )
        delete_request_2 = await prepare_delete_document_request(
            app,
            user,
            document_2.id,
            recipients_emails=[recipient.email],
            message='message',
        )
        response = await client.post(url, data=ujson.dumps({}), headers=prepare_auth_headers(user))
        assert response.status == HTTPStatus.BAD_REQUEST
        assert (await response.json())['code'] == 'invalid_request'

        response = await client.post(
            url,
            json={'delete_request_ids': [str(uuid.uuid1())]},
            headers=prepare_auth_headers(user),
        )
        assert response.status == HTTPStatus.BAD_REQUEST
        assert (await response.json())['code'] == 'delete_requests_mismatch'

        response = await client.post(
            url,
            json={'delete_request_ids': [delete_request_1.id, delete_request_2.id]},
            headers=prepare_auth_headers(user),
        )
        assert response.status == HTTPStatus.BAD_REQUEST
        assert (await response.json())['code'] == 'not_all_delete_request_allowed'

        response = await client.post(
            url,
            json={'delete_request_ids': [delete_request_1.id, delete_request_2.id]},
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == HTTPStatus.OK
        assert (await response.json())['status'] == 'ok'

        async with app['db'].acquire() as conn:
            documents = await select_documents_by_ids_with_company_info(
                conn, [document_1.id, document_2.id]
            )
            assert documents
            delete_requests = await select_delete_requests_by_ids(
                conn, [delete_request_1.id, delete_request_2.id]
            )
            assert delete_requests
            assert len(delete_requests) == 2
            assert {delete_request_1.id, delete_request_2.id} == {
                item.id for item in delete_requests
            }
            assert all(item.status.value == 'rejected' for item in delete_requests)
            assert all(item.date_rejected is not None for item in delete_requests)
        assert len(mailbox) == 1
        assert mailbox[0]['To'] == user.email
    finally:
        await cleanup_on_teardown(app)


async def test_cancel_delete_request(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    url = '/internal-api/cancel-delete-requests'
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document_1 = await prepare_document_data(
        app,
        user,
        another_recipients=[recipient],
        status_id=DocumentStatus.finished.value,
    )
    document_2 = await prepare_document_data(
        app,
        user,
        another_recipients=[recipient],
        status_id=DocumentStatus.finished.value,
    )
    delete_request_1 = await prepare_delete_document_request(
        app,
        user,
        document_1.id,
        recipients_emails=[recipient.email],
        message='message',
        receiver_edrpou=recipient.company_edrpou,
    )
    delete_request_2 = await prepare_delete_document_request(
        app,
        user,
        document_2.id,
        recipients_emails=[recipient.email],
        message='message',
        receiver_edrpou=recipient.company_edrpou,
    )
    response = await client.delete(
        url,
        headers=prepare_auth_headers(user),
    )
    assert response.status == 400
    response_json = await response.json()
    assert response_json['code'] == 'invalid_json_request'
    response = await client.delete(url, headers=prepare_auth_headers(user), json={})
    assert response.status == 400
    response_json = await response.json()
    assert response_json['code'] == 'invalid_json_request'
    response = await client.delete(
        url,
        headers=prepare_auth_headers(user),
        json={'documents_ids': [str(uuid.uuid1())]},
    )
    assert response.status == 400
    response_json = await response.json()
    assert response_json['code'] == 'delete_requests_mismatch'
    response = await client.delete(
        url,
        headers=prepare_auth_headers(recipient),
        json={'documents_ids': [document_1.id]},
    )
    assert response.status == 200
    async with app['db'].acquire() as conn:
        delete_requests = await select_delete_requests_by_ids(
            conn, [delete_request_1.id, delete_request_2.id]
        )
        assert delete_requests
        assert len(delete_requests) == 2
        cancelled_delete_requests = [
            req for req in delete_requests if req.status == DeleteRequestStatus.cancelled
        ]
        assert len(cancelled_delete_requests) == 1
        assert cancelled_delete_requests[0].id == delete_request_1.id


@pytest.mark.parametrize('review_type', [ReviewType.reject.value, ReviewType.approve.value, None])
async def test_3p_sending_with_reviews(aiohttp_client, review_type):
    """
    Update logic:
    as before, we can't sign the document without approval,
    but we can send the document to contragent side
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    coworker = await prepare_user_data(app, email='<EMAIL>')

    document_data = await prepare_document_data_for_email(
        app,
        owner=owner,
        create_recipient=True,
        first_sign_by=FirstSignBy.recipient,
        expected_owner_signatures=0,
        expected_recipient_signatures=1,
    )
    document = document_data.document
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        email=TEST_RECIPIENT_EMAIL,
    )
    recipient_edrpou = recipient.company_edrpou
    recipient_email = recipient.email

    # Make review required, add reviewer to review
    await prepare_review_requests(client, document, owner, reviewers=[coworker])

    if review_type:
        # Create reviews
        await prepare_review(client, user=owner, document=document, review_type=ReviewType.approve)
        await prepare_review(client, user=coworker, document=document, review_type=review_type)

    response = await client.post(
        f'/internal-api/documents/{document.id}/send',
        json={'edrpou': recipient_edrpou, 'email': recipient_email},
        headers=prepare_auth_headers(owner),
    )
    assert response.status == HTTPStatus.OK


async def test_unhide_contact_after_signing(
    aiohttp_client,
):
    email = '<EMAIL>'
    company_edrpou = '********'

    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    recipient1 = await prepare_user_data(
        app,
        email=email,
        company_edrpou=company_edrpou,
    )
    document = await prepare_document_data(
        app,
        owner,
        expected_owner_signatures=1,
        status_id=DocumentStatus.ready_to_be_signed.value,
        another_recipients=[recipient1],
    )

    async with app['db'].acquire() as conn:
        # Manually crate hidden contact emails
        contact_id = await insert_contact(
            conn=conn,
            company_id=owner.company_id,
            data={'edrpou': company_edrpou},
        )
        await insert_contact_person(
            conn=conn,
            contact_id=contact_id,
            data={
                'email': email,
                'is_email_hidden': True,
            },
        )

        await sign_and_send_document(
            client,
            document_id=document.id,
            signer=owner,
        )

        contact_person = await select_contact_person(
            conn=conn,
            email=email,
            edrpou=company_edrpou,
            company_id=owner.company_id,
        )
        # Not open contact on owner signature
        assert contact_person is not None
        assert contact_person.is_email_hidden is True

        await sign_and_send_document(
            client,
            document_id=document.id,
            signer=recipient1,
        )

        contact_person = await select_contact_person(
            conn=conn,
            email=email,
            edrpou=company_edrpou,
            company_id=owner.company_id,
        )
        # Recipient contact will be opened
        assert contact_person is not None
        assert contact_person.is_email_hidden is False


@pytest.mark.parametrize(
    'is_multilateral, '
    'document_recipients, '
    'edrpous, '
    'from_contact, '
    'hidden_contact, '
    'expected, '
    'hidden_emails',
    [
        # not multilateral document with empty recipient emails,
        # not from contact. Must be found such as recipient with this email
        # exists in database but also emails must be hidden.
        (
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': None,
                }
            ],
            [TEST_RECIPIENT_EDRPOU],
            False,
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': None,
                    'is_hidden': True,
                },
            ],
            [TEST_RECIPIENT_EMAIL],
        ),
        # Return filled recipient not hidden email
        (
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL2],
                }
            ],
            [TEST_RECIPIENT_EDRPOU],
            False,
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL2],
                    'is_hidden': False,
                },
            ],
            None,
        ),
        # Return filled recipient not hidden email and ignore not existed
        # company edrpou
        (
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL2],
                }
            ],
            [TEST_RECIPIENT_EDRPOU, TEST_DOCUMENT_EDRPOU_RECIPIENT],
            False,
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL2],
                    'is_hidden': False,
                },
            ],
            None,
        ),
        # Return not hidden contact
        (
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': None,
                }
            ],
            [TEST_RECIPIENT_EDRPOU],
            True,
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL],
                    'is_hidden': False,
                },
            ],
            None,
        ),
        # Return hidden contact email
        (
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': None,
                }
            ],
            [TEST_RECIPIENT_EDRPOU],
            True,
            True,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': None,
                    'is_hidden': True,
                },
            ],
            [TEST_RECIPIENT_ANOTHER_EMAIL],
        ),
        # Return hidden filled recipient not from contact
        (
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL2],
                    'is_emails_hidden': True,
                }
            ],
            [TEST_RECIPIENT_EDRPOU],
            True,
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': None,
                    'is_hidden': True,
                },
            ],
            # Empty list because hidden email is already filled in database
            # and there is not needs to save this email to Redis.
            [],
        ),
        # Return not hidden contact email
        (
            False,
            [],
            [TEST_RECIPIENT_EDRPOU],
            True,
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL],
                    'is_hidden': False,
                },
            ],
            None,
        ),
        # Not exists in contacts and database
        (
            False,
            [],
            [TEST_DOCUMENT_EDRPOU_RECIPIENT],
            True,
            False,
            [],
            None,
        ),
        # Return hidden contact email for multilateral document
        (
            True,
            [],
            [TEST_RECIPIENT_EDRPOU],
            True,
            True,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': None,
                    'is_hidden': True,
                },
            ],
            [TEST_RECIPIENT_ANOTHER_EMAIL],
        ),
        # Return filled recipient for multilateral document
        (
            True,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL2],
                    'is_emails_hidden': False,
                }
            ],
            [TEST_RECIPIENT_EDRPOU],
            False,
            False,
            [
                {
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                    'emails': [TEST_RECIPIENT_ANOTHER_EMAIL2],
                    'is_hidden': False,
                },
            ],
            None,
        ),
    ],
)
async def test_find_recipients_emails(
    aiohttp_client,
    is_multilateral,
    document_recipients,
    from_contact,
    hidden_contact,
    edrpous,
    expected,
    hidden_emails,
):
    app, client, user = await prepare_client(aiohttp_client)

    # TEST_RECIPIENT_EMAIL - actual user in recipient company
    # TEST_RECIPIENT_ANOTHER_EMAIL - recipient email in contacts
    # TEST_RECIPIENT_ANOTHER_EMAIL2 - filled recipient email by owner

    await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(
        app,
        user,
        is_multilateral=is_multilateral,
        document_recipients=document_recipients,
    )

    async with app['db'].acquire() as conn:
        if from_contact:
            await prepare_contact(
                conn=conn,
                user=user,
                contact={
                    **TEST_CONTACT['company'],
                    'edrpou': TEST_RECIPIENT_EDRPOU,
                },
                persons=[
                    {
                        **TEST_CONTACT['person'],
                        'email': TEST_RECIPIENT_ANOTHER_EMAIL,
                        'is_email_hidden': hidden_contact,
                    }
                ],
            )

    response = await request_find_recipients(
        client=client,
        user=user,
        document=document,
        edrpous=edrpous,
    )

    # compare list of dicts
    case = unittest.TestCase()
    case.assertCountEqual(response, expected)

    for recipient in response:
        if recipient['is_hidden']:
            emails_key = get_hidden_email_key(
                role_id=user.role_id,
                recipient_edrpou=recipient['edrpou'],
            )
            emails = await app['redis'].smembers(emails_key)
            assert emails is not None
            assert set(hidden_emails) == set(emails)


async def test_get_document_hash(aiohttp_client):
    test_content = b'test_content'
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='55555555',
    )

    document = await prepare_document_data(app, user, content=test_content)

    async with app['db'].acquire() as conn:
        document_meta = await select_document_meta(conn, document_id=document.id)
    assert document_meta is None

    expected_hash = await eusign_utils.generate_hash_base64(test_content)

    response = await client.get(
        f'/internal-api/documents/{document.id}/hash',
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200
    json = await response.json()
    assert json['document_hash'] == expected_hash

    # check document_meta provided
    document_meta = await get_document_meta(document_id=document.id)
    assert document_meta
    assert document_meta.content_hash == expected_hash
    assert document_meta.content_length == len(test_content)


async def test_get_document_hash_from_meta(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou='55555555',
    )

    document = await prepare_document_data(app, user)

    content_hash = 'some_hash'
    meta = {
        'document_id': document.id,
        'content_hash': content_hash,
        'content_length': 999,
    }
    async with app['db'].acquire() as conn:
        await add_documents_meta(conn, data=[meta])

    response = await client.get(
        f'/internal-api/documents/{document.id}/hash',
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200
    json = await response.json()
    assert json['document_hash'] == content_hash


async def test_open_access(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email=TEST_RECIPIENT_EMAIL)

    document = await prepare_document_data(app, user)
    async with app['db'].acquire() as conn:
        listings = await select_document_listings(
            conn=conn, document_id=document.id, edrpou=user.company_edrpou
        )
    assert len(listings) == 1
    assert listings[0].role_id == user.role_id

    response = await client.patch(
        f'/internal-api/documents/{document.id}/accesses',
        json={'roles_ids': [coworker.role_id]},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with app['db'].acquire() as conn:
        listings = await select_document_listings(
            conn=conn, document_id=document.id, edrpou=user.company_edrpou
        )
    assert len(listings) == 2
    assert {item.role_id for item in listings} == {coworker.role_id, user.role_id}
    for listing in listings:
        if listing.role_id == coworker.role_id:
            assert listing.sources.contains(AccessSource.viewer)


async def test_download_signatures_details(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    title = 'sample_document'
    document = await prepare_document_data(app, user, title=title, extension='.xml')

    r = await client.get(
        f'/internal-api/documents/{document.id}/sign-summary',
        headers=prepare_auth_headers(user),
    )

    expected_title = f'{title}_Kvytantsiia.pdf'
    assert r.content_type == 'application/pdf'
    assert r.content_disposition.filename == expected_title


async def test_change_recipient_delete_comments(aiohttp_client):
    """All comments from previous recipient must be deleted.

    Given:
        - document has comments from recipient.
    When:
        - user changes document recipient.
    Then:
        - all comments from previous recipient are deleted.
    """
    # Arrange
    app, client, owner = await prepare_client(aiohttp_client)
    first_recipient = await prepare_user_data(
        app, company_edrpou='12121212', email='<EMAIL>'
    )
    second_recipient = await prepare_user_data(
        app, company_edrpou='23232323', email='<EMAIL>'
    )
    document = await prepare_document_data(app, owner)
    await change_recipient(
        client,
        owner,
        document.id,
        edrpou=first_recipient.company_edrpou,
        emails=[first_recipient.email],
    )

    owner_comment = 'Comment from owner'
    rec_comment = 'Comment from first recipient'
    async with services.db.acquire() as conn:
        await comments.create_comment(
            conn=conn,
            document_id=document.id,
            document_version_id=None,
            role_id=first_recipient.role_id,
            type_=CommentType.comment,
            text=rec_comment,
            access_company_id=None,
        )
        await comments.create_comment(
            conn=conn,
            document_id=document.id,
            document_version_id=None,
            role_id=owner.role_id,
            type_=CommentType.comment,
            text=owner_comment,
            access_company_id=None,
        )
        comments_rows = await select_all(conn, comment_table.select())

    # Act
    resp = await change_recipient(
        client,
        owner,
        document.id,
        edrpou=second_recipient.company_edrpou,
        emails=[second_recipient.email],
    )

    # Assert
    assert resp.status == 200
    assert len(comments_rows) == 2

    async with services.db.acquire() as conn:
        comments_rows = await select_all(conn, comment_table.select())

    assert len(comments_rows) == 1
    assert comments_rows[0].text == owner_comment


@pytest.mark.parametrize(
    'amount, expected',
    [
        # amount is nullable field
        (None, None),
        # zero value is allowed
        ('0', 0),
        # whole value converts to integer
        ('1234362', 1_234_362),
        ('-1234362', -1_234_362),
        # value range is -100B <= x <= 100B
        ('10000000000000', 100_000_000_000_00),
        ('-10000000000000', -100_000_000_000_00),
    ],
)
async def test_update_document_amount(aiohttp_client, amount, expected):
    """
    Given:
        - `amount` parameter is present in request payload
    When:
        - user specifies amount on updating the document
    Then:
        - `amount` column is updated in `documents` table
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    await prepare_public_document_categories(amount=2)

    # Act
    resp = await request_document_update(
        client=client,
        user=user,
        document=document,
        document_settings={'amount': amount},
    )

    # Assert
    assert resp.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        updated_document = await select_document_by_id(conn, document.id)

    assert updated_document.amount == expected


@pytest.mark.parametrize(
    'amount',
    [
        # amount is out of range (-100B <= x <= 100B)
        '10000000000001',
        '-10000000000001',
    ],
)
async def test_update_document_amount_invalid(aiohttp_client, amount):
    """
    Given:
        - invalid `amount` parameter is present in request payload
    When:
        - user specifies invalid amount on updating the document
    Then:
        - update is failed, user has 400 Bad Request error code
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    # Act
    resp = await request_document_update(
        client=client,
        user=user,
        document=document,
        document_settings={'amount': amount},
    )

    # Assert
    assert resp.status == HTTPStatus.BAD_REQUEST


async def test_send_multilateral_document_success(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app, owner=owner, is_multilateral=True, status_id=DocumentStatus.flow.value
    )
    flow = await prepare_flow_item(
        app,
        document_id=document.id,
        signatures_count=1,
        pending_signatures_count=0,
        meta={'unfinished': True},
    )

    response = await client.post(
        f'/internal-api/documents/{document.id}/send',
        headers=prepare_auth_headers(owner),
    )

    assert response.status == HTTPStatus.OK

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        flow = await get_flow(flow_id=flow.id)

    assert document.status_id == DocumentStatus.finished.value
    assert document.date_finished is not None
    assert not flow.meta.unfinished


async def test_send_multilateral_document_not_finished(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app, owner=owner, is_multilateral=True, status_id=DocumentStatus.flow.value
    )
    await prepare_flow_item(
        app,
        document_id=document.id,
        signatures_count=1,
        pending_signatures_count=1,
    )

    response = await client.post(
        f'/internal-api/documents/{document.id}/send',
        headers=prepare_auth_headers(owner),
    )

    assert response.status == HTTPStatus.OK
    document = await get_document(document_id=document.id)
    assert document.status_id == DocumentStatus.flow.value


async def test_send_document_with_group(aiohttp_client):
    """
    Given:
    - document with signers:
    - role1
    - group1 (role1)
    - role1 signs document
    When:
    - send document
    Then:
    - document changes status from 7003 to 7004
    """

    # Arrange
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
    )

    # Create group
    async with services.db.acquire() as conn:
        group = await insert_group(
            conn=conn,
            name='test',
            id=TEST_UUID_1,
            created_by=user.role_id,
            company_id=user.company_id,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
        )

    # Upload document
    params = {
        'signer_parameters': {
            'signers': [
                {'value': user.role_id, 'signer_type': 'role'},
                {'value': group.id, 'signer_type': 'group'},
            ]
        },
    }

    data = FormData()
    data.add_field('file', b'content', filename='hello.txt')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        path=UPLOAD_DOCUMENT_URL,
        headers=prepare_auth_headers(user),
        data=data,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.CREATED, response_json

    document_id = response_json['documents'][0]['id']

    # Act
    await sign_and_send_document(
        client,
        document_id,
        user,
        sign_data=prepare_signature_form_data(
            user,
            p7s=io.BytesIO(b'p7s'),
            archive='zip',
            recipient_email=TEST_RECIPIENT_EMAIL,
            recipient_edrpou=TEST_RECIPIENT_EDRPOU,
        ),
    )

    await check_document_status(document_id, DocumentStatus.signed_and_sent)


async def test_signatures_count(aiohttp_client):
    """
    DOC-6182
    Given:
    - document with expected_recipient_signatures = 2
    - 1 signer from owner side
    When:
    - Sign document from owner and send
    - Add 2 signatures from recipient side (from the same role)
    Then:
    - document changes status from 7003 to 7008 (finished)
    """

    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        user,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
        expected_recipient_signatures=2,
        expected_owner_signatures=1,
        first_sign_by=FirstSignBy.owner,
    )
    async with services.db.acquire() as conn:
        await insert_document_signers(
            conn,
            [{'document_id': document.id, 'company_id': user.company_id, 'role_id': user.role_id}],
        )

    await sign_and_send_document(
        client,
        document.id,
        user,
        sign_data=prepare_signature_form_data(
            user,
            p7s=io.BytesIO(b'p7s'),
            company_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
            recipient_edrpou=recipient.company_edrpou,
        ),
    )

    await check_document_status(document.id, DocumentStatus.signed_and_sent)

    response = await sign_document(
        client,
        document_id=document.id,
        signer=recipient,
    )
    assert response.status == HTTPStatus.CREATED

    await sign_and_send_document(
        client,
        document.id,
        recipient,
        sign_data=prepare_signature_form_data(recipient, p7s=io.BytesIO(b'p7s')),
    )

    await check_document_status(document.id, DocumentStatus.finished)


async def test_doc_change_status_with_2_expected_owner_signatures(aiohttp_client):
    """
    DOC-6238 case 2 (from comments)
    Given:
    - Upload document with expected_owner_signatures=2
    When:
    - Add 2 signatures from user (without adding him to signers), send doc
    - Add recipient to signers, add signature.
    Then:
    - document changes status from 7007 to 7008 (finished)
    """
    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document = await prepare_document_data(
        app,
        user,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
        expected_owner_signatures=2,
        first_sign_by=FirstSignBy.owner,
        create_document_access_for_recipients=False,
    )

    response = await sign_document(
        client,
        document_id=document.id,
        signer=user,
    )
    assert response.status == HTTPStatus.CREATED

    # check that only owner has access to document
    async with services.db.acquire() as conn:
        listings = await select_listings(conn, [document.id])
        assert len(listings) == 1
        assert listings[0].access_edrpou == user.company_edrpou

    await sign_and_send_document(
        client,
        document.id,
        user,
        sign_data=prepare_signature_form_data(
            user,
            p7s=io.BytesIO(b'p7s'),
            company_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
            recipient_edrpou=recipient.company_edrpou,
        ),
    )

    # Add signer from recipient side
    async with services.db.acquire() as conn:
        await insert_document_signers(
            conn,
            [
                {
                    'document_id': document.id,
                    'company_id': recipient.company_id,
                    'role_id': recipient.role_id,
                }
            ],
        )

        # check access to document
        listings = await select_listings(conn, [document.id])
        assert len(listings) == 2
        assert {item.access_edrpou for item in listings} == {
            user.company_edrpou,
            recipient.company_edrpou,
        }

    await check_document_status(document.id, DocumentStatus.signed_and_sent)

    # Act
    await sign_and_send_document(
        client,
        document.id,
        recipient,
        sign_data=prepare_signature_form_data(recipient, p7s=io.BytesIO(b'p7s')),
    )

    await check_document_status(document.id, DocumentStatus.finished)


async def test_update_document_to_versioned(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)

    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user)
    s3_emulation[document.id] = UploadFile(
        key=f'{document.id}',
        body=b'content',
    )

    async with services.db.acquire() as conn:
        # Target company review: 2 review requests, 1 approved, 1 withdrawn
        await prepare_review_settings_db(
            conn=conn,
            user=user,
            document_id=document.id,
            is_required=True,
            is_parallel=True,
        )
        review_request_1 = await prepare_review_request_db(
            conn=conn,
            document_id=document.id,
            from_role_id=user.role_id,
            to_role_id=coworker_1.role_id,
        )
        review_request_2 = await prepare_review_request_db(
            conn=conn,
            document_id=document.id,
            from_role_id=user.role_id,
            to_role_id=coworker_2.role_id,
        )
        review_1 = await prepare_review_db(
            conn=conn,
            document_id=document.id,
            role_id=coworker_1.role_id,
            type_=ReviewType.approve,
        )
        review_2 = await prepare_review_db(
            conn=conn,
            document_id=document.id,
            role_id=coworker_2.role_id,
            type_=None,
        )
        await prepare_review_status_db(
            conn=conn,
            user=user,
            document_id=document.id,
        )

        # Comments: 1 external, 1 internal
        comment_1 = await prepare_comment_data(
            app=app,
            document_id=document.id,
            role_id=user.role_id,
            text='external comment',
            access_company_id=None,
        )
        comment_2 = await prepare_comment_data(
            app=app,
            document_id=document.id,
            role_id=user.role_id,
            text='internal comment',
            access_company_id=user.company_id,
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    response = await request_document_update(
        client=client,
        user=user,
        document=document,
        version_settings={'is_versioned': True},
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        versions = await select_document_versions(conn, document_ids=[document.id])
        assert len(versions) == 1
        version = versions[0]
        assert version.type == DocumentVersionType.new_upload
        assert version.name == '#1'
        assert version.role_id == user.role_id
        assert version.company_edrpou == user.company_edrpou
        assert version.is_sent is False
        assert version.extension == document.extension

        review_request_1 = await select_review_request(conn, review_request_id=review_request_1.id)
        assert review_request_1 is not None
        assert review_request_1.document_version_id == version.id

        review_request_2 = await select_review_request(conn, review_request_id=review_request_2.id)
        assert review_request_2 is not None
        assert review_request_2.document_version_id == version.id

        review_1 = await select_review(conn, review_id=review_1.id)
        assert review_1 is not None
        assert review_1.document_version_id == version.id

        review_2 = await select_review(conn, review_id=review_2.id)
        assert review_2 is not None
        assert review_2.document_version_id == version.id

        review_statuses = await select_review_statuses(conn, document_id=document.id)
        assert len(review_statuses) == 1
        review_status_1 = review_statuses[0]
        assert review_status_1.document_version_id == version.id

        comments_rows = await select_comments(conn, documents_ids=[document.id])
        assert len(comments_rows) == 2
        comments_map = {item.id: item for item in comments_rows}
        assert comments_map[comment_1.id].document_version_id == version.id
        assert comments_map[comment_2.id].document_version_id == version.id

        antivirus_check = await select_document_antivirus_check(conn, document_id=document.id)
        assert antivirus_check.document_version_id == version.id

    # The original of the document was moved from old location to new
    assert f'{document.id}' not in s3_emulation
    assert f'document_versions/{version.id}' in s3_emulation

    uploaded: UploadFile = s3_emulation[f'document_versions/{version.id}']
    assert uploaded.body == b'content'
    assert uploaded.encrypt is True
    assert uploaded.key == f'document_versions/{version.id}'


async def test_update_document_to_non_versioned(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)

    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user)

    version = await prepare_document_version(
        document_id=document.id,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        is_sent=False,
        type=DocumentVersionType.new_upload,
    )

    s3_emulation[f'document_versions/{version.id}'] = UploadFile(
        key=f'document_versions/{version.id}',
        body=b'content',
    )

    async with services.db.acquire() as conn:
        # Target company review: 2 review requests, 1 approved, 1 withdrawn
        await prepare_review_settings_db(
            conn=conn,
            user=user,
            document_id=document.id,
            is_required=True,
            is_parallel=True,
        )
        review_request_1 = await prepare_review_request_db(
            conn=conn,
            document_id=document.id,
            from_role_id=user.role_id,
            to_role_id=coworker_1.role_id,
            version_id=version.id,
        )
        review_request_2 = await prepare_review_request_db(
            conn=conn,
            document_id=document.id,
            from_role_id=user.role_id,
            to_role_id=coworker_2.role_id,
            version_id=version.id,
        )
        review_1 = await prepare_review_db(
            conn=conn,
            document_id=document.id,
            role_id=coworker_1.role_id,
            type_=ReviewType.approve,
            document_version_id=version.id,
        )
        review_2 = await prepare_review_db(
            conn=conn,
            document_id=document.id,
            role_id=coworker_2.role_id,
            type_=None,
            document_version_id=version.id,
        )
        await prepare_review_status_db(
            conn=conn,
            user=user,
            document_id=document.id,
        )

        # Comments: 1 external, 1 internal
        comment_1 = await prepare_comment_data(
            app=app,
            document_id=document.id,
            role_id=user.role_id,
            text='external comment',
            access_company_id=None,
            version_id=version.id,
        )
        comment_2 = await prepare_comment_data(
            app=app,
            document_id=document.id,
            role_id=user.role_id,
            text='internal comment',
            access_company_id=user.company_id,
            version_id=version.id,
        )
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                document_version_id=version.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    response = await request_document_update(
        client=client,
        user=user,
        document=document,
        version_settings={'is_versioned': False},
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        versions = await select_document_versions(conn, document_ids=[document.id])
        assert len(versions) == 0

        review_request_1 = await select_review_request(conn, review_request_id=review_request_1.id)
        assert review_request_1 is not None
        assert review_request_1.document_version_id is None

        review_request_2 = await select_review_request(conn, review_request_id=review_request_2.id)
        assert review_request_2 is not None
        assert review_request_2.document_version_id is None

        review_1 = await select_review(conn, review_id=review_1.id)
        assert review_1 is not None
        assert review_1.document_version_id is None

        review_2 = await select_review(conn, review_id=review_2.id)
        assert review_2 is not None
        assert review_2.document_version_id is None

        review_statuses = await select_review_statuses(conn, document_id=document.id)
        assert len(review_statuses) == 1
        review_status_1 = review_statuses[0]
        assert review_status_1.document_version_id is None

        comments_rows = await select_comments(conn, documents_ids=[document.id])
        assert len(comments_rows) == 2
        comments_map = {item.id: item for item in comments_rows}
        assert comments_map[comment_1.id].document_version_id is None
        assert comments_map[comment_2.id].document_version_id is None

        antivirus_check = await select_document_antivirus_check(conn, document_id=document.id)
        assert antivirus_check.document_version_id is None

    # The original of the document was moved from old location to new
    assert f'{document.id}' in s3_emulation
    assert f'document_versions/{version.id}' not in s3_emulation

    uploaded: UploadFile = s3_emulation[f'{document.id}']
    assert uploaded.body == b'content'
    assert uploaded.encrypt is True
    assert uploaded.key == f'{document.id}'


@pytest.mark.parametrize(
    'action, settings, expected, expected_status',
    [
        pytest.param(
            'add',
            {
                # user doesn't have access to 'document_tag1', but it's already connected
                # to the document, so validation should pass
                'tags': ['document_tag1', 'role_tag4'],
                'new_tags': ['new_tag6'],
            },
            ['document_tag1', 'document_tag2', 'role_tag4', 'new_tag6'],
            HTTPStatus.OK,
            id='add_action',
        ),
        pytest.param(
            'add',
            {
                'tags': [],
                # This tags exists in the company, but we can pass them by their names
                'new_tags': ['role_tag4', 'role_tag5'],
            },
            ['document_tag1', 'document_tag2', 'role_tag4', 'role_tag5'],
            HTTPStatus.OK,
            id='add_action_existed_in_new_tags',
        ),
        pytest.param(
            'add',
            {
                # Coworker could not add 'owner_tag1' because he has no access to it
                'tags': ['owner_tag1'],
                'new_tags': [],
            },
            ['document_tag1', 'document_tag2'],
            HTTPStatus.FORBIDDEN,
            id='add_action_no_access_in_company',
        ),
        pytest.param(
            'add',
            {
                # Coworker could not add 'recipient_tag1' because he has no access to it
                'tags': ['recipient_tag1'],
                'new_tags': [],
            },
            ['document_tag1', 'document_tag2'],
            HTTPStatus.FORBIDDEN,
            id='add_action_no_access_to_recipient_tag',
        ),
        pytest.param(
            'remove',
            {'tags': ['document_tag2', 'role_tag4']},
            ['document_tag1'],
            HTTPStatus.OK,
            id='remove_action',
        ),
        pytest.param(
            'remove',
            {'tags': ['document_tag1']},
            ['document_tag1', 'document_tag2'],
            HTTPStatus.FORBIDDEN,
            id='remove_action_no_access',
        ),
        pytest.param(
            'replace',
            {'tags': ['document_tag1', 'role_tag4'], 'new_tags': ['new_tag6']},
            ['document_tag1', 'role_tag4', 'new_tag6'],
            HTTPStatus.OK,
            id='replace_action',
        ),
        pytest.param(
            'replace',
            # coworker can't remove 'document_tag1' because he has no access to it
            {'tags': [], 'new_tags': []},
            ['document_tag1', 'document_tag2'],
            HTTPStatus.FORBIDDEN,
            id='replace_empty_action',
        ),
        pytest.param(
            'replace',
            # coworker have access to 'document_tag2', so can remove it by replacing,
            # untouched tag 'document_tag1' should be skipped on validation
            {'tags': ['document_tag1'], 'new_tags': []},
            ['document_tag1'],
            HTTPStatus.OK,
            id='replace_action_skip_connected',
        ),
        pytest.param(
            'replace',
            # coworker can pass 'role_tag5' by their names
            {'tags': ['document_tag1'], 'new_tags': ['role_tag5']},
            ['document_tag1', 'role_tag5'],
            HTTPStatus.OK,
            id='replace_action_existed_in_new_tags',
        ),
        pytest.param(
            'replace',
            # coworker doesn't have access to 'document_tag1', so can't remove it by replacing
            {'tags': ['document_tag2'], 'new_tags': []},
            ['document_tag1', 'document_tag2'],
            HTTPStatus.FORBIDDEN,
            id='replace_action_no_access_in_company',
        ),
        pytest.param(
            'replace',
            # coworker doesn't have access to 'recipient_tag1', so can't remove it by replacing
            {'tags': ['recipient_tag1', 'document_tag1'], 'new_tags': []},
            ['document_tag1', 'document_tag2'],
            HTTPStatus.FORBIDDEN,
            id='replace_action_no_access_to_recipient_tag',
        ),
    ],
)
async def test_update_document_tags(
    aiohttp_client,
    action: str,
    settings: DataDict,
    expected_status,
    expected: list[str],
):
    """
    Check that we can replace tags in the document.
    """

    app, client, user = await prepare_client(aiohttp_client)

    # coworker can access only his own tags because he has lower permissions level
    coworker = await prepare_user_data(
        app,
        company_edrpou=user.company_edrpou,
        email='<EMAIL>',
        user_role=UserRole.user,
        can_view_document=False,
    )

    recipient = await prepare_user_data(
        app=app,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        email=TEST_RECIPIENT_EMAIL,
    )

    document = await prepare_document_data(app, coworker, another_recipients=[recipient])

    async with services.db.acquire() as conn:
        await add_tags(
            conn=conn,
            documents_ids=[document.id],
            tags_ids=[],
            new_tags_names=['document_tag1', 'document_tag2'],
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            assigner_role_id=user.role_id,
        )

        await insert_tags_for_roles(
            conn=conn,
            names=['role_tag4', 'role_tag5'],
            company_id=coworker.company_id,
            roles_ids=[coworker.role_id],
            assigner_role_id=coworker.role_id,
        )
        await insert_tags_for_roles(
            conn=conn,
            names=['owner_tag1'],
            company_id=user.company_id,
            roles_ids=[user.role_id],
            assigner_role_id=user.role_id,
        )
        # coworker has access too only to one tag from the presented on the document
        tags_map = await get_company_tags(names=['document_tag2'], company_id=user.company_id)
        document_tag2_id = tags_map['document_tag2']
        await insert_role_tags_batch(
            conn=conn,
            tags_ids=[document_tag2_id],
            roles_ids=[coworker.role_id],
            assigner_role_id=user.role_id,
        )

        # In result:
        #  - coworker has access to 'document_tag2' that assigned to the document
        #  - coworker has access to 'role_tag4' and 'role_tag5' that exists only in his company
        #  - coworker has no access to 'document_tag1' that assigned to the document
        #  - coworker has no access to 'owner_tag1' that is not assigned to the document

        # The recipient has its own sets of tags, check that we are not removing them
        await add_tags(
            conn=conn,
            documents_ids=[document.id],
            tags_ids=[],
            new_tags_names=['recipient_tag1', 'recipient_tag2'],
            company_id=recipient.company_id,
            company_edrpou=recipient.company_edrpou,
            assigner_role_id=recipient.role_id,
        )

    tags_map = await get_company_tags(
        names=[
            'document_tag1',
            'document_tag2',
            'role_tag4',
            'role_tag5',
            'recipient_tag1',
            'recipient_tag2',
            'owner_tag1',
        ],
    )

    # replace names with ids
    if 'tags' in settings:
        settings['tags_ids'] = [tags_map[tag] for tag in settings.pop('tags')]

    response = await request_document_update(
        client=client,
        user=coworker,
        document=document,
        tags_settings={action: settings},
    )
    assert response.status == expected_status, await response.json()

    tags = await get_document_tags(document_id=document.id)
    recipient_tags = [tag.name for tag in tags if tag.company_id == recipient.company_id]
    target_tags = [tag.name for tag in tags if tag.company_id == user.company_id]

    # Recipient tags should be untouched
    assert sorted(recipient_tags) == ['recipient_tag1', 'recipient_tag2']

    assert sorted(target_tags) == sorted(expected)


async def test_convert_office_document_to_pdf_flow_by_recipient(aiohttp_client):
    """
    Check that it's possible to convert an office document to PDF by recipient, where
    the recipients should sign first.
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        extension='.docx',
        another_recipients=[recipient],
        first_sign_by=FirstSignBy.recipient,
    )
    await prepare_document_version(
        id=TEST_UUID_1,
        role_id=owner.role_id,
        company_edrpou=owner.company_edrpou,
        company_id=owner.company_id,
        document_id=document.id,
        extension='.docx',
        date_created=utc_now(),
    )

    await send_document(client=client, document_id=document.id, sender=owner)

    response = await client.post(
        path=f'/internal-api/documents/{document.id}/office-to-pdf',
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    # assert that we have a new version, that is sent and has pdf content
    async with services.db.acquire() as conn:
        versions = await select_document_versions(conn, document_ids=[document.id])
        assert len(versions) == 2

        upload_version = next((v for v in versions if v.is_new_upload), None)
        assert upload_version is not None

        converted_version = next((v for v in versions if v.is_converted_format), None)
        assert converted_version is not None
        assert converted_version.is_sent is True
        assert converted_version.extension == '.pdf'
        assert converted_version.company_edrpou == recipient.company_edrpou
        assert converted_version.role_id == recipient.role_id
        assert converted_version.name == 'name (PDF)'

        # Check action for document version format conversion is created
        actions = await select_document_actions_for(document_id=document.id)
        assert len(actions) == 2

        convert_action = document_actions.Action.document_version_format_convert
        action = next((a for a in actions if a.action == convert_action), None)
        assert action is not None

    # Check that everyone can sign a converted document now
    response = await sign_document(client=client, document_id=document.id, signer=recipient)
    assert response.status == HTTPStatus.CREATED, await response.json()

    response = await sign_document(client=client, document_id=document.id, signer=owner)
    assert response.status == HTTPStatus.CREATED, await response.json()

    document = await get_document(document_id=document.id)
    assert document.status == DocumentStatus.finished


async def test_convert_office_document_to_pdf_flow_by_owner(aiohttp_client):
    """
    Check that it's possible to convert an office document to PDF by recipient, where
    the recipients should sign first.
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        extension='.docx',
        another_recipients=[recipient],
        first_sign_by=FirstSignBy.recipient,
    )

    # Owner sends first version to the recipient
    await prepare_document_version(
        id=TEST_UUID_1,
        role_id=owner.role_id,
        company_edrpou=owner.company_edrpou,
        company_id=owner.company_id,
        document_id=document.id,
        extension='.docx',
        date_created=YESTERDAY_DATETIME,
    )

    await send_document(client=client, document_id=document.id, sender=owner)

    # The recipient decides to add another version on top of the existing one
    await prepare_document_version(
        id=TEST_UUID_2,
        role_id=recipient.role_id,
        company_edrpou=recipient.company_edrpou,
        company_id=recipient.company_id,
        extension='.docx',
        document_id=document.id,
        date_created=YESTERDAY_DATETIME + datetime.timedelta(hours=1),
    )

    await send_document(client=client, document_id=document.id, sender=recipient)

    # Before starting signing by the owner, the owner decides to convert the document to PDF
    response = await client.post(
        path=f'/internal-api/documents/{document.id}/office-to-pdf',
        headers=prepare_auth_headers(owner),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    # assert that we have a new version, that is sent and has pdf content
    async with services.db.acquire() as conn:
        versions = await select_document_versions(conn, document_ids=[document.id])
        assert len(versions) == 3

        upload_versions = [v for v in versions if v.is_new_upload]
        assert len(upload_versions) == 2

        converted_version = next((v for v in versions if v.is_converted_format), None)
        assert converted_version is not None
        assert converted_version.is_sent is True
        assert converted_version.extension == '.pdf'
        assert converted_version.company_edrpou == owner.company_edrpou
        assert converted_version.role_id == owner.role_id
        assert converted_version.name == 'name (PDF)'

        # Check action for document version format conversion is created
        actions = await select_document_actions_for(document_id=document.id)
        assert len(actions) == 2

        convert_action = document_actions.Action.document_version_format_convert
        action = next((a for a in actions if a.action == convert_action), None)
        assert action is not None

    # Check that everyone can sign a converted document now
    response = await sign_document(client=client, document_id=document.id, signer=owner)
    assert response.status == HTTPStatus.CREATED, await response.json()

    response = await sign_document(client=client, document_id=document.id, signer=recipient)
    assert response.status == HTTPStatus.CREATED, await response.json()

    document = await get_document(document_id=document.id)
    assert document.status == DocumentStatus.finished


async def test_convert_office_document_to_pdf_with_reviews(aiohttp_client):
    """
    Check that it's possible to sign a document after converting it to PDF with required reviews
    from both companies participating in the document
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        extension='.docx',
        another_recipients=[recipient],
        first_sign_by=FirstSignBy.recipient,
    )
    await prepare_document_version(
        id=TEST_UUID_1,
        role_id=owner.role_id,
        company_edrpou=owner.company_edrpou,
        company_id=owner.company_id,
        document_id=document.id,
        extension='.docx',
        date_created=YESTERDAY_DATETIME,
    )

    await send_document(client=client, document_id=document.id, sender=owner)

    # Add required review request from the recipient side
    await prepare_review_requests(
        client=client,
        document=document,
        initiator=recipient,
        reviewers=[recipient],
        is_required=True,
    )
    await prepare_review(
        client=client,
        user=recipient,
        document=document,
        review_type=ReviewType.approve,
    )

    # Add required review request from the owner side
    await prepare_review_requests(
        client=client,
        document=document,
        initiator=owner,
        reviewers=[owner],
        is_required=True,
    )
    await prepare_review(
        client=client,
        user=owner,
        document=document,
        review_type=ReviewType.approve,
    )

    response = await client.post(
        path=f'/internal-api/documents/{document.id}/office-to-pdf',
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    # assert that we have a new version, that is sent and has pdf content
    async with services.db.acquire() as conn:
        versions = await select_document_versions(conn, document_ids=[document.id])
        assert len(versions) == 2

        upload_version = next((v for v in versions if v.is_new_upload), None)
        assert upload_version is not None

        converted_version = next((v for v in versions if v.is_converted_format), None)
        assert converted_version is not None
        assert converted_version.is_sent is True
        assert converted_version.extension == '.pdf'
        assert converted_version.company_edrpou == recipient.company_edrpou
        assert converted_version.role_id == recipient.role_id
        assert converted_version.name == 'name (PDF)'

        # Check action for document version format conversion is created
        actions = await select_document_actions_for(document_id=document.id)
        assert len(actions) == 2

        convert_action = document_actions.Action.document_version_format_convert
        action = next((a for a in actions if a.action == convert_action), None)
        assert action is not None

        # Check that reviews from both companies are copied from the previous version
        # 2 from an upload version (owner, recipient), 2 from a converted version
        # (owner, recipient)
        statuses = await select_review_statuses(conn, document_id=document.id)
        assert len(statuses) == 4
        assert {(s.document_version_id, s.edrpou) for s in statuses} == {
            (upload_version.id, owner.company_edrpou),
            (upload_version.id, recipient.company_edrpou),
            (converted_version.id, owner.company_edrpou),
            (converted_version.id, recipient.company_edrpou),
        }
        assert all(s.status == ReviewStatus.approved for s in statuses)

        # 2 from upload version (owner, recipient), 2 from a converted version (owner, recipient)
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 4
        assert {(r.document_version_id, r.edrpou) for r in reviews} == {
            (upload_version.id, owner.company_edrpou),
            (upload_version.id, recipient.company_edrpou),
            (converted_version.id, owner.company_edrpou),
            (converted_version.id, recipient.company_edrpou),
        }
        assert all(r.type == ReviewType.approve for r in reviews)

        # check that all reviews have user_email
        assert all(r.user_email is not None for r in reviews)

    # Check that everyone can sign a converted document now
    response = await sign_document(client=client, document_id=document.id, signer=recipient)
    assert response.status == HTTPStatus.CREATED, await response.json()

    response = await sign_document(client=client, document_id=document.id, signer=owner)
    assert response.status == HTTPStatus.CREATED, await response.json()

    document = await get_document(document_id=document.id)
    assert document.status == DocumentStatus.finished


async def test_update_group_viewers(aiohttp_client, mailbox):
    """
    Test various scenarios of updating group viewers.
    Such as adding, replacing, and removing viewers with groups.
    """
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    coworker = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user, id=DOCUMENT_ID_1)

    async with app['db'].acquire() as conn:
        group0 = await add_group(
            conn=conn,
            name='test3',
            user=user,
            group_id=TEST_UUID_3,
        )
        await insert_group_member(
            conn=conn,
            group_id=group0.id,
            role_id=coworker2.role_id,
            created_by=user.role_id,
        )
        await insert_group_document_access(
            conn=conn,
            values_list=[
                {
                    'document_id': document.id,
                    'group_id': group0.id,
                    'created_by': user.role_id,
                    'company_id': user.company_id,
                }
            ],
        )
        group1 = await add_group(
            conn=conn,
            name='test',
            user=user,
            group_id=TEST_UUID_1,
        )
        await insert_group_member(
            conn=conn,
            group_id=group1.id,
            role_id=coworker.role_id,
            created_by=user.role_id,
        )
        group2 = await add_group(
            conn=conn,
            name='test2',
            user=user,
            group_id=TEST_UUID_2,
        )
        await insert_group_member(
            conn=conn,
            group_id=group2.id,
            role_id=coworker2.role_id,
            created_by=user.role_id,
        )

    # Case 0
    # Add group1 to the document
    # but group0 is already in the document
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'viewers_settings': {
                'add_viewers': [
                    {'type': 'group', 'id': group0.id},
                    {'type': 'group', 'id': group1.id},
                    {'type': 'role', 'id': user.role_id},
                ],
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.BAD_REQUEST, await response.json()

    # Case 1
    # Add group1 and group2 to the document
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'viewers_settings': {
                'add_viewers': [
                    {'type': 'group', 'id': group1.id},
                    {'type': 'role', 'id': user.role_id},
                ],
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()
    assert [mail['to'] for mail in mailbox] == [coworker.email]
    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [document.id])
        result = {(item.role_id, item.sources) for item in listings}
        assert result == {
            (user.role_id, AccessSource.default | AccessSource.viewer),
            (coworker.role_id, AccessSource.group_viewer),
        }

    mailbox.clear()

    # Case 2
    # Replace all viewers with another one
    # Only group2 is in the document
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'viewers_settings': {
                'replace_viewers': [
                    {'type': 'group', 'id': group2.id},
                ],
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()
    assert [mail['to'] for mail in mailbox] == [coworker2.email]
    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [document.id])
        result = {(item.role_id, item.sources) for item in listings}
        assert result == {
            (user.role_id, AccessSource.default),
            (coworker2.role_id, AccessSource.group_viewer),
        }

    mailbox.clear()

    # Case 3
    # Delete group1 from the document that are not in the document
    # It does nothing because group1 is not in the document
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'viewers_settings': {
                'remove_viewers': [
                    {'type': 'group', 'id': group1.id},
                ],
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()
    assert [mail['to'] for mail in mailbox] == []
    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [document.id])
        result = {(item.role_id, item.sources) for item in listings}
        assert result == {
            (user.role_id, AccessSource.default),
            (coworker2.role_id, AccessSource.group_viewer),
        }

    # Case 4
    # Delete group2 from the document that are not in the document
    response = await client.patch(
        f'/internal-api/documents/{document.id}',
        json={
            'viewers_settings': {
                'remove_viewers': [
                    {'type': 'group', 'id': group2.id},
                ],
            }
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()
    assert [mail['to'] for mail in mailbox] == []
    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [document.id])
        result = {(item.role_id, item.sources) for item in listings}
        assert result == {
            (user.role_id, AccessSource.default),
        }


async def test_delete_document_with_drafts(aiohttp_client, s3_emulation):
    """
    Make sure only related drafts are deleted when the document is deleted.
    """
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document = await prepare_document_data(
        app,
        user,
        id=TEST_UUID_1,
        another_recipients=[recipient],
    )
    document2 = await prepare_document_data(app, user, id=TEST_UUID_2)
    document3 = await prepare_document_data(
        app,
        user,
        id=TEST_UUID_3,
        another_recipients=[recipient],
    )

    version = await prepare_document_version(
        id=TEST_UUID_1,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        date_created=utc_now(),
    )
    version2 = await prepare_document_version(
        id=TEST_UUID_2,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document2.id,
        date_created=utc_now(),
    )
    version3 = await prepare_document_version(
        id=TEST_UUID_3,
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document3.id,
        date_created=utc_now(),
    )
    draft1 = await prepare_draft_from_version(role_id=user.role_id, version_id=version.id)
    draft2 = await prepare_draft_from_version(role_id=user.role_id, version_id=version2.id)

    await prepare_draft_from_version(role_id=user.role_id, version_id=version3.id)

    # Delete with request
    delete_request_1 = await prepare_delete_document_request(
        app,
        user,
        document.id,
        message='message',
        receiver_edrpou=recipient.company_edrpou,
    )
    response = await client.post(
        '/internal-api/documents/accept-delete-request',
        data=ujson.dumps({'delete_request_ids': [delete_request_1.id]}),
        headers=prepare_auth_headers(recipient),
    )
    assert response.status == 200

    async with services.db.acquire() as conn:
        assert await count(conn, document_table) == 2
        assert await count(conn, draft_table) == 2

    assert s3_emulation.delete_calls == [
        document.id,
        get_document_version_key(version_id=version.id),
        get_draft_s3_key(draft1.id),
    ]

    # Regular document delete
    await request_delete_document(client=client, user=user, document=document2)

    async with services.db.acquire() as conn:
        assert await count(conn, document_table) == 1
        assert await count(conn, draft_table) == 1

    assert s3_emulation.delete_calls == [
        document.id,
        get_document_version_key(version_id=version.id),
        get_draft_s3_key(draft1.id),
        document2.id,
        get_document_version_key(version_id=version2.id),
        get_draft_s3_key(draft2.id),
    ]


async def test_send_document_to_user_not_in_company(aiohttp_client, mailbox):
    """
    Given:
    - document
    - admin in the company
    When:
    - user sends document to recipient that doesn't have role in company
      but has role in another company
    Then:
    - notification is sent to admins in the company
    """
    # Arrange
    app, client, recipient_admin1 = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        can_receive_inbox=True,
        is_admin=True,
    )
    user_not_in_company_email = '<EMAIL>'
    await prepare_user_data(app, email=user_not_in_company_email, company_edrpou='********')

    owner = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        create_billing_account=True,
        enable_pro_functionality=True,
    )

    # Create document
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.recipient,
    )

    # Act
    response = await send_document(
        client=client,
        document_id=document.id,
        sender=owner,
        recipient_email=user_not_in_company_email,
        recipient_edrpou=recipient_admin1.company_edrpou,
    )

    # Assert
    assert response.status == HTTPStatus.OK, await response.json()

    # Check that notification is sent to all admins in the company
    admin_emails = {item['To'] for item in mailbox}
    assert admin_emails == {recipient_admin1.email, user_not_in_company_email}


async def test_send_document_to_user_without_roles(aiohttp_client, mailbox):
    """
    Given:
    - document
    - admin in the company
    When:
    - user sends document to recipient that doesn't exists in the system
    Then:
    - notification is sent to admins in the company
    """
    # Arrange
    app, client, recipient_admin1 = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        can_receive_inbox=True,
        is_admin=True,
    )
    user_not_in_company_email = '<EMAIL>'

    owner = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        create_billing_account=True,
        enable_pro_functionality=True,
    )

    # Create document
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.recipient,
    )

    # Act
    response = await send_document(
        client=client,
        document_id=document.id,
        sender=owner,
        recipient_email=user_not_in_company_email,
        recipient_edrpou=recipient_admin1.company_edrpou,
    )

    # Assert
    assert response.status == HTTPStatus.OK, await response.json()

    # Check that notification is sent to all admins in the company
    admin_emails = {item['To'] for item in mailbox}
    assert admin_emails == {recipient_admin1.email, user_not_in_company_email}


async def test_date_finished_cleanup_after_signing(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    recipient = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    document = await prepare_document_data(app, owner, first_sign_by=FirstSignBy.recipient)

    await send_document(
        client,
        document_id=document.id,
        sender=owner,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
    )
    await prepare_document_reject(client, user=recipient, document=document)
    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.status_id == DocumentStatus.reject.value
        assert document.date_finished is not None

    await sign_document(client, document_id=document.id, signer=recipient)

    async with app['db'].acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.status_id == DocumentStatus.signed_and_sent.value
        assert document.date_finished is None


async def test_delete_latest_doc_version(aiohttp_client):
    """Test case for DOC-7127"""
    app, client, user = await prepare_client(
        aiohttp_client, is_admin=True, create_billing_account=True
    )
    coworker = await prepare_user_data(app=app, email='<EMAIL>')

    recipient_1 = await prepare_user_data(
        app=app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    document = await prepare_document_data(app, owner=user)

    await prepare_document_version(
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        is_sent=False,
        type=DocumentVersionType.new_upload,
    )

    recipients_settings = {
        'recipients': [
            {
                'edrpou': recipient_1.company_edrpou,
                'emails': [recipient_1.email],
                'is_email_hidden': False,
                'role': 'signer',
            },
            {
                'edrpou': user.company_edrpou,
                'emails': [user.email],
                'is_email_hidden': False,
                'role': 'signer',
            },
        ],
    }

    await request_document_update(
        client,
        user=user,
        document=document,
        recipients_settings=recipients_settings,
        signers=[user, coworker],
    )
    await send_document(client, document_id=document.id, sender=user)

    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.edrpou_owner == user.company_edrpou
        assert document.edrpou_recipient == recipient_1.company_edrpou
        assert document.first_sign_by == FirstSignBy.recipient

    await prepare_document_version(
        role_id=recipient_1.role_id,
        company_edrpou=recipient_1.company_edrpou,
        company_id=recipient_1.company_id,
        document_id=document.id,
        is_sent=False,
        type=DocumentVersionType.new_upload,
    )
    await send_document(client, document_id=document.id, sender=recipient_1)
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.edrpou_owner == recipient_1.company_edrpou
        assert document.edrpou_recipient == user.company_edrpou
        assert document.first_sign_by == FirstSignBy.recipient

        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
        )

    response = await client.delete(
        DELETE_DOCUMENT_VERSION_URL.format(
            document_id=document.id,
            version_id=document_version.id,
        ),
        headers=prepare_auth_headers(recipient_1),
    )
    assert response.status == HTTPStatus.NO_CONTENT, await response.json()

    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.edrpou_owner == user.company_edrpou
        assert document.edrpou_recipient == recipient_1.company_edrpou
        assert document.first_sign_by == FirstSignBy.recipient

    await sign_and_send_document(client=client, document_id=document.id, signer=recipient_1)
    await prepare_signature_data(app, user, document)
    await sign_and_send_document(client=client, document_id=document.id, signer=coworker)

    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.status_id == DocumentStatus.finished.value


@pytest.mark.parametrize(
    'versions_count, expected_http_status', [(1, HTTPStatus.OK), (2, HTTPStatus.BAD_REQUEST)]
)
async def test_validation_on_update_versioned_to_multilateral_document(
    aiohttp_client, versions_count, expected_http_status
):
    app, client, user = await prepare_client(aiohttp_client)

    recipient_1 = await prepare_user_data(
        app=app,
        email=USER_EMAIL_2,
        company_edrpou=COMPANY_EDRPOU_2,
    )

    recipient_2 = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_ANOTHER_EMAIL,
        company_edrpou=COMPANY_EDRPOU_3,
    )

    document = await prepare_document_data(
        app,
        owner=user,
        is_internal=True,
    )
    for _ in range(versions_count):
        await prepare_document_version(
            role_id=user.role_id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            document_id=document.id,
            is_sent=False,
            type=DocumentVersionType.new_upload,
        )

    recipients_settings = {
        'recipients': [
            {
                'edrpou': user.company_edrpou,
                'emails': [user.email],
                'is_email_hidden': False,
                'role': 'signer',
            },
            {
                'edrpou': recipient_1.company_edrpou,
                'emails': [recipient_1.email],
                'is_email_hidden': False,
                'role': 'signer',
            },
            {
                'edrpou': recipient_2.company_edrpou,
                'emails': [recipient_2.email],
                'is_email_hidden': False,
                'role': 'signer',
            },
        ],
    }

    response = await request_document_update(
        client,
        user=user,
        document=document,
        recipients_settings=recipients_settings,
        excepted_status=expected_http_status,
    )
    if expected_http_status == HTTPStatus.BAD_REQUEST:
        response_json = await response.json()
        assert response_json['reason'] == 'Версійний документ не може бути багатостороннім'


async def test_change_internal_to_biliteral_versioned_document(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    recipient_1 = await prepare_user_data(
        app=app,
        email=USER_EMAIL_2,
        role_id=ROLE_ID_2,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    document = await prepare_document_data(
        app,
        owner=user,
        is_internal=True,
    )
    await prepare_document_version(
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        is_sent=False,
        type=DocumentVersionType.new_upload,
    )

    recipients_settings = {
        'recipients': [
            {
                'edrpou': user.company_edrpou,
                'emails': [user.email],
                'is_email_hidden': False,
                'role': 'signer',
            },
            {
                'edrpou': recipient_1.company_edrpou,
                'emails': [recipient_1.email],
                'is_email_hidden': False,
                'role': 'signer',
            },
        ],
    }

    await request_document_update(
        client,
        user=user,
        document=document,
        recipients_settings=recipients_settings,
    )

    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.is_internal is False
        assert document.first_sign_by == FirstSignBy.recipient

    # Check that we set FirstSignBy.owner when update recipient_settings with is_versioned=False
    await request_document_update(
        client,
        user=user,
        document=document,
        recipients_settings=recipients_settings,
        version_settings={'is_versioned': False},
    )
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, document.id)
        assert document.first_sign_by == FirstSignBy.owner


async def test_delete_document_with_pending_delete_request(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    document = await prepare_document_data(
        app,
        owner=user,
        is_internal=True,
    )
    await prepare_delete_document_request(app, user, document.id, status=DeleteRequestStatus.new)

    response = await client.delete(
        f'/internal-api/documents/{document.id}',
        headers=prepare_auth_headers(user),
    )
    assert response.status == 403

    data = await response.json()
    assert data == {
        'code': 'access_denied',
        'details': None,
        'reason': 'Документ має неопрацьований запит на видалення',
    }
