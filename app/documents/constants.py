#: Number of owner signatures by default
DEFAULT_EXPECTED_OWNER_SIGNATURES = 1

#: Number of recipient signatures by default
DEFAULT_EXPECTED_RECIPIENT_SIGNATURES = 1

#: Number of signatures before document moved to Finished state
DEFAULT_SIGNATURES_TO_FINISH = 2

#: Max number of files shown in email for signers
MAX_NUMBER_OF_FILES_FOR_SIGNERS = 5


# Time in second how long hidden email will be stored in Redis
TTL_HIDDEN_EMAIL = 60 * 60 * 3  # three hours

# List of document extensions that can be converted to PDF. Keep it in sync with
# CONVERT_TO_PDF_EXTENSIONS on the frontend. Also, if you're adding a new extension here,
# check that the file extension is supported by the Gotenberg service.
CONVERT_TO_PDF_EXTENSIONS = (
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.txt',
)

MAX_DOCUMENT_CHILDREN = 200
DOCUMENT_NUMBER_MAX_LENGTH = 512
DOCUMENT_TITLE_MAX_LENGTH = 512
DOCUMENT_AMOUNT_MAX_VALUE = 100_000_000_000_00

# Max number of tags that can be attached to a document (old)
TAGS_MAX_LENGTH = 255
