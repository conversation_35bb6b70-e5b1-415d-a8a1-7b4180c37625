import datetime
import logging
from _operator import eq
from collections.abc import Iterable, Mapping
from typing import Any

import sqlalchemy as sa
from citext import CIText
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql.elements import ClauseElement

from app.auth.db import USER_COLUMNS, select_role_by_emails_and_edrpous
from app.auth.tables import (
    company_table,
    role_table,
    user_active_role_company_join,
    user_role_join,
    user_table,
)
from app.auth.types import Auth<PERSON><PERSON>, User
from app.auth.utils import (
    can_user_view_all_company_documents,
    has_permission,
    is_admin,
)
from app.comments.tables import comment_table
from app.comments.types import CommentRecipientForNotification
from app.document_antivirus.tables import document_antivirus_check_table
from app.document_automation.tables import assigned_document_templates_table
from app.document_versions.tables import document_version_table
from app.documents.enums import (
    AccessSource,
    DeleteRequestStatus,
    DocumentAccessLevel,
    FirstSignBy,
)
from app.documents.tables import (
    company_listing_table,
    delete_document_settings_table,
    delete_request_table,
    document_access_settings_private_table,
    document_link_table,
    document_meta_table,
    document_recipients_join,
    document_recipients_table,
    document_table,
    document_with_uploader_fields,
    document_with_uploader_join,
    latest_document_recipients_table,
    listing_table,
    unnested_recipient_emails,
)
from app.documents.types import (
    CompanyAccess,
    DeleteDocumentFromDBIds,
    DeleteDocumentSettings,
    DeleteListingsCtx,
    Document,
    DocumentForOwnerEmail,
    DocumentForRecipientEmail,
    DocumentMeta,
    DocumentOwnerForZakupkiEmail,
    DocumentRecipient,
    ListingDataAggregator,
    ListingRow,
    PrivateDocumentIndexationItem,
    ReviewReminderDocumentRecipient,
    UpdateDocumentDict,
    UploaderInfo,
    UpsertDocumentRecipientDict,
)
from app.documents_fields.tables import document_parameters_table
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import BitMaskOperator, CommentStatus, DocumentStatus, UserRole
from app.lib.helpers import (
    generate_uuid,
    join_comma_separated_emails,
    split_comma_separated_emails,
)
from app.lib.types import (
    DataDict,
    StrDict,
    StrList,
)
from app.lib.validators import is_valid_email
from app.models import (
    exists,
    select_all,
    select_one,
)
from app.models.compat import SQLAlchemyValues
from app.notifications.tables import notification_table
from app.reviews.tables import (
    review_request_table,
    review_setting_table,
    review_status_table,
    review_table,
)
from app.services import services
from app.sign_sessions import db as sign_sessions
from app.sign_sessions.types import SignSessionExtended
from app.signatures.tables import (
    document_cloud_signer_table,
    document_signer_table,
    signature_table,
)
from app.tags.tables import document_tag_table

logger = logging.getLogger(__name__)

COMMENT_RECIPIENT_SELECTABLE_COLUMNS = [
    user_table.c.id,
    user_table.c.email,
    user_table.c.first_name,
    user_table.c.telegram_chat_id,
    user_table.c.language,
    role_table.c.id.label('role_id'),
    role_table.c.can_receive_inbox,
    role_table.c.can_receive_comments,
    role_table.c.can_receive_rejects,
    role_table.c.can_receive_reminders,
    role_table.c.can_receive_notifications,
    role_table.c.can_receive_access_to_doc,
    role_table.c.can_receive_delete_requests,
    company_table.c.id.label('company_id'),
    company_table.c.edrpou.label('company_edrpou'),
    company_table.c.name.label('company_name'),
]


DOCUMENT_FOR_EMAIL_COLUMNS = [
    # stable info which should be the same for both sides
    document_table.c.id,
    document_table.c.user_id,
    document_table.c.edrpou_owner,
    document_table.c.first_sign_by,
    document_table.c.title.label('doc_title'),
    document_table.c.expected_owner_signatures,
    document_table.c.expected_recipient_signatures,
    document_table.c.is_multilateral,
    document_recipients_table.c.edrpou.label('edrpou_recipient'),
    user_table.c.first_name,
    document_table.c.source,
    # recipient company if we are sending to owner
    # and owner if we are sending to recipient
    company_table.c.edrpou.label('company_edrpou'),
    company_table.c.name.label('company_name'),
]

# fmt: off
DOCUMENT_FOR_EMAIL_BASE_JOIN = (
    document_table
    .join(
        user_table,
        user_table.c.id == document_table.c.user_id,
    )
    .outerjoin(
        document_recipients_table,
        sa.and_(
            document_recipients_table.c.document_id == document_table.c.id,
            # ignore row with owner in `document_recipients_table`
            document_recipients_table.c.edrpou != document_table.c.edrpou_owner,
        ),
    )
)
# fmt: on

DEFAULT_GET_DELETE_REQUESTS_LIMIT = 100


def build_is_document_multi_sign_clause() -> ClauseElement:
    return sa.or_(
        sa.and_(
            document_table.c.first_sign_by == FirstSignBy.owner,
            document_table.c.expected_recipient_signatures > 0,
        ),
        sa.and_(
            document_table.c.first_sign_by == FirstSignBy.recipient,
            document_table.c.expected_owner_signatures > 0,
        ),
    )


def build_is_document_one_sign_clause() -> ClauseElement:
    return sa.or_(
        sa.and_(
            document_table.c.first_sign_by == FirstSignBy.owner,
            document_table.c.expected_recipient_signatures == 0,
        ),
        sa.and_(
            document_table.c.first_sign_by == FirstSignBy.recipient,
            document_table.c.expected_owner_signatures == 0,
        ),
    )


async def change_documents_for_public_api(conn: DBConnection, document_ids: list[str]) -> None:
    await update_documents(
        conn,
        document_ids=document_ids,
        data={
            'has_changed_for_public_api': True,
        },
    )


async def exists_document(
    conn: DBConnection,
    document_id: str,
) -> bool:
    """
    Check if document exists in database
    """
    return await exists(
        conn=conn,
        select_from=document_table,
        clause=document_table.c.id == document_id,
    )


async def delete_documents_from_db(
    conn: DBConnection,
    document_ids: StrList,
    tags_ids: StrList,
) -> DeleteDocumentFromDBIds:
    """Delete documents from database in transaction by given ID list."""
    from app.flow.tables import doc_flow_table

    async with conn.begin():
        await conn.execute(
            signature_table.delete().where(signature_table.c.document_id.in_(document_ids))
        )
        await delete_listings(conn, filters=[listing_table.c.document_id.in_(document_ids)])
        await conn.execute(
            review_table.delete().where(review_table.c.document_id.in_(document_ids))
        )
        await conn.execute(
            review_request_table.delete().where(
                review_request_table.c.document_id.in_(document_ids)
            )
        )
        await conn.execute(
            review_status_table.delete().where(review_status_table.c.document_id.in_(document_ids))
        )
        await conn.execute(
            review_setting_table.delete().where(
                review_setting_table.c.document_id.in_(document_ids)
            )
        )
        await conn.execute(
            document_signer_table.delete().where(
                document_signer_table.c.document_id.in_(document_ids)
            )
        )
        await conn.execute(
            document_link_table.delete().where(document_link_table.c.parent_id.in_(document_ids))
        )
        await conn.execute(
            document_link_table.delete().where(document_link_table.c.child_id.in_(document_ids))
        )
        comment_ids = await select_all(
            conn,
            (
                comment_table.delete()
                .where(comment_table.c.document_id.in_(document_ids))
                .returning(comment_table.c.id)
            ),
        )
        await sign_sessions.delete_sign_session(
            conn=conn,
            documents_ids=document_ids,
        )
        await conn.execute(
            notification_table.delete().where(notification_table.c.document_id.in_(document_ids))
        )
        await conn.execute(
            doc_flow_table.delete().where(doc_flow_table.c.document_id.in_(document_ids))
        )
        await conn.execute(
            document_recipients_table.delete().where(
                document_recipients_table.c.document_id.in_(document_ids)
            )
        )

        if tags_ids:
            from app.tags.db import log_tags_delete

            await log_tags_delete(conn, tags_ids=tags_ids)

        await conn.execute(
            document_tag_table.delete().where(document_tag_table.c.document_id.in_(document_ids))
        )

        from app.tags.db import safe_delete_document_tags

        await safe_delete_document_tags(conn=conn, tags_ids=tags_ids, documents_ids=document_ids)

        await conn.execute(
            assigned_document_templates_table.delete().where(
                assigned_document_templates_table.c.document_id.in_(document_ids)
            )
        )
        await conn.execute(
            document_parameters_table.delete().where(
                document_parameters_table.c.document_id.in_(document_ids)
            )
        )
        await conn.execute(
            document_antivirus_check_table.delete().where(
                document_antivirus_check_table.c.document_id.in_(document_ids)
            )
        )
        await conn.execute(
            document_cloud_signer_table.delete().where(
                document_cloud_signer_table.c.document_id.in_(document_ids)
            )
        )
        await conn.execute(
            document_version_table.delete().where(
                document_version_table.c.document_id.in_(document_ids)
            )
        )
        # NOTE: this should be the last line
        await conn.execute(document_table.delete().where(document_table.c.id.in_(document_ids)))

        return DeleteDocumentFromDBIds(comment_ids=[item.id for item in comment_ids])


async def exists_listing(conn: DBConnection, document_id: str, edrpou: str) -> bool:
    """Check if given EDRPOU has access to document ID via listing table."""
    clause = sa.and_(
        listing_table.c.document_id == document_id,
        listing_table.c.access_edrpou == edrpou,
    )
    return await exists(conn, listing_table, clause)


async def exists_recipient(conn: DBConnection, data: StrDict) -> bool:
    clause = sa.and_(
        document_table.c.edrpou_owner == data['user_edrpou'],
        document_table.c.status_id >= DocumentStatus.sent.value,
        document_recipients_table.c.edrpou == data['edrpou'],
    )

    select_from = document_table.outerjoin(
        document_recipients_table,
        document_recipients_table.c.document_id == document_table.c.id,
    )
    return await exists(conn, select_from, clause)


async def select_documents_by_recipient(conn: DBConnection, email: str, edrpou: str) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            sa.select([document_recipients_table.c.document_id.label('id')]).where(
                sa.and_(
                    document_recipients_table.c.edrpou == edrpou,
                    document_recipients_table.c.emails.contains(f'{{{email}}}'),
                )
            )
        ),
    )


async def insert_documents(
    conn: DBConnection,
    data: list[DataDict],
) -> list[Document]:
    """
    Insert multiple documents into database
    """
    rows = await select_all(
        conn=conn,
        query=(document_table.insert().values(data).returning(document_table)),
    )

    return [Document.from_row(row) for row in rows]


async def insert_listings(
    conn: DBConnection,
    data: DataDict | list[DataDict],
    overwrite_source: bool = False,
) -> None:
    """
    Add new item to listing table if current document not yet accessible by
    given EDRPOU.

    Also add records to company first access to document (company_listing_table).
    """
    if not data:
        return

    data = [data] if isinstance(data, Mapping) else data

    # Sort data to avoid deadlocks in case of concurrent upserts
    data = sorted(
        data,
        key=lambda item: (
            item['document_id'],
            item['access_edrpou'],
            item.get('role_id') or '',
        ),
    )

    statement = postgresql.insert(listing_table).values(data)

    if overwrite_source:
        new_sources = statement.excluded.sources
    else:
        append_operator = statement.excluded.sources.op(BitMaskOperator.append)
        new_sources = append_operator(listing_table.c.sources)

    async with conn.begin():
        await conn.execute(
            statement.on_conflict_do_update(
                index_elements=['access_edrpou', 'role_id', 'document_id'],
                set_={'sources': new_sources},
            )
        )
        await insert_company_listings(conn, data)


async def delete_listings(conn: DBConnection, filters: list[ClauseElement]) -> DeleteListingsCtx:
    """
    Delete data from listing_table + company_listing_table

    IMPORTANT! When calling this function, you need manually process its result:
      1) send document to indexation
      2) remove documents from ES_Index by DeleteListingsCtx.company_listing_ids, like this
         await schedule_remove_company_listings_es(DeleteListingsCtx.company_listing_ids)
    """

    assert filters, 'Expected not empty filters for deletion'

    async with conn.begin():
        result = await select_all(
            conn, query=(listing_table.delete().where(sa.and_(*filters)).returning(listing_table))
        )

        listings = [ListingRow.from_row(row) for row in result]

        disconnected_doc_ids = [item.document_id for item in listings]

        subquery = (
            sa.select([company_listing_table.c.id])
            .select_from(
                company_listing_table.outerjoin(
                    listing_table,
                    sa.and_(
                        company_listing_table.c.document_id == listing_table.c.document_id,
                        company_listing_table.c.edrpou == listing_table.c.access_edrpou,
                    ),
                )
            )
            .where(
                sa.and_(
                    company_listing_table.c.document_id.in_(disconnected_doc_ids),
                    listing_table.c.id.is_(None),
                )
            )
        )
        result = await select_all(
            conn,
            query=(
                company_listing_table.delete()
                .where(company_listing_table.c.id.in_(subquery))
                .returning(company_listing_table.c.id)
            ),
        )

    return DeleteListingsCtx(
        listings=listings,
        company_listing_ids=[row.id for row in result],
    )


async def insert_company_listings(conn: DBConnection, data: list[DataDict]) -> None:
    """
    Add records to company first access to document table (company_listing_table).
    """
    document_ids = list({item['document_id'] for item in data})

    documents_data = await select_all(
        conn,
        query=(
            sa.select([document_table.c.id, document_table.c.seqnum]).where(
                document_table.c.id.in_(document_ids)
            )
        ),
    )
    documents_num_map = {doc.id: doc.seqnum for doc in documents_data}

    insert_data_dict = {}
    for item in data:
        doc_id = item['document_id']
        edrpou = item['access_edrpou']
        insert_item = {
            'edrpou': edrpou,
            'document_id': doc_id,
            'document_num': documents_num_map.get(doc_id),
        }

        # In same cases, like tests, we can provide date_created in data parameter
        if 'date_created' in item:
            insert_item['date_created'] = item['date_created']

        insert_data_dict[(doc_id, edrpou)] = insert_item

    insert_data = list(insert_data_dict.values())

    # Sort data to avoid deadlocks in case of concurrent upserts
    insert_data = sorted(
        insert_data,
        key=lambda item: (
            item['document_id'],
            item['edrpou'],
        ),
    )

    insert_statement = postgresql.insert(company_listing_table).values(insert_data)
    statement = insert_statement.on_conflict_do_update(
        constraint='uix_company_edrpou_document_id',
        set_={
            # In case of conflict, pick the smallest date_created among what we already
            # have in a database and what we are trying to insert.
            'date_created': sa.func.least(
                company_listing_table.c.date_created,
                insert_statement.excluded.date_created,
            ),
        },
    )
    await conn.execute(statement)


async def delete_listing_source(
    conn: DBConnection,
    listings_ids: StrList,
    source: AccessSource,
) -> None:
    remove_source = listing_table.c.sources.op(BitMaskOperator.remove)
    await conn.execute(
        listing_table.update()
        .values({'sources': remove_source(source)})
        .where(listing_table.c.id.in_(listings_ids))
    )


async def delete_empty_listing(
    conn: DBConnection,
    *,
    listings_ids: StrList,
) -> DeleteListingsCtx:
    """
    IMPORTANT! When calling this function, you need manually process its result:
      1) send document to indexation
      2) remove documents from ES_Index by DeleteListingsCtx.company_listing_ids, like this
         await schedule_remove_company_listings_es(DeleteListingsCtx.company_listing_ids)
    """

    if not listings_ids:
        logging.exception('Can not delete empty listing')
        raise ValueError('Provide not empty parameters')

    filters = [
        listing_table.c.id.in_(listings_ids),
        listing_table.c.sources == AccessSource._empty,
    ]
    return await delete_listings(conn, filters)


async def select_document_by_id(
    conn: DBConnection,
    document_id: str,
    selectable: list[Any] | None = None,
) -> DBRow | None:
    """Select document by ID. Return None if document does not exist."""
    docs = await select_documents_by_ids_with_company_info(
        conn=conn,
        document_ids=[document_id],
        selectable=selectable,
    )
    if not docs:
        return None
    return docs[0]


async def select_document(
    conn: DBConnection,
    *,
    document_id: str | None,
) -> Document | None:
    """
    Get document from database, alternative to select_document_by_id, but with typed
    object in response.

    WARN: Do not add "joins" to that function, and keep it simple as possible. The main
    and single use case for that function – select a document row from the database.
    Other metadata, that exists in other tables, should be queried by a second query on
    the application level or by a custom function for narrow use cases.

    If you need to query a row from the “documents” table by applying a filter on
    another table, use EXIST filter, which does not require joining to the main table.
    """

    filters = []
    if document_id is not None:
        filters.append(document_table.c.id == document_id)

    assert filters, 'Add at least one filter'

    row = await select_one(
        conn=conn,
        query=(sa.select([document_table]).select_from(document_table).where(sa.and_(*filters))),
    )
    return Document.from_row(row) if row else None


async def select_documents(
    conn: DBConnection,
    *,
    documents_ids: list[str] | None = None,
) -> list[Document]:
    """
    Get documents from a database by given IDs.
    """
    filters = []
    if documents_ids is not None:
        filters.append(document_table.c.id.in_(documents_ids))

    assert filters, 'Add at least one filter'

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_table])
            .select_from(document_table)
            .where(
                sa.and_(*filters),
            )
        ),
    )
    return [Document.from_row(row) for row in rows]


async def select_document_date_rejected(
    conn: DBConnection,
    document_id: str,
) -> datetime.datetime | None:
    """Select document rejected date (last rejected comment date)."""
    return await conn.scalar(
        sa.select([comment_table.c.date_created])
        .select_from(
            document_table.outerjoin(
                comment_table,
                comment_table.c.document_id == document_table.c.id,
            )
        )
        .where(
            sa.and_(
                document_table.c.id == document_id,
                comment_table.c.status_id == CommentStatus.reject.value,
            )
        )
        .order_by(comment_table.c.date_created.desc())
        .limit(1)
    )


async def select_documents_date_rejected(
    conn: DBConnection,
    documents_ids: list[str],
) -> list[DBRow]:
    query = (
        sa.select(
            [
                comment_table.c.document_id,
                sa.func.max(comment_table.c.date_created).label('date_rejected'),
            ]
        )
        .where(
            sa.and_(
                comment_table.c.document_id.in_(documents_ids),
                comment_table.c.status_id == CommentStatus.reject.value,
            )
        )
        .group_by(comment_table.c.document_id)
    )
    return await select_all(conn, query)


async def select_document_for_owner_email(
    conn: DBConnection,
    document_id: str,
) -> DocumentForOwnerEmail | None:
    """
    Select basic info about the document for sending email to the document owner and
    info about the sender of that document (a recipient for the case when we are
    sending to the owner)
    """

    # TODO: move to array not comma separated emails
    email_column = sa.func.array_to_string(document_recipients_table.c.emails, ', ')

    row = await select_one(
        conn=conn,
        query=(
            sa.select(
                [
                    *DOCUMENT_FOR_EMAIL_COLUMNS,
                    # recipient email (sender of email)
                    email_column.label('email'),
                ]
            )
            .select_from(
                DOCUMENT_FOR_EMAIL_BASE_JOIN.join(
                    company_table,
                    company_table.c.edrpou == document_recipients_table.c.edrpou,
                )
            )
            .where(document_table.c.id == document_id)
        ),
    )
    return DocumentForOwnerEmail.from_row(row)


async def select_document_for_recipient_email(
    conn: DBConnection,
    document_id: str,
) -> DocumentForRecipientEmail | None:
    """
    Select basic info about the document for sending email to the document recipient
    and info about the sender of that document (a owner for the case when we are
    sending to the recipient)
    """
    # TODO: use separate database function for selecting information about "sender"
    #  object, to simplify overall workflow by using two generic function:
    #   - select_document(...)
    #   - select_user(...)
    row = await select_one(
        conn=conn,
        query=(
            sa.select(
                [
                    *DOCUMENT_FOR_EMAIL_COLUMNS,
                    document_table.c.source,
                    # document owner email and phone (sender of the document)
                    user_table.c.email,
                    user_table.c.phone,
                ]
            )
            .select_from(
                DOCUMENT_FOR_EMAIL_BASE_JOIN.join(
                    company_table,
                    company_table.c.edrpou == document_table.c.edrpou_owner,
                )
            )
            .where(document_table.c.id == document_id)
        ),
    )
    return DocumentForRecipientEmail.from_row(row) if row else None


async def select_document_owner_for_zakupki_email(
    conn: DBConnection, document_id: str
) -> DocumentOwnerForZakupkiEmail | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select(
                [
                    user_table.c.email,
                    user_table.c.first_name,
                    company_table.c.name,
                    company_table.c.edrpou,
                ]
            )
            .select_from(
                document_recipients_table.outerjoin(
                    user_table,
                    document_recipients_table.c.emails.any(user_table.c.email),
                ).outerjoin(
                    company_table,
                    company_table.c.edrpou == document_recipients_table.c.edrpou,
                )
            )
            .where(
                sa.and_(
                    document_recipients_table.c.document_id == document_id,
                    document_recipients_table.c.external_meta['is_document_owner'].astext == 'true',
                )
            )
        ),
    )
    return DocumentOwnerForZakupkiEmail.from_row(row) if row else None


async def select_unfinished_documents(conn: DBConnection, ids: list[str]) -> list[Document]:
    """Get unfinished documents from documents list"""
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_table])
            .select_from(document_table)
            .where(
                sa.and_(
                    document_table.c.id.in_(ids),
                    document_table.c.status_id.notin_(
                        [
                            DocumentStatus.finished.value,
                            DocumentStatus.deleted.value,
                            DocumentStatus.reject.value,
                        ]
                    ),
                )
            )
        ),
    )
    return [Document.from_row(row) for row in rows]


async def select_documents_by_owner_edrpou(conn: DBConnection, edrpou: str) -> list[DBRow]:
    """Get all documents by edrpou_owner"""

    return await select_all(
        conn=conn,
        query=(
            sa.select([document_table])
            .select_from(document_table)
            .where(document_table.c.edrpou_owner == edrpou)
        ),
    )


async def select_owner_documents_for_api(
    conn: DBConnection,
    documents_ids: list[str],
) -> list[DBRow]:
    """
    Get owner documents for API. It's optimized version of select_owner_documents_by
    """

    return await select_all(
        conn=conn,
        query=(
            sa.select([document_table])
            .select_from(document_table)
            .where(document_table.c.id.in_(documents_ids))
            .order_by(document_table.c.seqnum.desc())
        ),
    )


async def select_documents_by_ids(
    conn: DBConnection,
    ids: StrList,
    selectable: list[Any] | None = None,
) -> list[DBRow]:
    selectable_clause: list[Any] = selectable or [document_table]
    return await select_all(
        conn,
        (
            sa.select(selectable_clause)
            .select_from(document_table)
            .where(document_table.c.id.in_(ids))
        ),
    )


async def select_invalid_signed_documents_by_ids(conn: DBConnection, ids: StrList) -> set[str]:
    rows = await select_all(
        conn,
        (
            sa.select([document_table.c.id])
            .select_from(document_table)
            .where(
                sa.and_(
                    document_table.c.id.in_(ids),
                    document_table.c.is_invalid_signed.is_(True),
                )
            )
        ),
    )
    return {d.id for d in rows}


async def select_missing_documents_ids(
    conn: DBConnection,
    documents_ids: list[str],
) -> set[str]:
    """
    Return documents ids that do not exist in a database.
    """
    if not documents_ids:
        return set()

    doc_ids_values = SQLAlchemyValues(
        [sa.column('id', sa.String)],
        *[(doc_id,) for doc_id in documents_ids],
        alias_name='ids_to_check',
    )

    query = (
        sa.select([doc_ids_values.c.id])
        .select_from(
            doc_ids_values.outerjoin(
                document_table,
                doc_ids_values.c.id == document_table.c.id,
            )
        )
        .where(document_table.c.id.is_(None))
    )

    rows = await select_all(conn, query)
    return {row.id for row in rows}


async def select_documents_ids_with_access(
    conn: DBConnection,
    documents_ids: StrList,
    user: User,
) -> list[str]:
    from app.documents.utils import get_documents_access_filters

    rows = await select_all(
        conn,
        (
            sa.select([document_table.c.id])
            .select_from(
                document_table.outerjoin(
                    listing_table,
                    listing_table.c.document_id == document_table.c.id,
                )
            )
            .where(
                sa.and_(
                    document_table.c.id.in_(documents_ids),
                    get_documents_access_filters(user),
                )
            )
        ),
    )
    return [row.id for row in rows]


async def select_comment_recipients(
    conn: DBConnection,
    *,
    document_id: str,
    company_edrpou: str,
    is_internal: bool,
    author_email: str | None,
) -> list[CommentRecipientForNotification]:
    # build document_access sources filter:
    # sa.or_(
    #     # document signer
    #     listing_table.c.sources.contains(AccessSource.signer),
    #     # mentioned in comment
    #     listing_table.c.sources.contains(AccessSource.comment),
    #     # document viewer
    #     listing_table.c.sources.contains(AccessSource.viewer),
    #     # reviewer
    #     listing_table.c.sources.contains(AccessSource.reviewer),
    # ),
    # 2(signer) + 4(viewer) + 8(reviewer) + 16(comment) = 30
    # (sources & 30) <> 0 means “at least one of these flags is set in sources: 2, 4, 8, or 16.”
    sources_total = (
        AccessSource.signer.add(AccessSource.viewer)
        .add(AccessSource.reviewer)
        .add(AccessSource.comment)
    )
    access_source_filter = listing_table.c.sources.op('&')(sources_total) != AccessSource._empty

    if is_internal:
        # Build optimized query for internal comments

        company_filters = [company_table.c.edrpou == company_edrpou]
        if author_email:
            company_filters.append(user_table.c.email != author_email)

        comment_receivers_cte = (
            sa.select(COMMENT_RECIPIENT_SELECTABLE_COLUMNS)
            .select_from(user_active_role_company_join)
            .where(sa.and_(*company_filters))
            .cte('comment_receivers_cte')
        )

        # Users that has direct access
        join_access = comment_receivers_cte.outerjoin(
            listing_table,
            sa.and_(
                listing_table.c.role_id == comment_receivers_cte.c.role_id,
                listing_table.c.document_id == document_id,
                listing_table.c.access_edrpou == comment_receivers_cte.c.company_edrpou,
                access_source_filter,
            ),
        )

        # Users that are recipients for a given document
        recipients_emails = document_recipients_table.c.emails.cast(postgresql.ARRAY(CIText))
        join_recipients = join_access.outerjoin(
            document_recipients_table,
            sa.and_(
                document_recipients_table.c.document_id == document_id,
                document_recipients_table.c.edrpou == comment_receivers_cte.c.company_edrpou,
                recipients_emails.any(comment_receivers_cte.c.email),
            ),
        )

        # Select document owner
        join_documents = join_recipients.outerjoin(
            document_table,
            sa.and_(
                document_table.c.id == document_id,
                document_table.c.uploaded_by == comment_receivers_cte.c.role_id,
                document_table.c.edrpou_owner == comment_receivers_cte.c.company_edrpou,
            ),
        )

        query = (
            sa.select([comment_receivers_cte])
            .select_from(join_documents)
            .where(
                sa.or_(
                    listing_table.c.role_id.isnot(None),
                    document_recipients_table.c.document_id.isnot(None),
                    document_table.c.id.isnot(None),
                )
            )
            .distinct()
        )
    else:
        # Build optimized query for non-internal comments

        # Users that has direct access
        users_with_access_query = (
            sa.select(COMMENT_RECIPIENT_SELECTABLE_COLUMNS)
            .select_from(
                user_active_role_company_join.join(
                    listing_table, listing_table.c.role_id == role_table.c.id
                )
            )
            .where(
                sa.and_(
                    listing_table.c.document_id == document_id,
                    access_source_filter,
                )
            )
        )

        # Also select users that are recipients for given document
        recipients_emails = document_recipients_table.c.emails.cast(postgresql.ARRAY(CIText))
        users_document_recipients = sa.select(COMMENT_RECIPIENT_SELECTABLE_COLUMNS).select_from(
            user_active_role_company_join.join(
                document_recipients_table,
                sa.and_(
                    document_recipients_table.c.document_id == document_id,
                    document_recipients_table.c.edrpou == company_table.c.edrpou,
                    recipients_emails.any(user_table.c.email),
                ),
            )
        )

        # Select document owner
        users_document_owners = sa.select(COMMENT_RECIPIENT_SELECTABLE_COLUMNS).select_from(
            user_active_role_company_join.join(
                document_table,
                sa.and_(
                    document_table.c.id == document_id,
                    document_table.c.uploaded_by == role_table.c.id,
                    document_table.c.edrpou_owner == company_table.c.edrpou,
                ),
            )
        )

        possible_comment_recipient = sa.union_all(
            users_with_access_query,
            users_document_recipients,
            users_document_owners,
        ).alias('possible_comment_recipients')

        company_filters = [possible_comment_recipient.c.company_edrpou != company_edrpou]
        if author_email:
            company_filters.append(possible_comment_recipient.c.email != author_email)
        query = possible_comment_recipient.select().where(sa.and_(*company_filters)).distinct()

    rows = await select_all(conn, query)
    return [CommentRecipientForNotification.from_row(row) for row in rows]


async def select_default_comment_admin_recipients(
    conn: DBConnection, *, document_id: str, current_edrpou: str
) -> list[CommentRecipientForNotification]:
    # TODO: exclude private documents?
    query = (
        sa.select(COMMENT_RECIPIENT_SELECTABLE_COLUMNS)
        .select_from(
            user_active_role_company_join.join(
                listing_table,
                company_table.c.edrpou == listing_table.c.access_edrpou,
            )
        )
        .where(
            sa.and_(
                listing_table.c.document_id == document_id,
                listing_table.c.access_edrpou != current_edrpou,
                sa.or_(
                    role_table.c.user_role == UserRole.admin.value,
                    role_table.c.can_view_document.is_(True),
                ),
            )
        )
        .distinct()
    )
    rows = await select_all(conn, query)
    return [CommentRecipientForNotification.from_row(row) for row in rows]


async def select_reject_notification_recipients(
    conn: DBConnection,
    document: Document,
    rejecter_edrpou: str,
) -> list[User]:
    """
    Selects the recipients for a rejection notification for the given document.

    The recipients will be the coworkers for internal documents and the
    recipients for non-internal documents.
    """

    filters = [
        listing_table.c.document_id == document.id,
        user_table.c.is_placeholder.isnot(True),
        user_table.c.email.isnot(None),
    ]

    if document.is_internal:
        # Filter coworkers for internal documents
        filters.append(listing_table.c.access_edrpou == rejecter_edrpou)
    else:
        # Filter recipients for non-internal documents
        filters.append(listing_table.c.access_edrpou != rejecter_edrpou)

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([*USER_COLUMNS])
            .select_from(
                user_active_role_company_join.join(
                    listing_table,
                    listing_table.c.role_id == role_table.c.id,
                )
            )
            .where(sa.and_(*filters))
            .distinct(role_table.c.id)
        ),
    )

    return [User.from_row(row) for row in rows]


async def update_document(conn: DBConnection, data: UpdateDocumentDict) -> Document:
    """
    Update a document in database and return updated document

    WARNING: if you update "edrpou_recipient" and "email_recipient" fields, don't forget to
    update "document_recipients" table too
    """
    data = data.copy()
    document_id = data.pop('document_id')  # type: ignore[misc]
    data.setdefault('date_updated', sa.text('now()'))

    if '_unsafe_edrpou_recipient' in data:
        data['edrpou_recipient'] = data.pop('_unsafe_edrpou_recipient')  # type: ignore
    if '_unsafe_email_recipient' in data:
        data['email_recipient'] = data.pop('_unsafe_email_recipient')  # type: ignore

    if status_id := data.get('status_id'):
        data['date_finished'] = None
        if DocumentStatus(status_id).is_final:
            data['date_finished'] = sa.text('now()')

    document_row = await select_one(
        conn=conn,
        query=(
            document_table.update()
            .values(data)
            .where(document_table.c.id == document_id)
            .returning(document_table)
        ),
    )
    return Document.from_row(document_row)


async def update_documents(
    conn: DBConnection,
    document_ids: set[str] | list[str],
    data: DataDict,
    *,
    extra_clause: ClauseElement | None = None,
    return_updated_ids: bool = False,
) -> list[DBRow] | None:
    data.setdefault('date_updated', sa.text('now()'))
    if not document_ids:
        return None

    clause = document_table.c.id.in_(document_ids)

    if extra_clause is not None:
        clause = sa.and_(clause, extra_clause)

    if 'email_recipient' in data or 'edrpou_recipient' in data:
        raise ValueError('For updating recipients, use insert recipient function')

    if status_id := data.get('status_id'):
        data['date_finished'] = None
        if DocumentStatus(status_id).is_final:
            data['date_finished'] = sa.text('now()')

    query = document_table.update().values(data).where(clause)

    if not return_updated_ids:
        await conn.execute(query)
        return None

    return await select_all(conn, query=query.returning(document_table.c.id))


async def update_document_date_delivered_none(conn: DBConnection, document_id: str) -> None:
    await update_document(
        conn,
        {
            'document_id': document_id,
            'date_delivered': None,
        },
    )


async def _update_documents_date_delivered(
    conn: DBConnection,
    company_edrpou: str,
    documents_ids: list[str],
    date_delivered: datetime.datetime | None,
) -> None:
    # TODO: deprecate writing date delivery to the documents.date_delivery column
    document_3p_clause = sa.and_(
        document_table.c.first_sign_by == FirstSignBy.recipient,
        sa.or_(
            sa.and_(
                # Recipient
                document_table.c.edrpou_owner != company_edrpou,
                sa.or_(
                    document_table.c.status_id == DocumentStatus.sent.value,
                    document_table.c.status_id == DocumentStatus.finished.value,
                ),
            ),
            sa.and_(
                # document owner
                document_table.c.edrpou_owner == company_edrpou,
                sa.or_(
                    document_table.c.status_id == DocumentStatus.signed_and_sent.value,
                    document_table.c.status_id == DocumentStatus.finished.value,
                ),
            ),
        ),
    )

    # 3p case
    await conn.execute(
        document_table.update()
        .where(
            sa.and_(
                document_table.c.date_delivered.is_(None),
                document_table.c.id.in_(documents_ids),
                document_3p_clause,
            )
        )
        .values(date_delivered=date_delivered)
    )

    # non 3p case
    await conn.execute(
        document_table.update()
        .where(
            sa.and_(
                document_table.c.date_delivered.is_(None),
                document_table.c.id.in_(documents_ids),
                document_table.c.first_sign_by != FirstSignBy.recipient,
                document_table.c.edrpou_owner != company_edrpou,
            )
        )
        .values(date_delivered=date_delivered)
    )

    await conn.execute(
        document_recipients_table.update()
        .where(
            sa.and_(
                document_recipients_table.c.edrpou == company_edrpou,
                document_recipients_table.c.date_delivered.is_(None),
                document_recipients_table.c.date_sent.isnot(None),
                # Most of the time we will have only one document_id in the list
                document_recipients_table.c.document_id.in_(documents_ids),
            )
        )
        .values(date_delivered=date_delivered)
    )


async def update_sign_session_documents_date_delivered(
    conn: DBConnection,
    sign_session: SignSessionExtended,
    document_ids: list[str],
    date_delivered: datetime.datetime,
) -> None:
    filters = [
        listing_table.c.access_edrpou == sign_session.edrpou,
        listing_table.c.document_id.in_(document_ids),
        document_recipients_table.c.document_id.in_(document_ids),
    ]

    if sign_session.user_role and not is_admin(sign_session.user_role):
        filters.append(listing_table.c.role_id == sign_session.role_id)

    query = (
        sa.select([sa.distinct(document_recipients_table.c.document_id.label('id'))])
        .select_from(
            document_recipients_table.join(
                listing_table,
                listing_table.c.document_id == document_recipients_table.c.document_id,
            )
        )
        .where(sa.and_(*filters))
    )

    documents = await select_all(conn=conn, query=query)
    if not documents:
        return

    update_documents_ids = [document.id for document in documents]
    await _update_documents_date_delivered(
        conn=conn,
        company_edrpou=sign_session.edrpou,
        documents_ids=update_documents_ids,
        date_delivered=date_delivered,
    )


def build_private_document_access_filter(
    user: AuthUser | User,
) -> sa.sql.elements.BinaryExpression:
    """
    Build WHERE clause filter that checks if a document is private for a user's company. You can
    also use this filter to find out if a document is extended by adding sa.not_ to this filter

    WARNING: requires listing_table to be presented in FROM clause
    """
    return sa.exists(
        sa.select([sa.literal(1)])
        .select_from(document_access_settings_private_table)
        .where(
            sa.and_(
                document_access_settings_private_table.c.document_id == listing_table.c.document_id,
                document_access_settings_private_table.c.edrpou == user.company_edrpou,
            )
        )
    )


async def update_documents_date_delivered(
    conn: DBConnection,
    user: AuthUser | User | None,
    date_delivered: datetime.datetime | None,
    documents_ids: list[str],
) -> None:
    from app.documents.utils import get_documents_access_filters

    if not user or not user.company_edrpou:
        logger.warning(
            msg="Can't update date_delivered without user",
            extra={'user': user and user.to_dict()},
        )
        return

    filters = [
        document_recipients_table.c.date_sent.isnot(None),
        document_recipients_table.c.date_delivered.is_(None),
        document_recipients_table.c.edrpou == user.company_edrpou,
    ]

    if can_user_view_all_company_documents(user):
        # Check access to documents by company_edrpou (using company_listing_table)
        select_from_clause = document_recipients_table.join(
            company_listing_table,
            company_listing_table.c.document_id == document_recipients_table.c.document_id,
        )
        filters.append(company_listing_table.c.edrpou == user.company_edrpou)
    else:
        # Check access to documents by role_id (using listing_table)
        # TODO: need optimizations for that part of query
        select_from_clause = document_recipients_table.join(
            listing_table,
            listing_table.c.document_id == document_recipients_table.c.document_id,
        )
        filters.append(get_documents_access_filters(user))

    # Most of the time this list contains only one document_id
    filters.append(document_recipients_table.c.document_id.in_(documents_ids))

    # If some documents have date_delivered,
    # it will be filtered in _update_documents_date_delivered
    query = (
        sa.select([document_recipients_table.c.document_id.label('id')])
        .select_from(select_from_clause)
        .where(sa.and_(*filters))
    )

    async with services.db_readonly.acquire() as readonly_conn:
        documents = await select_all(readonly_conn, query)

    if not documents:
        return

    update_documents_ids = [document.id for document in documents]
    await _update_documents_date_delivered(
        conn=conn,
        company_edrpou=user.company_edrpou,
        documents_ids=update_documents_ids,
        date_delivered=date_delivered,
    )


async def create_document_access_for_recipient(
    conn: DBConnection,
    document_id: str,
    emails: list[str] | None,
    edrpou: str,
) -> None:
    """Create access to document for recipient by email and edrpous"""

    roles = []
    if emails:
        recipients = [{'edrpou': edrpou, 'email': email} for email in emails]
        roles = await select_role_by_emails_and_edrpous(conn=conn, data=recipients)

    listing_data = ListingDataAggregator()
    for role in roles:
        listing_data.add(
            document_id=document_id,
            access_edrpou=edrpou,
            role_id=role.id_,
            source=AccessSource.default,
        )

    # create listing for company, if one or more roles not in database
    if len(listing_data) == 0:
        listing_data.add(
            document_id=document_id,
            access_edrpou=edrpou,
            role_id=None,
            source=AccessSource.default,
        )

    await insert_listings(conn=conn, data=listing_data.as_db())


async def delete_recipients_access(
    conn: DBConnection,
    *,
    document_id: str,
    ignore_edrpou: str | None = None,
    companies_edrpous: list[str] | None = None,
) -> DeleteListingsCtx:
    """
    Delete access for all companies except current company
    WARN: must be used only by document owner

    IMPORTANT! When calling this function, you need manually process its result:
      1) send document to indexation
      2) remove documents from ES_Index by DeleteListingsCtx.company_listing_ids, like this
         await schedule_remove_company_listings_es(DeleteListingsCtx.company_listing_ids)
    """
    filters = [listing_table.c.document_id == document_id]

    if ignore_edrpou is not None:
        filters.append(listing_table.c.access_edrpou != ignore_edrpou)

    if companies_edrpous is not None:
        filters.append(listing_table.c.access_edrpou.in_(companies_edrpous))

    return await delete_listings(
        conn=conn,
        filters=filters,
    )


async def update_recipients_date_received_raw(
    conn: DBConnection,
    *,
    document_id: str,
    companies_edrpous: list[str],
    value: Any,
) -> None:
    """
    Update date_received for given companies in document_recipients table
    """
    if not companies_edrpous:
        return

    await conn.execute(
        document_recipients_table.update()
        .where(
            sa.and_(
                document_recipients_table.c.document_id == document_id,
                document_recipients_table.c.edrpou.in_(sorted(companies_edrpous)),
            )
        )
        .values(date_received=value)
    )


async def update_recipients_date_sent_raw(
    conn: DBConnection,
    *,
    document_id: str,
    companies_edrpous: list[str],
    value: Any,
) -> None:
    """
    Update date_sent for given companies in document_recipients table
    """
    if not companies_edrpous:
        return

    await conn.execute(
        document_recipients_table.update()
        .where(
            sa.and_(
                document_recipients_table.c.document_id == document_id,
                document_recipients_table.c.edrpou.in_(sorted(companies_edrpous)),
            )
        )
        .values(date_sent=value)
    )


async def update_recipients_unset_date_delivered(
    conn: DBConnection,
    *,
    document_id: str,
    company_edrpou: str,
    date_delivered: datetime.datetime,
) -> None:
    await conn.execute(
        document_recipients_table.update()
        .where(
            sa.and_(
                document_recipients_table.c.document_id == document_id,
                document_recipients_table.c.edrpou == company_edrpou,
                document_recipients_table.c.date_sent.isnot(None),
                document_recipients_table.c.date_delivered.is_(None),
            )
        )
        .values(date_delivered=date_delivered)
    )


async def update_recipients_unset_date_received(
    conn: DBConnection,
    *,
    document_id: str,
    company_edrpou: str,
    date_received: datetime.datetime,
) -> None:
    await conn.execute(
        document_recipients_table.update()
        .where(
            sa.and_(
                document_recipients_table.c.document_id == document_id,
                document_recipients_table.c.edrpou == company_edrpou,
                document_recipients_table.c.date_sent.isnot(None),
                document_recipients_table.c.date_received.is_(None),
            )
        )
        .values(date_received=date_received)
    )


async def update_legacy_document_recipient(
    conn: DBConnection,
    document_id: str,
    edrpou: str,
    emails: list[str],
) -> None:
    # The old column "email_recipient" in "documents" table store multiple emails in single
    # text field, so we need to join them with comma for backward compatibility
    _legacy_email = join_comma_separated_emails(emails)

    await conn.execute(
        document_table.update()
        .where(document_table.c.id == document_id)
        .values(
            {
                'edrpou_recipient': edrpou,
                'email_recipient': _legacy_email,
                'date_updated': sa.text('now()'),
            }
        )
    )


async def update_legacy_document_recipient_email(
    conn: DBConnection,
    *,
    document_id: str,
    emails: list[str],
) -> None:
    # The old column "email_recipient" in "documents" table store multiple emails in single
    # text field, so we need to join them with comma for backward compatibility
    _legacy_email = join_comma_separated_emails(emails)
    await select_one(
        conn=conn,
        query=(
            document_table.update()
            .where(document_table.c.id == document_id)
            .values({'email_recipient': _legacy_email, 'date_updated': sa.text('now()')})
        ),
    )


async def replace_bilateral_recipients(
    conn: DBConnection,
    document_id: str,
    recipient_edrpou: str,
    recipient_emails: list[str],
    is_recipient_emails_hidden: bool,
    document_owner_edrpou: str,
    prev_recipients: list[DocumentRecipient],
) -> None:
    # INFO: Bilateral document has only two recipients, document owner and actual recipient

    prev_recipient = next((r for r in prev_recipients if r.edrpou == recipient_edrpou), None)
    recipient = DocumentRecipient(
        document_id=document_id,
        edrpou=recipient_edrpou,
        emails=recipient_emails,
        is_emails_hidden=is_recipient_emails_hidden,
        from_flow=False,
        external_meta=prev_recipient.external_meta if prev_recipient else None,
        # Copy attributes from previous recipient
        id=prev_recipient.id if prev_recipient else generate_uuid(),
        date_sent=prev_recipient.date_sent if prev_recipient else None,
        date_received=prev_recipient.date_received if prev_recipient else None,
        date_delivered=prev_recipient.date_delivered if prev_recipient else None,
        date_created=prev_recipient.date_created if prev_recipient else utc_now(),
        assigner_role_id=prev_recipient.assigner_role_id if prev_recipient else None,
    )

    recipients = [recipient.as_db()]
    if recipient_edrpou != document_owner_edrpou:
        prev_owner = next((r for r in prev_recipients if r.edrpou == document_owner_edrpou), None)
        owner = DocumentRecipient(
            document_id=document_id,
            edrpou=document_owner_edrpou,
            emails=[],
            is_emails_hidden=False,
            from_flow=False,
            external_meta=prev_owner.external_meta if prev_owner else None,
            id=prev_owner.id if prev_owner else generate_uuid(),
            date_sent=prev_owner.date_sent if prev_owner else None,
            date_received=prev_owner.date_received if prev_owner else None,
            date_delivered=prev_owner.date_delivered if prev_owner else None,
            date_created=prev_owner.date_created if prev_owner else utc_now(),
            assigner_role_id=prev_owner.assigner_role_id if prev_owner else None,
        )
        recipients.append(owner.as_db())
    else:
        # TODO: check if someone uses this functional and if not remove this logic.
        logger.warning(
            msg='Set own company as bilateral recipient',
            extra={
                'recipient_edrpou': recipient_edrpou,
                'emails': recipient_emails,
                'document_owner_edrpou': document_owner_edrpou,
            },
        )

    # Replace recipient in document recipient table by removing all previous recipients
    # and inserting new ones
    await delete_recipients(conn=conn, document_id=document_id)
    await insert_recipients(conn, recipients=recipients)

    await update_legacy_document_recipient(
        conn=conn,
        document_id=document_id,
        edrpou=recipient_edrpou,
        emails=recipient_emails,
    )


async def select_documents_by_ids_with_company_info(
    conn: DBConnection, document_ids: StrList, selectable: list[Any] | None = None
) -> list[DBRow]:
    """Select documents by IDs."""

    return await select_all(
        conn,
        (
            sa.select(selectable or document_with_uploader_fields)
            .select_from(document_with_uploader_join)
            .where(document_table.c.id.in_(document_ids))
        ),
    )


async def select_document_uploader_info(
    conn: DBConnection,
    *,
    document_id: str,
) -> UploaderInfo | None:
    """
    Select uploader's email for document
    """

    row = await select_one(
        conn,
        (
            sa.select([user_table.c.email, role_table.c.id.label('role_id')])
            .select_from(
                document_table.outerjoin(
                    role_table,
                    role_table.c.id == document_table.c.uploaded_by,
                ).outerjoin(
                    user_table,
                    user_table.c.id == role_table.c.user_id,
                )
            )
            .where(document_table.c.id == document_id)
        ),
    )

    return UploaderInfo.from_row(row) if row else None


async def add_documents_links(conn: DBConnection, data: list[DataDict]) -> None:
    if not data:
        return
    await conn.execute(document_link_table.insert().values(data))


async def add_document_child(
    conn: DBConnection,
    *,
    company_edrpou: str,
    parent_id: str,
    child_id: str,
) -> None:
    data = {
        'parent_id': parent_id,
        'child_id': child_id,
        'company_edrpou': company_edrpou,
    }
    await add_documents_links(conn, [data])


async def delete_documents_links(conn: DBConnection, data: list[DataDict]) -> None:
    if not data:
        return

    filters = [
        sa.and_(
            document_link_table.c.company_edrpou == link['company_edrpou'],
            document_link_table.c.parent_id == link['parent_id'],
            document_link_table.c.child_id == link['child_id'],
        )
        for link in data
    ]
    await conn.execute(document_link_table.delete().where(sa.or_(*filters)))


async def delete_document_child(
    conn: DBConnection,
    *,
    company_edrpou: str,
    parent_id: str,
    child_id: str,
) -> None:
    data = {
        'parent_id': parent_id,
        'child_id': child_id,
        'company_edrpou': company_edrpou,
    }
    await delete_documents_links(conn, [data])


async def select_documents_link(
    conn: DBConnection,
    *,
    company_edrpou: str,
    parent_id: str,
    child_id: str,
) -> DBRow | None:
    return await select_one(
        conn=conn,
        query=(
            sa.select([document_link_table]).where(
                sa.and_(
                    document_link_table.c.company_edrpou == company_edrpou,
                    document_link_table.c.parent_id == parent_id,
                    document_link_table.c.child_id == child_id,
                )
            )
        ),
    )


async def select_parents_documents_links(
    conn: DBConnection,
    *,
    user: User,
    documents_ids: set[str],
) -> list[DBRow]:
    """
    Select parent documents for the given documents.

    Only documents that user has access to will be returned
    """
    from app.documents.utils import get_documents_access_filters

    parents_query = (
        sa.select([document_link_table.c.parent_id, document_link_table.c.child_id])
        .select_from(
            document_link_table.join(
                listing_table,
                listing_table.c.document_id == document_link_table.c.parent_id,
            )
        )
        .where(
            sa.and_(
                document_link_table.c.child_id.in_(documents_ids),
                # Sometimes user just doesn't have access to the parent document
                get_documents_access_filters(user),
            )
        )
        .order_by(document_link_table.c.date_created.asc())
    )
    return await select_all(conn, parents_query)


async def select_children_documents_links(
    conn: DBConnection,
    *,
    user: User,
    documents_ids: set[str],
) -> list[DBRow]:
    """
    Select children documents for the given documents.
    """
    from app.documents.utils import get_documents_access_filters

    children_query = (
        sa.select([document_link_table.c.parent_id, document_link_table.c.child_id])
        .select_from(
            document_link_table.join(
                listing_table,
                listing_table.c.document_id == document_link_table.c.child_id,
            )
        )
        .where(
            sa.and_(
                document_link_table.c.parent_id.in_(documents_ids),
                get_documents_access_filters(user),
            )
        )
        .order_by(document_link_table.c.date_created.asc())
    )
    return await select_all(conn, children_query)


async def select_documents_links_simple(
    conn: DBConnection,
    *,
    company_edrpou: str,
    documents_ids: set[str],
) -> list[DBRow]:
    query = (
        sa.select(
            [
                document_link_table.c.parent_id,
                document_link_table.c.child_id,
            ]
        )
        .select_from(document_link_table)
        .where(
            sa.and_(
                sa.or_(
                    document_link_table.c.child_id.in_(documents_ids),
                    document_link_table.c.parent_id.in_(documents_ids),
                ),
                document_link_table.c.company_edrpou == company_edrpou,
            )
        )
    )

    return await select_all(conn, query)


async def exist_linked_documents(
    conn: DBConnection,
    *,
    document_id: str,
    company_edrpou: str,
) -> bool:
    """
    Check if a document is linked to another document as a child or as a parent
    """
    return await exists(
        conn=conn,
        select_from=document_link_table,
        clause=sa.and_(
            document_link_table.c.company_edrpou == company_edrpou,
            sa.or_(
                document_link_table.c.child_id == document_id,
                document_link_table.c.parent_id == document_id,
            ),
        ),
    )


async def select_parents_documents_by_ids(
    conn: DBConnection,
    documents_ids: list[str],
) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    document_link_table.c.parent_id,
                    document_link_table.c.child_id,
                ]
            ).where(document_link_table.c.child_id.in_(documents_ids))
        ),
    )


def _split_recipient_email(email: str) -> list[str]:
    """
    Sometimes we might forget to split emails by comma or semicolon, and try to insert them
    to document recipients table. This function is used as a workaround to detect such cases
    and avoid inserting invalid data to the database.
    """
    if not email:
        return []

    emails = split_comma_separated_emails(email)

    if len(emails) > 1:
        logger.info(
            msg='Old format of emails in document_recipients table',
            extra={'emails': email},
            exc_info=True,  # to see where it was called
        )

    return emails


def _prepare_recipients_emails(
    data: list[UpsertDocumentRecipientDict],
) -> list[UpsertDocumentRecipientDict]:
    """
    Normalize data for inserting into document_recipients table
    """

    for item in data:
        new_emails: list[str] | None
        raw_emails: list[str] | str | None = item['emails']
        if not raw_emails:
            new_emails = None
        elif isinstance(raw_emails, str):
            new_emails = _split_recipient_email(raw_emails)
        elif isinstance(raw_emails, list):
            new_emails = []
            for raw_email in raw_emails:
                new_emails.extend(_split_recipient_email(raw_email))
        else:
            raise ValueError(f'Invalid emails format for recipient {item["edrpou"]}: {raw_emails}')

        # We see in a database that in some cases, we might accept emails in an invalid format.
        # To find and fix all such cases, for now, we are logging invalid emails without taking
        # any action.
        if new_emails and any(not is_valid_email(email) for email in new_emails):
            logger.info(
                msg='Invalid email format for recipient',
                exc_info=True,
                extra={'raw_email': raw_emails, 'new_email': new_emails},
            )

        # Convert an empty list to None
        item['emails'] = new_emails if new_emails else None

    return data


async def insert_recipients(
    conn: DBConnection,
    *,
    recipients: list[UpsertDocumentRecipientDict] | None = None,
    recipient: UpsertDocumentRecipientDict | None = None,
    with_return: bool = False,
) -> list[DocumentRecipient]:
    """
    Insert documents recipients and related objects in database.
    """
    _recipients: list[UpsertDocumentRecipientDict]
    if recipients is not None:
        _recipients = recipients
    elif recipient is not None:
        _recipients = [recipient]
    else:
        raise ValueError('Either recipients or recipient must be provided')

    if not _recipients:
        return []

    _recipients = _prepare_recipients_emails(_recipients)

    async with conn.begin():
        inserted_recipients = await upsert_recipients_raw(
            conn=conn,
            recipients=_recipients,
            with_return=with_return,
        )
        await upsert_latest_document_recipients(conn, recipients=_recipients)

    return inserted_recipients


async def upsert_recipients_raw(
    conn: DBConnection,
    *,
    recipients: list[UpsertDocumentRecipientDict],
    with_return: bool,
) -> list[DocumentRecipient]:
    """
    Insert or update recipients in document_recipients table

    NOTE: prefer to use "upsert_recipients" function instead of this one because it has more
    additional business logic
    """

    query = (
        postgresql.insert(document_recipients_table)
        .values(recipients)
        .on_conflict_do_update(
            index_elements=[
                document_recipients_table.c.document_id,
                document_recipients_table.c.edrpou,
            ],
            index_where=document_recipients_table.c.from_flow.is_(False),
            set_={
                'date_updated': sa.text('now()'),
                # NOTE: EXCLUDED table is used to reference values originally
                # proposed for insertion
                'edrpou': sa.text('EXCLUDED.edrpou'),
                'emails': sa.text('EXCLUDED.emails'),
                'from_flow': sa.text('EXCLUDED.from_flow'),
                'is_emails_hidden': sa.text('EXCLUDED.is_emails_hidden'),
            },
        )
    )

    if with_return:
        query = query.returning(document_recipients_table)
        rows = await select_all(conn, query)
        return [DocumentRecipient.from_row(row) for row in rows]

    await conn.execute(query)
    return []


async def upsert_latest_document_recipients(
    conn: DBConnection,
    recipients: list[UpsertDocumentRecipientDict],
) -> None:
    """
    Upsert latest document recipients
    """

    recipients = [r for r in recipients if r.get('is_emails_hidden') is not True and r['emails']]
    if not recipients:
        return

    documents_ids: set[str] = {r['document_id'] for r in recipients}
    document_rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_table.c.id, document_table.c.edrpou_owner])
            .select_from(document_table)
            .where(document_table.c.id.in_(documents_ids))
        ),
    )
    documents_owners: dict[str, str] = {d.id: d.edrpou_owner for d in document_rows}

    items: list[DataDict] = []
    seen_keys: set[tuple[str, str]] = set()
    for recipient in recipients:
        recipient_edrpou = recipient['edrpou']
        recipient_emails = recipient['emails']
        owner_edrpou = documents_owners.get(recipient['document_id'])
        if (
            (not owner_edrpou)
            or (not recipient_edrpou)
            or (not recipient_emails)
            or (recipient_edrpou == owner_edrpou)
        ):
            continue

        # ON CONFLICT DO UPDATE will not work if we have duplicated index elements in data
        seen_key = (recipient_edrpou, owner_edrpou)
        if seen_key in seen_keys:
            continue

        seen_keys.add(seen_key)

        item = {
            'owner_edrpou': owner_edrpou,
            'recipient_edrpou': recipient_edrpou,
            'recipient_emails': sorted(recipient_emails),
        }
        items.append(item)

    if not items:
        return

    # Sort items to avoid deadlocks
    items = sorted(items, key=lambda x: (x['owner_edrpou'], x['recipient_edrpou']))

    query = (
        postgresql.insert(latest_document_recipients_table)
        .values(items)
        .on_conflict_do_update(
            index_elements=[
                latest_document_recipients_table.c.owner_edrpou,
                latest_document_recipients_table.c.recipient_edrpou,
            ],
            set_={
                'recipient_emails': sa.text('EXCLUDED.recipient_emails'),
                'date_updated': sa.text('now()'),
            },
            # If recipient_emails are the same, do not update, just skip it
            where=(
                latest_document_recipients_table.c.recipient_emails
                != sa.text('EXCLUDED.recipient_emails')
            ),
        )
    )
    await conn.execute(query)


async def delete_recipients(
    conn: DBConnection,
    *,
    document_id: str,
    ignore_edrpou: str | None = None,
    edrpous: list[str] | None = None,
) -> None:
    """Remove all recipients for given document"""
    filters = [document_recipients_table.c.document_id == document_id]

    if ignore_edrpou:
        filters.append(document_recipients_table.c.edrpou != ignore_edrpou)
    if edrpous is not None:
        filters.append(document_recipients_table.c.edrpou.in_(edrpous))

    await conn.execute(document_recipients_table.delete().where(sa.and_(*filters)))


async def select_documents_recipients(
    conn: DBConnection,
    documents_ids: list[str],
    *,
    with_names: bool = False,
) -> list[DBRow]:
    if not documents_ids:
        return []

    selectable = [document_recipients_table]
    from_tables = document_recipients_table
    if with_names:
        selectable.append(company_table.c.name)
        from_tables = from_tables.outerjoin(
            company_table, company_table.c.edrpou == document_recipients_table.c.edrpou
        )

    return await select_all(
        conn,
        sa.select(selectable)
        .select_from(from_tables)
        .where(document_recipients_table.c.document_id.in_(documents_ids)),
    )


async def select_documents_recipients_by_ids(
    conn: DBConnection, recipients_ids: list[str]
) -> list[DBRow]:
    if not recipients_ids:
        return []
    return await select_all(
        conn,
        sa.select([document_recipients_table]).where(
            document_recipients_table.c.id.in_(recipients_ids)
        ),
    )


async def select_document_recipients(
    conn: DBConnection,
    *,
    document_id: str | None = None,
    documents_ids: list[str] | None = None,
    company_edrpou: str | None = None,
    recipients_ids: list[str] | None = None,
    recipient_id: str | None = None,
    ignore_edrpou: str | None = None,
) -> list[DocumentRecipient]:
    """
    Select recipients for given filters.
    """

    filters = []
    if document_id is not None:
        filters.append(document_recipients_table.c.document_id == document_id)
    if documents_ids is not None:
        filters.append(document_recipients_table.c.document_id.in_(documents_ids))
    if company_edrpou is not None:
        filters.append(document_recipients_table.c.edrpou == company_edrpou)
    if recipients_ids is not None:
        filters.append(document_recipients_table.c.id.in_(recipients_ids))
    if recipient_id is not None:
        filters.append(document_recipients_table.c.id == recipient_id)
    if ignore_edrpou is not None:
        filters.append(document_recipients_table.c.edrpou != ignore_edrpou)

    assert filters, 'At least one filter must be provided'

    rows = await select_all(
        conn=conn,
        query=(sa.select([document_recipients_table]).where(sa.and_(*filters))),
    )
    return [DocumentRecipient.from_row(row) for row in rows]


async def select_document_recipient(
    conn: DBConnection,
    *,
    document_id: str | None = None,
    edrpou: str | None = None,
    recipient_id: str | None = None,
) -> DocumentRecipient | None:
    """Select recipient for given document and edrpou."""
    recipients = await select_document_recipients(
        conn=conn,
        document_id=document_id,
        company_edrpou=edrpou,
        recipient_id=recipient_id,
    )
    return recipients[0] if recipients else None


async def insert_delete_requests(conn: DBConnection, data: list[dict[Any, Any]]) -> list[DBRow]:
    assert data
    query = (
        postgresql.insert(delete_request_table)
        .values(data)
        .returning(
            delete_request_table.c.id,
            delete_request_table.c.recipients_emails,
            delete_request_table.c.message,
            delete_request_table.c.document_id,
            delete_request_table.c.initiator_role_id,
            delete_request_table.c.reject_message,
            delete_request_table.c.receiver_edrpou,
            delete_request_table.c.initiator_edrpou,
            delete_request_table.c.status,
            delete_request_table.c.date_created,
            delete_request_table.c.date_accepted,
            delete_request_table.c.date_rejected,
            delete_request_table.c.seqnum,
        )
    )
    return await select_all(conn, query)


async def insert_delete_request(conn: DBConnection, data: dict[Any, Any]) -> DBRow:
    assert data
    result = await insert_delete_requests(conn, [data])
    return list(result)[0]


async def cancel_delete_requests(
    conn: DBConnection,
    delete_request_ids: list[str],
) -> None:
    await conn.execute(
        delete_request_table.update()
        .values({'status': DeleteRequestStatus.cancelled})
        .where(delete_request_table.c.id.in_(delete_request_ids))
    )


async def select_delete_requests_by(
    conn: DBConnection,
    *,
    request_id: str | None = None,
    document_id: str | None = None,
    documents_ids: list[str] | None = None,
    initiator_role_id: str | None = None,
    receiver_edrpou: str | None = None,
    recipient_email: str | None = None,
    status: DeleteRequestStatus | None = None,
) -> list[DBRow]:
    filters = []
    if request_id:
        filters.append(delete_request_table.c.id == request_id)
    if document_id:
        filters.append(delete_request_table.c.document_id == document_id)
    if documents_ids:
        filters.append(delete_request_table.c.document_id.in_(documents_ids))
    if receiver_edrpou:
        filters.append(delete_request_table.c.receiver_edrpou == receiver_edrpou)
    if initiator_role_id:
        filters.append(delete_request_table.c.initiator_role_id == initiator_role_id)
    if recipient_email:
        filters.append(delete_request_table.c.recipients_emails.any(recipient_email))
    if status:
        filters.append(delete_request_table.c.status == status)
    if not filters:
        return []
    return await select_all(conn, delete_request_table.select().where(sa.and_(*filters)))


async def get_documents_pending_delete_requests_mapping(
    conn: DBConnection,
    documents_ids: list[str],
) -> dict[str, bool]:
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([delete_request_table.c.document_id]).where(
                sa.and_(
                    delete_request_table.c.document_id.in_(documents_ids),
                    delete_request_table.c.status == DeleteRequestStatus.new,
                ),
            )
        ),
    )
    has_pending_delete_requests = {row.document_id for row in rows}
    return {
        document_id: document_id in has_pending_delete_requests for document_id in documents_ids
    }


async def select_delete_requests_by_ids(
    conn: DBConnection, delete_request_ids: list[str]
) -> list[DBRow]:
    return await select_all(
        conn,
        sa.select([delete_request_table]).where(delete_request_table.c.id.in_(delete_request_ids)),
    )


async def select_delete_requests_by_docs_ids(
    conn: DBConnection,
    documents_ids: Iterable[str],
) -> list[DBRow]:
    return await select_all(
        conn,
        sa.select([delete_request_table]).where(
            delete_request_table.c.document_id.in_(documents_ids)
        ),
    )


async def select_delete_requests_by_user(
    *,
    conn: DBConnection,
    user: User,
    statuses: list[DeleteRequestStatus],
    cursor: str | None = None,
    ids: list[str] | None = None,
    with_outgoing: bool = True,
) -> list[DBRow]:
    from app.documents.utils import get_documents_access_filters

    filters = [delete_request_table.c.status.in_(statuses)] if statuses else []

    if has_permission(user, {'can_delete_document_extended'}):
        # Select data based only on receiver/initiator edrpou from delete_request_table
        filters.append(
            sa.or_(
                delete_request_table.c.receiver_edrpou == user.company_edrpou,
                delete_request_table.c.initiator_edrpou == user.company_edrpou,
            )
        )
    else:
        # For non admin users select data based on access data from listing_table.
        # If no data available in listing_table (document was deleted),
        # select delete requests based only on information about initiator_role_id
        listing_exists_filter = (
            sa.exists()
            .select_from(listing_table)
            .where(
                sa.and_(
                    listing_table.c.document_id == delete_request_table.c.document_id,
                    get_documents_access_filters(user),
                )
            )
        )
        filters.append(
            sa.or_(
                listing_exists_filter,
                delete_request_table.c.initiator_role_id == user.role_id,
            )
        )

    if cursor and cursor.isnumeric():
        filters.append(delete_request_table.c.seqnum < int(cursor))

    if ids:
        filters.append(delete_request_table.c.id.in_(ids))

    if with_outgoing is False:
        filters.append(delete_request_table.c.receiver_edrpou == user.company_edrpou)

    query = (
        sa.select([delete_request_table])
        .select_from(delete_request_table)
        .where(sa.and_(*filters))
        .order_by(delete_request_table.c.seqnum.desc())
        .limit(DEFAULT_GET_DELETE_REQUESTS_LIMIT)
    )
    return await select_all(conn, query)


async def update_delete_requests(
    conn: DBConnection, ids: list[str], data: dict[str, Any]
) -> list[DBRow]:
    if not ids or not data:
        return []
    return await select_all(
        conn,
        (
            delete_request_table.update()
            .values(data)
            .where(delete_request_table.c.id.in_(ids))
            .returning(delete_request_table)
        ),
    )


async def select_bilateral_document_recipient(
    conn: DBConnection,
    *,
    document_id: str,
    document_owner_edrpou: str,
) -> DocumentRecipient | None:
    row = await select_one(
        conn,
        (
            sa.select([document_recipients_table]).where(
                sa.and_(
                    document_recipients_table.c.document_id == document_id,
                    document_recipients_table.c.edrpou != document_owner_edrpou,
                    document_recipients_table.c.from_flow.is_(False),
                )
            )
        ),
    )

    return DocumentRecipient.from_row(row) if row else None


async def select_roles_by_email_and_doc_receiver_edrpou(
    conn: DBConnection,
    emails: set[str],
    edrpous: set[str],
) -> list[DBRow]:
    if not emails or not edrpous:
        return []
    result = await select_all(
        conn,
        sa.select(
            [
                role_table.c.id.label('role_id'),
                role_table.c.company_id.label('company_id'),
                company_table.c.edrpou.label('edrpou'),
                user_table.c.email.label('email'),
            ]
        )
        .select_from(
            role_table.join(company_table, company_table.c.id == role_table.c.company_id).join(
                user_table, user_table.c.id == role_table.c.user_id
            )
        )
        .where(
            sa.and_(
                company_table.c.edrpou.in_(edrpous),
                user_table.c.email.in_(emails),
            )
        ),
    )
    return result


async def select_document_listings(
    conn: DBConnection,
    document_id: str,
    edrpou: str,
    *,
    clause: ClauseElement | None = None,
) -> list[DBRow]:
    where_clause = sa.and_(
        listing_table.c.access_edrpou == edrpou,
        listing_table.c.document_id == document_id,
    )
    if clause is not None:
        where_clause = sa.and_(where_clause, clause)

    return await select_all(conn, (listing_table.select().where(where_clause)))


async def select_document_listing_by_roles(
    conn: DBConnection,
    document_id: str,
    roles_ids: list[str],
) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            sa.select([listing_table]).where(
                sa.and_(
                    listing_table.c.role_id.in_(roles_ids),
                    listing_table.c.document_id == document_id,
                )
            )
        ),
    )


async def select_last_recipient_email(
    conn: DBConnection,
    owner_edrpou: str,
    recipients_edrpous: list[str],
) -> list[DBRow]:
    """
    Select last email used as recipient for given edrpous. Note that hidden
    recipients not selected from this query.
    """

    if not recipients_edrpous:
        return []

    if get_flag(FeatureFlags.USE_LATEST_RECIPIENTS_TABLE):
        return await select_all(
            conn=conn,
            query=(
                sa.select(
                    [
                        latest_document_recipients_table.c.recipient_edrpou.label('edrpou'),
                        # See table definition to see why we do type cast here
                        latest_document_recipients_table.c.recipient_emails.cast(
                            sa.ARRAY(sa.Text)
                        ).label('emails'),
                    ]
                )
                .select_from(latest_document_recipients_table)
                .where(
                    sa.and_(
                        latest_document_recipients_table.c.owner_edrpou == owner_edrpou,
                        latest_document_recipients_table.c.recipient_edrpou.in_(recipients_edrpous),
                    )
                )
            ),
        )

    return await select_all(
        conn,
        (
            sa.select(
                [
                    document_recipients_table.c.edrpou,
                    document_recipients_table.c.emails,
                ]
            )
            .select_from(document_recipients_join)
            .where(
                sa.and_(
                    document_table.c.edrpou_owner == owner_edrpou,
                    document_recipients_table.c.edrpou.in_(recipients_edrpous),
                    document_recipients_table.c.is_emails_hidden.is_(False),
                    document_recipients_table.c.emails.isnot(None),
                )
            )
            .order_by(
                document_recipients_table.c.edrpou,
                document_table.c.date_updated.desc(),
            )
            .distinct(document_recipients_table.c.edrpou)
        ),
    )


async def select_listings(conn: DBConnection, document_ids: Iterable[str]) -> list[ListingRow]:
    rows = await select_all(
        conn=conn,
        query=(listing_table.select().where(listing_table.c.document_id.in_(document_ids))),
    )
    return [ListingRow.from_row(row) for row in rows]


async def select_role_listings_with_tag_source(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    role_id: str,
    company_edrpou: str,
) -> set[str]:
    """
    Get documents ids that have already had a tag source in the listing table
    """
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([listing_table.c.document_id])
            .select_from(listing_table)
            .where(
                sa.and_(
                    listing_table.c.document_id.in_(documents_ids),
                    listing_table.c.role_id == role_id,
                    listing_table.c.access_edrpou == company_edrpou,
                    listing_table.c.sources.contains(AccessSource.tag),
                )
            )
        ),
    )
    return {row.document_id for row in rows}


async def select_document_listings_with_tag_source(
    conn: DBConnection,
    *,
    company_edrpou: str,
    document_id: str,
) -> list[str]:
    """
    Get listing IDs of the roles that contains a tag source for a given document
    """
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([listing_table.c.id])
            .select_from(listing_table)
            .where(
                sa.and_(
                    listing_table.c.access_edrpou == company_edrpou,
                    listing_table.c.document_id == document_id,
                    listing_table.c.role_id.isnot(None),
                    listing_table.c.sources.contains(AccessSource.tag),
                )
            )
        ),
    )
    return [row.id for row in rows]


async def select_listings_with_user_email(
    conn: DBConnection, document_ids: Iterable[str]
) -> list[DBRow]:
    return await select_all(
        conn,
        (
            sa.select(
                [
                    listing_table,
                    user_table.c.id.label('user_id'),
                    user_table.c.email.label('user_email'),
                ]
            )
            .select_from(
                listing_table.outerjoin(user_role_join, listing_table.c.role_id == role_table.c.id)
            )
            .where(listing_table.c.document_id.in_(document_ids))
        ),
    )


async def select_listings_by_source(
    conn: DBConnection,
    company_edrpou: str,
    source: AccessSource,
    *,
    document_id: str | None = None,
    roles_ids: list[str] | None = None,
    ignore_roles_ids: list[str] | None = None,
    user_emails: list[str] | None = None,
) -> list[ListingRow]:
    """Select listings for given documents that contains provided source"""
    filters = [
        listing_table.c.access_edrpou == company_edrpou,
        listing_table.c.sources.contains(source),
    ]
    if document_id is not None:
        filters.append(listing_table.c.document_id == document_id)
    if roles_ids is not None:
        filters.append(listing_table.c.role_id.in_(roles_ids))
    if ignore_roles_ids is not None:
        filters.append(~listing_table.c.role_id.in_(ignore_roles_ids))
    if user_emails:
        filters.append(
            sa.exists(
                sa.select([1])
                .select_from(user_role_join)
                .where(
                    sa.and_(
                        role_table.c.id == listing_table.c.role_id,
                        user_table.c.email.in_(user_emails),
                    )
                )
            )
        )

    rows = await select_all(conn, query=listing_table.select().where(sa.and_(*filters)))
    return [ListingRow.from_row(row) for row in rows]


async def select_listings_for_graph(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    company_edrpou: str,
    source: AccessSource,
) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    listing_table.c.id,
                    listing_table.c.document_id,
                    listing_table.c.date_created,
                    listing_table.c.role_id,
                ]
            )
            .where(
                sa.and_(
                    listing_table.c.document_id.in_(documents_ids),
                    listing_table.c.access_edrpou == company_edrpou,
                    listing_table.c.sources.contains(source),
                )
            )
            .order_by(listing_table.c.date_created.desc())
        ),
    )


async def count_document_children(
    conn: DBConnection, parent_id: str, *, company_edrpou: str
) -> int:
    return await conn.scalar(
        sa.select([sa.func.count()]).where(
            sa.and_(
                document_link_table.c.company_edrpou == company_edrpou,
                document_link_table.c.parent_id == parent_id,
            )
        )
    )


async def select_recipients_data(
    conn: DBConnection, document_id: str, edrpou: str, email: str | None = None
) -> list[DBRow]:
    filters = [
        document_recipients_table.c.document_id == document_id,
        document_recipients_table.c.edrpou == edrpou,
    ]
    if email:
        filters.append(
            document_recipients_table.c.emails.cast(postgresql.ARRAY(CIText)).contains(
                f'{{{email}}}'
            )
        )

    return await select_all(
        conn=conn, query=(document_recipients_table.select().where(sa.and_(*filters)))
    )


async def add_documents_meta(conn: DBConnection, data: list[DataDict]) -> None:
    if not data:
        return
    await conn.execute(postgresql.insert(document_meta_table).values(data).on_conflict_do_nothing())


async def select_document_meta(conn: DBConnection, document_id: str) -> DocumentMeta | None:
    row = await select_one(
        conn=conn,
        query=(
            document_meta_table.select().where(document_meta_table.c.document_id == document_id)
        ),
    )
    return DocumentMeta.from_row(row) if row else None


async def filter_visible_document_ids_for_user(
    conn: DBConnection,
    *,
    company_edrpou: str,
    document_ids: list[str],
    visibility_limit: int,
) -> set[str]:
    """
    Filters list of document ids by visibility limit, which is set in company billing config.
    """
    available_docs_list = (
        sa.select([company_listing_table.c.document_id])
        .where(company_listing_table.c.edrpou == company_edrpou)
        .order_by(
            company_listing_table.c.date_created.desc(),
            company_listing_table.c.document_num.desc(),
        )
        .limit(visibility_limit)
        .cte('available_docs_list')
    )
    rows = await select_all(
        conn,
        query=(
            sa.select([available_docs_list.c.document_id]).where(
                available_docs_list.c.document_id.in_(document_ids)
            )
        ),
    )
    return {row.document_id for row in rows}


async def select_documents_count_to_limit(
    conn: DBConnection,
    *,
    company_edrpou: str,
    limit: int,
) -> int:
    """
    Get the number of documents available for company, but not more than limit value.

    This function is used to check if a company reached the limit of available documents,
    so it doesn't need to count all documents.
    """
    available_docs_list = (
        sa.select([company_listing_table.c.document_id])
        .where(company_listing_table.c.edrpou == company_edrpou)
        .limit(limit)
        .cte('all_company_docs')
    )

    return await conn.scalar(sa.select([sa.func.count()]).select_from(available_docs_list))


async def select_documents_access_companies_edrpou(
    conn: DBConnection, document_ids: list[str]
) -> list[CompanyAccess]:
    rows = await select_all(
        conn,
        query=(
            sa.select(
                [
                    company_listing_table.c.id,
                    company_listing_table.c.document_id,
                    company_listing_table.c.edrpou,
                ]
            ).where(company_listing_table.c.document_id.in_(document_ids))
        ),
    )
    return [CompanyAccess.from_row(row) for row in rows]


async def exists_invalid_signed_company_docs(conn: DBConnection, edrpou: str) -> bool:
    select_from = document_table.join(
        listing_table, listing_table.c.document_id == document_table.c.id
    )
    clause = sa.and_(
        listing_table.c.access_edrpou == edrpou,
        document_table.c.is_invalid_signed.is_(True),
    )
    return await exists(conn, select_from=select_from, clause=clause)


async def update_email_recipient(
    conn: DBConnection, *, edrpou: str, email: str, edrpou_owner: str
) -> None:
    """Update document.email_recipient."""

    # Select document for updating: Document without recipient email or
    # all recipient emails is not valid.
    document_rows = await select_all(
        conn,
        (
            sa.select([document_table.c.id])
            .select_from(
                document_table.join(
                    document_recipients_table,
                    eq(document_recipients_table.c.document_id, document_table.c.id),
                ).outerjoin(
                    unnested_recipient_emails,
                    eq(unnested_recipient_emails.c.id, document_recipients_table.c.id),
                )
            )
            .where(
                sa.and_(
                    document_table.c.edrpou_owner == edrpou_owner,
                    document_recipients_table.c.edrpou == edrpou,
                )
            )
            .group_by(document_table.c.id, document_recipients_table.c.id)
            .having(
                sa.func.bool_or(
                    sa.or_(
                        unnested_recipient_emails.c.email.is_(None),
                        unnested_recipient_emails.c.email.notlike('%@%'),
                    )
                )
            )
        ),
    )
    document_ids = {row.id for row in document_rows}

    await select_all(
        conn,
        (
            document_table.update()
            .where(document_table.c.id.in_(document_ids))
            .values(email_recipient=email)
        ),
    )

    await insert_recipients(
        conn=conn,
        recipients=[
            UpsertDocumentRecipientDict(
                document_id=document_id,
                emails=[email] if email else None,
                edrpou=edrpou,
            )
            for document_id in set(document_ids)
        ],
    )


async def select_review_reminder_document_recipients(
    conn: DBConnection,
    *,
    documents_ids: list[str],
) -> list[ReviewReminderDocumentRecipient]:
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    document_recipients_table.c.document_id,
                    document_recipients_table.c.edrpou.label('company_edrpou'),
                    company_table.c.name.label('company_name'),
                ]
            )
            .select_from(
                document_recipients_table.outerjoin(
                    company_table,
                    company_table.c.edrpou == document_recipients_table.c.edrpou,
                )
            )
            .where(document_recipients_table.c.document_id.in_(documents_ids))
        ),
    )
    return [ReviewReminderDocumentRecipient.from_row(row) for row in rows]


async def select_delete_document_settings(
    conn: DBConnection,
    document_ids: list[str],
) -> list[DeleteDocumentSettings]:
    rows = await select_all(
        conn,
        (
            delete_document_settings_table.select().where(
                delete_document_settings_table.c.document_id.in_(document_ids)
            )
        ),
    )
    return [DeleteDocumentSettings.from_row(row) for row in rows]


async def delete_document_settings(
    conn: DBConnection,
    document_ids: list[str],
) -> list[str]:
    rows = await select_all(
        conn,
        delete_document_settings_table.delete()
        .where(delete_document_settings_table.c.document_id.in_(document_ids))
        .returning(delete_document_settings_table.c.document_id),
    )
    return [row.document_id for row in rows]


async def upsert_delete_document_settings(
    *,
    conn: DBConnection,
    document_ids: list[str],
    is_delete_request_required: bool | None = None,
) -> list[str]:
    data = {}
    if is_delete_request_required is not None:
        data['is_delete_request_required'] = is_delete_request_required
    assert data, 'At least one field must be provided'

    data_rows = [{**data, 'document_id': document_id} for document_id in document_ids]

    rows = await select_all(
        conn,
        (
            postgresql.insert(delete_document_settings_table)
            .values(data_rows)
            .returning(delete_document_settings_table.c.document_id)
            .on_conflict_do_update(
                index_elements=[delete_document_settings_table.c.document_id],
                set_=data,
            )
        ),
    )

    return [row.document_id for row in rows]


async def select_document_available_roles_for_graph(
    conn: DBConnection,
    *,
    user_id: str,
    document_id: str,
) -> list[str]:
    """
    Select user's roles that have access to the document
    """

    # TODO: add support for private documents?
    user_roles_cte = (
        sa.select(
            [
                role_table.c.id.label('role_id'),
                company_table.c.edrpou,
                sa.or_(
                    role_table.c.can_view_document.is_(True),
                    role_table.c.user_role == UserRole.admin.value,
                ).label('is_admin'),
            ]
        )
        .select_from(user_active_role_company_join)
        .where(user_table.c.id == user_id)
        .cte('user_roles')
    )

    # Admin has access to all documents in the company, so it's enough to check
    # only if there is any record in the listing table with that edrpou
    admin_condition = sa.and_(
        user_roles_cte.c.is_admin.is_(True),
        sa.exists(
            sa.select([listing_table.c.id])
            .select_from(listing_table)
            .where(
                sa.and_(
                    listing_table.c.document_id == document_id,
                    listing_table.c.access_edrpou == user_roles_cte.c.edrpou,
                )
            ),
        ),
    )

    # Non-admin user has access only to documents that they have access to
    user_condition = sa.and_(
        user_roles_cte.c.is_admin.is_(False),
        sa.exists(
            sa.select([listing_table.c.id])
            .select_from(listing_table)
            .where(
                sa.and_(
                    listing_table.c.document_id == document_id,
                    listing_table.c.role_id == user_roles_cte.c.role_id,
                )
            ),
        ),
    )

    roles = await select_all(
        conn=conn,
        query=(
            sa.select([user_roles_cte.c.role_id])
            .select_from(user_roles_cte)
            .where(sa.or_(admin_condition, user_condition))
        ),
    )
    return [row.role_id for row in roles]


async def select_document_access_level(
    conn: DBConnection,
    *,
    document_id: str,
    edrpou: str,
) -> DocumentAccessLevel:
    mapping = await select_documents_access_level(
        conn=conn,
        documents_ids=[document_id],
        edrpou=edrpou,
    )
    return mapping[document_id]


async def select_documents_access_level(
    conn: DBConnection,
    documents_ids: list[str],
    edrpou: str,
) -> dict[str, DocumentAccessLevel]:
    """
    Select document access level for given documents ids and edrpou
    """
    if not documents_ids:
        return {}

    is_private_filter = sa.exists(
        sa.select([1])
        .select_from(document_access_settings_private_table)
        .where(
            sa.and_(
                document_access_settings_private_table.c.document_id == document_table.c.id,
                document_access_settings_private_table.c.edrpou == edrpou,
            )
        )
    )

    private_documents = await select_all(
        conn=conn,
        query=(
            sa.select([document_table.c.id])
            .select_from(document_table)
            .where(sa.and_(document_table.c.id.in_(documents_ids), is_private_filter))
        ),
    )

    private_documents_ids = {row.id for row in private_documents}

    mapping: dict[str, DocumentAccessLevel] = {}
    for document_id in documents_ids:
        if document_id in private_documents_ids:
            mapping[document_id] = DocumentAccessLevel.private
        else:
            mapping[document_id] = DocumentAccessLevel.extended
    return mapping


async def select_private_documents_for_indexation(
    conn: DBConnection,
    *,
    documents_ids: list[str],
) -> list[PrivateDocumentIndexationItem]:
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    document_access_settings_private_table.c.document_id,
                    document_access_settings_private_table.c.edrpou,
                ]
            )
            .select_from(document_access_settings_private_table)
            .where(document_access_settings_private_table.c.document_id.in_(documents_ids))
        ),
    )
    return [PrivateDocumentIndexationItem.from_row(row) for row in rows]


async def select_private_documents_ids(
    conn: DBConnection,
    *,
    documents_ids: Iterable[str],
    company_edrpou: str,
) -> set[str]:
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_access_settings_private_table.c.document_id])
            .select_from(document_access_settings_private_table)
            .where(
                sa.and_(
                    document_access_settings_private_table.c.edrpou == company_edrpou,
                    document_access_settings_private_table.c.document_id.in_(documents_ids),
                )
            )
        ),
    )
    return {row.document_id for row in rows}


async def insert_document_access_settings_private(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    edrpou: str,
) -> None:
    """
    Mark that document has "private" access level
    """
    if not documents_ids:
        return

    data = [{'document_id': document_id, 'edrpou': edrpou} for document_id in documents_ids]
    await conn.execute(
        postgresql.insert(document_access_settings_private_table)
        .values(data)
        # When duplicate occurs, do nothing because result will be the same
        .on_conflict_do_nothing()
    )


async def delete_document_access_settings_private(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    edrpou: str,
) -> None:
    """
    Remove row that marks the document as "private" to make it have "extended" access level
    """
    await conn.execute(
        document_access_settings_private_table.delete().where(
            sa.and_(
                document_access_settings_private_table.c.document_id.in_(documents_ids),
                document_access_settings_private_table.c.edrpou == edrpou,
            )
        ),
    )
