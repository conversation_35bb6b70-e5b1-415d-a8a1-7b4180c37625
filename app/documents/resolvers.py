import logging

from hiku.engine import (
    Context,
    pass_context,
)
from hiku.graph import Nothing

from api.graph.constants import (
    DB_ENGINE_KEY,
    DB_READONLY_KEY,
    ES_KEY,
)
from api.graph.context import get_graph_context_dict__document_date_listing
from api.graph.exceptions import GraphQLError, GraphQLValidationError
from api.graph.types import FieldList
from api.graph.utils import get_base_graph_user, get_graph_user, get_raw_graph_user
from app.archive.utils import is_documents_archived
from app.auth.types import AuthUser, User, is_wide_user_type
from app.documents import db, utils
from app.documents.db import (
    select_delete_document_settings,
    select_document_available_roles_for_graph,
    select_invalid_signed_documents_by_ids,
    select_missing_documents_ids,
)
from app.documents.enums import DocumentAccessLevel
from app.documents.types import DocumentAccessGraph
from app.documents.utils import (
    filter_documents_by_billing_config_restrictions,
    update_documents_date_delivered_job,
)
from app.documents.validators import ResolveDocumentAccessesSchema
from app.es.enums import ESQuerySource
from app.es.fields import parse_es_date_field
from app.es.utils import RawESSearchResult, build_listing_query, fetch_es
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import tracking, validators
from app.lib.database import DBConnection
from app.lib.datetime_utils import utc_now
from app.lib.helpers import group_list
from app.lib.types import DataDict, StrList
from app.services import services
from app.sign_sessions.enums import SignSessionDocumentStatus
from app.signatures.db import select_signatures_by_documents_ids
from app.signatures.enums import SignatureAlgo
from app.signatures.tables import signature_table
from worker import topics

logger = logging.getLogger(__name__)

# Maximum number of documents that can be returned by Elasticsearch in a single query
ELASTIC_MAX_SEARCH_WINDOW_LIMIT = 10_000


class DocumentNotFoundError(GraphQLError):
    code = 'DOCUMENT_NOT_FOUND'
    message = _('Документ не знайдено')


class DocumentFoundForOtherRoleError(GraphQLError):
    code = 'DOCUMENT_FOUND_FOR_OTHER_ROLE'
    message = _('Документ доступний для користувача в іншій компанії')


@pass_context
async def resolve_is_viewable(
    ctx: Context,
    _: FieldList,
    documents_ids: StrList,
) -> list[list[bool]]:
    """
    Resolve boolean value if the document is viewable by the current user's company
    based on the billing limits
    """
    with tracking.get_available_documents.time():
        user = get_raw_graph_user(ctx)

        # Unauthenticated users and base users are not expected to have access to documents
        if not user or not is_wide_user_type(user):
            return [[False] for _ in documents_ids]

        # In sign sessions we do not check billing limits and just return True
        if isinstance(user, AuthUser):
            return [[True] for _ in documents_ids]

        async with ctx[DB_ENGINE_KEY].acquire() as conn:
            billing_limits_ctx = await filter_documents_by_billing_config_restrictions(
                conn=conn,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                document_ids=documents_ids,
            )
            if billing_limits_ctx is None:
                return [[True] for _ in documents_ids]

            # We should include documents with invalid signatures, even if they are not available
            # due to billing limits. This is needed because the user is not responsible for the
            # invalid signatures in our system, and we should give me a chance to re-sign the
            # document without buying a new rate.
            invalid_signed_doc_ids = await select_invalid_signed_documents_by_ids(
                conn=conn,
                ids=documents_ids,
            )

        # Do not send async jobs for company more than once per hour
        key = f'triggers:{user.company_id}:document_view_limit_trigger'
        ttl = 3600  # 1 hour in seconds
        event_triggered = await services.redis.get(key)
        if not event_triggered:
            await services.kafka.send_record(
                topic=topics.SEND_DOCUMENT_VIEW_LIMIT_NOTIFICATION,
                value={
                    'max_documents_count': billing_limits_ctx.max_documents_count,
                    'company_id': user.company_id,
                    'company_edrpou': user.company_edrpou,
                },
            )
            await services.redis.setex(key, value=1, time=ttl)

        return [
            [
                document_id in billing_limits_ctx.documents_ids
                or document_id in invalid_signed_doc_ids
            ]
            for document_id in documents_ids
        ]


@pass_context
async def resolve_access_level(
    ctx: Context,
    _: FieldList,
    documents_ids: list[str],
) -> list[list[str]]:
    user = get_graph_user(ctx)
    if not user:
        return [[DocumentAccessLevel.extended.value] for _ in documents_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        mapping = await db.select_documents_access_level(
            conn=conn,
            documents_ids=documents_ids,
            edrpou=user.company_edrpou,
        )

    return [
        [mapping.get(document_id, DocumentAccessLevel.extended).value]
        for document_id in documents_ids
    ]


@pass_context
async def resolve_has_eu_signatures(ctx: Context, _: FieldList, ids: StrList) -> list[list[bool]]:
    """
    Returns True for every document_id
    if that document has signatures with algo = 'ECDSA'
    """

    result_map: DataDict = {}
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        doc_eu_signatures = await select_signatures_by_documents_ids(
            conn=conn,
            documents_ids=ids,
            selectable=[
                signature_table.c.id,
                signature_table.c.document_id,
            ],
            algo=SignatureAlgo.ECDSA,
        )
        for row in doc_eu_signatures:
            result_map[row.document_id] = True

    return [[result_map.get(doc_id, False)] for doc_id in ids]


@pass_context
async def resolve_document_accesses(
    ctx: Context,
    documents_ids: StrList,
    raw_options: DataDict,
) -> list[list[DocumentAccessGraph]]:
    """
    Returns list of document access types for each document_id
    """

    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in documents_ids]

    options = validators.validate_pydantic(ResolveDocumentAccessesSchema, raw_options)

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        rows = await db.select_listings_for_graph(
            conn=conn,
            company_edrpou=user.company_edrpou,
            documents_ids=documents_ids,
            source=options.source,
        )

    mapping = group_list(rows, lambda r: r.document_id)
    return [
        [
            DocumentAccessGraph(id=row.id, dateCreated=row.date_created, roleId=row.role_id)
            for row in mapping[document_id]
        ]
        for document_id in documents_ids
    ]


@pass_context
async def resolve_is_delete_locked(
    ctx: Context,
    _: FieldList,
    ids: StrList,
) -> list[list[bool]]:
    async with ctx[DB_READONLY_KEY].acquire() as conn:
        settings = await select_delete_document_settings(
            conn=conn,
            document_ids=ids,
        )

    mapping = {setting.document_id: setting.is_delete_request_required for setting in settings}

    return [[mapping.get(doc_id, False)] for doc_id in ids]


@pass_context
async def resolve_is_archived(
    ctx: Context,
    _: FieldList,
    ids: StrList,
) -> list[list[bool]]:
    user = get_graph_user(ctx)
    if not user:
        return [[False] for _ in ids]

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        archive = await is_documents_archived(
            conn=conn,
            document_ids=ids,
            company_id=user.company_id,
        )

    return [[archive.get(doc_id, False)] for doc_id in ids]


@pass_context
async def can_delete_document(ctx: Context, _: FieldList, ids: StrList) -> list[list[bool]]:
    """Check if document can be deleted by current user."""

    user = get_graph_user(ctx)
    if not user:
        return [[False] for _ in ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        result = await utils.can_delete_documents(
            conn=conn,
            documents_ids=ids,
            current_user=user,
        )

    return [[result[document_id]] for document_id in ids]


@pass_context
async def resolve_document_available_roles(ctx: Context, options: DataDict) -> list[str]:
    """
    Get a list of current user's roles that have access to the document

    This query is used to offer the user to change a company if they don't have access to
    the document in the current company, but have access to another company
    """
    user = get_base_graph_user(ctx)
    if not user:
        return []

    document_id: str = options['documentId']

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        roles_ids = await db.select_document_available_roles_for_graph(
            conn=conn,
            user_id=user.id,
            document_id=document_id,
        )

    return roles_ids


async def schedule_jobs_on_resolve_document(
    *,
    document_id: str,
    sign_session_id: str | None,
    user: AuthUser | User,
) -> None:
    if sign_session_id:
        await services.kafka.send_record(
            topic=topics.UPDATE_SIGN_SESSION_DOCUMENT_STATUS,
            value={
                'document_id': document_id,
                'sign_session_id': sign_session_id,
                'next_status': SignSessionDocumentStatus.viewed.value,
                'previous_status': None,
            },
        )
        await services.kafka.send_record(
            topic=topics.UPDATE_SIGN_SESSION_DOCS_DATE_DELIVERED,
            value={
                'sign_session_id': sign_session_id,
                'document_ids': [document_id],
                'date_delivered': utc_now(),
            },
        )
    else:
        await update_documents_date_delivered_job(role_id=user.role_id, documents_ids=[document_id])


async def handle_resolve_document_not_found(
    conn: DBConnection,
    *,
    options: DataDict,
    user: AuthUser | User,
) -> Nothing:
    """
    Handle a case when a document was not resolved for "document" root node.
    """

    # Previously, we always returned Nothing when a document was not found. This approach worked,
    # but it was challenging to distinguish between a case where a document was not found and a
    # case where a document was found, but the user did not have access to it. To clarify this
    # distinction, we now raise a different error in the latter case. To maintain backward
    # compatibility, we still return Nothing if the frontend or mobile client has not updated its
    # error handling yet, but in the future this option should be removed and the error should be
    # raised in all cases.
    raise_error = options.get('withError', False)

    if not raise_error:
        logger.info(
            msg='Returning nothing for resolve document',
            extra={'options': options},
        )
        return Nothing

    if (user_id := user.id) is None:
        raise DocumentNotFoundError()

    options_document_id: str | None = options.get('id')
    if not options_document_id:
        raise DocumentNotFoundError()

    # If the document is not found but the user has access to other roles, the mobile or
    # frontend clients should display a message offering the option to switch to the role
    # where the document is available. See also "documentAvailableRoles" query.
    available_roles = await select_document_available_roles_for_graph(
        conn=conn,
        user_id=user_id,
        document_id=options_document_id,
    )
    if available_roles:
        raise DocumentFoundForOtherRoleError()

    raise DocumentNotFoundError()


async def resolve_documents_from_es(
    ctx: Context, options: DataDict, user: User | AuthUser
) -> tuple[int, StrList]:
    es = ctx[ES_KEY]
    db = ctx[DB_READONLY_KEY]

    listing_dates_context = get_graph_context_dict__document_date_listing(ctx)

    limit = options['limit'] or 0
    offset = options['offset'] or 0
    if limit + offset > ELASTIC_MAX_SEARCH_WINDOW_LIMIT:
        raise GraphQLValidationError(
            errors=[f'Query offset + limit must be less than {ELASTIC_MAX_SEARCH_WINDOW_LIMIT}']
        )

    async with db.acquire() as conn:
        from api.graph.low_level.utils import prepare_resolve_es_documents_options

        options = await prepare_resolve_es_documents_options(conn, options, user)

    query = await build_listing_query(user, es.documents, options, with_listing_date=True)
    result = await fetch_es(query=query, source=ESQuerySource.graph)
    documents_count = result.total
    documents_ids_es = [h.document_id for h in result.hits]

    if get_flag(FeatureFlags.ENABLE_LISTING_DATE_FROM_ES):
        raw_result = RawESSearchResult(raw=result.raw)
        for hit in raw_result.hits:
            document_id: str | None = hit.get_field(name='document_id')
            if not document_id:
                continue

            # Extract "Document.viewer_dates.date_created" from inner hits:
            # -> inner_hits.viewer_dates.hits.hits[0]._source.date_created
            raw_date: str | None = hit.inner_hit_field('viewer_dates.date_created')
            if parsed_date := parse_es_date_field(raw_date):
                listing_dates_context[document_id] = parsed_date

    result_document_ids = documents_ids_es
    async with db.acquire() as conn:
        documents_ids_missing_in_db = await select_missing_documents_ids(
            conn=conn,
            documents_ids=documents_ids_es,
        )

        if documents_ids_missing_in_db:
            logger.warning(
                msg='Mismatch of documents from ES and DB',
                extra={
                    'len_es': len(documents_ids_es),
                    'missing_ids_documents': documents_ids_missing_in_db,
                },
            )
            result_document_ids = [
                doc_id for doc_id in documents_ids_es if doc_id not in documents_ids_missing_in_db
            ]

    return documents_count, result_document_ids
