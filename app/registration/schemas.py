from typing import Union, cast

import pydantic

from app.auth.types import UpdateRoleDict
from app.lib import validators_pydantic as pv


class BaseInviteUserSchema(pydantic.BaseModel):
    """
    Common schema for invite user. For recipient and coworker, there are more specific schemas
    that will be validated further.
    """

    email: pv.Email
    edrpou: pv.EDRPOU


class InviteCoworkerPermissionSchema(pydantic.BaseModel):
    """
    List of permissions that can be granted to invited coworker.

    "None" means that permission is not granted.

    WARNING: do not add "can_view_private_document" permission to this list because it has
    higher security sensitivity and should be granted only manually by admin.
    """

    can_view_document: bool | None = None
    can_comment_document: bool | None = None
    can_upload_document: bool | None = None
    can_download_document: bool | None = None
    can_print_document: bool | None = None
    can_delete_document: bool | None = None
    can_sign_and_reject_document: bool | None = None
    can_sign_and_reject_document_external: bool | None = None
    can_sign_and_reject_document_internal: bool | None = None
    can_invite_coworkers: bool | None = None
    can_edit_company: bool | None = None
    can_edit_roles: bool | None = None
    can_create_tags: bool | None = None
    can_edit_document_automation: bool | None = None
    can_edit_document_fields: bool | None = None
    can_edit_document_category: bool | None = None
    can_extract_document_structured_data: bool | None = None
    can_edit_document_structured_data: bool | None = None
    can_archive_documents: bool | None = None
    can_edit_templates: bool | None = None
    can_edit_directories: bool | None = None
    can_delete_archived_documents: bool | None = None
    can_remove_itself_from_approval: bool | None = None
    can_view_private_document: bool | None = None
    can_view_coworkers: bool | None = None

    can_change_document_signers_and_reviewers: bool | None = None
    can_delete_document_extended: bool | None = None
    can_download_actions: bool | None = None
    can_edit_company_contact: bool | None = None
    can_edit_required_fields: bool | None = None
    can_edit_security: bool | None = None

    is_admin: bool | None = None

    def to_update_dict(self) -> UpdateRoleDict:
        data = self.model_dump(exclude={'is_admin'}, exclude_none=True)
        return cast(UpdateRoleDict, data)


class InviteCoworkerSchema(BaseInviteUserSchema):
    email: pv.Email
    edrpou: pv.EDRPOU
    position: str | None = pydantic.Field(default=None, max_length=2048)
    permissions: InviteCoworkerPermissionSchema | None = None


class InviteRecipientSchema(BaseInviteUserSchema): ...


InviteUserSchemaRequest = pydantic.RootModel[Union[InviteCoworkerSchema, InviteRecipientSchema]]  # noqa: UP007
