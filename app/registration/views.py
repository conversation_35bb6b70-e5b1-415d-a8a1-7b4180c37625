import logging
from http import HTTPStatus

from aiohttp import web

import app.contacts.utils as contacts
from api.errors import Code, DoesNotExist, Error, Object
from app.actions.utils import get_source
from app.auth import concierge
from app.auth.db import (
    is_user_email_exists,
    select_base_user,
    select_user,
)
from app.auth.decorators import (
    base_login_required,
    login_required,
    redirect_to_app,
    sign_session_base_login_required,
    sign_session_login_required,
)
from app.auth.enums import AuthFactor
from app.auth.types import AuthUser, BaseUser, UpdateBaseUserDict, User
from app.auth.utils import (
    create_autogenerated_user,
    login_user_registration,
    send_jobs_about_new_role,
    sync_user_roles,
    update_user,
)
from app.auth.validators import validate_user_permission
from app.crm.utils import send_user_to_crm
from app.events.user_actions import utils as user_actions_utils
from app.events.user_actions.utils import get_event_source
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import validators
from app.lib.datetime_utils import ONE_MINUTE_DELTA
from app.lib.helpers import client_rate_limit, get_client_ip, json_response
from app.lib.types import DataDict
from app.mobile.notifications import notifications as mobile_notifications
from app.onboarding import utils as onboarding_utils
from app.openapi.decorators import openapi_docs
from app.profile.utils import (
    add_esputnik_contact,
    generate_esputnik_registration_event,
    update_esputnik_email_confirmation,
    update_esputnik_position,
)
from app.registration import db, utils
from app.registration.db import insert_registration_info
from app.registration.emailing import (
    send_confirmation_email,
    send_coworker_invite_email,
    send_coworker_invite_email_with_password_recover,
    send_invite_email,
    send_recipient_invite_email,
)
from app.registration.enums import RegistrationSource
from app.registration.schemas import BaseInviteUserSchema, InviteUserSchemaRequest
from app.registration.utils import (
    add_active_role,
    create_company_registration_token,
    delete_company_registration_token,
    process_invite_coworker,
    redirect_after_confirmation,
    save_esputnik_company_check,
    save_event_about_new_active_role,
    send_jobs_about_company_update,
    send_jobs_about_new_active_role,
)
from app.registration.validators import (
    CheckEmailExistsSchema,
    RegistrationChangeEmailSchema,
    validate_change_email,
    validate_complete_registration,
    validate_confirm_email,
    validate_creates_signer_role,
    validate_invite_coworker,
    validate_invite_recipient,
    validate_registration,
    validate_registration_info,
    validate_registration_with_token,
    validate_set_registration_source,
    validate_short_registration,
)
from app.services import services
from worker import topics

logger = logging.getLogger(__name__)

INVITE_CONTACT_PENDING_TIME = 60 * 60 * 24


@client_rate_limit(limit=30, delta=30 * ONE_MINUTE_DELTA)
async def check_email_registration(request: web.Request) -> web.Response:
    """Check email exists or not in order to redirect user to auth/registration accordingly"""

    data = {'email': request.rel_url.query.get('email')}
    valid_data = validators.validate_pydantic(CheckEmailExistsSchema, data)

    async with services.db.acquire() as conn:
        exists = await is_user_email_exists(conn=conn, email=valid_data.email)

    if not exists:
        raise DoesNotExist(Object.user)

    return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def complete_registration(request: web.Request, user: BaseUser | User) -> web.Response:
    """Complete registration after user passing his ECP data to backend"""

    if user.registration_completed:
        return web.json_response()

    if services.config.feature_flags.pass_sign_info_to_backend:
        raw_data = await validators.validate_json_request(request=request)
    else:
        raw_data = await validators.validate_json_post_request(request=request)
    async with services.db.acquire() as conn:
        ctx = await validate_complete_registration(conn, user, dict(raw_data))

        output = await add_active_role(conn, ctx)
        role = output.role

    await services.kafka.send_record(
        topic=topics.INITIATE_REMINDERS_FOR_NEW_SUCCESSFULLY_REGISTERED_EMPLOYEE,
        value={'role_id': role.id},
    )

    await send_jobs_about_new_active_role(
        user_id=user.id,
        user_email=user.email,
        role_id=role.id,
        company_id=role.company_id,
        company_edrpou=ctx.edrpou,
        company_name=ctx.company_name,
        is_company_registered=output.is_company_registered,
    )
    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await save_event_about_new_active_role(
            request_source=get_event_source(request),
            user=user,
            company_id=role.company_id,
            signature_info=ctx.signature_info,
        )

    await delete_company_registration_token(user_id=user.id, user_email=user.email)

    return web.json_response()


async def change_email(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Change user email before email verification is done.

    To change email after email verification, we have a separate flow called "user_email_change"
    in "app.profile" module.
    """

    data = await validators.validate_json_request(request)
    valid = validators.validate_pydantic(RegistrationChangeEmailSchema, data)
    async with request.app['db'].acquire() as conn:
        email = valid.email.strip()
        await validate_change_email(
            conn=conn,
            user=user,
            email=email,
        )

        async with conn.begin():
            await update_user(
                conn=conn,
                user_id=user.id,
                data={'email': email},
            )

            # Update profile on concierge with the new email to make new email visible for other
            # Vchasno services, like EDI or TTN. Each service should compare the email by user_id
            # and update the email if it's different.
            await concierge.update_user_profile(conn=conn, user_id=user.id)

    await send_confirmation_email(
        email=email,
        user_id=user.id,
        redirect_url=data.get('redirect'),
    )
    await send_user_to_crm(user_id=user.id)

    return web.HTTPPartialContent()


async def confirm_email(request: web.Request) -> web.Response:
    """Confirm user email by token from confirmation email."""

    async with services.db.acquire() as conn:
        ctx = await validate_confirm_email(request=request, conn=conn)
        await utils.confirm_email(conn, request=request, ctx=ctx)

    return redirect_after_confirmation(
        to_url=ctx.redirect_url,
        is_logged=ctx.is_logged,
    )


async def invite_user_coworker(
    request: web.Request,
    user: User,
    raw_data: DataDict,
) -> web.Response:
    """
    Invite coworker to the service

    When a user is invited to a company, we automatically create an active role for them within
    that company. This eliminates the need to wait for the user to register and assign a role
    themselves.

    If the user is new to the platform, we also send them an email containing a token that
    automatically confirms their email upon registration. There is no need to send a separate
    confirmation email if the user arrives via the invitation email.
    """
    async with services.db.acquire() as conn:
        options = await validate_invite_coworker(conn=conn, user=user, data=raw_data)

        async with conn.begin():
            await onboarding_utils.record_user_onboarding(
                conn=conn,
                user_id=user.id,
                has_invited_coworker=True,
            )

            invited_user: BaseUser
            if options.existed_user:
                invited_user = options.existed_user
            else:
                invited_user = await create_autogenerated_user(
                    conn=conn,
                    email=options.email,
                    is_email_confirmed=False,
                    is_registration_completed=False,
                    created_by=user.role_id,
                    source=RegistrationSource.vchasno,
                )

            role = await process_invite_coworker(
                conn=conn,
                invited_by=user,
                options=options,
                invited_user=invited_user,
            )

    await send_jobs_about_new_role(role_id=role.id, company_id=role.company_id)
    await send_jobs_about_company_update(company_id=user.company_id)
    await add_esputnik_contact(email=invited_user.email)

    if options.is_new_user:
        await send_coworker_invite_email_with_password_recover(initiator=user, ctx=options)

    else:
        await update_esputnik_position(
            user_email=invited_user.email,
            role_id=role.id,
            new_position=options.position,
            old_position=None,
        )

        await send_coworker_invite_email(
            recipient_email=options.email,
            recipient_edrpou=options.edrpou,
            user=user,
        )

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION) and role.status.is_active:
        await user_actions_utils.add_user_action(
            user_action=user_actions_utils.types.UserAction(
                action=user_actions_utils.types.Action.role_create,
                source=user_actions_utils.get_event_source(request),
                email=invited_user.email,
                user_id=invited_user.id,
                phone=invited_user.phone,
                company_id=user.company_id,
                extra={
                    'affected_user_email': invited_user.email,
                    'ip': get_client_ip(request),
                    'user_agent': request.headers.get('User-Agent'),
                },
            )
        )

    # TODO: consider to move to the worker, to avoid blocking the request with remote call
    if not options.is_new_user:
        async with services.db.acquire() as conn:
            coworker = await select_user(conn=conn, role_id=role.id)
            await mobile_notifications.send_push_notification_about_company_invite(
                recipient=coworker,
            )

    return web.json_response(status=HTTPStatus.CREATED)


async def invite_user_recipient(
    user: User,
    raw_data: DataDict,
) -> web.Response:
    """
    Invite recipient to the service

    Since anyone can send an invitation to anyone, we don't create any role in any status
    for the invited user. This is done to prevent malicious actors from somehow creating a role
    in the invited user's company without actual consent.
    """
    async with services.db.acquire() as conn:
        options = await validate_invite_recipient(conn=conn, data=raw_data)

        logger.info(
            msg='Invite recipient to Vchasno',
            extra={
                'company_id': user.company_id,
                'user_email': user.email,
                'recipient_email': options.email,
            },
        )
        async with conn.begin():
            await onboarding_utils.record_user_onboarding(
                conn=conn,
                user_id=user.id,
                has_invited_recipient=True,
            )

            # For non-registered users, we create a link with a token that allows user to register
            # and confirm email in one step. No role is created for invited user, they should
            # add them by standard way after registration.
            if not (options.existed_user and options.existed_user.email_confirmed):
                await send_invite_email(
                    conn=conn,
                    current_user=user,
                    recipient_edrpou=options.edrpou,
                    recipient_email=options.email,
                    # values is None, because we are not creating a role
                    email_domains=None,
                )
                return web.json_response(status=HTTPStatus.OK)

            # User is already registered and their email is confirmed
            invited_user: BaseUser = options.existed_user

            await contacts.save_to_contacts(
                conn=conn,
                edrpou=options.edrpou,
                email=options.email,
                company_id=user.company_id,
            )

        # If a partner is already registered, we send an informative email that some user wants
        # to exchange documents with them in this company. But we don't create any role
        # automatically, and no changes are made to the user's profile.
        await send_recipient_invite_email(
            recipient_email=options.email,
            recipient_edrpou=options.edrpou,
            user=user,
        )

    await add_esputnik_contact(email=invited_user.email)

    return web.json_response(status=HTTPStatus.CREATED)


@openapi_docs(
    summary=_('Запросити співробітника або контрагента у сервіс'),
    request_json=InviteUserSchemaRequest,
)
@login_required()
async def invite_user(request: web.Request, user: User) -> web.Response:
    """
    Invite either a coworker or a partner to Vchasno.

    Coworkers are created instantly with an active role.
    Partners get a pending role if a user is already registered, otherwise
    they just receive an invitation email.
    """
    raw_data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(BaseInviteUserSchema, raw_data)

    if user.company_edrpou == valid_data.edrpou:
        return await invite_user_coworker(
            request=request,
            user=user,
            raw_data=raw_data,
        )
    return await invite_user_recipient(user=user, raw_data=raw_data)


@redirect_to_app
async def registration(request: web.Request) -> web.Response:
    """Register new user.

    This initiates open registration process. To complete process User must
    confirm his email and complete registration by verifying ECP on frontend.
    """

    async with services.db.acquire() as conn:
        valid_json = await validators.validate_json_request(request)
        ctx = await validate_registration(conn, valid_json)
        await utils.web_registration(conn, request=request, ctx=ctx)

    raise web.HTTPFound('/app/registration/confirm-email')


@redirect_to_app
async def registration_with_token(request: web.Request) -> web.Response:
    """Register new user with token.

    Token assigned to user while inviting him by email. After user able to
    change email, but system ensures that original EDRPOU not changed. This
    denies ability to make fishing registration by guessing other company
    EDRPOU.
    """

    async with services.db.acquire() as conn:
        raw_data = await validators.validate_json_request(request)
        validation_ctx = await validate_registration_with_token(conn, raw_data)
        invited_by_coworker = validation_ctx.token.invited_by_company == validation_ctx.token.edrpou

        registration_ctx = await utils.process_registration_with_token(conn, validation_ctx)
        user = registration_ctx.user
        role = registration_ctx.role

        await add_esputnik_contact(kafka=services.kafka, email=validation_ctx.email)
        await save_esputnik_company_check(cookies=request.cookies, email=validation_ctx.email)

        if company_id := registration_ctx.company_id:
            await send_jobs_about_company_update(company_id=company_id)

        if not validation_ctx.is_email_confirmed:
            await send_confirmation_email(
                email=validation_ctx.email,
                user_id=user.id,
                redirect_url=validation_ctx.redirect_url,
            )
        else:
            # Confirm email as separate job to send welcome letter via ESputnik
            await update_esputnik_email_confirmation(validation_ctx.email)

        await send_user_to_crm(user.id)
        if invited_by_coworker and role:
            await send_jobs_about_new_role(role.id, role.company_id)

            if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
                initiated_by = await select_base_user(
                    conn=conn, user_id=validation_ctx.token.invited_by_user
                )
                if initiated_by:
                    await user_actions_utils.add_user_action(
                        user_action=user_actions_utils.types.UserAction(
                            action=user_actions_utils.types.Action.role_create,
                            source=user_actions_utils.get_event_source(request),
                            email=user.email,
                            user_id=user.id,
                            phone=user.phone,
                            company_id=role.company_id,
                            extra={
                                'affected_user_email': user.email,
                                'ip': get_client_ip(request),
                                'user_agent': request.headers.get('User-Agent'),
                            },
                        )
                    )
                else:
                    logger.info("Couldn't find initiator of role creation")

        await login_user_registration(
            request=request,
            conn=conn,
            user=user,
            first_factor=AuthFactor.email,
        )

    return web.json_response(status=201)


@base_login_required()
async def resend_confirmation_email(__: web.Request, user: BaseUser | User) -> web.Response:
    """Resend confirmation email if user is still not confirmed his email."""
    if user.email and not user.email_confirmed:
        await send_confirmation_email(user_id=user.id, email=user.email)

    return web.json_response()


@login_required()
async def invite_unregistered_contacts(request: web.Request, user: User) -> web.Response:
    """Invite new users using unregistered contacts"""

    validate_user_permission(user, {'can_edit_company_contact'})

    app = request.app
    redis = app['redis']
    key = f'invite-contacts-{user.company_id}'

    if await redis.get(key):
        raise Error(Code.too_many_request_to_invite_contacts, status=418)

    await app['kafka'].send_record(
        topic=topics.INVITE_UNREGISTERED_CONTACTS,
        value={
            'user_role_id': user.role_id,
            'source': get_source(request).value,
        },
    )

    # one day key
    await redis.setex(key, time=INVITE_CONTACT_PENDING_TIME, value='1')

    app_brand: str = services.config.app.brand
    return json_response(
        {
            'message': (
                _('Вашим контактам буде надіслано запрошення приєднатися до "{brand}".').bind(
                    brand=app_brand
                )
            ),
        }
    )


@base_login_required()
async def save_registration_info(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Save registration info, such as UTM or redirect after registration was done
    """
    options = await validate_registration_info(request)

    async with request.app['db'].acquire() as conn:
        # prevent rewrite already existed registration info and skip such requests
        if await db.exists_registration_info_by_user(conn, user_id=user.id):
            return web.json_response(status=HTTPStatus.OK)

        db_data = options.as_db(user_id=user.id)
        await insert_registration_info(conn, data=db_data)

    return web.json_response(status=HTTPStatus.CREATED)


@sign_session_base_login_required
async def generate_company_registration_token(
    request: web.Request, user: AuthUser | User | BaseUser
) -> web.Response:
    """
    Generate and store token that user should sign for approving company identity
    """
    token = await create_company_registration_token(
        user_id=user.id,
        user_email=user.email,
    )
    return web.json_response({'token': token.original, 'hash': token.base64_hash})


@sign_session_login_required
async def create_signer_role(request: web.Request, user: AuthUser | User) -> web.Response:
    """
    Generate a signer from the sign session. It should be used before document signing
    because the signing API requires an active user role.
    """

    async with services.db.acquire() as conn:
        ctx = await validate_creates_signer_role(conn, request, user)
        async with conn.begin():
            output = await utils.create_signer_base(conn=conn, ctx=ctx)

    await utils.schedule_create_signer_async_jobs(
        request=request,
        output=output,
    )

    await delete_company_registration_token(user_id=None, user_email=ctx.email)

    return web.json_response()


@redirect_to_app
async def short_registration(request: web.Request) -> web.Response:
    """
    Short registration by setting up phone and replacing
    autogenerated password.

    It is used for confirming users who were invited either by Vchasno
    coworkers or Vchasno EDI buyer company.
    """
    async with request.app['db'].acquire() as conn:
        data, user = await validate_short_registration(conn, request)
        update_data: UpdateBaseUserDict = {
            'new_password': data['password'],
            'email_confirmed': True,
            'is_autogenerated_password': False,
        }

        async with conn.begin():
            user = await update_user(
                conn=conn,
                user_id=user.id,
                data=update_data,
            )

        await login_user_registration(
            request=request,
            conn=conn,
            user=user,
            first_factor=AuthFactor.email,
        )

    await send_user_to_crm(user.id)
    await add_esputnik_contact(kafka=request.app['kafka'], email=user.email)
    await generate_esputnik_registration_event(email=user.email)
    await save_esputnik_company_check(cookies=request.cookies, email=user.email)

    return web.json_response()


@base_login_required()
async def set_source(request: web.Request, user: BaseUser | User) -> web.Response:
    source = await validate_set_registration_source(request)
    async with services.db.acquire() as conn:
        await update_user(
            conn=conn,
            user_id=user.id,
            data={'source': source},
        )
    await generate_esputnik_registration_event(email=user.email)
    return web.json_response()


@base_login_required()
async def sync_roles(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Try to sync roles for current user from CRM
    https://tabula-rasa.atlassian.net/l/cp/C49j1ZhA
    """

    result = await sync_user_roles(
        request_source=get_event_source(request),
        user=user,
    )
    return web.json_response(text=result.to_json())
