from http import HTTPStatus

from aiohttp import web

from api.errors import AccessDenied
from app.auth.types import User
from app.events.user_actions import types
from app.events.user_actions.utils import add_user_action, add_user_actions, get_event_source
from app.groups.db import (
    exist_groups,
    select_group,
    select_group_members,
    select_groups,
)
from app.groups.types import GroupMember
from app.groups.utils import (
    add_group,
    add_group_member,
    change_group_name,
    remove_group,
    remove_group_members,
)
from app.groups.validators import (
    check_group_permission,
    validate_add_group,
    validate_delete_group,
    validate_delete_group_member,
    validate_edit_group,
    validate_insert_group_member,
)
from app.lib import validators
from app.services import services


async def get_group_handler(request: web.Request, user: User) -> web.Response:
    """
    Handler for getting a group.
    """

    check_group_permission(user)

    async with services.db.acquire() as conn:
        group = await select_group(
            conn=conn,
            id=request.match_info['group_id'],
            company_id=user.company_id,
        )

    if not group:
        raise AccessDenied()

    return web.json_response(status=HTTPStatus.OK, data=group.to_response_dict())


async def get_groups_handler(
    _: web.Request,
    user: User,
) -> web.Response:
    """
    Handler for getting groups.
    """

    check_group_permission(user)

    async with services.db.acquire() as conn:
        groups = await select_groups(
            conn=conn,
            company_id=user.company_id,
            is_deleted=False,
            order=['name'],
        )

    return web.json_response(
        status=HTTPStatus.OK, data=[group.to_response_dict() for group in groups]
    )


async def add_group_handler(request: web.Request, user: User) -> web.Response:
    """
    Handler for adding a group.
    """

    raw_data = await validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        options = await validate_add_group(conn=conn, raw_data=raw_data, user=user)

        group = await add_group(conn=conn, name=options.name, user=options.initiator)

    await add_user_action(
        user_action=types.UserAction(
            action=types.Action.group_create,
            source=get_event_source(request),
            email=user.email,
            user_id=user.id,
            phone=user.auth_phone,
            company_id=user.company_id,
            extra={'group_id': group.id, 'name': group.name},
        )
    )

    return web.json_response(status=HTTPStatus.CREATED, data=group.to_response_dict())


async def update_group_handler(
    request: web.Request,
    user: User,
) -> web.Response:
    """
    Handler for updating a group.
    """

    raw_data = await validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        options = await validate_edit_group(
            conn=conn,
            raw_data=raw_data,
            user=user,
            group_id=request.match_info['group_id'],
        )

        group = await change_group_name(
            conn=conn,
            id=options.group.id,
            name=options.name,
        )

    await add_user_action(
        user_action=types.UserAction(
            action=types.Action.group_rename,
            source=get_event_source(request),
            email=user.email,
            user_id=user.id,
            phone=user.auth_phone,
            company_id=user.company_id,
            extra={
                'group_id': options.group.id,
                'name_old': options.group.name,
                'name_new': options.name,
            },
        )
    )

    assert group is not None
    response_dict = group.to_response_dict()

    return web.json_response(status=HTTPStatus.OK, data=response_dict)


async def delete_group_handler(
    request: web.Request,
    user: User,
) -> web.Response:
    """
    Handler for deleting a group.
    """

    async with services.db.acquire() as conn:
        options = await validate_delete_group(
            conn=conn,
            user=user,
            group_id=request.match_info['group_id'],
        )

        group = await remove_group(
            conn=conn,
            deleted_by=options.initiator.role_id,
            id=options.group_id,
        )

    if group:
        await add_user_action(
            user_action=types.UserAction(
                action=types.Action.group_delete,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={'group_id': options.group_id, 'name': group.name},
            )
        )

    return web.json_response(status=HTTPStatus.NO_CONTENT)


async def get_group_members_handler(
    request: web.Request,
    user: User,
) -> web.Response:
    """
    Handler for getting group members.
    """

    check_group_permission(user)

    async with services.db.acquire() as conn:
        if not await exist_groups(
            conn=conn,
            ids=[request.match_info['group_id']],
            company_id=user.company_id,
        ):
            raise AccessDenied()

        group_members = await select_group_members(
            conn=conn,
            group_ids=[request.match_info['group_id']],
            company_id=user.company_id,
            is_deleted=False,
        )

    return web.json_response(
        status=HTTPStatus.OK,
        data=[group_member.to_response_dict() for group_member in group_members],
    )


async def add_group_member_handler(
    request: web.Request,
    user: User,
) -> web.Response:
    """
    Handler for adding a group member.
    """

    raw_data = await validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        options = await validate_insert_group_member(
            conn=conn,
            raw_data=raw_data,
            user=user,
            group_id=request.match_info['group_id'],
        )
        response_dict = []
        group_members: list[GroupMember] = []
        for role_id in options.role_ids:
            group_member = await add_group_member(
                conn=conn,
                group_id=options.group.id,
                role_id=role_id,
                initiator=options.initiator,
            )
            response_dict.append(group_member.to_response_dict())
            group_members.append(group_member)

    if group_members:
        role_ids = [member.role_id for member in group_members]
        async with services.db.acquire() as conn:
            affected_users = await select_group_members(
                conn=conn,
                group_ids=[options.group.id],
                company_id=user.company_id,
                is_deleted=False,
            )
        actions = [
            types.UserAction(
                action=types.Action.group_member_add,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={
                    'name': options.group.name,
                    'group_id': options.group.id,
                    'affected_user_email': getattr(member, 'user_email', None),
                },
            )
            for member in affected_users
            if member.role_id in role_ids
        ]
        if actions:
            await add_user_actions(actions)

    return web.json_response(status=HTTPStatus.CREATED, data=response_dict)


async def delete_group_member_handler(
    request: web.Request,
    user: User,
) -> web.Response:
    """
    Handler for removing a group member.
    """

    raw_data = await validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        options = await validate_delete_group_member(
            conn=conn,
            user=user,
            raw_data=raw_data,
            group_id=request.match_info['group_id'],
        )
        group_members = await remove_group_members(
            conn=conn,
            deleted_by=options.initiator.role_id,
            ids=options.group_member_ids,
        )

    if group_members:
        role_ids = [member.role_id for member in group_members]
        async with services.db.acquire() as conn:
            affected_users = await select_group_members(
                conn=conn,
                group_ids=[options.group.id],
                company_id=user.company_id,
                is_deleted=True,
            )
        actions = [
            types.UserAction(
                action=types.Action.group_member_remove,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={
                    'name': options.group.name,
                    'group_id': options.group.id,
                    'affected_user_email': getattr(member, 'user_email', None),
                },
            )
            for member in affected_users
            if member.role_id in role_ids
        ]
        if actions:
            await add_user_actions(actions)

    return web.json_response(status=HTTPStatus.NO_CONTENT)
