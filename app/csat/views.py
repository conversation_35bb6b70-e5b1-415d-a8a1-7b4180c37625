import logging
from http import HTTPStatus

from aiohttp import web

from app.auth.decorators import login_required
from app.auth.types import User
from app.csat import db, validators
from app.lib.validators import validate_json_request, validate_pydantic
from app.openapi.decorators import openapi_docs
from app.services import services

logger = logging.getLogger(__name__)


@openapi_docs(summary='Add CSAT feedback', request_json=validators.CsatRequestSchema)
@login_required()
async def add_csat_feedback(request: web.Request, user: User) -> web.Response:
    raw_data = await validate_json_request(request)
    valid_data = validate_pydantic(validators.CsatRequestSchema, raw_data)

    async with services.db_readonly.acquire() as conn:
        await validators.validate_add_csat_feedback(conn=conn, user_id=user.id, data=valid_data)

    async with services.db.acquire() as conn:
        await db.insert_csat_survey(
            conn=conn,
            data={
                'user_id': user.id,
                'company_id': user.company_id,
                'type': valid_data.type,
                'estimate': valid_data.estimate,
                'feedback': valid_data.feedback,
            },
        )

    return web.json_response(data={'status': 'ok'}, status=HTTPStatus.CREATED)
