from app.csat import db, enums
from app.services import services
from app.tests import common


async def test_select_csat_surveys(aiohttp_client):
    app, client, user = await common.prepare_client(aiohttp_client)

    async with services.db.acquire() as conn:
        csats_with_estimate = [
            await db.insert_csat_survey(
                conn=conn,
                data={
                    'user_id': user.id,
                    'company_id': user.company_id,
                    'type': enums.CsatActionType.upload_doc_with_signing,
                    'estimate': 1,
                    'feedback': f'test feedbackl {i}',
                },
            )
            for i in range(2)
        ]

        csats_without_estimate = [
            await db.insert_csat_survey(
                conn=conn,
                data={
                    'user_id': user.id,
                    'company_id': user.company_id,
                    'type': enums.CsatActionType.upload_doc_without_signing,
                },
            )
            for _ in range(2)
        ]

        assert (
            await db.select_csat_surveys(
                conn=conn,
                user_id=user.id,
                type=enums.CsatActionType.upload_doc_with_signing,
                with_estimate=True,
            )
            == csats_with_estimate
        )

        assert (
            await db.select_csat_surveys(
                conn=conn,
                user_id=user.id,
                type=enums.CsatActionType.upload_doc_with_signing,
                with_estimate=False,
            )
            == []
        )

        assert (
            await db.select_csat_surveys(
                conn=conn,
                user_id=user.id,
                type=enums.CsatActionType.upload_doc_without_signing,
                with_estimate=False,
            )
            == csats_without_estimate
        )
