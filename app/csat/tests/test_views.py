import http

from app.csat import db, enums, types
from app.services import services
from app.tests import common


async def test_csat_feedback_added_successfully(aiohttp_client):
    _, client, user = await common.prepare_client(aiohttp_client)

    response = await client.post(
        path='/internal-api/csat',
        json={
            'type': enums.CsatActionType.upload_doc_with_signing.value,
            'estimate': 4,
            'feedback': 'Test feedback',
        },
        headers=common.prepare_auth_headers(user),
    )

    assert response.status == http.HTTPStatus.CREATED

    async with services.db.acquire() as conn:
        csat_surveys = await db.select_csat_surveys(conn=conn, user_id=user.id)

    assert len(csat_surveys) == 1
    assert csat_surveys[0] == types.CsatSurvey(
        id=csat_surveys[0].id,
        user_id=user.id,
        type=enums.CsatActionType.upload_doc_with_signing,
        company_id=user.company_id,
        date_created=csat_surveys[0].date_created,
        estimate=4,
        feedback='Test feedback',
    )


async def test_forbidden_to_add_csat_feedback_already_submitted_once(aiohttp_client):
    """
    Given an attempt to submit csat (of the same type) twice - by a single user.
    Expected error to be raised.
    """
    _, client, user = await common.prepare_client(aiohttp_client)

    # Submit first CSAT - expected success
    response = await client.post(
        path='/internal-api/csat',
        json={
            'type': enums.CsatActionType.upload_doc_with_signing.value,
            'estimate': 4,
            'feedback': 'Test feedback',
        },
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == http.HTTPStatus.CREATED

    # Submit second CSAT - expected error
    response = await client.post(
        path='/internal-api/csat',
        json={
            'type': enums.CsatActionType.upload_doc_with_signing.value,
            'estimate': 4,
            'feedback': 'Test feedback',
        },
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == http.HTTPStatus.BAD_REQUEST
    data = await response.json()

    assert data == {
        'code': 'invalid_request',
        'details': {},
        'reason': 'CSAT опитування вже було проведено для Вас.',
    }

    # Submit third CSAT (of another type) - expected success
    response = await client.post(
        path='/internal-api/csat',
        json={
            'type': enums.CsatActionType.upload_doc_without_signing.value,
            'estimate': 4,
            'feedback': 'Test feedback',
        },
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == http.HTTPStatus.CREATED


async def test_forbidden_to_add_csat_feedback_already_submitted_empty_twice(aiohttp_client):
    """
    Given an attempt to submit csat (of the same type) twice - by a single user.
    Expected error to be raised.
    """
    _, client, user = await common.prepare_client(aiohttp_client)

    # Submit first empty CSAT - expected success
    response = await client.post(
        path='/internal-api/csat',
        json={'type': enums.CsatActionType.upload_doc_with_signing.value},
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == http.HTTPStatus.CREATED

    # Submit second empty CSAT - expected success
    response = await client.post(
        path='/internal-api/csat',
        json={
            'type': enums.CsatActionType.upload_doc_with_signing.value,
            'estimate': 4,
            'feedback': 'Test feedback',
        },
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == http.HTTPStatus.CREATED

    # Submit third CSAT - expected error
    response = await client.post(
        path='/internal-api/csat',
        json={'type': enums.CsatActionType.upload_doc_with_signing.value},
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == http.HTTPStatus.BAD_REQUEST
    data = await response.json()

    assert data == {
        'code': 'invalid_request',
        'details': {},
        'reason': 'CSAT опитування вже було проведено для Вас.',
    }


async def test_add_feedback_without_estimate(aiohttp_client):
    _, client, user = await common.prepare_client(aiohttp_client)

    # Submit first empty CSAT - expected success
    response = await client.post(
        path='/internal-api/csat',
        json={'type': enums.CsatActionType.upload_doc_with_signing.value, 'feedback': 'test'},
        headers=common.prepare_auth_headers(user),
    )
    assert response.status == http.HTTPStatus.BAD_REQUEST
    data = await response.json()

    assert data == {
        'code': 'invalid_request',
        'details': {},
        'reason': "Estimate є обов'язковим при вказанні feedback.",
    }
