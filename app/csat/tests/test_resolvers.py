from datetime import datetime, timedelta

import pytest
from sqlalchemy.dialects.postgresql import insert

from app.csat import constants, db, enums, tables
from app.services import services
from app.tests import common


@pytest.mark.parametrize(
    'csat_date_created, expected_result',
    (
        [
            # User has never undergone a CSAT survey
            None,
            True,
        ],
        [
            # User has recently undergone a CSAT survey
            datetime.now() - timedelta(days=constants.INTERVAL_DAYS_BETWEEN_DIFFERENT_CSATS - 3),
            False,
        ],
        [
            # User has undergone a CSAT survey a while ago
            datetime.now() - timedelta(days=constants.INTERVAL_DAYS_BETWEEN_DIFFERENT_CSATS + 3),
            True,
        ],
    ),
)
async def test_resolve_is_ready_for_next_csat(
    aiohttp_client,
    csat_date_created,
    expected_result,
):
    _, client, user = await common.prepare_client(aiohttp_client)
    query = '{ currentUser { isReadyForNextCsat } }'

    if csat_date_created:
        async with services.db.acquire() as conn:
            await conn.execute(
                insert(tables.csat_survey_table).values(
                    {
                        'user_id': user.id,
                        'company_id': user.company_id,
                        'type': enums.CsatActionType.upload_doc_with_signing,
                        'estimate': 1,
                        'date_created': csat_date_created,
                    }
                )
            )

    resp = await common.fetch_graphql(client, query, common.prepare_auth_headers(user))
    assert resp['currentUser']['isReadyForNextCsat'] == expected_result


async def test_resolve_csat_surveys(aiohttp_client):
    _, client, user = await common.prepare_client(aiohttp_client)
    query = '{ currentUser { csatSurveys { id type estimate dateCreated } } }'

    async with services.db.acquire() as conn:
        first_csat = await db.insert_csat_survey(
            conn=conn,
            data={
                'user_id': user.id,
                'company_id': user.company_id,
                'type': enums.CsatActionType.upload_doc_with_signing,
                'estimate': 2,
            },
        )
        second_csat = await db.insert_csat_survey(
            conn=conn,
            data={
                'user_id': user.id,
                'company_id': user.company_id,
                'type': enums.CsatActionType.upload_doc_without_signing,
                'estimate': 2,
            },
        )

    resp = await common.fetch_graphql(client, query, common.prepare_auth_headers(user))
    csats = resp['currentUser']['csatSurveys']

    for csat in (first_csat, second_csat):
        assert {
            'id': csat.id,
            'type': csat.type.value,
            'estimate': csat.estimate,
            'dateCreated': csat.date_created.isoformat(),
        } in csats
