import datetime
from dataclasses import dataclass

from app.csat import enums
from app.lib.database import DBRow


@dataclass(frozen=True)
class CsatSurvey:
    """
    Represents a single entry for CSAT survery - filled by the user.
    """

    id: str
    user_id: str
    company_id: str
    type: enums.CsatActionType
    date_created: datetime.datetime

    estimate: int | None = None
    feedback: str | None = None

    @staticmethod
    def from_db(row: DBRow) -> 'CsatSurvey':
        return CsatSurvey(
            id=row.id,
            user_id=row.user_id,
            company_id=row.company_id,
            type=row.type,
            date_created=row.date_created,
            estimate=row.estimate,
            feedback=row.feedback,
        )
