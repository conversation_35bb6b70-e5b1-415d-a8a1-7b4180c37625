from collections import defaultdict
from datetime import timedelta
from typing import Any

import sqlalchemy as sa
from hiku.engine import Context, pass_context

from api.graph import constants as graph_constants
from api.graph import utils as graph_utils
from app.csat import constants, db
from app.lib import datetime_utils
from app.models import exists


@pass_context
async def resolve_is_ready_for_next_csat(ctx: Context, *args: Any) -> list[list[bool]]:
    """
    Resolve whether user is ready for the next CSAT (if sufficient time has passed)
    """
    user = graph_utils.get_base_graph_user(ctx)
    if not user:
        return [[True]]

    async with ctx[graph_constants.DB_ENGINE_KEY].acquire() as conn:
        has_recent_csat = await exists(
            conn=conn,
            select_from=db.csat_survey_table,
            clause=sa.and_(
                db.csat_survey_table.c.user_id == user.id,
                # At least X amount of days should pass before the next CSAT
                db.csat_survey_table.c.date_created
                >= datetime_utils.local_now()
                - timedelta(days=constants.INTERVAL_DAYS_BETWEEN_DIFFERENT_CSATS),
            ),
        )

    return [[not has_recent_csat]]


@pass_context
async def resolve_csat_surveys(ctx: Context, ids: list[str]) -> list[list[str]]:
    """
    Fetch the CSAT surveys the users have undergone.
    """

    async with ctx[graph_constants.DB_ENGINE_KEY].acquire() as conn:
        csats = await db.select_csat_surveys_for_graph(conn=conn, user_ids=ids)

    csats_map: defaultdict[str, list[str]] = defaultdict(list)
    for csat in csats:
        csats_map[csat.user_id].append(csat.id)

    return [csats_map.get(user_id, []) for user_id in ids]
