from typing import TypedDict

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.csat import enums, types
from app.csat.tables import csat_survey_table
from app.lib.database import DBConnection, DBRow
from app.models import select_all, select_one


class InsertCSATSurveyDict(TypedDict):
    user_id: str
    company_id: str
    type: enums.CsatActionType
    estimate: int | None
    feedback: str | None


async def insert_csat_survey(conn: DBConnection, data: InsertCSATSurveyDict) -> types.CsatSurvey:
    row = await select_one(
        conn=conn,
        query=(insert(csat_survey_table).values(data).returning(csat_survey_table)),
    )
    return types.CsatSurvey.from_db(row)


async def select_csat_surveys_for_graph(conn: DBConnection, *, user_ids: list[str]) -> list[DBRow]:
    if not user_ids:
        return []

    return await select_all(
        conn=conn,
        query=(
            sa.select([csat_survey_table.c.id, csat_survey_table.c.user_id])
            .select_from(csat_survey_table)
            .where(csat_survey_table.c.user_id.in_(user_ids))
            .order_by(csat_survey_table.c.date_created.desc())
        ),
    )


async def select_csat_surveys(
    conn: DBConnection,
    *,
    user_id: str | None = None,
    type: enums.CsatActionType | None = None,
    with_estimate: bool | None = None,
    limit: int | None = None,
) -> list[types.CsatSurvey]:
    filters = []

    if user_id is not None:
        filters.append(csat_survey_table.c.user_id == user_id)

    if type is not None:
        filters.append(csat_survey_table.c.type == type)

    if with_estimate is True:
        filters.append(csat_survey_table.c.estimate.isnot(None))
    elif with_estimate is False:
        filters.append(csat_survey_table.c.estimate.is_(None))

    assert filters, 'At least one filter is required'

    query = (
        csat_survey_table.select()
        .where(sa.and_(*filters))
        .order_by(csat_survey_table.c.date_created.desc())
    )

    if limit is not None:
        query = query.limit(limit)

    rows = await select_all(conn, query)
    return [types.CsatSurvey.from_db(row) for row in rows]
