import pydantic

from api import errors
from app.csat import db, enums
from app.i18n import _
from app.lib.database import DBConnection


class CsatRequestSchema(pydantic.BaseModel):
    """Schema for CSAT request validation."""

    type: enums.CsatActionType
    estimate: int | None = pydantic.Field(default=None, ge=1, le=5)
    feedback: str | None = pydantic.Field(default=None, max_length=1000)

    @pydantic.model_validator(mode='after')
    def check_feedback_requires_estimate(self) -> 'CsatRequestSchema':
        if self.feedback and self.estimate is None:
            raise errors.InvalidRequest(reason=_("Estimate є обов'язковим при вказанні feedback."))
        return self


async def validate_add_csat_feedback(
    conn: DBConnection,
    *,
    user_id: str,
    data: CsatRequestSchema,
) -> None:
    """
    - if customer has already submitted valid CSAT feedback - do not allow to submit it twice.
    - if customer has already sent empty CSAT feedback - do not allow to submit it further.
    """

    csat_feedbacks_with_estimate = await db.select_csat_surveys(
        conn=conn,
        user_id=user_id,
        type=data.type,
        with_estimate=True,
        limit=2,
    )
    csat_feedbacks_without_estimate = await db.select_csat_surveys(
        conn=conn,
        user_id=user_id,
        type=data.type,
        with_estimate=False,
        limit=3,
    )

    if len(csat_feedbacks_with_estimate) >= 1 or len(csat_feedbacks_without_estimate) >= 2:
        raise errors.InvalidRequest(reason=_('CSAT опитування вже було проведено для Вас.'))
