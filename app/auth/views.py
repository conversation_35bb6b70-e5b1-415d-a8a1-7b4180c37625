import hashlib
import logging
import time

import aiohttp_session
from aiohttp import web

from api.errors import Code, Error, InvalidRequest
from api.public.decorators import api_handler
from app.auth import concierge, db, two_factor, utils
from app.auth.constants import (
    NEXT_URL_VERIFY_2FA_PREFIX,
)
from app.auth.db import delete_tokens as delete_tokens_func
from app.auth.db import (
    insert_token,
    insert_tokens,
    select_base_user,
)
from app.auth.decorators import base_login_required, login_required, redirect_to_app
from app.auth.enums import AuthFactor, AuthFlow
from app.auth.helpers import generate_hidden_email, generate_hidden_number
from app.auth.providers.apple.utils import get_user_with_apple
from app.auth.types import BaseUser, User, is_wide_user_type
from app.auth.utils import (
    get_next_url_from_request,
    get_user_id_from_request,
    login_user,
    logout_user,
    update_company_by_edrpou,
    update_user,
)
from app.auth.validators import (
    ChangeCompanyPermissionsSchema,
    validate_activate_role,
    validate_add_token,
    validate_add_tokens,
    validate_delete_token,
    validate_delete_tokens,
    validate_google_auth,
    validate_login,
    validate_logout_all_sessions,
    validate_logout_session,
    validate_microsoft_auth,
    validate_pending_2fa_web,
    validate_send_email_2fa_token,
    validate_update_company_config,
    validate_user_permission,
    validate_verify_email_2fa,
    validate_verify_email_2fa_token,
    validate_verify_phone_2fa,
)
from app.events.types import DeleteTokenData
from app.events.user_actions import types as user_actions_types
from app.events.user_actions import utils as user_actions_utils
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import session_utils, validators
from app.lib.datetime_utils import ONE_MINUTE_DELTA
from app.lib.helpers import (
    get_client_ip,
    json_response,
    to_json,
    validate_rate_limit,
)
from app.lib.session_utils import destroy_session, get_session_redis_key
from app.lib.validators import validate_json_request
from app.openapi.decorators import openapi_docs
from app.registration.emailing import send_confirmation_email, send_user_welcome_email
from app.registration.utils import send_confirm_email_jobs, send_registration_jobs
from app.services import services
from app.services import services as app_services

logger = logging.getLogger(__name__)


@login_required()
async def switch_role(request: web.Request, user: User) -> web.Response:
    """
    Switch between roles for current user
    """
    app = request.app

    async with app['db'].acquire() as conn:
        role = await validate_activate_role(
            conn=conn,
            data={'role_id': request.match_info['role_id'], 'user_id': user.id},
        )

        await utils.switch_role(request, role, user)

    return web.json_response()


@login_required()
async def update_company_config(request: web.Request, user: User) -> web.Response:
    """
    Update config with a limited set of fields.

    INFO: superadmin handlers for company config update:
     - app.profile.views.update_company_config_superadmin_handler
     - api.private.super_admin.views.upsert_company_config
    """

    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        ctx, prev_config = await validate_update_company_config(
            conn=conn,
            data=data,
            user=user,
        )

        async with conn.begin():
            await db.update_company_config(
                conn=conn,
                company_id=user.company_id,
                config=ctx.to_db_config(),
                admin_config=ctx.to_db_admin_config(),
            )

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        changes = ctx.to_event_data(prev_config)
        if changes:
            actions = [
                user_actions_types.UserAction(
                    action=user_actions_types.Action.company_update,
                    source=user_actions_utils.get_event_source(request),
                    email=user.email,
                    user_id=user.id,
                    phone=user.auth_phone,
                    company_id=user.company_id,
                    extra={'changed_field': field, 'new_value': value},
                )
                for field, value in changes.items()
            ]
            await user_actions_utils.add_user_actions(actions)

    return web.json_response()


@login_required()
async def add_token(request: web.Request, user: User) -> web.Response:
    """Adding authorization token for current user or coworker."""
    async with request.app['db'].acquire() as conn:
        target_role_id, date_expired = await validate_add_token(
            conn, user, await validators.validate_json_request(request)
        )
        token = await insert_token(conn, {'role_id': target_role_id, 'date_expired': date_expired})

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        from app.events.user_actions import utils as user_actions_utils

        user_actions = await user_actions_utils.build_token_create_user_actions(
            actor_user=user,
            role_ids=[target_role_id],
            expire_date=date_expired,
            request=request,
        )
        await user_actions_utils.add_user_actions(user_actions)

    logger.info(
        'Token added',
        extra={
            'by_edrpou': user.company_edrpou,
            'by_role': user.role_id,
            'target_role': target_role_id,
            'date_expired': date_expired,
        },
    )

    return web.json_response(data={'token': token})


@api_handler
async def recreate_token(request: web.Request, user: User) -> web.Response:
    """Delete user's old token and create new one."""
    data = await validators.validate_json_request(request)
    data['role_id'] = user.role_id
    async with request.app['db'].acquire() as conn:
        async with conn.begin():
            target_role_id = await validate_delete_token(conn, user, data)
            deleted_tokens = await delete_tokens_func(conn, [target_role_id])

            target_role_id, date_expired = await validate_add_token(conn, user, data)
            token = await insert_token(
                conn, {'role_id': target_role_id, 'date_expired': date_expired}
            )

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        from app.events.user_actions import utils as user_actions_utils

        delete_actions = await user_actions_utils.build_token_delete_user_actions(
            actor_user=user,
            tokens=[DeleteTokenData.from_row(token) for token in deleted_tokens],
            request=request,
        )
        create_actions = await user_actions_utils.build_token_create_user_actions(
            actor_user=user,
            role_ids=[target_role_id],
            expire_date=date_expired,
            request=request,
        )
        await user_actions_utils.add_user_actions([*delete_actions, *create_actions])

    logger.info(
        'Token recreated',
        extra={
            'by_edrpou': user.company_edrpou,
            'by_role': user.role_id,
            'target_role': target_role_id,
            'date_expired': date_expired,
        },
    )
    data = {'email': user.email, 'token': token, 'date_expired': date_expired}

    return web.json_response(data, dumps=to_json)


@api_handler
async def add_tokens(request: web.Request, user: User) -> web.Response:
    """Batch adding authorization tokens for coworker."""
    async with request.app['db'].acquire() as conn:
        roles, existed_tokens_for, date_expired = await validate_add_tokens(
            conn, user, await validators.validate_json_request(request)
        )
        roles_to_create = [role for role in roles if role.user_email not in existed_tokens_for]
        roles_tokens_mapping = await insert_tokens(
            conn, roles=roles_to_create, date_expired=date_expired
        )

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        from app.events.user_actions import utils as user_actions_utils

        user_actions = await user_actions_utils.build_token_create_user_actions(
            actor_user=user,
            role_ids=[role.id_ for role in roles_to_create],
            expire_date=date_expired,
            request=request,
        )
        await user_actions_utils.add_user_actions(user_actions)

    new_tokens = [
        {
            'email': role.user_email,
            'token': roles_tokens_mapping[role.id_],
            'date_expired': date_expired,
        }
        for role in roles_to_create
    ]
    data = {'new_tokens': new_tokens, 'existed_tokens': list(existed_tokens_for)}
    return web.json_response(data, dumps=to_json)


@login_required()
async def delete_token(request: web.Request, user: User) -> web.Response:
    async with request.app['db'].acquire() as conn:
        target_role_id = await validate_delete_token(
            conn, user, await validators.validate_json_request(request)
        )
        deleted_tokens = await delete_tokens_func(conn, [target_role_id])

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        delete_actions = await user_actions_utils.build_token_delete_user_actions(
            actor_user=user,
            tokens=[DeleteTokenData.from_row(token) for token in deleted_tokens],
            request=request,
        )
        await user_actions_utils.add_user_actions(delete_actions)

    logger.info(
        'Token deleted',
        extra={
            'by_edrpou': user.company_edrpou,
            'by_role': user.role_id,
            'target_role': target_role_id,
        },
    )

    return web.json_response(status=204)


@api_handler
async def delete_tokens(request: web.Request, user: User) -> web.Response:
    async with request.app['db'].acquire() as conn:
        roles = await validate_delete_tokens(
            conn, user, await validators.validate_json_request(request)
        )
        tokens = await delete_tokens_func(conn, [role.id_ for role in roles])

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        delete_actions = await user_actions_utils.build_token_delete_user_actions(
            actor_user=user,
            tokens=[DeleteTokenData.from_row(token) for token in tokens],
            request=request,
        )
        await user_actions_utils.add_user_actions(delete_actions)

    data = {'deleted_tokens': [role.user_email for role in roles]}
    return web.json_response(data=data)


@login_required()
async def colbert_identify(request: web.Request, user: User) -> web.Response:
    """Identify user for Colbert service."""
    colbert_config = services.config.colbert

    if not colbert_config:
        raise Error(Code.error_500, status=500, reason=_('Colbert config not found'))

    ts = int(time.time())
    user_id = user.id
    sha1 = hashlib.sha1(
        '{}:{}:{}'.format(ts, colbert_config['secret'], user_id).encode('utf-8')
    ).hexdigest()

    return web.json_response({'ts': ts, 'id': user_id, 'sha1': sha1})


@redirect_to_app
async def login(request: web.Request) -> web.Response:
    """Check credentials and log user into current session."""

    async with services.db.acquire() as conn:
        raw_data = await validators.validate_json_request(request)

        ctx = await validate_login(
            conn=conn,
            data=raw_data,
            request=request,
        )

        response = await utils.login_user(
            request=request,
            conn=conn,
            user=ctx.user,
            remember=ctx.remember,
            first_factor=AuthFactor.email,
            ask_second_factor=True,
        )
        logger.info(
            'Login successful',
            extra={
                'user_id': ctx.user.id,
                'email': ctx.user.email,
            },
        )

    return json_response(data=response.to_dict())


async def logout(request: web.Request) -> web.Response:
    """Logout user from current session if any."""
    user_id = await get_user_id_from_request(request)
    if user_id:
        session = await aiohttp_session.get_session(request)

        await logout_user(request)
        async with request.app['db'].acquire() as conn:
            user = await select_base_user(conn, user_id=user_id, only_with_email=False)

        await session_utils.remove_active_user_session(app_services.redis, user_id, session)

        await concierge.sign_out()

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            assert user is not None
            await user_actions_utils.add_user_action(
                user_action=user_actions_types.UserAction(
                    action=user_actions_types.Action.logout_successful,
                    source=user_actions_utils.get_event_source(request),
                    user_id=user.id,
                    phone=user.auth_phone,
                    email=user.email,
                    extra={
                        'ip': get_client_ip(request),
                        'user_agent': request.headers.get('User-Agent'),
                    },
                )
            )

    response = web.json_response()
    concierge.delete_concierge_cookie(response)

    # NOTE: Additionally, cors_middleware attaches headers for CORS
    return response


async def base_get_hidden_phone(request: web.Request, user: BaseUser) -> web.Response:
    """Retrieve user phone in hidden format in order to show it on 2fa flow screen"""

    if not user.phone:
        raise InvalidRequest(reason=_('Користувач не має номеру телефона'))

    return web.json_response({'phone': generate_hidden_number(user.phone)})


@base_login_required(allow_2fa_unverified=True)
async def get_hidden_phone(request: web.Request, user: BaseUser | User) -> web.Response:
    """Retrieve user phone in hidden format in order to show it on 2fa flow screen"""
    return await base_get_hidden_phone(request, user)


async def base_get_hidden_email(request: web.Request, user: BaseUser) -> web.Response:
    """Retrieve user email in hidden format in order to show it on 2fa flow screen"""
    if not user.email:
        raise InvalidRequest(reason=_('Користувач не має email'))

    return web.json_response({'email': generate_hidden_email(user.email)})


@base_login_required(allow_2fa_unverified=True)
async def get_hidden_email(request: web.Request, user: BaseUser | User) -> web.Response:
    """Retrieve user email in hidden format in order to show it on 2fa flow screen"""
    return await base_get_hidden_email(request, user)


@base_login_required(allow_2fa_unverified=True)
async def verify_phone_2fa(request: web.Request, user: BaseUser | User) -> web.Response:
    """Verify TOTP code, received by user via SMS/Viber/Google Authenticator.

    If the key is valid allow user to access app, if not show the error.
    """
    valid_data = await validate_verify_phone_2fa(request, user=user)

    await two_factor.invalidate_phone_2fa_totp_web(request, user)

    async with services.db.acquire() as conn:
        await update_user(
            conn=conn,
            user_id=user.id,
            data={'is_phone_verified': True},
        )

    next_url = await get_next_url_from_request(request, NEXT_URL_VERIFY_2FA_PREFIX)
    response = web.json_response({'next_url': next_url})

    if valid_data.trusted:
        two_factor.trust_device_for_2fa_web(user, response=response)

    return response


@base_login_required(allow_2fa_unverified=True)
async def verify_email_2fa(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Verify second factor email and password when first factor is phone
    """
    async with services.db.acquire() as conn:
        data = await validate_verify_email_2fa(request, conn, user=user)

    # Remove pending 2FA status to allow user to access app
    session = await aiohttp_session.get_session(request)
    two_factor.delete_pending_2fa_web(session)

    next_url = await get_next_url_from_request(request, NEXT_URL_VERIFY_2FA_PREFIX)
    response = web.json_response({'next_url': next_url})
    if data.trusted:
        two_factor.trust_device_for_2fa_web(user, response=response)

    return response


@base_login_required(allow_2fa_unverified=True)
async def send_email_2fa_token(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Send link to confirm 2fa by email
    """
    await validate_send_email_2fa_token(request, user)

    await two_factor.send_email_2fa_token_web(user)

    return web.Response()


@base_login_required(allow_2fa_unverified=True)
async def verify_email_2fa_token(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Confirm 2FA link sent to user's email
    """
    await validate_verify_email_2fa_token(request, user)

    await two_factor.reset_email_2fa_token(user)

    # Remove pending 2FA status to allow user to access app
    session = await aiohttp_session.get_session(request)
    two_factor.delete_pending_2fa_web(session)

    next_url = await get_next_url_from_request(request, NEXT_URL_VERIFY_2FA_PREFIX)
    return web.json_response({'next_url': next_url})


@base_login_required(allow_2fa_unverified=True)
async def resend_phone_2fa_code(request: web.Request, user: BaseUser | User) -> web.Response:
    await validate_rate_limit(
        key=f'resend_phone_2fa_code:{user.id}',
        limit=1,
        delta=ONE_MINUTE_DELTA,
    )
    await validate_pending_2fa_web(
        request=request,
        second_factor=AuthFactor.phone,
    )

    async with services.db.acquire() as conn:
        await two_factor.start_phone_2fa_web(
            request=request,
            conn=conn,
            user=user,
            first_factor=AuthFactor.email,
        )
    return web.json_response()


@base_login_required()
async def logout_all_sessions(request: web.Request, user: User | BaseUser) -> web.Response:
    session = await aiohttp_session.get_session(request)
    current_session_id = session.identity
    async with services.db.acquire() as conn:
        ctx = await validate_logout_all_sessions(
            conn=conn,
            user=user,
            raw_data=await validators.validate_json_request(request),
        )

        exclude_sessions: list[str] | None = None
        if not ctx.terminate_current_session and current_session_id:
            exclude_sessions = [current_session_id]

        counter = await utils.logout_all_sessions(
            conn,
            user_id=ctx.logout_user_id,
            with_web=True,
            with_mobile=ctx.with_mobile,
            with_concierge=True,
            web_sessions_exclude=exclude_sessions,
        )

    if user.id == ctx.logout_user_id and ctx.terminate_current_session:
        await logout_user(request)

    # register only for users with some role
    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION) and is_wide_user_type(user):
        await user_actions_utils.add_user_action(
            user_action=user_actions_types.UserAction(
                action=user_actions_types.Action.logout_successful,
                source=user_actions_utils.get_event_source(request),
                user_id=user.id,
                phone=user.auth_phone,
                email=user.email,
                company_id=user.company_id,
                extra={'terminated_sessions_count': counter},
            )
        )

    return web.Response()


@login_required()
async def logout_session(request: web.Request, user: User) -> web.Response:
    """
    Logout user from the specific session.
    """

    async with services.db.acquire() as conn:
        logout_session_id = await validate_logout_session(
            conn=conn,
            user=user,
            raw_data=await validators.validate_json_request(request),
        )
    await services.redis.delete(get_session_redis_key(logout_session_id))

    # User may log out from the current session
    # To avoid session saving after request finished in middleware
    # mark current session as not active.
    session = await aiohttp_session.get_session(request)
    if logout_session_id == session.identity:
        await destroy_session(session)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await user_actions_utils.add_user_action(
            user_action=user_actions_types.UserAction(
                action=user_actions_types.Action.logout_successful,
                source=user_actions_utils.get_event_source(request),
                user_id=user.id,
                phone=user.auth_phone,
                email=user.email,
                company_id=user.company_id,
            )
        )

    return web.Response()


async def google_auth(request: web.Request) -> web.Response:
    """
    Handle login/registration by Google.

    More information:
    https://developers.google.com/identity/gsi/web/guides/overview
    """
    google_auth = await validate_google_auth(request=request)
    async with app_services.db.acquire() as conn:
        response = await utils.google_auth(
            request=request,
            conn=conn,
            raw_token=google_auth['token'],
            registration_source=google_auth.get('source'),
            invite_email=google_auth.get('invite_email'),
        )

    return json_response(response.to_dict())


async def microsoft_auth(request: web.Request) -> web.Response:
    """
    Handle login/registration by Microsoft.

    More information:
    https://learn.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow#redirect-uris-for-single-page-apps-spas

    Generate testing token:
    - https://github.com/Azure-Samples/ms-identity-javascript-v2

    Why we need access_token AND id_token:
    - access_token is used to get user info from Microsoft Graph API
    - id_token is used to verify that user is authenticated by Microsoft and issued for our app

    access token might be not jwt token [1], but id_token is always jwt token [2].

    1. https://learn.microsoft.com/en-us/entra/identity-platform/access-tokens
    2. https://learn.microsoft.com/en-us/entra/identity-platform/id-tokens
    """
    microsoft_auth = await validate_microsoft_auth(request=request)
    async with app_services.db.acquire() as conn:
        response = await utils.microsoft_auth(
            request=request,
            conn=conn,
            raw_access_token=microsoft_auth['access_token'],
            raw_id_token=microsoft_auth['id_token'],
            registration_source=microsoft_auth.get('source'),
            invite_email=microsoft_auth.get('invite_email'),
        )

    return json_response(response.to_dict())


async def apple_auth(request: web.Request) -> web.Response:
    """
    Handle login/registration by Apple.
    """

    ctx = await get_user_with_apple(request=request)

    async with services.db.acquire() as conn:
        response = await login_user(
            request=request,
            conn=conn,
            user=ctx.user,
            remember=True,
            first_factor=AuthFactor.email,
            ask_second_factor=ctx.challenge_2fa,
        )

        if registration_ctx := ctx.registration_ctx:
            redirect_url = registration_ctx.redirect_url or response.next_url
            if not ctx.user.email_confirmed:
                await send_confirmation_email(
                    user_id=ctx.user.id,
                    email=registration_ctx.email,
                    redirect_url=redirect_url,
                )
            else:
                #  Send any necessary jobs related to the registration and email
                #  confirmation process
                await send_registration_jobs(
                    user=ctx.user,
                    cookies=request.cookies,
                )
                await send_confirm_email_jobs(user=ctx.user)
                await send_user_welcome_email(user=ctx.user)
            return web.json_response(
                data={
                    **response.to_dict(),
                    'next_url': redirect_url,
                    'flow': AuthFlow.registration,
                }
            )

    return web.json_response(
        data={
            **response.to_dict(),
            'flow': AuthFlow.login,
        }
    )


@openapi_docs(
    summary=_('Оновити дозволи для компанії'),
    request_json=ChangeCompanyPermissionsSchema,
)
@login_required()
async def update_company_secure_permissions(request: web.Request, user: User) -> web.Response:
    """
    Update company security permissions by admin.
    """

    validate_user_permission(user, {'can_edit_security'})
    raw_json = await validate_json_request(request)
    data = validators.validate_pydantic(ChangeCompanyPermissionsSchema, raw_json)
    current_ip = get_client_ip(request)
    if (
        data.allowed_ips
        and current_ip
        and current_ip not in data.allowed_ips
        and not data.force_ip_update
    ):
        raise InvalidRequest(
            reason=_(
                'Новий список IP-адрес не містить поточної IP-адреси.'
                'Переконайтеся, що у вас є доступ до системи з новими IP-адресами.'
            ),
            data={
                'allowed_ips': data.allowed_ips,
                'current_ip': current_ip,
            },
        )

    async with services.db.acquire() as conn:
        await update_company_by_edrpou(
            conn=conn,
            edrpou=user.company_edrpou,
            data=data.to_dict(),
        )
    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        changes = data.to_dict()
        actions = [
            user_actions_types.UserAction(
                action=user_actions_types.Action.company_update,
                source=user_actions_utils.get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={'changed_field': field, 'new_value': value},
            )
            for field, value in changes.items()
        ]
        if actions:
            await user_actions_utils.add_user_actions(actions)

    return web.json_response()
