from __future__ import annotations

import datetime
import logging
from collections.abc import Iterable
from dataclasses import dataclass
from typing import (
    Any,
)

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, insert
from sqlalchemy.sql import ClauseElement, Selectable
from typing_extensions import deprecated

from api.enums import Vendor
from api.private.super_admin.db import insert_super_admin_action
from api.private.super_admin.tables import super_admin_permissions_table
from app.auth import helpers
from app.auth.enums import (
    InviteSource,
    RoleStatus,
    StatEntity,
    UserEmailChangeStatus,
)
from app.auth.schemas import CompanyConfig
from app.auth.tables import (
    active_role_company_join,
    company_config_table,
    company_meta_table,
    company_statistic_table,
    company_table,
    is_active_filter,
    role_company_join,
    role_table,
    token_table,
    user_active_role_company_join,
    user_email_change_table,
    user_meta_table,
    user_role_company_join,
    user_role_company_strict_join,
    user_role_company_token_join,
    user_table,
    zvit_users_table,
)
from app.auth.types import (
    AuthUser,
    AuthUserExtended,
    BaseUser,
    Company,
    CompanyConfigRow,
    CompanyForIndexation,
    InsertBaseUserDict,
    InsertRoleDict,
    LastRoleDetails,
    Role,
    RoleDB,
    RoleForIndexation,
    SuperAdminPermissions,
    UpdateBaseUserDict,
    UpdateRoleDict,
    User,
    UserEmailChange,
    ZvitUser,
    to_role,
)
from app.esputnik.enums import Event
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import local_now
from app.lib.enums import SuperAdminActionType, UserRole
from app.lib.helpers import (
    deep_merge_dicts,
    remove_keys_from_dict,
)
from app.lib.types import (
    DataDict,
    StrList,
)
from app.models import (
    count,
    exists,
    select_all,
    select_one,
)
from app.reviews.tables import review_table

logger = logging.getLogger(__name__)

COMPANY_NOT_UPDATABLE_FIELDS = {'id', 'edrpou', 'is_legal', 'date_created'}

SELECT_ROLE_FIELDS = (
    role_table,
    company_table.c.edrpou.label('company_edrpou'),
    company_table.c.name.label('company_name'),
    company_table.c.full_name.label('company_full_name'),
    company_table.c.ipn.label('company_ipn'),
    company_table.c.is_legal,
    company_table.c.is_dealer,
    user_table.c.email.label('user_email'),
)

SELECT_ROLE_QUERY = sa.select(SELECT_ROLE_FIELDS).select_from(user_role_company_join)


# types: AuthUser, User
# tables: user_table, role_table, company_table
USER_COLUMNS = [
    user_table,
    role_table.c.id.label('role_id'),
    role_table.c.can_view_document,
    role_table.c.can_comment_document,
    role_table.c.can_upload_document,
    role_table.c.can_download_document,
    role_table.c.can_print_document,
    role_table.c.can_delete_document,
    role_table.c.can_sign_and_reject_document,
    role_table.c.can_sign_and_reject_document_internal,
    role_table.c.can_sign_and_reject_document_external,
    role_table.c.can_invite_coworkers,
    role_table.c.can_edit_company,
    role_table.c.can_edit_roles,
    role_table.c.can_create_tags,
    role_table.c.can_edit_document_automation,
    role_table.c.can_edit_document_fields,
    role_table.c.can_edit_document_category,
    role_table.c.can_extract_document_structured_data,
    role_table.c.can_edit_document_structured_data,
    role_table.c.can_archive_documents,
    role_table.c.can_delete_archived_documents,
    role_table.c.can_edit_templates,
    role_table.c.can_edit_directories,
    role_table.c.can_remove_itself_from_approval,
    role_table.c.can_edit_security,
    role_table.c.can_download_actions,
    role_table.c.can_change_document_signers_and_reviewers,
    role_table.c.can_delete_document_extended,
    role_table.c.can_edit_company_contact,
    role_table.c.can_edit_required_fields,
    role_table.c.can_view_private_document,
    role_table.c.can_view_coworkers,
    role_table.c.can_receive_inbox,
    role_table.c.can_receive_inbox_as_default,
    role_table.c.can_receive_comments,
    role_table.c.can_receive_rejects,
    role_table.c.can_receive_reminders,
    role_table.c.can_receive_reviews,
    role_table.c.can_receive_review_process_finished,
    role_table.c.can_receive_review_process_finished_assigner,
    role_table.c.can_receive_sign_process_finished,
    role_table.c.can_receive_sign_process_finished_assigner,
    role_table.c.can_receive_notifications,
    role_table.c.can_receive_access_to_doc,
    role_table.c.can_receive_delete_requests,
    role_table.c.can_receive_new_roles,
    role_table.c.can_receive_token_expiration,
    role_table.c.can_receive_email_change,
    role_table.c.can_receive_admin_role_deletion,
    role_table.c.has_few_signatures,
    role_table.c.has_few_reviews,
    role_table.c.has_hrs_role,
    role_table.c.is_counted_in_billing_limit,
    role_table.c.allowed_ips.label('role_allowed_ips'),
    role_table.c.allowed_api_ips.label('role_allowed_api_ips'),
    role_table.c.sort_documents.label('role_sort_documents'),
    role_table.c.show_child_documents,
    role_table.c.user_role,
    role_table.c.position,
    role_table.c.status.label('role_status'),
    role_table.c.is_default_recipient,
    company_table.c.id.label('company_id'),
    company_table.c.edrpou.label('company_edrpou'),
    company_table.c.name.label('company_name'),
    company_table.c.full_name.label('company_full_name'),
    company_table.c.email_domains.label('company_email_domains'),
    company_table.c.allowed_ips.label('company_allowed_ips'),
    company_table.c.allowed_api_ips.label('company_allowed_api_ips'),
    company_table.c.is_legal,
]

ExtraJoin = tuple[Any, Any]


@dataclass(frozen=True)
class SelectUsersBulkInput:
    email: str
    company_id: str


async def count_user_roles(
    conn: DBConnection, user_id: str, status: RoleStatus | None = None
) -> int:
    """Count how many roles exists for given user ID."""
    clause = sa.and_(role_table.c.user_id == user_id)
    if status:
        clause = sa.and_(clause, role_table.c.status == status)
    return await count(conn, role_table, clause)


async def count_company_roles_for_billing(
    conn: DBConnection,
    company_id: str,
) -> int:
    clause = sa.and_(
        role_table.c.company_id == company_id,
        role_table.c.is_counted_in_billing_limit.is_(True),
        role_table.c.status == RoleStatus.active,
    )
    return await count(conn, role_table, clause)


async def count_company_roles(conn: DBConnection, company_id: str, only_active: bool = True) -> int:
    clause = role_table.c.company_id == company_id
    if only_active:
        clause = sa.and_(clause, role_table.c.status == RoleStatus.active)
    return await count(conn, role_table, clause)


async def count_company_roles_group_by_company(
    conn: DBConnection, company_ids: set[str], only_active: bool = True
) -> DataDict:
    clause = role_table.c.company_id.in_(company_ids)
    if only_active:
        clause = sa.and_(clause, role_table.c.status == RoleStatus.active)

    query = (
        sa.select([role_table.c.company_id, sa.func.count()])
        .where(clause)
        .group_by(role_table.c.company_id)
    )

    rows = await select_all(conn, query)
    return {row[0]: row[1] for row in rows}


async def decrement_company_upload_documents_left(
    conn: DBConnection, company_id: str, value: int = 1
) -> None:
    await update_company_upload_documents_left(conn, company_id, -value)


async def delete_tokens(conn: DBConnection, role_ids: list[str]) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            token_table.delete().where(token_table.c.role_id.in_(role_ids)).returning(token_table)
        ),
    )


async def delete_user_tokens(conn: DBConnection, user_id: str) -> None:
    ids = await select_all(
        conn=conn,
        query=(
            sa.select([role_table.c.id])
            .select_from(role_table)
            .where(role_table.c.user_id == user_id)
        ),
    )
    await conn.execute(token_table.delete().where(token_table.c.role_id.in_([r.id for r in ids])))


async def deny_role(conn: DBConnection, role_id: str) -> None:
    """
    TAG: role_deactivation
    """
    await update_role(
        conn=conn,
        role_id=role_id,
        data={'status': RoleStatus.denied},
    )


async def exist_admin_in_company(conn: DBConnection, company_id: str) -> bool:
    return await exists(
        conn,
        role_table,
        sa.and_(
            role_table.c.company_id == company_id,
            role_table.c.status == RoleStatus.active,
            role_table.c.user_role == UserRole.admin.value,
        ),
    )


async def exist_roles(
    conn: DBConnection,
    *,
    user_id: str | None = None,
    company_id: str | None = None,
    company_edrpou: str | None = None,
    exclude_role_id: str | None = None,
    role_status: RoleStatus | None = RoleStatus.active,
) -> bool:
    """Check if company coworkers exists"""
    filters = []

    if user_id is not None:
        filters.append(role_table.c.user_id == user_id)

    # Filter by role status
    if role_status:
        filters.append(role_table.c.status == role_status)

    # Exclude some roles (typically current role)
    if exclude_role_id is not None:
        filters.append(role_table.c.id != exclude_role_id)

    # Filter by company ID
    if company_id is not None:
        filters.append(role_table.c.company_id == company_id)

    # Filter by company EDRPOU
    if company_edrpou is not None:
        filters.append(
            sa.exists()
            .select_from(company_table)
            .where(
                sa.and_(
                    company_table.c.edrpou == company_edrpou,
                    company_table.c.id == role_table.c.company_id,
                ),
            )
        )

    assert filters, 'Provide at least one filter'

    return await exists(
        conn=conn,
        select_from=role_table,
        clause=sa.and_(*filters),
    )


async def select_is_company_registered(conn: DBConnection, *, edrpou: str) -> bool:
    """Check whether company is registered or not (has at least one active role)."""

    registered_edrpous = await select_registered_companies_edrpous(conn, companies_edrpous=[edrpou])
    return edrpou in registered_edrpous


async def select_registered_companies_edrpous(
    conn: DBConnection,
    *,
    companies_edrpous: list[str],
) -> set[str]:
    """
    Check whether companies are registered or not (has at least one active role)
    """

    # Does an active role exist for each company?
    is_active_role_exists = sa.exists(
        sa.select([sa.literal(1)])
        .select_from(
            role_table.join(
                user_table,
                role_table.c.user_id == user_table.c.id,
            )
        )
        .where(sa.and_(is_active_filter, role_table.c.company_id == company_table.c.id))
    )

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.edrpou])
            .select_from(company_table)
            .where(
                sa.and_(
                    company_table.c.edrpou.in_(companies_edrpous),
                    company_table.c.is_legal.is_(True),
                    is_active_role_exists,
                )
            )
        ),
    )
    return {row.edrpou for row in rows}


async def exists_company_by_edrpou(
    conn: DBConnection,
    edrpou: str,
    *,
    is_legal: bool = True,
) -> bool:
    """Check whether company exists by given EDRPOU or not."""
    clause = sa.and_(company_table.c.edrpou == edrpou, company_table.c.is_legal.is_(is_legal))
    select_from = company_table

    return await exists(conn, select_from, clause)


async def exists_company_by_id(
    conn: DBConnection, company_id: str, *, user_id: str | None = None
) -> bool:
    select_from = company_table
    clause = company_table.c.id == company_id

    if user_id:
        select_from = user_active_role_company_join
        clause = sa.and_(clause, user_table.c.id == user_id)

    return await exists(conn, select_from, clause)


async def exists_role_by_id(
    conn: DBConnection,
    role_id: str,
    *,
    company_id: str | None = None,
) -> bool:
    """Check whether role exists in database by given ID."""
    select_from = role_table
    clause = role_table.c.id == role_id

    if company_id:
        clause = sa.and_(clause, role_table.c.company_id == company_id)

    return await exists(
        conn=conn,
        select_from=select_from,
        clause=clause,
    )


async def exists_role_by_user_id(
    conn: DBConnection,
    role_id: str,
    user_id: str,
    role_status: RoleStatus | None = None,
) -> bool:
    select_from = role_table
    clause = sa.and_(role_table.c.id == role_id, role_table.c.user_id == user_id)
    if role_status:
        clause = sa.and_(clause, role_table.c.status == role_status)
    return await exists(conn, select_from, clause)


async def exists_token_by(conn: DBConnection, clause: ClauseElement) -> bool:
    return await exists(conn, token_table, clause)


async def is_user_email_exists(
    conn: DBConnection,
    email: str,
    *,
    exclude_placeholder: bool = True,
) -> bool:
    """
    Check whether user exists by given email or not
    """
    filters = [user_table.c.email == email]
    if exclude_placeholder:
        filters.append(user_table.c.is_placeholder.isnot(True))

    return await exists(
        conn=conn,
        select_from=user_table,
        clause=sa.and_(*filters),
    )


async def exists_user_by_id(conn: DBConnection, user_id: str) -> bool:
    return await exists(conn, user_table, user_table.c.id == user_id)


async def increment_company_upload_documents_left(
    conn: DBConnection,
    company_id: str,
    payer_id: str,
    uploader_company_id: str | None,
    value: int = 1,
) -> None:
    if company_id != payer_id and company_id == uploader_company_id:
        await update_company_upload_documents_left(conn, company_id, value)


def _prepare_user_for_database_insert(user: InsertBaseUserDict) -> DataDict:
    user_data: DataDict = user.copy()  # type: ignore

    # Generate password hash from raw string
    password_raw: str | None = user_data['password']
    if password_raw is not None:
        password_hash = helpers.generate_password_hash(password_raw)
        user_data['password'] = password_hash

    # Default values
    user_data.setdefault('phone', None)
    user_data.setdefault('date_updated', sa.text('now()'))
    return user_data


async def insert_user(
    conn: DBConnection,
    data: InsertBaseUserDict,
) -> BaseUser:
    """
    Insert a new User instance into a database.

    After insertion is done, return new user data from DB via additional hit.

    WARN: Use "create_user" instead of this function in new code to create a user it have
    additional logic to update user in Vchasno Profile.
    """
    insert_data = _prepare_user_for_database_insert(user=data)

    row = await select_one(
        conn=conn,
        query=user_table.insert().values(insert_data).returning(user_table),
    )
    return BaseUser.from_row(row)


async def insert_company(
    conn: DBConnection, data: DataDict, *, ignore_existing: bool = False
) -> str:
    """Insert new Company instance into database.

    If ``ignore_existing`` enabled - ignore when Company with given EDRPOU
    already exists in database.
    """
    data.setdefault('date_updated', sa.text('now()'))
    data.setdefault('is_legal', True)

    query = insert(company_table).values(data).returning(company_table.c.id)
    if ignore_existing:
        query = query.on_conflict_do_nothing()

    # Company inserted in database - return its ID
    company_id = await conn.scalar(query)
    if company_id:
        return company_id

    # Otherwise company already in database, but need to be queried to fetch
    # its ID, cause RETURNING id does not return its value on previous step
    return await conn.scalar(
        sa.select([company_table.c.id]).where(
            sa.and_(
                company_table.c.edrpou == data['edrpou'],
                company_table.c.is_legal.is_(data['is_legal']),
            )
        )
    )


async def upsert_company(conn: DBConnection, data: DataDict) -> Company:
    """Insert or Update new Company instance into database"""
    data.setdefault('date_updated', sa.text('now()'))
    data.setdefault('is_legal', True)

    # Company inserted in database - return its data
    query = insert(company_table).values(data).returning(company_table)

    update_data = remove_keys_from_dict(data, COMPANY_NOT_UPDATABLE_FIELDS)
    if update_data:
        query = query.on_conflict_do_update(
            index_elements=[company_table.c.edrpou, company_table.c.is_legal],
            set_=update_data,
        )

    row = await select_one(conn, query)
    return Company.from_row(row)


async def upsert_role(
    conn: DBConnection,
    insert_data: InsertRoleDict,
    update_data: UpdateRoleDict,
) -> DBRow:
    """
    Insert or Update new Role instance into database

    Before using this function, make sure that data for insertion and update contains proper
    permissions and notifications settings based on company config.
    """
    insert_data.setdefault('user_role', UserRole.user.value)  # ???
    insert_data.setdefault('date_updated', sa.text('now()'))

    query = insert(role_table).values(insert_data).returning(role_table)

    if update_data:
        query = query.on_conflict_do_update(
            index_elements=[role_table.c.user_id, role_table.c.company_id],
            set_=update_data,
        )

    return await select_one(conn, query)


async def insert_role(conn: DBConnection, data: InsertRoleDict) -> DBRow:
    """
    Connect User & Company instances by creating new Role.

    WARNING: before using this function, make sure that data for insertion contains proper
    permissions and notifications settings based on company config:
     - invited by coworker — permissions based on config.default_role_permissions
     - registered by key — permissions based on config.key_default_role_permissions
    Use "create_coworker_role" whenever is possible.
    """

    return await select_one(conn, role_table.insert().values(data).returning(role_table))


async def insert_roles(conn: DBConnection, data: list[InsertRoleDict]) -> list[DBRow]:
    """
    Create multiple roles in a database at once.

    WARNING: before using this function, make sure that data for insertion contains proper
    permissions and notifications settings based on company config:
     - invited by coworker — permissions based on config.default_role_permissions
     - registered by key — permissions based on config.key_role_permissions
    Use "create_coworker_roles" whenever is possible.
    """
    if not data:
        return []

    return await select_all(conn, (role_table.insert().values(data).returning(role_table.c.id)))


@deprecated('Vendor is deprecated field')
def _get_vendor(data: DataDict) -> str:
    return data.get('vendor') or Vendor.onec.value


async def insert_token(conn: DBConnection, data: DataDict) -> str:
    token = helpers.generate_api_token()

    data['token_hash'] = helpers.generate_hash_sha512(token)
    data['vendor'] = _get_vendor(data)

    await conn.execute(insert(token_table).values(data))
    return token


async def insert_tokens(
    conn: DBConnection,
    roles: list[Role],
    date_expired: datetime.datetime | None,
    vendor: Vendor | None = None,
) -> DataDict:
    data = []
    roles_tokens_mapping: dict[str, str] = {}
    if not roles:
        return roles_tokens_mapping
    for role in roles:
        token = helpers.generate_api_token()
        token_hash = helpers.generate_hash_sha512(token)
        data.append(
            {
                'role_id': role.id_,
                'token_hash': token_hash,
                'vendor': vendor or Vendor.onec.value,
                'date_expired': date_expired,
            }
        )
        roles_tokens_mapping[role.id_] = token

    await conn.execute(insert(token_table).values(data))
    return roles_tokens_mapping


async def select_role_ids_with_tokens(conn: DBConnection, roles_ids: StrList) -> set[str]:
    tokens = await select_all(
        conn=conn,
        query=(
            sa.select([token_table.c.role_id])
            .select_from(token_table)
            .where(token_table.c.role_id.in_(roles_ids))
        ),
    )
    return {token.role_id for token in tokens}


async def select_tokens_for_graph(
    conn: DBConnection, roles_ids: StrList, user: User
) -> list[DBRow]:
    selectable = [token_table.c.token_hash, token_table.c.role_id]

    if not user.role_id:
        return []

    # Non admin user can access only own token
    if not user.is_admin:
        _roles_ids = set(roles_ids).intersection({user.role_id})
        return await select_all(
            conn=conn,
            query=(sa.select(selectable).where(token_table.c.role_id.in_(_roles_ids))),
        )

    # Admin user can access tokens only in current company
    return await select_all(
        conn=conn,
        query=(
            sa.select(selectable)
            .select_from(token_table.join(role_table, role_table.c.id == token_table.c.role_id))
            .where(
                sa.and_(
                    token_table.c.role_id.in_(roles_ids),
                    role_table.c.company_id == user.company_id,
                )
            )
        ),
    )


async def select_companies_by_edrpou(
    conn: DBConnection,
    edrpous: list[str],
    *,
    selectable: list[Any] | None = None,
) -> list[DBRow]:
    if selectable is None:
        selectable = [company_table]

    return await select_all(
        conn, (sa.select(selectable).where(company_table.c.edrpou.in_(edrpous)))
    )


async def select_companies_by_ids(
    conn: DBConnection,
    companies_ids: list[str],
    *,
    selectable: list[Any] | None = None,
) -> list[DBRow]:
    if not companies_ids:
        return []

    selectable = selectable or [company_table]

    return await select_all(
        conn, (sa.select(selectable).where(company_table.c.id.in_(companies_ids)))
    )


async def select_company_by_edrpou(conn: DBConnection, edrpou: str) -> DBRow | None:
    """Select company by given EDRPOU / legal flag."""
    results = await select_companies_by_edrpou(conn, [edrpou])
    if not results:
        return None
    result: DBRow = results[0]
    return result


async def select_company_by_id(
    conn: DBConnection,
    company_id: str,
    *,
    selectable: list[Selectable] | None = None,
) -> DBRow | None:
    selectable = selectable or [company_table]
    return await select_one(conn, (sa.select(selectable).where(company_table.c.id == company_id)))


async def select_company_by_role_id(conn: DBConnection, role_id: str) -> DBRow | None:
    return await select_one(
        conn,
        (
            sa.select([company_table])
            .select_from(role_company_join)
            .where(role_table.c.id == role_id)
        ),
    )


async def select_company(
    conn: DBConnection,
    *,
    company_id: str | None = None,
    edrpou: str | None = None,
    role_id: str | None = None,
    is_legal: bool | None = True,
) -> Company | None:
    """Select optional company object"""

    filters = []

    # Company filters
    if company_id is not None:
        filters.append(company_table.c.id == company_id)
    if edrpou is not None:
        filters.append(company_table.c.edrpou == edrpou)
    if is_legal is not None:
        filters.append(company_table.c.is_legal == is_legal)

    # Role filters
    if role_id is not None:
        filters.append(
            sa.exists()
            .select_from(role_table)
            .where(
                sa.and_(
                    role_table.c.company_id == company_table.c.id,
                    role_table.c.id == role_id,
                )
            )
        )

    query = sa.select([company_table]).where(sa.and_(*filters))
    row = await select_one(conn, query)
    return Company.from_row(row) if row else None


async def select_expected_company(conn: DBConnection, *, company_id: str) -> Company:
    """
    Get the company in context where we sure that it exists in database.

    In other contexts, like in validators, use validate_company_exists instead.
    """
    company = await select_company(conn, company_id=company_id)
    assert company, 'Company not found'
    return company


def _build_company_config(company_config: DBRow | None) -> CompanyConfig:
    """
    It is helper function, use it carefully.
    In most cases you should use app.auth.utils.get_company_config instead of this.
    """
    config = company_config.config if company_config else {}
    admin_config = company_config.admin_config if company_config else {}
    return CompanyConfig(**deep_merge_dicts(config, admin_config))


async def select_companies_configs_by_edrpous(
    conn: DBConnection,
    *,
    companies_edrpous: list[str],
) -> dict[str, CompanyConfig]:
    """
    Select companies configs by given companies EDRPOUs.
    """
    configs = await select_all(
        conn=conn,
        query=(
            sa.select([company_config_table, company_table.c.edrpou])
            .select_from(
                company_config_table.join(
                    company_table,
                    company_config_table.c.company_id == company_table.c.id,
                )
            )
            .where(company_table.c.edrpou.in_(companies_edrpous))
        ),
    )
    return {config.edrpou: _build_company_config(config) for config in configs}


async def select_companies_configs_rows(
    conn: DBConnection,
    *,
    companies_edrpous: list[str] | None = None,
) -> list[CompanyConfigRow]:
    """
    Select companies configs database rows by given filters
    """

    filters = []
    if companies_edrpous is not None:
        filters.append(
            sa.exists()
            .select_from(company_table)
            .where(
                sa.and_(
                    company_table.c.id == company_config_table.c.company_id,
                    company_table.c.edrpou.in_(companies_edrpous),
                )
            )
        )

    assert filters, 'Provide at least one filter'

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([company_config_table])
            .select_from(company_config_table)
            .where(sa.and_(*filters))
        ),
    )

    results: list[CompanyConfigRow] = []
    for row in rows:
        results.append(
            CompanyConfigRow(
                company_id=row.company_id,
                config=_build_company_config(row),
            )
        )

    return results


async def select_companies_configs_by_ids(
    conn: DBConnection,
    *,
    companies_ids: list[str],
) -> dict[str, CompanyConfig]:
    """
    Select companies configs by given companies IDs.
    """
    configs = await select_all(
        conn=conn,
        query=(
            sa.select([company_config_table]).where(
                company_config_table.c.company_id.in_(companies_ids)
            )
        ),
    )
    return {config.company_id: _build_company_config(config) for config in configs}


async def select_companies_configs_by_roles_ids(
    conn: DBConnection,
    *,
    roles_ids: list[str],
) -> dict[str, CompanyConfig]:
    """
    Select companies configs by given role IDs (for graph).
    """
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    company_config_table.c.config,
                    company_config_table.c.admin_config,
                    role_table.c.id.label('role_id'),
                ]
            )
            .select_from(
                role_table.join(
                    company_config_table,
                    sa.and_(
                        role_table.c.company_id == company_config_table.c.company_id,
                        role_table.c.status == RoleStatus.active,
                    ),
                )
            )
            .where(role_table.c.id.in_(roles_ids))
        ),
    )

    return {row.role_id: _build_company_config(row) for row in rows}


async def select_company_config(
    conn: DBConnection,
    *,
    company_id: str | None = None,
    company_edrpou: str | None = None,
) -> CompanyConfig:
    """
    Select company config by given company id or EDRPOU.
    """

    # In case if no company identity filter provided, return defaults
    if not (company_id or company_edrpou):
        logger.warning('No company identity filter provided, returning defaults')
        return _build_company_config(company_config=None)

    assert company_id or company_edrpou, 'At least identity filter must be provided'

    filters = []
    if company_id:
        filters.append(company_config_table.c.company_id == company_id)

    if company_edrpou:
        filters.append(
            sa.exists()
            .select_from(company_table)
            .where(
                sa.and_(
                    company_table.c.id == company_config_table.c.company_id,
                    company_table.c.edrpou == company_edrpou,
                )
            )
        )

    query = sa.select([company_config_table]).where(sa.and_(*filters))
    company_config = await select_one(conn, query)

    return _build_company_config(company_config)


async def select_company_edrpou_by_role_id(conn: DBConnection, role_id: str) -> str | None:
    return await conn.scalar(
        sa.select([company_table.c.edrpou])
        .select_from(role_company_join)
        .where(role_table.c.id == role_id)
    )


async def select_company_id(conn: DBConnection, edrpou: str, is_legal: bool = True) -> str | None:
    return await conn.scalar(
        sa.select([company_table.c.id]).where(
            sa.and_(company_table.c.edrpou == edrpou, company_table.c.is_legal.is_(is_legal))
        )
    )


async def select_company_id_by_role_id(conn: DBConnection, role_id: str) -> str | None:
    row = await conn.scalar(sa.select([role_table.c.company_id]).where(role_table.c.id == role_id))
    return row or None


async def select_coworkers(
    conn: DBConnection, company_id: str, clause: ClauseElement | None = None
) -> list[DBRow]:
    company_clause = company_table.c.id == company_id
    clause = company_clause if clause is None else sa.and_(company_clause, clause)

    return await select_all(
        conn,
        (
            sa.select(
                [
                    user_table.c.id,
                    user_table.c.email,
                    role_table.c.id.label('role_id'),
                    role_table.c.user_role,
                    role_table.c.can_receive_inbox,
                    role_table.c.can_receive_comments,
                    role_table.c.can_receive_rejects,
                    role_table.c.can_receive_reminders,
                    role_table.c.can_receive_reviews,
                    role_table.c.can_receive_review_process_finished,
                    role_table.c.can_receive_review_process_finished_assigner,
                    role_table.c.can_receive_sign_process_finished,
                    role_table.c.can_receive_sign_process_finished_assigner,
                    role_table.c.can_receive_notifications,
                    role_table.c.can_receive_access_to_doc,
                    role_table.c.can_receive_delete_requests,
                ]
            )
            .select_from(user_active_role_company_join)
            .where(clause)
        ),
    )


async def select_exists_users_emails(conn: DBConnection, emails: list[str]) -> list[DBRow]:
    return await select_all(
        conn,
        (
            sa.select([user_table.c.email])
            .select_from(user_table)
            .where(user_table.c.email.in_(emails))
        ),
    )


async def select_last_role_details(
    conn: DBConnection,
    user: AuthUser | AuthUserExtended | BaseUser,
) -> LastRoleDetails:
    """
    Return last role details if they exists
    """
    role_id: str | None = None
    if isinstance(user, AuthUserExtended):
        role_id = user.last_role_id or user.role_id
    elif isinstance(user, AuthUser):
        role_id = user.role_id
    elif isinstance(user, User):
        role_id = user.last_role_id or user.role_id
    elif isinstance(user, BaseUser):
        role_id = user.last_role_id

    if role_id:
        role = await select_role_by_id(conn, role_id)
        if role:
            return LastRoleDetails(
                role_id=role.id_,
                company_edrpou=role.company_edrpou,
                company_id=role.company_id,
            )

    return LastRoleDetails(role_id=None, company_id=None, company_edrpou=None)


async def select_roles(
    conn: DBConnection,
    *,
    roles_ids: Iterable[str] | None = None,
    role_status: RoleStatus | None = None,
    user_id: str | None = None,
    user_email: str | None = None,
    user_emails: list[str] | None = None,
    company_id: str | None = None,
    company_edrpous: list[str] | None = None,
    can_receive_finished_docs: bool | None = None,
    date_created_gte: datetime.datetime | None = None,
    only_active: bool = False,
    offset: int = 0,
    limit: int = 0,
    order: list[Any] | None = None,
) -> list[Role]:
    """Select list of roles"""

    where = sa.true()

    # Filters by roles table
    if roles_ids:
        where = sa.and_(where, role_table.c.id.in_(roles_ids))
    if role_status:
        where = sa.and_(where, role_table.c.status == role_status)
    if can_receive_finished_docs is not None:
        where = sa.and_(where, role_table.c.can_receive_finished_docs == can_receive_finished_docs)
    if date_created_gte:
        where = sa.and_(where, role_table.c.date_created >= date_created_gte)

    # Filters by users table
    if user_id is not None:
        where = sa.and_(where, user_table.c.id == user_id)
    if user_email is not None:
        where = sa.and_(where, user_table.c.email == user_email)
    if user_emails is not None:
        where = sa.and_(where, user_table.c.email.in_(user_emails))

    # Filters by company table
    if company_id is not None:
        where = sa.and_(where, company_table.c.id == company_id)
    if company_edrpous:
        where = sa.and_(where, company_table.c.edrpou.in_(company_edrpous))

    # Combined filters
    if only_active:
        where = sa.and_(
            where,
            is_active_filter,
        )

    stmt = SELECT_ROLE_QUERY.where(where)

    if offset:
        stmt = stmt.offset(offset)
    if limit:
        stmt = stmt.limit(limit)
    if order:
        stmt = stmt.order_by(*order)

    data = await select_all(conn, query=stmt)

    roles = [to_role(item) for item in data]
    return [role for role in roles if role]


async def select_roles_by_emails(
    conn: DBConnection, company_id: str, emails: list[str]
) -> list[Role]:
    """Select coworker roles by company_id and user emails"""
    return await select_roles(
        conn=conn,
        company_id=company_id,
        user_emails=emails,
    )


async def select_role_by_emails_and_edrpous(conn: DBConnection, data: list[DataDict]) -> list[Role]:
    if not data:
        return []

    clause = sa.or_(
        *[
            sa.and_(
                user_table.c.email == item['email'],
                company_table.c.edrpou == item['edrpou'],
            ).self_group()
            for item in data
        ]
    )
    rows = await select_all(conn, SELECT_ROLE_QUERY.where(clause))

    roles: list[Role] = []
    for item in rows:
        role = to_role(item)
        if not role:
            continue
        roles.append(role)

    return roles


async def select_roles_by_email(conn: DBConnection, email: str) -> list[Role]:
    """Select roles for given user by email"""
    return await select_roles(conn, user_email=email)


async def select_role_by(
    conn: DBConnection,
    company_edrpou: str,
    mixed: str,
    is_legal: bool = True,
    role_status: RoleStatus | None = None,
) -> Role | None:
    """
    Select role by company EDRPOU & user ID or email.
    NOTE: use function "select_role" instead, which has more predictable API
    """

    if '@' in mixed:
        return await select_role(
            conn=conn,
            user_email=mixed,
            role_status=role_status,
            company_edrpou=company_edrpou,
            is_legal=is_legal,
        )
    return await select_role(
        conn=conn,
        user_id=mixed,
        role_status=role_status,
        company_edrpou=company_edrpou,
        is_legal=is_legal,
    )


async def select_role(
    conn: DBConnection,
    *,
    role_id: str | None = None,
    role_status: RoleStatus | None = None,
    company_id: str | None = None,
    company_edrpou: str | None = None,
    user_id: str | None = None,
    user_email: str | None = None,
    is_legal: bool | None = None,
) -> Role | None:
    """
    Select single role. At least one role's identity should be provided: ID of role
    or combination of unique user and company attributes.
    """

    # Check that at least one role identity is provided
    if not role_id:
        assert (company_id or company_edrpou) and (user_id or user_email)

    filters = []

    # Filters by roles table
    if role_id is not None:
        filters.append(role_table.c.id == role_id)
    if role_status is not None:
        filters.append(role_table.c.status == role_status)

    # Filter by companies table
    if company_id is not None:
        filters.append(company_table.c.id == company_id)
    if company_edrpou is not None:
        filters.append(company_table.c.edrpou == company_edrpou)
    if is_legal is not None:
        filters.append(company_table.c.is_legal.is_(is_legal))

    # Filters by users table
    if user_id is not None:
        filters.append(user_table.c.id == user_id)
    if user_email is not None:
        filters.append(user_table.c.email == user_email)

    assert filters, 'Provide filters'

    query = SELECT_ROLE_QUERY.where(sa.and_(*filters))
    row = await select_one(conn, query=query)
    return to_role(row)


async def select_role_by_id(
    conn: DBConnection,
    role_id: str | None,
    role_status: RoleStatus | None = None,
) -> Role | None:
    """Select role by given optional role_id"""
    if role_id is None:
        return None

    return await select_role(
        conn=conn,
        role_id=role_id,
        role_status=role_status,
    )


async def select_roles_by_ids(
    conn: DBConnection,
    roles_ids: list[str],
    role_status: RoleStatus | None = None,
) -> list[Role]:
    """Select list of roles by given IDs"""
    if not roles_ids:
        return []

    return await select_roles(
        conn=conn,
        roles_ids=roles_ids,
        role_status=role_status,
    )


async def select_roles_with_reviews_by_ids(
    conn: DBConnection, roles_ids: list[str], document_id: str
) -> list[DBRow]:
    role_with_review = user_role_company_join.outerjoin(
        review_table,
        sa.and_(
            review_table.c.role_id == role_table.c.id,
            review_table.c.document_id == document_id,
            review_table.c.is_last.is_(True),
        ),
    )
    query = (
        sa.select([*SELECT_ROLE_FIELDS, review_table.c.type.label('review_type')])
        .where(role_table.c.id.in_(roles_ids))
        .select_from(role_with_review)
    )
    return await select_all(conn, query=query)


async def select_token_by(conn: DBConnection, clause: ClauseElement) -> DBRow | None:
    return await select_one(
        conn,
        (sa.select([token_table]).select_from(user_role_company_token_join).where(clause)),
    )


async def select_users_bulk(
    conn: DBConnection,
    input: list[SelectUsersBulkInput],
) -> list[User]:
    email_company_pairs = [(user.email, user.company_id) for user in input]

    rows = await select_all(
        conn=conn,
        query=(
            sa.select(USER_COLUMNS)
            .select_from(user_role_company_strict_join)
            .where(sa.tuple_(user_table.c.email, company_table.c.id).in_(email_company_pairs))
        ),
    )
    return [User.from_row(row) for row in rows]


async def select_user(
    conn: DBConnection,
    *,
    user_id: str | None = None,
    email: str | None = None,
    role_id: str | None = None,
    company_id: str | None = None,
    company_edrpou: str | None = None,
    use_active_filter: bool = True,
    is_legal: bool = True,
    exclude_placeholder: bool = True,
    only_with_email: bool = True,
) -> User | None:
    """
    Select user with company and role info. Direct strict alternative for
    "select_user_by" that returns non-typed DBRow

    NOTE: For selecting user without company info use "select_base_user".
    """

    # check that user requested with uniq fields that represent one role in database
    assert role_id or ((user_id or email) and (company_id or company_edrpou))

    filters = []
    if user_id is not None:
        filters.append(user_table.c.id == user_id)

    if email is not None:
        filters.append(user_table.c.email == email)

    if role_id is not None:
        filters.append(role_table.c.id == role_id)

    if company_id is not None:
        filters.append(company_table.c.id == company_id)

    if company_edrpou is not None:
        filters.append(company_table.c.edrpou == company_edrpou)

    if is_legal:
        filters.append(company_table.c.is_legal.is_(is_legal))

    if use_active_filter:
        filters.append(is_active_filter)

    if exclude_placeholder:
        filters.append(user_table.c.is_placeholder.isnot(True))

    if only_with_email:
        filters.append(user_table.c.email.isnot(None))

    row = await select_one(
        conn=conn,
        query=(
            sa.select(USER_COLUMNS)
            .select_from(user_role_company_strict_join)
            .where(sa.and_(*filters))
        ),
    )
    return User.from_row(row) if row else None


async def select_any_company_admin(
    conn: DBConnection,
    company_edrpou: str,
) -> User | None:
    """
    Return one random, active admin of the company identified by company_edrpou
    """

    row = await select_one(
        conn=conn,
        query=(
            sa.select(USER_COLUMNS)
            .select_from(user_role_company_strict_join)
            .where(
                sa.and_(
                    role_table.c.user_role == UserRole.admin.value,
                    company_table.c.edrpou == company_edrpou,
                    user_table.c.is_placeholder.is_(False),
                    user_table.c.email.isnot(None),
                    is_active_filter,
                )
            )
            .limit(1)
        ),
    )
    return User.from_row(row) if row else None


async def select_user_with_any_role(
    conn: DBConnection,
    *,
    user_id: str,
    only_with_email: bool = True,
) -> BaseUser | User | None:
    """
    Select user with any active role. If no active role found return base user info.
    """
    filters = [
        user_table.c.id == user_id,
        user_table.c.is_placeholder.isnot(True),
    ]
    if only_with_email:
        filters.append(user_table.c.email.isnot(None))

    row = await select_one(
        conn=conn,
        query=(
            sa.select(USER_COLUMNS)
            .select_from(user_active_role_company_join)
            .where(sa.and_(*filters))
        ),
    )
    return User.from_base_row(row) if row else None


async def select_users_with_active_hrs_roles(
    conn: DBConnection,
    company_id: str,
    counted_for_billing_limit: bool | None = None,
) -> list[User]:
    filters = [
        role_table.c.has_hrs_role.is_(True),
        role_table.c.company_id == company_id,
        role_table.c.status == RoleStatus.active,
        user_table.c.is_placeholder.isnot(True),
        user_table.c.email.isnot(None),
    ]
    if counted_for_billing_limit is not None:
        filters.append(role_table.c.is_counted_in_billing_limit.is_(counted_for_billing_limit))

    rows = await select_all(
        conn=conn,
        query=(
            sa.select(USER_COLUMNS)
            .select_from(user_role_company_strict_join)
            .where(sa.and_(*filters))
        ),
    )
    return [User.from_row(row) for row in rows]


async def select_users(
    conn: DBConnection,
    *,
    roles_ids: list[str] | None = None,
    company_edrpou: str | None = None,
    role_status: RoleStatus | None = None,
    user_role: UserRole | None = None,
    can_receive_inbox: bool | None = None,
    use_active_filter: bool = True,
    is_legal: bool = True,
    exclude_placeholder: bool = True,
    only_with_email: bool = True,
) -> list[User]:
    """Select users with company and role info"""

    filters = []

    if roles_ids is not None:
        filters.append(role_table.c.id.in_(roles_ids))

    if company_edrpou is not None:
        filters.append(company_table.c.edrpou == company_edrpou)

    if role_status is not None:
        filters.append(role_table.c.status == role_status)

    if user_role is not None:
        filters.append(role_table.c.user_role == user_role.value)

    if can_receive_inbox is not None:
        filters.append(role_table.c.can_receive_inbox.is_(can_receive_inbox))

    assert filters, 'Provide at least one filter'

    if is_legal:
        filters.append(company_table.c.is_legal.is_(is_legal))

    if use_active_filter:
        filters.append(is_active_filter)

    if exclude_placeholder:
        filters.append(user_table.c.is_placeholder.isnot(True))

    if only_with_email:
        filters.append(user_table.c.email.isnot(None))

    rows = await select_all(
        conn=conn,
        query=(
            sa.select(USER_COLUMNS)
            .select_from(user_role_company_strict_join)
            .where(sa.and_(*filters))
        ),
    )
    return [User.from_row(row) for row in rows]


async def select_users_by_emails(
    conn: DBConnection, emails: list[str], *, verified_phone: bool | None = None
) -> list[BaseUser]:
    clause = user_table.c.email.in_(emails)
    if verified_phone is not None:
        clause = sa.and_(clause, user_table.c.is_phone_verified.is_(verified_phone))
    rows = await select_all(conn, user_table.select().where(clause))
    return [BaseUser.from_row(row) for row in rows]


async def select_base_user(
    conn: DBConnection,
    *,
    email: str | None = None,
    user_id: str | None = None,
    google_id: str | None = None,
    microsoft_id: str | None = None,
    apple_id: str | None = None,
    exclude_placeholder: bool = True,
    only_with_email: bool = True,
    auth_phone: str | None = None,
    phone: str | None = None,
) -> BaseUser | None:
    """Select info about user, without role and company info"""
    filters = []
    if email is not None:
        filters.append(user_table.c.email == email)
    if user_id is not None:
        filters.append(user_table.c.id == user_id)
    if google_id is not None:
        filters.append(user_table.c.google_id == google_id)
    if microsoft_id is not None:
        filters.append(user_table.c.microsoft_id == microsoft_id)
    if apple_id is not None:
        filters.append(user_table.c.apple_id == apple_id)
    if auth_phone is not None:
        filters.append(user_table.c.auth_phone == auth_phone)
    # Pay attention that this phone can be unverified
    if phone is not None:
        filters.append(user_table.c.phone == phone)

    # When you need only real users (not placeholder users)
    if exclude_placeholder:
        filters.append(user_table.c.is_placeholder.isnot(True))

    if only_with_email:
        filters.append(user_table.c.email.isnot(None))

    assert filters, 'Provide at least one filter'

    query = sa.select([user_table]).select_from(user_table).where(sa.and_(*filters))
    row = await select_one(conn, query)
    return BaseUser.from_row(row) if row else None


async def is_verified_phone_exists(
    conn: DBConnection,
    phone: str,
) -> bool:
    """
    Check if there are at least one user with given verified phone number to avoid creating
    offer users to enable phone auth, instead of registering new user with phone auth.
    """
    return await exists(
        conn=conn,
        select_from=user_table,
        clause=sa.and_(
            user_table.c.phone == phone,
            user_table.c.is_phone_verified.is_(True),
            user_table.c.is_placeholder.isnot(True),
        ),
    )


async def is_auth_phone_exists(conn: DBConnection, auth_phone: str) -> bool:
    """
    Check that auth phone is not used by any user to catch unique constraint violation earlier
    """
    return await exists(
        conn=conn,
        select_from=user_table,
        clause=sa.and_(
            user_table.c.auth_phone == auth_phone,
        ),
    )


async def select_user_by_token_hash(
    conn: DBConnection,
    token_hash: str,
    *,
    raw_token: str | None = None,
    ensure_active_user: bool = True,
    exclude_placeholder: bool = True,
    only_with_email: bool = True,
    is_legal: bool = True,
) -> User | None:
    selectable = [
        *USER_COLUMNS,
        token_table.c.token_hash,
        token_table.c.vendor.label('token_vendor'),
    ]

    # For tests only we can pass raw_token to get it in result
    if raw_token:
        selectable.append(sa.sql.expression.literal(raw_token).label('raw_token'))

    filters = [
        token_table.c.token_hash == token_hash,
        sa.or_(
            token_table.c.date_expired.is_(None),
            token_table.c.date_expired > local_now(),
        ),
        company_table.c.is_legal.is_(is_legal),
    ]

    if ensure_active_user:
        filters.append(is_active_filter)
    if exclude_placeholder:
        filters.append(user_table.c.is_placeholder.isnot(True))
    if only_with_email:
        filters.append(user_table.c.email.isnot(None))

    row = await select_one(
        conn=conn,
        query=(
            sa.select(selectable)
            .select_from(
                user_role_company_strict_join.join(
                    token_table,
                    token_table.c.role_id == role_table.c.id,
                )
            )
            .where(sa.and_(*filters))
        ),
    )
    return User.from_row(row) if row else None


async def select_users_by_role_ids(conn: DBConnection, ids: StrList) -> list[User]:
    rows = await select_all(
        conn,
        (
            sa.select(USER_COLUMNS)
            .select_from(user_role_company_join)
            .where(role_table.c.id.in_(ids))
        ),
    )
    return [User.from_row(row) for row in rows]


async def select_user_companies(conn: DBConnection, *, user_id: str) -> list[Company]:
    """
    Select all companies where user has an active role
    """
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([company_table])
            .select_from(active_role_company_join)
            .where(
                sa.and_(
                    role_table.c.user_id == user_id,
                    company_table.c.id.isnot(None),
                ),
            )
        ),
    )
    return [Company.from_row(row) for row in rows]


async def update_company_by_edrpou(
    conn: DBConnection,
    edrpou: str,
    data: DataDict,
) -> None | Company:
    """Update a company by edrpou

    WARNING: do not use that function directly,
    use "update_company_by_edrpou" from "app.auth.utils"
    """
    is_legal = data.pop('is_legal', True)
    if not data:
        return None

    data['date_updated'] = sa.text('now()')
    row = await select_one(
        conn=conn,
        query=(
            company_table.update()
            .values(data)
            .where(
                sa.and_(
                    company_table.c.edrpou == edrpou,
                    company_table.c.is_legal.is_(is_legal),
                )
            )
            .returning(company_table)
        ),
    )
    return Company.from_row(row) if row else None


async def update_company_by_id(conn: DBConnection, company_id: str, data: DataDict) -> None:
    if not data:
        return
    data['date_updated'] = sa.text('now()')
    await conn.execute(company_table.update().values(data).where(company_table.c.id == company_id))


async def update_company_config(
    conn: DBConnection,
    company_id: str,
    config: DataDict | None = None,
    admin_config: DataDict | None = None,
) -> None:
    """
    If config for company exists in database, just insert new config, else merge
    previous and new config
    """
    if not config and not admin_config:
        return

    _config_update: DataDict = config or {}
    _admin_config_update: DataDict = admin_config or {}

    # Deep merge existing config and given config
    # WARNING:
    #   - don't use psql native merge here since it's not supporting deep merge
    #   - pay attention that "_config_update" and "_admin_config_update" overrides the previous
    #     "config" and "admin_config" in ON CONFLICT DO UPDATE clause. So even if nothing was
    #     changed for that column, remember to assign previous column value to "_config_update"
    #     and "_admin_config_update" variables to avoid losing data.
    async with conn.begin():
        prev_row = await select_one(
            conn=conn,
            query=(
                sa.select([company_config_table.c.config, company_config_table.c.admin_config])
                .select_from(company_config_table)
                .where(company_config_table.c.company_id == company_id)
                # We are locking this row to avoid lost updates, where two transactions read the
                # same config to update it, but the first committed update is overwritten by the
                # second-committed update. This lock tell the second transaction to wait for
                # the first transaction to commit before it can read the config.
                .with_for_update()
            ),
        )
        if prev_row:
            if prev_config := prev_row.config:
                _config_update = deep_merge_dicts(prev_config, _config_update)
            if prev_admin_config := prev_row.admin_config:
                _admin_config_update = deep_merge_dicts(prev_admin_config, _admin_config_update)

        data = {
            'company_id': company_id,
            'config': _config_update,
            'admin_config': _admin_config_update,
        }

        stmt = insert(company_config_table).values(data)
        await conn.execute(
            stmt.on_conflict_do_update(
                index_elements=[company_config_table.c.company_id],
                set_={
                    # INFO: "excluded" here means the row that was attempted to be inserted.
                    # WARNING: we override the previous "config" and "admin_config" values here
                    # in case of conflict. So "config" and "admin_config" values must contain the
                    # whole config merged with the previous one.
                    'config': stmt.excluded.config,
                    'admin_config': stmt.excluded.admin_config,
                },
            )
        )


async def batch_update_company_config(
    conn: DBConnection,
    company_ids: list[str],
    config: DataDict | None = None,
    admin_config: DataDict | None = None,
) -> None:
    """
    If some config for company exists in database, just insert new config,
    else merge previous and new config
    """
    if not config and not admin_config:
        return

    _config_update: DataDict = config or {}
    _admin_config_update: DataDict = admin_config or {}

    _is_config_nested = any(isinstance(v, dict) for v in _config_update.values())
    _is_admin_config_nested = any(isinstance(v, dict) for v in _admin_config_update.values())
    if _is_config_nested or _is_admin_config_nested:
        raise NotImplementedError('Deep merge is not implemented yet')

    data = [
        {
            'config': _config_update,
            'admin_config': _admin_config_update,
            'company_id': cid,
        }
        for cid in company_ids
    ]

    stmt = insert(company_config_table).values(data)
    config_column = company_config_table.c.config
    admin_config_column = company_config_table.c.admin_config

    await conn.execute(
        stmt.on_conflict_do_update(
            index_elements=[company_config_table.c.company_id],
            set_={
                # TODO: like in "update_company_config" add deep merge here, but be careful with
                #  it to avoid losing previous data
                'config': config_column.concat(stmt.excluded.config),
                'admin_config': admin_config_column.concat(stmt.excluded.admin_config),
            },
        )
    )


async def update_company_ipn(
    conn: DBConnection,
    edrpou: str | None,
    ipn: str | None,
) -> None:
    """
    Set IPN for company by EDRPOU if IPN is not set yet
    """
    if edrpou is None or ipn is None:
        return

    await conn.execute(
        company_table.update()
        .values(ipn=ipn, date_updated=sa.text('now()'))
        .where(
            sa.and_(
                company_table.c.edrpou == edrpou,
                company_table.c.ipn.is_(None),
                company_table.c.is_legal.is_(True),
            )
        )
    )


async def update_companies_ipn(
    conn: DBConnection,
    data: dict[str, str],
) -> None:
    """
    Set IPN for companies by EDRPOU if IPN is not set yet

    Data is a dictionary where key is EDRPOU and value is IPN
    """
    if not data:
        return

    edrpous = set(data.keys())

    # For most of the companies, IPN is already set, so we can avoid making UPDATE statement
    # in most cases by checking for which companies IPN is not set yet.
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.edrpou])
            .select_from(company_table)
            .where(
                sa.and_(
                    company_table.c.edrpou.in_(edrpous),
                    company_table.c.ipn.is_(None),
                    company_table.c.is_legal.is_(True),
                )
            )
        ),
    )

    # After filtering out companies that already have IPN set, we now have a smaller list
    # of EDRPOU to update
    edrpous = {company.edrpou for company in rows}
    update_data = [
        {
            'edrpou': edrpou,
            'ipn': ipn,
        }
        for edrpou, ipn in data.items()
        if edrpou in edrpous
    ]

    if not update_data:
        return

    # Usually, if you want to update multiple rows in PostgresSQL using several identifiers,
    # you can use the "UPDATE ... FROM (...) AS t WHERE id = t.id" syntax. The "FROM" clause can
    # be another table, a subquery, a CTE, or the "VALUES" clause. The main idea is that the
    # "FROM" clause should behave like a table.
    #
    # I tried to pass update data as "VALUES" clause but failed to implement it using
    # SQLAlchemy 1.3 syntax. Instead of using the "VALUES" clause, I find a way to use
    # the "jsonb_to_recordset" function, which accepts JSONB data and converts it into
    # a table-like structure. It's a bit more complex, but it should work better than
    # using a for loop for each company. If you are reading this when SQLAlchemy is updated
    # to version 1.4 or higher, you can try to use the "sa.values" function instead of this
    # hacky approach.
    data_stmt = (
        sa.text("""
            (SELECT t.edrpou, t.ipn
            FROM jsonb_to_recordset(:update_data) AS t(edrpou text, ipn text))
        """)
        .bindparams(sa.bindparam('update_data', update_data, type_=JSONB))
        .columns(edrpou=sa.Text(), ipn=sa.Text())
        .alias('temp1')
    )

    # Update multimple rows at once using UPDATE FROM SELECT syntax
    update_stmt = (
        sa.update(company_table)
        .values({'ipn': data_stmt.c.ipn, 'date_updated': sa.func.now()})
        .where(
            sa.and_(
                company_table.c.edrpou == data_stmt.c.edrpou,
                company_table.c.is_legal.is_(True),
            )
        )
    )

    await conn.execute(update_stmt)


async def update_company_upload_documents_left(
    conn: DBConnection, company_id: str, value: int
) -> None:
    await conn.execute(
        company_table.update()
        .values({'upload_documents_left': (company_table.c.upload_documents_left + value)})
        .where(company_table.c.id == company_id)
    )


async def update_role(
    conn: DBConnection,
    role_id: str,
    data: UpdateRoleDict,
) -> RoleDB | None:
    if not data:
        return None

    data['date_updated'] = sa.text('now()')
    role = await select_one(
        conn,
        role_table.update().values(data).where(role_table.c.id == role_id).returning(role_table),
    )
    return Role.from_row(role) if role else None


async def upsert_super_admin_permissions(
    conn: DBConnection,
    role_id: str,
    super_admin_permissions: DataDict,
    request_user: User | None,
) -> None:
    """Insert or update super admin permissions for role_id"""
    if not super_admin_permissions or not request_user:
        return

    super_admin_permissions['role_id'] = role_id

    await conn.execute(
        insert(super_admin_permissions_table)
        .values(super_admin_permissions)
        .returning(super_admin_permissions_table)
        .on_conflict_do_update(
            index_elements=[super_admin_permissions_table.c.role_id],
            set_=super_admin_permissions,
        )
    )
    await insert_super_admin_action(
        conn,
        SuperAdminActionType.update_sa_permissions,
        request_user,
        [super_admin_permissions],
    )


async def update_roles(conn: DBConnection, role_ids: list[str], data: UpdateRoleDict) -> None:
    """Mass update roles.

    Update all role by IDs with given data. This is useful to agree on multiple
    roles, or for example to make user an admin or regular user on given list
    of roles.
    """
    data.setdefault('date_updated', sa.text('now()'))
    await conn.execute(role_table.update().values(data).where(role_table.c.id.in_(role_ids)))


async def update_users_email_confirmed(
    conn: DBConnection,
    user_ids: list[str],
    is_email_confirmed: bool,
) -> None:
    """
    Update email_confirmed field for multiple users
    """
    if not user_ids:
        return

    data = {
        'date_updated': sa.text('now()'),
        'email_confirmed': is_email_confirmed,
    }
    await conn.execute(user_table.update().values(data).where(user_table.c.id.in_(user_ids)))


async def update_users_registration_completed(
    conn: DBConnection,
    user_ids: list[str],
    registration_completed: bool,
) -> str | None:
    """Update multiple users data"""

    data = {
        'date_updated': sa.text('now()'),
        'registration_completed': registration_completed,
    }
    return await conn.scalar(
        user_table.update()
        .values(data)
        .where(user_table.c.id.in_(user_ids))
        .returning(user_table.c.id)
    )


async def raw_update_user(
    conn: DBConnection,
    user_id: str,
    data: UpdateBaseUserDict,
) -> BaseUser:
    """

    WARNING: avoid using this function directly, use "update_user" from "app.auth.utils" instead.
    """
    assert data, 'Provide data to update user'
    update_data = data.copy()

    new_password: str | None = update_data.pop('new_password', None)
    if new_password:
        update_data['password'] = helpers.generate_password_hash(new_password)

    update_data['date_updated'] = sa.text('now()')
    row = await select_one(
        conn=conn,
        query=(
            user_table.update()
            .values(update_data)
            .where(user_table.c.id == user_id)
            .returning(user_table)
        ),
    )
    return BaseUser.from_row(row)


async def set_telegram_chat_id_by_phone(
    conn: DBConnection, phone: str, chat_id: int
) -> list[DBRow]:
    """Add chat id for all users with given phone number"""
    return await select_all(
        conn,
        (
            user_table.update()
            .values({'telegram_chat_id': chat_id})
            .where(
                sa.and_(
                    user_table.c.phone == phone,
                    user_table.c.is_phone_verified.is_(True),
                )
            )
            .returning(user_table.c.id)
        ),
    )


async def delete_telegram_id(conn: DBConnection, chat_id: int) -> None:
    """Set to null telegram chat id"""
    await conn.execute(
        user_table.update()
        .values({'telegram_chat_id': None})
        .where(user_table.c.telegram_chat_id == chat_id)
    )


async def delete_telegram_chat_id_by_user_id(conn: DBConnection, user_id: str) -> None:
    """Set to null telegram chat id for given user"""
    await conn.execute(
        user_table.update().values({'telegram_chat_id': None}).where(user_table.c.id == user_id)
    )


async def exists_telegram_chat_id(conn: DBConnection, chat_id: int) -> bool:
    clause = user_table.c.telegram_chat_id == chat_id
    return await exists(conn, user_table, clause)


async def select_telegram_chat_id_by_phone(conn: DBConnection, phone: str) -> str | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select([user_table.c.telegram_chat_id]).where(
                sa.and_(
                    user_table.c.phone == phone,
                    user_table.c.telegram_chat_id.isnot(None),
                )
            )
        ),
    )
    return row.telegram_chat_id if row else None


async def select_recipient_email_from_company(
    conn: DBConnection, edrpous: list[str]
) -> list[DBRow]:
    """
    Select emails from company that will be assigned as recipients emails
    for document. Selects all active admins from company or first any
    user in company if there is not any active admins.
    """
    if not edrpous:
        return []

    strict_user_role_company_join = user_table.join(
        role_table, role_table.c.user_id == user_table.c.id
    ).join(company_table, company_table.c.id == role_table.c.company_id)

    selectable = [company_table.c.edrpou, user_table.c.email]

    where_clauses = [
        role_table.c.status == RoleStatus.active,
        company_table.c.edrpou.in_(edrpous),
    ]

    # TODO: ask @v.onuchak if we need to add admin or can_view_document
    all_active_company_admins = (
        sa.select(selectable)
        .select_from(strict_user_role_company_join)
        .where(sa.and_(*where_clauses, role_table.c.user_role == UserRole.admin.value))
    )

    # Select first user in every given company, such as there is order on
    # user role, admin always will be first.
    first_user_in_company = (
        sa.select(selectable)
        .select_from(strict_user_role_company_join)
        .where(sa.and_(*where_clauses))
        .order_by(company_table.c.edrpou, role_table.c.user_role.desc())
        .distinct(company_table.c.edrpou)
    )

    return await select_all(conn, query=all_active_company_admins.union(first_user_in_company))


async def get_registered_companies(conn: DBConnection, edrpous: list[str]) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.edrpou, company_table.c.name])
            .select_from(active_role_company_join)
            .where(company_table.c.edrpou.in_(edrpous))
            .distinct(company_table.c.id)
        ),
    )


async def update_company_entity_count(
    conn: DBConnection, company_id: str, *, entity: StatEntity, count: int
) -> DBRow:
    query = (
        insert(company_statistic_table)
        .values({entity.value: count, 'company_id': company_id})
        .on_conflict_do_update(
            index_elements=[company_statistic_table.c.company_id],
            set_={entity.value: count},
        )
        .returning(company_statistic_table)
    )

    return await select_one(conn, query)


async def select_company_stat(conn: DBConnection, company_id: str) -> DBRow | None:
    return await select_one(
        conn=conn,
        query=(
            company_statistic_table.select().where(
                company_statistic_table.c.company_id == company_id
            )
        ),
    )


async def select_companies_stats(conn: DBConnection, companies_ids: list[str]) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            company_statistic_table.select().where(
                company_statistic_table.c.company_id.in_(companies_ids)
            )
        ),
    )


async def select_default_recipients(conn: DBConnection, edrpous: list[str]) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    company_table.c.edrpou,
                    user_table.c.email,
                    role_table.c.id.label('role_id'),
                ]
            )
            .select_from(user_active_role_company_join)
            .where(
                sa.and_(
                    company_table.c.edrpou.in_(edrpous),
                    role_table.c.is_default_recipient,
                )
            )
        ),
    )


async def select_default_recipient(conn: DBConnection, edrpou: str) -> DBRow | None:
    rows = await select_default_recipients(conn, [edrpou])
    return rows[0] if rows else None


async def reset_password(conn: DBConnection, *, user_id: str) -> None:
    await conn.execute(
        user_table.update()
        .values(
            {
                'password': helpers.generate_password_hash(helpers.autogenerate_password()),
                'is_autogenerated_password': True,
                'date_updated': sa.text('now()'),
            }
        )
        .where(user_table.c.id == user_id)
    )


async def reset_2fa(conn: DBConnection, *, user_id: str) -> None:
    await conn.execute(
        user_table.update()
        .values(
            {
                'is_2fa_enabled': False,
                'phone': None,
                'is_phone_verified': False,
                'telegram_chat_id': None,
            }
        )
        .where(user_table.c.id == user_id)
    )


async def block_roles_2fa(conn: DBConnection, user_id: str, companies_ids: list[str]) -> None:
    """

    TAG: role_deactivation
    """

    data: UpdateRoleDict = {
        'status': RoleStatus.blocked_2fa,
        'date_updated': sa.text('now()'),
    }
    await conn.execute(
        role_table.update()
        .values(data)
        .where(
            sa.and_(
                role_table.c.user_id == user_id,
                role_table.c.company_id.in_(companies_ids),
                role_table.c.status == RoleStatus.active,
            ),
        )
    )


async def select_active_user_roles(conn: DBConnection, user_id: str) -> list[User]:
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(USER_COLUMNS.copy())
            .select_from(user_active_role_company_join)
            .where(
                sa.and_(
                    role_table.c.user_id == user_id,
                    user_table.c.is_placeholder.isnot(True),
                    user_table.c.email.isnot(None),
                )
            )
        ),
    )
    return [User.from_row(row) for row in rows]


async def select_user_roles(conn: DBConnection, user_id: str) -> list[DBRow]:
    return await select_all(
        conn=conn, query=role_table.select().where(role_table.c.user_id == user_id)
    )


async def unblock_roles_2fa(conn: DBConnection, user_id: str) -> None:
    """
    Unblock roles with status "blocked_2fa". This status can be set only for roles in "active"
    status, so we can unblock them by setting status back to "active" status
    """

    # TAG: role_activation
    data: UpdateRoleDict = {
        'status': RoleStatus.active,
        'date_updated': sa.text('now()'),
        # NOTE: When unblock role we don't need to update activation columns, like "activated_by",
        # "date_activated" and "activation_source because the user was active before and just
        # temporary was blocked by 2FA policy
    }
    await conn.execute(
        role_table.update()
        .values(data)
        .where(
            sa.and_(
                role_table.c.user_id == user_id,
                role_table.c.status == RoleStatus.blocked_2fa,
            ),
        )
    )


async def upsert_user_meta(
    conn: DBConnection,
    data: DataDict,
) -> None:
    """Update or insert user meta data."""
    await conn.execute(
        insert(user_meta_table)
        .values(data)
        .on_conflict_do_update(index_elements=[user_meta_table.c.user_id], set_=data)
    )


async def upsert_invalid_password_count(
    conn: DBConnection,
    *,
    user_id: str,
    value: int,
) -> None:
    """Update count of invalid password attempts."""
    data: DataDict = {'user_id': user_id, 'invalid_password_count': value}
    await upsert_user_meta(
        conn=conn,
        data=data,
    )


async def select_invalid_password_count(conn: DBConnection, user_id: str) -> int:
    """Get count of invalid password attempts."""
    row = await select_one(
        conn=conn,
        query=(
            sa.select([user_meta_table.c.invalid_password_count])
            .select_from(user_meta_table)
            .where(user_meta_table.c.user_id == user_id)
        ),
    )
    return int(row.invalid_password_count) if row else 0


async def select_phone_usage_info(conn: DBConnection, user_id: str) -> bool:
    """Return True if the user opened the document at least once from the phone"""
    row = await select_one(
        conn=conn,
        query=(
            sa.select([user_meta_table.c.mobile_usage])
            .select_from(user_meta_table)
            .where(user_meta_table.c.user_id == user_id)
        ),
    )
    return row.mobile_usage if row else False


async def select_company_meta(
    conn: DBConnection,
    *,
    edrpou: str | None = None,
    company_id: str | None = None,
) -> DBRow:
    if company_id:
        return await select_one(
            conn=conn,
            query=(
                company_meta_table.select().where(company_meta_table.c.company_id == company_id)
            ),
        )
    if edrpou:
        return await select_one(
            conn=conn,
            query=(
                sa.select([company_meta_table])
                .select_from(
                    company_meta_table.outerjoin(
                        company_table,
                        company_meta_table.c.company_id == company_table.c.id,
                    )
                )
                .where(company_table.c.edrpou == edrpou)
            ),
        )
    raise ValueError('Could not load company meta: neither edrpou nor company id were provided')


async def select_active_companies_roles(
    conn: DBConnection,
    company_ids: list[str] | None = None,
    company_edrpous: list[str] | None = None,
    user_role: UserRole | None = None,
) -> list[Role]:
    assert any((company_ids, company_edrpous)), 'Provide at lest one filter'

    query = SELECT_ROLE_QUERY

    if user_role:
        query = query.where(role_table.c.user_role == user_role.value)

    if company_ids:
        query = query.where(role_table.c.company_id.in_(company_ids))

    if company_edrpous:
        query = query.where(company_table.c.edrpou.in_(company_edrpous))

    rows = await select_all(conn=conn, query=query)

    roles: list[Role] = []
    for item in rows:
        role = to_role(item)
        if not role:
            continue
        roles.append(role)

    return roles


async def select_company_roles(
    conn: DBConnection, company_id: str, selectable: list[Selectable] | None = None
) -> list[DBRow]:
    if not selectable:
        selectable = [role_table]
    return await select_all(
        conn=conn,
        query=(
            sa.select(selectable)
            .select_from(role_table)
            .where(role_table.c.company_id == company_id)
        ),
    )


async def update_company_documents_sent(conn: DBConnection, company_id: str, count: int) -> None:
    data = {'company_id': company_id, 'sent_documents_count_synced': count}
    await conn.execute(
        insert(company_meta_table)
        .values(data)
        .on_conflict_do_update(index_elements=[company_meta_table.c.company_id], set_=data)
    )


async def update_document_expiring_event(
    conn: DBConnection,
    company_id: str,
    latest_billing_account_id: str,
    event: Event,
) -> None:
    data = {
        'company_id': company_id,
        'documents_expires_last_billing_account_id': latest_billing_account_id,
        'documents_expires_last_event': event,
    }

    await conn.execute(
        insert(company_meta_table)
        .values(data)
        .on_conflict_do_update(index_elements=[company_meta_table.c.company_id], set_=data)
    )


async def update_invited_data(
    conn: DBConnection,
    company_id: str,
    invited_type: InviteSource,
    invited_edrpou: str,
) -> None:
    data = {
        'company_id': company_id,
        'invited_type': invited_type,
        'invited_edrpou': invited_edrpou,
    }

    await conn.execute(
        insert(company_meta_table)
        .values(data)
        .on_conflict_do_update(index_elements=[company_meta_table.c.company_id], set_=data)
    )


async def is_document_expiring_event_sent(
    conn: DBConnection,
    company_id: str,
    latest_billing_account_id: str,
    event: Event,
) -> bool:
    clause = sa.and_(
        company_meta_table.c.company_id == company_id,
        company_meta_table.c.documents_expires_last_billing_account_id == latest_billing_account_id,
        company_meta_table.c.documents_expires_last_event == event,
    )

    return await exists(conn, company_meta_table, clause)


async def select_roles_ids(
    conn: DBConnection,
    *,
    company_id: str | None = None,
    only_active: bool = True,
) -> list[str]:
    """Lightweight function that returns only IDS of roles"""
    filters = []

    if company_id is not None:
        filters.append(role_table.c.company_id == company_id)

    assert filters, 'Provide at least one filter'

    if only_active:
        filters.append(role_table.c.status == RoleStatus.active)

    query = sa.select([role_table.c.id]).where(sa.and_(*filters))
    rows = await select_all(conn, query=query)

    return [row.id for row in rows]


async def get_super_admin_permissions(
    conn: DBConnection,
    user_role_id: str | None,
) -> set[str]:
    if not user_role_id:
        return set()

    query = sa.select(
        [
            super_admin_permissions_table.c.can_view_client_data,
            super_admin_permissions_table.c.can_edit_client_data,
            super_admin_permissions_table.c.can_edit_special_features,
        ]
    ).where(super_admin_permissions_table.c.role_id == user_role_id)
    row = await select_one(conn, query=query)

    return SuperAdminPermissions.from_db(row).to_permissions_set()


async def get_super_admin_permissions_for_roles(
    conn: DBConnection,
    role_ids: list[str],
) -> dict[str, set[str]]:
    """
    Returns dict with role_id as keys and sa_permission names as values

    If there are no sa_permissions for role it'll be absent in result dict
    """
    query = sa.select(
        [
            super_admin_permissions_table.c.role_id,
            super_admin_permissions_table.c.can_view_client_data,
            super_admin_permissions_table.c.can_edit_client_data,
            super_admin_permissions_table.c.can_edit_special_features,
        ]
    ).where(super_admin_permissions_table.c.role_id.in_(role_ids))
    rows = await select_all(conn, query=query)

    return {row.role_id: SuperAdminPermissions.from_db(row).to_permissions_set() for row in rows}


async def select_roles_for_contact_recipient_indexation(
    conn: DBConnection,
    *,
    roles_ids: list[str] | None = None,
    limit: int | None = None,
    cursor: str | None = None,
) -> list[RoleForIndexation]:
    """
    Select roles for contact recipient indexation
    """
    filters = []
    if roles_ids is not None:
        filters.append(role_table.c.id.in_(roles_ids))
    if cursor is not None:
        filters.append(role_table.c.id > cursor)
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    role_table.c.id,
                    role_table.c.company_id,
                    role_table.c.date_created,
                    role_table.c.status.label('role_status'),
                    user_table.c.email.label('user_email'),
                    user_table.c.first_name.label('user_first_name'),
                    user_table.c.second_name.label('user_second_name'),
                    user_table.c.last_name.label('user_last_name'),
                    user_table.c.registration_completed.label('user_registration_completed'),
                    company_table.c.edrpou.label('company_edrpou'),
                    company_table.c.name.label('company_name'),
                    company_table.c.full_name.label('company_full_name'),
                ]
            )
            .select_from(
                user_table.join(role_table, role_table.c.user_id == user_table.c.id).join(
                    company_table, company_table.c.id == role_table.c.company_id
                )
            )
            .where(sa.and_(*filters))
            .order_by(role_table.c.id)
            .limit(limit)
        ),
    )
    return [RoleForIndexation.from_row(row) for row in rows]


async def select_companies_for_contact_recipient_indexation(
    conn: DBConnection,
    *,
    companies_ids: list[str] | None = None,
    companies_edrpous: list[str] | None = None,
    limit: int | None = None,
    cursor: str | None = None,
) -> list[CompanyForIndexation]:
    filters = []
    if companies_edrpous is not None:
        filters.append(company_table.c.edrpou.in_(companies_edrpous))
    if companies_ids is not None:
        filters.append(company_table.c.id.in_(companies_ids))
    if cursor is not None:
        filters.append(company_table.c.id > cursor)

    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    company_table.c.id,
                    company_table.c.edrpou,
                    company_table.c.name,
                    company_table.c.full_name,
                    company_table.c.date_created,
                ]
            )
            .select_from(company_table)
            .where(sa.and_(*filters))
            .order_by(company_table.c.id)
            .limit(limit)
        ),
    )
    return [CompanyForIndexation.from_row(row) for row in rows]


async def select_companies_ids_by_edrpous(
    conn: DBConnection,
    *,
    edrpous: list[str],
) -> dict[str, str]:
    rows = await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.id, company_table.c.edrpou])
            .select_from(company_table)
            .where(company_table.c.edrpou.in_(edrpous))
        ),
    )
    return {row.edrpou: row.id for row in rows}


async def select_companies_names_for_graph(
    conn: DBConnection,
    edrpous: list[str],
) -> list[DBRow]:
    """
    Select companies' names for graph by edrpous
    """
    return await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.edrpou, company_table.c.name, company_table.c.full_name])
            .select_from(company_table)
            .where(company_table.c.edrpou.in_(edrpous))
        ),
    )


async def update_role_has_signed_documents(conn: DBConnection, role_id: str) -> None:
    data: UpdateRoleDict = {
        'has_signed_documents': True,
        'date_updated': sa.text('now()'),
    }
    await conn.execute(
        role_table.update()
        .values(data)
        .where(sa.and_(role_table.c.id == role_id, role_table.c.has_signed_documents.is_(False)))
    )


async def select_raw_user_role_position(conn: DBConnection, role_id: str) -> str | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select([role_table.c.position])
            .select_from(role_table)
            .where(role_table.c.id == role_id)
        ),
    )
    return row.position if row else None


async def insert_user_email_change(
    conn: DBConnection,
    data: DataDict,
) -> UserEmailChange:
    row = await select_one(
        conn=conn,
        query=(insert(user_email_change_table).values(data).returning(user_email_change_table)),
    )
    return UserEmailChange.from_row(row)


async def cancel_pending_user_email_changes(
    conn: DBConnection,
    user_id: str,
) -> None:
    """
    Cancel all pending email changes for given user
    """
    await conn.execute(
        user_email_change_table.update()
        .values({'status': UserEmailChangeStatus.cancelled})
        .where(
            sa.and_(
                user_email_change_table.c.user_id == user_id,
                user_email_change_table.c.status.in_(
                    [
                        UserEmailChangeStatus.verify_2fa,
                        UserEmailChangeStatus.verify_email,
                        UserEmailChangeStatus.verify_password,
                        UserEmailChangeStatus.pending,
                    ]
                ),
            )
        )
    )


async def update_user_email_change(
    conn: DBConnection,
    *,
    change_id: str,
    data: DataDict,
) -> None:
    await conn.execute(
        user_email_change_table.update()
        .values(data)
        .where(user_email_change_table.c.id == change_id)
    )


async def select_latest_user_email_changes(
    conn: DBConnection,
    *,
    user_id: str,
) -> UserEmailChange | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select([user_email_change_table])
            .select_from(user_email_change_table)
            .where(user_email_change_table.c.user_id == user_id)
            .order_by(user_email_change_table.c.seqnum.desc())
        ),
    )
    return UserEmailChange.from_row(row) if row else None


async def select_user_email_change(conn: DBConnection, *, change_id: str) -> UserEmailChange | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select([user_email_change_table])
            .select_from(user_email_change_table)
            .where(user_email_change_table.c.id == change_id)
        ),
    )
    return UserEmailChange.from_row(row) if row else None


async def select_zvit_user(
    conn: DBConnection,
    user_id: str | None = None,
    email: str | None = None,
) -> ZvitUser | None:
    filters = []

    if user_id is not None:
        filters.append(zvit_users_table.c.id == user_id)

    if email is not None:
        filters.append(zvit_users_table.c.email == email)

    assert filters, 'Provide at least one filter'

    row = await select_one(conn=conn, query=sa.select([zvit_users_table]).where(sa.and_(*filters)))
    return ZvitUser.from_row(row) if row else None
