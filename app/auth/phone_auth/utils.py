import logging

from api import errors
from app.auth.db import select_base_user
from app.auth.phone_auth.types import PhoneAuthOutput
from app.auth.utils import create_user
from app.lib.database import DBConnection
from app.lib.sender.client import EvoSenderError
from app.lib.sender.enums import SenderMessageType, SenderMethod, SenderProject
from app.lib.sender.utils import generate_otp_code, send_phone_otp_code
from app.services import services

logger = logging.getLogger(__name__)


PHONE_AUTH_REDIS_KEY_TEMPLATE = 'phone_auth:{phone}'


async def get_auth_phone_otp(phone: str) -> str | None:
    """
    Generate a Redis key for phone authentication OTP
    """
    key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    return await services.redis.get(key)


async def set_auth_phone_otp(phone: str, otp: str) -> None:
    """
    Set a Redis key for phone authentication OTP
    """
    otp_code_ttl = services.config.auth.totp_interval
    otp_code_key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    await services.redis.setex(otp_code_key, value=otp, time=otp_code_ttl)


async def delete_auth_phone_otp(phone: str) -> None:
    """
    Delete a Redis key for phone authentication OTP
    """
    otp_code_key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    await services.redis.delete(otp_code_key)


async def process_phone_auth(
    conn: DBConnection,
    *,
    auth_phone: str,
    code: str,
) -> PhoneAuthOutput:
    """
    Authenticate user by phone number and code
    """

    logger.info('Phone auth request', extra={'phone': auth_phone})

    expected_code = await get_auth_phone_otp(phone=auth_phone)
    if not expected_code:
        raise errors.Error(errors.Code.invalid_totp_code)

    if code != expected_code:
        # NOTE: to prevent brute-force attacks, we have rate limiting during validation
        raise errors.Error(errors.Code.invalid_totp_code)

    user = await select_base_user(conn=conn, auth_phone=auth_phone, only_with_email=False)
    if user:
        is_created = False
    else:
        is_created = True
        user = await create_user(
            conn=conn,
            email=None,
            is_placeholder=False,
            first_name=None,
            second_name=None,
            last_name=None,
            phone=auth_phone,
            auth_phone=auth_phone,
            is_phone_verified=True,
            is_registration_completed=False,
            is_email_confirmed=False,
            password=None,
            is_autogenerated_password=False,
            created_by=None,
            promo_code=None,
            trial_auto_enabled=False,
            pending_referrer_role_id=None,
            source=None,
            google_id=None,
            apple_id=None,
            microsoft_id=None,
            registration_method=None,
        )

    # Delete code after successful authentication to prevent replay attacks
    await delete_auth_phone_otp(phone=auth_phone)

    return PhoneAuthOutput(
        is_created=is_created,
        user_id=user.id,
        user=user,
    )


async def send_phone_auth_code(
    *,
    phone: str,
    method: SenderMethod,
    billing_project: SenderProject | None = None,
) -> None:
    """
    Send phone verification code to the user
    """

    logger.info('Send phone auth code request', extra={'phone': phone})
    otp = generate_otp_code()

    await set_auth_phone_otp(phone=phone, otp=otp)

    try:
        await send_phone_otp_code(
            phone=phone,
            otp=otp,
            method=method,
            message_type=SenderMessageType.phone_auth,
            billing_project=billing_project,
        )
    except EvoSenderError:
        raise errors.Error(errors.Code.invalid_phone_number, details={'phone': phone})
