import logging
from datetime import timed<PERSON><PERSON>

import pydantic
from aiohttp import web

from api.errors import InvalidRequest
from app.auth.db import (
    is_auth_phone_exists,
    is_verified_phone_exists,
    select_base_user,
)
from app.auth.validators import validate_user_not_banned
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database.types import DBConnection
from app.lib.helpers import get_client_ip, validate_rate_limit
from app.lib.sender.enums import SenderMethod
from app.lib.sender.utils import validate_send_phone_auth_rate_limit

logger = logging.getLogger(__name__)


class SendPhoneAuthCodeSchema(pydantic.BaseModel):
    phone: pv.Phone
    method: SenderMethod = SenderMethod.sms


class ProcessPhoneAuth(pydantic.BaseModel):
    phone: pv.Phone
    code: str = pydantic.Field(min_length=6, max_length=6)


async def validate_auth_phone_registration_usage(conn: DBConnection, auth_phone: str) -> None:
    """
    When phone numbers are not used for authentication, we typically allow registering a brand new
    user with that phone. However, this leads to a problem: if a phone number already exists in the
    system (linked to another user, but not used for login), a user might mistakenly try to log in
    with it. In doing so, they would unknowingly create a new account instead of accessing their
    existing one — likely not their intention.

    To prevent this, we currently block registration with phone numbers already associated with
    another user, even if not used for authentication. We prompt the user to either use a different
    phone number or log in via another method (email, Google, etc.), and enable phone authentication
    from their profile settings if needed.

    This is a temporary safeguard to reduce accidental account duplication. In the future, we may
    relax this restriction, but only if it's made explicitly clear to the user that they are
    creating a new, separate account not linked to any existing one associated with that phone
    number.
    """

    # If auth phone exists -> login flow
    # If auth phone does not exist -> registration flow
    if await is_auth_phone_exists(conn, auth_phone=auth_phone):
        return

    if await is_verified_phone_exists(conn, phone=auth_phone):
        raise InvalidRequest(
            reason=_(
                'На цей номер телефону неможливо зареєструватися, оскільки його вже додано до '
                'системи. Якщо це ваш номер телефону і у вас вже є обліковий запис, увійдіть '
                'у ваш акаунт, використовуючи інший спосіб автентифікації, та увімкніть вхід за '
                'номером телефону в налаштуваннях профілю'
            )
        )


async def validate_send_phone_auth_base(
    conn: DBConnection,
    auth_phone: str,
    client_ip: str | None,
    *,
    reject_used_phone_registration: bool,
) -> None:
    """
    Common validator for internal and private API endpoint to start phone authentication process.
    """
    await validate_send_phone_auth_rate_limit(auth_phone=auth_phone, client_ip=client_ip)

    if reject_used_phone_registration:
        await validate_auth_phone_registration_usage(conn, auth_phone=auth_phone)


async def validate_send_phone_auth(
    conn: DBConnection, request: web.Request
) -> SendPhoneAuthCodeSchema:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(SendPhoneAuthCodeSchema, raw_data)

    client_ip = get_client_ip(request)

    await validate_send_phone_auth_base(
        conn,
        auth_phone=data.phone,
        client_ip=client_ip,
        # For the web and mobile API, we avoid creating a new user if the phone number is already
        # associated with another account for non-auth purposes (e.g., 2FA). This helps prevent
        # accidental creation of duplicate accounts.
        reject_used_phone_registration=True,
    )

    user = await select_base_user(conn, auth_phone=data.phone, only_with_email=False)
    if user:
        validate_user_not_banned(request, user)

    return data


async def validate_process_phone_auth_base(
    conn: DBConnection,
    auth_phone: str,
    *,
    reject_used_phone_registration: bool,
) -> None:
    """
    Common validator for internal and private API endpoint to process phone authentication.
    """
    await validate_rate_limit(
        key=f'phone_auth:process:{auth_phone}',
        limit=5,
        delta=timedelta(minutes=15),
    )

    if reject_used_phone_registration:
        await validate_auth_phone_registration_usage(conn, auth_phone=auth_phone)


async def validate_process_phone_auth(conn: DBConnection, request: web.Request) -> ProcessPhoneAuth:
    raw_data = await validators.validate_json_request(request)

    data = validators.validate_pydantic(ProcessPhoneAuth, raw_data)

    await validate_process_phone_auth_base(
        conn=conn,
        auth_phone=data.phone,
        reject_used_phone_registration=True,
    )

    return data
