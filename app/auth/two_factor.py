"""
Check app/auth/README.md for more details about 2FA implementation
"""

from __future__ import annotations

import base64
import hashlib
import hmac
import logging
import time
from secrets import token_urlsafe
from typing import Any, assert_never

import aiohttp_session
import pydantic
import sqlalchemy as sa
import trafaret as t
from aiohttp import web
from aiohttp_session import Session

from app.auth.constants import MONTH
from app.auth.db import block_roles_2fa
from app.auth.enums import AuthFactor
from app.auth.tables import active_role_company_join, company_table, role_table
from app.auth.types import BaseUser
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import emailing, validators
from app.lib.database import DBConnection
from app.lib.sender.enums import SenderMessageType
from app.lib.sender.utils import (
    generate_otp_code,
    send_phone_otp_code,
    validate_send_phone_2fa_rate_limit,
)
from app.lib.urls import build_url
from app.models import select_all
from app.services import services

logger = logging.getLogger(__name__)


#: Flag for remembering device when 2FA enabled
TRUST_2FA_KEY = 'trust_2fa'

#: Flag for verifying 2FA code
PENDING_2FA_KEY = 'pending_2fa'


class Pending2FASchema(pydantic.BaseModel):
    """
    Schema of the object stored in redis session to indicate that user needs to verify 2FA
    """

    first_factor: AuthFactor
    second_factor: AuthFactor


async def start_2fa_web(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
    first_factor: AuthFactor,
) -> None:
    """
    Start 2FA process for web application.

    If user used email and password (or social auth) as first authentication factor we send SMS or
    Viber message to the user phone number with TOTP code and store flag in session that user need
    to verify 2FA code to access @login_required routes.

    If user used phone number + OTP code as first authentication factor we do not send SMS or
    Viber message, but instead ask user to enter email and password to verify his identity.
    """
    if first_factor == AuthFactor.email:
        await start_phone_2fa_web(
            request=request,
            conn=conn,
            user=user,
            first_factor=first_factor,
        )
        return

    if first_factor == AuthFactor.phone:
        await start_email_2fa_web(
            request=request,
            conn=conn,
            user=user,
            first_factor=first_factor,
        )
        return

    assert_never(first_factor)


async def start_phone_2fa_web(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
    first_factor: AuthFactor,
) -> str | None:
    """
    Generate TOTP (time-based one-time password) for current user if any of his
    roles has enabled 2FA. After password is ready store flag in session, that
    user need to verify TOTP after login.

    If none user roles enabled 2FA - return None.
    """

    if not (first_factor == AuthFactor.email and user.email):
        logger.warning(
            'Unexpected first factor for phone 2FA',
            extra={'user_email': user.email, 'first_factor': first_factor},
        )
        raise AssertionError('Unexpected case for phone 2FA')

    second_factor = AuthFactor.phone

    if user.phone is None:
        return None

    if _is_device_trusted_for_2fa_web(user, request):
        return None

    session = await aiohttp_session.get_session(request)
    if not await has_2fa_enabled(conn, user, second_factor=second_factor):
        delete_pending_2fa_web(session)
        return None

    await validate_send_phone_2fa_rate_limit(user_id=user.id)

    otp = await _create_2fa_otp_code_web(user_id=user.id)
    await send_phone_otp_code(phone=user.phone, otp=otp, message_type=SenderMessageType.phone_2fa)

    # Store flag in session that backend need to check 2FA
    set_pending_2fa_web(session=session, first_factor=first_factor, second_factor=second_factor)

    return otp


async def start_email_2fa_web(
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
    first_factor: AuthFactor,
) -> None:
    """
    Start second factor authentication by email+password for web application
    """
    second_factor = AuthFactor.email

    if _is_device_trusted_for_2fa_web(user, request):
        return

    session = await aiohttp_session.get_session(request)
    if not await has_2fa_enabled(conn, user, second_factor=second_factor):
        delete_pending_2fa_web(session)
        return

    # Store flag in session that backend need to check 2FA
    set_pending_2fa_web(session=session, first_factor=first_factor, second_factor=second_factor)


async def get_required_2fa_mobile(
    conn: DBConnection,
    user: BaseUser,
    first_factor: AuthFactor,
) -> Pending2FASchema | None:
    """
    Check if 2FA is enabled for mobile application by first factor and return required
    second factor if it is enabled.
    """
    if first_factor == AuthFactor.email:
        second_factor = AuthFactor.phone
    elif first_factor == AuthFactor.phone:
        second_factor = AuthFactor.email
    else:
        assert_never(first_factor)

    is_enabled = await has_2fa_enabled(conn, user, second_factor=second_factor)
    if not is_enabled:
        return None

    return Pending2FASchema(
        first_factor=first_factor,
        second_factor=second_factor,
    )


async def start_2fa_mobile(user: BaseUser, pending_2fa: Pending2FASchema) -> None:
    """
    Start 2FA process for mobile application.

    If user used email and password (or social auth) as first authentication factor we send SMS or
    Viber message to the user phone number with TOTP code and store flag in session that user need
    to verify 2FA code to access @login_required routes.

    If user used phone number + OTP code as first authentication factor we do not send SMS or
    Viber message, but instead ask user to enter email and password to verify his identity.
    """
    first_factor = pending_2fa.first_factor
    second_factor = pending_2fa.second_factor

    if first_factor == AuthFactor.email:
        assert second_factor == AuthFactor.phone, 'Unexpected second factor for email'

        await start_phone_2fa_code_mobile(
            user_email=user.email,
            user_id=user.id,
            user_phone=user.phone,
            first_factor=first_factor,
        )
        return

    if first_factor == AuthFactor.phone:
        assert second_factor == AuthFactor.email, 'Unexpected second factor for phone'

        # For email 2FA we ask user to enter email and password, so no additional actions is
        # required do to here. On this step we should already have refresh token to be created
        # with "pending" status which indicates that user needs to process second factor.
        return

    assert_never(first_factor)


async def start_phone_2fa_code_mobile(
    user_email: str | None,
    user_id: str | None,
    user_phone: str | None,
    first_factor: AuthFactor,
) -> None:
    """
    Generate 2fa code and send sms with it to the user phone number (mobile API)
    """

    assert first_factor == AuthFactor.email, 'Unexpected first factor for phone 2FA'

    if not user_email or not user_id or not user_phone:
        logger.warning(
            'Cannot send 2fa code. User data is not full',
            extra={
                'user_email': user_email,
                'user_id': user_id,
                'user_phone': user_phone,
            },
        )
        return

    await validate_send_phone_2fa_rate_limit(user_id=user_id)

    otp = await _create_2fa_otp_code_mobile(user_id=user_id)
    await send_phone_otp_code(phone=user_phone, otp=otp, message_type=SenderMessageType.phone_2fa)


async def get_2fa_otp_mobile(*, user_id: str) -> str | None:
    return await services.redis.get(f'2fa_mobile_{user_id}')


async def get_2fa_otp_web(*, user_id: str) -> str | None:
    return await services.redis.get(f'2fa_{user_id}')


async def reset_2fa_otp_web(*, user_id: str) -> None:
    await services.redis.delete(f'2fa_{user_id}')


async def reset_2fa_otp_mobile(*, user_id: str) -> None:
    await services.redis.delete(f'2fa_mobile_{user_id}')


async def _create_2fa_otp_code_web(*, user_id: str) -> str:
    """
    Create and save OTP code for web 2FA by SMS/Viber. Previously generated OTP code
    will be replaced with a new one.
    """
    otp = generate_otp_code()
    ttl = services.config.auth.totp_interval
    await services.redis.setex(f'2fa_{user_id}', value=otp, time=ttl)
    return otp


async def _create_2fa_otp_code_mobile(*, user_id: str) -> str:
    otp = generate_otp_code()
    ttl = services.config.auth.totp_interval
    await services.redis.setex(f'2fa_mobile_{user_id}', value=otp, time=ttl)
    return otp


def _get_2fa_totp_secret(user: BaseUser, app_salt: str) -> bytes:
    """Get unique TOTP secret for given user.

    As of now this secret combines:

    - User ID (public DB field)
    - Phone salt (private DB field)
    - Application salt (private (secret) app config value)

    We need to avoid passing this secret as is outside as it allows attacker to
    get access to TOTP passwords for attacked user.
    """
    user_salt = user.phone_salt
    return base64.b32encode('-'.join((user.id, user_salt, app_salt)).encode('ascii'))


def is_2fa_phone_valid(phone: str | None) -> bool:
    if not phone:
        return False
    try:
        validators.validate_phone(phone)
        return True
    except t.DataError:
        return False


def is_valid_second_factor(factor: AuthFactor, user: BaseUser) -> bool:
    """
    Check if user has valid second factor for 2FA verification.
    """
    if factor == AuthFactor.phone:
        return is_2fa_phone_valid(user.phone)

    if factor == AuthFactor.email:
        return user.email is not None

    assert_never(factor)


async def has_2fa_enabled(conn: DBConnection, user: BaseUser, second_factor: AuthFactor) -> bool:
    """
    Check whether 2FA enabled for given user.

    2FA (two-factor auth) enabled for user when,

    - Any of his companies has enabled 2FA for internal users
    - He received a document from company with enabled 2FA for their document
      receivers
    """
    from app.auth.utils import get_company_configs
    from app.billing.utils import get_billing_companies_configs

    if get_flag(FeatureFlags.DISABLE_2FA_VERIFICATION):
        return False

    is_valid_factor = is_valid_second_factor(factor=second_factor, user=user)
    log_extra = {
        'user_id': user.id,
        'user_phone': user.phone,
        'user_email': user.email,
        'second_factor': second_factor.value,
    }

    if user.is_2fa_enabled:
        # We still return True because we can't automatically relax 2FA requirements for users
        # who have manually enabled it in their profile, even if their second factor is invalid.
        # TODO: think how properly to handle this case from security perspective.
        if not is_valid_factor:
            logger.warning('Second factor is invalid', extra=log_extra)
            return True

        logger.info('2FA enabled for user in profile', extra=log_extra)
        return True

    # NOTE: check app/auth/README.md for more details about company-wide 2FA requirements and
    # how we enforce it for coworkers.

    log_extra = {
        'user_id': user.id,
        'user_phone': user.phone,
        'second_factor': second_factor.value,
    }

    # Check if 2FA enabled due to internal user rule
    companies_with_active_roles = await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.id])
            .select_from(active_role_company_join)
            .where(role_table.c.user_id == user.id)
            .distinct()
        ),
    )

    companies_ids = [company.id for company in companies_with_active_roles]

    companies_configs = await get_company_configs(conn=conn, company_ids=companies_ids)
    companies_ids_with_enabled_2fa = [
        company_id
        for company_id, config in companies_configs.items()
        if config.enable_2fa_for_internal_users
    ]

    # When 2FA is not enabled in user's profile and no companies require it, then we can skip
    # 2FA verification.
    if not companies_ids_with_enabled_2fa:
        logger.info('2FA not enabled by rule for given user', extra=log_extra)
        return False

    # At least one company in which user has an active role has enabled required 2FA for coworkers.
    # If we find that user has an invalid phone to verify 2FA, we should block him and
    # ask to enter and verify a new phone number
    if not is_valid_factor:
        await block_roles_2fa(
            conn=conn,
            user_id=user.id,
            companies_ids=companies_ids_with_enabled_2fa,
        )
        logger.info(
            '2FA enabled for user by rule, but second factor is not valid',
            extra={
                'member_at': companies_ids_with_enabled_2fa,
                **log_extra,
            },
        )
        return False

    logger.info(
        '2FA enabled for user by rule',
        extra={
            'member_at': companies_ids_with_enabled_2fa,
            'phone_verified': user.is_phone_verified,
            **log_extra,
        },
    )

    billing_companies_configs = await get_billing_companies_configs(
        conn=conn,
        companies_ids=companies_ids_with_enabled_2fa,
    )

    # There should be at least one company with proper permissions.
    # This permission is based on the rate of the company.
    # E.g. company should have a rate where `can_enforce_2fa` is True.
    # Can be found in `app.billing.rates`
    return any(config.can_enforce_2fa for config in billing_companies_configs.values())


async def invalidate_phone_2fa_totp_web(request: web.Request, user: BaseUser) -> None:
    """
    Invalidate check 2FA flag in request session if any and
    remove it from Redis to avoid OTP reuse.
    """
    session = await aiohttp_session.get_session(request)
    delete_pending_2fa_web(session)
    await reset_2fa_otp_web(user_id=user.id)


def _sign_with_2fa_totp_secret(user: BaseUser, msg: str) -> str:
    app_salt = services.config.auth.totp_secret_salt
    secret = _get_2fa_totp_secret(user, app_salt=app_salt)
    return hmac.new(secret, msg.encode('utf8'), hashlib.sha256).hexdigest()


def trust_device_for_2fa_web(user: BaseUser, response: web.Response) -> None:
    """Set cookie which will be used to decide whether 2fa is needed."""

    expiration = str(int(time.time()) + MONTH)
    signature = _sign_with_2fa_totp_secret(user, msg=expiration)
    value = f'{expiration}|{signature}'
    cookie_secure = services.config.app.cookie_secure
    response.set_cookie(
        TRUST_2FA_KEY, value=value, httponly=True, secure=cookie_secure, max_age=MONTH
    )


def _is_device_trusted_for_2fa_web(user: BaseUser, request: web.Request) -> bool:
    """
    Decide whether current device is trusted and second factor
    is not needed.
    """
    value = request.cookies.get(TRUST_2FA_KEY, '')
    if '|' not in value:
        return False
    expiration, signature = value.split('|', 2)
    # Check expiration date.
    if not expiration.isnumeric() or int(expiration) < time.time():
        return False
    # Check signature.
    expected_signature = _sign_with_2fa_totp_secret(user, expiration)
    if signature != expected_signature:
        return False
    return True


def delete_pending_2fa_web(session: Session) -> None:
    """Delete verify 2FA flag from session storage."""
    session.pop(PENDING_2FA_KEY, None)


def is_pending_2fa_web(session: Session) -> bool:
    """Get verify 2FA flag from current session.

    When flag in session it means - user still need to verify by 2FA
    """

    return PENDING_2FA_KEY in session


def get_pending_2fa_web(session: Session) -> Pending2FASchema | None:
    """Get verify 2FA flag from current session.

    When flag in session it means - user still need to verify by 2FA
    """

    raw_pending: dict[str, Any] | None = session.get(PENDING_2FA_KEY)
    if not raw_pending:
        return None
    return Pending2FASchema.model_validate(raw_pending)


def set_pending_2fa_web(
    session: Session,
    *,
    first_factor: AuthFactor,
    second_factor: AuthFactor,
) -> None:
    """
    Save pending 2FA state in session, it works as indicator that user needs to verify 2FA
    before accessing protected routes
    """
    state = Pending2FASchema(first_factor=first_factor, second_factor=second_factor)
    session[PENDING_2FA_KEY] = state.model_dump(mode='json')


def _build_email_2fa_token_key(user: BaseUser) -> str:
    return f'web_2fa:email_token:{user.id}'


async def get_email_2fa_token(user: BaseUser) -> str | None:
    """
    Get the 2FA token for email verification
    """
    return await services.redis.get(_build_email_2fa_token_key(user))


async def reset_email_2fa_token(user: BaseUser) -> None:
    """
    Reset the 2FA token for email verification
    """
    await services.redis.delete(_build_email_2fa_token_key(user))


async def generate_email_2fa_token(user: BaseUser) -> str:
    """
    Generate a 2FA token for email verification
    """

    token = token_urlsafe(27)

    await services.redis.set(_build_email_2fa_token_key(user), token, ex=60 * 30)  # 30 min

    return token


async def send_email_2fa_token_web(user: BaseUser) -> None:
    """
    Send email with link to verify 2FA by email for web application
    """

    assert user.email is not None

    token = await generate_email_2fa_token(user)
    await emailing.send_email(
        recipient_mixed=user.email,
        subject=_('Підтвердження двофакторної аутентифікації'),
        template_name='email_2fa_token',
        context={
            'url': build_url(
                path='/auth/email-2fa/verify-token',
                get={'token': token},
            ),
        },
    )
