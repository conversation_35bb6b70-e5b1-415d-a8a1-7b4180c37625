from __future__ import annotations

import dataclasses
import datetime
from dataclasses import dataclass, fields
from typing import TYPE_CHECKING, Literal, Named<PERSON><PERSON><PERSON>, Required, TypedDict, assert_never

import u<PERSON><PERSON>
from typing_extensions import TypeIs

from api import errors
from app.auth import helpers
from app.auth.enums import (
    AuthFlow,
    AuthMethod,
    RoleActivationSource,
    RoleStatus,
    UserEmailChangeStatus,
)
from app.auth.schemas import CompanyConfig
from app.auth.session_manager.types import LastActivity
from app.lib.database import DBRow
from app.lib.enums import DocumentListSortDate, Language, UserRole
from app.lib.types import DataDict
from app.registration.enums import RegistrationMethod
from app.sign_sessions.enums import SignSessionType

if TYPE_CHECKING:
    from app.sign_sessions.types import SignSessionExtended


@dataclass(kw_only=True)
class AuthUser:
    """
    AuthUser model represents user in sign session context only. Actual user might be registered
    or not registered in the system. It does not represent user in the system, but rather
    abstract user that does not have access to sign or view single document
    """

    email: str
    company_edrpou: str

    # Those fields are nullable, because AuthUser can be created for not existed user yet. If
    # in sign session user is required to be registered, check AuthUserExtended model.
    id: str | None  # user ID, not session ID
    role_id: str | None
    user_role: int | None
    company_id: str | None
    is_legal: bool | None

    # Permissions fields are set on the fly in auth_middleware based on sign session type
    # NOTE: permissions that are not presented here are considered as False. Before adding new
    # permission, evaluate carefully with security team if this action under that permission should
    # be allowed in sign session at all or user should login to the system to perform it.
    can_view_document: bool
    can_comment_document: bool
    can_download_document: bool
    can_print_document: bool
    can_delete_document: bool
    can_sign_and_reject_document: bool
    can_sign_and_reject_document_external: bool
    can_sign_and_reject_document_internal: bool

    # ID of documents that user have access to
    documents_ids: list[str]
    sign_session: SignSessionExtended

    @property
    def is_admin(self) -> bool:
        return self.user_role == UserRole.admin.value

    def to_dict(self) -> DataDict:
        return dataclasses.asdict(self)

    @staticmethod
    def from_sign_session(sign_session: SignSessionExtended) -> AuthUser:
        s = sign_session  # shortcut for readability

        # In both sign and view sessions we grant read access to document
        # TODO: Consider setting this flag to False for sign session users and rely only on
        # document_ids field to determine access rights. This flag grants wide access to almost
        # all documents in the company, which is later narrowed down to a single document by
        # the "document_ids" field.
        _can_view_document = True

        # Should be 4 cases:
        # - sign session, user without role
        # - sign session, user with role
        # - view session, user without role
        # - view session, user with role
        if s.type == SignSessionType.sign_session and not s.role_id:
            return AuthUser(
                id=None,
                user_role=None,
                company_id=None,
                email=s.email,
                role_id=s.role_id,
                company_edrpou=s.edrpou,
                is_legal=s.is_legal,
                # read permissions - always granted
                can_view_document=_can_view_document,
                can_download_document=True,
                can_print_document=True,
                # write permissions - allowed by default
                # TODO: consider to set write permissions to False, because we giving
                # higher permissions to the anonymous user than to the registered user.
                can_comment_document=False,  # false due to technical limitation
                can_sign_and_reject_document=True,
                can_sign_and_reject_document_external=True,
                can_sign_and_reject_document_internal=True,
                can_delete_document=True,
                documents_ids=[s.document_id],
                sign_session=s,
            )
        if s.type == SignSessionType.sign_session and s.role_id:
            return AuthUser(
                id=s.user_id,
                email=s.email,
                role_id=s.role_id,
                company_edrpou=s.edrpou,
                company_id=s.company_id,
                is_legal=s.is_legal,
                user_role=s.user_role,
                # read permissions - respect user's permissions (except view document)
                can_view_document=_can_view_document,
                can_download_document=s.can_download_document,
                can_print_document=s.can_print_document,
                # write permissions - respect user's permissions
                can_comment_document=s.can_comment_document,
                can_sign_and_reject_document=s.can_sign_and_reject_document,
                can_sign_and_reject_document_external=s.can_sign_and_reject_document_external,
                can_sign_and_reject_document_internal=s.can_sign_and_reject_document_internal,
                can_delete_document=s.can_delete_document,
                documents_ids=[s.document_id],
                sign_session=s,
            )
        if s.type == SignSessionType.view_session and not s.role_id:
            # For view session users we grant only read access to document and for sign session
            # type we grant full access to document whenever user is not registered in the system.
            # When user is registered in the system, we use his/her permissions + sign session
            # type to determine access rights.

            return AuthUser(
                id=None,
                user_role=None,
                company_id=None,
                email=s.email,
                role_id=s.role_id,
                company_edrpou=s.edrpou,
                is_legal=s.is_legal,
                # read permissions - always granted
                can_view_document=_can_view_document,
                can_download_document=True,
                can_print_document=True,
                # write permissions - always denied
                can_comment_document=False,
                can_sign_and_reject_document=False,
                can_sign_and_reject_document_external=False,
                can_sign_and_reject_document_internal=False,
                can_delete_document=False,
                documents_ids=[s.document_id],
                sign_session=s,
            )
        if s.type == SignSessionType.view_session and s.role_id:
            return AuthUser(
                id=s.user_id,
                email=s.email,
                role_id=s.role_id,
                company_edrpou=s.edrpou,
                company_id=s.company_id,
                is_legal=s.is_legal,
                user_role=s.user_role,
                # read permissions - respect user's permissions (except view document)
                can_view_document=_can_view_document,
                can_download_document=s.can_download_document,
                can_print_document=s.can_print_document,
                # write permissions - always denied
                can_comment_document=False,
                can_sign_and_reject_document=False,
                can_sign_and_reject_document_external=False,
                can_sign_and_reject_document_internal=False,
                can_delete_document=False,
                documents_ids=[s.document_id],
                sign_session=s,
            )

        raise ValueError(f'Unknown case: {sign_session.type=}, {s.role_id=}')


@dataclass(kw_only=True)
class AuthUserExtended(AuthUser):
    """
    This class represents an "upgraded" AuthUser when User is registered in the database.

    Allows to make AuthUser more strict and make some fields not nullable, when they are not
    nullable in User model.
    """

    # ... fields from AuthUser

    # Override nullable fields from AuthUser that are not nullable in User model
    id: str
    role_id: str
    user_role: int
    company_id: str
    company_edrpou: str
    is_legal: bool

    # ... additional fields from User model that does not exist in AuthUser
    first_name: str | None
    second_name: str | None
    last_name: str | None
    last_role_id: str | None
    company_name: str | None
    phone: str | None

    # User model itself. It's required to build AuthUserExtended
    user: User

    @property
    def full_name(self) -> str:
        """Combine non-empty name parts into full name"""
        parts_raw = [self.first_name, self.second_name, self.last_name]
        parts = [part for part in parts_raw if part]
        name = ' '.join(parts)
        return name.strip()

    @staticmethod
    def build(*, user: User, auth_user: AuthUser) -> AuthUserExtended:
        """
        Build new AuthUser based on AuthUser and required User model.
        """

        # In both sign and view sessions we grant read access to document
        can_view_document = True

        # Read permissions is always set to current user's permissions
        can_download_document = user.can_download_document
        can_print_document = user.can_print_document

        # Write permissions depend on sign session type and current user's permissions
        if auth_user.sign_session.type == SignSessionType.sign_session:
            can_comment_document = user.can_comment_document
            can_delete_document = user.can_delete_document
            can_sign_and_reject_document = user.can_sign_and_reject_document
            can_sign_and_reject_document_external = user.can_sign_and_reject_document_external
            can_sign_and_reject_document_internal = user.can_sign_and_reject_document_internal

        elif auth_user.sign_session.type == SignSessionType.view_session:
            can_comment_document = False
            can_delete_document = False
            can_sign_and_reject_document = False
            can_sign_and_reject_document_external = False
            can_sign_and_reject_document_internal = False

            # TODO: investigate if we should set user_role to "user" in case of view session,
            # because when user has "admin" role, we automatically grant them access to
            # do everything, no matter what separate permissions we are setting here.

        else:
            assert_never(auth_user.sign_session.type)

        return AuthUserExtended(
            # ... auth user fields
            email=auth_user.email,
            # ... permissions
            can_view_document=can_view_document,
            can_comment_document=can_comment_document,
            can_download_document=can_download_document,
            can_print_document=can_print_document,
            can_delete_document=can_delete_document,
            can_sign_and_reject_document=can_sign_and_reject_document,
            can_sign_and_reject_document_external=can_sign_and_reject_document_external,
            can_sign_and_reject_document_internal=can_sign_and_reject_document_internal,
            documents_ids=auth_user.documents_ids,
            sign_session=auth_user.sign_session,
            # ... user fields that overrides auth users fields
            id=user.id,
            role_id=user.role_id,
            user_role=user.user_role,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            is_legal=user.is_legal,
            # ... additional user fields (new to AuthUser)
            first_name=user.first_name,
            second_name=user.second_name,
            last_name=user.last_name,
            last_role_id=user.last_role_id,
            company_name=user.company_name,
            phone=user.phone,
            user=user,
        )


@dataclass
class RoleDB:
    """
    Represents a role in the database
    """

    id_: str
    company_id: str
    user_id: str

    # ID of the role that invited this role
    invited_by: str | None
    activated_by: str | None

    date_agreed: datetime.datetime
    date_created: datetime.datetime
    date_updated: datetime.datetime
    date_deleted: datetime.datetime | None
    date_invited: datetime.datetime | None
    date_activated: datetime.datetime | None

    # Access permissions
    allowed_ips: list[str]
    allowed_api_ips: list[str]
    can_comment_document: bool
    can_delete_document: bool
    can_download_document: bool
    can_edit_company: bool
    can_edit_roles: bool
    can_create_tags: bool
    can_edit_document_automation: bool
    can_edit_document_fields: bool
    can_edit_document_category: bool
    can_extract_document_structured_data: bool
    can_edit_document_structured_data: bool
    can_invite_coworkers: bool
    can_print_document: bool
    can_sign_and_reject_document: bool
    can_sign_and_reject_document_external: bool
    can_sign_and_reject_document_internal: bool
    can_upload_document: bool
    can_view_document: bool
    can_archive_documents: bool
    can_delete_archived_documents: bool
    can_view_coworkers: bool
    can_edit_templates: bool
    can_edit_directories: bool
    can_remove_itself_from_approval: bool
    can_change_document_signers_and_reviewers: bool
    can_delete_document_extended: bool
    can_download_actions: bool
    can_edit_company_contact: bool
    can_edit_required_fields: bool
    can_edit_security: bool
    can_view_private_document: bool

    user_role: int

    # Email notification permissions
    can_receive_notifications: bool
    can_receive_comments: bool
    can_receive_inbox: bool
    can_receive_inbox_as_default: bool
    can_receive_rejects: bool
    can_receive_reminders: bool
    can_receive_reviews: bool
    can_receive_review_process_finished: bool
    can_receive_review_process_finished_assigner: bool
    can_receive_sign_process_finished: bool
    can_receive_sign_process_finished_assigner: bool
    can_receive_access_to_doc: bool
    can_receive_delete_requests: bool
    can_receive_new_roles: bool
    can_receive_token_expiration: bool
    can_receive_email_change: bool
    can_receive_admin_role_deletion: bool

    # Useful meta
    has_few_signatures: bool
    has_few_reviews: bool

    # HRS
    has_hrs_role: bool

    # UI Settings
    show_invite_tooltip: bool
    sort_documents: DocumentListSortDate
    status: RoleStatus

    # Additional info
    position: str | None
    has_signed_documents: bool
    with_signature_key: bool
    is_counted_in_billing_limit: bool

    activation_source: RoleActivationSource | None

    @property
    def id(self) -> str:
        return self.id_

    @property
    def is_active(self) -> bool:
        return self.status == RoleStatus.active

    @property
    def is_admin(self) -> bool:
        return self.user_role == UserRole.admin

    @property
    def is_deleted(self) -> bool:
        return self.status == RoleStatus.deleted

    @property
    def is_denied(self) -> bool:
        return self.status == RoleStatus.denied

    @property
    def is_pending(self) -> bool:
        return self.status == RoleStatus.pending

    @staticmethod
    def from_row(row: DBRow) -> RoleDB:
        return RoleDB(
            id_=row.id,
            company_id=row.company_id,
            user_id=row.user_id,
            invited_by=row.invited_by,
            activated_by=row.activated_by,
            date_agreed=row.date_agreed,
            date_created=row.date_created,
            date_deleted=row.date_deleted,
            date_updated=row.date_updated,
            date_invited=row.date_invited,
            date_activated=row.date_activated,
            # Access permissions
            allowed_ips=row.allowed_ips,
            allowed_api_ips=row.allowed_api_ips,
            can_comment_document=row.can_comment_document,
            can_delete_document=row.can_delete_document,
            can_download_document=row.can_download_document,
            can_edit_company=row.can_edit_company,
            can_edit_roles=row.can_edit_roles,
            can_create_tags=row.can_create_tags,
            can_edit_document_automation=row.can_edit_document_automation,
            can_edit_document_fields=row.can_edit_document_fields,
            can_edit_document_category=row.can_edit_document_category,
            can_extract_document_structured_data=row.can_extract_document_structured_data,
            can_edit_document_structured_data=row.can_edit_document_structured_data,
            can_invite_coworkers=row.can_invite_coworkers,
            can_print_document=row.can_print_document,
            can_sign_and_reject_document=row.can_sign_and_reject_document,
            can_sign_and_reject_document_external=row.can_sign_and_reject_document_external,
            can_sign_and_reject_document_internal=row.can_sign_and_reject_document_internal,
            can_upload_document=row.can_upload_document,
            can_view_document=row.can_view_document,
            user_role=row.user_role,
            can_change_document_signers_and_reviewers=row.can_change_document_signers_and_reviewers,
            can_delete_document_extended=row.can_delete_document_extended,
            can_download_actions=row.can_download_actions,
            can_edit_company_contact=row.can_edit_company_contact,
            can_edit_required_fields=row.can_edit_required_fields,
            can_edit_security=row.can_edit_security,
            can_view_private_document=row.can_view_private_document,
            # Email notification permissions
            can_receive_notifications=row.can_receive_notifications,
            can_receive_comments=row.can_receive_comments,
            can_receive_inbox=row.can_receive_inbox,
            can_receive_inbox_as_default=row.can_receive_inbox_as_default,
            can_receive_rejects=row.can_receive_rejects,
            can_receive_reminders=row.can_receive_reminders,
            can_receive_reviews=row.can_receive_reviews,
            can_receive_review_process_finished=row.can_receive_review_process_finished,
            can_receive_review_process_finished_assigner=row.can_receive_review_process_finished_assigner,
            can_receive_sign_process_finished=row.can_receive_sign_process_finished,
            can_receive_sign_process_finished_assigner=row.can_receive_sign_process_finished_assigner,
            can_receive_access_to_doc=row.can_receive_access_to_doc,
            can_receive_delete_requests=row.can_receive_delete_requests,
            can_archive_documents=row.can_archive_documents,
            can_delete_archived_documents=row.can_delete_archived_documents,
            can_view_coworkers=row.can_view_coworkers,
            can_edit_templates=row.can_edit_templates,
            can_edit_directories=row.can_edit_directories,
            can_remove_itself_from_approval=row.can_remove_itself_from_approval,
            can_receive_new_roles=row.can_receive_new_roles,
            can_receive_token_expiration=row.can_receive_token_expiration,
            can_receive_email_change=row.can_receive_email_change,
            can_receive_admin_role_deletion=row.can_receive_admin_role_deletion,
            # UI Settings
            show_invite_tooltip=row.show_invite_tooltip,
            sort_documents=row.sort_documents,
            status=row.status,
            # Additional info
            position=row.position,
            has_few_signatures=row.has_few_signatures,
            has_few_reviews=row.has_few_reviews,
            has_signed_documents=row.has_signed_documents,
            with_signature_key=row.with_signature_key,
            activation_source=row.activation_source,
            is_counted_in_billing_limit=row.is_counted_in_billing_limit,
            has_hrs_role=row.has_hrs_role,
        )


@dataclass
class Role(RoleDB):
    """
    Represents a role in database + additional company and user info
    """

    # ... all the fields from RoleDB

    # Company details
    company_edrpou: str
    company_full_name: str
    company_id: str
    company_name: str
    company_ipn: str
    is_dealer: bool
    is_legal: bool

    # User details
    user_email: str
    user_id: str


class UpdateRoleDict(TypedDict, total=False):
    invited_by: str | None
    deleted_by: str | None
    activated_by: str | None
    date_agreed: datetime.datetime
    date_updated: datetime.datetime
    date_invited: datetime.datetime | None
    date_deleted: datetime.datetime | None
    date_activated: datetime.datetime | None
    activation_source: RoleActivationSource

    # Access permissions
    allowed_ips: list[str]
    allowed_api_ips: list[str]
    can_comment_document: bool
    can_delete_document: bool
    can_download_document: bool
    can_edit_company: bool
    can_edit_roles: bool
    can_create_tags: bool
    can_edit_document_automation: bool
    can_edit_document_fields: bool
    can_edit_document_category: bool
    can_extract_document_structured_data: bool
    can_edit_document_structured_data: bool
    can_invite_coworkers: bool
    can_print_document: bool
    can_sign_and_reject_document: bool
    can_sign_and_reject_document_external: bool
    can_sign_and_reject_document_internal: bool
    can_upload_document: bool
    can_view_document: bool
    can_archive_documents: bool
    can_delete_archived_documents: bool
    can_view_coworkers: bool
    can_edit_templates: bool
    can_edit_directories: bool
    can_remove_itself_from_approval: bool
    can_change_document_signers_and_reviewers: bool
    can_delete_document_extended: bool
    can_download_actions: bool
    can_edit_company_contact: bool
    can_edit_required_fields: bool
    can_edit_security: bool
    can_view_private_document: bool
    user_role: int

    # Email notification permissions
    can_receive_notifications: bool
    can_receive_comments: bool
    can_receive_inbox: bool
    can_receive_inbox_as_default: bool
    can_receive_rejects: bool
    can_receive_reminders: bool
    can_receive_reviews: bool
    can_receive_review_process_finished: bool
    can_receive_review_process_finished_assigner: bool
    can_receive_sign_process_finished: bool
    can_receive_sign_process_finished_assigner: bool
    can_receive_access_to_doc: bool
    can_receive_delete_requests: bool
    can_receive_finished_docs: bool
    can_receive_new_roles: bool
    can_receive_token_expiration: bool
    can_receive_email_change: bool
    can_receive_admin_role_deletion: bool

    # Useful meta
    has_few_signatures: bool
    has_few_reviews: bool

    # UI Settings
    show_invite_tooltip: bool
    show_child_documents: bool
    sort_documents: DocumentListSortDate
    status: RoleStatus
    is_default_recipient: bool

    # HRS
    has_hrs_role: bool

    # Additional info
    position: str | None
    has_signed_documents: bool
    is_counted_in_billing_limit: bool


class InsertRoleDict(TypedDict, total=False):
    company_id: Required[str]
    user_id: Required[str]
    invited_by: str | None
    deleted_by: str | None
    activated_by: str | None
    date_agreed: datetime.datetime
    date_updated: datetime.datetime
    date_invited: datetime.datetime | None
    date_deleted: datetime.datetime | None
    date_activated: datetime.datetime | None
    activation_source: RoleActivationSource

    # Access permissions
    allowed_ips: list[str]
    allowed_api_ips: list[str]
    can_comment_document: bool
    can_delete_document: bool
    can_download_document: bool
    can_edit_company: bool
    can_edit_roles: bool
    can_create_tags: bool
    can_edit_document_automation: bool
    can_edit_document_fields: bool
    can_edit_document_category: bool
    can_extract_document_structured_data: bool
    can_edit_document_structured_data: bool
    can_invite_coworkers: bool
    can_print_document: bool
    can_sign_and_reject_document: bool
    can_sign_and_reject_document_external: bool
    can_sign_and_reject_document_internal: bool
    can_upload_document: bool
    can_view_document: bool
    can_archive_documents: bool
    can_delete_archived_documents: bool
    can_view_coworkers: bool
    can_edit_templates: bool
    can_edit_directories: bool
    can_remove_itself_from_approval: bool
    can_change_document_signers_and_reviewers: bool
    can_delete_document_extended: bool
    can_download_actions: bool
    can_edit_company_contact: bool
    can_edit_required_fields: bool
    can_edit_security: bool
    can_view_private_document: bool
    user_role: int

    # Email notification permissions
    can_receive_notifications: bool
    can_receive_comments: bool
    can_receive_inbox: bool
    can_receive_inbox_as_default: bool
    can_receive_rejects: bool
    can_receive_reminders: bool
    can_receive_reviews: bool
    can_receive_review_process_finished: bool
    can_receive_review_process_finished_assigner: bool
    can_receive_sign_process_finished: bool
    can_receive_sign_process_finished_assigner: bool
    can_receive_access_to_doc: bool
    can_receive_delete_requests: bool
    can_receive_finished_docs: bool
    can_receive_new_roles: bool
    can_receive_token_expiration: bool
    can_receive_email_change: bool
    can_receive_admin_role_deletion: bool

    # Useful meta
    has_few_signatures: bool
    has_few_reviews: bool

    # HRS
    has_hrs_role: bool

    # UI Settings
    show_invite_tooltip: bool
    show_child_documents: bool
    sort_documents: DocumentListSortDate
    is_default_recipient: bool

    status: RoleStatus

    # Additional info
    position: str | None
    has_signed_documents: bool
    with_signature_key: bool
    is_counted_in_billing_limit: bool


class AuthCtx(NamedTuple):
    method: AuthMethod | None
    company_config: CompanyConfig
    last_activity: LastActivity | None
    user: AuthUser | BaseUser | User | None
    super_admin_permissions: set[str] | None = None


def to_role(data: DBRow) -> Role | None:
    if not data:
        return None
    return Role(
        id_=data.id,
        date_agreed=data.date_agreed,
        date_created=data.date_created,
        date_deleted=data.date_deleted,
        date_updated=data.date_updated,
        date_invited=data.date_invited,
        date_activated=data.date_activated,
        invited_by=data.invited_by,
        activated_by=data.activated_by,
        # Access permissions
        allowed_ips=data.allowed_ips,
        allowed_api_ips=data.allowed_api_ips or [],
        can_comment_document=data.can_comment_document,
        can_delete_document=data.can_delete_document,
        can_download_document=data.can_download_document,
        can_edit_company=data.can_edit_company,
        can_edit_roles=data.can_edit_roles,
        can_create_tags=data.can_create_tags,
        can_edit_document_automation=data.can_edit_document_automation,
        can_edit_document_fields=data.can_edit_document_fields,
        can_edit_document_category=data.can_edit_document_category,
        can_extract_document_structured_data=data.can_extract_document_structured_data,
        can_edit_document_structured_data=data.can_edit_document_structured_data,
        can_invite_coworkers=data.can_invite_coworkers,
        can_print_document=data.can_print_document,
        can_sign_and_reject_document=data.can_sign_and_reject_document,
        can_sign_and_reject_document_external=data.can_sign_and_reject_document_external,
        can_sign_and_reject_document_internal=data.can_sign_and_reject_document_internal,
        can_upload_document=data.can_upload_document,
        can_view_document=data.can_view_document,
        can_archive_documents=data.can_archive_documents,
        can_delete_archived_documents=data.can_delete_archived_documents,
        can_view_coworkers=data.can_view_coworkers,
        can_edit_templates=data.can_edit_templates,
        can_edit_directories=data.can_edit_directories,
        can_remove_itself_from_approval=data.can_remove_itself_from_approval,
        can_change_document_signers_and_reviewers=data.can_change_document_signers_and_reviewers,
        can_delete_document_extended=data.can_delete_document_extended,
        can_download_actions=data.can_download_actions,
        can_edit_company_contact=data.can_edit_company_contact,
        can_edit_required_fields=data.can_edit_required_fields,
        can_edit_security=data.can_edit_security,
        can_view_private_document=data.can_view_private_document,
        is_counted_in_billing_limit=data.is_counted_in_billing_limit,
        has_hrs_role=data.has_hrs_role,
        user_role=data.user_role,
        # Email notification permissions
        can_receive_notifications=data.can_receive_notifications,
        can_receive_comments=data.can_receive_comments,
        can_receive_inbox=data.can_receive_inbox,
        can_receive_inbox_as_default=data.can_receive_inbox_as_default,
        can_receive_rejects=data.can_receive_rejects,
        can_receive_reminders=data.can_receive_reminders,
        can_receive_reviews=data.can_receive_reviews,
        can_receive_review_process_finished=data.can_receive_review_process_finished,
        can_receive_review_process_finished_assigner=data.can_receive_review_process_finished_assigner,
        can_receive_sign_process_finished=data.can_receive_sign_process_finished,
        can_receive_sign_process_finished_assigner=data.can_receive_sign_process_finished_assigner,
        can_receive_access_to_doc=data.can_receive_access_to_doc,
        can_receive_delete_requests=data.can_receive_delete_requests,
        can_receive_new_roles=data.can_receive_new_roles,
        can_receive_token_expiration=data.can_receive_token_expiration,
        can_receive_email_change=data.can_receive_email_change,
        can_receive_admin_role_deletion=data.can_receive_admin_role_deletion,
        # UI Settings
        show_invite_tooltip=data.show_invite_tooltip,
        sort_documents=data.sort_documents,
        status=data.status,
        # Additional info
        position=data.position,
        has_few_signatures=data.has_few_signatures,
        has_few_reviews=data.has_few_reviews,
        has_signed_documents=data.has_signed_documents,
        with_signature_key=data.with_signature_key,
        activation_source=data.activation_source,
        # Company details
        company_edrpou=data.company_edrpou,
        company_full_name=data.company_full_name,
        company_id=data.company_id,
        company_name=data.company_name,
        company_ipn=data.company_ipn,
        is_dealer=data.is_dealer,
        is_legal=data.is_legal,
        # User details
        user_email=data.user_email,
        user_id=data.user_id,
    )


@dataclass(frozen=True, slots=True)
class SuperAdminPermissions:
    can_view_client_data: bool
    can_edit_client_data: bool
    can_edit_special_features: bool

    def to_permissions_set(self) -> set[str]:
        """Returns only allowed permissions names (set to true)"""
        return {field.name for field in fields(self) if getattr(self, field.name)}

    @classmethod
    def from_db(cls, row: DBRow) -> SuperAdminPermissions:
        values = {field.name: getattr(row, field.name, False) for field in fields(cls)}
        return cls(**values)


@dataclass(frozen=True)
class BaseUser:
    id: str
    created_by: str | None
    last_role_id: str | None
    pending_referrer_role_id: str | None
    # INFO: email can be None if user registered with phone only
    email: str | None
    password: str | None  # it's hash, it's not real password
    phone: str | None
    auth_phone: str | None
    phone_salt: str
    first_name: str | None
    second_name: str | None
    last_name: str | None
    promo_code: str | None
    email_confirmed: bool
    is_logged_once: bool
    is_placeholder: bool
    is_banned: bool
    registration_completed: bool
    is_autogenerated_password: bool
    is_phone_verified: bool
    is_2fa_enabled: bool
    trial_auto_enabled: bool
    telegram_chat_id: int | None
    source: str
    is_subscribed_esputnik: bool
    google_id: str | None
    microsoft_id: str | None
    apple_id: str | None
    language: Language | None
    date_created: datetime.datetime
    date_updated: datetime.datetime
    date_deleted: datetime.datetime | None
    registration_method: str | None
    extra: DataDict | None

    _row: DBRow

    @staticmethod
    def from_row(row: DBRow) -> BaseUser:
        return BaseUser(
            id=row.id,
            created_by=row.created_by,
            last_role_id=row.last_role_id,
            pending_referrer_role_id=row.pending_referrer_role_id,
            is_placeholder=row.is_placeholder,
            is_banned=row.is_banned,
            email=row.email,
            password=row.password,
            phone=row.phone,
            auth_phone=row.auth_phone,
            phone_salt=row.phone_salt,
            first_name=row.first_name,
            second_name=row.second_name,
            last_name=row.last_name,
            promo_code=row.promo_code,
            email_confirmed=row.email_confirmed,
            registration_completed=row.registration_completed,
            is_autogenerated_password=row.is_autogenerated_password,
            is_phone_verified=row.is_phone_verified,
            is_2fa_enabled=row.is_2fa_enabled,
            trial_auto_enabled=row.trial_auto_enabled,
            telegram_chat_id=row.telegram_chat_id,
            source=row.source,
            is_subscribed_esputnik=row.is_subscribed_esputnik,
            google_id=row.google_id,
            microsoft_id=row.microsoft_id,
            apple_id=row.apple_id,
            language=row.language,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_deleted=row.date_deleted,
            is_logged_once=row.is_logged_once,
            registration_method=row.registration_method,
            extra=row.extra,
            _row=row,
        )

    def to_dict(self) -> DataDict:
        """Convert dataclass instance to dict, ignore "_row" field"""
        value = {}
        for field in dataclasses.fields(self):
            if field.name != '_row':
                value[field.name] = getattr(self, field.name)

        return value

    @property
    def full_name(self) -> str:
        """Combine non-empty name parts into full name"""
        parts_raw = [self.first_name, self.second_name, self.last_name]
        parts = [part for part in parts_raw if part]
        name = ' '.join(parts)
        return name.strip()

    def is_valid_password(self, password: str | None) -> bool:
        """
        Check if the given password matches the hash of the user's password.

        NOTE: use "validate_is_user_login_password_valid" or
        "validate_is_user_current_password_valid" functions whenever possible, because it also
        has proper error handling in case of invalid password.
        """

        # password is invalid if it's empty or None
        if not password:
            return False

        # also we can't check password if current password is None
        if not self.password:
            return False

        # Compare password hashes
        return helpers.check_password_hash(
            hash_str=self.password,
            password=password,
        )


class UpdateBaseUserDict(TypedDict, total=False):
    """
    Represents a dict with fields that can be passed to the "update_user" function.
    """

    # this field will be hashed and converted to "password" field if present
    email: str | None
    new_password: str | None
    password: str | None
    created_by: str | None
    last_role_id: str | None
    pending_referrer_role_id: str | None
    phone: str | None
    auth_phone: str | None
    first_name: str | None
    second_name: str | None
    last_name: str | None
    email_confirmed: bool
    registration_completed: bool
    is_autogenerated_password: bool
    is_phone_verified: bool
    promo_code: str | None
    is_2fa_enabled: bool
    trial_auto_enabled: bool
    telegram_chat_id: str | None
    is_subscribed_esputnik: bool
    source: str | None  # RegistrationSource
    google_id: str | None
    apple_id: str | None
    microsoft_id: str | None
    language: Language | None
    registration_method: RegistrationMethod | None
    date_updated: datetime.datetime
    is_logged_once: bool
    is_placeholder: bool
    is_banned: bool
    date_created: datetime.datetime
    date_deleted: datetime.datetime


class InsertBaseUserDict(TypedDict):
    """
    Represents a dict with fields that can be passed to the "insert_user" function.
    """

    id: str
    email: str | None
    password: str | None  # not hashed password, it will be hashed before saving
    created_by: str | None
    pending_referrer_role_id: str | None
    phone: str | None
    auth_phone: str | None
    first_name: str | None
    second_name: str | None
    last_name: str | None
    email_confirmed: bool
    registration_completed: bool
    is_autogenerated_password: bool
    is_phone_verified: bool
    promo_code: str | None
    trial_auto_enabled: bool
    source: str | None  # RegistrationSource
    google_id: str | None
    apple_id: str | None
    microsoft_id: str | None
    registration_method: RegistrationMethod | None
    is_placeholder: bool
    date_created: datetime.datetime


@dataclass(frozen=True)
class User(BaseUser):
    """User with required role and company info"""

    # user attributes (inherited from BaseUser)

    # role attributes
    role_id: str
    can_view_document: bool
    can_comment_document: bool
    can_upload_document: bool
    can_download_document: bool
    can_print_document: bool
    can_delete_document: bool
    can_sign_and_reject_document: bool
    can_sign_and_reject_document_external: bool
    can_sign_and_reject_document_internal: bool
    can_invite_coworkers: bool
    can_edit_company: bool
    can_edit_roles: bool
    can_create_tags: bool
    can_edit_document_automation: bool
    can_edit_document_fields: bool
    can_edit_document_category: bool
    can_extract_document_structured_data: bool
    can_edit_document_structured_data: bool
    can_archive_documents: bool
    can_delete_archived_documents: bool
    can_view_coworkers: bool
    can_receive_inbox: bool
    can_receive_inbox_as_default: bool
    can_receive_comments: bool
    can_receive_rejects: bool
    can_receive_reminders: bool
    can_receive_reviews: bool
    can_receive_review_process_finished: bool
    can_receive_review_process_finished_assigner: bool
    can_receive_sign_process_finished: bool
    can_receive_sign_process_finished_assigner: bool
    can_receive_notifications: bool
    can_receive_access_to_doc: bool
    can_receive_delete_requests: bool
    can_receive_new_roles: bool
    can_receive_token_expiration: bool
    can_receive_email_change: bool
    can_receive_admin_role_deletion: bool
    can_edit_templates: bool
    can_edit_directories: bool
    can_remove_itself_from_approval: bool
    can_edit_security: bool
    can_download_actions: bool
    can_change_document_signers_and_reviewers: bool
    can_delete_document_extended: bool
    can_edit_company_contact: bool
    can_edit_required_fields: bool
    can_view_private_document: bool
    role_allowed_ips: list[str]
    role_allowed_api_ips: list[str]
    role_sort_documents: DocumentListSortDate
    show_child_documents: bool
    user_role: UserRole
    role_status: RoleStatus
    is_default_recipient: bool
    position: str | None

    # HRS
    has_hrs_role: bool

    # company attributes
    company_id: str
    company_edrpou: str
    company_name: str | None
    company_full_name: str | None
    company_email_domains: list[str]
    company_allowed_ips: list[str]
    company_allowed_api_ips: list[str]
    is_legal: bool

    # useful meta attributes
    has_few_signatures: bool
    has_few_reviews: bool
    is_counted_in_billing_limit: bool

    # From "tokens" table (only for API users)
    token_hash: str | None
    token_vendor: str | None
    raw_token: str | None  # not empty only in test environment

    @staticmethod
    def from_base_row(row: DBRow) -> BaseUser | User:
        """
        Convert a database row into a User or BaseUser instance, depending on whether the
        role_id is present in the given row. This function is useful to convert user, that was
        selected during authentification, to a typed object, where User and BaseUser are both
        possible.

        During authentification, we don't enforce users to have a role, so technically all
        @login_required users should receive a BaseUser | User object. But in reality,
        most handlers wrapped with @login_required decorator expect a User object, several handlers
        expect a BaseUser object (before the first role is added) and only a few cases where
        handling both types are needed.
        """

        if row.role_id:
            return User.from_row(row)
        return BaseUser.from_row(row)

    @staticmethod
    def from_row(row: DBRow) -> User:
        return User(
            # user attributes
            id=row.id,
            created_by=row.created_by,
            last_role_id=row.last_role_id,
            pending_referrer_role_id=row.pending_referrer_role_id,
            email=row.email,
            password=row.password,
            phone=row.phone,
            auth_phone=row.auth_phone,
            phone_salt=row.phone_salt,
            first_name=row.first_name,
            second_name=row.second_name,
            last_name=row.last_name,
            promo_code=row.promo_code,
            email_confirmed=row.email_confirmed,
            is_logged_once=row.is_logged_once,
            is_placeholder=row.is_placeholder,
            is_banned=row.is_banned,
            registration_completed=row.registration_completed,
            is_autogenerated_password=row.is_autogenerated_password,
            is_phone_verified=row.is_phone_verified,
            is_2fa_enabled=row.is_2fa_enabled,
            trial_auto_enabled=row.trial_auto_enabled,
            telegram_chat_id=row.telegram_chat_id,
            source=row.source,
            is_subscribed_esputnik=row.is_subscribed_esputnik,
            google_id=row.google_id,
            microsoft_id=row.microsoft_id,
            apple_id=row.apple_id,
            language=row.language,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_deleted=row.date_deleted,
            registration_method=row.registration_method,
            extra=row.extra,
            # roles attributes
            role_id=row.role_id,
            can_view_document=row.can_view_document,
            can_comment_document=row.can_comment_document,
            can_upload_document=row.can_upload_document,
            can_download_document=row.can_download_document,
            can_print_document=row.can_print_document,
            can_delete_document=row.can_delete_document,
            can_sign_and_reject_document=row.can_sign_and_reject_document,
            can_sign_and_reject_document_external=row.can_sign_and_reject_document_external,
            can_sign_and_reject_document_internal=row.can_sign_and_reject_document_internal,
            can_invite_coworkers=row.can_invite_coworkers,
            can_edit_company=row.can_edit_company,
            can_edit_roles=row.can_edit_roles,
            can_create_tags=row.can_create_tags,
            can_edit_document_automation=row.can_edit_document_automation,
            can_edit_document_fields=row.can_edit_document_fields,
            can_edit_document_category=row.can_edit_document_category,
            can_extract_document_structured_data=row.can_extract_document_structured_data,
            can_edit_document_structured_data=row.can_edit_document_structured_data,
            can_archive_documents=row.can_archive_documents,
            can_delete_archived_documents=row.can_delete_archived_documents,
            can_view_coworkers=row.can_view_coworkers,
            can_edit_templates=row.can_edit_templates,
            can_edit_directories=row.can_edit_directories,
            can_remove_itself_from_approval=row.can_remove_itself_from_approval,
            can_edit_security=row.can_edit_security,
            can_download_actions=row.can_download_actions,
            can_change_document_signers_and_reviewers=row.can_change_document_signers_and_reviewers,
            can_delete_document_extended=row.can_delete_document_extended,
            can_edit_company_contact=row.can_edit_company_contact,
            can_edit_required_fields=row.can_edit_required_fields,
            can_view_private_document=row.can_view_private_document,
            can_receive_inbox=row.can_receive_inbox,
            can_receive_inbox_as_default=row.can_receive_inbox_as_default,
            can_receive_comments=row.can_receive_comments,
            can_receive_rejects=row.can_receive_rejects,
            can_receive_reminders=row.can_receive_reminders,
            can_receive_reviews=row.can_receive_reviews,
            can_receive_review_process_finished=row.can_receive_review_process_finished,
            can_receive_review_process_finished_assigner=row.can_receive_review_process_finished_assigner,
            can_receive_sign_process_finished=row.can_receive_review_process_finished,
            can_receive_sign_process_finished_assigner=row.can_receive_sign_process_finished_assigner,
            can_receive_notifications=row.can_receive_notifications,
            can_receive_access_to_doc=row.can_receive_access_to_doc,
            can_receive_delete_requests=row.can_receive_delete_requests,
            can_receive_new_roles=row.can_receive_new_roles,
            can_receive_token_expiration=row.can_receive_token_expiration,
            can_receive_email_change=row.can_receive_email_change,
            can_receive_admin_role_deletion=row.can_receive_admin_role_deletion,
            role_allowed_ips=row.role_allowed_ips,
            role_allowed_api_ips=row.role_allowed_api_ips,
            role_sort_documents=row.role_sort_documents,
            show_child_documents=row.show_child_documents,
            user_role=UserRole(row.user_role),
            role_status=row.role_status,
            is_default_recipient=row.is_default_recipient,
            position=row.position,
            # companies attributes
            company_id=row.company_id,
            company_edrpou=row.company_edrpou,
            company_name=row.company_name,
            company_full_name=row.company_full_name,
            company_email_domains=row.company_email_domains,
            company_allowed_ips=row.company_allowed_ips,
            company_allowed_api_ips=row.company_allowed_api_ips,
            is_legal=row.is_legal,
            _row=row,
            # useful meta attributes
            has_few_signatures=row.has_few_signatures,
            has_few_reviews=row.has_few_reviews,
            has_hrs_role=row.has_hrs_role,
            is_counted_in_billing_limit=row.is_counted_in_billing_limit,
            # From "tokens" table (only for API users)
            token_hash=row.get('token_hash', None),
            token_vendor=row.get('token_vendor', None),
            raw_token=row.get('raw_token', None),  # not empty only in test environment
        )

    @property
    def is_active(self) -> bool:
        return self.registration_completed and self.role_status == RoleStatus.active

    @property
    def is_pending(self) -> bool:
        return self.role_status == RoleStatus.pending

    @property
    def is_admin(self) -> bool:
        return self.user_role == UserRole.admin

    @property
    def allowed_ips(self) -> list[str]:
        return self.role_allowed_ips + self.company_allowed_ips


@dataclass
class Company:
    id: str
    edrpou: str
    ipn: str | None
    name: str | None
    full_name: str | None
    is_legal: bool
    dealt_by_company: str | None
    dealt_by_user: str | None
    is_dealer: bool
    render_signature_in_interface: bool
    render_signature_on_print_document: bool
    upload_documents_left: int
    is_unlimited_upload: bool
    activity_field: str | None
    employees_number: str | None
    phone: str | None
    email_domains: list[str] | None
    allowed_ips: list[str]
    allowed_api_ips: list[str]
    inactivity_timeout: int
    date_created: datetime.datetime
    date_updated: datetime.datetime
    _row: DBRow

    @staticmethod
    def from_row(row: DBRow) -> Company:
        return Company(
            id=row.id,
            edrpou=row.edrpou,
            ipn=row.ipn,
            name=row.name,
            full_name=row.full_name,
            is_legal=row.is_legal,
            dealt_by_company=row.dealt_by_company,
            dealt_by_user=row.dealt_by_user,
            is_dealer=row.is_dealer,
            render_signature_in_interface=row.render_signature_in_interface,
            render_signature_on_print_document=row.render_signature_on_print_document,
            upload_documents_left=row.upload_documents_left,
            is_unlimited_upload=row.is_unlimited_upload,
            activity_field=row.activity_field,
            employees_number=row.employees_number,
            phone=row.phone,
            email_domains=row.email_domains,
            allowed_ips=row.allowed_ips,
            allowed_api_ips=row.allowed_api_ips,
            inactivity_timeout=row.inactivity_timeout,
            date_created=row.date_created,
            date_updated=row.date_updated,
            _row=row,
        )


@dataclass
class LoginUserCtx:
    email: str
    password: str
    remember: bool
    user: BaseUser


@dataclass
class AuthUserResponse:
    """
    Response for after user logged in or registered successfully
    """

    flow: AuthFlow
    next_url: str
    is_2fa_enabled: bool
    email: str | None = None

    def to_dict(self) -> DataDict:
        return {
            'flow': self.flow.value,
            'email': self.email,
            'next_url': self.next_url,
            'is_2fa_enabled': self.is_2fa_enabled,
        }


class RolesGroup:
    """
    Represent a group of roles as one object. Helps to get role by email and EDRPOU
    """

    def __init__(self, roles: list[Role]) -> None:
        self._roles = roles

        # Group objects by email and EDRPOU
        self._group: dict[tuple[str, str], Role] = {
            (role.user_email.lower(), role.company_edrpou): role for role in roles
        }

    def get(self, *, email: str, edrpou: str) -> Role | None:
        """Get role by email and EDRPOU."""
        return self._group.get((email.lower(), edrpou))


@dataclass
class CompanyConfigRow:
    company_id: str
    config: CompanyConfig


@dataclass
class CompanyForIndexation:
    id: str
    edrpou: str
    name: str
    full_name: str
    date_created: datetime.datetime

    @staticmethod
    def from_row(row: DBRow) -> CompanyForIndexation:
        return CompanyForIndexation(
            id=row.id,
            edrpou=row.edrpou,
            name=row.name,
            full_name=row.full_name,
            date_created=row.date_created,
        )


@dataclass
class RoleForIndexation:
    # role attributes
    id: str
    role_status: RoleStatus
    date_created: datetime.datetime
    # user attributes
    user_email: str
    user_name: str
    user_registration_completed: bool
    # company attributes
    company_id: str
    company_edrpou: str
    company_name: str
    company_full_name: str

    @staticmethod
    def from_row(row: DBRow) -> RoleForIndexation:
        user_name_parts_raw = [row.user_first_name, row.user_second_name, row.user_last_name]
        user_name_parts = [part for part in user_name_parts_raw if part]
        user_name = ' '.join(user_name_parts)

        return RoleForIndexation(
            id=row.id,
            date_created=row.date_created,
            role_status=row.role_status,
            user_email=row.user_email,
            user_name=user_name,
            user_registration_completed=row.user_registration_completed,
            company_id=row.company_id,
            company_edrpou=row.company_edrpou,
            company_name=row.company_name,
            company_full_name=row.company_full_name,
        )

    @property
    def is_active_role(self) -> bool:
        return self.role_status == RoleStatus.active and self.user_registration_completed


@dataclass
class UserForGeneratingSigner:
    email: str
    registration_completed: bool
    pending_referrer_role_id: str | None
    first_name: str | None
    second_name: str | None
    last_name: str | None
    role_id: str | None
    status: RoleStatus | None

    @staticmethod
    def from_row(row: DBRow) -> UserForGeneratingSigner:
        return UserForGeneratingSigner(
            registration_completed=row.registration_completed,
            pending_referrer_role_id=row.pending_referrer_role_id,
            first_name=row.first_name,
            second_name=row.second_name,
            last_name=row.last_name,
            email=row.email,
            role_id=row.role_id,
            status=row.status,
        )


@dataclass(frozen=True)
class LastRoleDetails:
    role_id: str | None
    company_id: str | None
    company_edrpou: str | None


@dataclass(frozen=True)
class LogoutAllSessionsCtx:
    logout_user_id: str
    with_mobile: bool
    terminate_current_session: bool


@dataclass
class UserEmailChange:
    id: str
    user_id: str
    new_email: str
    old_email: str | None
    status: UserEmailChangeStatus
    date_created: datetime.datetime
    date_updated: datetime.datetime
    date_contacts_synced: datetime.datetime | None

    @staticmethod
    def from_row(row: DBRow) -> UserEmailChange:
        return UserEmailChange(
            id=row.id,
            user_id=row.user_id,
            new_email=row.new_email,
            old_email=row.old_email,
            status=row.status,
            date_created=row.date_created,
            date_updated=row.date_updated,
            date_contacts_synced=row.date_contacts_synced,
        )


USER_PERMISSION_KEY = Literal[
    'can_archive_documents',
    'can_change_document_signers_and_reviewers',
    'can_comment_document',
    'can_create_tags',
    'can_delete_archived_documents',
    'can_delete_document',
    'can_delete_document_extended',
    'can_download_actions',
    'can_download_document',
    'can_edit_company',
    'can_edit_company_contact',
    'can_edit_directories',
    'can_edit_document_automation',
    'can_edit_document_category',
    'can_extract_document_structured_data',
    'can_edit_document_structured_data',
    'can_edit_document_fields',
    'can_edit_required_fields',
    'can_edit_roles',
    'can_edit_security',
    'can_edit_templates',
    'can_invite_coworkers',
    'can_print_document',
    'can_remove_itself_from_approval',
    'can_sign_and_reject_document',
    'can_sign_and_reject_document_internal',
    'can_sign_and_reject_document_external',
    'can_upload_document',
    'can_view_coworkers',
    'can_view_document',
    'can_view_private_document',
    # NOT implemented yet, wait  for new versions
    'can_add_document_versions',
]

USER_NOTIFICATION_SETTING_KEY = Literal[
    'can_receive_inbox',
    'can_receive_comments',
    'can_receive_rejects',
    'can_receive_reminders',
    'can_receive_reviews',
    'can_receive_review_process_finished',
    'can_receive_review_process_finished_assigner',
    'can_receive_sign_process_finished',
    'can_receive_sign_process_finished_assigner',
    'can_receive_access_to_doc',
    'can_receive_delete_requests',
    'can_receive_inbox_as_default',
    'can_receive_new_roles',
    'can_receive_admin_role_deletion',
    'can_receive_email_change',
    'can_receive_token_expiration',
]


@dataclass(frozen=True)
class SyncRolesResults:
    """
    Results of sync_user_roles function
    """

    new_roles: list[str]  # list[role_id]
    errors: dict[str, errors.Error]  # dict[company_edrpou, error]

    def to_json(self) -> str:
        return ujson.dumps(
            {
                'new_roles': self.new_roles,
                'errors': {edrpou: error.to_dict() for edrpou, error in self.errors.items()},
            }
        )


@dataclass
class RolePermissions:
    can_view_document: bool
    can_comment_document: bool
    can_upload_document: bool
    can_download_document: bool
    can_print_document: bool
    can_delete_document: bool
    can_sign_and_reject_document: bool
    can_sign_and_reject_document_external: bool
    can_sign_and_reject_document_internal: bool
    can_invite_coworkers: bool
    can_edit_company: bool
    can_edit_roles: bool
    can_create_tags: bool
    can_edit_document_automation: bool
    can_edit_document_fields: bool
    can_archive_documents: bool
    can_edit_templates: bool
    can_edit_directories: bool
    can_delete_archived_documents: bool
    can_remove_itself_from_approval: bool
    can_view_coworkers: bool


@dataclass(frozen=True)
class ZvitUser:
    id: str
    email: str
    password: str
    imported_by_role_id: str
    date_created: datetime.datetime

    @staticmethod
    def from_row(row: DBRow) -> ZvitUser:
        return ZvitUser(
            id=row.id,
            email=row.email,
            password=row.password,
            imported_by_role_id=row.imported_by_role_id,
            date_created=row.date_created,
        )


def is_wide_user_type(user: User | AuthUser | BaseUser) -> TypeIs[User | AuthUser]:
    """
    Check if the user is an instance of User, AuthUser, but not a BaseUser.

    Under term "wide" we mean that user has role + company attributes, so it won't fail
    with AttributeError. Note that even if AuthUser is wide, it doesn't mean that role
    information is available, so we still need to check if user has role_id
    and company_id attributes.
    """
    return isinstance(user, User | AuthUser)
