import logging
from collections.abc import Callable
from functools import wraps
from typing import assert_never

import aiohttp_session
from aiohttp import web
from yarl import URL

from api import errors
from app.auth import two_factor
from app.auth.constants import (
    AUTH_METHOD_APP_KEY,
    AUTH_USER_COMPANY_CONFIG,
    AUTH_USER_SUPER_ADMIN_PERMISSIONS,
    NEXT_URL_LOGIN_PREFIX,
    NEXT_URL_PERMISSION_PREFIX,
    NEXT_URL_VERIFY_2FA_PREFIX,
    ORIGIN_HEADER,
    REFERER_HEADER,
    USER_AGENT_HEADER,
    USER_APP_KEY,
)
from app.auth.enums import AuthFactor, AuthMethod
from app.auth.schemas import CompanyConfig
from app.auth.types import AuthUser, BaseUser, User, is_wide_user_type
from app.auth.utils import (
    check_xsrf_token,
    get_request_url,
    get_request_user,
    has_super_admin_access,
    is_admin_user,
    store_next_url,
)
from app.config import is_test_environ
from app.lib.database import DBRow
from app.lib.helpers import get_request_canonical_path, is_domain_trusted
from app.lib.redirects import redirect
from app.lib.types import (
    AnyUserHandler,
    BaseUserHandler,
    Handler,
    HandlerResponse,
    SignSessionHandler,
    UserHandler,
)
from app.services import services

logger = logging.getLogger(__name__)

CheckFunc = Callable[
    [
        AuthUser | DBRow | BaseUser | User | None,
        CompanyConfig,
        set[str] | None,
        set[str] | None,
    ],
    bool,
]


class LoginRequiredError(errors.Error):
    code: errors.Code
    status: int

    # Those not exists in original Error class, but we define them here to
    # be configure proper redirects
    redirect_url: str
    redirect_prefix: str

    async def redirect(self, request: web.Request) -> web.HTTPFound:
        # Define redirect URL and prefix or override this method in subclasses
        assert self.redirect_url is not None
        assert self.redirect_prefix is not None

        await store_next_url(request, prefix=self.redirect_prefix)
        return web.HTTPFound(self.redirect_url or '/auth')


class LoginRequiredNoEmailError(LoginRequiredError):
    code = errors.Code.access_denied
    status = 401

    async def redirect(self, request: web.Request) -> web.HTTPFound:
        return redirect('errors.email_required')


class LoginRequiredNoUserError(LoginRequiredError):
    code = errors.Code.login_required
    status = 403
    redirect_url = '/auth/login'
    redirect_prefix = NEXT_URL_LOGIN_PREFIX


class LoginRequiredInvalidAuthMethodError(LoginRequiredError):
    code = errors.Code.invalid_auth_method
    status = 403
    redirect_url = '/auth/access-denied'
    redirect_prefix = NEXT_URL_PERMISSION_PREFIX


class LoginRequiredXsrfTokenError(LoginRequiredError):
    code = errors.Code.access_denied
    status = 403
    redirect_url = '/auth/access-denied'
    redirect_prefix = NEXT_URL_PERMISSION_PREFIX


class LoginRequiredIsNotAdminError(LoginRequiredError):
    code = errors.Code.access_denied
    status = 403
    redirect_url = '/auth/access-denied'
    redirect_prefix = NEXT_URL_PERMISSION_PREFIX


class LoginRequiredIsNotSuperAdminError(LoginRequiredError):
    code = errors.Code.access_denied
    status = 403
    redirect_url = '/auth/access-denied'
    redirect_prefix = NEXT_URL_PERMISSION_PREFIX


class LoginRequiredIs2FAUnverifiedError(LoginRequiredError):
    code = errors.Code.verify_2fa_required
    status = 401

    def __init__(self, pending_2fa: two_factor.Pending2FASchema) -> None:
        self.pending_2fa = pending_2fa
        super().__init__(
            details={
                'first_factor': self.pending_2fa.first_factor.value,
                'second_factor': self.pending_2fa.second_factor.value,
            }
        )

    async def redirect(self, request: web.Request) -> web.HTTPFound:
        await store_next_url(request, prefix=NEXT_URL_VERIFY_2FA_PREFIX)
        url = URL(str(request.rel_url))
        query_params = dict(url.query) if url.query.get('redirect') else None
        if self.pending_2fa.second_factor == AuthFactor.phone:
            return redirect('auth', tail='/phone-2fa/verify', get=query_params)
        if self.pending_2fa.second_factor == AuthFactor.email:
            return redirect('auth', tail='/email-2fa/verify', get=query_params)

        assert_never(self.pending_2fa.second_factor)


async def _is_xsrf_token_valid(
    request: web.Request,
    login_method: AuthMethod | None,
    auth_user: AuthUser | BaseUser,
) -> bool:
    """
    Validate XSRF token to prevent CSRF attacks
    """

    config = services.config.app

    xsrf_token: str = request.headers.get('X-XSRF-Token', '')
    secret: str = config.fernet_key

    cookie_name = config.cookie_name
    auth_cookie = request.cookies.get(cookie_name)
    is_test = config.test

    # Do not check XSRF token for those requests:
    # - if user is not logged in
    # - if check disabled manually
    # - if it is a test environment
    # - if user is logged in by token or sign sesion
    # - if it is a download request
    # - if we need to redirect to auth
    if (
        not auth_cookie
        or is_test  # TODO: rewrite tests to use XSRF token
        or login_method != AuthMethod.session
        or '/downloads/' in request.path
    ):
        return True

    is_valid = check_xsrf_token(
        secret=secret,
        auth_cookie=auth_cookie,
        token=xsrf_token,
    )

    if not is_valid:
        request_path = get_request_canonical_path(request)
        logger.warning(
            msg='XSRF error',
            extra={
                'user_id': auth_user.id,
                'user_email': auth_user.email,
                'user_company_edrpou': (
                    auth_user.company_edrpou if is_wide_user_type(auth_user) else None
                ),
                'user_role_id': (auth_user.role_id if is_wide_user_type(auth_user) else None),
                'request_path': request_path,
                'request_url': get_request_url(request),
                'request_method': request.method,
                'request_origin': request.headers.get(ORIGIN_HEADER, ''),
                'request_referrer': request.headers.get(REFERER_HEADER, ''),
                'request_user_agent': request.headers.get(USER_AGENT_HEADER, ''),
            },
        )
        return False

    return True


async def check_login_required(
    request: web.Request,
    *,
    allow_2fa_unverified: bool,
    check_admin: bool,
    check_super_admin: bool,
    should_check_xsrf: bool,
    expect_auth_methods: set[AuthMethod],
    required_permissions: set[str] | None = None,
) -> AuthUser | User | BaseUser:
    """
    Check if user is logged in and has proper permissions to access the page.
    """
    user = get_request_user(request)
    company_config: CompanyConfig = request[AUTH_USER_COMPANY_CONFIG]
    super_admin_permissions: set[str] = request[AUTH_USER_SUPER_ADMIN_PERMISSIONS]
    login_method: AuthMethod | None = request[AUTH_METHOD_APP_KEY]

    # User is not logged in
    if not user:
        raise LoginRequiredNoUserError()

    # In some cases we want to allow one more auth method, like session and sign session
    if not is_test_environ() and login_method not in expect_auth_methods:
        raise LoginRequiredInvalidAuthMethodError()

    if should_check_xsrf and not await _is_xsrf_token_valid(
        request=request,
        login_method=login_method,
        auth_user=user,
    ):
        raise LoginRequiredXsrfTokenError()

    # Check whether user is admin
    if check_admin and not is_admin_user(user=user):
        raise LoginRequiredIsNotAdminError()

    # Check whether user is super admin
    if check_super_admin and not has_super_admin_access(
        user=user,
        company_config=company_config,
        required_permissions=required_permissions,
        super_admin_permissions=super_admin_permissions,
    ):
        raise LoginRequiredIsNotSuperAdminError()

    # Allow making requests without 2FA verification for methods that are used
    # to complete 2FA verification
    if not allow_2fa_unverified:
        session = await aiohttp_session.get_session(request)
        pending_2fa = two_factor.get_pending_2fa_web(session)
        if pending_2fa:
            raise LoginRequiredIs2FAUnverifiedError(pending_2fa=pending_2fa)

    # Everything is OK, user logged in, permitted & verified
    return user


def login_required(
    *,
    allow_2fa_unverified: bool = False,
    check_admin: bool = False,
    check_super_admin: bool = False,
    should_check_xsrf: bool = True,
    expect_auth_method: AuthMethod = AuthMethod.session,
    expect_auth_methods: set[AuthMethod] | None = None,
    required_permissions: set[str] | None = None,
) -> Callable[[UserHandler], Handler]:
    """
    Decorator to handle authentication for given view handler.

    By default user must be authorized by session/token and verified by 2FA (if
    enabled) to have access to decorated handler. But it possible also to
    specify checking per
    """
    check_auth_methods: set[AuthMethod] = expect_auth_methods or {expect_auth_method}

    def decorator(handler: UserHandler) -> Handler:
        @wraps(handler)
        async def wrapper(request: web.Request) -> HandlerResponse:
            user = await check_login_required(
                request=request,
                allow_2fa_unverified=allow_2fa_unverified,
                check_admin=check_admin,
                check_super_admin=check_super_admin,
                should_check_xsrf=should_check_xsrf,
                expect_auth_methods=check_auth_methods,
                required_permissions=required_permissions,
            )
            if not isinstance(user, User):
                logger.warning(
                    'Login required handler called with AuthUser, but expected User',
                    extra={
                        'request_path': get_request_canonical_path(request),
                        'user_id': user.id,
                        'user_email': user.email,
                        'user_edrpou': user.company_edrpou if is_wide_user_type(user) else None,
                    },
                )
                raise LoginRequiredInvalidAuthMethodError()

            return await handler(request, user)

        return wrapper

    return decorator


def sign_session_login_required(handler: SignSessionHandler) -> Handler:
    """
    Decorator to handle authentication for both sign session and web sessions.

    For now we don't have sign session handlers that should work only in sign session context,
    so this decorator always assume that user is logged in by web session or user is authenticated
    by sign session ID. Parameters:

    @sign_session_login_required
    async def some_handler(request: web.Request, user: AuthUser | User) -> web.Response:
        pass
    """

    @wraps(handler)
    async def wrapper(request: web.Request) -> HandlerResponse:
        user = await check_login_required(
            request=request,
            allow_2fa_unverified=False,
            check_admin=False,
            check_super_admin=False,
            should_check_xsrf=True,
            expect_auth_methods={AuthMethod.session, AuthMethod.sign_session},
            required_permissions=None,
        )

        if not isinstance(user, AuthUser | User):
            raise LoginRequiredInvalidAuthMethodError()

        return await handler(request, user)

    return wrapper


def sign_session_base_login_required(handler: AnyUserHandler) -> Handler:
    """
    Decorator to handle authentication for sign session handlers and web handlers that might
    work with base user or full user objects.

    @sign_session_base_login_required
    async def some_handler(request: web.Request, user: AuthUser | BaseUser | User) -> web.Response:
        pass
    """

    @wraps(handler)
    async def wrapper(request: web.Request) -> HandlerResponse:
        user = await check_login_required(
            request=request,
            allow_2fa_unverified=False,
            check_admin=False,
            check_super_admin=False,
            should_check_xsrf=True,
            expect_auth_methods={AuthMethod.session, AuthMethod.sign_session},
            required_permissions=None,
        )

        return await handler(request, user)

    return wrapper


def graph_login_required(handler: AnyUserHandler) -> Handler:
    """
    Decorator to handle authentication for GraphQL handlers.

    This decorator is used to ensure that the user is logged in and has the necessary permissions
    to access the GraphQL endpoint.
    """

    @wraps(handler)
    async def wrapper(request: web.Request) -> HandlerResponse:
        user = await check_login_required(
            request=request,
            allow_2fa_unverified=False,
            check_admin=False,
            check_super_admin=False,
            should_check_xsrf=True,
            expect_auth_methods={AuthMethod.session, AuthMethod.sign_session},
            required_permissions=None,
        )

        return await handler(request, user)

    return wrapper


def base_login_required(
    *,
    allow_2fa_unverified: bool = False,
    check_admin: bool = False,
    check_super_admin: bool = False,
    should_check_xsrf: bool = True,
    expect_auth_method: AuthMethod = AuthMethod.session,
    expect_auth_methods: set[AuthMethod] | None = None,
    required_permissions: set[str] | None = None,
) -> Callable[[BaseUserHandler], Handler]:
    check_auth_methods: set[AuthMethod] = expect_auth_methods or {expect_auth_method}

    def decorator(handler: BaseUserHandler) -> Handler:
        @wraps(handler)
        async def wrapper(request: web.Request) -> HandlerResponse:
            user = await check_login_required(
                request=request,
                allow_2fa_unverified=allow_2fa_unverified,
                check_admin=check_admin,
                check_super_admin=check_super_admin,
                should_check_xsrf=should_check_xsrf,
                expect_auth_methods=check_auth_methods,
                required_permissions=required_permissions,
            )

            if isinstance(user, AuthUser):
                logger.warning(
                    'Base login required handler called with AuthUser, but expected BaseUser',
                    extra={
                        'request_path': get_request_canonical_path(request),
                        'user_id': user.id,
                        'user_email': user.email,
                        'user_edrpou': user.company_edrpou if is_wide_user_type(user) else None,
                    },
                )
                raise LoginRequiredInvalidAuthMethodError()

            return await handler(request, user)

        return wrapper

    return decorator


def redirect_to_app(handler: Handler) -> Handler:
    """
    Redirect user to app if user is logged in and completed 2FA verification. Usually used for
    pages which handle login or registration processes, like login page, registration page, etc.
    This allows skip auth page at all if user is already logged in and redirect to app page
    directly.

    Frontend can provide "?redirect=..." query parameter to redirect user to specific URL after
    login, otherwise we redirect to /app page by default.
    """

    @wraps(handler)
    async def wrapper(request: web.Request) -> HandlerResponse:
        session = await aiohttp_session.get_session(request)

        is_pending_2fa = two_factor.is_pending_2fa_web(session)

        if request[USER_APP_KEY] and not is_pending_2fa:
            redirect_url = request.rel_url.query.get('redirect')
            if redirect_url and is_domain_trusted(URL(redirect_url)):
                return web.HTTPFound(redirect_url)

            return redirect('app', get=dict(request.rel_url.query), tail='')

        return await handler(request)

    return wrapper


def redirect_to_auth(handler: BaseUserHandler) -> Handler:
    """
    When user is not logged in, redirect to auth page instead of returning 40x error.

    This decorator can be useful for pages, but not for API handlers
    """

    @wraps(handler)
    async def wrapper(request: web.Request) -> web.StreamResponse:
        try:
            user = await check_login_required(
                request=request,
                allow_2fa_unverified=False,
                check_admin=False,
                check_super_admin=False,
                should_check_xsrf=False,
                expect_auth_methods={AuthMethod.session},
            )
        except LoginRequiredError as err:
            # Convert to error to redirect to avoid noisy 40x errors in logs for expected flow
            return await err.redirect(request)

        if isinstance(user, AuthUser):
            error = LoginRequiredInvalidAuthMethodError()
            return await error.redirect(request)

        return await handler(request, user)

    return wrapper


def debug_handler(handler: Handler) -> Handler:
    @wraps(handler)
    async def wrapper(request: web.Request) -> HandlerResponse:
        if not services.config.app.debug:
            return web.Response(status=404)

        return await handler(request)

    return wrapper


def super_admin_permission_required(
    required_permissions: set[str],
) -> Callable[[UserHandler], Handler]:
    """
    Decorator for web handlers that require super admin permissions to access.

    NOTE: for super admin API handlers use "api_super_admin_permission_required" instead.
    """
    return login_required(
        check_super_admin=True,
        required_permissions=required_permissions,
    )
