from http import HTTPStatus

from conciergelib import headers as concierge_headers

from app.auth.db import select_base_user
from app.auth.phone_auth.utils import PHONE_AUTH_REDIS_KEY_TEMPLATE
from app.auth.tests.utils import PROCESS_PHONE_AUTH_CODE_URL, SEND_PHONE_AUTH_CODE_URL
from app.services import services
from app.tests.common import prepare_app_client, prepare_base_user_data, prepare_referer_headers


async def test_send_phone_auth_code(aiohttp_client, evo_sender_mock):
    """
    Check what we return when phone is valid
    """
    app, client = await prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    response = await client.post(
        path=SEND_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': phone},
    )

    assert response.status == HTTPStatus.OK
    redis_key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    code = await services.redis.get(redis_key)
    assert code is not None
    assert len(code) == 6 and code.isdigit()

    assert evo_sender_mock.message_count == 1
    assert evo_sender_mock.only_sms is True
    assert evo_sender_mock.message == f'{code} - код підтвердження http://localhost:8000'
    assert evo_sender_mock.phone == phone


async def test_send_phone_auth_code_sms_failure(aiohttp_client, evo_sender_mock):
    """
    Check what we return when phone is invalid
    """
    app, client = await prepare_app_client(aiohttp_client)

    response = await client.post(
        path=SEND_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': '+38099123456711'},
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    data = await response.json()

    assert data == {
        'code': 'invalid_request',
        'details': {'phone': 'Please supply valid ukrainian phone number'},
        'reason': 'Виникла помилка, перевірте введені дані',
    }

    redis_key = 'auth_phone_+38099123456711'
    assert await services.redis.get(redis_key) is None
    assert evo_sender_mock.message_count == 0


async def test_send_phone_auth_code_phone_already_used_for_non_auth(
    aiohttp_client, evo_sender_mock
):
    """
    Test that we reject registration when phone is already associated with another user
    for non-auth purposes (e.g., 2FA, contact phone)
    """
    app, client = await prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    await prepare_base_user_data(
        app=app,
        auth_phone=None,
        phone=phone,
        email='<EMAIL>',
        first_name='Existing',
        last_name='User',
        is_placeholder=False,
        email_confirmed=True,
        registration_completed=True,
        is_phone_verified=True,
    )

    # Try to send auth code for registration with the same phone
    # Should return error, because phone is already used for non-auth purposes
    # (e.g., 2FA, contact phone), so we can't use it for authentication
    # This is to prevent accidental creation of duplicate accounts
    response = await client.post(
        path=SEND_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': phone},
    )

    assert response.status == HTTPStatus.BAD_REQUEST
    data = await response.json()

    assert data == {
        'code': 'invalid_request',
        'reason': (
            'На цей номер телефону неможливо зареєструватися, оскільки його вже додано до '
            'системи. Якщо це ваш номер телефону і у вас вже є обліковий запис, увійдіть '
            'у ваш акаунт, використовуючи інший спосіб автентифікації, та увімкніть вхід за '
            'номером телефону в налаштуваннях профілю'
        ),
        'details': {},
    }

    assert evo_sender_mock.message_count == 0

    redis_key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    assert await services.redis.get(redis_key) is None


async def test_send_phone_auth_code_allows_auth_phone_login(aiohttp_client, evo_sender_mock):
    """
    Test that we allow sending auth code when phone is already used for authentication
    (login flow, not registration)
    """
    app, client = await prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    await prepare_base_user_data(
        app=app,
        auth_phone=phone,
        phone=phone,
        email='<EMAIL>',
        first_name='Existing',
        last_name='User',
        is_placeholder=False,
        email_confirmed=True,
        registration_completed=True,
        is_phone_verified=True,
    )

    # Try to send auth code - should succeed (login flow)
    response = await client.post(
        path=SEND_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': phone},
    )

    assert response.status == HTTPStatus.OK

    assert evo_sender_mock.message_count == 1
    assert evo_sender_mock.phone == phone

    redis_key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    code = await services.redis.get(redis_key)
    assert code is not None
    assert len(code) == 6 and code.isdigit()


async def test_process_phone_auth_new_user(aiohttp_client, evo_sender_mock, concierge_emulation):
    """
    Check what we return when user is created
    """
    app, client = await prepare_app_client(aiohttp_client)

    phone = '+380991234567'

    response = await client.post(
        path=SEND_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': phone},
    )
    assert response.status == HTTPStatus.OK
    code = await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone))
    assert code is not None
    assert len(code) == 6 and code.isdigit()
    assert evo_sender_mock.message == f'{code} - код підтвердження http://localhost:8000'
    assert evo_sender_mock.phone == phone
    assert evo_sender_mock.only_sms is True
    assert evo_sender_mock.message_count == 1
    evo_sender_mock.reset()

    concierge_token = concierge_emulation.generate_new_session_token()

    response = await client.post(
        path=PROCESS_PHONE_AUTH_CODE_URL,
        headers={
            **prepare_referer_headers(client),
            concierge_headers.ACCESS_TOKEN: concierge_token,
        },
        json={'phone': phone, 'code': code},
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, auth_phone=phone, only_with_email=False)
        assert user is not None
        assert user.id is not None
        assert user.phone == phone
        assert user.email is None
        assert user.first_name is None
        assert user.last_name is None
        assert user.second_name is None
        assert user.email_confirmed is False
        assert user.is_placeholder is False

    assert await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)) is None
    assert evo_sender_mock.message_count == 0

    assert concierge_emulation.count_user_session(user_id=user.id) == 1
    assert concierge_emulation.get_user_profile(user_id=user.id) == {
        'email': None,
        'first_name': None,
        'last_name': None,
        'is_email_confirmed': False,
        'is_super_admin': False,
        'phone': phone,
        'auth_phone': phone,
    }


async def test_process_phone_auth_existing_user(
    aiohttp_client,
    evo_sender_mock,
    concierge_emulation,
):
    """
    Check what we return when user already exists
    """
    app, client = await prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    user = await prepare_base_user_data(
        app=app,
        auth_phone=phone,
        phone=None,
        email=None,
        first_name='Test',
        last_name='User',
        second_name=None,
        is_placeholder=False,
        email_confirmed=False,
        registration_completed=False,
    )

    response = await client.post(
        path=SEND_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': phone},
    )
    assert response.status == HTTPStatus.OK

    code = await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone))
    assert code is not None
    assert evo_sender_mock.message_count == 1
    evo_sender_mock.reset()

    concierge_token = concierge_emulation.generate_new_session_token()

    response = await client.post(
        path=PROCESS_PHONE_AUTH_CODE_URL,
        headers={
            **prepare_referer_headers(client),
            concierge_headers.ACCESS_TOKEN: concierge_token,
        },
        json={'phone': phone, 'code': code},
    )

    assert response.status == HTTPStatus.OK

    assert await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)) is None
    assert evo_sender_mock.message_count == 0

    assert concierge_emulation.count_user_session(user_id=user.id) == 1
    assert concierge_emulation.get_user_profile(user_id=user.id) == {
        'email': None,
        'first_name': 'Test',
        'last_name': 'User',
        'is_email_confirmed': False,
        'is_super_admin': False,
        'phone': None,
        'auth_phone': phone,
    }


async def test_process_phone_auth_incorrect_code(aiohttp_client, evo_sender_mock):
    """
    Check that we can't use incorrect code
    """
    app, client = await prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    await services.redis.setex(
        PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone),
        services.config.auth.totp_interval,
        '123456',
    )

    response = await client.post(
        path=PROCESS_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': phone, 'code': '654321'},
    )

    assert response.status == HTTPStatus.BAD_REQUEST
    data = await response.json()
    assert data == {
        'code': 'invalid_totp_code',
        'reason': 'Код невірний. Спробуйте ще раз',
        'details': None,
    }

    assert await services.redis.get(PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)) == '123456'

    assert evo_sender_mock.message_count == 0


async def test_process_phone_auth_code_expired(aiohttp_client, evo_sender_mock):
    """
    Check that we can't use not existed / expired code
    """
    _, client = await prepare_app_client(aiohttp_client)

    phone = '+380991234567'

    response = await client.post(
        path=PROCESS_PHONE_AUTH_CODE_URL,
        headers=prepare_referer_headers(client),
        json={'phone': phone, 'code': '123456'},
    )

    assert response.status == HTTPStatus.BAD_REQUEST
    data = await response.json()
    assert data == {
        'code': 'invalid_totp_code',
        'reason': 'Код невірний. Спробуйте ще раз',
        'details': None,
    }

    assert evo_sender_mock.message_count == 0
