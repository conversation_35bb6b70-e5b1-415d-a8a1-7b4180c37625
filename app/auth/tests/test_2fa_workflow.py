import secrets
from http import HTTPStatus
from urllib.parse import urlparse

import pytest
import ujson
import yarl

from app.auth import two_factor
from app.auth.enums import AuthFactor
from app.auth.helpers import generate_hidden_number
from app.auth.tests.utils import (
    request_process_phone_auth_code,
    request_send_phone_auth_code,
)
from app.lib.sender import utils as sender_utils
from app.tests.common import (
    AUTH_HIDDEN_PHONE_2FA_URL,
    AUTH_VERIFY_2FA_URL,
    GRAPHQL_URL,
    LOGIN_URL,
    LOGOUT_URL,
    PROFILE_URL,
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    TEST_USER_PHONE,
    VCHASNO_EDRPOU,
    fetch_graphql,
    login,
    prepare_client,
    prepare_referer_headers,
)

API_RESEND_2FA_URL = '/internal-api/2fa/phone/resend'
API_VERIFY_2FA_URL = '/internal-api/2fa/phone/verify'
API_VERIFY_EMAIL_2FA_URL = '/internal-api/2fa/email/verify'
API_HIDDEN_EMAIL_2FA_URL = '/internal-api/2fa/email/hidden-email'
API_SEND_EMAIL_2FA_TOKEN_URL = '/internal-api/2fa/email/send-token'
API_VERIFY_EMAIL_2FA_TOKEN_URL = '/internal-api/2fa/email/verify-token'

APP_URL = '/app/profile/settings?utm_source=zk_account&utm_campaign=about_vchasno&utm_medium=banner'
AUTH_LOGIN_URL = '/auth/login'

GQL_CURRENT_USER_EMAIL = '{ currentUser { email } }'


async def enable_2fa(conn, user, second_factor):
    return True


async def test_2fa_internal_user_workflow(monkeypatch, evo_sender_mock, aiohttp_client):
    otp_code = '123456'

    monkeypatch.setattr(two_factor, 'has_2fa_enabled', enable_2fa)
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    # 2FA enabled due to internal user rule
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        password=TEST_USER_PASSWORD,
        phone=TEST_USER_PHONE,
    )
    headers = prepare_referer_headers(client)

    # Open static page in browser - redirect to login page
    response = await client.get(APP_URL, allow_redirects=False)
    assert response.status == 302
    assert response.headers['Location'] == AUTH_LOGIN_URL

    # Open login page, do not redirect anywhere
    response = await client.get(AUTH_LOGIN_URL, allow_redirects=False)
    assert response.status == 200
    assert str(response.url.relative()) == AUTH_LOGIN_URL

    # Login user with email/password
    response = await login(client, user.email, TEST_USER_PASSWORD)
    data = await response.json()
    assert data['next_url'] == APP_URL
    assert data['is_2fa_enabled']

    # Verify that SMS with code was sent.
    assert otp_code in evo_sender_mock.message
    assert user.phone == evo_sender_mock.phone

    # Open static page in browser - redirect to verify 2fa page
    response = await client.get(APP_URL, allow_redirects=False)
    assert response.status == 302
    assert response.headers['Location'] == AUTH_VERIFY_2FA_URL

    # Open verify 2fa page, do not redirect anywhere
    response = await client.get(AUTH_VERIFY_2FA_URL, allow_redirects=False)
    assert response.status == 200
    assert str(response.url.relative()) == AUTH_VERIFY_2FA_URL

    # Open internal API page in browser - get 401 Unathorized status
    response = await client.post(
        GRAPHQL_URL,
        data=ujson.dumps({'query': GQL_CURRENT_USER_EMAIL}),
        headers=headers,
    )
    assert response.status == 401

    # Not only when requesting GraphQL, but when requesting other API
    # endpoint as well
    response = await client.post(
        PROFILE_URL, data=ujson.dumps({'phone': TEST_USER_PHONE}), headers=headers
    )
    assert response.status == 401

    # Resend code if old code didn't reach the user
    response = await client.post(API_RESEND_2FA_URL, headers=headers)
    assert response.status == 200

    # Verify user by TOTP code
    response = await client.post(
        API_VERIFY_2FA_URL,
        data=ujson.dumps({'code': otp_code, 'trusted': False}),
        headers=headers,
    )
    assert response.status == 200
    data = await response.json()
    assert data['next_url'] == APP_URL

    # Request app URL, do not redirect anywhere
    response = await client.get(APP_URL, allow_redirects=False)
    assert response.status == 200
    assert str(response.url.relative()) == APP_URL

    # Make requests to GraphQL & internal API - everything is OK
    data = await fetch_graphql(client, GQL_CURRENT_USER_EMAIL)
    assert data['currentUser']['email'] == user.email

    response = await client.post(
        PROFILE_URL, data=ujson.dumps({'phone': TEST_USER_PHONE}), headers=headers
    )
    assert response.status == 200


async def test_2fa_throttling(monkeypatch, evo_sender_mock, aiohttp_client):
    monkeypatch.setattr(two_factor, 'has_2fa_enabled', enable_2fa)
    monkeypatch.setattr(secrets, 'randbelow', lambda _: '123456')
    monkeypatch.setattr(sender_utils, 'DEFAULT_SMS_LIMIT', 2)

    # 2FA enabled due to internal user rule
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        password=TEST_USER_PASSWORD,
        phone=TEST_USER_PHONE,
    )

    headers = prepare_referer_headers(client)

    response = await login(client, user.email, TEST_USER_PASSWORD)
    assert response.status == HTTPStatus.OK

    response = await client.post(API_RESEND_2FA_URL, headers=headers)
    assert response.status == HTTPStatus.OK

    response = await client.post(API_RESEND_2FA_URL, headers=headers)
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS
    data = await response.json()
    assert data['details']['lock_time'] <= 60

    response = await client.post(API_RESEND_2FA_URL, headers=headers)
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS
    assert (await response.json())['details']['lock_time'] <= 3600

    # assert login request return http_429(TOO_MANY_REQUESTS)
    response = await client.post(
        LOGIN_URL,
        json={'email': user.email, 'password': TEST_USER_PASSWORD},
        headers=prepare_referer_headers(client),
    )
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS
    assert (await response.json())['details']['lock_time'] <= 3600


async def test_login_2fa_throttling(monkeypatch, evo_sender_mock, aiohttp_client):
    monkeypatch.setattr(two_factor, 'has_2fa_enabled', enable_2fa)
    monkeypatch.setattr(secrets, 'randbelow', lambda _: '123456')
    monkeypatch.setattr(sender_utils, 'DEFAULT_SMS_LIMIT', 1)

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        password=TEST_USER_PASSWORD,
        phone=TEST_USER_PHONE,
    )
    headers = prepare_referer_headers(client)

    response = await client.post(
        LOGIN_URL,
        json={'email': user.email, 'password': TEST_USER_PASSWORD},
        headers=headers,
    )
    assert response.status == HTTPStatus.OK

    response = await client.post(
        LOGIN_URL,
        json={'email': user.email, 'password': TEST_USER_PASSWORD},
        headers=headers,
    )
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS
    assert (await response.json())['details']['lock_time'] <= 3600


@pytest.mark.parametrize('trusted', [True, False])
async def test_2fa_trust_device(monkeypatch, evo_sender_mock, aiohttp_client, trusted):
    otp_code = '123456'

    monkeypatch.setattr(two_factor, 'has_2fa_enabled', enable_2fa)
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        password=TEST_USER_PASSWORD,
        phone=TEST_USER_PHONE,
    )
    headers = prepare_referer_headers(client)

    # Login user with email/password
    await login(client, user.email, TEST_USER_PASSWORD)

    # Open static page in browser - redirect to verify 2fa page
    response = await client.get(APP_URL, allow_redirects=False)
    assert response.status == 302
    assert response.headers['Location'] == AUTH_VERIFY_2FA_URL

    # Resend code if old code didn't reach the user
    response = await client.post(API_RESEND_2FA_URL, headers=headers)
    assert response.status == 200

    response = await client.post(
        API_VERIFY_2FA_URL,
        data=ujson.dumps({'code': otp_code, 'trusted': trusted}),
        headers=headers,
    )
    assert response.status == 200
    data = await response.json()
    assert data['next_url'] == APP_URL

    # Logout.
    response = await client.post(LOGOUT_URL, headers=headers)
    assert response.status == 200

    # Login again.
    response = await login(client, user.email, TEST_USER_PASSWORD, remember=True)
    data = await response.json()
    assert response.status == 200
    assert urlparse(data['next_url']).path == '/app'
    assert data['is_2fa_enabled'] != trusted

    # Open app to validate whether authenticated or not.
    response = await client.get(APP_URL, allow_redirects=False)

    if trusted:
        # If we trusted the device, second login attempt
        # shouldn't require second factor.
        assert response.status == 200
    else:
        # Otherwise, client is redirected to 2FA page.
        assert response.status == 302
        assert response.headers['Location'] == AUTH_VERIFY_2FA_URL


async def test_2fa_hidden_phone(monkeypatch, evo_sender_mock, aiohttp_client):
    otp_code = '123456'

    monkeypatch.setattr(two_factor, 'has_2fa_enabled', enable_2fa)
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    # 2FA enabled due to internal user rule
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        password=TEST_USER_PASSWORD,
        phone=TEST_USER_PHONE,
    )
    headers = prepare_referer_headers(client)

    await login(client, user.email, TEST_USER_PASSWORD)

    # Open static page in browser - redirect to login page
    response = await client.get(APP_URL, allow_redirects=False)
    assert response.status == 302
    assert response.headers['Location'] == AUTH_VERIFY_2FA_URL

    response = await client.get(AUTH_HIDDEN_PHONE_2FA_URL, headers=headers)
    assert response.status == 200

    data = await response.json()
    assert data['phone'] == generate_hidden_number(TEST_USER_PHONE)


async def test_2fa_email_verification_workflow(monkeypatch, aiohttp_client, evo_sender_mock):
    """Test email 2FA verification workflow when phone is used as first factor"""

    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_USER_EMAIL,
        password=TEST_USER_PASSWORD,
        company_edrpou=VCHASNO_EDRPOU,
        auth_phone=TEST_USER_PHONE,
        is_2fa_enabled=True,
    )
    headers = prepare_referer_headers(client)

    otp_code = await request_send_phone_auth_code(client, auth_phone=user.auth_phone)
    response_data = await request_process_phone_auth_code(
        client=client,
        auth_phone=user.auth_phone,
        code=otp_code,
    )
    assert response_data == {
        'email': TEST_USER_EMAIL,
        'flow': 'login',
        'is_2fa_enabled': True,
        'next_url': 'http://localhost:8000/app',
    }

    # there is access to get hidden email for 2FA
    response = await client.get(API_HIDDEN_EMAIL_2FA_URL, headers=headers)
    assert response.status == 200
    data = await response.json()
    assert data['email'] == 't****r@v****.ua'

    # user can't use phone as second factor, if first factor is phone
    response = await client.post(
        API_VERIFY_2FA_URL,
        json={'code': otp_code, 'trusted': False},
        headers=headers,
    )
    assert response.status == 400
    response_data = await response.json()
    assert response_data['reason'] == 'Очікується інший тип двофакторної аутентифікації'
    assert response_data['details']['current'] == AuthFactor.phone.value
    assert response_data['details']['pending'] == AuthFactor.email.value

    response = await client.post(API_RESEND_2FA_URL, headers=headers)
    assert response.status == 400
    response_data = await response.json()
    assert response_data['reason'] == 'Очікується інший тип двофакторної аутентифікації'
    assert response_data['details']['current'] == AuthFactor.phone.value
    assert response_data['details']['pending'] == AuthFactor.email.value

    # To complete second factor verification, user need to enter correct password

    # If password is not correct, it should return 401 Unauthorized
    response = await client.post(
        path=API_VERIFY_EMAIL_2FA_URL,
        json={'password': 'wrong_password_1!', 'trusted': False},
        headers=headers,
    )
    assert response.status == 400
    response_data = await response.json()
    assert response_data['reason'] == 'Помилка в логіні або паролі'

    # But if password is correct, it should return 200 OK and remove pending 2FA state
    response = await client.post(
        path=API_VERIFY_EMAIL_2FA_URL,
        json={'password': TEST_USER_PASSWORD, 'trusted': False},
        headers=headers,
    )
    assert response.status == 200
    data = await response.json()
    assert data['next_url'] == 'http://localhost:8000/app'


async def test_2fa_email_verification_workflow_no_password(
    aiohttp_client,
    monkeypatch,
    evo_sender_mock,
    mailbox,
    send_email_mock,
):
    """
    Test email 2FA verification workflow when phone is used as first factor
    and user doesn't have password
    """

    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_USER_EMAIL,
        password=None,
        company_edrpou=VCHASNO_EDRPOU,
        auth_phone=TEST_USER_PHONE,
        is_2fa_enabled=True,
    )
    headers = prepare_referer_headers(client)

    otp_code = await request_send_phone_auth_code(client, auth_phone=user.auth_phone)
    response_data = await request_process_phone_auth_code(
        client=client,
        auth_phone=user.auth_phone,
        code=otp_code,
    )
    assert response_data == {
        'email': TEST_USER_EMAIL,
        'flow': 'login',
        'is_2fa_enabled': True,
        'next_url': 'http://localhost:8000/app',
    }

    # there is access to get hidden email for 2FA
    response = await client.get(API_HIDDEN_EMAIL_2FA_URL, headers=headers)
    assert response.status == 200
    data = await response.json()
    assert data['email'] == 't****r@v****.ua'

    # user can't use phone as second factor, if first factor is phone
    response = await client.post(
        API_VERIFY_2FA_URL,
        json={'code': otp_code, 'trusted': False},
        headers=headers,
    )
    assert response.status == 400
    response_data = await response.json()
    assert response_data['reason'] == 'Очікується інший тип двофакторної аутентифікації'
    assert response_data['details']['current'] == AuthFactor.phone.value
    assert response_data['details']['pending'] == AuthFactor.email.value

    response = await client.post(API_RESEND_2FA_URL, headers=headers)
    assert response.status == 400
    response_data = await response.json()
    assert response_data['reason'] == 'Очікується інший тип двофакторної аутентифікації'
    assert response_data['details']['current'] == AuthFactor.phone.value
    assert response_data['details']['pending'] == AuthFactor.email.value

    # If password is not correct, it should return 401 Unauthorized
    response = await client.post(
        path=API_VERIFY_EMAIL_2FA_URL,
        json={'password': 'some_fake_password!', 'trusted': False},
        headers=headers,
    )
    assert response.status == 400
    response_data = await response.json()
    assert response_data['reason'] == 'Помилка в логіні або паролі'

    # But instead user can send link to email with token to confirm email
    response = await client.post(
        path=API_SEND_EMAIL_2FA_TOKEN_URL,
        headers=headers,
    )
    assert response.status == 200

    send_email_mock.assert_called_once()
    email_kwargs = send_email_mock.mock_calls[0].kwargs
    assert email_kwargs['recipient_mixed'] == TEST_USER_EMAIL
    assert email_kwargs['template_name'] == 'email_2fa_token'
    assert 'url' in email_kwargs['context']
    url_with_token = yarl.URL(email_kwargs['context']['url'])
    assert url_with_token.path == '/auth/email-2fa/verify-token'
    token = url_with_token.query.get('token')
    assert token

    response = await client.post(
        path=API_VERIFY_EMAIL_2FA_TOKEN_URL,
        json={'token': token},
        headers=headers,
    )
    assert response.status == 200
    data = await response.json()
    assert data['next_url'] == 'http://localhost:8000/app'

    # check that user is fully authenticated
    data = await fetch_graphql(client, query=GQL_CURRENT_USER_EMAIL)
    assert data['currentUser']['email'] == TEST_USER_EMAIL

    # second attempt to verify with the same token should fails
    response = await client.post(
        path=API_VERIFY_EMAIL_2FA_TOKEN_URL,
        json={'token': token},
        headers=headers,
    )
    assert response.status == 400
    data = await response.json()
    assert data['reason'] == 'Недійсний токен підтвердження електронної пошти'
