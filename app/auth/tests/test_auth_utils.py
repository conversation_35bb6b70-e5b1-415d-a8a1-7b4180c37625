from unittest.mock import patch

import pytest

from app.auth.helpers import (
    autogenerate_password,
    check_password_hash_bcrypt,
    check_password_hash_pbkdf2_hmac_sha512,
    generate_hidden_number,
    generate_password_hash,
    generate_password_hash_bcrypt,
)
from app.auth.schemas import (
    DefaultRolePermissionsBase,
    DefaultRolePermissionsCoworker,
    DefaultRolePermissionsKey,
)
from app.auth.tests.utils import prepare_dummy_base_user
from app.auth.types import AuthUser, BaseUser, InsertRoleDict, User
from app.auth.utils import (
    allow_unathenticated_ua,
    create_user,
    get_short_company_name,
    get_user_full_name,
    has_permission,
    is_admin,
    match_ip,
    set_default_role_settings,
    try_to_syncronously_sync_user_roles,
)
from app.events.user_actions.types import Source
from app.lib.enums import UserRole
from app.services import services
from app.tests.common import (
    cleanup_on_teardown,
    create_dataclass_mock,
    make_mocked_request,
    prepare_app_client,
    prepare_client,
    prepare_user_data,
)

TEST_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko'


BASE_CREATE_USER_DATA = {
    'email': '<EMAIL>',
    'is_email_confirmed': True,
    'is_registration_completed': True,
    'password': 'createduser_password',
    'is_autogenerated_password': False,
    'created_by': None,
    'first_name': 'Created',
    'second_name': 'User',
    'last_name': 'Vchasno',
    'promo_code': None,
    'trial_auto_enabled': True,
    'pending_referrer_role_id': None,
    'auth_phone': None,
    'phone': None,
    'is_phone_verified': True,
    'source': Source.internal,
    'google_id': None,
    'microsoft_id': None,
    'apple_id': None,
    'registration_method': None,
    'is_placeholder': False,
}


@pytest.mark.parametrize(
    'user_agent, family, expected',
    [(TEST_USER_AGENT, 'IE', True), (TEST_USER_AGENT, 'Chrome', False)],
)
def test_allow_unauthorized_ua(user_agent, family, expected):
    request = make_mocked_request('GET', '/', headers={'User-Agent': user_agent})
    assert allow_unathenticated_ua(request, family=family) is expected


def test_autogenerate_password():
    test_password = autogenerate_password()
    assert len(test_password) == 8

    other_passwords = [autogenerate_password() for _ in range(1000)]
    assert test_password not in other_passwords
    assert len(other_passwords) == len(set(other_passwords))


@pytest.mark.parametrize(
    'password',
    [
        '12345678',
        'secret-password',
        'very-long-secret-password-with-digits-1213-and-dashes',
    ],
)
def test_check_generated_password_hash(password):
    password_hash = generate_password_hash_bcrypt(password)
    assert check_password_hash_bcrypt(password_hash, password) is True


@pytest.mark.parametrize(
    'user_role, expected', [(UserRole.user.value, False), (UserRole.admin.value, True)]
)
def test_is_admin(user_role, expected):
    assert is_admin(user_role) is expected


@pytest.mark.parametrize(
    'current_ip, rule',
    [
        ('127.0.0.1', '127.0.0.1'),
        ('***********', '192.168.1.*'),
        ('************', '212*'),
        ('************', '212.23.*.*'),
        ('************', '212.*.31.*'),
        ('************', '212.*.*.22'),
        ('***********', '***********/24'),
        ('*************', '***********/24'),
    ],
)
def test_match_ip(current_ip, rule):
    assert match_ip(current_ip, rule) is True


@pytest.mark.parametrize(
    'current_ip, rule',
    [
        ('127.0.0.1', '*********'),
        ('********', '127*'),
        ('***********', '192.168.0.*'),
        ('********', '***********/16'),
        ('***********', '***********/24'),
        ('***********', '***********/32'),
        ('***********', '***********/31'),
    ],
)
def test_match_ip_not_matched(current_ip, rule):
    assert match_ip(current_ip, rule) is False


@pytest.mark.parametrize(
    'names, expected',
    [
        (
            ('first_name', 'second_name', 'last_name'),
            'first_name second_name last_name',
        ),
        ((None, 'second_name', 'last_name'), 'second_name last_name'),
        (('first_name', None, 'last_name'), 'first_name last_name'),
        (('first_name', 'second_name', ''), 'first_name second_name'),
        (('first_name', None, ''), 'first_name'),
        ((None, None, None), ''),
    ],
)
async def test_get_user_full_name(aiohttp_client, names, expected):
    app, client = await prepare_app_client(aiohttp_client)

    user = await prepare_user_data(
        app, first_name=names[0], second_name=names[1], last_name=names[2]
    )

    try:
        assert get_user_full_name(user) == expected
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'name, expected',
    [
        ('Приватне підприємство Вчасно', 'ПП Вчасно'),
        ('Приватне Підприємство Вчасно', 'ПП Вчасно'),
        ('ПРИВАТНЕ ПІДПРИЄМСТВО Вчасно', 'ПП Вчасно'),
        ('ТОВ Вчасно', 'ТОВ Вчасно'),
        ('товариство з обмеженою відповідальністю Вчасно', 'ТОВ Вчасно'),
        ('повне товариство Вчасно повне товариство', 'ПТ Вчасно повне товариство'),
        ('пРИВАТНЕ ПІДПрИЄМСТВО Вчасно', 'ПП Вчасно'),
        ('акціонерне товариство Вчасно', 'АТ Вчасно'),
        ('фізична особа – підприємець Вчасно', 'ФОП Вчасно'),
        ('фізична особа - підприємець Вчасно', 'ФОП Вчасно'),
        ('фізична особа–підприємець Вчасно', 'ФОП Вчасно'),
        ('фізична особа-підприємець Вчасно', 'ФОП Вчасно'),
        ('Іноземне підприємство Вчасно', 'ІП Вчасно'),
        ('Міжнородне товариство Вчасно', 'Міжнородне товариство Вчасно'),
        ('фізична особа Вчасно', 'фізична особа Вчасно'),
        ('ФОП', 'ФОП'),
        ('фізична особа Вчасно', 'фізична особа Вчасно'),
        ('Вчасно Приватне підприємство', 'Вчасно Приватне підприємство'),
        ('державне підприємство Вчасно', 'ДП Вчасно'),
        ('   державне підприємство Вчасно', 'ДП Вчасно'),
    ],
)
async def test_get_short_company_name(name, expected):
    assert get_short_company_name(name) == expected


@pytest.mark.parametrize(
    'user, password, expected',
    [
        ({'password': 'doe123'}, 'doe123', True),
        ({'password': 'pass'}, 'not pass', False),
        ({'password': 'pass'}, None, False),
        ({'password': None}, 'pass', False),
    ],
)
async def test_user_is_valid_password(user, password, expected):
    # hash password if it's exists
    if p := user['password']:
        user['password'] = generate_password_hash(p)

    base_user = prepare_dummy_base_user(**user)
    assert base_user.is_valid_password(password=password) == expected


@pytest.mark.parametrize(
    'phone, expected_hidden_phone',
    [
        ('+380996549376', '+38********76'),  # UA
        ('+48224202020', '+48*******20'),  # PL
        ('+447326434785', '+44********85'),  # UK
    ],
)
def test_generate_hidden_number(phone, expected_hidden_phone):
    assert generate_hidden_number(phone) == expected_hidden_phone


@pytest.mark.parametrize(
    'permissions, expected',
    [
        pytest.param(
            DefaultRolePermissionsCoworker(),
            {
                'can_comment_document': True,
                'can_upload_document': True,
                'can_download_document': True,
                'can_print_document': True,
                'can_delete_document': True,
                'can_sign_and_reject_document': True,
                'can_sign_and_reject_document_internal': True,
                'can_sign_and_reject_document_external': True,
                'can_invite_coworkers': True,
                'can_edit_company': False,
                'can_edit_roles': False,
                'can_create_tags': True,
                'can_edit_document_automation': True,
                'can_edit_document_fields': True,
                'can_edit_document_category': True,
                'can_extract_document_structured_data': False,
                'can_edit_document_structured_data': False,
                'can_archive_documents': True,
                'can_edit_templates': False,
                'can_edit_directories': False,
                'can_remove_itself_from_approval': False,
                'can_delete_archived_documents': True,
                'can_change_document_signers_and_reviewers': False,
                'can_delete_document_extended': False,
                'can_download_actions': False,
                'can_edit_company_contact': False,
                'can_edit_required_fields': False,
                'can_edit_security': False,
                'can_view_private_document': False,
                'can_receive_inbox': True,
                'can_receive_inbox_as_default': False,
                'can_receive_comments': True,
                'can_receive_rejects': True,
                'can_receive_reminders': True,
                'can_receive_reviews': True,
                'can_receive_review_process_finished': False,
                'can_receive_review_process_finished_assigner': True,
                'can_receive_sign_process_finished': False,
                'can_receive_sign_process_finished_assigner': True,
                'can_receive_access_to_doc': True,
                'can_receive_delete_requests': True,
                'can_view_document': False,
                'can_view_coworkers': True,
                'user_role': UserRole.user,
                'can_receive_admin_role_deletion': False,
                'can_receive_email_change': False,
                'can_receive_new_roles': False,
                'can_receive_token_expiration': False,
            },
            id='default',
        ),
        pytest.param(
            DefaultRolePermissionsCoworker(
                can_comment_document=False,
                can_edit_company=True,
                can_edit_roles=True,
                can_view_document=True,
                can_extract_document_structured_data=True,
                user_role=UserRole.admin,
            ),
            {
                'can_comment_document': False,
                'can_upload_document': True,
                'can_download_document': True,
                'can_print_document': True,
                'can_delete_document': True,
                'can_sign_and_reject_document': True,
                'can_sign_and_reject_document_internal': True,
                'can_sign_and_reject_document_external': True,
                'can_invite_coworkers': True,
                'can_edit_company': True,
                'can_edit_roles': True,
                'can_create_tags': True,
                'can_edit_document_automation': True,
                'can_edit_document_fields': True,
                'can_edit_document_category': True,
                'can_extract_document_structured_data': True,
                'can_edit_document_structured_data': False,
                'can_archive_documents': True,
                'can_edit_templates': False,
                'can_edit_directories': False,
                'can_remove_itself_from_approval': False,
                'can_delete_archived_documents': True,
                'can_change_document_signers_and_reviewers': False,
                'can_delete_document_extended': False,
                'can_download_actions': False,
                'can_edit_company_contact': False,
                'can_edit_required_fields': False,
                'can_edit_security': False,
                'can_view_private_document': False,
                'can_receive_inbox': True,
                'can_receive_inbox_as_default': True,
                'can_receive_comments': True,
                'can_receive_rejects': True,
                'can_receive_reminders': True,
                'can_receive_reviews': True,
                'can_receive_review_process_finished': False,
                'can_receive_review_process_finished_assigner': True,
                'can_receive_sign_process_finished': False,
                'can_receive_sign_process_finished_assigner': True,
                'can_receive_access_to_doc': True,
                'can_receive_delete_requests': True,
                'can_view_document': True,
                'can_view_coworkers': True,
                'user_role': UserRole.admin,
                'can_receive_admin_role_deletion': True,
                'can_receive_email_change': True,
                'can_receive_new_roles': True,
                'can_receive_token_expiration': True,
            },
            id='custom',
        ),
        pytest.param(
            DefaultRolePermissionsKey(),
            {
                'can_comment_document': True,
                'can_upload_document': True,
                'can_download_document': True,
                'can_print_document': True,
                'can_delete_document': True,
                'can_sign_and_reject_document': True,
                'can_sign_and_reject_document_internal': True,
                'can_sign_and_reject_document_external': True,
                'can_invite_coworkers': True,
                'can_edit_company': False,
                'can_edit_roles': False,
                'can_create_tags': True,
                'can_edit_document_automation': True,
                'can_edit_document_fields': True,
                'can_edit_document_category': True,
                'can_extract_document_structured_data': False,
                'can_edit_document_structured_data': False,
                'can_archive_documents': True,
                'can_edit_templates': False,
                'can_edit_directories': False,
                'can_remove_itself_from_approval': False,
                'can_delete_archived_documents': True,
                'can_change_document_signers_and_reviewers': False,
                'can_delete_document_extended': False,
                'can_download_actions': False,
                'can_edit_company_contact': False,
                'can_edit_required_fields': False,
                'can_edit_security': False,
                'can_view_private_document': False,
                'can_receive_inbox': True,
                'can_receive_comments': True,
                'can_receive_rejects': True,
                'can_receive_reminders': True,
                'can_receive_reviews': True,
                'can_receive_review_process_finished': False,
                'can_receive_review_process_finished_assigner': True,
                'can_receive_sign_process_finished': False,
                'can_receive_sign_process_finished_assigner': True,
                'can_receive_access_to_doc': True,
                'can_receive_delete_requests': True,
                'can_view_document': True,
                'can_view_coworkers': True,
                'user_role': UserRole.admin,
                'can_receive_inbox_as_default': True,
                'can_receive_admin_role_deletion': True,
                'can_receive_email_change': True,
                'can_receive_new_roles': True,
                'can_receive_token_expiration': True,
            },
            id='default_key',
        ),
        pytest.param(
            DefaultRolePermissionsKey(
                can_comment_document=False,
                can_edit_company=True,
                can_edit_roles=True,
                can_view_document=False,
                can_extract_document_structured_data=True,
                user_role=UserRole.user,
            ),
            {
                'can_comment_document': False,
                'can_upload_document': True,
                'can_download_document': True,
                'can_print_document': True,
                'can_delete_document': True,
                'can_sign_and_reject_document': True,
                'can_sign_and_reject_document_internal': True,
                'can_sign_and_reject_document_external': True,
                'can_invite_coworkers': True,
                'can_edit_company': True,
                'can_edit_roles': True,
                'can_create_tags': True,
                'can_edit_document_automation': True,
                'can_edit_document_fields': True,
                'can_edit_document_category': True,
                'can_extract_document_structured_data': True,
                'can_edit_document_structured_data': False,
                'can_archive_documents': True,
                'can_edit_templates': False,
                'can_edit_directories': False,
                'can_remove_itself_from_approval': False,
                'can_delete_archived_documents': True,
                'can_change_document_signers_and_reviewers': False,
                'can_delete_document_extended': False,
                'can_download_actions': False,
                'can_edit_company_contact': False,
                'can_edit_required_fields': False,
                'can_edit_security': False,
                'can_view_private_document': False,
                'can_receive_inbox': True,
                'can_receive_comments': True,
                'can_receive_rejects': True,
                'can_receive_reminders': True,
                'can_receive_reviews': True,
                'can_receive_review_process_finished': False,
                'can_receive_review_process_finished_assigner': True,
                'can_receive_sign_process_finished': False,
                'can_receive_sign_process_finished_assigner': True,
                'can_receive_access_to_doc': True,
                'can_receive_delete_requests': True,
                'can_view_document': False,
                'can_view_coworkers': True,
                'user_role': UserRole.user,
                'can_receive_inbox_as_default': False,
                'can_receive_admin_role_deletion': False,
                'can_receive_email_change': False,
                'can_receive_new_roles': False,
                'can_receive_token_expiration': False,
            },
            id='custom_key',
        ),
    ],
)
async def test_set_default_role_permissions_all_keys(
    permissions: DefaultRolePermissionsBase,
    expected: dict,
):
    """
    Check that all keys from DefaultRolePermissions are set to the insert_data in
    set_default_role_permissions function.
    """

    insert_data: InsertRoleDict = {
        'company_id': '1',
        'user_id': '2',
    }
    set_default_role_settings(
        default=permissions,
        data=insert_data,
        invited_by=None,
    )
    # Check that previous keys are not removed
    assert insert_data.pop('company_id', None) == '1'
    assert insert_data.pop('user_id', None) == '2'

    assert insert_data == expected


async def test_try_to_syncronously_sync_user_roles(aiohttp_client):
    """
    Given a call to try_to_syncronously_sync_user_roles
    If function doesn't meet the timeout period to execute
    Expected function to be sent over to async processing
    """

    # Arrange
    app, client, user = await prepare_client(aiohttp_client)

    # Mock timeout context manager
    class FakeTimeout:
        def __init__(self, *_args, **_kwargs):
            pass

        async def __aenter__(self):
            raise TimeoutError

        async def __aexit__(self, *args):
            pass

    # Act
    with patch('asyncio.timeout', FakeTimeout):
        await try_to_syncronously_sync_user_roles(
            request_source=Source.internal,
            user=BaseUser.from_row(user),
        )

    # Assert
    assert len(services.kafka.messages) == 1
    assert services.kafka.messages[0][0] == 'vchasno-test-sync-user-roles'


@pytest.mark.parametrize(
    'hash, password, expected_result',
    [
        (
            'AQAAAAIAAYagAAAAEDca48i0K2098lffgpFwp9QNPv8lQ+0ZKdxLiyjSLeemG8OTdGyos5iQBdoKLscZoQ==',
            '3719asdf!',
            True,
        ),
        (
            'AQAAAAIAAYagAAAAEHTY8imO0MvXHbZikoyDw6yfORr1DKOeABVZSk8oGqljL0eyRymYfNpOaCiecKqbOg==',
            '^opOAcjz10',
            True,
        ),
        (
            'AQAAAAIAAYagAAAAED94TS8CspWwNE/AXFX/Baw6m/wMvuBkGy0YewvsH9eOaeNW7gn5w5daKMakUzgo5g==',
            'simple_password&8',
            True,
        ),
        (
            'AQAAAAIAAYagAAAAED94TS8CspWwNE/AXFX/Baw6m/wMvuBkGy0YewvsH9eOaeNW7gn5w5daKMakUzgo5g==',
            'some_fake_password!123',
            False,
        ),
    ],
)
def test_check_password_hash_pbkdf2_hmac_sha512(hash, password, expected_result):
    assert check_password_hash_pbkdf2_hmac_sha512(hash, password) is expected_result


@pytest.mark.parametrize(
    'user_data, create_params',
    [
        pytest.param(
            None,
            BASE_CREATE_USER_DATA,
            id='create_new_user',
        ),
        pytest.param(
            {
                'email': '<EMAIL>',
                'is_placeholder': True,
                'email_confirmed': False,
                'registration_completed': False,
                'first_name': 'Old',
                'second_name': 'Name',
                'last_name': 'User',
                'telegram_chat_id': None,
            },
            BASE_CREATE_USER_DATA,
            id='user_exists_but_placeholder',
        ),
        pytest.param(
            {
                'email': '<EMAIL>',
                'is_placeholder': True,
                'email_confirmed': True,
                'registration_completed': False,
                'is_autogenerated_password': True,
                'first_name': None,
                'second_name': None,
                'last_name': None,
                'telegram_chat_id': None,
            },
            {
                **BASE_CREATE_USER_DATA,
                'first_name': 'Updated',
                'second_name': 'Updated',
                'last_name': 'Updated',
            },
            id='user_exists_placeholder_with_autogenerated_password',
        ),
        pytest.param(
            {
                'email': '<EMAIL>',
                'is_placeholder': True,
                'email_confirmed': False,
                'registration_completed': False,
                'created_by': None,
                'phone': '+380111111111',
                'is_phone_verified': False,
                'telegram_chat_id': None,
            },
            {
                **BASE_CREATE_USER_DATA,
                'phone': '+380222222222',
                'is_phone_verified': True,
                'created_by': None,
            },
            id='user_exists_placeholder_update_phone',
        ),
        pytest.param(
            {
                'email': '<EMAIL>',
                'is_placeholder': True,
                'email_confirmed': False,
                'registration_completed': False,
                'google_id': 'old-google-id',
                'microsoft_id': None,
                'apple_id': None,
                'telegram_chat_id': None,
            },
            {
                **BASE_CREATE_USER_DATA,
                'google_id': 'new-google-id',
                'microsoft_id': 'microsoft-id',
                'apple_id': 'apple-id',
            },
            id='user_exists_placeholder_update_oauth_ids',
        ),
    ],
)
async def test_create_user(aiohttp_client, user_data, create_params):
    """
    Test that user creation works correctly.
    """
    app, client, user = await prepare_client(aiohttp_client)

    if user_data:
        await prepare_user_data(app, **user_data)

    async with services.db.acquire() as conn:
        user = await create_user(conn, **create_params)

    assert user.is_placeholder is False  # always should be False after creation
    assert user.email == create_params['email']
    assert user.first_name == create_params['first_name']
    assert user.second_name == create_params['second_name']
    assert user.last_name == create_params['last_name']
    assert user.email_confirmed == create_params['is_email_confirmed']
    assert user.registration_completed == create_params['is_registration_completed']
    assert user.is_autogenerated_password == create_params['is_autogenerated_password']
    assert user.created_by == create_params['created_by']
    assert user.trial_auto_enabled == create_params['trial_auto_enabled']
    assert user.pending_referrer_role_id == create_params['pending_referrer_role_id']
    assert user.phone == create_params['phone']
    assert user.is_phone_verified == create_params['is_phone_verified']
    assert user.source == (str(_s.value) if (_s := create_params.get('source')) else None)
    assert user.google_id == create_params['google_id']
    assert user.microsoft_id == create_params['microsoft_id']
    assert user.apple_id == create_params['apple_id']
    assert user.registration_method == create_params['registration_method']
    assert user.date_created is not None
    assert user.date_updated is not None
    assert user.is_subscribed_esputnik is False
    assert user.telegram_chat_id is None


@pytest.mark.parametrize(
    'user_object, params, expected',
    [
        # TODO: fix this test case because AuthUser should be able to have access only to
        # sign and view single document, but not to edit company
        pytest.param(
            create_dataclass_mock(
                AuthUser,
                user_role=UserRole.admin.value,
                can_delete_document=False,
            ),
            {'permissions': {'can_edit_company'}},
            True,
            id='admin_auth_user_has_access_regardless_of_any_permission_flags',
        ),
        pytest.param(
            create_dataclass_mock(AuthUser, user_role=UserRole.user.value),
            {'permissions': {'can_edit_company'}},  # AuthUser doesn't have this permission at all
            False,
            id='auth_user_permission_not_exist',
        ),
        pytest.param(
            create_dataclass_mock(
                AuthUser,
                user_role=UserRole.user.value,
                can_delete_document=False,
            ),
            {'permissions': {'can_delete_document'}},
            False,
            id='auth_user_permission_disabled',
        ),
        pytest.param(
            create_dataclass_mock(
                AuthUser,
                user_role=UserRole.user.value,
                can_delete_document=True,
            ),
            {'permissions': {'can_delete_document'}},
            True,
            id='auth_user_permission_enabled',
        ),
        pytest.param(
            create_dataclass_mock(
                AuthUser,
                user_role=UserRole.user.value,
                can_view_document=True,
                can_print_document=False,
            ),
            {'permissions': {'can_view_document', 'can_print_document'}, 'all_permissions': True},
            False,
            id='auth_user_not_all_permissions_enabled',
        ),
        pytest.param(
            create_dataclass_mock(
                AuthUser,
                user_role=UserRole.user.value,
                can_view_document=True,
                can_print_document=False,
            ),
            {'permissions': {'can_view_document', 'can_print_document'}, 'all_permissions': False},
            True,
            id='auth_user_at_least_one_permission_enabled',
        ),
        pytest.param(
            create_dataclass_mock(
                User,
                user_role=UserRole.admin.value,
                can_edit_company=False,
            ),
            {'permissions': {'can_edit_company'}},
            True,
            id='admin_user_permission_disabled',
        ),
        pytest.param(
            create_dataclass_mock(
                User,
                user_role=UserRole.user.value,
                can_view_document=True,
                can_edit_company=True,
            ),
            {'permissions': {'can_view_document', 'can_edit_company'}, 'all_permissions': True},
            True,
            id='user_permission_enabled',
        ),
        pytest.param(
            create_dataclass_mock(
                User,
                user_role=UserRole.user.value,
                can_view_document=True,
                can_edit_company=False,
            ),
            {'permissions': {'can_view_document', 'can_edit_company'}, 'all_permissions': True},
            False,
            id='user_permission_one_permission_disabled',
        ),
        pytest.param(
            create_dataclass_mock(
                User,
                user_role=UserRole.user.value,
                can_view_document=True,
                can_edit_company=False,
            ),
            {'permissions': {'can_view_document', 'can_edit_company'}, 'all_permissions': False},
            True,
            id='user_permission_at_least_one_enabled',
        ),
        pytest.param(
            create_dataclass_mock(User, user_role=UserRole.user.value, can_invite_coworkers=False),
            {'permissions': {'can_invite_coworkers'}},
            False,
            id='user_permission_disabled',
        ),
        pytest.param(
            create_dataclass_mock(BaseUser),
            {'permissions': {'can_view_document'}},
            False,
            id='base_user_no_permission',
        ),
        pytest.param(
            create_dataclass_mock(BaseUser),
            {'permissions': {'can_view_document', 'can_edit_company'}, 'all_permissions': False},
            False,
            id='base_user_no_permissions_any',
        ),
    ],
)
def test_has_permission(user_object, params, expected):
    """
    A comprehensive test for the `has_permission` function, covering various user types
    (User, AuthUser, BaseUser), admin override logic, and the 'all' vs 'any'
    permission checking modes.
    """
    assert (
        has_permission(
            user_object,
            required_permissions=params['permissions'],
            all_permissions=params.get('all_permissions', True),
        )
        == expected
    )


def test_has_permission_raises_errors():
    """Tests that has_permission raises AssertionErrors for invalid inputs."""
    user = create_dataclass_mock(User, user_role=UserRole.user.value)

    # Test for empty required_permissions set
    with pytest.raises(AssertionError):
        has_permission(user, required_permissions=set())

    # Test for invalid permission key
    with pytest.raises(AssertionError, match='Invalid permission name "can_do_magic"'):
        has_permission(user, required_permissions={'can_do_magic'})
