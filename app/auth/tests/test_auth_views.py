import asyncio
import datetime
import inspect
import typing
import uuid
from collections import Counter
from http import HTTPStatus
from unittest.mock import AsyncMock

import aiohttp.web
import pytest
import sqlalchemy as sa
import ujson

import app.auth.views
import app.config
import app.lib.middlewares
import app.signals
from api.enums import Vendor
from app.app import create_app
from app.auth import validators as auth_validators
from app.auth.constants import MONTH
from app.auth.db import (
    delete_tokens,
    insert_token,
    insert_user,
    select_base_user,
    select_company_meta,
    select_role_by_id,
    select_token_by,
    update_role,
)
from app.auth.enums import InviteSource, RoleStatus
from app.auth.helpers import (
    autogenerate_password,
    check_password_hash_md5,
    generate_hash_sha512,
)
from app.auth.providers.apple.client import AppleGetAccessCodeResponse
from app.auth.providers.google import GoogleTokenPayload
from app.auth.schemas import VersionSettings, VersionSettingsCategoryConfig
from app.auth.session_manager.constants import PUBLIC_SESSION_ID_SESSION_KEY
from app.auth.tables import token_table, zvit_users_table
from app.auth.tests.utils import get_base_user
from app.auth.types import User
from app.auth.utils import create_autogenerated_coworker, get_company, get_company_config
from app.document_categories.types import PublicDocumentCategory
from app.document_versions.enums import VersionReviewFlow
from app.events.user_actions.db import select_user_actions_for
from app.events.user_actions.types import Action as UserAction
from app.events.user_actions.types import UserActionDB
from app.lib import eusign_utils
from app.lib.datetime_utils import local_now, parse_raw_datetime
from app.lib.enums import RenderSignatureAtPage, SignatureType, UserRole
from app.lib.session_utils import get_active_sessions_key_for, get_session_redis_key
from app.models import select_one
from app.profile.tests.test_profile_views import (
    ADD_COMPANY_URL,
    TEST_USER_NAME,
)
from app.registration.enums import RegistrationMethod, RegistrationSource
from app.services import services
from app.tests.common import (
    API_V2_RECREATE_TOKEN,
    API_V2_TOKENS,
    GRAPHQL_URL,
    LOGIN_URL,
    LOGOUT_ALL_SESSIONS_URL,
    LOGOUT_URL,
    TEST_COMPANY_NAME,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    TOKENS_URL,
    cleanup_on_teardown,
    login,
    prepare_app_client,
    prepare_auth_headers,
    prepare_client,
    prepare_referer_headers,
    prepare_signature_info,
    prepare_user_data,
    set_company_config,
)

GQL_CURRENT_USER = ujson.dumps({'query': '{ currentUser { id email } }'})

TEST_COWORKER_EMAIL = '<EMAIL>'
LOGIN_WITH_GOOGLE_URL = '/auth-api/providers/google'
REGISTRATION_WITH_GOOGLE_URL = LOGIN_WITH_GOOGLE_URL
REGISTRATION_WITH_GOOGLE_MOBILE_URL = '/mobile-api/v1/auth/providers/google'
AUTOGENERATED_EDRPOU_1 = 'auto_Y4wRy2p7kasEgQmOSt6AKTPWC8M05rGF'

TEST_GOOGLE_TOKEN_PAYLOAD = {
    'sub': 'test_101',
    'iss': 'https://accounts.google.com',
    'email': '<EMAIL>',
    'email_verified': True,
    'name': 'Hello Kitty',
    'given_name': 'Hello',
    'family_name': 'Kitty',
}

LOGIN_WITH_APPLE_URL = '/auth-api/providers/apple'
TEST_APPLE_TOKEN_PAYLOAD = {
    'sub': 'test_101',
    'iss': 'https://appleid.apple.com',
    'email': '<EMAIL>',
    'email_verified': True,
}

TEST_EDRPOU_1 = '********'
TEST_EDRPOU_2 = '********'

TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'


async def test_add_token(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    data = {'role_id': user.role_id}

    real_validator = auth_validators._validate_token_not_exists

    async def fake_validator(a, b):
        pass

    monkeypatch.setattr(auth_validators, '_validate_token_not_exists', fake_validator)
    response = await client.post(
        TOKENS_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
    )
    monkeypatch.setattr(auth_validators, '_validate_token_not_exists', real_validator)

    assert response.status == 200
    result = await response.json()

    async with app['db'].acquire() as conn:
        token = await select_token_by(
            conn,
            sa.and_(
                token_table.c.role_id == user.role_id,
                token_table.c.token_hash != user.token_hash,
            ),
        )
    assert generate_hash_sha512(result['token']) == token.token_hash


async def test_add_batch_tokens(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app, company_edrpou=user.company_edrpou, email='<EMAIL>'
    )
    data = {'emails': [user.email, coworker.email], 'expire_days': 30}

    async with app['db'].acquire() as conn:
        await delete_tokens(conn, [coworker.role_id])

    response = await client.post(
        API_V2_TOKENS, data=ujson.dumps(data), headers=prepare_auth_headers(user)
    )
    assert response.status == 200
    result = await response.json()

    assert result['existed_tokens'] == [user.email]
    new_tokens = result['new_tokens']
    assert len(new_tokens) == 1
    expire_days = parse_raw_datetime(new_tokens[0]['date_expired'])
    assert expire_days > local_now() + datetime.timedelta(days=29)
    assert new_tokens[0]['email'] == coworker.email

    async with app['db'].acquire() as conn:
        token = await select_token_by(
            conn,
            sa.and_(
                token_table.c.role_id == coworker.role_id,
                token_table.c.token_hash != coworker.token_hash,
            ),
        )
    assert generate_hash_sha512(new_tokens[0]['token']) == token.token_hash


async def test_recreate_token(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    data = {'expire_days': 30}

    response = await client.post(
        API_V2_RECREATE_TOKEN,
        data=ujson.dumps(data),
        headers=prepare_auth_headers(user),
    )

    assert response.status == 200
    result = await response.json()

    async with app['db'].acquire() as conn:
        token = await select_token_by(
            conn,
            sa.and_(
                token_table.c.role_id == user.role_id,
                token_table.c.token_hash != user.token_hash,
            ),
        )
    assert generate_hash_sha512(result['token']) == token.token_hash


async def test_add_token_already_exists(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    data = {'role_id': user.role_id, 'vendor': Vendor.onec.value}

    try:
        response = await client.post(
            TOKENS_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
        )
        assert response.status == 400
        result = await response.json()
        assert result['code'] == 'object_already_exists'
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('vendor', [Vendor.onec.value, Vendor.api.value])
async def test_add_token_denied(aiohttp_client, vendor):
    app, client, user = await prepare_client(aiohttp_client)
    data = {'role_id': user.role_id, 'vendor': vendor}

    try:
        response = await client.post(
            TOKENS_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
        )
        assert response.status == 403

        result = await response.json()
        assert result['code'] == 'access_denied'
    finally:
        await cleanup_on_teardown(app)


async def test_add_token_for_coworker(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app, company_edrpou=user.company_edrpou, email='<EMAIL>'
    )
    data = {'role_id': coworker.role_id}

    real_validator = auth_validators._validate_token_not_exists

    async def fake_validator(a, b):
        pass

    monkeypatch.setattr(auth_validators, '_validate_token_not_exists', fake_validator)
    response = await client.post(
        TOKENS_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
    )
    monkeypatch.setattr(auth_validators, '_validate_token_not_exists', real_validator)

    assert response.status == 200
    result = await response.json()

    async with app['db'].acquire() as conn:
        token = await select_token_by(
            conn,
            sa.and_(
                token_table.c.role_id == coworker.role_id,
                token_table.c.token_hash != coworker.token_hash,
            ),
        )

    assert generate_hash_sha512(result['token']) == token.token_hash


@pytest.mark.parametrize('vendor', [Vendor.onec.value, Vendor.api.value])
async def test_add_token_forbidden(aiohttp_client, vendor):
    client = await aiohttp_client(create_app())
    response = await client.post(
        '/internal-api/tokens',
        data=ujson.dumps({'role_id': str(uuid.uuid4()), 'vendor': vendor}),
        headers=prepare_referer_headers(client),
    )
    assert response.status == 403


async def test_add_token_invalid_role(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    data = {'role_id': str(uuid.uuid4()), 'vendor': Vendor.api.value}

    try:
        response = await client.post(
            TOKENS_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
        )
        assert response.status == 404
        result = await response.json()
        assert result['code'] == 'object_does_not_exist'
    finally:
        await cleanup_on_teardown(app)


async def test_colbert_identify(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    try:
        response = await client.get(
            '/internal-api/colbert/identify', headers=prepare_auth_headers(user)
        )
        assert response.status == 200
        result = await response.json()
        if not services.config.colbert:
            assert result is None
        else:
            assert result['ts'] is not None
            assert result['id'] is not None
            assert result['sha1'] is not None

    finally:
        await cleanup_on_teardown(app)


async def test_delete_token(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client, is_admin=True, token_vendor=Vendor.onec.value
    )

    async with app['db'].acquire() as conn:
        token = await insert_token(conn, {'role_id': user.role_id})

        response = await client.delete(
            TOKENS_URL,
            json={'role_id': user.role_id},
            headers=prepare_auth_headers(user),
        )
        assert response.status == 204

        deleted_token = await select_token_by(
            conn, token_table.c.token_hash == generate_hash_sha512(token)
        )
        assert deleted_token is None


async def test_delete_token_does_not_exist(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    invalid_role = str(uuid.uuid4())
    response = await client.delete(
        TOKENS_URL, json={'role_id': invalid_role}, headers=prepare_auth_headers(user)
    )
    assert response.status == 404

    result = await response.json()
    assert result['code'] == 'object_does_not_exist'


async def test_delete_token_for_coworker(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app, company_edrpou=user.company_edrpou, email=TEST_COWORKER_EMAIL
    )

    async with app['db'].acquire() as conn:
        response = await client.delete(
            TOKENS_URL,
            json={'role_id': coworker.role_id},
            headers=prepare_auth_headers(user),
        )
        assert response.status == 204

        deleted_token = await select_token_by(conn, token_table.c.token_hash == coworker.token_hash)
        assert deleted_token is None


async def test_delete_token_for_not_coworker(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    recipient = await prepare_user_data(
        app, company_edrpou='1234567890', email=TEST_DOCUMENT_EMAIL_RECIPIENT
    )

    async with app['db'].acquire() as conn:
        response = await client.delete(
            TOKENS_URL,
            json={'role_id': recipient.role_id},
            headers=prepare_auth_headers(user),
        )
        assert response.status == 403

        not_deleted_token = await select_token_by(
            conn, token_table.c.token_hash == recipient.token_hash
        )
        assert not_deleted_token is not None


async def test_delete_token_forbidden(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    fake_role = str(uuid.uuid4())
    response = await client.delete(
        TOKENS_URL, json={'role_id': fake_role}, headers=prepare_auth_headers(user)
    )
    assert response.status == 403


@pytest.mark.parametrize(
    'user_password, input_password, remember, login_status, graph_status',
    [
        (TEST_USER_PASSWORD, TEST_USER_PASSWORD, False, 200, 200),
        (TEST_USER_PASSWORD, TEST_USER_PASSWORD, True, 200, 200),
        (TEST_USER_PASSWORD, TEST_USER_PASSWORD[::-1], False, 400, 403),
        (TEST_USER_PASSWORD, TEST_USER_PASSWORD[::-1], True, 400, 403),
    ],
)
async def test_login(
    aiohttp_client, user_password, input_password, remember, login_status, graph_status
):
    app, client, user = await prepare_client(aiohttp_client, password=user_password)
    headers = prepare_referer_headers(client)

    try:
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps(
                {'email': user.email, 'password': input_password, 'remember': remember}
            ),
            headers=headers,
        )
        assert response.status == login_status

        if login_status == 200:
            assert await services.redis.lrange(get_active_sessions_key_for(user.id), 0, -1)

        response = await client.post(GRAPHQL_URL, data=GQL_CURRENT_USER, headers=headers)
        assert response.status == graph_status
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'wrong_pass_retries, right_pass_retries, status_code',
    [
        (10, 5, HTTPStatus.TOO_MANY_REQUESTS),  # Apply limiter after 10 attempts
        (9, 5, HTTPStatus.OK),  # Reset limiter after success
    ],
)
async def test_login_rate_limit(
    aiohttp_client, wrong_pass_retries, right_pass_retries, status_code
):
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)

    headers = prepare_referer_headers(client)
    for _ in range(wrong_pass_retries):
        response = await client.post(
            LOGIN_URL,
            json={
                'email': user.email,
                'password': TEST_USER_PASSWORD[::-1],
                'remember': False,
            },
            headers=headers,
        )
        assert response.status == HTTPStatus.BAD_REQUEST

    for _ in range(right_pass_retries):
        response = await client.post(
            LOGIN_URL,
            json={
                'email': user.email,
                'password': TEST_USER_PASSWORD,
                'remember': False,
            },
            headers=headers,
        )
        assert response.status == status_code


class TestOutdatedCookieLogin:
    """Tests login attempts with cookies that are associated with outdated
    sessions.

    Checks for a regression introduced by [DOC-4305]
    (commit da389c8608cdc35acdec2a6917741518cbbb8a22).
    """

    async def test_outdated_cookie_second_login_should_give_access_to_app(
        self,
        aiohttp_client: aiohttp.test_utils.TestClient,
    ) -> None:
        """Attempts to log in with valid credentials and an session cookie that
        corresponds to a session that has outlived its max age should
        successfully log the user in and give appropriate access to the
        application.

        Given:
            - A user has logged in previously.
            - And a user holds a session cookie from the previous login.
            - And the session associated with the session cookie has expired.
        When:
            - The user attempts to log in with valid credentials.
        Then:
            - The application logs the user in successfully.
            - The application gives the user access to the application's
              routes.
        """
        app, client, user = await prepare_client(aiohttp_client)

        try:
            # Login in the first time to create a valid login session
            first_login_response = await login(
                client,
                email=user.email,
                password=TEST_USER_PASSWORD,
                remember=False,
            )

            # Extract session information from the first login
            session_cookie_name = services.config.app.cookie_name
            first_session_cookie = {
                session_cookie_name: first_login_response.cookies[session_cookie_name].value
            }
            session_id = first_session_cookie[session_cookie_name]

            # Set the current session's `created` attribute to a point before the
            # currently configured max age to simulate the session being outdated
            redis = services.redis
            session_key = f'{session_cookie_name}_{session_id}'
            stored_session_raw = await redis.get(session_key)
            stored_session = ujson.loads(stored_session_raw)
            current_max_age = services.config.app.session_max_age
            created_before_max_age = stored_session['created'] - current_max_age - 10_000
            stored_session['created'] = created_before_max_age
            stored_session_serialized = ujson.dumps(stored_session)
            await redis.set(session_key, stored_session_serialized)

            # Try to log in the second time
            second_login_response = await login(
                client,
                email=user.email,
                password=TEST_USER_PASSWORD,
                remember=False,
                cookies=first_session_cookie,
            )
            assert second_login_response.status == 200
            second_login_session_cookie = {
                session_cookie_name: second_login_response.cookies[session_cookie_name]
            }

            # After the second login, the user should be able to access the
            # application route
            app_access = await client.get(
                '/app',
                cookies=second_login_session_cookie,
            )
            assert app_access.status == 200
            assert app_access.url.path == '/app'
        finally:
            await cleanup_on_teardown(app, clean_redis=True)


class TestLoginViewSessionFixation:
    """Test for the session fixation in the login view.

    The login view has to resist session fixation by itself, without any
    decorators wrapping it. To achieve this, the view must issue new
    sessions whenever there is a successful login.
    """

    TMP_LOGIN_ROUTE = '/nonsense_route/login'

    @pytest.fixture(params=[True, False])
    def remember(self, request) -> bool:
        """Return a value for the "Remember Me" login option."""
        return request.param

    async def _get_fake_app_with_testing_route(
        self,
    ) -> tuple[aiohttp.web.Application, str]:
        """Return a fake application instance with a testing route for the login view.

        Since `create_app()`, as defined in the application code, sets up
        routes, the previously defined routes would take precedence over newly
        defined routes. Therefore, we need to attach the undecorated login view
        to a new testing route, and run the test on the newly attached route.
        """
        fake_app = create_app()

        test_route = self.TMP_LOGIN_ROUTE
        # Since create_app() freezes the app and its contents, unfreeze the
        # router to attach the testing route
        fake_app.router._frozen = False
        undecorated_login_view = inspect.unwrap(app.auth.views.login)
        fake_app.router.add_route('POST', test_route, undecorated_login_view)
        fake_app.router._frozen = True

        return fake_app, test_route

    def get_session_cookie_name(self, app: aiohttp.web.Application) -> str:
        """Return the name of a session cookie."""
        return services.config.app.cookie_name

    def get_current_session_cookie(
        self,
        app: aiohttp.web.Application,
        response: aiohttp.web.Response,
    ):
        """Return the current session cookie."""
        session_cookie_name = services.config.app.cookie_name
        return response.cookies[session_cookie_name]

    async def test_successful_login_creates_new_session(
        self,
        aiohttp_client,
        remember: bool,
    ) -> None:
        """The login view should issue new sesisons on each login.

        Issuing new sessions on every login is a behavior that OWASP recommends
        to protect against session fixation attacks.
        (https://owasp.org/www-community/attacks/Session_fixation).

        Given:
            - A user is currently logged in.
            - A user holds a session cookie associated with the current login.
        When:
            - A client makes a request to log in with the user's current
              session cookie.
        Then:
            - The application issues a new session cookie.
        """
        try:
            fake_app, test_route = await self._get_fake_app_with_testing_route()

            aiohttp_client = await aiohttp_client(fake_app)
            user = await prepare_user_data(fake_app)
            referer_headers = prepare_referer_headers(aiohttp_client)

            first_response = await aiohttp_client.post(
                test_route,
                data=ujson.dumps(
                    {
                        'email': user.email,
                        'password': TEST_USER_PASSWORD,
                        'remember': remember,
                    }
                ),
                headers=referer_headers,
            )
            assert first_response.status == 200

            first_session_cookie = self.get_current_session_cookie(fake_app, first_response)
            first_session_cookies = {
                self.get_session_cookie_name(fake_app): self.get_current_session_cookie(
                    fake_app, first_response
                )
            }

            second_response = await aiohttp_client.post(
                test_route,
                data=ujson.dumps(
                    {
                        'email': user.email,
                        'password': TEST_USER_PASSWORD,
                        'remember': remember,
                    }
                ),
                headers=referer_headers,
                cookies=first_session_cookies,
            )
            assert second_response.status == 200

            second_session_cookie = self.get_current_session_cookie(fake_app, second_response)

            assert first_session_cookie.value != second_session_cookie.value
            assert first_session_cookie != second_session_cookie
        finally:
            await cleanup_on_teardown(fake_app, clean_redis=True)

    async def test_second_login_destroys_previous_session(
        self,
        aiohttp_client,
        remember: bool,
    ) -> None:
        """The login view should destroy any previous sessions passed with a
        session cookie on login.

        Given:
            - A user holds a session cookie.
            - A session cookie is associated with a session that exists in
              session storage.
        When:
            - A user tries to log in.
        Then:
            - The login view destroys the session attached to a cookie that
              the user currently holds.
        """
        try:
            fake_app, test_route = await self._get_fake_app_with_testing_route()

            aiohttp_client = await aiohttp_client(fake_app)
            user = await prepare_user_data(fake_app)
            referer_headers = prepare_referer_headers(aiohttp_client)

            first_response = await aiohttp_client.post(
                test_route,
                data=ujson.dumps(
                    {
                        'email': user.email,
                        'password': TEST_USER_PASSWORD,
                        'remember': remember,
                    }
                ),
                headers=referer_headers,
            )
            assert first_response.status == 200

            first_session_cookie = self.get_current_session_cookie(fake_app, first_response)
            first_session_cookies = {
                self.get_session_cookie_name(fake_app): self.get_current_session_cookie(
                    fake_app, first_response
                )
            }

            second_response = await aiohttp_client.post(
                test_route,
                data=ujson.dumps(
                    {
                        'email': user.email,
                        'password': TEST_USER_PASSWORD,
                        'remember': remember,
                    }
                ),
                headers=referer_headers,
                cookies=first_session_cookies,
            )
            assert second_response.status == 200

            redis = services.redis
            session_key_name = (
                f'{self.get_session_cookie_name(fake_app)}_{first_session_cookie.value}'
            )
            first_session = await redis.get(session_key_name)
            assert first_session is None

        finally:
            await cleanup_on_teardown(fake_app, clean_redis=True)


@pytest.mark.parametrize(
    'remember, get_expected_session_max_age',
    [
        (False, lambda app: services.config.app.session_max_age),
        (True, lambda _: MONTH * 3),
    ],
)
async def test_login_successful_login_updates_active_session_list_expiration(
    aiohttp_client,
    remember: bool,
    get_expected_session_max_age: typing.Callable[[aiohttp.web.Application], int],
) -> None:
    """Test if the login view after a successful login updates the expiration
    time of a Redis key that holds a list of active sessions for a user.
    """
    app, client, user = await prepare_client(aiohttp_client)
    expected_session_list_ttl = get_expected_session_max_age(app)

    _ = await login(client, user.email, TEST_USER_PASSWORD, remember=remember)
    user_active_sessions_key = get_active_sessions_key_for(user.id)

    assert await services.redis.lrange(user_active_sessions_key, 0, -1)

    session_list_ttl = await services.redis.ttl(user_active_sessions_key)

    assert session_list_ttl <= expected_session_list_ttl


@pytest.mark.parametrize(
    'user_password_length, remember, login_status',
    [(None, True, 200), (None, False, 200), (6, True, 400), (6, False, 400)],
)
async def test_login_autogenerated_user(
    aiohttp_client, user_password_length, remember, login_status
):
    if user_password_length is None:
        user_password = autogenerate_password()
    else:
        user_password = autogenerate_password(user_password_length)

    app, client, invited_by = await prepare_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    try:
        async with app['db'].acquire() as conn:
            await create_autogenerated_coworker(
                conn=conn,
                edrpou=TEST_EDRPOU_1,
                email=TEST_EMAIL_1,
                password=user_password,
                first_name=None,
                second_name=None,
                last_name=None,
                phone=None,
                is_phone_verified=False,
                position=None,
                created_by=User.from_row(invited_by),
            )

        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps(
                {
                    'email': TEST_EMAIL_1,
                    'password': user_password,
                    'remember': remember,
                }
            ),
            headers=headers,
        )
        assert response.status == login_status
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'password',
    [
        '',
        TEST_USER_PASSWORD,
        None,
    ],
)
async def test_login_no_password(
    aiohttp_client,
    password,
) -> None:
    """Check that it's not possible to login if user doesn't have password at all"""

    app, client = await prepare_app_client(aiohttp_client)

    await prepare_user_data(
        app=app,
        email=TEST_USER_EMAIL,
        password=None,
    )

    response = await client.post(
        LOGIN_URL,
        json={
            'email': TEST_USER_EMAIL,
            'password': password,
            'remember': True,
        },
        headers=prepare_referer_headers(client),
    )
    assert response.status == HTTPStatus.BAD_REQUEST


async def test_logout(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)
    headers = prepare_referer_headers(client)

    try:
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': user.email, 'password': TEST_USER_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        assert await services.redis.lrange(get_active_sessions_key_for(user.id), 0, -1)

        response = await client.post(LOGOUT_URL, headers=headers)
        assert response.status == 200

        assert not await services.redis.lrange(get_active_sessions_key_for(user.id), 0, -1)

        response = await client.post(GRAPHQL_URL, data=GQL_CURRENT_USER, headers=headers)
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


async def test_logout_no_user(aiohttp_client):
    client = await aiohttp_client(create_app())
    response = await client.post(LOGOUT_URL, headers=prepare_referer_headers(client))
    assert response.status == 200


async def test_logout_all_sessions(aiohttp_client, concierge_emulation):
    app, client_1, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)
    client_2 = await aiohttp_client(client_1.server)

    lodin_data = ujson.dumps({'email': user.email, 'password': TEST_USER_PASSWORD})

    session_id_key = get_active_sessions_key_for(user.id)

    response = await client_1.post(
        LOGIN_URL, data=lodin_data, headers=prepare_referer_headers(client_1)
    )
    assert response.status == 200

    sessions = await services.redis.lrange(session_id_key, 0, -1)
    assert len(sessions) == 1

    response = await client_2.post(
        LOGIN_URL, data=lodin_data, headers=prepare_referer_headers(client_2)
    )
    assert response.status == 200

    sessions = await services.redis.lrange(session_id_key, 0, -1)
    assert len(sessions) == 2

    resp_1 = await client_1.post(GRAPHQL_URL, headers=prepare_referer_headers(client_1))
    resp_2 = await client_2.post(GRAPHQL_URL, headers=prepare_referer_headers(client_2))
    assert (await resp_1.json())['code'] != 'login_required'
    assert (await resp_2.json())['code'] != 'login_required'

    response = await client_1.post(
        LOGOUT_ALL_SESSIONS_URL, json={}, headers=prepare_referer_headers(client_1)
    )
    assert response.status == 200

    sessions = await services.redis.lrange(session_id_key, 0, -1)
    assert len(sessions) == 0

    resp_1 = await client_1.post(GRAPHQL_URL, headers=prepare_referer_headers(client_1))
    resp_2 = await client_2.post(GRAPHQL_URL, headers=prepare_referer_headers(client_2))
    assert (await resp_1.json())['code'] == 'login_required'
    assert (await resp_2.json())['code'] == 'login_required'


@pytest.mark.parametrize(
    ['url', 'expected_response', 'test_graph'],
    [
        (
            LOGIN_WITH_GOOGLE_URL,
            {
                'next_url': 'http://localhost:8000/app',
                'flow': 'login',
                'is_2fa_enabled': False,
                'email': '<EMAIL>',
            },
            True,
        ),
    ],
)
async def test_auth_with_google_login(
    aiohttp_client, monkeypatch, url, expected_response, test_graph
):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    token_payload = TEST_GOOGLE_TOKEN_PAYLOAD.copy()
    token_payload_obj = GoogleTokenPayload.from_dict(token_payload)
    verify_token_mock = AsyncMock(return_value=token_payload_obj)
    monkeypatch.setattr('app.auth.providers.google.verify_oauth2_token', verify_token_mock)

    response = await client.post(
        path=url,
        json={'token': 'jwt-token-from-google'},
        headers=headers,
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()

    response_json.pop('refresh_token', None)
    response_json.pop('access_token', None)

    assert response_json == expected_response

    user_updated = await get_base_user(user_id=user.id)
    assert user_updated.google_id == token_payload_obj.sub

    if test_graph:
        response = await client.post(
            path=GRAPHQL_URL,
            data=GQL_CURRENT_USER,
            headers=headers,
        )
        assert response.status == HTTPStatus.OK
        response_json = await response.json()
        assert response_json['data']['currentUser']['id'] == user.id


@pytest.mark.parametrize(
    ['url', 'expected_response'],
    [
        (
            REGISTRATION_WITH_GOOGLE_URL,
            {
                'code': 'invalid_email_provided',
                'reason': 'Електронні пошти не збігаються',
                'details': None,
            },
        ),
        (
            REGISTRATION_WITH_GOOGLE_MOBILE_URL,
            {
                'code': 'invalid_email_provided',
                'reason': 'Електронні пошти не збігаються',
                'details': None,
            },
        ),
    ],
)
async def test_auth_with_google_wrong_email_provided(
    aiohttp_client, monkeypatch, url, expected_response
):
    app, client, user = await prepare_client(aiohttp_client, is_dealer=True)
    headers = prepare_referer_headers(client)

    email = '<EMAIL>'
    token_payload = {
        **TEST_GOOGLE_TOKEN_PAYLOAD.copy(),
        'email': email,
    }
    token_payload_obj = GoogleTokenPayload.from_dict(token_payload)
    verify_token_mock = AsyncMock(return_value=token_payload_obj)
    monkeypatch.setattr(
        'app.auth.providers.google.verify_oauth2_token',
        verify_token_mock,
    )

    registration_data = {
        'token': 'jwt-token-from-google',
        'referrer': user.role_id,
        'source': RegistrationSource.after_invite.value,
        'redirect': 'http://localhost/some-path',
        'promo_code': 'some-promo-code',
        'trial_auto_enable': True,
        'invite_email': '<EMAIL>',
    }
    response = await client.post(
        path=url,
        json=registration_data,
        headers=headers,
        allow_redirects=False,
    )
    assert response.status == HTTPStatus.BAD_REQUEST, await response.json()
    response_json = await response.json()
    assert response_json == expected_response

    registration_data = {
        'token': 'jwt-token-from-google',
        'referrer': user.role_id,
        'source': RegistrationSource.after_invite.value,
        'redirect': 'http://localhost/some-path',
        'promo_code': 'some-promo-code',
        'trial_auto_enable': True,
    }
    response = await client.post(
        path=url,
        json=registration_data,
        headers=headers,
        allow_redirects=False,
    )
    assert response.status == HTTPStatus.BAD_REQUEST, await response.json()
    response_json = await response.json()
    assert response_json == expected_response


@pytest.mark.parametrize(
    'url',
    [
        REGISTRATION_WITH_GOOGLE_URL,
        REGISTRATION_WITH_GOOGLE_MOBILE_URL,
    ],
)
async def test_auth_with_google_email_not_confirmed(aiohttp_client, monkeypatch, url):
    """
    Case:
    - User is registered with usual registration
    - User didn't pass email verification yet, and logged out
    - User trying to sign in using Google
    Expected behaviour:
    - Email should be automatically confirmed
    """

    app, client, user = await prepare_client(aiohttp_client, is_dealer=True, email_confirmed=False)
    headers = prepare_referer_headers(client)

    assert user.email_confirmed is False

    token_payload = TEST_GOOGLE_TOKEN_PAYLOAD.copy()
    token_payload_obj = GoogleTokenPayload.from_dict(token_payload)
    verify_token_mock = AsyncMock(return_value=token_payload_obj)
    monkeypatch.setattr(
        'app.auth.providers.google.verify_oauth2_token',
        verify_token_mock,
    )

    response = await client.post(
        path=url,
        json={
            'token': 'jwt-token-from-google',
        },
        headers=headers,
    )
    assert response.status == HTTPStatus.OK

    async with services.db_readonly.acquire() as conn:
        user = await select_base_user(conn, email=user.email)

    assert user.email_confirmed is True


@pytest.mark.parametrize(
    ['url', 'expected_response', 'test_graph'],
    [
        (
            LOGIN_WITH_GOOGLE_URL,
            {
                'next_url': 'http://localhost/some-path',
                'flow': 'registration',
                'is_2fa_enabled': False,
                'email': '<EMAIL>',
            },
            True,
        ),
    ],
)
async def test_auth_with_google_registration(
    aiohttp_client, monkeypatch, url, expected_response, test_graph
):
    app, client, user = await prepare_client(aiohttp_client, is_dealer=True)
    headers = prepare_referer_headers(client)

    email = '<EMAIL>'
    token_payload = {
        **TEST_GOOGLE_TOKEN_PAYLOAD.copy(),
        'email': email,
    }
    token_payload_obj = GoogleTokenPayload.from_dict(token_payload)
    verify_token_mock = AsyncMock(return_value=token_payload_obj)
    monkeypatch.setattr('app.auth.providers.google.verify_oauth2_token', verify_token_mock)

    registration_data = {
        'token': 'jwt-token-from-google',
        'referrer': user.role_id,
        'source': RegistrationSource.vchasno.value,
        'redirect': 'http://localhost/some-path',
        'promo_code': 'some-promo-code',
        'trial_auto_enable': True,
    }
    response = await client.post(
        path=url,
        json=registration_data,
        headers=headers,
        allow_redirects=False,
    )
    assert response.status == HTTPStatus.OK, await response.json()
    response_json = await response.json()

    response_json.pop('refresh_token', None)
    response_json.pop('access_token', None)

    assert response_json == expected_response

    async with services.db.acquire() as conn:
        new_user = await select_base_user(conn, google_id=token_payload_obj.sub)

    assert new_user is not None
    assert new_user.google_id == token_payload_obj.sub
    assert new_user.email == token_payload_obj.email
    assert new_user.first_name == token_payload_obj.given_name
    assert new_user.last_name == token_payload_obj.family_name
    assert new_user.email_confirmed is True
    assert new_user.pending_referrer_role_id == user.role_id
    assert new_user.promo_code == 'some-promo-code'
    assert new_user.trial_auto_enabled is True
    assert new_user.source == RegistrationSource.vchasno.value
    assert new_user.is_autogenerated_password is False
    assert new_user.password is None
    assert new_user.registration_method == RegistrationMethod.google
    assert new_user.is_logged_once is True

    if test_graph:
        response = await client.post(
            path=GRAPHQL_URL,
            data=GQL_CURRENT_USER,
            headers=headers,
        )
        assert response.status == HTTPStatus.OK


@pytest.mark.parametrize(
    ['url', 'expected_response', 'test_graph'],
    [
        (
            LOGIN_WITH_APPLE_URL,
            {
                'email': '<EMAIL>',
                'next_url': 'http://localhost:8000/app',
                'is_2fa_enabled': False,
                'flow': 'login',
            },
            True,
        ),
    ],
)
async def test_auth_with_apple_login_user_exists_and_login_before(
    aiohttp_client,
    monkeypatch,
    url,
    expected_response,
    test_graph,
):
    from app.auth.providers.apple.client import apple_auth

    app, client, user = await prepare_client(
        aiohttp_client, apple_id=TEST_APPLE_TOKEN_PAYLOAD['sub']
    )
    headers = prepare_referer_headers(client)

    token = AppleGetAccessCodeResponse(**TEST_APPLE_TOKEN_PAYLOAD)
    verify_token_mock = AsyncMock(return_value=token)
    monkeypatch.setattr(
        apple_auth,
        'get_access_token',
        verify_token_mock,
    )

    response = await client.post(
        path=url,
        json={
            'code': 'apple_code',
        },
        headers=headers,
    )
    response_json = await response.json()

    response_json.pop('refresh_token', None)
    response_json.pop('access_token', None)

    assert response.status == HTTPStatus.OK
    assert response_json == expected_response

    async with services.db.acquire() as conn:
        new_user = await select_base_user(conn, apple_id=token.sub)

    assert new_user is not None
    assert new_user.apple_id == token.sub
    assert new_user.email == token.email

    if test_graph:
        response = await client.post(
            path=GRAPHQL_URL,
            data=GQL_CURRENT_USER,
            headers=headers,
        )
        assert response.status == HTTPStatus.OK
        response_json = await response.json()
        assert response_json['data']['currentUser']['id'] == user.id


@pytest.mark.parametrize(
    ['url', 'expected_response', 'test_graph'],
    [
        (
            LOGIN_WITH_APPLE_URL,
            {
                'email': '<EMAIL>',
                'flow': 'login',
                'is_2fa_enabled': False,
                'next_url': 'http://localhost:8000/app',
            },
            True,
        ),
    ],
)
async def test_auth_with_apple_login_user_exists_and_connected(
    aiohttp_client,
    monkeypatch,
    url,
    expected_response,
    test_graph,
):
    from app.auth.providers.apple.client import apple_auth

    app, client, user = await prepare_client(
        aiohttp_client, apple_id=TEST_APPLE_TOKEN_PAYLOAD['sub']
    )
    headers = prepare_referer_headers(client)

    token = AppleGetAccessCodeResponse(**TEST_APPLE_TOKEN_PAYLOAD)
    verify_token_mock = AsyncMock(return_value=token)
    monkeypatch.setattr(
        apple_auth,
        'get_access_token',
        verify_token_mock,
    )

    response = await client.post(
        path=url,
        json={
            'code': 'apple_code',
        },
        headers=headers,
    )
    response_json = await response.json()

    assert response.status == HTTPStatus.OK

    response_json.pop('refresh_token', None)
    response_json.pop('access_token', None)

    assert response_json == expected_response

    async with services.db.acquire() as conn:
        new_user = await select_base_user(conn, apple_id=token.sub)

    assert new_user is not None
    assert new_user.apple_id == token.sub
    assert new_user.email == token.email

    if test_graph:
        response = await client.post(
            path=GRAPHQL_URL,
            data=GQL_CURRENT_USER,
            headers=headers,
        )
        assert response.status == HTTPStatus.OK
        response_json = await response.json()
        assert response_json['data']['currentUser']['id'] == user.id


async def test_auth_with_apple_login_user_exists_and_not_connected_email_is_not_verified(
    aiohttp_client,
    monkeypatch,
):
    """
    Connect user with given apple_id
    """
    from app.auth.providers.apple.client import apple_auth

    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_APPLE_TOKEN_PAYLOAD['email'],
    )
    headers = prepare_referer_headers(client)

    token = AppleGetAccessCodeResponse.from_dict(
        {**TEST_APPLE_TOKEN_PAYLOAD, 'email_verified': False}
    )
    verify_token_mock = AsyncMock(return_value=token)
    monkeypatch.setattr(
        apple_auth,
        'get_access_token',
        verify_token_mock,
    )

    # make sure user can login with login after connecting
    response = await client.post(
        path=LOGIN_WITH_APPLE_URL,
        json={
            'code': 'apple_code',
        },
        headers=headers,
    )
    response_json = await response.json()

    assert response.status == HTTPStatus.BAD_REQUEST
    assert response_json == {
        'code': 'invalid_request',
        'reason': 'Email from Apple ID is not verified',
        'details': None,
    }

    async with app['db'].acquire() as conn:
        user = await select_base_user(conn, email=user.email)
        assert user.apple_id is None


@pytest.mark.parametrize(
    ['url', 'expected_response', 'test_graph'],
    [
        (
            LOGIN_WITH_APPLE_URL,
            {
                'email': '<EMAIL>',
                'flow': 'registration',
                'is_2fa_enabled': False,
                'next_url': 'http://localhost:8000/app',
            },
            True,
        ),
    ],
)
async def test_auth_with_apple_login_new_user_with_verified_email(
    aiohttp_client,
    monkeypatch,
    mailbox,
    url,
    expected_response,
    test_graph,
):
    from app.auth.providers.apple.client import apple_auth

    app, client = await prepare_app_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    token = AppleGetAccessCodeResponse.from_dict(TEST_APPLE_TOKEN_PAYLOAD)
    verify_token_mock = AsyncMock(return_value=token)
    monkeypatch.setattr(
        apple_auth,
        'get_access_token',
        verify_token_mock,
    )

    # make sure user can login with login after connecting
    response = await client.post(
        path=url,
        json={
            'code': 'apple_code',
            'user': {
                'email': TEST_APPLE_TOKEN_PAYLOAD['email'],
                'name': {
                    'firstName': 'Test',
                    'lastName': 'User',
                },
            },
        },
        headers=headers,
    )
    response_json = await response.json()

    assert response.status == HTTPStatus.OK

    response_json.pop('refresh_token', None)
    response_json.pop('access_token', None)

    assert response_json == expected_response

    async with app['db'].acquire() as conn:
        user = await select_base_user(conn, email=TEST_APPLE_TOKEN_PAYLOAD['email'])
        assert user.apple_id == TEST_APPLE_TOKEN_PAYLOAD['sub']
        assert user.email_confirmed is True
        assert user.first_name == 'Test'
        assert user.last_name == 'User'

    assert len(mailbox) == 0

    if test_graph:
        response = await client.post(
            path=GRAPHQL_URL,
            data=GQL_CURRENT_USER,
            headers=headers,
        )
        assert response.status == HTTPStatus.OK
        response_json = await response.json()
        assert response_json['data']['currentUser']['email'] == TEST_APPLE_TOKEN_PAYLOAD['email']


async def test_auth_with_apple_login_user_with_verified_email_after_failed_invite(
    aiohttp_client,
    monkeypatch,
):
    """
    Fail to login user after invite due to different emails
    """

    from app.auth.providers.apple.client import apple_auth

    app, client = await prepare_app_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    token = AppleGetAccessCodeResponse.from_dict(TEST_APPLE_TOKEN_PAYLOAD)
    verify_token_mock = AsyncMock(return_value=token)
    monkeypatch.setattr(
        apple_auth,
        'get_access_token',
        verify_token_mock,
    )

    response = await client.post(
        path=LOGIN_WITH_APPLE_URL,
        json={
            'code': 'apple_code',
            'source': RegistrationSource.after_invite.value,
            'invite_email': '1' + TEST_APPLE_TOKEN_PAYLOAD['email'],
        },
        headers=headers,
    )
    response_json = await response.json()

    assert response.status == HTTPStatus.BAD_REQUEST
    assert response_json == {
        'code': 'invalid_email_provided',
        'reason': 'Електронні пошти не збігаються',
        'details': None,
    }


@pytest.mark.parametrize(
    ['url', 'expected_response', 'test_graph'],
    [
        (
            LOGIN_WITH_APPLE_URL,
            {
                'email': '<EMAIL>',
                'flow': 'registration',
                'is_2fa_enabled': False,
                'next_url': 'http://localhost:8000/app',
            },
            True,
        ),
    ],
)
async def test_auth_with_apple_login_new_user_with_unverified_email(
    aiohttp_client,
    monkeypatch,
    mailbox,
    url,
    expected_response,
    test_graph,
):
    from app.auth.providers.apple.client import apple_auth

    app, client = await prepare_app_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    token = AppleGetAccessCodeResponse.from_dict(
        {
            **TEST_APPLE_TOKEN_PAYLOAD,
            'email_verified': False,
        }
    )
    verify_token_mock = AsyncMock(return_value=token)
    monkeypatch.setattr(
        apple_auth,
        'get_access_token',
        verify_token_mock,
    )

    # make sure user can login with login after connecting
    response = await client.post(
        path=url,
        json={
            'code': 'apple_code',
        },
        headers=headers,
    )
    response_json = await response.json()

    assert response.status == HTTPStatus.OK

    response_json.pop('refresh_token', None)
    response_json.pop('access_token', None)

    assert response_json == expected_response

    async with app['db'].acquire() as conn:
        user = await select_base_user(conn, email=TEST_APPLE_TOKEN_PAYLOAD['email'])
        assert user.apple_id == TEST_APPLE_TOKEN_PAYLOAD['sub']
        assert user.email_confirmed is False

    assert mailbox[0]['Subject'] == 'Підтвердження реєстрації'

    if test_graph:
        response = await client.post(
            path=GRAPHQL_URL,
            data=GQL_CURRENT_USER,
            headers=headers,
        )
        assert response.status == HTTPStatus.OK
        response_json = await response.json()
        assert response_json['data']['currentUser']['email'] == TEST_APPLE_TOKEN_PAYLOAD['email']


async def test_auth_with_apple_using_hidden_email_when_apple_id_exists(aiohttp_client, monkeypatch):
    """
    Given an apple_id, which already exists in a system under one email
    When attempting to log in using different email, but same apple_id
    Expected successfull attempt with to log in into the existing account
    """
    from app.auth.providers.apple.client import apple_auth

    # Prepare first user already having apple id assigned
    app, client, first_user = await prepare_client(
        aiohttp_client, apple_id=TEST_APPLE_TOKEN_PAYLOAD['sub']
    )
    headers = prepare_referer_headers(client)

    # Imitate that second user tries to register with the same apple_id
    second_user_email = '<EMAIL>'
    payload = TEST_APPLE_TOKEN_PAYLOAD.copy()
    payload['email'] = second_user_email
    token = AppleGetAccessCodeResponse(**payload)
    verify_token_mock = AsyncMock(return_value=token)
    monkeypatch.setattr(apple_auth, 'get_access_token', verify_token_mock)

    # Assert that apple_id belongs to first user, and second user doesn't exist
    async with services.db.acquire() as conn:
        first_user = await select_base_user(conn=conn, email=first_user.email)
        second_user = await select_base_user(conn=conn, email=second_user_email)
        assert first_user.apple_id == TEST_APPLE_TOKEN_PAYLOAD['sub']
        assert second_user is None

    # Try to login second user
    response = await client.post(
        path=LOGIN_WITH_APPLE_URL,
        json={'code': 'apple_code'},
        headers=headers,
    )
    response_json = await response.json()
    assert response.status == HTTPStatus.OK

    response_json.pop('refresh_token', None)
    response_json.pop('access_token', None)

    assert response_json == {
        'email': first_user.email,
        'flow': 'login',
        'is_2fa_enabled': False,
        'next_url': 'http://localhost:8000/app',
    }

    # Assert apple_id was transferred from first user to second
    async with services.db.acquire() as conn:
        first_user = await select_base_user(conn, email=first_user.email)
        second_user = await select_base_user(conn, email=second_user_email)
        assert first_user.apple_id == TEST_APPLE_TOKEN_PAYLOAD['sub']
        assert second_user is None


@pytest.mark.parametrize(
    'json_data, expected',
    [
        (
            {
                'company_edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'company_name': TEST_COMPANY_NAME,
                'name': TEST_USER_NAME,
                'invited_type': InviteSource.invite_letter,
                'invited_edrpou': '*********',
            },
            {
                'invited_type': InviteSource.invite_letter,
                'invited_edrpou': '*********',
            },
        ),
        (
            {
                'company_edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'company_name': TEST_COMPANY_NAME,
                'name': TEST_USER_NAME,
                'invited_type': InviteSource.document,
                'invited_edrpou': AUTOGENERATED_EDRPOU_1,
            },
            {
                'invited_type': InviteSource.document,
                'invited_edrpou': AUTOGENERATED_EDRPOU_1,
            },
        ),
    ],
)
async def test_add_new_company_with_active_role_with_referral_info(
    aiohttp_client,
    monkeypatch,
    json_data,
    expected,
):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)
    headers = prepare_auth_headers(user)

    # monkeypatch verify function
    sign = prepare_signature_info(SignatureType.signature, user.company_edrpou)
    monkeypatch.setattr(eusign_utils, 'verify_sync', lambda *_, **__: sign)

    # Act
    await client.post(
        ADD_COMPANY_URL,
        json=json_data,
        headers=headers,
    )

    # Assert
    async with app['db'].acquire() as conn:
        meta = await select_company_meta(conn=conn, edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT)

    assert meta.invited_type == expected['invited_type']
    assert meta.invited_edrpou == expected['invited_edrpou']


@pytest.mark.parametrize(
    'company_config, update_data, expected',
    [
        # Update all fields
        (
            {},
            {
                'msViewerEnabled': True,
                'allow_unregistered_document_view': True,
                'render_review_in_interface': True,
                'render_review_on_print_document': True,
                'render_signature_at_page': RenderSignatureAtPage.last.value,
            },
            {
                'msViewerEnabled': True,
                'allow_unregistered_document_view': True,
                'render_review_in_interface': True,
                'render_review_on_print_document': True,
                'render_signature_at_page': RenderSignatureAtPage.last,
            },
        ),
        # Update nothing
        (
            {
                'msViewerEnabled': False,
                'allow_unregistered_document_view': False,
                'render_review_in_interface': False,
                'render_review_on_print_document': False,
                'render_signature_at_page': RenderSignatureAtPage.first.value,
            },
            {},
            {
                'msViewerEnabled': False,
                'allow_unregistered_document_view': False,
                'render_review_in_interface': False,
                'render_review_on_print_document': False,
                'render_signature_at_page': RenderSignatureAtPage.first,
            },
        ),
        # Update specific fields
        (
            {
                'msViewerEnabled': True,
                'allow_unregistered_document_view': False,
                'render_review_in_interface': True,
                'render_review_on_print_document': False,
                'render_signature_at_page': RenderSignatureAtPage.all.value,
                'version_settings': {
                    'review_flow': VersionReviewFlow.continued,
                    'category_config': {
                        'type': 'allow',
                        'categories_ids': [
                            PublicDocumentCategory.sales_invoice.value,
                            PublicDocumentCategory.act.value,
                        ],
                    },
                },
            },
            {
                'render_signature_at_page': RenderSignatureAtPage.last.value,
                'version_settings': {
                    'review_flow': VersionReviewFlow.restarted,
                    'category_config': {
                        'type': 'allow',
                        'category_ids': [],
                    },
                },
            },
            {
                'msViewerEnabled': True,
                'allow_unregistered_document_view': False,
                'render_review_in_interface': True,
                'render_review_on_print_document': False,
                'render_signature_at_page': RenderSignatureAtPage.last,
                'version_settings': VersionSettings(
                    review_flow=VersionReviewFlow.restarted,
                    category_config=VersionSettingsCategoryConfig(
                        type='allow',
                        category_ids=[],
                    ),
                ),
            },
        ),
    ],
)
async def test_update_company_config(
    aiohttp_client,
    company_config,
    update_data,
    expected,
):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    await set_company_config(
        app=app,
        company_id=user.company_id,
        update_admin=False,
        **company_config,
    )

    response = await client.patch(
        path='/internal-api/companies/additional/configs',
        json=update_data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        config = await get_company_config(conn, company_id=user.company_id)

    for key, value in expected.items():
        assert getattr(config, key) == value


@pytest.mark.parametrize('inactivity_timeout', [15 * 60, 60 * 60])
async def test_update_company_secure_permissions(aiohttp_client, inactivity_timeout):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    # Set new values
    domains = ['test.com', 'test2.com']
    ips = ['127.0.0.1']
    api_ips = ['***********']
    response = await client.patch(
        path='/internal-api/companies/security/permissions',
        json={
            'email_domains': domains,
            'allowed_ips': ips,
            'allowed_api_ips': api_ips,
            'inactivity_timeout': inactivity_timeout,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        company = await get_company(conn, company_id=user.company_id)
        assert company.email_domains == domains
        assert company.allowed_ips == ips
        assert company.allowed_api_ips == api_ips
        assert company.inactivity_timeout == inactivity_timeout

    # Set new values
    domains = ['test3.com']
    ips = ['*********']
    api_ips = ['***********']
    response = await client.patch(
        path='/internal-api/companies/security/permissions',
        json={
            'email_domains': domains,
            'allowed_ips': ips,
            'allowed_api_ips': api_ips,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        company = await get_company(conn, company_id=user.company_id)
        assert company.email_domains == domains
        assert company.allowed_ips == ips
        assert company.allowed_api_ips == api_ips
        assert company.inactivity_timeout == inactivity_timeout

    # Update only one field
    domains = ['test.com']
    response = await client.patch(
        path='/internal-api/companies/security/permissions',
        json={
            'email_domains': domains,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        company = await get_company(conn, company_id=user.company_id)
        assert company.email_domains == domains
        assert company.allowed_ips == ips
        assert company.allowed_api_ips == api_ips
        assert company.inactivity_timeout == inactivity_timeout

    # Set empty values e.g. remove
    domains = []
    ips = []
    api_ips = []
    response = await client.patch(
        path='/internal-api/companies/security/permissions',
        json={
            'email_domains': domains,
            'allowed_ips': ips,
            'allowed_api_ips': api_ips,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    # User can't set allowed_ips IPs if there are no user's current IP in the list
    ips = ['*******']
    response = await client.patch(
        path='/internal-api/companies/security/permissions',
        json={
            'allowed_ips': ips,
        },
        headers={**prepare_auth_headers(user), 'X-Real-IP': '*******'},
    )
    assert response.status == HTTPStatus.BAD_REQUEST, await response.json()
    # But with force - it is possible
    ips = ['*******']
    response = await client.patch(
        path='/internal-api/companies/security/permissions',
        json={
            'allowed_ips': ips,
            'force_ip_update': True,
        },
        headers={**prepare_auth_headers(user), 'X-Real-IP': '*******'},
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        company = await get_company(conn, company_id=user.company_id)
        assert company.email_domains == domains
        assert company.allowed_ips == ips
        assert company.allowed_api_ips == api_ips

    # Make sure that we stored user actions
    async with services.events_db.acquire() as conn:
        events = await select_user_actions_for(company_id=user.company_id)
        assert len(events) == 12

    # All actions should be company_update and belong to the same company/user
    for ev in events:
        assert isinstance(ev, UserActionDB)
        assert ev.company_id == user.company_id
        assert ev.email == user.email
        assert ev.action == UserAction.company_update

    def norm_pair(field, value):
        if isinstance(value, list):
            return (field, tuple(value))
        return (field, value)

    actual_pairs = Counter(
        norm_pair(ev.extra.get('changed_field'), ev.extra.get('new_value')) for ev in events
    )

    expected_pairs = Counter(
        {
            norm_pair('email_domains', ['test.com', 'test2.com']),
            norm_pair('allowed_ips', ['127.0.0.1']),
            norm_pair('allowed_api_ips', ['***********']),
            norm_pair('inactivity_timeout', inactivity_timeout),
            norm_pair('email_domains', ['test3.com']),
            norm_pair('allowed_ips', ['*********']),
            norm_pair('allowed_api_ips', ['***********']),
            norm_pair('email_domains', ['test.com']),
            norm_pair('email_domains', []),
            norm_pair('allowed_ips', []),
            norm_pair('allowed_api_ips', []),
            norm_pair('allowed_ips', ['*******']),
        }
    )

    assert actual_pairs == expected_pairs


@pytest.mark.slow
async def test_session_expire(aiohttp_client, test_flags):
    app, client, user = await prepare_client(aiohttp_client, company_inactivity_timeout=1)

    await login(client, user.email, TEST_USER_PASSWORD)
    response = await client.post(
        GRAPHQL_URL, data=GQL_CURRENT_USER, headers=prepare_auth_headers(user)
    )
    assert response.status == HTTPStatus.OK

    await asyncio.sleep(2)

    response = await client.post(
        GRAPHQL_URL,
        data=GQL_CURRENT_USER,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.FORBIDDEN
    assert await response.json() == {
        'code': 'session_expired',
        'reason': 'Сесія закінчилася, будь ласка, авторизуйтесь знову',
        'details': None,
    }


async def test_logout_from_session_id(
    aiohttp_client,
    test_flags,
):
    """
    Login and try to logout using session_id
    """
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)
    headers = prepare_referer_headers(client)
    await login(client, email=user.email, password=TEST_USER_PASSWORD)

    session_id = await services.redis.lindex(get_active_sessions_key_for(user.id), 0)
    session_raw = await services.redis.get(get_session_redis_key(session_id))
    assert session_raw is not None
    session = ujson.loads(session_raw)['session']

    # make sure that is able to use api before logout
    response = await client.post(GRAPHQL_URL, data=GQL_CURRENT_USER, headers=headers)
    assert response.status == 200

    response = await client.post(
        '/auth-api/logout-session',
        json={'session_id': session[PUBLIC_SESSION_ID_SESSION_KEY]},
        headers=headers,
    )
    assert response.status == 200

    # make sure that can't use api after logout
    response = await client.post(GRAPHQL_URL, data=GQL_CURRENT_USER, headers=headers)
    assert response.status == 403

    # Extra checks
    # To validate permissions
    # make sure we get proper session_id
    await login(client, email=user.email, password=TEST_USER_PASSWORD)

    session_id = await services.redis.lindex(get_active_sessions_key_for(user.id), 1)
    session_raw = await services.redis.get(get_session_redis_key(session_id))
    assert session_raw is not None
    session = ujson.loads(session_raw)['session']
    client.session.cookie_jar.clear()

    coworker = await prepare_user_data(
        app,
        company_edrpou=user.company_edrpou,
        email='<EMAIL>',
        user_role=UserRole.user,
    )
    coworker_admin = await prepare_user_data(
        app,
        company_edrpou=user.company_edrpou,
        email='<EMAIL>',
    )
    recipient = await prepare_user_data(
        app, company_edrpou='1234567890', email=TEST_DOCUMENT_EMAIL_RECIPIENT
    )

    # Other user can't logout other user's session
    # user is not connected to the company at all
    await login(client, email=recipient.email, password=TEST_USER_PASSWORD)
    response = await client.post(
        '/auth-api/logout-session',
        json={'session_id': session[PUBLIC_SESSION_ID_SESSION_KEY]},
        headers=prepare_referer_headers(client),
    )
    assert response.status == 403

    # user is coworker
    client.session.cookie_jar.clear()
    await login(client, email=coworker.email, password=TEST_USER_PASSWORD)
    response = await client.post(
        '/auth-api/logout-session',
        json={'session_id': session[PUBLIC_SESSION_ID_SESSION_KEY]},
        headers=prepare_referer_headers(client),
    )
    assert response.status == 403

    # user is admin and is able to do it
    # but can't logout other user's session
    # without user_id
    client.session.cookie_jar.clear()
    await login(client, email=coworker_admin.email, password=TEST_USER_PASSWORD)
    response = await client.post(
        '/auth-api/logout-session',
        json={'session_id': session[PUBLIC_SESSION_ID_SESSION_KEY]},
        headers=prepare_referer_headers(client),
    )
    assert response.status == 403

    # user is admin and is able to do it
    client.session.cookie_jar.clear()
    await login(client, email=coworker_admin.email, password=TEST_USER_PASSWORD)
    response = await client.post(
        '/auth-api/logout-session',
        json={'session_id': session[PUBLIC_SESSION_ID_SESSION_KEY], 'user_id': user.id},
        headers=prepare_referer_headers(client),
    )
    assert response.status == 200


async def test_delete_hrs_role(aiohttp_client):
    """
    Given a HRS role
    When trying to delete the role
    Expected error to raise
    But when HRS role is deleted
    Expected role to delete as well
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    # Arrange hrs role
    role = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        has_hrs_role=True,
    )

    # Act
    response = await client.delete(
        f'/internal-api/roles/{role.role_id}',
        headers=prepare_auth_headers(user),
    )

    # Assert
    data = await response.json()
    assert response.status == HTTPStatus.BAD_REQUEST
    assert data == {
        'code': 'invalid_request',
        'details': {},
        'reason': 'Співробітник має кадрову роль. Видаліть роль із сервісу Вчасно.Кадри',
    }

    # Remove HRS role
    async with services.db.acquire() as conn:
        await update_role(
            conn=conn,
            role_id=role.role_id,
            data={'has_hrs_role': False},
        )

    # Act
    response = await client.delete(
        f'/internal-api/roles/{role.role_id}',
        headers=prepare_auth_headers(user),
    )

    # Assert
    data = await response.json()
    assert response.status == HTTPStatus.NO_CONTENT

    async with services.db.acquire() as conn:
        role = await select_role_by_id(conn, role.role_id)

    assert role.date_deleted
    assert role.status == RoleStatus.deleted


async def test_login_using_vchasno_zvit_credentials(aiohttp_client):
    """
    Given a user, which was imported by Vchasno.Zvit user import process
        See https://vchasno-group.atlassian.net/browse/DOC-7355
    Given that user doesn't have a password set in EDO
    When user tries to login using Vchasno.Zvit credentials
    Expected:
        - User to be able to login with Zvit credentials
        - User password in user_table be updated with bcrypt-hashed password
    """
    # Arrange
    app, client, admin = await prepare_client(aiohttp_client)

    async with services.db.acquire() as conn:
        # Arrange regular user without password set
        user = await insert_user(
            conn=conn,
            data={
                'id': str(uuid.uuid4()),
                'email': '<EMAIL>',
                'password': None,  # Password is None for now
                'is_autogenerated_password': False,
                'first_name': None,
                'second_name': None,
                'last_name': None,
                'phone': None,
                'is_phone_verified': False,
                'email_confirmed': True,
                'registration_completed': False,
                'created_by': None,
                'promo_code': None,
                'trial_auto_enabled': False,
                'pending_referrer_role_id': None,
                'source': RegistrationSource.zvit.value,
                'google_id': None,
                'microsoft_id': None,
                'apple_id': None,
                'registration_method': None,
            },
        )
        assert user.password is None

        # Arrange zvit user with PBKDF2-HMAC-SHA512 password
        await select_one(
            conn=conn,
            query=(
                zvit_users_table.insert()
                .values(
                    {
                        # At this point all zvit users should have original_user_id
                        'id': user.id,
                        'email': user.email,
                        'password': (
                            'AQAAAAIAAYagAAAAED94TS8CspWwNE/AXFX/Baw6m/wMvuBkGy0YewvsH9eOaeNW7gn5w5daKMakUzgo5g=='
                        ),
                        'imported_by_role_id': admin.role_id,
                    }
                )
                .returning(zvit_users_table)
            ),
        )

    # Act - invalid Zvit password
    response = await login(
        client, email=user.email, password='invalid_simple_password&8', ensure_success=False
    )
    assert response.status == 400

    # Act - valid Zvit password
    response = await login(client, email=user.email, password='simple_password&8')

    # Assert there is an active session
    assert response.status == 200
    assert await services.redis.lrange(get_active_sessions_key_for(user.id), 0, -1)

    # Assert graphql works fine
    response = await client.post(
        GRAPHQL_URL, data=GQL_CURRENT_USER, headers=prepare_referer_headers(client)
    )
    assert response.status == 200

    # Assert user password was updated
    async with services.db.acquire() as conn:
        user = await select_base_user(conn=conn, user_id=user.id)
        date_updated_first = user.date_updated

    assert user.password is not None
    # Using MD5 and not bcrypt here since it's test env
    # See app.auth.helpers::90
    assert check_password_hash_md5(user.password, 'simple_password&8') is True

    # Assert user is able to login after password was created
    response = await login(client, email=user.email, password='simple_password&8')
    assert response.status == 200
    assert await services.redis.lrange(get_active_sessions_key_for(user.id), 0, -1)

    # Assert user wasn't updated second time
    async with services.db.acquire() as conn:
        user = await select_base_user(conn=conn, user_id=user.id)
        date_updated_second = user.date_updated

    assert date_updated_first == date_updated_second


async def test_prevent_attempt_to_login_with_zvit_password_if_there_is_edo_password(
    aiohttp_client,
):
    """
    Given a user, which was imported by Vchasno.Zvit user import process
        See https://vchasno-group.atlassian.net/browse/DOC-7355
    Given that user existed in EDO before Zvit import, had EDO password
    When user tries to login using Vchasno.Zvit credentials
    Expected:
        - Error to be raised with an advice to log in using EDO credentials instead
    """

    # Arrange
    app, client, user = await prepare_client(
        aiohttp_client,
        password=TEST_USER_PASSWORD,  # This is EDO password
    )

    some_admin = await prepare_user_data(app=app, email='<EMAIL>')

    async with services.db.acquire() as conn:
        # Arrange zvit user with PBKDF2-HMAC-SHA512 password
        await select_one(
            conn=conn,
            query=(
                zvit_users_table.insert()
                .values(
                    {
                        'id': user.id,
                        'email': user.email,
                        # Arrange Zvit password - simple_password&8
                        'password': (
                            'AQAAAAIAAYagAAAAED94TS8CspWwNE/AXFX/Baw6m/wMvuBkGy0YewvsH9eOaeNW7gn5w5daKMakUzgo5g=='
                        ),
                        'imported_by_role_id': some_admin.role_id,
                    }
                )
                .returning(zvit_users_table)
            ),
        )

    # Attempt to log in with Zvit password
    response = await login(
        client, email=user.email, password='simple_password&8', ensure_success=False
    )
    assert response.status == 400
    data = await response.json()
    assert data == {
        'code': 'invalid_credentials',
        'details': None,
        'reason': 'Будь ласка, авторизуйтесь, використовуючи пароль від Вчасно.ЕДО',
    }

    # Attempt to log in with Vchasno password
    response = await login(client, email=user.email, password=TEST_USER_PASSWORD)
    assert response.status == 200

    # Assert there is an active session
    assert await services.redis.lrange(get_active_sessions_key_for(user.id), 0, -1)

    # Assert graphql works fine
    response = await client.post(
        GRAPHQL_URL, data=GQL_CURRENT_USER, headers=prepare_referer_headers(client)
    )
    assert response.status == 200
