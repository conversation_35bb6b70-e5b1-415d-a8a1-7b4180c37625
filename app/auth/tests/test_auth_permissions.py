from __future__ import annotations

import dataclasses
import unittest
from typing import get_args
from unittest.mock import Mock

from app.auth.constants import (
    HRS_NOT_BILLABLE_PERMISSIONS,
    USER_NOTIFICATIONS_SETTINGS_KEYS,
    USER_NOTIFICATIONS_SETTINGS_KEYS__ADMIN_ENABLED,
    USER_PERMISSIONS,
)
from app.auth.schemas import <PERSON><PERSON>ultRolePermissionsCoworker, DefaultRolePermissionsKey
from app.auth.types import (
    USER_NOTIFICATION_SETTING_KEY,
    USER_PERMISSION_KEY,
    Role,
    RoleDB,
    User,
)
from app.auth.utils import set_default_role_settings
from app.lib.enums import UserRole
from app.profile.validators import UpdateRoleSchema


def get_dataclass_fields(cls) -> set[str]:
    return {f.name for f in dataclasses.fields(cls)}


def get_pydantic_fields(cls) -> set[str]:
    return set(cls.model_fields.keys())


class TestPermissionsListCoverage:
    """
    Check that which of all permissions are covered in different places in the codebase

    The main idea of this test is when you add new permission to the USER_PERMISSIONS list,
    almost all of these tests should fail loudly to remind you to check all of those places.
    Sometimes new permission should be excluded from some of those places, you can add new
    permission to exclude list or add actual handling of this permission in a specific place.
    """

    async def test_literal_type(self):
        # this requires to literal to be available in runtime
        literal_values = get_args(USER_PERMISSION_KEY)
        assert set(USER_PERMISSIONS) == set(literal_values)

    async def test_notification_literal_type(self):
        # this requires to literal to be available in runtime
        literal_values = get_args(USER_NOTIFICATION_SETTING_KEY)
        assert set(USER_NOTIFICATIONS_SETTINGS_KEYS) == set(literal_values)

    async def test_hrs_not_billable(self):
        assert set(HRS_NOT_BILLABLE_PERMISSIONS).issubset(USER_PERMISSIONS)

    async def test_notifications_no_intersection(self):
        # Action: check that there is no intersection between permissions and notification settings
        assert set(USER_PERMISSIONS) & set(USER_NOTIFICATIONS_SETTINGS_KEYS) == set()

    async def test_admin_enabled_notifications(self):
        assert set(USER_NOTIFICATIONS_SETTINGS_KEYS__ADMIN_ENABLED).issubset(
            USER_NOTIFICATIONS_SETTINGS_KEYS
        )

    async def test_user_type(self):
        user_annotations = get_dataclass_fields(User)
        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')

        # Action: add new permission or notification setting to "User" class
        assert settings_keys - user_annotations == set()

    async def test_role_db_type(self):
        user_annotations = get_dataclass_fields(RoleDB)
        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')

        # Action: add new permission or notification setting to "RoleDB" class
        assert settings_keys - user_annotations == set()

    async def test_role_type(self):
        user_annotations = get_dataclass_fields(Role)
        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')

        # Add new permission or notification setting to "Role" class
        assert settings_keys - user_annotations == set()

    async def test_update_user_role_schema(self):
        schema_annotations = get_pydantic_fields(UpdateRoleSchema)
        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')

        # Action: add new key to a right set here if it's not permission or notification setting
        assert settings_keys - schema_annotations == set()

        # Update dict contains more than just settings keys
        assert len(schema_annotations - settings_keys) > 0
        assert 'user_role' in schema_annotations, 'user_role should be in the update_dict'

    async def test_update_role_schema_update_dict(self):
        mock_obj = Mock(return_value=True)
        update_dict = UpdateRoleSchema.to_update_dict(mock_obj)

        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')
        update_keys = set(update_dict.keys())

        # Action: add new key to a right set here if it's not permission or notification setting
        assert settings_keys - update_keys == set()

        # Update dict contains more than just settings keys
        assert len(update_keys - settings_keys) > 0
        assert 'user_role' in update_keys, 'user_role should be in the update_dict'

    async def test_default_role_permissions_key(self):
        fields = get_pydantic_fields(DefaultRolePermissionsKey)
        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')

        # Action: add new key to a right set here if it's not permission or notification setting
        assert fields - settings_keys == {
            # this is only one key that is set in default role settings that is nor boolean
            # permission setting, nor boolean notification setting
            'user_role',
        }

        # Action: add new permission or notification setting to "DefaultRolePermissionsKey"
        assert settings_keys - fields == set()

    async def test_default_role_permissions_coworker(self):
        fields = get_pydantic_fields(DefaultRolePermissionsCoworker)
        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')

        # Action: add new key to a right set here if it's not permission or notification setting
        assert fields - settings_keys == {
            # this is only one key that is set in default role settings that is nor boolean
            # permission setting, nor boolean notification setting
            'user_role',
        }

        # Action: add new permission or notification setting to "DefaultRolePermissionsCoworker"
        assert settings_keys - fields == set()

    async def test_set_default_role_permissions(self):
        insert_dict = {}
        default = DefaultRolePermissionsKey()

        # this function should set all permission from default to some value
        set_default_role_settings(insert_dict, default, invited_by=None)

        settings_keys = set(USER_PERMISSIONS) | set(USER_NOTIFICATIONS_SETTINGS_KEYS)
        # TODO[version2]: remove this
        settings_keys.remove('can_add_document_versions')
        insert_keys = set(insert_dict.keys())

        # Action: add new key to a right set here if it's not permission or notification setting
        assert insert_keys - settings_keys == {
            # this is only one key that is set in default role settings that is nor boolean
            # permission setting, nor boolean notification setting
            'user_role',
        }

        # Action: add setting new permission or notification setting to "set_default_role_settings"
        # function.
        assert settings_keys - insert_keys == set()


def test_set_default_role_settings_with_inviter():
    insert_dict = {
        'can_view_document': False,  # should not be set
        'can_comment_document': True,  # should not be set
    }

    inviter = Mock()

    # Set all permissions to False by dfault
    inviter.role_id = 'inviter_role'
    for permission in USER_PERMISSIONS:
        setattr(inviter, permission, False)
    for notification in USER_NOTIFICATIONS_SETTINGS_KEYS:
        setattr(inviter, notification, False)

    inviter.user_role = UserRole.user
    inviter.can_view_document = True
    inviter.can_comment_document = True
    inviter.can_invite_coworkers = False
    inviter.can_edit_company = True
    inviter.can_view_private_document = True

    # Seal prevention from accessing attributes that are not defined in the mock
    unittest.mock.seal(inviter)

    default_permissions = DefaultRolePermissionsKey(
        user_role=UserRole.admin,
        can_view_document=True,
        can_comment_document=True,
        can_invite_coworkers=True,
        can_edit_company=False,
        can_view_private_document=True,
    )
    set_default_role_settings(insert_dict, default_permissions, invited_by=inviter)

    # We don't set default admin role permission because inviter has a lower role
    assert insert_dict['user_role'] == UserRole.user

    # Initial dict values should be unchanged
    assert insert_dict['can_view_document'] is False
    assert insert_dict['can_comment_document'] is True

    # We don't set can_invite_coworkers to True because inviter doesn't have this permission
    assert insert_dict['can_invite_coworkers'] is False

    # Even if an inviter has can_edit_company permission, we set it to default value
    assert insert_dict['can_edit_company'] is False

    # We set can_view_private_document to True because inviter and default have this permission
    assert insert_dict['can_view_private_document'] is True
