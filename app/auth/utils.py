import asyncio
import ipaddress
import logging
import typing as t
from fnmatch import fnmatch
from secrets import token_urlsafe

import aiohttp_session
import sqlalchemy as sa
from aiohttp import web
from aiohttp_session import Session
from ua_parser import user_agent_parser
from vchasno_crm.errors import VchasnoCRMError
from yarl import URL

from api import errors
from app.auth import concierge, db, helpers, providers, schemas, two_factor
from app.auth.constants import (
    COMPANY_NAME_ABBREVIATION_MAPPING,
    MAX_AGE_SESSION,
    MAX_AGE_SESSION_KEY,
    NEXT_URL_LOGIN_PREFIX,
    ROLE_ID_REQUEST_KEY,
    ROLE_ID_SESSION_KEY,
    SIGN_SESSION_ID_HEADER,
    SIGN_SESSION_ID_REQUEST_KEY,
    USER_APP_KEY,
    USER_ID_SESSION_KEY,
    USER_NOTIFICATIONS_SETTINGS_KEYS,
    USER_NOTIFICATIONS_SETTINGS_KEYS__ADMIN_ENABLED,
    USER_PERMISSIONS,
)
from app.auth.db import (
    insert_user,
    raw_update_user,
    select_companies_by_edrpou,
    select_company_id_by_role_id,
    select_roles,
    select_user,
    update_role,
)
from app.auth.enums import (
    AuthFactor,
    AuthFlow,
    RoleActivationSource,
    RoleStatus,
    StatEntity,
)
from app.auth.helpers import (
    generate_hash_sha512,
)
from app.auth.schemas import CompanyConfig, DefaultRolePermissionsBase
from app.auth.session_manager.types import LastActivity
from app.auth.session_manager.utils import (
    save_last_user_activity,
)
from app.auth.types import (
    USER_NOTIFICATION_SETTING_KEY,
    USER_PERMISSION_KEY,
    AuthUser,
    AuthUserExtended,
    AuthUserResponse,
    BaseUser,
    Company,
    InsertBaseUserDict,
    InsertRoleDict,
    Role,
    RoleDB,
    RolePermissions,
    SyncRolesResults,
    UpdateBaseUserDict,
    UpdateRoleDict,
    User,
    is_wide_user_type,
)
from app.contacts import utils as contacts
from app.crm.utils import send_role_to_crm
from app.events.user_actions import types as user_actions_types
from app.events.user_actions import utils as user_actions_utils
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import (
    regexp,
    session_utils,
)
from app.lib.constants import NO_VALUE, NO_VALUE_TYPE
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import Language, UserRole
from app.lib.helpers import get_client_ip
from app.lib.session_utils import get_session_redis_key
from app.lib.types import DataDict
from app.lib.urls import build_url
from app.lib.validators import is_valid_email, validate_uuid
from app.registration.enums import RegistrationMethod, RegistrationSource
from app.registration.types import AddActiveRoleCtx
from app.services import services
from app.trigger_notifications.utils import TriggerNotificationHowTo
from app.vchasno_profile.utils import create_vchasno_profile_sync
from app.youcontrol.utils import send_sync_company_info_from_youcontrol
from worker import topics

logger = logging.getLogger(__name__)

# Basic service level wrapper for basic database operation
get_user = db.select_user
get_base_user = db.select_base_user
get_company = db.select_company
get_role = db.select_role
get_company_config = db.select_company_config
get_companies_configs_by_edrpous = db.select_companies_configs_by_edrpous
get_companies_configs_by_ids = db.select_companies_configs_by_ids


HRS_ROLE_PERMISSIONS = RolePermissions(
    can_comment_document=True,
    can_sign_and_reject_document=True,
    can_sign_and_reject_document_external=True,
    can_sign_and_reject_document_internal=True,
    can_delete_document=True,
    can_view_document=False,
    can_upload_document=False,
    can_download_document=False,
    can_print_document=False,
    can_invite_coworkers=False,
    can_edit_company=False,
    can_edit_roles=False,
    can_create_tags=False,
    can_edit_document_automation=False,
    can_edit_document_fields=False,
    can_archive_documents=False,
    can_edit_templates=False,
    can_edit_directories=False,
    can_delete_archived_documents=False,
    can_remove_itself_from_approval=False,
    can_view_coworkers=False,
)


async def get_company_configs(
    conn: DBConnection,
    *,
    company_ids: list[str] | None = None,
    company_edrpous: list[str] | None = None,
) -> dict[str, schemas.CompanyConfig]:
    """
    Get configs for multiple companies by their IDs or EDRPOUs.
    returns dict of company_id/company_edrpou -> CompanyConfig
    """

    assert not (company_ids and company_edrpous), 'Use either company_ids or company_edrpous'

    if company_ids:
        return await db.select_companies_configs_by_ids(
            conn=conn,
            companies_ids=company_ids,
        )
    if company_edrpous:
        return await db.select_companies_configs_by_edrpous(
            conn=conn,
            companies_edrpous=company_edrpous,
        )
    return {}


async def get_companies_configs_by_roles_ids(
    conn: DBConnection,
    *,
    roles_ids: list[str],
) -> dict[str, schemas.CompanyConfig]:
    """
    Get configs for multiple companies by their roles IDs (for graph)
    """
    return await db.select_companies_configs_by_roles_ids(
        conn=conn,
        roles_ids=roles_ids,
    )


def get_default_company_config() -> schemas.CompanyConfig:
    return schemas.CompanyConfig()


async def switch_role(request: web.Request, role: Role, user: User) -> None:
    """Activate new role for current session."""
    session = await aiohttp_session.get_session(request)
    store_role_id(session, role.id_)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await user_actions_utils.add_user_action(
            user_action=user_actions_types.UserAction(
                action=user_actions_types.Action.login_to_company,
                source=user_actions_utils.get_event_source(request),
                user_id=user.id,
                phone=user.auth_phone,
                email=user.email,
                company_id=user.company_id,
            )
        )

        await user_actions_utils.add_user_action(
            user_action=user_actions_types.UserAction(
                action=user_actions_types.Action.logout_from_company,
                source=user_actions_utils.get_event_source(request),
                user_id=user.id,
                phone=user.auth_phone,
                email=user.email,
                company_id=user.company_id,
            )
        )


def allow_unathenticated_ua(request: web.Request, *, family: str) -> bool:
    """
    Does current request allowed to be processed with given User-Agent details?

    This is necessary to allow making proxy requests to CA Servers from IE web
    workers.
    """
    user_agent = request.headers['User-Agent']
    parsed = user_agent_parser.ParseUserAgent(user_agent)

    if parsed['family'] == family:
        logger.info(
            'Allow unathenticated user to access page due to User-Agent family match',
            extra={
                'family': family,
                'url': str(request.rel_url),
                'user_agent': user_agent,
            },
        )
        return True

    return False


def build_next_url_key(prefix: str) -> str:
    """Build key for storing next URL in session due to user role."""
    return f'next_key_{prefix}'


def delete_next_url(session: Session, prefix: str) -> None:
    """Delete next URL for given prefix from session."""
    session.pop(build_next_url_key(prefix), None)


def delete_role_id(session: Session) -> None:
    """Delete role ID from session storage."""
    session.pop(ROLE_ID_SESSION_KEY, None)


def delete_user_id(session: Session) -> None:
    """Delete user ID from session storage."""
    session.pop(USER_ID_SESSION_KEY, None)


def get_next_url(session: Session, prefix: str) -> str | None:
    """Get next URL from session if any by given prefix."""
    return session.get(build_next_url_key(prefix))


async def get_next_url_from_request(
    request: web.Request, prefix: str, *, to_delete: bool = True
) -> str:
    """Get next URL to access after some action done by user."""
    session = await aiohttp_session.get_session(request)
    next_url = get_next_url(session, prefix)

    if to_delete:
        delete_next_url(session, prefix)

    return next_url or build_url('app', tail='')


def get_role_id(session: Session) -> str | None:
    """Get active user role ID from session storage."""
    return session.get(ROLE_ID_SESSION_KEY)


async def get_role_id_from_request(request: web.Request, *, session: Session) -> str | None:
    """
    First check for special `rid` param in query, then attempt to get active
    user role ID from session storage.
    """
    role_id = request.rel_url.query.get(ROLE_ID_REQUEST_KEY)
    # If role ID passed in query string - set it to user session, so all next
    # requests reuse it
    if role_id:
        store_role_id(session, role_id)
        return role_id

    # Otherwise use role ID from session if any
    return get_role_id(session)


def get_sign_session_id_from_request(request: web.Request) -> str | None:
    """
    First check for special `ssid` param in query, then attempt to get sign
    session ID from headers.
    """
    value = request.rel_url.query.get(SIGN_SESSION_ID_REQUEST_KEY) or request.headers.get(
        SIGN_SESSION_ID_HEADER
    )
    if not value:
        return None

    valid_value = validate_uuid(value)
    if not valid_value:
        logger.warning('Attempt to use malformed sign session ID', extra={'sign_session_id': value})
        return None

    return valid_value


def get_user_id(session: Session | DataDict) -> str | None:
    """Get user ID from session storage if available."""
    return session.get(USER_ID_SESSION_KEY)


async def get_user_id_from_request(request: web.Request) -> str | None:
    """Get user ID for current request if available."""
    return get_user_id(await aiohttp_session.get_session(request))


def is_active_role(role: Role) -> bool:
    return role.status == RoleStatus.active


def is_admin(user_role: int | None) -> bool:
    """Check whether given user role is admin or not?"""
    return user_role == UserRole.admin.value


def has_direct_permission(user: AuthUser | User | BaseUser, *, permission: str) -> bool:
    """
    Check whether given user has permission enabled or not, without admin checking
    """

    # Check permission in runtime because sometimes it's easier to process untyped dict with
    # permissions instead of manually checking all possible permissions. Mypy won't catch mistake
    # if you pass the wrong permission name, but at least you will get runtime error
    assert permission in USER_PERMISSIONS, f'Invalid permission name "{permission}"'

    return bool(getattr(user, permission, None))


def has_permission(
    user: AuthUser | User | BaseUser,
    required_permissions: set[USER_PERMISSION_KEY],
    all_permissions: bool = True,
) -> bool:
    """
    Check whether given user has all permissions or not?
    """

    # At least one permission is required and all permissions should be valid
    assert required_permissions

    # Admin user can access everything
    if is_admin(getattr(user, 'user_role', None)):
        return True

    if not all_permissions:
        return any(
            has_direct_permission(user, permission=permission)
            for permission in required_permissions
        )

    return all(
        has_direct_permission(user, permission=permission) for permission in required_permissions
    )


def is_admin_user(user: AuthUser | User | BaseUser | None) -> bool:
    """Check whether given user is admin or not?"""
    if not user:
        return False

    # BaseUser -> is not admin
    if not is_wide_user_type(user):
        return False

    return is_admin(user.user_role)


def is_master_company(edrpou: str | None, company_config: CompanyConfig) -> bool:
    """Check whether given company is master account."""
    return bool(edrpou and company_config.master is True)


def is_master_admin(
    user_role: int | None, edrpou: str | None, company_config: CompanyConfig
) -> bool:
    """Check whether given user role is admin and company is master account."""
    return bool(is_admin(user_role) and is_master_company(edrpou, company_config))


def is_super_admin_company(edrpou: str | None, company_config: CompanyConfig) -> bool:
    """
    Check whether given edrpou company is master account and able to act as super admins
    """
    return bool(
        is_master_company(edrpou=edrpou, company_config=company_config)
        and company_config
        and company_config.admin_is_superadmin
    )


def can_user_view_all_company_documents(user: AuthUser | User) -> bool:
    """
    If current user is able to view all documents in the company

    Currently, all documents are split into two distinct groups "private" and "extended".
    If a user has permission to view both groups, we consider that user is able to view all
    documents in the company.
    """

    return has_permission(
        user=user,
        required_permissions={'can_view_document', 'can_view_private_document'},
        all_permissions=True,
    )


def has_super_admin_access(
    user: AuthUser | User | BaseUser | None,
    company_config: CompanyConfig,
    required_permissions: set[str] | None = None,
    super_admin_permissions: set[str] | None = None,
) -> bool:
    """Check whether given user has access to super admin functionality"""
    if not required_permissions or not super_admin_permissions:
        return False

    # we are using "getattr" for correctly handle case when given user
    # is BaseUser or DBRow without role and company details
    company_edrpou: str | None = getattr(user, 'company_edrpou', None)

    return (
        has_super_admin_permissions(required_permissions, super_admin_permissions)
        if is_super_admin_company(edrpou=company_edrpou, company_config=company_config)
        else False
    )


def has_super_admin_permissions(
    required_permissions: set[str] | None = None,
    super_admin_permissions: set[str] | None = None,
) -> bool:
    if not required_permissions or not super_admin_permissions:
        return False

    return not required_permissions.isdisjoint(super_admin_permissions)


async def login_user_base(
    *,
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
    first_factor: AuthFactor,
    ask_second_factor: bool,
    remember: bool = False,
) -> None:
    """
    Log user into current session.

    The main difference between that functions and "login_user" function
    is that "login_user_base" can be used with the registration logic. In those
    cases, we don't need to trigger events about successful login because the
    registration flow has its own events.

    Pay attention that this function creates a new web session for the user, so don't use it
    in the context of server-to-server integration, mobile API, or other cases where
    session management is handled differently.

    Usually remember and second_factor parameters are set to False for registration and
    enabled for direct login.
    """
    from app.auth import two_factor

    # Always ensure that a new session is created on each login to guard
    # against session fixation attacks.
    session = await session_utils.ensure_new_session(request)

    log_extra = {'user_id': user.id}
    logger.info('Login user', extra=log_extra)

    await concierge.sign_in_with_update(conn, user_id=user.id, user=user)

    # Initialize and mutate the session only after the sign-in is complete
    remember_session(session, remember)
    store_user_id(session, user.id)

    # Usually we record the last user activity for every request in the middleware before request
    # will be processed, but in case of login it's important to record the last activity manually
    # to ensure that session will be recorded in the active user sessions list, even if no other
    # requests will be made after login.
    save_last_user_activity(
        session,
        last_activity=LastActivity.from_request(request),
    )
    await session_utils.add_active_user_session(services.redis, user.id, session)

    # Once the session is initialized, proceed to 2FA if needed
    if ask_second_factor:
        await two_factor.start_2fa_web(
            request=request,
            conn=conn,
            user=user,
            first_factor=first_factor,
        )

    await db.upsert_invalid_password_count(conn, user_id=user.id, value=0)

    await update_user_is_logged_once(conn, user=user)


async def update_user_is_logged_once(conn: DBConnection, user: BaseUser) -> None:
    """
    Mark user as registered in the system

    After the first login, we consider the user registered if they were previously marked as
    not registered. For example, we can automatically create a user with an active role and
    then send them an invitation to the company with an autogenerated password. The user can
    use that password to log in to the system without going through the registration process.
    """
    if not user.is_logged_once:
        await update_user(conn, user_id=user.id, data={'is_logged_once': True})


async def logout_user(request: web.Request) -> None:
    """Logout user from current session and wipe entire session data."""
    session = await aiohttp_session.get_session(request)
    session.invalidate()


def match_ip(current: str, rule: str) -> bool:
    """Check whether current IP matched rule or not.

    This is a shortcut on top of ``fnmatch.fnmatch`` function to allow unit
    testing matching IP functionality.
    """

    # handle mask
    # mask doesn't support wildcards
    if len(rule.split('/')) == 2:
        return ipaddress.IPv4Address(current) in ipaddress.IPv4Network(rule)

    return fnmatch(current, rule)


def remember_session(session: Session, remember: bool) -> None:
    """
    Hack to remember user session for three months if he clicked on
    "Remember Me" checkbox on Login Page.
    """
    if remember:
        session[MAX_AGE_SESSION_KEY] = session._max_age = MAX_AGE_SESSION


async def store_next_url(request: web.Request, prefix: str) -> None:
    """Store next URL to session.

    This allow to redirect user back to originally requested page. The key
    stored to session due to role of page required, which means logged in
    regular user will not be redirected to next admin page if he somehow
    request it.
    """
    url = str(request.rel_url)

    # Ignore requesting public or intenral API & downloads URLs
    if url[:5] == '/api/' or url[:11] == '/downloads/' or url[:14] == '/internal-api/':
        return

    session = await aiohttp_session.get_session(request)
    session[build_next_url_key(prefix)] = url


def store_role_id(session: Session, role_id: str) -> None:
    """Store given role ID to session."""
    session[ROLE_ID_SESSION_KEY] = role_id


def store_user_id(session: Session, user_id: str) -> None:
    """Store given user ID to session."""
    session[USER_ID_SESSION_KEY] = user_id


def build_user_full_name(
    first_name: str | None,
    second_name: str | None,
    last_name: str | None,
) -> str:
    """Construct user full name from different parts of user's name"""

    parts_raw = [first_name, second_name, last_name]
    parts = [part for part in parts_raw if part]
    name = ' '.join(parts)
    return name.strip()


def get_user_full_name(user: AuthUserExtended | User) -> str:
    """Build user full name for given user"""

    return build_user_full_name(
        first_name=user.first_name,
        second_name=user.second_name,
        last_name=user.last_name,
    )


def get_short_company_name(name: str) -> str:
    old_name = name
    name = name.lstrip().lower()
    leading_spaces = len(old_name) - len(name)

    for full_name, abbreviation in COMPANY_NAME_ABBREVIATION_MAPPING.items():
        if name.startswith(full_name):
            return abbreviation + old_name[len(full_name) + leading_spaces :]

    return old_name


async def logout_all_sessions(
    conn: DBConnection,
    *,
    user_id: str,
    with_web: bool,
    with_mobile: bool,
    with_concierge: bool,
    web_sessions_exclude: list[str] | None = None,
) -> int:
    counter: int = 0

    # Logout from EDO web app by removing all active sessions
    if with_web:
        exclude_sessions = web_sessions_exclude or []
        session_id_key = session_utils.get_active_sessions_key_for(user_id)
        session_ids = await services.redis.lrange(session_id_key, 0, -1)
        for sid in session_ids:
            if sid in exclude_sessions:
                continue
            await services.redis.delete(get_session_redis_key(sid))

        if not exclude_sessions:
            await services.redis.delete(session_id_key)

        counter += len(session_ids)

    # Logout from mobile app by deleting tokens from the database
    if with_mobile:
        # logout from mobile app
        from app.mobile.auth.utils import process_mobile_logout_all

        mobile_counter = await process_mobile_logout_all(
            conn=conn,
            user_id=user_id,
        )
        counter += mobile_counter

    # Logout from concierge to logout from other products (e.g., EDI or KEP)
    if with_concierge:
        await concierge.sign_out_user_sessions(user_id=user_id)

    return counter


async def sync_listing_job(role_id: str, delay: int = 3) -> None:
    if delay > 0:
        async with services.db.acquire() as conn:
            await services.kafka.add_task(
                topic=topics.CREATE_DOCUMENTS_ROLE_ACCESS,
                delay_min=delay,
                data={'role_id': role_id},
                conn=conn,
            )
        return

    await services.kafka.send_record(
        topics.CREATE_DOCUMENTS_ROLE_ACCESS,
        value={'role_id': role_id},
    )


async def schedule_reindex_contact_recipients_by_contacts_job(contact_edrpou: str) -> None:
    """Schedule an async job to reindex contacts by contact EDRPOU"""
    data = {'contact_edrpou': contact_edrpou, 'reset_lock': True}
    await services.kafka.send_record(topics.REINDEX_CONTACTS_RECIPIENTS_BY_CONTACTS, data)


async def update_company_roles_count(company_id: str) -> None:
    data = {'company_id': company_id, 'entity': StatEntity.role.value}
    await services.kafka.send_record(topics.UPDATE_COMPANY_ENTITY_COUNT, data)


async def update_company_tags_count(company_id: str) -> None:
    data = {'company_id': company_id, 'entity': StatEntity.tag.value}
    await services.kafka.send_record(topics.UPDATE_COMPANY_ENTITY_COUNT, data)


async def update_company_templates_count(company_id: str) -> None:
    data = {'company_id': company_id, 'entity': StatEntity.template.value}
    await services.kafka.send_record(topics.UPDATE_COMPANY_ENTITY_COUNT, data)


async def update_company_document_fields_count(company_id: str) -> None:
    data = {'company_id': company_id, 'entity': StatEntity.document_field.value}
    await services.kafka.send_record(topics.UPDATE_COMPANY_ENTITY_COUNT, data)


async def delete_role(
    conn: DBConnection,
    role: Role,
    status: RoleStatus,
    by_user: User | None,
) -> None:
    from app.tags.db import delete_roles_tags

    async with conn.begin():
        await delete_role_base(
            conn,
            role_id=role.id_,
            role_user_id=role.user_id,
            deleted_by=by_user.role_id if by_user else None,
            status=status,
        )

    # If previously user was active, we should decrease the role count by 1
    if role.status == RoleStatus.active:
        await update_company_roles_count(role.company_id)

        # If deleted last active role in company - reindex
        # contacts with the given edrpou in all companies
        if not await db.select_is_company_registered(conn, edrpou=role.company_edrpou):
            await schedule_reindex_contact_recipients_by_contacts_job(role.company_edrpou)

    # In case when a role is **intentionally** deleted, remove also tags assigned to this role.
    # We have a logic that automatically removes tags from a company when no roles are assigned
    # to them, but if tags are assigned only to deleted roles, it creates dangling tags.
    # Also, keep in mind that we are not deleting tag access to the documents for the role, to
    # keep as much information as possible in unchangeable state and able to restore a role
    # if needed
    if status in (RoleStatus.deleted, RoleStatus.user_deleted):
        await delete_roles_tags(conn, role_id=role.id_)


async def send_jobs_about_new_role(role_id: str, company_id: str) -> None:
    await send_role_to_crm(role_id)
    await sync_listing_job(role_id)
    await update_company_roles_count(company_id)
    await generate_esputnik_employees_limit_reached(company_id)

    # Trigger notifications
    trigger_notification = TriggerNotificationHowTo()
    await trigger_notification.send(roles_ids=[role_id])


async def update_has_signed_documents_for_role(role_id: str | None) -> None:
    if not role_id:
        msg = 'Can not update has_signed_documents for role'
        logger.warning(msg=msg, extra={'role_id': role_id})

    await services.kafka.send_record(
        topic=topics.UPDATE_HAS_ROLE_SIGNED_DOCUMENTS, value={'role_id': role_id}
    )


def generate_xsrf_token(secret: str, auth_cookie: str) -> str:
    salt = token_urlsafe(16)
    hash_ = generate_hash_sha512(f'{secret}:{auth_cookie}:{salt}')
    return f'{salt}:{hash_}'


def check_xsrf_token(secret: str, auth_cookie: str, token: str) -> bool:
    if not token:
        return False
    salt, hash_ = token.split(':')
    pattern = f'{secret}:{auth_cookie}:{salt}'
    pattern_old = f'{secret}:{salt}'
    return hash_ in (
        generate_hash_sha512(pattern),
        generate_hash_sha512(pattern_old),  # TODO: delete
    )


async def update_invite_trigger_notification(role_id: str | None) -> None:
    if not role_id:
        return

    await services.kafka.send_record(
        topic=topics.UPDATE_NOTIFICATION_INVITE_COMPANIES,
        value={'role_id': role_id},
    )


async def block_account(conn: DBConnection, user: BaseUser) -> None:
    """Logout all user's sessions and set him an autogenerated password."""

    await logout_all_sessions(
        conn=conn,
        user_id=user.id,
        with_web=True,
        with_mobile=True,
        with_concierge=True,
    )
    await db.delete_user_tokens(conn, user.id)
    await db.reset_password(conn, user_id=user.id)
    logger.info('User was blocked', extra={'user_id': user.id, 'email': user.email})


async def ban_user(conn: DBConnection, *, user_id: str, is_banned: bool, reason: str) -> None:
    """
    Update user is_banned status in the database.
    """
    logger.info(
        msg='Update user is_banned status',
        extra={'user_id': user_id, 'is_banned': is_banned, 'reason': reason},
    )
    await update_user(
        conn=conn,
        user_id=user_id,
        data={'is_banned': is_banned},
    )


async def count_invalid_password_attempts(
    conn: DBConnection,
    user: BaseUser,
) -> None:
    count = await db.select_invalid_password_count(conn, user_id=user.id)
    if count == 100:
        await block_account(conn, user)
        raise errors.Error(
            code=errors.Code.too_many_request,
            reason=_(
                'Ви перевищили ліміт спроб вводу пароля. Ваш обліковий запис '
                'було заблоковано. Для відновлення доступу відновіть пароль.'
            ),
        )

    await db.upsert_invalid_password_count(conn, user_id=user.id, value=count + 1)


def is_fop(edrpou: str) -> bool:
    return bool(regexp.individual_company_re.match(edrpou))


async def create_company(conn: DBConnection, data: DataDict, ignore_existing: bool = True) -> str:
    """
    Create company and all required additional entities
    """

    async with conn.begin():
        company_id = await db.insert_company(
            conn=conn,
            data=data,
            ignore_existing=ignore_existing,
        )

        await contacts.add_company_to_contact_recipients_indexation(
            conn=conn,
            company_id=company_id,
        )

    await send_sync_company_info_from_youcontrol(
        conn=conn,
        edrpou=data['edrpou'],
        # Active role might be created after company.
        # Try to wait until role will be added.
        delay_min=1,
    )

    return company_id


async def generate_esputnik_employees_limit_reached(company_id: str) -> None:
    await services.kafka.send_record(
        topics.ESPUTNIK_GENERATE_EMPLOYEES_LIMIT_REACHED_EVENT,
        {'company_id': company_id},
    )


async def create_autogenerated_coworker(
    conn: DBConnection,
    edrpou: str,
    email: str,
    password: str,
    first_name: str | None,
    second_name: str | None,
    last_name: str | None,
    phone: str | None,
    is_phone_verified: bool,
    position: str | None,
    created_by: User,
) -> User:
    """
    Create coworker with autogenerated password and active role
    """
    created_by_role_id = created_by.role_id
    logger.info(
        msg='Create new coworker with autogenerated password',
        extra={
            'created_by': created_by_role_id,
            'email': email,
            'first_name': first_name,
            'second_name': second_name,
            'last_name': last_name,
            'phone': phone,
            'is_phone_verified': is_phone_verified,
            'email_confirmed': True,
            'registration_completed': True,
        },
    )

    async with conn.begin():
        company_id = await create_company(
            conn=conn,
            data={'edrpou': edrpou},
            ignore_existing=True,
        )

        new_user = await create_autogenerated_user(
            conn=conn,
            created_by=created_by_role_id,
            email=email,
            password=password,
            first_name=first_name,
            second_name=second_name,
            last_name=last_name,
            phone=phone,
            is_phone_verified=is_phone_verified,
            is_email_confirmed=True,
            # Because we create a user with an active role on the next step, we already create
            # a user with completed registration (at least one role is active)
            is_registration_completed=True,
        )

        user_id = new_user.id

        # TAG: role_activation
        role = await create_coworker_role(
            conn=conn,
            data={
                'company_id': company_id,
                'user_id': user_id,
                'show_invite_tooltip': True,
                'date_agreed': sa.text('now()'),
                'status': RoleStatus.active,
                'position': position,
                # We consider this action as "invitation" to the company because later we
                'invited_by': created_by_role_id,
                'date_invited': sa.text('now()'),
                'activated_by': created_by_role_id,
                'date_activated': sa.text('now()'),
                'activation_source': RoleActivationSource.invite,
            },
            invited_by=created_by,
        )

        # Update user instance with last role ID to use
        await update_user(
            conn=conn,
            user_id=user_id,
            data={'last_role_id': role.id},
        )

        await contacts.add_role_to_contact_recipients_indexation(conn, role_id=role.id)

    # NOTE: do not schedule async jobs here, because that function can be called inside
    # transaction and an async job can be executed before transaction commits. Use
    # "send_jobs_about_autogenerated_user" function instead

    row = await select_user(
        conn=conn,
        user_id=user_id,
        role_id=role.id,
    )
    assert row, 'User should be created'
    return row


async def create_coworker_role(
    conn: DBConnection,
    data: InsertRoleDict,
    invited_by: User | None,
) -> RoleDB:
    """
    Insert a new role created by a coworker by inviting the user to the company.

    For roles that are registered by the user themselves by signing the company token with a key,
    use "activate_role" function, which sets different defaults than this function.
    """

    company_id = data['company_id']
    assert company_id, 'Company ID is required'

    config = await get_company_config(conn, company_id=company_id)
    set_default_role_settings(
        data=data,
        default=config.default_role_permissions,
        invited_by=invited_by,
    )

    data.setdefault('date_updated', sa.text('now()'))

    row = await db.insert_role(conn, data=data)

    return RoleDB.from_row(row)


async def create_coworker_roles(
    conn: DBConnection,
    *,
    data: list[InsertRoleDict],
    invited_by: User,
) -> list[DBRow]:
    """
    Insert multiple roles created by coworker by inviting users to the company
    """
    company_id = invited_by.company_id
    config = await get_company_config(conn, company_id=company_id)
    for item in data:
        assert item['company_id'] == company_id, 'Company ID in role must be equal to company_id'
        set_default_role_settings(
            data=item,
            default=config.default_role_permissions,
            invited_by=invited_by,
        )

    return await db.insert_roles(conn, data=data)


async def delete_role_base(
    conn: DBConnection,
    *,
    role_id: str,
    role_user_id: str,
    deleted_by: str | None = None,
    status: RoleStatus,
) -> None:
    # TAG: role_deactivation
    async with conn.begin():
        await update_role(
            conn=conn,
            role_id=role_id,
            data={
                # Deleted statuses: "deleted", "user_deleted", "rate_deleted"
                'status': status,
                'is_default_recipient': False,
                'date_deleted': sa.text('now()'),
                'deleted_by': deleted_by,
            },
        )
        await contacts.add_role_to_contact_recipients_indexation(conn, role_id=role_id)

        # If deleted last active role - move to initial confirmation step,
        # and kill all sessions
        if not await db.exist_roles(conn, user_id=role_user_id):
            await update_user(
                conn=conn,
                user_id=role_user_id,
                data={'registration_completed': False},
            )
            # logout all sessions related to DOC-7281 issue
            await logout_all_sessions(
                conn=conn,
                user_id=role_user_id,
                with_web=True,
                with_mobile=True,
                with_concierge=True,
            )

    # send to crm
    await send_role_to_crm(role_id)


async def google_auth(
    request: web.Request,
    conn: DBConnection,
    raw_token: str,
    registration_source: RegistrationSource | None = None,
    invite_email: str | None = None,
) -> AuthUserResponse:
    """
    Login/register user by JWT token from Google Sign In button.

    It's OK to have request and response objects everywhere on the service
    level to simplify logic that depends on that objects.

    This function has WEB specific logic, like "login_user_base", so be careful to use it
     in the context of mobile API or server-to-server integrations.
    """
    from app.registration import utils as registration

    token = await providers.google.verify_oauth2_token(token=raw_token)

    ask_second_factor = True
    if registration_source == RegistrationSource.after_invite.value:
        if not invite_email or invite_email.lower() != token.email.lower():
            raise errors.GoogleAuthorisationError(code=errors.Code.invalid_email_provided)

        # TODO: understand why introduced that check and remove it because it allow to bypass
        # 2FA verification if malicious user will have access to google account of target user.
        # https://vchasno-group.atlassian.net/browse/DOC-5926
        ask_second_factor = False

    # Log in by Google ID from token
    google_id = token.sub
    user = await db.select_base_user(conn, google_id=google_id)
    if user:
        response = await login_user(
            request=request,
            conn=conn,
            user=user,
            remember=True,
            first_factor=AuthFactor.email,
            ask_second_factor=ask_second_factor,
        )
        return response

    # Log in by email from token
    google_email = token.email
    user = await db.select_base_user(conn, email=google_email)
    if user:
        # Add Google ID to user profile
        update_data: UpdateBaseUserDict = {
            'google_id': google_id,
            'email_confirmed': True,
        }
        if registration_source == RegistrationSource.after_invite.value:
            update_data['registration_method'] = RegistrationMethod.google
        await update_user(conn=conn, user_id=user.id, data=update_data)
        response = await login_user(
            request=request,
            conn=conn,
            user=user,
            remember=True,
            ask_second_factor=ask_second_factor,
            first_factor=AuthFactor.email,
        )
        return response

    return await registration.google_registration(
        request=request,
        conn=conn,
        token=token,
    )


async def microsoft_auth(
    request: web.Request,
    conn: DBConnection,
    raw_access_token: str,
    raw_id_token: str,
    registration_source: RegistrationSource | None = None,
    invite_email: str | None = None,
) -> AuthUserResponse:
    """
    Login/register user by JWT token from Microsoft Sign In button.

    It's OK to have request and response objects everywhere on the service
    level to simplify logic that depends on that objects.
    """
    from app.registration import utils as registration

    token = await providers.microsoft.verify_oauth2_token(
        access_token=raw_access_token,
        id_token=raw_id_token,
    )

    ask_second_factor = True
    if registration_source == RegistrationSource.after_invite.value:
        if not invite_email or not token.email or invite_email.lower() != token.email.lower():
            raise errors.MicrosoftAuthorisationError(code=errors.Code.invalid_email_provided)
        ask_second_factor = False

    # Log in by Microsoft ID from token
    microsoft_id = token.id
    user = await db.select_base_user(conn, microsoft_id=microsoft_id)
    if user:
        response = await login_user(
            request=request,
            conn=conn,
            user=user,
            remember=True,
            first_factor=AuthFactor.email,
            ask_second_factor=ask_second_factor,
        )
        return response

    # Log in by email from token
    if microsoft_email := token.email:
        user = await db.select_base_user(conn, email=microsoft_email)
        if user:
            # Add Microsoft ID to user profile
            update_data: UpdateBaseUserDict = {
                'microsoft_id': microsoft_id,
                'email_confirmed': True,
            }
            if registration_source == RegistrationSource.after_invite.value:
                update_data['registration_method'] = RegistrationMethod.microsoft
            await update_user(conn=conn, user_id=user.id, data=update_data)
            response = await login_user(
                request=request,
                conn=conn,
                user=user,
                remember=True,
                ask_second_factor=ask_second_factor,
                first_factor=AuthFactor.email,
            )
            return response

    return await registration.microsoft_registration(
        request=request,
        conn=conn,
        token=token,
    )


async def login_user_registration(
    *,
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
    first_factor: AuthFactor,
) -> None:
    """
    Login user into current session just right after registration is done
    """
    return await login_user_base(
        request=request,
        conn=conn,
        user=user,
        remember=False,
        first_factor=first_factor,
        # we don't ask for second factor on registration because we should call this function
        # for cases when user is not registered yet, so he can't have 2FA enabled right after
        # registration
        ask_second_factor=False,
    )


async def login_user(
    *,
    request: web.Request,
    conn: DBConnection,
    user: BaseUser,
    first_factor: AuthFactor,
    ask_second_factor: bool,
    remember: bool = False,
) -> AuthUserResponse:
    """
    Log user into current session. Send events about successfully login and
    generate response for frontend
    """
    from app.auth.validators import validate_user_not_banned

    validate_user_not_banned(request, user)

    await login_user_base(
        request=request,
        conn=conn,
        user=user,
        remember=remember,
        first_factor=first_factor,
        ask_second_factor=ask_second_factor,
    )

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        company_id = None
        if user.last_role_id:
            company_id = await select_company_id_by_role_id(conn, user.last_role_id)
        await user_actions_utils.add_user_action(
            user_action=user_actions_types.UserAction(
                action=user_actions_types.Action.login_successful,
                source=user_actions_utils.get_event_source(request),
                user_id=user.id,
                phone=user.auth_phone,
                email=user.email,
                company_id=company_id,
                extra={
                    'ip': get_client_ip(request),
                    'user_agent': request.headers.get('User-Agent'),
                },
            )
        )

    await update_invite_trigger_notification(role_id=user.last_role_id)

    # Generate response object
    next_url = await get_next_url_from_request(request, NEXT_URL_LOGIN_PREFIX)

    session = await aiohttp_session.get_session(request)
    is_2fa_enabled = two_factor.is_pending_2fa_web(session)

    if get_flag(FeatureFlags.ENABLE_ROLES_SYNC_ON_LOGIN):
        await try_to_syncronously_sync_user_roles(
            request_source=user_actions_utils.get_event_source(request),
            user=user,
        )

    return AuthUserResponse(
        flow=AuthFlow.login,
        email=user.email,
        next_url=next_url,
        is_2fa_enabled=is_2fa_enabled,
    )


async def get_expected_base_user(
    conn: DBConnection,
    *,
    email: str | None = None,
    user_id: str | None = None,
    only_with_email: bool = True,
) -> BaseUser:
    """
    Select base user in context where we're expecting that user will exist.
    For example after insert into database or after validation
    """
    user = await db.select_base_user(
        conn=conn,
        email=email,
        user_id=user_id,
        only_with_email=only_with_email,
    )
    assert user, 'User is expected'
    return user


async def get_expected_user(
    conn: DBConnection,
    user_id: str | None = None,
    email: str | None = None,
    role_id: str | None = None,
    company_id: str | None = None,
    company_edrpou: str | None = None,
    use_active_filter: bool = True,
    is_legal: bool = True,
) -> User:
    """
    Select user in context where we're expecting that user will exist.
    For example after insert into database or after validation
    """
    user = await db.select_user(
        conn=conn,
        user_id=user_id,
        email=email,
        role_id=role_id,
        company_id=company_id,
        company_edrpou=company_edrpou,
        use_active_filter=use_active_filter,
        is_legal=is_legal,
    )
    assert user, 'User is expected'
    return user


async def update_user_email_confirmed(
    conn: DBConnection,
    *,
    user_id: str,
    is_email_confirmed: bool,
) -> BaseUser:
    # TODO: update here
    await update_user(
        conn=conn,
        user_id=user_id,
        data={'email_confirmed': is_email_confirmed},
    )
    user = await get_expected_base_user(conn, user_id=user_id)
    return user


async def update_user_phone(
    conn: DBConnection,
    user_id: str,
    *,
    phone: str | None,
    auth_phone: str | None,
    is_phone_verified: bool,
    telegram_chat_id: str | None,
    is_2fa_enabled: bool | NO_VALUE_TYPE = NO_VALUE,
) -> None:
    """
    Update phone and related fields for user
    """

    data: UpdateBaseUserDict = {
        'phone': phone,
        'auth_phone': auth_phone,
        'telegram_chat_id': telegram_chat_id,
        'is_phone_verified': is_phone_verified,
    }
    if not isinstance(is_2fa_enabled, NO_VALUE_TYPE):
        data['is_2fa_enabled'] = is_2fa_enabled

    await update_user(
        conn=conn,
        user_id=user_id,
        data=data,
    )


async def update_user_language(
    conn: DBConnection,
    user_id: str,
    language: Language,
) -> None:
    """Update preferred language for user."""

    await update_user(
        conn=conn,
        user_id=user_id,
        data={'language': language},
    )


async def update_company_by_edrpou(
    conn: DBConnection,
    edrpou: str,
    data: DataDict,
) -> None | Company:
    """
    Update a company by edrpou and return an updated company or None if company
    """
    async with conn.begin():
        company = await db.update_company_by_edrpou(
            conn=conn,
            edrpou=edrpou,
            data=data,
        )
        if not company:
            return None

        await contacts.add_company_to_contact_recipients_indexation(
            conn=conn,
            company_id=company.id,
        )

    return company


async def create_autogenerated_user(
    conn: DBConnection,
    *,
    email: str,
    is_email_confirmed: bool,
    password: str | None = None,
    is_registration_completed: bool,
    created_by: str | None = None,
    first_name: str | None = None,
    second_name: str | None = None,
    last_name: str | None = None,
    phone: str | None = None,
    is_phone_verified: bool = False,
    source: RegistrationSource | None = None,
) -> BaseUser:
    """
    Create a new user with autogenerated password

    Why do we need to autogenerate password at all? Sometimes we need to create a user in the
    database before user registered in the system. However, currently a registration process is
    not adapted to correctly work when a user exists in a database, even if we create this user
    automatically for our internal needs. We show user an error message that a user already
    exists, even if the user has never registered in the system. As a workaround, we generate a
    random password, set a flag that this password is autogenerated and send a user an email with
    a password link to set a new password.

    Probably a better solution in the long run would be to refactor a registration process and
    distinguish between "registered" and "autogenerated" users.
    """

    if not password:
        password = helpers.autogenerate_password()

    return await create_user(
        conn=conn,
        email=email,
        password=password,
        first_name=first_name,
        second_name=second_name,
        last_name=last_name,
        phone=phone,
        auth_phone=None,
        is_phone_verified=is_phone_verified,
        is_email_confirmed=is_email_confirmed,
        is_registration_completed=is_registration_completed,
        is_autogenerated_password=True,
        source=source,
        created_by=created_by,
        promo_code=None,
        trial_auto_enabled=False,
        pending_referrer_role_id=None,
        google_id=None,
        microsoft_id=None,
        apple_id=None,
        registration_method=None,
        # todo: make it true and remove password autogeneration, system should handle properly
        # preallocated users
        is_placeholder=False,
    )


async def create_user(
    conn: DBConnection,
    *,
    email: str | None,
    is_email_confirmed: bool,
    is_registration_completed: bool,
    password: str | None,
    is_autogenerated_password: bool,
    created_by: str | None,
    first_name: str | None,
    second_name: str | None,
    last_name: str | None,
    promo_code: str | None,
    trial_auto_enabled: bool,
    pending_referrer_role_id: str | None,
    phone: str | None,
    auth_phone: str | None,
    is_phone_verified: bool,
    source: RegistrationSource | None,
    google_id: str | None,
    microsoft_id: str | None,
    apple_id: str | None,
    registration_method: RegistrationMethod | None,
    is_placeholder: bool,
) -> BaseUser:
    """
    Insert a new User instance into a database.

    After insertion is done, return new user data from DB via additional hit.

    WARN: Consider to use "insert_autogenerated_user" or "create_user_on_registration" which have
    better typing and default values.
    """

    assert email or auth_phone, 'Email or phone is required'

    # Usually, we validate the email before creating a user, but there are several invalid cases
    # in the database that weren't validated earlier. Because of that, we validate the email here
    # to prevent creating a user with an invalid email and to fix these cases.
    if email and not is_valid_email(email):
        logger.warning('Invalid email provided', extra={'email': email})
        raise AssertionError('Invalid email provided')

    async with conn.begin():
        # Sometimes we can have already preallocated user in the database, which we still consider
        # as not created or not registered user. In this case, we update existing user with new data
        # and return it
        assert email or auth_phone, 'Email or phone is required'
        user: BaseUser | None = None
        if email:
            user = await db.select_base_user(
                conn=conn,
                email=email,
                exclude_placeholder=False,
                only_with_email=True,
            )
        elif auth_phone:
            user = await db.select_base_user(
                conn=conn,
                auth_phone=auth_phone,
                exclude_placeholder=False,
                only_with_email=False,
            )

        common_data: UpdateBaseUserDict = {
            'is_placeholder': is_placeholder,
            'is_autogenerated_password': is_autogenerated_password,
            'first_name': first_name,
            'second_name': second_name,
            'last_name': last_name,
            'phone': phone,
            'auth_phone': auth_phone,
            'is_phone_verified': is_phone_verified,
            'email_confirmed': is_email_confirmed,
            'registration_completed': is_registration_completed,
            'created_by': created_by,
            'promo_code': promo_code,
            'trial_auto_enabled': trial_auto_enabled,
            'pending_referrer_role_id': pending_referrer_role_id,
            'source': source.value if source else None,
            'google_id': google_id,
            'microsoft_id': microsoft_id,
            'apple_id': apple_id,
            'registration_method': registration_method,
            'date_created': utc_now(),
        }
        if user:
            update_data: UpdateBaseUserDict = {
                'new_password': password,
                **common_data,
            }
            user = await raw_update_user(conn=conn, user_id=user.id, data=update_data)
            await create_vchasno_profile_sync(conn, user_id=user.id)

        else:
            user_data: InsertBaseUserDict = {
                'email': email,
                'password': password,
                **common_data,  # type: ignore[typeddict-item]
            }
            user = await insert_user(conn=conn, data=user_data)
            await create_vchasno_profile_sync(conn, user_id=user.id)

        return user


async def update_user(
    conn: DBConnection,
    user_id: str,
    data: UpdateBaseUserDict,
) -> BaseUser:
    """
    Update user table and keep vchasno profile user data in sync
    """
    async with conn.begin():
        # Make vchasno profile sync request only if fields that are used in vchasno profile are
        # changed. This is needed to avoid unnecessary requests to other vchasno services
        if (
            'email' in data
            or 'first_name' in data
            or 'second_name' in data
            or 'last_name' in data
            or 'auth_phone' in data
            or 'date_deleted' in data
        ):
            await create_vchasno_profile_sync(conn, user_id=user_id)

        user = await db.raw_update_user(
            conn=conn,
            user_id=user_id,
            data=data,
        )

    return user


def is_user_registered(
    is_logged_once: bool,
    email: str | None,
    is_email_confirmed: bool,
    auth_phone: str | None,
    is_registration_completed: bool,
) -> bool:
    """
    Check whether the user is fully registered in the system.

    We use different flags to control the various stages of the registration process. In a basic
    scenario, the user registers in the system, confirms their email, and completes registration
    by adding at least one active role. However, in certain cases, such as when a coworker
    invites the user to the service, we might assign an active role before the registration is
    fully completed (is_registration_completed=True). Similarly, we might mark the email as
    trusted before the user completes the registration process (is_email_confirmed=True).

    Essentially, all combinations of these flags are possible. A user is considered fully
    registered when all flags are set to True.
    """

    # - is_logged_once — user logged in at least once in the system
    # - is_email_confirmed — user confirmed their email address
    # - is_registration_completed — user has at least one active role
    return (
        is_logged_once
        and ((bool(email) and is_email_confirmed) or bool(auth_phone))
        and is_registration_completed
    )


def set_default_role_settings(
    data: InsertRoleDict,
    default: DefaultRolePermissionsBase,
    invited_by: User | None,
) -> None:
    """
    Set default role permissions and notifications for a new role based on company config

    IF a user is invited by another user, provide "invited_by" user to avoid setting higher
    default permissions than the user who invited has.
    """

    # This is the only one key in default settings that are not boolean value, so we have
    # separate handling for it
    if 'user_role' not in data:
        if invited_by and invited_by.user_role != UserRole.admin:
            data['user_role'] = UserRole.user  # the lowest role
        else:
            data['user_role'] = default.user_role

    # Notification settings historically stored alongside permissions
    _is_admin = is_admin(data['user_role'])
    for notification_key in USER_NOTIFICATIONS_SETTINGS_KEYS:
        _notification_key = t.cast(USER_NOTIFICATION_SETTING_KEY, notification_key)
        # For admins, we enable some notifications by default, so they don't miss important
        # events. They can always disable them later if they don't want to receive them.
        if _is_admin and _notification_key in USER_NOTIFICATIONS_SETTINGS_KEYS__ADMIN_ENABLED:
            data.setdefault(_notification_key, True)
        else:
            data.setdefault(_notification_key, getattr(default, notification_key))

    for permission_key in USER_PERMISSIONS:
        _permission: USER_PERMISSION_KEY = t.cast(USER_PERMISSION_KEY, permission_key)
        if _permission not in data:
            # TODO[version2]: remove this
            if _permission == 'can_add_document_versions':
                continue
            if invited_by and not getattr(invited_by, _permission):
                # If user that inviting doesn't have some permission, then we won't set it for
                # invited user as well. This should prevent a situation from getting higher
                # permissions via invitation of another email address that he or she controls.
                data[_permission] = False
            else:
                data[_permission] = getattr(default, _permission)


def get_request_url(request: web.Request) -> URL:
    """
    Temporary workaround to upgrade vchasno_crm >= 0.3.5 which depends on yarl >= 1.18.0.
    yarl > 1.12.0 breaks aiohttp==3.10.0.
    aiohttp >= 3.10.7 breaks our CI/CD runner.
    This function is a workaround to upgrade yarl without changing aiohttp version.

    TODO: Remove this function and replace the code below,
     after upgrading aiohttp version and fixing runner:
    ```
    request_url = get_request_url(request)
    =>
    request_url = request.url
    ```
    """
    url = URL.build(
        scheme=request.scheme,
        authority=request.host,
    )
    return url.join(request.rel_url)


async def sync_user_roles(
    request_source: user_actions_types.Source,
    user: BaseUser,
) -> SyncRolesResults:
    """
    Sync user roles from Vchasno CRM
    """
    from app.registration.utils import (
        add_active_role,
        save_event_about_new_active_role,
        send_jobs_about_new_active_role,
    )
    from app.registration.validators import validate_add_role_to_company

    if not services.crm_client:
        logger.info('CRM client is not configured')
        return SyncRolesResults(new_roles=[], errors={})

    # Try to get information about user from CRM
    try:
        info = await services.crm_client.get_user_info(
            email=user.email,
            vchasno_id=user.id,
        )
    except VchasnoCRMError:
        info = None

    if not info or not info.roles:
        logger.info('No roles found in CRM', extra={'user_id': user.id})
        return SyncRolesResults(new_roles=[], errors={})

    # Filter only roles where user has used the signature key (kasa, kep, etc.)
    roles_with_keys = [role for role in info.roles if role.role.with_signature_key]
    if not roles_with_keys:
        logger.info('No roles which have used signature key', extra={'user_id': user.id})
        return SyncRolesResults(new_roles=[], errors={})

    async with services.db.acquire() as conn:
        existing_roles = await select_roles(conn=conn, user_id=user.id)
        existing_companies = await select_companies_by_edrpou(
            conn=conn,
            edrpous=[role.company.edrpou for role in roles_with_keys],
        )

        edrpous_where_user_already_exists = {role.company_edrpou for role in existing_roles}
        existing_companies_map = {company.edrpou: company for company in existing_companies}

        failed_to_create_roles: dict[str, errors.Error] = {}
        created_roles: dict[str, RoleDB] = {}

        # Iterate over roles which should be added to the service
        for role in roles_with_keys:
            # If role already exists - skip role
            if role.company.edrpou in edrpous_where_user_already_exists:
                continue

            # If company already exists - check if role could be added
            if company := existing_companies_map.get(role.company.edrpou):
                try:
                    await validate_add_role_to_company(
                        conn=conn,
                        user=user,
                        company=company,
                        send_notification_to_admins=False,
                    )
                except errors.Error as e:
                    failed_to_create_roles[role.company.edrpou] = e
                    continue

            # Add a role to the system
            ctx = AddActiveRoleCtx(
                user=user,
                activation_source=RoleActivationSource.sync_role,
                activated_by=None,
                edrpou=role.company.edrpou,
                company_name=role.company.name,
                last_name=info.user.surname,
                first_name=info.user.given_name,
                second_name=info.user.middle_name,
                phone=info.user.mobile_phone,
                position=role.role.position,
                signature_info=None,
            )
            output = await add_active_role(conn, ctx)

            # Intiate jobs about new active role
            await send_jobs_about_new_active_role(
                user_id=user.id,
                user_email=user.email,
                role_id=output.role.id,
                company_id=output.role.company_id,
                company_edrpou=ctx.edrpou,
                company_name=ctx.company_name,
                is_company_registered=output.is_company_registered,
            )
            await save_event_about_new_active_role(
                request_source=request_source,
                user=user,
                company_id=output.role.company_id,
                signature_info=None,
            )

            created_roles[output.company.edrpou] = output.role

    logger.info(
        'Sync roles result',
        extra={
            'user_id': user.id,
            'created_role_ids': [role.id for role in created_roles.values()],
            'failed_to_create_roles': [
                {
                    'edrpou': edrpou,
                    'reason': str(reason),
                }
                for edrpou, reason in failed_to_create_roles.items()
            ],
        },
    )

    return SyncRolesResults(
        new_roles=[role.id for role in created_roles.values()],
        errors=failed_to_create_roles,
    )


async def try_to_syncronously_sync_user_roles(
    request_source: user_actions_types.Source,
    user: BaseUser,
) -> None:
    try:
        async with asyncio.timeout(services.config.app.sync_roles_timeout):
            await sync_user_roles(request_source=request_source, user=user)
    except TimeoutError:
        logger.info(
            'Unable to syncronously sync roles for a user. Intitiating an async job.',
            extra={'user_id': user.id},
        )
        await services.kafka.send_record(
            topic=topics.SYNC_USER_ROLES,
            value={'request_source': request_source.value, 'user_id': user.id},
        )


async def reset_role_to_default_hrs(conn: DBConnection, role: Role | DBRow) -> None:
    """
    Update role permissions to be default HRS.
    """

    await update_role(
        conn=conn,
        role_id=role.id,
        data=UpdateRoleDict(
            can_comment_document=HRS_ROLE_PERMISSIONS.can_comment_document,
            can_sign_and_reject_document=HRS_ROLE_PERMISSIONS.can_sign_and_reject_document,
            can_sign_and_reject_document_external=HRS_ROLE_PERMISSIONS.can_sign_and_reject_document_external,
            can_sign_and_reject_document_internal=HRS_ROLE_PERMISSIONS.can_sign_and_reject_document_internal,
            can_delete_document=HRS_ROLE_PERMISSIONS.can_delete_document,
            can_view_document=HRS_ROLE_PERMISSIONS.can_view_document,
            can_upload_document=HRS_ROLE_PERMISSIONS.can_upload_document,
            can_download_document=HRS_ROLE_PERMISSIONS.can_download_document,
            can_print_document=HRS_ROLE_PERMISSIONS.can_print_document,
            can_invite_coworkers=HRS_ROLE_PERMISSIONS.can_invite_coworkers,
            can_edit_company=HRS_ROLE_PERMISSIONS.can_edit_company,
            can_edit_roles=HRS_ROLE_PERMISSIONS.can_edit_roles,
            can_create_tags=HRS_ROLE_PERMISSIONS.can_create_tags,
            can_edit_document_automation=HRS_ROLE_PERMISSIONS.can_edit_document_automation,
            can_edit_document_fields=HRS_ROLE_PERMISSIONS.can_edit_document_fields,
            can_archive_documents=HRS_ROLE_PERMISSIONS.can_archive_documents,
            can_edit_templates=HRS_ROLE_PERMISSIONS.can_edit_templates,
            can_edit_directories=HRS_ROLE_PERMISSIONS.can_edit_directories,
            can_delete_archived_documents=HRS_ROLE_PERMISSIONS.can_delete_archived_documents,
            can_remove_itself_from_approval=HRS_ROLE_PERMISSIONS.can_remove_itself_from_approval,
            can_view_coworkers=HRS_ROLE_PERMISSIONS.can_view_coworkers,
            is_counted_in_billing_limit=False,
        ),
    )
    logger.info(
        'Reset user role to default HRS ones',
        extra={'role_id': role.id},
    )


def get_request_user(request: web.Request) -> AuthUser | BaseUser | User | None:
    return request[USER_APP_KEY]
