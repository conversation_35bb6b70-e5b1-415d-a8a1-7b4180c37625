import base64
import datetime
import logging
from collections import defaultdict
from typing import cast

from aiohttp import web
from aiohttp.web_request import <PERSON><PERSON><PERSON>
from py_eusign import eusign

from api.downloads.archives import prepare_p7s_file
from api.downloads.types import to_document
from api.downloads.utils import download_document_content
from api.errors import (
    InvalidRequest,
)
from app.auth.db import (
    increment_company_upload_documents_left,
    select_company_by_edrpou,
    select_role_by_id,
    select_user,
    select_users,
)
from app.auth.enums import RoleActivationSource
from app.auth.types import AuthUser, AuthUserExtended, Role, User
from app.auth.utils import (
    get_short_company_name,
    get_user_full_name,
    update_company_by_edrpou,
    update_has_signed_documents_for_role,
)
from app.billing.api import charge_document
from app.billing.types import ChargeDocumentContext
from app.contacts import utils as contacts
from app.contacts.db import update_main_recipient
from app.contacts.types import ContactDetails, to_contact_details
from app.contacts.utils import unhide_contact_emails
from app.crm.utils import send_company_to_crm
from app.documents import utils as documents
from app.documents.db import (
    update_document,
    update_document_date_delivered_none,
    update_recipients_unset_date_delivered,
    update_recipients_unset_date_received,
)
from app.documents.enums import DocumentSource, FirstSignBy
from app.documents.types import (
    Document,
    DocumentWithUploader,
    UpdateDocumentDict,
    UpdateSignersDataSignerEntity,
    UpdateSignersDataSignerType,
)
from app.documents.utils import (
    get_asic_s3_key,
    get_external_key_signature_s3_key,
    get_external_stamp_signature_s3_key,
    get_internal_signature_s3_key,
    get_xml_to_pdf_key,
    is_first_sign_by_recipient_document,
    replace_bilateral_recipient_email,
    schedule_jobs_about_finished_document,
    send_document_status_callback_job,
)
from app.drafts.utils import delete_drafts
from app.events import document_actions
from app.flow import utils as flow_utils
from app.flow.types import UpdateFlowOnSignOutput
from app.flow.utils import set_flow_unfinished_status
from app.groups.db import select_group, select_group_members
from app.i18n import _
from app.lib import eusign_utils, s3_utils, validators
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import to_local_datetime, utc_now
from app.lib.enums import (
    DocumentStatus,
    SignatureFormat,
    SignersSource,
    Source,
)
from app.lib.eusign_utils import (
    MAX_SIGNATURE_WRAPS,
    build_container_with_multiple_signatures,
    is_internal_signature_sync,
)
from app.lib.helpers import (
    not_none,
    run_sync,
)
from app.lib.locks import redis_lock
from app.lib.types import DataDict
from app.profile.utils import generate_esputnik_first_incoming_signing_event
from app.registration.utils import activate_role
from app.services import services
from app.sign_sessions.enums import SignSessionDocumentStatus
from app.sign_sessions.utils import update_sign_session_document_status
from app.signatures import db
from app.signatures.db import (
    insert_signature,
    select_signatures,
)
from app.signatures.enums import SignatureAlgo, SignatureSource
from app.signatures.types import (
    AddSignatureResultCtx,
    AddSignatureValidationCtx,
    DocumentSignerWithEdrpou,
    P7SContent,
    Signature,
    SignerId,
)
from app.signatures.validators import (
    validate_add_signature,
    validate_signature_json_data,
)
from worker import topics

logger = logging.getLogger(__name__)

exists_document_signers = db.exists_document_signers


# Too generic name for FOP that we update as soon as we have a signature with better
# company name for it. For example, user can register a company with one key that has
# "ФІЗИЧНА ОСОБА" as a name, and then sign a document with another key that has a better
# name for the company.
UPDATABLE_BAD_COMPANY_NAMES = ('ФІЗИЧНА ОСОБА', 'Фізична особа')


async def send_next_recipient_notification_on_sign(
    *,
    update_flow_output: UpdateFlowOnSignOutput | None,
    document: Document,
    source: Source,
) -> None:
    """
    Send notification to the next recipient (not internal document signers).

    Currently, only multilateral documents send a document to the next recipient on sign. Internal
    documents don't have recipients at all, and bilateral document due to historical reasons
    has separate action "send" to send a document to the recipient.
    """

    if document.is_multilateral:
        # probably, it is an extra sign
        if not update_flow_output:
            logger.info(
                msg='Multilateral document without sign, skip notification',
                extra={'document': document},
            )
            return

        # If the current flow is finished after this sign, we should send a document to
        # the next recipients.
        # This function handles correctly case where there are no more next recipients.
        if update_flow_output.updated_flow.pending_signatures_count == 0:
            await flow_utils.send_multilateral_documents_to_recipients_job(
                documents_ids=[document.id],
                source=source,
            )


async def send_updated_company_to_crm_on_sign(
    user_company_id: str | None,
    is_company_updated: bool,
) -> None:
    """
    Send company data to CRM on sign if the company name was updated
    """
    if is_company_updated and user_company_id:
        await send_company_to_crm(user_company_id)


async def send_next_signer_notifications_on_sign(
    *,
    conn: DBConnection,
    user: AuthUserExtended | User | None,
    next_signer: DocumentSignerWithEdrpou | None,
    document: Document,
    update_flow_output: UpdateFlowOnSignOutput | None,
) -> None:
    """
    Send notification to the next document signer in current company
    """

    if not next_signer:
        return

    recipient_ids = []
    group_name = None
    if next_signer.role_id:
        recipient_ids.append(next_signer.role_id)
    elif next_signer.group_id:
        members = await select_group_members(
            conn=conn,
            group_ids=[next_signer.group_id],
        )
        recipient_ids.extend(m.role_id for m in members)

        group = await select_group(conn=conn, id=next_signer.group_id)
        if group:
            group_name = group.name

    recipients = await select_users(
        conn=conn,
        roles_ids=recipient_ids,
    )

    assigner_user: User | None = None
    if next_signer.assigner:
        assigner_user = await select_user(
            conn=conn,
            role_id=next_signer.assigner,
        )

    should_send: bool = False

    # For internal and bilateral document, when "next_signer" is provided, then
    # we can safely send notification to that signer
    if document.is_internal or document.is_bilateral:
        should_send = True

    # For multilateral document, send notification to the next signer
    # only when we know that process not finished yet and there are
    # next signers to sign.
    if document.is_multilateral:
        updated_flow = update_flow_output and update_flow_output.updated_flow
        if updated_flow and updated_flow.pending_signatures_count > 0:
            should_send = True

    if should_send:
        await send_notification_to_next_signer_on_sign(
            user=assigner_user or user,
            recipients=recipients,
            document=document,
            group_name=group_name,
        )


async def schedule_jobs_about_finished_document_on_sign(
    document: DocumentWithUploader,
    next_status: DocumentStatus | None,
    update_flow_output: UpdateFlowOnSignOutput | None,
) -> None:
    """
    Schedule jobs about a finished document, like notifications or callback to EDI
    """

    should_send: bool = False

    # For internal and bilateral documents we know ahead next status of the document
    if document.is_internal or document.is_bilateral:
        should_send = next_status == DocumentStatus.finished

    # For multilateral documents, we know that document status was changed
    # only after we updated flow status
    if document.is_multilateral and update_flow_output:
        should_send = update_flow_output.document_status_new == DocumentStatus.finished

    if should_send:
        await schedule_jobs_about_finished_document(document)


async def send_notification_to_next_signer_on_sign(
    user: AuthUserExtended | User | None,
    recipients: list[User],
    document: Document,
    *,
    group_name: str | None = None,
) -> None:
    """Sending notification email for next signer"""

    if user is None or not recipients:
        return

    # prepare data for sending to kafka
    documents_ids = [document.id]
    coworker = {'name': get_user_full_name(user), 'email': user.email}
    signers = [
        {
            'name': get_user_full_name(signer),
            'email': signer.email,
            'telegram_chat_id': signer.telegram_chat_id,
            'role_id': signer.role_id,
            'company_id': signer.company_id,
            'company_name': signer.company_name,
            'company_edrpou': signer.company_edrpou,
            'can_receive_inbox': signer.can_receive_inbox,
            'can_receive_notifications': signer.can_receive_notifications,
            'group_name': group_name,
        }
        for signer in recipients
    ]

    await services.kafka.send_record(
        topics.SEND_NOTIFICATION_TO_SIGNERS,
        value={
            'documents_ids': documents_ids,
            'more_documents_count': 0,
            'coworker': coworker,
            'signers': signers,
        },
    )


async def _upload_signature_to_s3(
    key: str | None,
    stamp: str | None,
    container: bytes | None,
    signature: Signature,
    document_id: str,
    signature_algo: SignatureAlgo | None,
) -> None:
    """
    Upload signature to S3 storage

    NOTE: use this function before or inside a database transaction that inserts a signature.
    Never use it after the transaction is committed or you can end up with a signature in the
    database and no corresponding file in S3.
    """
    pending_uploads: list[s3_utils.UploadFile] = []

    if container is not None:
        # Internal signatures are passed as "container" parameter
        if signature_algo == SignatureAlgo.ECDSA:
            key = get_asic_s3_key(document_id)
        else:
            # Regular internal signature
            key = get_internal_signature_s3_key(
                document_id=document_id,
                signature_id=signature.id,
            )

        file = s3_utils.UploadFile(
            key=key,
            body=container,
        )
        pending_uploads.append(file)

    else:
        # External signatures are passed as "key" and "stamp" parameters
        if signature.key_exists:
            assert key is not None, 'Key is required for signature that claims to have key'

            key_s3 = get_external_key_signature_s3_key(
                document_id=document_id,
                signature_id=signature.id,
            )
            key_content = base64.b64decode(key)
            file = s3_utils.UploadFile(
                key=key_s3,
                body=key_content,
            )
            pending_uploads.append(file)

        if signature.stamp_exists:
            assert stamp is not None, 'Stamp is required for signature that claims to have stamp'
            stamp_s3 = get_external_stamp_signature_s3_key(
                document_id=document_id,
                signature_id=signature.id,
            )
            stamp_content = base64.b64decode(stamp)
            file = s3_utils.UploadFile(
                key=stamp_s3,
                body=stamp_content,
            )
            pending_uploads.append(file)

    await s3_utils.upload_batch(pending_uploads)


async def _update_contacts_on_sign(
    conn: DBConnection,
    user: AuthUserExtended | User | None,
    document: Document,
    is_owner_signature: bool,
    is_recipients_hidden: bool,
    owner_user: User | None,
    owner_company: DBRow,
) -> None:
    if not user:
        return

    if not user.email:
        return

    is_3p = is_first_sign_by_recipient_document(document)

    # We assume that auth_user always has edrpou and email
    assert user.company_edrpou, 'Expected AuthUser with edrpou'

    user_contact_details = ContactDetails(
        edrpou=user.company_edrpou,
        email=user.email,
        phone=user.phone,
        first_name=user.first_name,
        second_name=user.second_name,
        last_name=user.last_name,
        company_name=user.company_name,
        is_email_hidden=False,
    )

    # First sign by owner (standard flow). Owner signature
    if not is_3p and is_owner_signature:
        # Make document recipient main contact for current user
        recipient_email: str | None = document.email_recipient
        if recipient_email:
            await update_main_recipient(
                conn=conn,
                company_id=user.company_id,
                details=ContactDetails(
                    edrpou=not_none(document.edrpou_recipient),
                    email=recipient_email,  # todo: split email and use list of emails
                    is_email_hidden=is_recipients_hidden,
                ),
            )

    # First sign by owner (standard flow). Recipient signature
    elif not is_3p:
        # Make document owner main contact for current user
        if owner_user:
            await update_main_recipient(
                conn=conn,
                company_id=user.company_id,
                details=to_contact_details(owner_user),
            )

        # As well as make current user main contact for document
        # owner
        await update_main_recipient(
            conn=conn,
            company_id=owner_company.id,
            details=user_contact_details,
        )

        # Make visible recipient email for document owner
        await unhide_contact_emails(
            conn=conn,
            contact_edrpou=user.company_edrpou,
            contact_email=user.email,
            company_id=owner_company.id,
        )

    # First sign by recipient. Owner signature
    elif is_3p and is_owner_signature:
        # Make current user main contact for document recipient

        # UPD: Code below is actually doesn't work.
        # Function update_main_recipient() do nothing if user ContactUser
        # doesn't have company_id (None in this case).
        # I left this code here for future reference, if someone will
        # try to fix this bug.

        # contact_user = ContactUser(None, None, document.edrpou_recipient)
        # await update_main_recipient(conn=conn, user=contact_user, details=user_contact_details)
        pass

    # First sign by recipient. Recipient signature
    elif is_3p:
        # Make current user main contact for document owner
        await update_main_recipient(
            conn=conn,
            company_id=owner_company.id,
            details=user_contact_details,
        )

        # Make document owner main contact for current user
        if owner_user:
            await update_main_recipient(
                conn=conn,
                company_id=user.company_id,
                details=to_contact_details(owner_user),
            )

        # Make visible recipient email for document owner
        await unhide_contact_emails(
            conn=conn,
            contact_edrpou=user.company_edrpou,
            contact_email=user.email,
            company_id=owner_company.id,
        )


async def _update_document_invalid_signed_status(
    conn: DBConnection,
    document: Document,
) -> None:
    """
    Set document.is_invalid_signed to False
    if every EDRPOU has additional_signatures_count after 14.12.2022 >= invalid_signatures_count

    14.12.2022 - the date when we restricted the creation of invalid signatures
    """

    if not document.is_invalid_signed:
        return

    threshold_date = to_local_datetime(datetime.datetime(2022, 12, 14, 23, 59, 59))

    signatures = await select_signatures(conn, [document.id])
    invalid_key_sign_count_by_edrpou: dict[str, int] = defaultdict(int)
    invalid_stamp_sign_count_by_edrpou: dict[str, int] = defaultdict(int)
    additional_key_sign_count_by_edrpou: dict[str, int] = defaultdict(int)
    additional_stamp_sign_count_by_edrpou: dict[str, int] = defaultdict(int)

    for sign in signatures:
        edrpou = sign.key_owner_edrpou or sign.stamp_owner_edrpou
        if not edrpou:
            continue

        if sign.is_valid is False:
            if sign.key_owner_edrpou:
                invalid_key_sign_count_by_edrpou[edrpou] += 1

            if sign.stamp_owner_edrpou:
                invalid_stamp_sign_count_by_edrpou[edrpou] += 1

            continue

        if sign.key_timemark and sign.key_timemark > threshold_date:
            additional_key_sign_count_by_edrpou[edrpou] += 1
        if sign.stamp_timemark and sign.stamp_timemark > threshold_date:
            additional_stamp_sign_count_by_edrpou[edrpou] += 1

    is_keys_fixed = False
    for edrpou, invalid_key_count in invalid_key_sign_count_by_edrpou.items():
        additional_key_sign_count = additional_key_sign_count_by_edrpou.get(edrpou, 0)
        if additional_key_sign_count < invalid_key_count:
            break
    else:
        is_keys_fixed = True

    is_stamps_fixed = False
    for edrpou, invalid_stamp_count in invalid_stamp_sign_count_by_edrpou.items():
        additional_stamp_sign_count = additional_stamp_sign_count_by_edrpou.get(edrpou, 0)
        if additional_stamp_sign_count < invalid_stamp_count:
            break
    else:
        is_stamps_fixed = True

    if is_keys_fixed and is_stamps_fixed:
        await update_document(
            conn,
            {
                'document_id': document.id,
                'is_invalid_signed': False,
            },
        )


async def _charge_document_on_sign(
    conn: DBConnection,
    user: AuthUser | User | None,
    charge_context: ChargeDocumentContext | None,
    uploader_company_id: str | None,
    is_first_document_sign: bool,
    document: Document,
) -> None:
    """
    Charge a document on sign using validated charge context.
    """
    if not uploader_company_id:
        # TODO: refactor if uploader always provided
        logger.warning('Uploader_company_id missed during adding signature')

    if not user or not user.company_id:
        return

    # Bilateral can also have valid charge context, but we don't charge them on "send" action.
    # Only internal documents are charged on sign.
    if not (document.is_internal and is_first_document_sign):
        return

    # Sometimes an internal document can be free and context in this case is None
    if not charge_context:
        return

    await charge_document(conn, charge_context)
    await increment_company_upload_documents_left(
        conn=conn,
        company_id=user.company_id,
        payer_id=charge_context.payer_id,
        uploader_company_id=uploader_company_id,
    )


async def _unhide_recipients_on_sign(
    conn: DBConnection,
    is_recipients_hidden: bool,
    is_owner_signature: bool,
    user: AuthUser | User | None,
    document_id: str,
) -> None:
    if not (
        (user and user.company_edrpou and user.email)
        and is_recipients_hidden
        and not is_owner_signature
    ):
        return

    # Make hidden recipient visible for document owner.
    await replace_bilateral_recipient_email(
        conn=conn,
        document_id=document_id,
        edrpou=user.company_edrpou,
        new_emails=[user.email],
        is_emails_hidden=False,
    )


async def _activate_signer_role(
    conn: DBConnection, role: Role | None, company: DBRow | None
) -> None:
    if not role:
        logger.warning('Signer does not have any role')
        return

    if role.is_pending:
        # TODO: remove have separate method that activate role by signing registration token
        logger.warning('Activate role during sign', extra={'role': str(role)})
        await activate_role(
            conn=conn,
            user_id=role.user_id,
            company=company,  # type: ignore[arg-type]
            activated_by=None,
            activation_source=RoleActivationSource.signature,
            with_signature_key=False,  # TODO: should be True?
            position=None,
        )
        await contacts.add_role_to_contact_recipients_indexation(conn, role_id=role.id)


async def update_recipient_dates_on_sign(
    conn: DBConnection,
    document: Document,
    company_edrpou: str,
    is_owner_signature: bool,
) -> None:
    """
    Update date_delivered & date_received during sign
    """

    # When the recipient signs a bilateral document first, we need to reset the date
    # delivered in legacy column "documents.date_delivered" to show the recipient that
    # document is opened by the owner after recipient sign and sent this document back.
    # Check app/documents/README.md for details about this legacy column.
    if not is_owner_signature and document.first_sign_by == FirstSignBy.recipient:
        await update_document_date_delivered_none(conn, document_id=document.id)

    date_signed = utc_now()
    await update_recipients_unset_date_delivered(
        conn=conn,
        document_id=document.id,
        company_edrpou=company_edrpou,
        date_delivered=date_signed,
    )
    await update_recipients_unset_date_received(
        conn=conn,
        document_id=document.id,
        company_edrpou=company_edrpou,
        date_received=date_signed,
    )


async def _clean_document_visualisation(document: Document) -> None:
    # Cleanup previously stored PDF file for XML document
    if not document.s3_xml_to_pdf_key:
        return

    key = get_xml_to_pdf_key(document.id, document.s3_xml_to_pdf_key)
    await s3_utils.delete(key)


def prepare_signature_container(p7s: FileField | None) -> bytes | None:
    if not p7s:
        return None

    if p7s.file.tell():
        p7s.file.seek(0)

    return p7s.file.read()


def get_signature_source_from_request(request: web.Request) -> SignatureSource:
    """
    Parse request path and determine signature source from it
    """
    if request.path.startswith('/mobile-api'):
        return SignatureSource.mobile
    return SignatureSource.web


async def get_add_signature_request_data(request: web.Request) -> DataDict:
    data: DataDict = dict(await validators.validate_post_request(request))
    document_id = request.match_info['document_id']

    source = get_signature_source_from_request(request)
    return {
        **data,
        **validate_signature_json_data(data),
        'document_id': document_id,
        'p7s': await run_sync(func=prepare_signature_container, p7s=data.get('p7s')),
        'source': source.value,
    }


async def send_add_signature_event(
    *,
    document_id: str,
    document_source: DocumentSource,
    signature_id: str,
    signature_edrpou: str,
    vendor_id: str | None = None,
) -> None:
    await services.kafka.send_record(
        topic=topics.DOCUMENT_SIGNATURE_ADDED_EVENT,
        value={
            'document_id': document_id,
            'vendor_id': vendor_id,
            'document_source': document_source.value,
            'signature_id': signature_id,
            'signature_edrpou': signature_edrpou,
        },
    )


async def send_documents_rejected_event(
    *,
    initiator_role_id: str | None,
    document_ids: list[str],
    comment: str | None,
) -> None:
    await services.kafka.send_record(
        topic=topics.DOCUMENTS_REJECTED_EVENT,
        value={
            'initiator_role_id': initiator_role_id,
            'document_ids': document_ids,
            'comment': comment,
        },
    )


async def _process_signature_for_edi_document(
    conn: DBConnection,
    ctx: AddSignatureValidationCtx,
) -> AddSignatureValidationCtx:
    """
    Process first signature for Document from EDI.
    EDI documents always have document.signature_format = 'internal_wrapped' and
    we don't want to change that format on receiving first signature in
    format = 'external_separated'.
    """
    document = ctx.document
    data = ctx.data

    if not document.source or not document.source.is_from_edi:
        return ctx

    if not data.is_first_document_signing:
        return ctx

    # Currently covers case only for external_separated signatures
    if data.format != SignatureFormat.external_separated:
        return ctx

    if document.signature_format != SignatureFormat.internal_wrapped:
        return ctx

    # convert signature and set appropriate signature_format
    key = prepare_p7s_file(not_none(data.key))
    stamp_str = data.stamp
    stamp = prepare_p7s_file(stamp_str) if stamp_str is not None else None

    original = await download_document_content(
        conn=conn,
        document=to_document(document),
        version_id=ctx.document_version_id,
    )

    data.key = None
    data.stamp = None
    data.format = document.signature_format
    data.is_internal = True
    data.p7s = build_container_with_multiple_signatures(
        original_file_content=original, signatures=list(filter(None, [key, stamp]))
    )

    return AddSignatureValidationCtx(
        document=document,
        data=data,
        next_signer=ctx.next_signer,
        document_version_id=ctx.document_version_id,
        group_ids=ctx.group_ids,
        charge_context=None,  # EDI documents are free
        user=ctx.user,
    )


async def add_signature_schedule_async_jobs(
    conn: DBConnection,
    ctx: AddSignatureResultCtx,
    request_source: Source,
) -> None:
    if ctx.signer_role:
        # We can't log anonymous signatures
        await document_actions.add_document_action(
            document_action=document_actions.DocumentAction(
                action=document_actions.Action.document_sign,
                document_id=ctx.document_with_uploader.id,
                document_edrpou_owner=ctx.document_with_uploader.edrpou_owner,
                document_title=ctx.document_with_uploader.title,
                company_id=ctx.signer_role.company_id,
                company_edrpou=ctx.signer_role.company_edrpou,
                email=ctx.signer_role.user_email,
                role_id=ctx.validation_ctx.user_role_id,
            )
        )

        if ctx.signer_role.has_few_signatures is True:
            await services.kafka.send_record(
                topic=topics.RECALCULATE_SIGNATURES_COUNT_FOR_USEFUL_META,
                value={'role_id': ctx.signer_role.id_},
            )

    await send_updated_company_to_crm_on_sign(
        user_company_id=ctx.validation_ctx.user_company_id,
        is_company_updated=ctx.is_company_updated,
    )

    await update_has_signed_documents_for_role(role_id=ctx.validation_ctx.user_role_id)

    await send_next_signer_notifications_on_sign(
        conn=conn,
        user=ctx.validation_ctx.user,
        next_signer=ctx.validation_ctx.next_signer,
        document=ctx.document_with_uploader,
        update_flow_output=ctx.update_flow_output,
    )

    await send_next_recipient_notification_on_sign(
        document=ctx.document_with_uploader,
        source=request_source,
        update_flow_output=ctx.update_flow_output,
    )

    await schedule_jobs_about_finished_document_on_sign(
        document=ctx.document_with_uploader,
        next_status=ctx.validation_ctx.next_status,
        update_flow_output=ctx.update_flow_output,
    )

    await send_document_status_callback_job(
        document_id=ctx.document_with_uploader.id,
        uploaded_by_edrpou=ctx.document_with_uploader.uploaded_by_edrpou,
    )

    await send_add_signature_event(
        document_id=ctx.document_with_uploader.id,
        document_source=ctx.document_with_uploader.source,
        signature_id=ctx.signature_id,
        signature_edrpou=ctx.validation_ctx.signature_edrpou,
        vendor_id=ctx.document_with_uploader.vendor_id,
    )

    await generate_esputnik_first_incoming_signing_event(
        ctx.validation_ctx.user,
        ctx.document_with_uploader,
    )


async def add_signature(
    conn: DBConnection,
    user: AuthUserExtended | User | None,
    data: DataDict,
    sign_session_id: str | None = None,
) -> AddSignatureResultCtx:
    """
    Add signature to the document.

    Warning: maybe you want to use in pair with `add_signature_schedule_async_jobs`
    to schedule async jobs after signature added.
    It is separated to have more control over async jobs scheduling,
    for example, to call it outside outer transaction.
    """

    # Lock the process to prevent race condition when applying signatures
    async with redis_lock(
        f'lock_document_signing_{data["document_id"]}',
        timeout=60,
    ):
        ctx = await validate_add_signature(
            conn=conn,
            user=user,
            data=data,
            sign_session_id=sign_session_id,
        )

        # Apply specific rules for adding the first signature to EDI documents
        ctx = await _process_signature_for_edi_document(conn, ctx)

        document = ctx.document
        schema = ctx.data
        is_recipients_hidden = schema.is_recipient_emails_hidden
        is_owner_signature = schema.is_owner_signature
        is_first_document_sign = schema.is_first_document_signing

        logger.info(
            msg='Adding new signature',
            extra={
                'data': str(
                    {
                        'document_id': document.id,
                        'role_id': ctx.user and ctx.user.role_id,
                        'user_email': ctx.user and ctx.user.email,
                        'user_edrpou': ctx.user and ctx.user.company_edrpou,
                        'acsk': schema.acsk,
                        'format': schema.format and schema.format.value,
                        'is_recipient_emails_hidden': is_recipients_hidden,
                        'uploader_company_id': schema.uploader_company_id,
                        'is_first_document_signing': is_first_document_sign,
                        'is_internal': schema.is_internal,
                        'user_role_id': schema.user_role_id,
                        'user_id': schema.user_id,
                        'key_acsk': schema.key_acsk,
                        'key_timemark': schema.key_timemark,
                        'key_serial_number': schema.key_serial_number,
                        'key_company_fullname': schema.key_company_fullname,
                        'key_owner_edrpou': schema.key_owner_edrpou,
                        'key_owner_position': schema.key_owner_position,
                        'key_owner_fullname': schema.key_owner_fullname,
                        'key_is_legal': schema.key_is_legal,
                        'key_power_type': schema.key_power_type and schema.key_power_type.value,
                        'key_certificate_power_type': schema.key_certificate_power_type
                        and schema.key_certificate_power_type.value,
                        'stamp_acsk': schema.stamp_acsk,
                        'stamp_timemark': schema.stamp_timemark,
                        'stamp_serial_number': schema.stamp_serial_number,
                        'stamp_company_fullname': schema.stamp_company_fullname,
                        'stamp_owner_edrpou': schema.stamp_owner_edrpou,
                        'stamp_owner_position': schema.stamp_owner_position,
                        'stamp_owner_fullname': schema.stamp_owner_fullname,
                        'stamp_is_legal': schema.stamp_is_legal,
                        'stamp_power_type': schema.stamp_power_type
                        and schema.stamp_power_type.value,
                        'stamp_certificate_power_type': schema.stamp_certificate_power_type
                        and schema.stamp_certificate_power_type.value,
                        'is_owner_signature': is_owner_signature,
                        'is_recipient_signature': schema.is_recipient_signature,
                        'next_status_id': schema.next_status_id,
                    }
                )
            },
        )

        signer_role = await select_role_by_id(conn, ctx.user_role_id)
        signer_company = await select_company_by_edrpou(conn, ctx.signature_edrpou)

        owner_user = await documents.get_document_owner(conn, document=document)
        owner_company = await select_company_by_edrpou(conn, document.edrpou_owner)

        await _clean_document_visualisation(document)

        async with conn.begin():
            # Insert signature and update document data if signed by owner
            signature = await insert_signature(conn, schema.model_dump(), group_ids=ctx.group_ids)
            signature_id = signature.id

            await _activate_signer_role(conn=conn, role=signer_role, company=signer_company)

            await _upload_signature_to_s3(
                key=schema.key,
                stamp=schema.stamp,
                signature=signature,
                container=ctx.signature_container,
                document_id=document.id,
                signature_algo=ctx.signature_algo,
            )

            await _update_contacts_on_sign(
                conn=conn,
                document=document,
                user=ctx.user,
                is_owner_signature=is_owner_signature,
                is_recipients_hidden=is_recipients_hidden,
                owner_user=owner_user,
                owner_company=owner_company,  # type: ignore[arg-type]
            )

            await _unhide_recipients_on_sign(
                conn=conn,
                document_id=document.id,
                user=ctx.user,
                is_owner_signature=is_owner_signature,
                is_recipients_hidden=is_recipients_hidden,
            )

            await update_recipient_dates_on_sign(
                conn=conn,
                document=document,
                company_edrpou=ctx.signature_edrpou,
                is_owner_signature=is_owner_signature,
            )

            await update_sign_session_document_status(
                conn=conn,
                sign_session_id=sign_session_id,
                next_status=SignSessionDocumentStatus.signed,
            )

            await _charge_document_on_sign(
                conn=conn,
                document=document,
                user=ctx.user,
                charge_context=ctx.charge_context,
                uploader_company_id=schema.uploader_company_id,
                is_first_document_sign=is_first_document_sign,
            )

            is_company_updated = await update_company_name_on_sign(
                conn=conn,
                user=ctx.user,
                new_name=schema.key_company_fullname,
            )

            update_flow_output = await flow_utils.update_flow_on_sign(
                conn=conn,
                document=document,
                flow=ctx.flow,
                user=ctx.user,
            )

            await _update_document_invalid_signed_status(conn, document)

            # remove all drafts for this document
            await delete_drafts(
                conn=conn,
                document_ids=[document.id],
            )

        return AddSignatureResultCtx(
            validation_ctx=ctx,
            document_with_uploader=document,
            is_company_updated=is_company_updated,
            update_flow_output=update_flow_output,
            signer_role=signer_role,
            signature_id=signature_id,
        )


def unpack_signature_container_sync(content: bytes) -> P7SContent:
    """
    Read content of p7s file and return original of document and signatures to this
    documents.
    """
    signatures = []
    signatures_count = eusign.get_signs_count(content)
    if signatures_count == 0:
        logger.warning('P7S file without signatures', extra={'p7s': content})
        raise InvalidRequest(reason=_('P7s файл без підписів'))

    original: bytes | None = None
    for idx in range(signatures_count):
        signature, original = eusign_utils.verify_internal_sync(idx, content)
        signatures.append(signature)

    return P7SContent(
        # cast because we have at least one iteration over signatures
        content=cast(bytes, original),
        original=cast(bytes, original),
        signatures=signatures,
    )


def unpack_wrapped_signature_container_sync(data: bytes, iteration: int = 0) -> P7SContent:
    """
    Universal function for unpacking the signature containers. Can be used for both
    wrapped and appended signatures formats
    """
    if iteration > MAX_SIGNATURE_WRAPS:
        reason = _('Контейнер підписано більше ніж {wraps}').bind(wraps=MAX_SIGNATURE_WRAPS)
        logger.warning(msg='Container is too wrapped', extra={'iteration': iteration})
        raise InvalidRequest(reason=reason)

    content = unpack_signature_container_sync(data)
    if not is_internal_signature_sync(content.original):
        return content

    wrapped = unpack_wrapped_signature_container_sync(content.original, iteration + 1)
    return P7SContent(content=wrapped, original=content.original, signatures=content.signatures)


async def unpack_wrapped_signature_container(data: bytes, iteration: int = 0) -> P7SContent:
    return await run_sync(
        func=unpack_wrapped_signature_container_sync,
        data=data,
        iteration=iteration,
    )


async def is_internal_signature(data: bytes) -> bool:
    return await run_sync(func=is_internal_signature_sync, content=data)


async def update_company_name_on_sign(
    conn: DBConnection,
    user: AuthUserExtended | User | None,
    new_name: str | None,
) -> bool:
    """
    Update company name in 2 cases:
    - if company is FOP and has mistaken name
        (e.g. "ФІЗИЧНА ОСОБА" instead of "Іванов Іван Іванович")
    - if not company name


    IMPORTANT: remember to send an updated company to CRM
      outside of transaction where this function is called
    """
    if not user or not user.company_edrpou:
        return False

    old_name = user.company_name
    edrpou = user.company_edrpou

    # New name can't be empty to update company name
    if not new_name:
        return False

    # If new name is same as old name, we don't need to update it to avoid unnecessary
    # database operations
    if new_name == old_name:
        return False

    if (not old_name) or (old_name in UPDATABLE_BAD_COMPANY_NAMES):
        company = await update_company_by_edrpou(
            conn=conn,
            edrpou=edrpou,
            data={
                'name': get_short_company_name(new_name),
                'full_name': new_name,
            },
        )
        return bool(company)

    return False


async def get_signature_serial_number(signature: bytes) -> str:
    """Returns serial number of signature/stamp"""

    signature_data = await eusign_utils.get_cert_info(signature)
    return signature_data.serial


async def update_document_signers(
    conn: DBConnection,
    *,
    document: Document,
    company_id: str,
    company_edrpou: str,
    signers: list[UpdateSignersDataSignerEntity],
    parallel_signing: bool,
    is_document_owner: bool,
    current_role_id: str,
    signers_source: SignersSource | None = None,
) -> Document:
    """
    This function replaces all signers for document to another.

    NOTE: remember to update document status after this function call. See the comment at the end
    of this function.
    """

    async with conn.begin():
        # Remove previous signers
        old_signers = await db.delete_document_signers(
            conn=conn,
            document_id=document.id,
            company_id=company_id,
        )
        old_signers_map = {
            SignerId(role_id=signer.role_id, group_id=signer.group_id): signer
            for signer in old_signers
        }

        res = []
        for order, signer in enumerate(signers, start=1):
            data: DataDict = {
                'document_id': document.id,
                'company_id': company_id,
                'assigner': current_role_id,
                'order': None if parallel_signing else order,
                'source': signers_source,
                'date_signed': None,
                'group_signer_id': None,
            }

            if signer.type == UpdateSignersDataSignerType.role:
                signer_id = SignerId(role_id=signer.id, group_id=None)
            elif signer.type == UpdateSignersDataSignerType.group:
                signer_id = SignerId(role_id=None, group_id=signer.id)
            else:
                raise ValueError(f'Unknown signer type: {signer.type}')

            data['role_id'] = signer_id.role_id
            data['group_id'] = signer_id.group_id

            # Preserve some fields from previously removed signers
            if old_signer := old_signers_map.get(signer_id):
                data['date_signed'] = old_signer.date_signed
                data['group_signer_id'] = old_signer.group_signer_id
                data['assigner'] = old_signer.assigner or current_role_id

            res.append(data)

        # Insert role that not present in current signers
        if res:
            await db.insert_document_signers(conn, data=res)

        # Update expected signatures count on signers update.
        document = await update_signers_expected_signatures(
            conn=conn,
            document=document,
            signers=signers,
            is_document_owner=is_document_owner,
            company_edrpou=company_edrpou,
        )

        # NOTE: This function doesn't update the "status" of the document; its status should be
        # updated in the caller function. The main reason for this is that we use this function
        # in the "DocumentUpdate" class, where we can update recipients and signers in one
        # transaction, and both of them should be taken into account when updating the document
        # status.

    return document


async def update_signers_expected_signatures_multilateral(
    conn: DBConnection,
    *,
    document_id: str,
    company_edrpou: str,
    signers: list[UpdateSignersDataSignerEntity],
) -> None:
    """
    Update expected signatures count for multilateral document on documents signers
    update.
    """
    state = await flow_utils.get_flows_state(conn, document_id=document_id)

    # We update the first flow of the current company that is not signed yet. In cases when
    # a document still has "UPLOADED" status, it is maybe not the first flow in the order.
    # That's why we can't use "flow.get_current_company_flow()" here, which returns None if
    # it's not the current company's turn to sign.
    flow = next((f for f in state.flows if f.edrpou == company_edrpou and f.should_be_signed), None)
    if not flow:
        logger.info(
            msg='Could not update pending signatures for flow',
            extra={
                'document_id': document_id,
                'company_edrpou': company_edrpou,
                'signers': signers,
            },
        )
        return

    already_signed_count = max(flow.signatures_count - flow.pending_signatures_count, 0)
    signatures_count = len(signers)
    # We can't remove already signed signers so this can't be negative
    pending_signatures_count = signatures_count - already_signed_count

    if signatures_count:
        # If signers list is not empty, we should update pending signatures
        # count to the number of signers.
        await flow_utils.update_flow_signatures_count(
            conn=conn,
            flow=flow,
            signatures_count=signatures_count,
            pending_signatures_count=pending_signatures_count,
        )
        # The pending_signatures_count can be set to 0 only when the last signer signs
        # the document or when the last signer was removed.
        # In this function pending_signatures_count equals to 0 means that
        # the last signer was removed, so we need to mark flow as unfinished
        unfinished = not bool(pending_signatures_count)
        await set_flow_unfinished_status(conn=conn, flow=flow, unfinished=unfinished)
    else:
        await flow_utils.update_flow_signatures_count(
            conn=conn,
            flow=flow,
            signatures_count=1,
            pending_signatures_count=1,
        )


async def update_signers_expected_signatures(
    conn: DBConnection,
    *,
    document: Document,
    signers: list[UpdateSignersDataSignerEntity],
    is_document_owner: bool,
    company_edrpou: str,
) -> Document:
    """Update expected signatures count on signers update."""

    # For multilateral documents we're updating signatures count and pending signatures count
    # in "flows" table
    if document.is_multilateral:
        await update_signers_expected_signatures_multilateral(
            conn=conn,
            document_id=document.id,
            company_edrpou=company_edrpou,
            signers=signers,
        )
        return document

    # Internal & Bilateral documents

    # When user removes all signers from the document, document
    # still need at least one signature. But there is case when document
    # already has expected signatures equal to 0, and in this situation
    # user doesn't have ability to change signers at all. Validation of
    # this case is out of the scope of this function
    expected_signatures = 1 if not signers else len(signers)

    update_data: UpdateDocumentDict
    if is_document_owner:
        update_data = {
            'document_id': document.id,
            'expected_owner_signatures': expected_signatures,
        }
    else:
        update_data = {
            'document_id': document.id,
            'expected_recipient_signatures': expected_signatures,
        }

    return await documents.update_document(conn=conn, data=update_data)
