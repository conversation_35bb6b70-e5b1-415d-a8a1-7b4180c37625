from __future__ import annotations

import datetime
import logging
import typing as t
from collections import defaultdict
from collections.abc import Iterator
from dataclasses import dataclass
from enum import auto
from typing import NamedTuple, Self

from app.auth.types import AuthUser, AuthUserExtended, Role, User
from app.billing.types import ChargeDocumentContext
from app.document_versions.types import DocumentVersion
from app.documents.types import DocumentWithUploader, RecipientsEmailsOptions
from app.flow.types import FlowItem, UpdateFlowOnSignOutput
from app.groups.types import GroupMembersMapping
from app.lib.database import DBRow
from app.lib.enums import DocumentStatus, NamedEnum, SignatureFormat, SignersSource
from app.lib.helpers import not_none
from app.lib.types import DataDict
from app.signatures.constants import IGNORE_LAST_BYTE_RANGE
from app.signatures.enums import (
    CertificatePowerType,
    SignatureAlgo,
    SignaturePowerType,
    SignatureSource,
    SignatureType,
)

if t.TYPE_CHECKING:
    from app.signatures.validators import AddSignatureWorkingSchema

logger = logging.getLogger(__name__)


class PrivateKey(NamedTuple):
    key: str
    key_acsk: str
    key_serial_number: str
    key_timemark: datetime.datetime
    key_company_fullname: str
    key_owner_edrpou: str
    key_owner_fullname: str
    key_owner_position: str | None
    key_is_legal: bool

    stamp: str | None
    stamp_acsk: str | None
    stamp_serial_number: str | None
    stamp_timemark: datetime.datetime | None
    stamp_company_fullname: str | None
    stamp_owner_edrpou: str | None
    stamp_owner_fullname: str | None
    stamp_owner_position: str | None
    stamp_is_legal: bool | None

    is_internal: bool


class SignatureItem(NamedTuple):
    edrpou: str
    role_id: str
    is_valid: bool
    date_created: datetime.datetime


class SignerItem(NamedTuple):
    edrpou: str
    email: str
    role_id: str
    is_parallel: bool
    is_next: bool
    is_from_group: bool


class AddSignatureValidationCtx(NamedTuple):
    data: AddSignatureWorkingSchema
    document: DocumentWithUploader
    next_signer: DocumentSignerWithEdrpou | None
    document_version_id: str | None
    # used to update document_signature for this groups
    group_ids: list[str]
    charge_context: ChargeDocumentContext | None
    user: AuthUserExtended | User | None

    @property
    def user_role_id(self) -> str | None:
        return self.user.role_id if self.user else None

    @property
    def user_company_id(self) -> str | None:
        return self.user.company_id if self.user else None

    @property
    def next_status_id(self) -> int | None:
        return self.data.next_status_id

    @property
    def next_status(self) -> DocumentStatus | None:
        status_id = self.next_status_id
        return DocumentStatus(status_id) if status_id is not None else None

    @property
    def signature_format(self) -> SignatureFormat:
        return not_none(self.data.format)

    @property
    def signature_algo(self) -> SignatureAlgo | None:
        return self.data.algo

    @property
    def signature_container(self) -> bytes | None:
        return self.data.p7s

    @property
    def signature_edrpou(self) -> str:
        # stamp-only signing is possible
        edrpou = self.data.key_owner_edrpou or self.data.stamp_owner_edrpou
        assert edrpou is not None
        return edrpou

    @property
    def flow(self) -> FlowItem | None:
        return self.data.flow


class AddSignatureResultCtx(NamedTuple):
    signature_id: str
    validation_ctx: AddSignatureValidationCtx
    document_with_uploader: DocumentWithUploader
    is_company_updated: bool
    update_flow_output: UpdateFlowOnSignOutput | None
    signer_role: Role | None


class P7SContent:
    def __init__(
        self,
        content: bytes | P7SContent,
        original: bytes,
        signatures: list[DataDict],
    ) -> None:
        self.content = content
        self.original = original
        self.signatures = signatures

    @property
    def root(self) -> bytes:
        if isinstance(self.content, P7SContent):
            return self.content.root
        return self.content

    @property
    def depth(self) -> int:
        level = 1
        content = self.content
        while isinstance(content, P7SContent):
            content = content.content
            level += 1
        return level

    @property
    def all_signatures(self) -> list[DataDict]:
        """Return all signatures in reverse order"""
        content = self.content
        signatures = list(reversed(self.signatures))
        while isinstance(content, P7SContent):
            signatures.extend(reversed(content.signatures))
            content = content.content

        return signatures

    def __iter__(self) -> Iterator[P7SContent]:
        content: P7SContent | bytes = self
        while isinstance(content, P7SContent):
            yield content
            content = content.content


@dataclass
class Signature:
    # Basic fields
    id: str
    document_id: str
    role_id: str | None
    user_email: str | None
    owner_edrpou: str
    format: SignatureFormat
    algo: SignatureAlgo | None
    source: str | None
    date_created: datetime.datetime

    key_acsk: str | None
    key_timemark: datetime.datetime | None
    key_serial_number: str | None
    key_company_fullname: str | None
    key_owner_edrpou: str | None
    key_owner_position: str | None
    key_owner_fullname: str | None
    key_is_legal: bool | None
    key_power_type: SignaturePowerType | None
    key_certificate_power_type: CertificatePowerType | None
    key_exists: bool

    stamp_acsk: str | None
    stamp_timemark: datetime.datetime | None
    stamp_serial_number: str | None
    stamp_company_fullname: str | None
    stamp_owner_edrpou: str | None
    stamp_owner_position: str | None
    stamp_owner_fullname: str | None
    stamp_is_legal: bool | None
    stamp_power_type: SignaturePowerType | None
    stamp_certificate_power_type: CertificatePowerType | None
    stamp_exists: bool

    is_valid: bool

    is_internal: bool
    internal_file_name: str | None

    _row: DBRow

    @staticmethod
    def get_format_from_row(row: DBRow) -> SignatureFormat:
        """Extract signature format from raw database object"""
        raw_format = row.format
        if raw_format:
            return SignatureFormat(raw_format)

        # Before the signature format was introduced,
        # we used only the "_separated" formats
        if row.is_internal:
            return SignatureFormat.internal_separated

        return SignatureFormat.external_separated

    @classmethod
    def from_row(cls, row: DBRow) -> Signature:
        format_ = cls.get_format_from_row(row)
        owner_edrpou: str = row.key_owner_edrpou or row.stamp_owner_edrpou

        return Signature(
            id=row.id,
            document_id=row.document_id,
            role_id=row.role_id,
            user_email=row.user_email,
            owner_edrpou=owner_edrpou,
            key_acsk=row.key_acsk,
            key_timemark=row.key_timemark,
            key_serial_number=row.key_serial_number,
            key_company_fullname=row.key_company_fullname,
            key_owner_edrpou=row.key_owner_edrpou,
            key_owner_position=row.key_owner_position,
            key_owner_fullname=row.key_owner_fullname,
            key_is_legal=row.key_is_legal,
            key_power_type=row.key_power_type,
            key_certificate_power_type=row.key_certificate_power_type,
            key_exists=row.key_exists,
            stamp_acsk=row.stamp_acsk,
            stamp_timemark=row.stamp_timemark,
            stamp_serial_number=row.stamp_serial_number,
            stamp_company_fullname=row.stamp_company_fullname,
            stamp_owner_edrpou=row.stamp_owner_edrpou,
            stamp_owner_position=row.stamp_owner_position,
            stamp_owner_fullname=row.stamp_owner_fullname,
            stamp_is_legal=row.stamp_is_legal,
            stamp_power_type=row.stamp_power_type,
            stamp_certificate_power_type=row.stamp_certificate_power_type,
            stamp_exists=row.stamp_exists,
            is_internal=row.is_internal,
            internal_file_name=row.internal_file_name,
            is_valid=row.is_valid,
            format=format_,
            algo=SignatureAlgo(row.algo) if row.algo else None,
            source=row.source,
            date_created=row.date_created,
            _row=row,
        )

    @property
    def is_eusign_key(self) -> bool:
        return self.format.is_eusign and bool(self.key_serial_number)

    @property
    def is_eusign_stamp(self) -> bool:
        return self.format.is_eusign and bool(self.stamp_serial_number)

    @property
    def is_lost_byte_case(self) -> bool:
        """Ignore last byte from original or not.

        This is required as from 2017-11-08T10:46:17+0300 till
        2017-11-10T14:59:56+0300 all documents on Vchasno signed without last byte
        from original file. Which means we need to shrink that byte on generating
        archive, so original file + external signature can be verified at external
        service (CZO).
        """
        start, end = IGNORE_LAST_BYTE_RANGE
        return start <= self.date_created <= end

    @property
    def is_convertable(self) -> bool:
        """
        Property used in download archive module, to mark sign type as type,
        which we can convert to SignatureArchiveFormat.internal_appended

        - internal_appended is allowed even though it's the target format
        to be able to process combined formats for instance
        internal_appended + external_separated.

        Didn't include:
        - `external_wrapped`, `internal_wrapped`, `internal_asic` - signature types,
            where each subsequent signature is superimposed on the previous signature,
            not on the original file. Can't convert them to `internal_appended`
        """
        return self.format in (
            SignatureFormat.external_separated,
            SignatureFormat.internal_separated,
            SignatureFormat.internal_appended,
        )


@dataclass(frozen=True)
class DocumentSigner:
    id: str
    document_id: str
    company_id: str
    role_id: str | None
    group_id: str | None
    group_signer_id: str | None
    date_signed: datetime.datetime | None
    order: int | None
    assigner: str | None  # role ID
    date_created: datetime.datetime
    source: SignersSource

    @property
    def is_signed(self) -> bool:
        return self.date_signed is not None

    @staticmethod
    def from_row(row: DBRow) -> DocumentSigner:
        return DocumentSigner(
            id=row['id'],
            document_id=row['document_id'],
            company_id=row['company_id'],
            role_id=row['role_id'],
            group_id=row['group_id'],
            group_signer_id=row['group_signer_id'],
            date_signed=row['date_signed'],
            order=row['order'],
            assigner=row['assigner'],
            date_created=row['date_created'],
            source=row['source'],
        )

    def is_signer_role(self, *, role_id: str, group_members: GroupMembersMapping) -> bool:
        """
        If a given role should sign a document as direct signer or as a member of a signer group?
        """

        if group_id := self.group_id:
            return role_id in group_members.get(group_id, [])

        return self.role_id == role_id


@dataclass(frozen=True)
class SignersInfoCtx:
    class EntityType(NamedEnum):
        group = auto()
        role = auto()
        email = auto()  # used for validation only incoming request data

    entity: EntityType
    # role
    role_id: str | None
    # group
    group_id: str | None
    group_role_ids: list[str]

    @property
    def is_group(self) -> bool:
        return self.entity == self.EntityType.group

    @property
    def is_role(self) -> bool:
        return self.entity == self.EntityType.role


@dataclass(frozen=True)
class DocumentSignerWithEdrpou(DocumentSigner):
    # ...Document signer fields

    # Company fields
    company_edrpou: str

    @staticmethod
    def from_row(row: DBRow) -> DocumentSignerWithEdrpou:
        return DocumentSignerWithEdrpou(
            id=row.id,
            document_id=row.document_id,
            company_id=row.company_id,
            role_id=row.role_id,
            group_id=row.group_id,
            group_signer_id=row.group_signer_id,
            date_signed=row.date_signed,
            order=row.order,
            assigner=row.assigner,
            date_created=row.date_created,
            source=row.source,
            company_edrpou=row.company_edrpou,
        )


class SignerId(NamedTuple):
    role_id: str | None
    group_id: str | None


@dataclass
class AddSignatureStateCtx:
    signatures: list[Signature]
    document_version: DocumentVersion | None
    document_signers: list[DocumentSignerWithEdrpou]
    next_signer: DocumentSignerWithEdrpou | None
    group_ids: list[str]
    group_members: defaultdict[str, list[str]]
    flow: FlowItem | None
    # is required if a document is NOT multilateral (has not flow)
    next_status: DocumentStatus | None
    recipient: RecipientsEmailsOptions | None

    @property
    def document_version_id(self) -> str | None:
        return self.document_version.id if self.document_version else None


@dataclass
class SignatureInfoData:
    """
    Container to hold information about "key" or "stamp" in a structured way. "Key" and "stamp"
    share the same set of fields, but in a database we store them with different prefixes, like
    "key_acsk", "key_timemark", "stamp_acsk", "stamp_timemark" etc. This class implements
    a common interface to work with both types of signatures

    Use this class to properly preprocess and prepare "key" or "stamp" data for inserting
    a new signature into the database.
    """

    acsk: str | None
    timemark: datetime.datetime | None
    serial_number: str | None
    company_fullname: str | None
    owner_edrpou: str | None
    owner_position: str | None
    owner_fullname: str | None
    is_legal: bool | None
    signature_power_type: SignaturePowerType | None
    certificate_power_type: CertificatePowerType | None

    @classmethod
    def from_dict(cls, sign_info: DataDict) -> Self:
        """
        Preprocess and convert to typed object dict with signature info about "key" or "stamp"
        """
        from app.lib import eusign_utils

        return cls(
            acsk=sign_info['issuer_cn'],
            timemark=eusign_utils.get_time_mark(sign_info),
            serial_number=sign_info['serial'],
            company_fullname=eusign_utils.get_company_name(sign_info),
            owner_edrpou=eusign_utils.get_edrpou(sign_info),
            owner_position=sign_info['subject_title'],
            owner_fullname=eusign_utils.get_owner_full_name(sign_info),
            is_legal=eusign_utils.is_legal(sign_info),
            signature_power_type=eusign_utils.get_signature_power(sign_info),
            certificate_power_type=eusign_utils.get_certificate_power(sign_info),
        )

    @classmethod
    def to_empty_key(cls) -> Self:
        return cls(
            acsk=None,
            timemark=None,
            serial_number=None,
            company_fullname=None,
            owner_edrpou=None,
            owner_position=None,
            owner_fullname=None,
            is_legal=True,
            signature_power_type=None,
            certificate_power_type=None,
        )

    @classmethod
    def to_empty_stamp(cls) -> Self:
        return cls(
            acsk=None,
            timemark=None,
            serial_number=None,
            company_fullname=None,
            owner_edrpou=None,
            owner_position=None,
            owner_fullname=None,
            # pay attention, this field is not set to True like in SignatureKeyInfoData
            # because we "stamp_is_legal" can be null in the database, but "key_is_legal" is not
            is_legal=None,
            signature_power_type=None,
            certificate_power_type=None,
        )

    @classmethod
    def to_empty_key_dict(cls) -> DataDict:
        return cls.to_empty_key().to_key_dict()

    @classmethod
    def to_empty_stamp_dict(cls) -> DataDict:
        return cls.to_empty_stamp().to_stamp_dict()

    def to_key_dict(self) -> DataDict:
        return {
            'key_acsk': self.acsk,
            'key_timemark': self.timemark,
            'key_serial_number': self.serial_number,
            'key_company_fullname': self.company_fullname,
            'key_owner_edrpou': self.owner_edrpou,
            'key_owner_position': self.owner_position,
            'key_owner_fullname': self.owner_fullname,
            'key_is_legal': self.is_legal,
            'key_power_type': self.signature_power_type,
            'key_certificate_power_type': self.certificate_power_type,
        }

    def to_stamp_dict(self) -> DataDict:
        return {
            'stamp_acsk': self.acsk,
            'stamp_timemark': self.timemark,
            'stamp_serial_number': self.serial_number,
            'stamp_company_fullname': self.company_fullname,
            'stamp_owner_edrpou': self.owner_edrpou,
            'stamp_owner_position': self.owner_position,
            'stamp_owner_fullname': self.owner_fullname,
            'stamp_is_legal': self.is_legal,
            'stamp_power_type': self.signature_power_type,
            'stamp_certificate_power_type': self.certificate_power_type,
        }


class DocumentSignerCounter:
    """
    A container that holds the count of signers for a document and a company. It could be just a
    dictionary, but this class provides a safer way to build a key from two values that are easy
    to confuse with each other.
    """

    def __init__(self) -> None:
        self.mapping: dict[tuple[str, str], int] = {}

    def get(self, *, document_id: str, company_edrpou: str) -> int:
        return self.mapping.get((document_id, company_edrpou), 0)

    def set(self, *, document_id: str, company_edrpou: str, count: int) -> None:
        self.mapping[(document_id, company_edrpou)] = count


@dataclass(frozen=True)
class SignatureCertificate:
    role_id: str
    serial_number: str
    is_stamp: bool
    acsk: str
    type: SignatureType
    date_end: datetime.datetime

    locality: str | None
    state: str | None

    @staticmethod
    def from_db(row: DBRow) -> SignatureCertificate:
        return SignatureCertificate(
            role_id=row.role_id,
            serial_number=row.serial_number,
            is_stamp=row.is_stamp,
            acsk=row.acsk,
            type=row.type,
            date_end=row.date_end,
            locality=row.locality,
            state=row.state,
        )

    @property
    def is_vchasno(self) -> bool:
        return 'VCHASNO SERVICE' in self.acsk or 'Вчасно Сервіс' in self.acsk


@dataclass
class AddSignatureDiiaRequestCtx:
    document: DocumentWithUploader
    sign_algo: SignatureAlgo | None
    request_user: AuthUser | User
    # Is request user exists in the DB? Can be None for sign session context (AuthUser)
    registered_user: User | None
    sign_session_id: str | None
    source: SignatureSource | None
