import base64
import binascii
import logging
import typing as t
from collections import defaultdict
from itertools import zip_longest

import pydantic
import ujson
from aiohttp import web
from py_eusign import eusign

from api.downloads.types import to_document
from api.downloads.utils import (
    download_asic_container_content,
    download_document_content,
    download_wrapped_signature_content,
)
from api.errors import (
    FIELD_IS_REQUIRED,
    AccessDenied,
    AlreadyExists,
    Code,
    DoesNotExist,
    Error,
    InvalidRequest,
    Object,
    ServerError,
)
from app.archive.utils import is_document_archived
from app.auth.db import select_company_by_role_id, select_user
from app.auth.types import AuthUser, AuthUserExtended, User
from app.auth.utils import get_company_config, get_sign_session_id_from_request
from app.auth.validators import (
    validate_edrpou,
    validate_user_permission,
)
from app.billing.types import ChargeDocumentContext
from app.billing.validators import validate_charge_document, validate_document_payer
from app.comments.db import select_rejects
from app.document_revoke.types import DocumentRevokeSignature
from app.document_versions.enums import DocumentVersionType
from app.document_versions.types import DocumentVersion
from app.document_versions.utils import (
    get_latest_document_version,
)
from app.documents import types as document_types
from app.documents.enums import FirstSignBy
from app.documents.types import DocumentWithUploader, RecipientsEmailsOptions
from app.documents.utils import (
    get_document_next_status_signing,
    get_required_recipient,
    is_first_sign_by_recipient_document,
    is_wait_owner_signature,
)
from app.documents.validators import (
    validate_document_access,
    validate_document_exists,
    validate_self_recipient,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.flow.types import FlowItem
from app.flow.utils import FlowsState
from app.groups.types import GroupMembersMapping
from app.groups.utils import get_group_members_by_group_ids
from app.i18n import _
from app.lib import eusign_utils, validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.enums import DocumentStatus, SignatureFormat
from app.lib.enums import SignatureType as SignatureFileType
from app.lib.helpers import not_none, split_comma_separated_emails
from app.lib.logging import truncate_logging_extra
from app.lib.types import DataDict, StrDict
from app.reviews.validators import validate_required_review_for_document
from app.services import services
from app.sign_sessions import utils as sign_sessions
from app.signatures.db import (
    select_document_signers_extended,
    select_signature_by_id,
    select_signatures,
)
from app.signatures.enums import (
    ACSK,
    CertificatePowerType,
    SignatureAlgo,
    SignaturePowerType,
    SignatureSource,
    SignatureType,
)
from app.signatures.types import (
    AddSignatureDiiaRequestCtx,
    AddSignatureStateCtx,
    AddSignatureValidationCtx,
    DocumentSignerWithEdrpou,
    Signature,
    SignatureInfoData,
)

_sentinel = object()

logger = logging.getLogger(__name__)


class AddSignatureRequestSchema(pydantic.BaseModel):
    # Incoming request fields
    document_id: pv.UUID
    format: SignatureFormat | None = None
    id: pv.UUID | None = None
    edrpou_recipient: pv.EDRPOU | None = None
    emails_recipient: list[pv.Email] | None = None
    # deprecated: use `emails_recipient` instead or update document before signing
    email_recipient: list[pv.Email] | None = None

    # external signature strings (base64)
    key: str | None = None
    stamp: str | None = None

    company_name: str | None = None
    archive: t.Literal['zip'] | None = None
    source: SignatureSource
    p7s: bytes | None = None
    acsk: str | None = None
    algo: SignatureAlgo | None = None
    # free-form fields from client
    fields: DataDict | None = None

    # accepted extras previously allowed explicitly
    edrpou_owner: pv.EDRPOU | None = None
    email_owner: list[pv.Email] | None = None

    # TODO: remove with config.feature_flags.pass_sign_info_to_backend
    # optional sign info (when frontend passes it instead of backend parsing)
    # key signer
    key_acsk: str | None = None
    key_serial_number: str | None = None
    key_timemark: pv.SoftDatetime | None = None
    key_company_fullname: str | None = None
    key_owner_edrpou: pv.EDRPOU | None = None
    key_owner_fullname: str | None = None
    key_owner_position: str | None = None
    key_is_legal: bool | None = None
    # stamp signer
    stamp_acsk: str | None = None
    stamp_serial_number: str | None = None
    stamp_timemark: pv.SoftDatetime | None = None
    stamp_company_fullname: str | None = None
    stamp_owner_edrpou: pv.EDRPOU | None = None
    stamp_owner_fullname: str | None = None
    stamp_owner_position: str | None = None
    stamp_is_legal: bool | None = None

    @pydantic.field_validator('email_recipient', 'email_owner', mode='before')
    @classmethod
    def _split_email_string_to_list(cls, v: t.Any) -> list[str] | None:
        if v is None:
            return v
        if isinstance(v, str):
            emails = split_comma_separated_emails(v.strip())
            return emails if emails else None
        return v

    @pydantic.model_validator(mode='after')
    def _rename_recipient_emails(self) -> t.Self:
        # normalize legacy email_recipient -> emails_recipient
        legacy = self.email_recipient
        if not legacy:
            return self

        if self.emails_recipient:
            raise InvalidRequest(reason=_('Еmail не може бути заповнено двічі'))

        self.emails_recipient = legacy
        self.email_recipient = None
        return self


class AddSignatureWorkingSchema(AddSignatureRequestSchema):
    model_config = pydantic.ConfigDict(arbitrary_types_allowed=True)

    # Extended fields computed during validation
    # signer info (key)
    key_acsk: str | None = None
    key_serial_number: str | None = None
    key_timemark: pv.Datetime | None = None
    key_company_fullname: str | None = None
    key_owner_edrpou: pv.EDRPOU | None = None
    key_owner_fullname: str | None = None
    key_owner_position: str | None = None
    key_is_legal: bool | None = None
    key_power_type: SignaturePowerType | None = None
    key_certificate_power_type: CertificatePowerType | None = None
    # signer info (stamp)
    stamp_acsk: str | None = None
    stamp_serial_number: str | None = None
    stamp_timemark: pv.Datetime | None = None
    stamp_company_fullname: str | None = None
    stamp_owner_edrpou: pv.EDRPOU | None = None
    stamp_owner_fullname: str | None = None
    stamp_owner_position: str | None = None
    stamp_is_legal: bool | None = None
    stamp_power_type: SignaturePowerType | None = None
    stamp_certificate_power_type: CertificatePowerType | None = None

    # runtime context
    uploader_company_id: str | None = None
    is_first_document_signing: bool = False
    is_internal: bool | None = None
    user_role_id: str | None = None
    user_id: str | None = None
    user_email: str | None = None
    internal_file_name: str | None = None

    # recipient computed data
    is_recipient_emails_hidden: bool = False
    group_ids: list[str] = pydantic.Field(default_factory=list)
    is_first_sign_by_recipient_document: bool | None = None
    is_owner_signature: pv.BoolNullToFalse = False
    is_recipient_signature: bool | None = None
    next_status_id: int | None = None
    flow: FlowItem | None = None


class SignatureSerialNumberSchema(pydantic.BaseModel):
    signature: bytes | None = None
    stamp: bytes | None = None

    @pydantic.field_validator('signature', 'stamp', mode='before')
    @classmethod
    def _decode_base64(cls, v: t.Any) -> bytes | None:
        if v is None:
            return None
        try:
            return base64.b64decode(v)
        except binascii.Error:
            raise ValueError('Invalid base64-encoded data')


class UpdateEmptySignatureSchema(pydantic.BaseModel):
    signature_id: pv.UUID
    user_id: pv.UUID

    key_acsk: str
    key_serial_number: str
    key_timemark: pv.SoftDatetime | None = None
    key_owner_edrpou: pv.EDRPOU
    key_owner_fullname: str

    stamp_acsk: str | None = None
    stamp_serial_number: str | None = None
    stamp_timemark: pv.SoftDatetime | None = None
    stamp_owner_edrpou: pv.EDRPOU | None = None
    stamp_owner_fullname: str | None = None


class CertificateInfoSchema(pydantic.BaseModel):
    role_id: pv.UUID
    is_stamp: bool
    serial_number: str = pydantic.Field(max_length=128)
    acsk: str = pydantic.Field(max_length=128)
    state: str | None = pydantic.Field(default=None, max_length=128)
    locality: str | None = pydantic.Field(default=None, max_length=128)
    type: SignatureType
    date_end: pv.Date


class CloudSignerSchema(pydantic.BaseModel):
    document_id: pv.UUID
    operation_id: str = pydantic.Field(max_length=88)


class DiiaRequestSchema(pydantic.BaseModel):
    sign_algo: SignatureAlgo | None = None


async def validate_signature_access(conn: DBConnection, signature_id: str, user: User) -> Signature:
    signature = await select_signature_by_id(conn, signature_id)
    if not signature:
        raise DoesNotExist(Object.signature, signature_id=signature_id)

    signature_edrpou = signature.stamp_owner_edrpou or signature.key_owner_edrpou
    if signature_edrpou != user.company_edrpou:
        raise DoesNotExist(Object.signature, signature_id=signature_id)

    return signature


def _validate_next_document_flow_to_sign_parallel(
    state: FlowsState,
    signature_edrpou: str,
    user: AuthUser | User | None,
) -> FlowItem | None:
    """
    Get next parallel flow that current user should sign for a multilateral document
    """

    next_flows = state.parallel_flows_for_sign

    next_flows = [f for f in next_flows if f.edrpou == signature_edrpou]

    # anonymous signature case:
    # - don't allow extra signatures for anonymous users
    # - any next flow that company should sign are OK for anon signature
    if not user:
        if not next_flows:
            raise InvalidRequest(reason=_('Неможливо допідписати документ анонімно'))
        return next_flows[0]

    # try to find the next flow that user should sign
    for flow in next_flows:
        if not flow.is_signed_by_user(user) and not flow.is_finished:
            return flow

    # in other cases, consider it as an extra signature if the company is a signer
    # of the document but the current user is not a signer of any next flow
    if state.is_signed_once_by_company(company_edrpou=signature_edrpou):
        return None

    raise AccessDenied(reason=_('Документ не очікує підписів від вашої компанії'))


def _validate_next_document_flow_to_sign_ordered(
    state: FlowsState,
    signature_edrpou: str,
    user: AuthUser | User | None,
) -> FlowItem | None:
    """
    Get the next order flow that current user should sign for a multilateral document
    """

    next_flows = state.ordered_flows_for_sign

    # case when all flows are signed:
    #  - anonymous signatures are not allowed
    #  - allow only extra signature
    if not next_flows:
        if not user:
            raise AccessDenied(reason=_('Неможливо допідписати документ анонімно'))
        if state.is_signed_once_by_company(company_edrpou=signature_edrpou):
            return None  # extra sign
        raise AccessDenied(reason=_('Документ не очікує підписів від вашої компанії'))

    # next flows for ordered document always have 0 or 1 elements
    next_flow: FlowItem = next_flows[0]

    # case when another company should sign the next flow:
    #  - allow only extra signature
    if next_flow.edrpou != signature_edrpou:
        if not user:
            raise AccessDenied(reason=_('Неможливо допідписати документ анонімно'))
        if state.is_signed_once_by_company(company_edrpou=signature_edrpou):
            return None  # extra sign
        raise AccessDenied(reason=_('Зараз черга іншої компанії підписувати послідовний документ'))

    # case when the current company should sign the next flow
    if user and next_flow.is_signed_by_user(user):
        return None  # extra sign

    return next_flow


def validate_next_document_flow_to_sign(
    state: FlowsState,
    signature_edrpou: str,
    user: AuthUser | User | None,
) -> FlowItem | None:
    """
    Select flow for a multilateral document that the current user should sign.

    This function either returns the next flow that the user should sign
    or None if it's an extra sign
    """

    if state.is_ordered:
        return _validate_next_document_flow_to_sign_ordered(
            state=state,
            signature_edrpou=signature_edrpou,
            user=user,
        )

    if state.is_parallel:
        return _validate_next_document_flow_to_sign_parallel(
            state=state,
            signature_edrpou=signature_edrpou,
            user=user,
        )

    return None


async def validate_send_flow_on_sign(
    conn: DBConnection,
    *,
    document: document_types.Document,
    current_company_edrpou: str,
    flow: FlowItem | None,
    state: FlowsState,
) -> None:
    """
    Check if we can send a document to recipients after signing.
    """
    from app.flow.validators import validate_first_send_multilateral_document

    if not flow:
        return

    # This conditions means that document will be sent for the first
    # time to the recipients
    if document.edrpou_owner == current_company_edrpou and flow.pending_signatures_count == 1:
        await validate_first_send_multilateral_document(
            conn=conn,
            document=document,
            state=state,
            current_company_edrpou=current_company_edrpou,
        )


def validate_add_signature_data(data: DataDict) -> AddSignatureRequestSchema:
    try:
        return validators.validate_pydantic(AddSignatureRequestSchema, data)
    except InvalidRequest as err:
        logger.warning(
            'Wrong data for adding signature',
            extra={'data': truncate_logging_extra(data), 'errors': err.details},
        )
        raise


async def validate_add_signature_type(
    first_signature: Signature | DocumentRevokeSignature | None,
    signature_container: bytes | None,
    signature_external: str | None,
    *,
    log_extra: DataDict | None = None,
) -> None:
    _log_extra: DataDict = log_extra or {}

    # Validate signature type (internal or external)
    is_container_file_field = isinstance(signature_container, bytes)
    if (
        (not first_signature or not first_signature.is_internal)
        and not signature_external
        and not is_container_file_field
    ):
        logger.warning(
            'Key or signature file was not provided',
            extra={
                'signature_key': signature_external,
                'signature_file': signature_container,
                **_log_extra,
            },
        )
        raise InvalidRequest(key=FIELD_IS_REQUIRED)


def validate_add_signature_recipient(
    document: document_types.DocumentWithUploader,
    recipient: RecipientsEmailsOptions | None,
    user: AuthUser | User | None,
    *,
    log_extra: DataDict | None = None,
) -> None:
    _log_extra: DataDict = log_extra or {}
    is_3p = is_first_sign_by_recipient_document(document)
    is_owner_signature = is_wait_owner_signature(document)
    if not is_3p and is_owner_signature:
        if not recipient:
            if not document.is_internal:
                logger.warning(
                    msg='Sign document without email or edrpou',
                    extra={
                        **truncate_logging_extra(_log_extra),
                        'document': document.to_log_extra(),
                    },
                )
                raise InvalidRequest(
                    reason=_('ЄДРПОУ чи email мають бути заповненими'),
                    edrpou=FIELD_IS_REQUIRED,
                    email=FIELD_IS_REQUIRED,
                )
        else:
            validate_self_recipient(recipient, user)


async def validate_charge_document_on_sign(
    conn: DBConnection,
    document: document_types.DocumentWithUploader,
    user: AuthUser | User | None,
    is_internal_bill_sending: bool,
) -> ChargeDocumentContext | None:
    """
    Get who should pay for that document and validate that payer can pay for that document.

    NOTE: Although we charge on "sign" action only "internal" documents, we also validate
    "bilateral" documents here. For "bilateral" documents, charging happens on the "send" action,
    but we validate both "sign" and "send" to fail early and to avoid "signed", but not "sent"
    documents as much as possible.
    """

    role_id: str | None = user.role_id if user else None
    if not role_id:
        # TODO: add charging for anon users
        logger.warning('Skip charging anon signature', extra={'document_id': document.id})
        return None

    # We can charge only an owner of the document
    is_owner_signature = is_wait_owner_signature(document)
    if not is_owner_signature:
        return None

    # For document with first sign by recipient, we are charging on "send" action
    if document.first_sign_by == FirstSignBy.recipient:
        return None

    payer_id = await validate_document_payer(conn, document=document)
    return await validate_charge_document(
        conn=conn,
        role_id=role_id,
        document_id=document.id,
        document_is_internal=document.is_internal,
        document_is_internal_bill=is_internal_bill_sending,
        company_id=payer_id,
        document_vendor=document.vendor,
        document_source=document.source,
    )


def validate_add_signature_permission(
    user: AuthUser | User | User | None, document: DocumentWithUploader
) -> None:
    if not user:
        return

    if get_flag(FeatureFlags.ENABLE_SEPARATE_DOC_SIGN_PERMISSIONS):
        if document.is_internal:
            validate_user_permission(user, {'can_sign_and_reject_document_internal'})
        else:
            validate_user_permission(user, {'can_sign_and_reject_document_external'})
    else:
        validate_user_permission(user, {'can_sign_and_reject_document'})


def validate_add_signature_status(document: document_types.Document, signature_edrpou: str) -> None:
    # Prepare flags for owner/recipient signature
    is_owner_signature = is_wait_owner_signature(document)
    is_recipient_signature = not is_owner_signature
    # If document not signed yet, only owner can sign it first. If document
    # already signed by owner, only partner can sign it.
    if (is_owner_signature and document.edrpou_owner != signature_edrpou) or (
        is_recipient_signature and document.edrpou_recipient != signature_edrpou
    ):
        logger.warning(
            'Unable to sign document by given EDRPOU',
            extra={
                'company_edrpou': signature_edrpou,
                'is_owner_signature': is_owner_signature,
                'is_recipient_signature': is_recipient_signature,
                'owner_edrpou': document.edrpou_owner,
                'recipient_edrpou': document.edrpou_recipient,
                'status_id': document.status_id,
            },
        )
        reason = _('Неможливо підписати документ, використовуючи ЄДРПОУ поточного користувача')
        raise AccessDenied(reason=reason)


async def validate_add_signature_document_in_rejected_status(
    conn: DBConnection,
    *,
    document: document_types.Document,
    signers: list[DocumentSignerWithEdrpou],
    group_members: GroupMembersMapping,
    user: AuthUser | User | None,
) -> None:
    """
    Validate if the current user can add a signature to a rejected document.

    Someone how rejected or is a signer of the document can't sign rejected document and return
    it back to the signing process.
    """

    if document.status_id != DocumentStatus.reject.value:
        return

    if not signers:
        return

    rejects = await select_rejects(conn, document.id)
    if not rejects:
        logger.warning(
            'Document has 7006 status without rejects',
            extra={'document_id': document.id},
        )
        return

    user_role_id: str | None = user.role_id if user else None
    if not user_role_id:
        return

    latest_reject = sorted(rejects, key=lambda r: r.date_created)[-1]
    if latest_reject.role_id == user_role_id:
        return

    if any(s.is_signer_role(role_id=user_role_id, group_members=group_members) for s in signers):
        return

    raise Error(Code.invalid_action, reason=_('Неможливо підписати відхилений документ'))


def rename_recipient_emails_field_on_sign(valid_data: AddSignatureRequestSchema) -> None:
    """
    Normalize legacy email_recipient (list) into emails_recipient field on model.
    """
    legacy = valid_data.email_recipient
    if not legacy:
        return

    if valid_data.emails_recipient:
        raise InvalidRequest(reason=_('Еmail не може бути заповнено двічі'))

    valid_data.emails_recipient = legacy
    valid_data.email_recipient = None


async def validate_add_signature(
    *,
    conn: DBConnection,
    user: AuthUserExtended | User | None,
    data: DataDict,
    sign_session_id: str | None = None,
) -> AddSignatureValidationCtx:
    request_schema = validate_add_signature_data(data)

    signature_container = request_schema.p7s
    signature_external = request_schema.key or request_schema.stamp

    # Document must exist in database
    document = await validate_document_exists(conn, document_id=request_schema.document_id)

    signatures = await select_signatures(conn, [document.id])
    document_version = await validate_versioned_document_signer(
        conn=conn,
        document=document,
        signatures=signatures,
        user=user,
    )

    # Read data from sign info if necessary.
    # Request may contain optional sign info fields already validated by AddSignatureRequestSchema
    # (it is the old behavior when config.feature_flags.pass_sign_info_to_backend is True).
    # Build a working schema without re-validating.
    working_schema = AddSignatureWorkingSchema.model_construct(**request_schema.model_dump())

    if not services.config.feature_flags.pass_sign_info_to_backend:
        signature_data = await validate_signature_file(
            conn=conn,
            document=document,
            data=working_schema,
            signature_container=signature_container,
            signatures=signatures,
            user=user,
            version_id=document_version.id if document_version else None,
        )
        for k, v in signature_data.items():
            setattr(working_schema, k, v)

    signature_edrpou = working_schema.key_owner_edrpou
    stamp_edrpou = working_schema.stamp_owner_edrpou

    if signature_edrpou is None:
        # validate stamp-only signing
        validate_first_sign_with_key(stamp_edrpou, signatures)
        signature_edrpou = stamp_edrpou

    assert signature_edrpou is not None

    ctx = await validate_add_signature_state(
        conn=conn,
        user=user,
        document=document,
        sign_session_id=sign_session_id,
        edrpou_recipient=working_schema.edrpou_recipient,
        emails_recipient=working_schema.emails_recipient,
        signatures=signatures,
        document_version=document_version,
        signature_edrpou=signature_edrpou,
    )

    # Prepare uploader `company_id`, to use in decisions with upload counter
    uploader_company = await select_company_by_role_id(conn, not_none(document.uploaded_by))

    working_schema.is_recipient_emails_hidden = False
    working_schema.emails_recipient = None
    working_schema.edrpou_recipient = None

    working_schema.uploader_company_id = getattr(uploader_company, 'id', None)
    working_schema.is_first_document_signing = len(ctx.signatures) == 0
    working_schema.is_internal = bool(signature_container)
    working_schema.user_role_id = user.role_id if user else None
    working_schema.user_id = user.id if user else None
    working_schema.user_email = user.email if user else None
    if working_schema.archive and working_schema.is_internal:
        working_schema.internal_file_name = f'p7s.{working_schema.archive}'

    await validate_add_signature_type(
        first_signature=ctx.signatures[0] if ctx.signatures else None,
        signature_container=signature_container,
        signature_external=signature_external,
        log_extra={'valid_data': working_schema.model_dump()},
    )

    # Make sure that non-test EDRPOU passed from frontend
    validate_edrpou(signature_edrpou)
    validate_edrpou(stamp_edrpou)

    # Make sure that key/stamp serial number not used before by other coworker
    validate_unique_serial_numbers(
        signatures=ctx.signatures,
        key_serial_number=working_schema.key_serial_number,
        stamp_serial_number=working_schema.stamp_serial_number,
        signature_edrpou=signature_edrpou,
    )

    group_ids = get_signing_by_groups(
        document_signers=ctx.document_signers,
        user=user,
        group_members=ctx.group_members,
        signatures=ctx.signatures,
        next_signer=ctx.next_signer,
    )

    # For multilateral document we have separate validation from some point
    if document.is_multilateral:
        working_schema.flow = ctx.flow
        return AddSignatureValidationCtx(
            data=working_schema,
            document=document,
            next_signer=ctx.next_signer,
            group_ids=group_ids,
            document_version_id=None,
            charge_context=None,  # multilateral is free for now
            user=user,
        )

    charge_context = await validate_charge_document_on_sign(
        conn=conn,
        user=user,
        document=document,
        # TODO: pass parameter from worker job that sends bills
        is_internal_bill_sending=False,
    )

    working_schema.group_ids = [
        signing.group_id for signing in ctx.document_signers if signing.group_id
    ]

    # Update signature data
    working_schema.is_first_sign_by_recipient_document = is_first_sign_by_recipient_document(
        document
    )
    working_schema.is_owner_signature = is_wait_owner_signature(document)
    working_schema.is_recipient_signature = not working_schema.is_owner_signature

    if ctx.next_status is not None:
        working_schema.next_status_id = ctx.next_status.value
    else:
        raise ServerError(code=Code.error_500)

    if ctx.recipient:
        working_schema.emails_recipient = ctx.recipient.emails
        working_schema.edrpou_recipient = ctx.recipient.edrpou
        working_schema.is_recipient_emails_hidden = ctx.recipient.is_emails_hidden

    if user and user.company_id:
        is_archived = await is_document_archived(
            conn=conn,
            document_id=document.id,
            company_id=user.company_id,
        )
        if is_archived:
            raise AccessDenied(reason=_('Документ знаходиться в архіві'))

    return AddSignatureValidationCtx(
        data=working_schema,
        document=document,
        next_signer=ctx.next_signer,
        group_ids=group_ids,
        document_version_id=ctx.document_version_id,
        charge_context=charge_context,
        user=user,
    )


def get_signing_by_groups(
    document_signers: list[DocumentSignerWithEdrpou],
    user: AuthUser | User | None,
    signatures: list[Signature],
    group_members: defaultdict[str, list[str]],
    next_signer: DocumentSignerWithEdrpou | None = None,
) -> list[str]:
    """
    Get groups that should be marked as signed
    when document is signed by current user
    """
    if not user:
        return []

    is_ordered = document_signers and document_signers[0].order

    group_ids = []

    # All groups where current user is a member
    if not is_ordered:
        group_ids = [
            signing.group_id
            for signing in document_signers
            if signing.group_id and user.role_id in group_members[signing.group_id]
        ]
    else:
        # Find all groups that should be marked as signed
        # based on next_signer.
        # Next signer might be None or next role or next group where user not a member
        # but until that all groups should be marked as signed
        if next_signer and next_signer.order:
            # try to mark groups as signed until next signer
            suitable_groups = [
                signing.group_id
                for signing in document_signers
                if signing.group_id
                and not signing.date_signed
                and signing.order
                and signing.order <= next_signer.order
            ]
        else:
            # it's last signer
            # try to mark all groups as signed
            suitable_groups = [
                signing.group_id
                for signing in document_signers
                if signing.group_id and not signing.date_signed
            ]
        # check if user from these groups already signed the document
        # if so - mark group as signed
        for role_id in [s.role_id for s in signatures] + [user.role_id]:
            for group_id in filter(None, suitable_groups):
                if role_id in group_members[group_id]:
                    group_ids.append(group_id)

    return group_ids


async def validate_versioned_document_signer(
    conn: DBConnection,
    document: document_types.Document,
    signatures: list[Signature],
    user: AuthUser | User | None,
) -> DocumentVersion | None:
    """
    Validation for versioned documents.
    - Validate that only recipient of a version can add first signature for versioned document.

    Returns the latest version (any tyep) of the document if it's versioned
    """

    # Multilateral documents currently doesn't support versioning
    if document.is_multilateral:
        return None

    # For internal documents, we don't need to check the EDRPOU of the version because internal
    # versions of the document are created only by one company. Also, "is_sent" is not used
    # for internal documents.
    if document.is_internal:
        return await get_latest_document_version(conn, document_id=document.id)

    latest_any_version = await get_latest_document_version(
        conn=conn,
        document_id=document.id,
        is_sent=True,
    )

    # The document is not versioned at all
    if not latest_any_version:
        return None

    # no need to validate if a document is already signed by somebody
    if any(signatures):
        return latest_any_version

    # For bilateral documents, although the latest version can be "converted_format" we try to
    # find the latest version with "new_upload" (or "editor_created") type to understand who
    # should sign the version first. This is because the "converted_format" version doesn't need
    # to be "approved" by sign by another side, but when a company uploads and sends "new_upload"
    # it should be signed by another side first, company that didn't upload the document.
    latest_bilateral_version = await get_latest_document_version(
        conn=conn,
        document_id=document.id,
        is_sent=True,
        versions_types=[DocumentVersionType.new_upload, DocumentVersionType.editor_created],
    )
    if not latest_bilateral_version:
        raise InvalidRequest(
            reason=_('Не вдалося знайти останню версію, завантажену і відправлену іншою стороною')
        )

    if not user or not user.company_edrpou:
        # Don't know how to handle this case ¯\_(ツ)_/¯
        logger.info(
            'Could not find a user',
            extra={
                'document_version': latest_bilateral_version.to_dict(),
                'user_id': getattr(user, 'id', None),
            },
        )
        raise ServerError(code=Code.error_500)

    if (
        not document.is_multilateral
        and not document.is_internal
        and latest_bilateral_version.company_edrpou == user.company_edrpou
    ):
        raise InvalidRequest(
            reason=_('Власник версії документа не може підписати документ першим'),
        )

    return latest_any_version


def validate_p7s(p7s: str, **kwargs: t.Any) -> bytes:
    """Decode base64-encoded p7s value."""
    try:
        return base64.b64decode(p7s)
    except binascii.Error:
        raise Error(Code.invalid_p7s_external_value, details=kwargs)


async def validate_sign_info(
    original: bytes, key: str | None, stamp: str | None, acsk: ACSK
) -> DataDict:
    data: DataDict = {}

    _validate_signature_original_size(content=original)

    # Append key sign info
    if key:
        key_bytes = validate_p7s(key, key=True)
        await _validate_signature_file_type(key=key_bytes)
        key_sign_info = await eusign_utils.verify(
            content=original,
            p7s=key_bytes,
            acsk=acsk,
            key=True,
            label=_('ключа КЕП/ЕЦП'),
        )
        key_info = SignatureInfoData.from_dict(key_sign_info)
        data.update(key_info.to_key_dict())
    else:
        data.update(SignatureInfoData.to_empty_key_dict())

    # Append stamp sign info if necessary
    if stamp:
        stamp_bytes = validate_p7s(stamp, key=True)
        await _validate_signature_file_type(stamp=stamp_bytes)
        stamp_sign_info = await eusign_utils.verify(
            content=original,
            p7s=stamp_bytes,
            acsk=acsk,
            label=_('електронної печатки'),
            stamp=True,
        )
        stamp_info = SignatureInfoData.from_dict(stamp_sign_info)
        data.update(stamp_info.to_stamp_dict())
    else:
        data.update(SignatureInfoData.to_empty_stamp_dict())

    return data


async def validate_internal_original(
    original: bytes,
    signature: bytes,
    existing_signatures: list[Signature] | list[DocumentRevokeSignature],
) -> list[DataDict]:
    """
    Unpack internal signature, validate original and return latest signatures
    for provided company edrpou.
    Returns not stored signatures
    """
    from app.signatures.utils import unpack_wrapped_signature_container

    existing_sign_info_map = _get_signature_info_map(existing_signatures)

    # Unwrap signature container
    container = await unpack_wrapped_signature_container(signature)

    # Iterate over last signatures from container of current edrpou
    signatures: list[DataDict] = []
    signature_original: bytes | None = None
    for _signature in container:
        # Iterate over all signatures in container in reverse mode,
        # because we expected that user signatures was added to the end of
        # signature container
        for signature_info in reversed(_signature.signatures):
            # Iterate until we find existed serial number and
            # that signature.is_valid = True
            existing_signature = existing_sign_info_map.get(signature_info['serial'])
            if existing_signature and existing_signature.is_valid:
                break
            signatures.append(signature_info)
            signature_original = _signature.original
        else:
            # just continue a loop if all signatures are from current company edrpou
            continue

        # nested loop was broke, so break this loop too
        break

    if signature_original != original:
        raise Error(Code.invalid_container_internal_original)

    return signatures


def _get_signature_info_map(
    signatures: list[Signature] | list[DocumentRevokeSignature],
) -> dict[str, Signature] | dict[str, DocumentRevokeSignature]:
    """
    Returns info about signature by serial_number
    """
    result: DataDict = {}
    for signature in signatures:
        if signature.key_serial_number:
            result[signature.key_serial_number] = signature
        if signature.stamp_serial_number:
            result[signature.stamp_serial_number] = signature
    return result


def _validate_signature_original_size(content: bytes) -> None:
    # Do not allow sign empty files
    if len(content) == 0:
        raise InvalidRequest(reason=_('Неможливо підписати порожній файл'))


def _validate_is_key_signature(signature_type: str) -> None:
    if signature_type != SignatureFileType.signature.value:
        raise Error(Code.invalid_p7s_key_type)


def _validate_is_stamp_signature(signature_type: str) -> None:
    if signature_type != SignatureFileType.stamp.value:
        raise Error(Code.invalid_p7s_stamp_type)


async def _validate_signature_file_type(
    *,
    key: bytes | None = None,
    stamp: bytes | None = None,
) -> None:
    if key:
        cert_info_ex = await eusign_utils.get_cert_info(signature=key)
        signature_type = eusign_utils.guess_signature_type(cert_info_ex)
        if signature_type != SignatureFileType.signature:
            raise Error(Code.invalid_p7s_key_type)

    if stamp:
        cert_info_ex = await eusign_utils.get_cert_info(signature=stamp)
        signature_type = eusign_utils.guess_signature_type(cert_info_ex)

        if signature_type != SignatureFileType.stamp:
            raise Error(Code.invalid_p7s_stamp_type)


async def validate_asic_internal(
    file_name: str,
    original: bytes,
    signature: bytes,
    existed_container: bytes | None,
) -> DataDict:
    _validate_signature_original_size(content=original)

    data = {}
    data.update(SignatureInfoData.to_empty_key_dict())
    data.update(SignatureInfoData.to_empty_stamp_dict())

    sign_index = 0
    if existed_container:
        # If container contains N signatures already,
        # validation will check (N + 1) signature that we are trying to add,
        # so sign_index = (N + 1) - 1 = N (sign_index starts from 0)
        sign_index = eusign_utils.get_asic_signatures_count(existed_container)

    try:
        container = eusign_utils.prepare_asic_container(
            file_name=file_name,
            file_content=original,
            existed_container=existed_container,
        )
        container.finish_signing(signature)
        container_content = container.save()
    except eusign.EusignError:
        logger.exception(
            'Cant create asic container with provided signature',
            extra={
                'is_prev_container_exists': bool(existed_container),
            },
        )
        raise Error(Code.invalid_container_internal_original)

    sign_info, signature_original = await eusign_utils.verify_asic_internal(
        idx=sign_index, content=container_content
    )
    if signature_original != original:
        raise Error(Code.invalid_container_internal_original)

    # Currently we support only EU signature with keys (added via DIIA)
    key_info = SignatureInfoData.from_dict(sign_info)
    data.update(key_info.to_key_dict())

    # Replace signature with created/updated ASiC-e container (for saving to s3)
    data['p7s'] = container_content

    return data


async def validate_sign_info_internal(
    original: bytes,
    signature: bytes,
    signatures: list[Signature] | list[DocumentRevokeSignature],
) -> DataDict:
    _validate_signature_original_size(content=original)

    # Prepend empty data that will be overwritten by data from internal
    # signature later
    data = {}
    data.update(SignatureInfoData.to_empty_key_dict())
    data.update(SignatureInfoData.to_empty_stamp_dict())

    found_signs: defaultdict[str, int] = defaultdict(int)

    signatures_data = await validate_internal_original(
        original=original, signature=signature, existing_signatures=signatures
    )

    for sign_info in signatures_data:
        signature_type = eusign_utils.guess_type(sign_info)
        if signature_type == SignatureFileType.signature:
            _validate_is_key_signature(sign_info['signature_type'])
            key_info = SignatureInfoData.from_dict(sign_info)
            data.update(key_info.to_key_dict())
            found_signs['key'] += 1

        elif signature_type == SignatureFileType.stamp:
            _validate_is_stamp_signature(sign_info['signature_type'])
            stamp_info = SignatureInfoData.from_dict(sign_info)
            data.update(stamp_info.to_stamp_dict())
            found_signs['stamp'] += 1
        else:
            t.assert_never(signature_type)

    # Check signs count
    if found_signs['key'] > 1 or found_signs['stamp'] > 1:
        logger.warning('Internal signatures count not match')
        raise Error(Code.invalid_container_internal_sign_count)

    if not found_signs:
        logger.warning('Internal signature has neither key or stamp info')
        raise Error(Code.invalid_container_internal)

    return data


def validate_signature_json_data(data: DataDict) -> DataDict:
    try:
        if data_str := data.get('data'):
            return ujson.loads(data_str)
        return {}
    except ValueError:
        raise Error(Code.invalid_json_request)


def validate_unique_serial_numbers(
    signatures: list[Signature] | list[DocumentRevokeSignature],
    key_serial_number: str | None,
    stamp_serial_number: str | None,
    signature_edrpou: str,
) -> None:
    """Validate that user doesn't use key/stamp already used by coworker."""

    for item in signatures:
        # Exclude non-coworkers signatures
        if (
            item.key_owner_edrpou != signature_edrpou
            and item.stamp_owner_edrpou != signature_edrpou
        ):
            continue

        # Key already used
        if item.key_serial_number and item.key_serial_number == key_serial_number and item.is_valid:
            raise AlreadyExists(
                Object.key,
                reason=_(
                    'Неможливо підписати документ, використовуючи вказаний '
                    'ключ КЕП/ЕЦП. Документ вже підписаний цим '
                    'ключем КЕП/ЕЦП'
                ),
                serial_number=key_serial_number,
            )

        # Stamp already used
        if (
            item.stamp_serial_number
            and item.stamp_serial_number == stamp_serial_number
            and item.is_valid
        ):
            raise AlreadyExists(
                Object.stamp,
                reason=_(
                    'Неможливо підписати документ, використовуючи вказану '
                    'печатку. Документ вже підписано цією печаткою'
                ),
                serial_number=stamp_serial_number,
            )


def validate_update_empty_signature(data: StrDict) -> UpdateEmptySignatureSchema:
    """Validate data for update empty signature."""
    return validators.validate_pydantic(UpdateEmptySignatureSchema, data)


def validate_user_edrpou(user_edrpou: str, sign_info_edrpou: str | None) -> None:
    """Ensure that current user has same EDRPOU as one from sign info."""
    if sign_info_edrpou and user_edrpou != sign_info_edrpou:
        details = {'sign_info_edrpou': sign_info_edrpou, 'user_edrpou': user_edrpou}
        logger.warning('Wrong edrpou in external signature', extra=details)
        raise Error(Code.invalid_sign_info_edrpou, details=details)


def validate_document_signers(
    user: AuthUser | User | None,
    document_signers: list[DocumentSignerWithEdrpou],
    signatures: list[Signature],
    group_members: defaultdict[str, list[str]],
) -> DocumentSignerWithEdrpou | None:
    if not user:
        return None

    next_signer: DocumentSignerWithEdrpou | None = None
    if not document_signers:
        return next_signer

    is_ordered = document_signers[0].order is not None

    if is_ordered:
        signers = [s for s in document_signers if s.date_signed is None]
        for current, next_s in zip_longest(signers, signers[1:]):
            # can be signed by current user
            if current.role_id and current.role_id == user.role_id:
                next_signer = next_s
                continue
            if current.group_id and user.role_id in group_members.get(current.group_id, []):
                next_signer = next_s
                continue

            # if signer is a group and role from this group signed the document before
            if current.group_id:
                for role_id in [s.role_id for s in signatures] + [user.role_id]:
                    if role_id in group_members.get(current.group_id, []):
                        continue

            next_signer = current
            break

    error_reason = _('Документ повинен підписати інший співробітник')

    for signer in document_signers:
        if signer.role_id == user.role_id:
            return next_signer
        if signer.group_id and user.role_id in group_members.get(signer.group_id, []):
            return next_signer

        # In case when order of signatures is important (order != None)
        # check that first signatures are existed
        if signer.order and not signer.date_signed:
            raise AccessDenied(reason=error_reason)

    raise AccessDenied(reason=error_reason)


async def validate_add_signature_diia_request(
    request: web.Request,
    conn: DBConnection,
    request_user: AuthUser | User,
) -> AddSignatureDiiaRequestCtx:
    """
    Check if the user can create a QR code with a deeplink to sign the document using the Diia app.

    It is better to reuse here as much validation as possible from the "add_signature"
    endpoint to fail early. Signing through the Diia app is quite slow and requires unusual user
    interaction, so if it is not possible to sign the document now, we should stop the signing
    process before it even starts.
    """
    from app.signatures.utils import get_signature_source_from_request

    document_id = request.match_info['document_id']
    sign_session_id = get_sign_session_id_from_request(request)

    raw_data = await validators.validate_json_request(request, allow_blank=True)
    valid_data = validators.validate_pydantic(DiiaRequestSchema, raw_data)

    logger.info('Request deeplink from Diia', extra={'document_id': document_id})

    # When a user is not present (ex, in case of sign session), we will ask user to sign
    # a company registration token also to register a new user.
    registered_user: User | None
    if isinstance(request_user, AuthUser):
        company_edrpou = request_user.company_edrpou
        assert company_edrpou, 'Company EDRPOU is required for Diia sign request'
        registered_user = await select_user(
            conn=conn,
            email=request_user.email,
            company_edrpou=company_edrpou,
        )
    else:
        registered_user = request_user

    document = await validate_document_exists(conn, {'document_id': document_id})
    validate_add_signature_permission(request_user, document)

    # This validation can work in logged context (web/mobile) as well in sign session
    # context, because "auth_user" is always present in both cases.
    await validate_document_access(conn, document_id=document_id, user=request_user)

    await validate_allowed_format_by_owner(
        conn=conn,
        document=document,
        signature_format=SignatureFormat.internal_asic
        if valid_data.sign_algo == SignatureAlgo.ECDSA.value
        else SignatureFormat.external_separated,
    )

    signatures = await select_signatures(conn, [document_id])

    # This validation is optional, so it's OK do not perform them if a user is not present.
    # But in most cases, when a user exists, we should check them to fail early.
    if registered_user:
        document_signers = await select_document_signers_extended(
            conn=conn,
            company_id=registered_user.company_id,
            document_id=document_id,
        )
        group_members = await get_group_members_by_group_ids(
            conn,
            group_ids=[s.group_id for s in document_signers if s.group_id],
        )
        validate_document_signers(
            user=registered_user,
            document_signers=document_signers,
            signatures=signatures,
            group_members=group_members,
        )

    source = get_signature_source_from_request(request)

    return AddSignatureDiiaRequestCtx(
        document=document,
        sign_algo=valid_data.sign_algo,
        sign_session_id=sign_session_id,
        request_user=request_user,
        registered_user=registered_user,
        source=source,
    )


async def validate_signature_file(
    conn: DBConnection,
    document: DocumentWithUploader,
    data: AddSignatureWorkingSchema,
    signature_container: bytes | None,
    signatures: list[Signature],
    user: AuthUser | User | None,
    version_id: str | None,
) -> DataDict:
    signature_format = not_none(data.format)
    acsk_param = data.acsk
    acsk = ACSK[acsk_param] if acsk_param is not None else ACSK.default
    user_edrpou = user.company_edrpou if user else None

    if signature_format == SignatureFormat.external_separated:
        # Verify & read sign info data from external signature
        original = await download_document_content(
            conn=conn,
            document=to_document(document),
            version_id=version_id,
        )
        valid_data = await validate_sign_info(
            original=original,
            key=data.key,
            stamp=data.stamp,
            acsk=acsk,
        )

    elif signature_format in (
        SignatureFormat.internal_separated,
        SignatureFormat.internal_appended,
    ):
        # Verify & read sign info data from internal signature
        signature = t.cast(bytes, signature_container)
        original = await download_document_content(
            conn=conn,
            document=to_document(document),
            version_id=version_id,
        )
        valid_data = await validate_sign_info_internal(
            original=original,
            signature=signature,
            signatures=signatures,
        )

    elif signature_format == SignatureFormat.internal_wrapped:
        signature = t.cast(bytes, signature_container)
        content = await download_wrapped_signature_content(
            conn=conn, document=to_document(document)
        )
        valid_data = await validate_sign_info_internal(
            original=content,
            signature=signature,
            signatures=signatures,
        )

    elif signature_format == SignatureFormat.internal_asic:
        # All ECDSA signatures stored in one asic container
        signature = t.cast(bytes, signature_container)
        existed_container = await download_asic_container_content(document.id)
        downloads_document = to_document(document)
        original = await download_document_content(
            conn=conn,
            document=downloads_document,
            version_id=version_id,
        )
        valid_data = await validate_asic_internal(
            file_name=downloads_document.file_name,
            original=original,
            signature=signature,
            existed_container=existed_container,
        )

    else:
        logger.error(
            msg='Requested signature format validation was not implemented',
            extra={
                'signature_format': signature_format.value,
                'document_id': document.id,
            },
        )
        raise Error(Code.not_implemented)

    await validate_allowed_format_by_owner(
        conn=conn,
        document=document,
        signature_format=signature_format,
    )

    if user_edrpou:
        validate_user_edrpou(user_edrpou, valid_data['key_owner_edrpou'])
        validate_user_edrpou(user_edrpou, valid_data.get('stamp_owner_edrpou'))

    return valid_data


def has_company_signers_ahead(
    edrpou: str, document_signers: list[DocumentSignerWithEdrpou]
) -> bool:
    """Checks whether current user company has further sign flow for given document."""
    return any(
        signer
        for signer in document_signers
        if signer.company_edrpou == edrpou and not signer.date_signed
    )


def _is_extra_signature(
    *,
    signature_edrpou: str,
    document: document_types.Document,
    signatures: list[Signature],
    document_signers: list[DocumentSignerWithEdrpou],
) -> bool:
    """Checks if current signature is 'extra one'."""

    # any signature without flow (signers were not added explicitly)
    is_implicit_sign = not has_company_signers_ahead(signature_edrpou, document_signers)

    # current company has already started sign flow (at least one signature was added)
    company_signatures = [
        sign
        for sign in signatures
        if sign.key_owner_edrpou == signature_edrpou or sign.stamp_owner_edrpou == signature_edrpou
    ]
    is_signed_by_company = len(company_signatures) > 0

    is_owner_sign = signature_edrpou == document.edrpou_owner
    if is_owner_sign:
        pending = document.expected_owner_signatures - len(company_signatures)
    else:
        pending = document.expected_recipient_signatures - len(company_signatures)

    # if document already was signed, then current signature is considered
    # as extra signature, not recipient one
    is_extra_sign = is_signed_by_company and is_implicit_sign and pending < 1

    return is_extra_sign


def validate_first_sign_with_key(
    edrpou: str | None, signatures: list[Signature] | list[DocumentRevokeSignature]
) -> None:
    """
    Validates first signing from a company.
    User can't sign a document without a key on first signing.
    """
    if not any(s.key_owner_edrpou == edrpou for s in signatures):
        raise AccessDenied(reason=_('Неможливо підписати документ лише печаткою'))


async def validate_add_signature_state(
    *,
    conn: DBConnection,
    document: document_types.DocumentWithUploader,
    user: AuthUser | User | None,
    sign_session_id: str | None = None,
    edrpou_recipient: str | None = None,
    emails_recipient: list[str] | None = None,
    signature_edrpou: str | None = None,
    signatures: list[Signature] | None = None,
    document_version: DocumentVersion | None | object = _sentinel,
) -> AddSignatureStateCtx:
    """
    Validate document state before adding a signature.
    To make sure that document state is valid for adding a signature or
    user can sign document.
    """
    validate_add_signature_permission(user, document)

    if user is not None:
        if sign_session_id:
            # TODO: Refactor this logic, as we are not actually checking access to the document
            #  here. Instead, we are verifying access to the sign session. Keep in mind that a
            #  "user" may not have direct access to the document, but the sign session can grant
            #  them access to it.
            session = await sign_sessions.get_optional_sign_session(
                conn=conn,
                sign_session_id=sign_session_id,
            )
            if not session or user.company_edrpou != session.edrpou:
                raise AccessDenied(reason=_('Доступ до документу заборонено'))

            # Document can't be signed using anything but sign session
            # for example you can't sign a document if session is created
            # for document viewing
            if not session.type.is_sign_session:
                raise AccessDenied(
                    reason=_('Неможливо підписати документ, використовуючи сесію підписання'),
                )

            logger.warning(
                msg='User is trying to sign a document using a sign session',
                extra={
                    'sign_session_document_id': session.document_id,
                    'sign_session_email': session.email,
                    'sign_session_edrpou': session.edrpou,
                    'user_email': user.email,
                    'user_edrpou': user.company_edrpou,
                    'document_id': document.id,
                },
            )
        else:
            await validate_document_access(conn=conn, user=user, document_id=document.id)
    else:
        # For now, we are granting full access to anonymous users,
        # but it's better to log this case for further analysis and possible refactoring
        logger.warning(
            msg='Anonymous user is trying to sign a document',
            extra={
                'document_id': document.id,
            },
        )

    signature_edrpou = signature_edrpou or (user.company_edrpou if user else None)
    assert signature_edrpou, 'Signature EDRPOU is not set'

    signatures = await select_signatures(conn, [document.id]) if signatures is None else signatures
    document_signers = await select_document_signers_extended(
        conn=conn,
        company_id=user.company_id if user else None,
        edrpou=signature_edrpou,
        document_id=document.id,
    )
    group_ids = [s.group_id for s in document_signers if s.group_id]
    group_members = await get_group_members_by_group_ids(
        conn,
        group_ids=group_ids,
    )

    # Validation for versioned documents
    document_version_id = None
    if document_version is _sentinel:
        document_version = await validate_versioned_document_signer(
            conn=conn,
            document=document,
            signatures=signatures,
            user=user,
        )
        document_version_id = document_version.id if document_version else None

    # Validate ability to sign document with review request
    await validate_required_review_for_document(
        conn=conn,
        edrpou=signature_edrpou,
        document_id=document.id,
        action=_('підписати'),
        version_id=document_version_id,
    )

    await validate_add_signature_document_in_rejected_status(
        conn,
        document=document,
        signers=document_signers,
        group_members=group_members,
        user=user,
    )

    if document.is_multilateral:
        from app.flow.utils import get_flows_state

        flows_state = await get_flows_state(conn=conn, document_id=document.id)
        next_flow = validate_next_document_flow_to_sign(
            state=flows_state,
            signature_edrpou=signature_edrpou,
            user=user,
        )
        next_signer = validate_document_signers(
            user=user,
            document_signers=document_signers,
            signatures=signatures,
            group_members=group_members,
        )
        await validate_send_flow_on_sign(
            conn=conn,
            document=document,
            flow=next_flow,
            state=flows_state,
            current_company_edrpou=signature_edrpou,
        )
        return AddSignatureStateCtx(
            signatures=signatures,
            document_version=t.cast(DocumentVersion | None, document_version),
            flow=next_flow,
            document_signers=document_signers,
            next_signer=next_signer,
            group_ids=group_ids,
            group_members=group_members,
            next_status=None,
            recipient=None,
        )

    is_extra_sign = _is_extra_signature(
        signature_edrpou=signature_edrpou,
        document=document,
        signatures=signatures,
        document_signers=document_signers,
    )

    if not is_extra_sign and not document_version:
        # Validations for bilateral and internal documents
        validate_add_signature_status(
            document=document,
            signature_edrpou=signature_edrpou,
        )

    if not is_extra_sign:
        validate_document_signers(
            user=user,
            document_signers=document_signers,
            signatures=signatures,
            group_members=group_members,
        )

    # If owner signature check recipient EDRPOU & email
    edrpou_recipient = edrpou_recipient or document.edrpou_recipient
    emails_recipient = emails_recipient or split_comma_separated_emails(document.email_recipient)
    recipient = await get_required_recipient(
        conn=conn,
        document_id=document.id,
        document_owner_edrpou=document.edrpou_owner,
        edrpou_recipient=edrpou_recipient,
        emails_recipient=emails_recipient,
    )
    validate_add_signature_recipient(
        document=document,
        recipient=recipient,
        user=user,
    )

    # Generate next status for the document
    if is_extra_sign:
        # extra signatures do not impact current document status
        next_status = DocumentStatus(document.status_id)
        next_signer = None
    else:
        next_status = get_document_next_status_signing(
            user=user,
            document=document,
            signatures=signatures,
            document_signers=document_signers,
            group_members=group_members,
        )
        next_signer = validate_document_signers(
            user=user,
            document_signers=document_signers,
            signatures=signatures,
            group_members=group_members,
        )

    return AddSignatureStateCtx(
        signatures=signatures,
        document_version=t.cast(DocumentVersion | None, document_version),
        flow=None,
        document_signers=document_signers,
        next_signer=next_signer,
        group_ids=group_ids,
        group_members=group_members,
        next_status=next_status,
        recipient=recipient,
    )


async def can_sign_by_user(
    *,
    conn: DBConnection,
    document_id: str,
    user: AuthUser | User | None,
    sign_session_id: str | None = None,
) -> bool:
    """
    Check if user can sign the document.
    Uses validate_add_signature like logic.
    """

    try:
        document = await validate_document_exists(conn, {'document_id': document_id})
        await validate_add_signature_state(
            conn=conn,
            document=document,
            user=user,
            sign_session_id=sign_session_id,
        )
    except Error:
        return False

    return True


async def can_sign_by_user_for_graph(
    *,
    document_id: str,
    user: AuthUser | User | None,
    sign_session_id: str | None = None,
) -> bool:
    async with services.db_readonly.acquire() as conn:
        return await can_sign_by_user(
            conn=conn,
            document_id=document_id,
            user=user,
            sign_session_id=sign_session_id,
        )


async def validate_allowed_format_by_owner(
    conn: DBConnection,
    signature_format: SignatureFormat,
    document: document_types.Document,
) -> None:
    """
    Make sure that signature with this format can be added to the document
    """
    if get_flag(FeatureFlags.DISABLE_ALLOWED_SIGNATURE_FORMAT_RESTRICTIONS):
        return

    # always allow format from the document itself
    if signature_format == document.signature_format:
        return

    config = await get_company_config(conn, company_edrpou=document.edrpou_owner)
    if config.allowed_signature_types is not None:
        allowed_signatures = {document.signature_format, *config.allowed_signature_types}
        if signature_format not in allowed_signatures:
            raise Error(
                Code.invalid_signature_format,
                details={
                    'signature_format': signature_format.value,
                    'allowed_signature_types': sorted(allowed_signatures),
                },
            )
