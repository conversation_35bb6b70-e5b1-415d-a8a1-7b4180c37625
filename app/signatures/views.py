import logging
from http import HTTPStatus

from aiohttp import web

from api.downloads.types import to_document
from api.downloads.utils import (
    download_asic_container_content,
    download_latest_document_version,
)
from api.public.utils import update_processed_status
from app import diia
from app.actions.utils import get_source
from app.auth.decorators import (
    login_required,
    sign_session_login_required,
    super_admin_permission_required,
)
from app.auth.types import AuthUser, BaseUser, User
from app.auth.utils import get_sign_session_id_from_request
from app.auth.validators import validate_extended_auth_user_exists
from app.diia.schemas import AddSignatureDiiaAction
from app.documents import utils as documents
from app.es.utils import send_to_indexator
from app.i18n import _
from app.lib import eusign_utils, tracking, validators
from app.lib.database import DBRow
from app.lib.helpers import generate_base64_str
from app.registration.utils import create_company_registration_token
from app.services import services
from app.sign_sessions.utils import get_extended_auth_user
from app.signatures import kep
from app.signatures.db import (
    delete_cloud_signer,
    insert_certificate_info,
    insert_cloud_signer,
    update_signature,
)
from app.signatures.enums import (
    SignatureAlgo,
)
from app.signatures.utils import (
    add_signature,
    add_signature_schedule_async_jobs,
    get_add_signature_request_data,
    get_signature_serial_number,
)
from app.signatures.validators import (
    CertificateInfoSchema,
    CloudSignerSchema,
    SignatureSerialNumberSchema,
    validate_add_signature_diia_request,
    validate_update_empty_signature,
)

logger = logging.getLogger(__name__)


async def add(request: web.Request, raw_user: AuthUser | User) -> web.Response:
    """Sign document by owner or recipient."""
    app = request.app

    async with app['db'].acquire() as conn:
        # Active user with a role is required for adding signatures.
        # We pre-register the user before the signature is added
        request_user, existing_user = await validate_extended_auth_user_exists(
            conn=conn,
            raw_user=raw_user,
            ensure_active_user=True,
        )

        data = await get_add_signature_request_data(request)

        ctx = await add_signature(
            conn=conn,
            user=request_user,
            data=data,
            sign_session_id=get_sign_session_id_from_request(request),
        )
        await add_signature_schedule_async_jobs(
            conn=conn,
            ctx=ctx,
            request_source=get_source(request),
        )

        await update_processed_status(
            conn,
            ctx.document_with_uploader.id,
            status_id=ctx.document_with_uploader.status_id,
            processed=False,
            user_company_id=request_user.company_id,
        )

        await documents.autosend_document_on_sign(
            conn=conn,
            company_edrpou=request_user.company_edrpou,
            document_id=ctx.document_with_uploader.id,
            request_source=get_source(request),
            # TODO: update send_document to work with User | AuthUserExtended instead of User only
            # to avoid losing important information about sign sessions
            user=existing_user,
        )

    await send_to_indexator(
        app['redis'], document_ids=[ctx.document_with_uploader.id], to_slow_queue=False
    )

    tracking.signatures_count_web.inc()
    return web.json_response(status=HTTPStatus.CREATED)


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_empty(request: web.Request, user: User) -> web.Response:
    """Update empty signature by saving detailed info."""
    schema = validate_update_empty_signature(
        dict(
            await validators.validate_json_request(request),
            signature_id=request.match_info['signature_id'],
            user_id=user.id,
        )
    )

    signature_id = schema.signature_id
    data = schema.model_dump(mode='json', exclude={'signature_id', 'user_id'})

    async with request.app['db'].acquire() as conn:
        await update_signature(conn, signature_id=signature_id, data=data)

    return web.json_response()


@sign_session_login_required
async def update_certificate(request: web.Request, raw_user: AuthUser | User) -> web.Response:
    data = await validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        user = await get_extended_auth_user(conn, raw_user=raw_user)

    schema = validators.validate_pydantic(CertificateInfoSchema, {'role_id': user.role_id, **data})

    async with request.app['db'].acquire() as conn:
        await insert_certificate_info(conn, schema.model_dump())

    return web.Response()


async def diia_request(request: web.Request, raw_user: AuthUser | User) -> web.Response:
    """
    Create deeplink for QR code to sign a document in Diia app

    Where it can be requested:
    - in web UI
    - in mobile app
    - in sign session

    """
    async with services.db.acquire() as conn:
        valid_ctx = await validate_add_signature_diia_request(
            request=request,
            conn=conn,
            request_user=raw_user,
        )
        document = to_document(valid_ctx.document)
        file_content = await download_latest_document_version(conn, document)

    if valid_ctx.sign_algo == SignatureAlgo.ECDSA:
        existed_container = await download_asic_container_content(document.id_)
        container = eusign_utils.prepare_asic_container(
            file_name=document.raw_file_name,
            file_content=file_content,
            existed_container=existed_container,
        )
        hash_base64 = generate_base64_str(container.get_data_for_signing())
    else:
        hash_base64 = await eusign_utils.generate_hash_base64(file_content)

    deeplink_document = diia.DiiaDeeplinkFile(
        title=f'Підпис документу: {document.title}',
        hash=hash_base64,
    )

    action_source = diia.get_diia_action_source(request)

    action: AddSignatureDiiaAction
    files: list[diia.DiiaDeeplinkFile]
    if user := valid_ctx.registered_user:
        # The most common case of signing a document in the Diia app, whether via the mobile or
        # web UI, or in the context of a sign session, where a user is already registered
        # with the company.
        action = AddSignatureDiiaAction(
            email=user.email,
            document_id=document.id_,
            user_id=user.id,
            role_id=user.role_id,
            # Even if a user exists, it's important to provide "sign_session_id" to properly
            # validate access to the document, based on sign session, not only on the user.
            # The user itself might not have access to the document, but the sign session created
            # by someone more privileged, can provide direct access to the document
            sign_session_id=valid_ctx.sign_session_id,
            sign_algo=valid_ctx.sign_algo,
            should_register_signer=False,
            sign_source=valid_ctx.source,
            source=action_source,
        )
        files = [deeplink_document]
    else:
        # This case involves signing a document in the Diia app within the context of a sign
        # session when the user is not registered with the company (anonymous user). To properly
        # register the user and add them to the company, we ask the user to sign a company token
        # in addition to the document.
        user_email = valid_ctx.request_user.email
        action = AddSignatureDiiaAction(
            email=user_email,
            document_id=document.id_,
            user_id=None,
            role_id=None,
            sign_session_id=valid_ctx.sign_session_id,
            sign_algo=valid_ctx.sign_algo,
            should_register_signer=True,
            sign_source=valid_ctx.source,
            source=action_source,
        )

        company_token = await create_company_registration_token(
            user_id=valid_ctx.request_user.id,
            user_email=valid_ctx.request_user.email,
        )
        deeplink_token = diia.DiiaDeeplinkFile(
            title=_('Токен реєстрації').value,
            hash=company_token.base64_hash,
        )
        files = [deeplink_document, deeplink_token]

    action_ctx = await diia.request_action(
        action=action,
        files=files,
        sign_algo=valid_ctx.sign_algo,
    )

    return web.json_response(action_ctx.to_dict())


@login_required()
async def add_operation_id(request: web.Request, user: User) -> web.Response:
    """
    Add new operation id for vchasno cloud signer
    """

    raw_data = await validators.validate_json_request(request)

    schema = validators.validate_pydantic(CloudSignerSchema, raw_data)

    async with services.db.acquire() as conn:
        await insert_cloud_signer(conn=conn, data={'role_id': user.role_id, **schema.model_dump()})

    return web.json_response(status=HTTPStatus.CREATED)


@login_required()
async def delete_operation_id(request: web.Request, user: User) -> web.Response:
    """
    Delete operation id from db for vchasno cloud signer after document is singed
    """

    operation_id = request.match_info['operation_id']

    async with services.db.acquire() as conn:
        await delete_cloud_signer(conn=conn, role_id=user.role_id, operation_id=operation_id)

    return web.json_response(status=HTTPStatus.CREATED)


async def get_serial_number(request: web.Request, _: DBRow | None = None) -> web.Response:
    """Returns serial number of signature/stamp"""

    data = await validators.validate_json_request(request)
    schema = validators.validate_pydantic(SignatureSerialNumberSchema, data)

    signature = schema.signature
    stamp = schema.stamp

    result = {}

    if signature is not None:
        result['signature_serial_number'] = await get_signature_serial_number(
            signature,
        )

    if stamp is not None:
        result['stamp_serial_number'] = await get_signature_serial_number(
            stamp,
        )

    return web.json_response(result)


async def kep_user_certificates(
    request: web.Request, user: AuthUser | User | BaseUser
) -> web.Response:
    """
    Get user certificates from KEP
    """

    res = await kep.client.get_kep_user_certificates(auth_user=user)

    return web.json_response(res.to_dict())


async def kep_graphql_proxy(request: web.Request, user: AuthUser | BaseUser | User) -> web.Response:
    """
    Proxy GraphQL request to KEP with integration token and service identifier
    """

    raw_data = await validators.validate_json_request(request)

    res = await kep.client.proxy_kep_graphql_request(
        auth_user=user,
        raw_data=raw_data,
    )

    return web.json_response(res)
