from __future__ import annotations

import datetime
import typing
from dataclasses import dataclass
from enum import auto
from typing import Named<PERSON>uple, cast

from aiohttp import web

from app.document_versions.types import DocumentVersion
from app.i18n.types import LazyI18nString
from app.lib.database import DBRow
from app.lib.datetime_utils import to_local_datetime
from app.lib.enums import Language, NamedEnum
from app.lib.types import DataDict
from app.reviews.enums import (
    ReviewRequestSource,
    ReviewRequestStatus,
    ReviewStatus,
    ReviewType,
)

if typing.TYPE_CHECKING:
    from app.documents.types import Document


class TReview(NamedTuple):
    edrpou: str
    role_id: str
    date_created: datetime.datetime
    group_id: str | None
    # If "type_" is None, it's mean that user decides
    # to withdraw their own review
    type_: str | None


class TReviewRequest(NamedTuple):
    edrpou: str
    from_role_id: str
    to_role_id: str | None
    to_group_id: str | None
    status: str
    is_parallel: bool
    is_next: bool
    is_from_group: bool
    review_exist: bool


class TReviewStatus(NamedTuple):
    edrpou: str
    status: str
    is_required: bool
    version_id: str | None


class DownloadReviewHistoryCtx(NamedTuple):
    document: Document
    format_: str
    request: web.Request


class ReviewHistoryItem(NamedTuple):
    user_name: str
    user_email: str
    action: LazyI18nString
    date: datetime.datetime

    @property
    def date_str(self) -> str:
        return to_local_datetime(self.date).strftime('%d.%m.%Y %H:%M:%S')


class ReviewHistory(NamedTuple):
    document_id: str
    document_title: str
    document_number: str | None
    document_date: datetime.datetime | None

    items: list[ReviewHistoryItem]

    @property
    def document_date_str(self) -> str:
        if not self.document_date:
            return ''
        return to_local_datetime(self.document_date).strftime('%d.%m.%Y %H:%M:%S')


class ReviewerId(NamedTuple):
    """
    Identifier of the reviewer. It can be either role_id or group_id.

    Use it to match reviews and review requests.
    """

    role_id: str | None
    group_id: str | None

    @staticmethod
    def from_review_row(review: DBRow) -> ReviewerId:
        if review.group_id:
            return ReviewerId(role_id=None, group_id=review.group_id)
        return ReviewerId(role_id=review.role_id, group_id=None)

    @staticmethod
    def from_request_row(request: DBRow) -> ReviewerId:
        if request.to_group_id:
            return ReviewerId(role_id=None, group_id=request.to_group_id)
        return ReviewerId(role_id=request.to_role_id, group_id=None)


@dataclass(frozen=True)
class Recipient:
    """Represents a review request recipient"""

    reviewer: ReviewerId
    initiator_role_id: str
    source: ReviewRequestSource | None = None

    @property
    def id(self) -> str:
        if self.is_role:
            return cast(str, self.reviewer.role_id)
        return cast(str, self.reviewer.group_id)

    @property
    def is_role(self) -> bool:
        return self.reviewer.role_id is not None

    @property
    def is_group(self) -> bool:
        return self.reviewer.group_id is not None


@dataclass(frozen=True)
class RecipientRoleId:
    """Represents a recipient role identifier"""

    id: str
    source: ReviewRequestSource | None = None

    @property
    def is_assigned_from_template(self) -> bool:
        return self.source == ReviewRequestSource.template


class ReviewState:
    reviews: list[DBRow]

    def __init__(
        self,
        reviews: list[DBRow],
        requests: list[DBRow],
        status: ReviewStatusDB | None,
        settings: ReviewSetting | None,
        document_version: DocumentVersion | None,
        statuses_versions: list[ReviewStatusDB],
    ) -> None:
        # Current document version
        self.document_version = document_version

        # Use it with caution, it contains all reviews for all document versions
        self._reviews_versions = reviews

        # Review for current document version
        self.reviews = reviews
        if self.document_version:
            # Remove reviews from other document versions
            self.reviews = [r for r in reviews if r.document_version_id == self.document_version.id]

        self.settings = settings

        self.requests = requests
        if self.is_ordered:
            # sorted from the lowest to the highest
            self.requests = sorted(self.requests, key=lambda r: r.order or -1)

        # General status of the review process
        self.status = status.status if status else None

        # For each document version, we have a separate review status. This list contains the
        # statuses for previous document versions. Here we store all statuses, including the
        # current one.
        self.statuses_versions = statuses_versions

        self._review_map = {ReviewerId.from_review_row(review): review for review in self.reviews}

    @property
    def is_empty(self) -> bool:
        """Condition in which we consider whole review state is empty"""

        # If user doesn't have review request or requests consider it as empty
        if not self.requests and not self.reviews:
            return True

        # If user doesn't have review requests and all reviews is canceled/withdrawn
        # REMINDER: When review.type is None it means that user decide to withdraw
        # their own review.
        if not self.requests and all(review.type is None for review in self.reviews):
            return True

        # Otherwise review is not empty
        return False

    @property
    def document_version_id(self) -> str | None:
        return self.document_version.id if self.document_version else None

    def calc_review_status(self) -> ReviewStatus:
        """Get new review status after reviews update"""
        assert self.reviews or self.requests, 'Review should not be empty'

        request_groups = set()
        request_roles = set()
        approved_groups = set()
        approved_roles = set()

        for request in self.requests:
            if request.to_role_id:
                request_roles.add(request.to_role_id)
            if request.to_group_id:
                request_groups.add(request.to_group_id)

        for review in self.reviews:
            # If at least one review has been rejected,
            # then we consider the whole review process as rejected
            if review.type == ReviewType.reject:
                return ReviewStatus.rejected

            if review.type == ReviewType.approve:
                if review.role_id:
                    approved_roles.add(review.role_id)
                if review.group_id:
                    approved_groups.add(review.group_id)

        # Compare which roles are expected to review and which roles
        # have already approved this document.
        if request_roles - approved_roles or request_groups - approved_groups:
            return ReviewStatus.pending

        return ReviewStatus.approved

    def calc_next_review_requests(self) -> list[str]:
        """
        Get a list of next review requests that is waiting for review.
        """

        done_by_groups = set()
        done_by_roles = set()
        for review in (r for r in self.reviews if r.type):
            if review.role_id:
                done_by_roles.add(review.role_id)
            if review.group_id:
                done_by_groups.add(review.group_id)

        next_requests: list[str] = []
        # requests are ordered in __init__ method
        for request in self.requests:
            # Skip deleted requests
            if request.status != ReviewRequestStatus.active:
                continue

            # skip already reviewed requests
            if request.to_role_id in done_by_roles or request.to_group_id in done_by_groups:
                continue

            # For ordered review get first not reviewed request
            if self.is_ordered:
                next_requests.append(request.id)
                break

            # For parallel review get all not reviewed requests
            if self.is_parallel:
                next_requests.append(request.id)

        return next_requests

    @property
    def is_parallel(self) -> bool:
        return bool(s.is_parallel) if (s := self.settings) else True

    @property
    def is_ordered(self) -> bool:
        return not self.is_parallel

    @property
    def is_required(self) -> bool:
        return bool(self.settings and self.settings.is_required)

    @property
    def review_request_notification_recipients(self) -> list[Recipient]:
        # Remove already reviewed review requests
        ids = [ReviewerId.from_review_row(reviewer) for reviewer in self.reviews]
        requests = [r for r in self.requests if ReviewerId(r.to_role_id, r.to_group_id) not in ids]
        recipients = [
            Recipient(
                reviewer=ReviewerId(request.to_role_id, request.to_group_id),
                initiator_role_id=request.from_role_id,
                source=request.source,
            )
            for request in requests
        ]

        if not self.is_ordered:
            # In case of parallel review all roles from review requests
            # should receive notification
            return recipients

        # For ordered review get first not reviewed review request
        return recipients[:1]

    def get_review(self, role_id: str | None, group_id: str | None) -> DBRow | None:
        return self._review_map.get(ReviewerId(role_id=role_id, group_id=group_id))


class ReplaceReviewRequestsCtx(NamedTuple):
    document: Document
    reviewers: list[ReviewsInfoCtx]
    reviewers_source: ReviewRequestSource
    is_ordered: bool
    is_required: bool
    state: ReviewState

    @property
    def roles_ids(self) -> list[str]:
        """
        Flatten roles from signer_entities.
        """
        res = []
        for signer in self.reviewers:
            if signer.is_role and signer.role_id:
                res.append(signer.role_id)
            elif signer.is_group:
                res.extend(signer.group_role_ids)

        return res


@dataclass(frozen=True)
class FinishedReviewRecipient:
    """
    Row selected from the database for sending an email to the review assigner and document
    uploaders about the end of the review process finished for a single document.

    We consider that the review process is finished and when all reviews are approved
    or one of the reviews is rejected.
    """

    # User id
    id: str

    document_id: str
    document_title: str
    document_number: str

    user_email: str
    user_first_name: str | None
    user_language: Language | None

    role_id: str

    company_id: str
    company_edrpou: str
    company_name: str | None

    @staticmethod
    def from_row(row: DBRow) -> FinishedReviewRecipient:
        return FinishedReviewRecipient(
            id=row.user_id,
            document_id=row.document_id,
            document_title=row.document_title,
            document_number=row.document_number,
            user_email=row.user_email,
            user_first_name=row.user_first_name,
            user_language=row.user_language,
            role_id=row.role_id,
            company_id=row.company_id,
            company_edrpou=row.company_edrpou,
            company_name=row.company_name,
        )


@dataclass
class ReviewRejecterForEmail:
    email: str
    first_name: str | None
    second_name: str | None
    last_name: str | None
    company_id: str

    @staticmethod
    def from_dict(rejecter: DataDict) -> ReviewRejecterForEmail:
        return ReviewRejecterForEmail(
            email=rejecter['email'],
            first_name=rejecter['first_name'],
            second_name=rejecter['second_name'],
            last_name=rejecter['last_name'],
            company_id=rejecter['company_id'],
        )


@dataclass
class ReviewStatusDB:
    id: str
    document_id: str
    document_version_id: str | None
    edrpou: str
    status: ReviewStatus
    is_last: bool
    next_review_requests: list[str]
    date_created: datetime.datetime
    date_updated: datetime.datetime

    @staticmethod
    def from_row(row: DBRow) -> ReviewStatusDB:
        return ReviewStatusDB(
            id=row.id,
            document_id=row.document_id,
            document_version_id=row.document_version_id,
            edrpou=row.edrpou,
            status=row.status,
            is_last=row.is_last,
            next_review_requests=row.next_review_requests,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )


@dataclass
class ReviewSetting:
    id: str
    document_id: str
    company_id: str
    is_required: bool
    is_parallel: bool | None
    date_created: datetime.datetime
    date_updated: datetime.datetime

    @staticmethod
    def from_row(row: DBRow) -> ReviewSetting:
        return ReviewSetting(
            id=row.id,
            document_id=row.document_id,
            company_id=row.company_id,
            is_required=row.is_required,
            is_parallel=row.is_parallel,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )


@dataclass
class ReviewRequest:
    id: str
    document_id: str
    document_version_id: str | None
    from_role_id: str
    to_role_id: str | None
    to_group_id: str | None
    order: str | None
    status: ReviewRequestStatus
    date_created: datetime.datetime
    date_updated: datetime.datetime

    @staticmethod
    def from_row(row: DBRow) -> ReviewRequest:
        return ReviewRequest(
            id=row.id,
            document_id=row.document_id,
            document_version_id=row.document_version_id,
            from_role_id=row.from_role_id,
            to_role_id=row.to_role_id,
            to_group_id=row.to_group_id,
            order=row.order,
            status=row.status,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )


@dataclass
class ReviewStatusDBExtended(ReviewStatusDB):
    """Review status with settings"""

    is_required: bool

    @staticmethod
    def from_row(row: DBRow) -> ReviewStatusDBExtended:
        return ReviewStatusDBExtended(
            # status columns
            id=row.id,
            document_id=row.document_id,
            document_version_id=row.document_version_id,
            edrpou=row.edrpou,
            status=row.status,
            is_last=row.is_last,
            next_review_requests=row.next_review_requests,
            date_created=row.date_created,
            date_updated=row.date_updated,
            # settings columns
            is_required=row.is_required,
        )

    @property
    def is_approved(self) -> bool:
        return self.status == ReviewStatus.approved


@dataclass(frozen=True)
class ReviewsInfoCtx:
    class EntityType(NamedEnum):
        group = auto()
        role = auto()

    entity: EntityType
    # role
    role_id: str | None
    # group
    group_id: str | None
    group_role_ids: list[str]

    @property
    def is_group(self) -> bool:
        return self.entity == self.EntityType.group

    @property
    def is_role(self) -> bool:
        return self.entity == self.EntityType.role


@dataclass(frozen=True)
class ReviewRequestsAndReviews:
    """Dataclass for representation union review_request_table and review_table"""

    document_id: str
    from_role_id: str
    to_role_id: str | None
    to_group_id: str | None
    order: int | None
    status: ReviewRequestStatus

    review_id: str | None
    review_role_id: str | None
    review_group_id: str | None
    review_type: ReviewType | None

    @staticmethod
    def from_row(row: DBRow) -> ReviewRequestsAndReviews:
        return ReviewRequestsAndReviews(
            # Review-request data
            document_id=row.document_id,
            from_role_id=row.from_role_id,
            to_role_id=row.to_role_id,
            to_group_id=row.to_group_id,
            order=row.order,
            status=row.status,
            # Review data, if join exist
            review_id=row.review_id,
            review_role_id=row.review_role_id,
            review_group_id=row.review_group_id,
            review_type=row.review_type,
        )


@dataclass
class AcceptedReviewNotificationRecipient:
    email: str
    first_name: str | None
    company_id: str | None
    company_edrpou: str | None
    company_name: str | None
    language: Language | None


@dataclass
class AcceptedReviewNotificationDocument:
    document_id: str
    document_title: str | None
    document_number: str | None
