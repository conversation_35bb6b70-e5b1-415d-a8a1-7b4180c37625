from operator import eq

import sqlalchemy as sa

from app.auth.tables import company_table
from app.models import columns, metadata
from app.reviews.enums import (
    ReviewRequestSource,
    ReviewRequestStatus,
    ReviewSource,
    ReviewStatus,
    ReviewType,
)

REVIEW_REQUEST_INDEX_COLUMNS = ('document_id', 'to_role_id')
REVIEW_SETTING_CONSTRAINT = 'uix_reviews_settings_doc_id_comp_id'


review_table = sa.Table(
    'reviews',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
    ),
    # Versioned document always will have document_version_id, even first version
    # this value populates here app.reviews.db.insert_review
    # Each new version of the document will create own set of reviews and review process
    # should be started from the beginning. But we keep old reviews for the history and
    # ability to restore old version of the document
    columns.ForeignKey(
        'document_version_id',
        'document_versions.id',
        nullable=True,
        ondelete='CASCADE',
        index=True,
    ),
    columns.ForeignKey(
        'role_id',
        'roles.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
    ),
    # Email of the user who reviewed the document. This field is stored here to preserve email at
    # the time of signing. When a user changes email, this field should not be updated.
    columns.Email('user_email', nullable=True),
    # Can be singed by role (role_id = uuid4 and group_id = None)
    # or by group (role_id = uuid4 and group_id = uuid4)
    columns.ForeignKey('group_id', 'groups.id', nullable=True, ondelete='NO ACTION'),
    # approve/reject of review or null, which means that a user cancels own a review
    columns.Enum(
        'type',
        ReviewType,
        nullable=True,
    ),
    columns.SoftEnum('source', ReviewSource, nullable=True),
    columns.Boolean('is_last', default_value=True),
    columns.DateCreated(),
    sa.Index('idx_reviews_group_id', 'group_id', postgresql_concurrently=True),
)


review_request_table = sa.Table(
    'review_requests',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        index=True,
        nullable=False,
        ondelete='NO ACTION',
    ),
    # Versioned document always will have document_version_id, even first version
    # this value populates here app.reviews.db.insert_review
    #
    # Unlike reviews and review_statuses, when user uploads a new version of the document
    # we don't reset review requests. Previous review requests are still actual
    # for the new version of the document and should be reviewed by the same roles.
    # So, while processing review requests, do not filter out the old review requests
    # by document_version_id
    columns.ForeignKey(
        'document_version_id',
        'document_versions.id',
        nullable=True,
        ondelete='CASCADE',
        index=True,
    ),
    columns.ForeignKey(
        'from_role_id',
        'roles.id',
        nullable=False,
        ondelete='NO ACTION',
    ),
    columns.ForeignKey(
        'to_role_id',
        'roles.id',
        nullable=True,
        ondelete='NO ACTION',
    ),
    # Review request can be assigned to a role (to_role_id is value and group_id is None)
    # or to a group (to_role_id is None and group_id is value). (but not both)
    columns.ForeignKey('to_group_id', 'groups.id', nullable=True, ondelete='NO ACTION'),
    sa.Column('order', sa.SmallInteger(), nullable=True),
    # We need to manage notifications according to a review request source
    columns.SoftEnum('source', ReviewRequestSource, nullable=True),
    # Active/deleted review request
    # Pay attention that it's not a result of the review for given request
    columns.Enum(
        'status',
        ReviewRequestStatus,
        ReviewRequestStatus.active,
    ),
    columns.DateCreated(),
    columns.DateUpdated(),
    sa.UniqueConstraint(
        *REVIEW_REQUEST_INDEX_COLUMNS,
        name='uix_review_requests_document_id_to_role_id',
    ),
    sa.Index('idx_review_requests_to_group_id', 'to_group_id', postgresql_concurrently=True),
    sa.CheckConstraint(
        'to_role_id IS NULL AND group_id IS NOT NULL '
        'OR '
        'to_role_id IS NOT NULL AND group_id IS NULL',
        name='ck_document_reviewers_role_id_xor_group_id',
    ),
    sa.Index(
        'idx_review_requests_date_created',
        'date_created',
        postgresql_concurrently=True,
    ),
)


review_status_table = sa.Table(
    'reviews_statuses',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        index=True,
        nullable=False,
        ondelete='NO ACTION',
    ),
    # Versioned document always will have document_version_id, even first version
    # this value populates here app.reviews.db.insert_review
    # Each new version of the document will create own set of reviews and review process
    # should be started from the beginning
    columns.ForeignKey(
        'document_version_id',
        'document_versions.id',
        nullable=True,
        ondelete='CASCADE',
        index=True,
    ),
    columns.EDRPOU(nullable=False),
    columns.Enum(
        'status',
        ReviewStatus,
        nullable=False,
        enum_name='review_status',
    ),
    # Is this review for the last version of the document?
    columns.Boolean('is_last', default_value=True),
    # JSON array of the next review requests (review_requests.id)
    columns.JSONB('next_review_requests', server_default=sa.text("'[]'")),
    columns.DateCreated(),
    columns.DateUpdated(),
)

sa.Index(
    'uix_reviews_statuses_document_id_edrpou_document_version_id',
    review_status_table.c.document_id,
    review_status_table.c.edrpou,
    review_status_table.c.document_version_id,
    unique=True,
    postgresql_where=review_status_table.c.document_version_id.isnot(None),
)
sa.Index(
    'uix_reviews_statuses_document_id_edrpou_doc_version_id_null',
    review_status_table.c.document_id,
    review_status_table.c.edrpou,
    unique=True,
    postgresql_where=review_status_table.c.document_version_id.is_(None),
)
sa.Index(
    'ix_reviews_statuses_date_updated',
    review_status_table.c.date_updated,
    postgresql_concurrently=True,
)

review_setting_table = sa.Table(
    'reviews_settings',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        index=True,
        nullable=False,
        ondelete='NO ACTION',
    ),
    columns.ForeignKey(
        'company_id',
        'companies.id',
        nullable=False,
        ondelete='NO ACTION',
    ),
    # finished review is required for sign document
    columns.Boolean('is_required'),
    columns.Boolean('is_parallel', default=True, nullable=True),
    columns.DateCreated(),
    columns.DateUpdated(),
    sa.UniqueConstraint(
        'document_id',
        'company_id',
        name=REVIEW_SETTING_CONSTRAINT,
    ),
)


review_status_company_setting_join = review_status_table.join(
    company_table,
    review_status_table.c.edrpou == company_table.c.edrpou,
).outerjoin(
    review_setting_table,
    sa.and_(
        eq(
            review_status_table.c.document_id,
            review_setting_table.c.document_id,
        ),
        eq(
            review_setting_table.c.company_id,
            company_table.c.id,
        ),
    ),
)
