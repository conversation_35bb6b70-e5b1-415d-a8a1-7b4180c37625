from enum import Enum, unique

from app.i18n import _


@unique
class ReviewSource(Enum):
    web = 'web'
    mobile = 'mobile'


@unique
class ReviewType(Enum):
    approve = 'approve'
    reject = 'reject'


@unique
class ReviewTypeAction(Enum):
    approve = _('Погодження підтверджено')
    reject = _('Погодження відмовлено')
    empty = _('Погодження видалено')


@unique
class ReviewRequestStatus(Enum):
    active = 'active'
    deleted = 'deleted'


@unique
class ReviewRequestSource(Enum):
    api = 'api'
    web = 'web'
    template = 'template'


@unique
class ReviewRequestStatusAction(Enum):
    active = _('Запрошення на погодження створено')
    deleted = _('Запрошення на погодження видалено')


@unique
class ReviewStatus(Enum):
    pending = 'pending'
    approved = 'approved'
    rejected = 'rejected'
