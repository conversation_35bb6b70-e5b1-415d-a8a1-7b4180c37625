import datetime
from operator import eq

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.auth.enums import RoleStatus
from app.auth.tables import (
    company_table,
    role_table,
    user_table,
)
from app.auth.types import User
from app.documents.tables import document_table
from app.groups.tables import group_table
from app.lib.database import DBConnection, DBRow
from app.lib.types import (
    DataDict,
    StrList,
)
from app.models import (
    select_all,
    select_one,
)
from app.reviews.enums import (
    ReviewRequestStatus,
    ReviewStatus,
)
from app.reviews.tables import (
    REVIEW_REQUEST_INDEX_COLUMNS,
    REVIEW_SETTING_CONSTRAINT,
    review_request_table,
    review_setting_table,
    review_status_company_setting_join,
    review_status_table,
    review_table,
)
from app.reviews.types import (
    FinishedReviewRecipient,
    ReviewRequest,
    ReviewRequestsAndReviews,
    ReviewSetting,
    ReviewStatusDB,
    ReviewStatusDBExtended,
)
from app.uploads.types import ReviewRequestData


async def insert_document_reviews(conn: DBConnection, data: list[DataDict]) -> list[DBRow]:
    """
    Filter exist reviews and mark as is_last=True, and insert new reviews
    """
    if not data:
        return []

    document_id: str = data[0]['document_id']
    document_version_id: str | None = data[0].get('document_version_id')
    group_ids: list[str] = [i['group_id'] for i in data if i.get('group_id')]
    role_id: str = data[0]['role_id']

    async with conn.begin():
        await conn.execute(
            review_table.update()
            .values(is_last=False)
            .where(
                sa.and_(
                    review_table.c.is_last == sa.true(),
                    review_table.c.document_id == document_id,
                    review_table.c.document_version_id == document_version_id,
                    sa.or_(
                        review_table.c.group_id.in_(group_ids),
                        review_table.c.role_id == role_id,
                    ),
                )
            )
        )
        reviews = await select_all(
            conn,
            (
                insert(review_table)
                .values(data)
                .returning(
                    review_table.c.id,
                    review_table.c.document_id,
                    review_table.c.role_id,
                    review_table.c.user_email,
                    review_table.c.group_id,
                    review_table.c.type,
                    review_table.c.document_version_id,
                )
            ),
        )
    return reviews


async def select_review(conn: DBConnection, review_id: str) -> DBRow:
    query = review_table.select().where(review_table.c.id == review_id)

    return await select_one(conn, query)


async def select_review_status_by_document_id(
    conn: DBConnection,
    *,
    company_edrpou: str,
    document_id: str,
    version_id: str | None,
) -> DBRow:
    filters = sa.and_(
        review_status_table.c.document_id == document_id,
        review_status_table.c.edrpou == company_edrpou,
    )
    if version_id:
        filters = sa.and_(
            filters,
            review_status_table.c.document_version_id == version_id,
        )

    query = (
        sa.select(
            [
                review_status_table,
                review_setting_table.c.is_required,
            ]
        )
        .select_from(
            review_status_table.join(
                company_table,
                company_table.c.edrpou == review_status_table.c.edrpou,
            ).outerjoin(
                review_setting_table,
                sa.and_(
                    review_setting_table.c.company_id == company_table.c.id,
                    eq(
                        review_setting_table.c.document_id,
                        review_status_table.c.document_id,
                    ),
                ),
            )
        )
        .where(filters)
        # For versioned documents, there can be multiple statuses for each version. In this case,
        # the developer should pass "version_id" to this function to select status for the specific
        # version.
        # However, if "version_id" is not provided due to a developer's mistake, let's return the
        # latest status with the assumption that it will represent the status of review for the
        # latest version. It is not always the correct answer, but it is better than returning
        # status for a random version.
        .order_by(review_status_table.c.date_updated.desc())
    )

    return await select_one(conn, query)


async def select_reviews_statuses(
    conn: DBConnection,
    *,
    company_edrpou: str,
    documents_ids: list[str],
) -> list[ReviewStatusDB]:
    query = (
        sa.select([review_status_table])
        .select_from(review_status_table)
        .where(
            sa.and_(
                review_status_table.c.document_id.in_(documents_ids),
                review_status_table.c.edrpou == company_edrpou,
            )
        )
    )
    rows = await select_all(conn, query)
    return [ReviewStatusDB.from_row(row) for row in rows]


async def select_reviews_without_request(
    conn: DBConnection,
    document_ids: list[str] | set[str],
    user_role_id: str,
) -> list[DBRow]:
    exists_clause = (
        sa.exists()
        .select_from(review_request_table)
        .where(
            review_table.c.role_id == review_request_table.c.to_role_id,
        )
    )
    filters = sa.and_(
        review_table.c.role_id == user_role_id,
        review_table.c.document_id.in_(document_ids),
        review_table.c.is_last == sa.true(),
        review_table.c.type.isnot(None),
        review_table.c.group_id.is_(None),
        ~exists_clause,
    )

    query = sa.select([review_table]).where(filters).order_by(review_table.c.date_created.desc())

    return await select_all(conn, query)


async def select_reviews(
    conn: DBConnection,
    *,
    document_ids: StrList | None = None,
    company_edrpou: str | None = None,
    last_reviews_only: bool = True,
    with_cancellations: bool = False,
    document_with_version_ids: list[tuple[str, str | None]] | None = None,
    group_ids: StrList | None = None,
) -> list[DBRow]:
    reviews_cte_filters = []

    if last_reviews_only:
        reviews_cte_filters.append(review_table.c.is_last.is_(True))

    if not with_cancellations:
        reviews_cte_filters.append(review_table.c.type.isnot(None))

    if group_ids:
        reviews_cte_filters.append(review_table.c.group_id.in_(group_ids))

    if document_with_version_ids:
        # version_id may be None, so we can't use `in_` here
        # see: build_company_id_filter for more details
        _filters = []
        for document_id, version_id in document_with_version_ids:
            if version_id is None:
                _filters.append(
                    sa.and_(
                        review_table.c.document_id == document_id,
                        review_table.c.document_version_id.is_(None),
                    )
                )
            else:
                _filters.append(
                    sa.and_(
                        review_table.c.document_id == document_id,
                        review_table.c.document_version_id == version_id,
                    )
                )
        reviews_cte_filters.append(sa.or_(*_filters))

    if document_ids:
        reviews_cte_filters.append(review_table.c.document_id.in_(document_ids))

    assert reviews_cte_filters, 'At least one filter should be provided'

    # filters reviews by documents + additional filters, because each document have reviews
    # only from few companies (from 1 to ~100). So, we narrow down the search space first and
    # then filter by company in the next query. Before this optimization, query planner
    # was doing filtering by company first, then by role, and then by document, which was
    # very slow.
    documents_reviews_cte = (
        sa.select([review_table]).where(sa.and_(*reviews_cte_filters)).cte('documents_reviews')
    )

    company_filter = [sa.true()]
    if company_edrpou:
        company_filter.append(company_table.c.edrpou == company_edrpou)

    query = (
        sa.select(
            [
                documents_reviews_cte,
                company_table.c.edrpou,
                company_table.c.id.label('company_id'),
            ]
        )
        .select_from(
            documents_reviews_cte.outerjoin(
                role_table, documents_reviews_cte.c.role_id == role_table.c.id
            ).join(company_table, role_table.c.company_id == company_table.c.id)
        )
        .where(sa.and_(*company_filter))
        .order_by(documents_reviews_cte.c.date_created.desc())
    )

    return await select_all(conn, query)


async def select_viewer_reviews(
    conn: DBConnection,
    *,
    document_id: str,
    version_id: str | None,
    company_edrpou: str,
) -> list[DBRow]:
    reviews_cte_filters = [
        review_table.c.document_id == document_id,
        review_table.c.is_last.is_(True),
        review_table.c.type.isnot(None),
    ]
    if version_id:
        reviews_cte_filters.append(review_table.c.document_version_id == version_id)

    # filters reviews by documents + additional filters, because each document have reviews
    # only from few companies (from 1 to ~100). So, we narrow down the search space first and
    # then filter by company in the next query. Before this optimization, query planner
    # was doing filtering by company first, then by role, and then by document, which was
    # very slow.
    documents_reviews_cte = (
        sa.select([review_table]).where(sa.and_(*reviews_cte_filters)).cte('documents_reviews')
    )

    # Join with companies only in the second part
    query = (
        sa.select(
            [
                user_table.c.first_name,
                user_table.c.second_name,
                user_table.c.last_name,
                documents_reviews_cte,
                documents_reviews_cte.c.user_email.label('email'),
                company_table.c.edrpou,
                company_table.c.id.label('company_id'),
                role_table.c.position,
            ]
        )
        .select_from(
            documents_reviews_cte.join(
                role_table,
                documents_reviews_cte.c.role_id == role_table.c.id,
            )
            .join(
                company_table,
                role_table.c.company_id == company_table.c.id,
            )
            .join(
                user_table,
                user_table.c.id == role_table.c.user_id,
            )
        )
        .where(company_table.c.edrpou == company_edrpou)
        .order_by(documents_reviews_cte.c.date_created.desc())
    )

    return await select_all(conn, query)


async def insert_review_request(conn: DBConnection, data: DataDict | ReviewRequestData) -> DBRow:
    # TODO: Remove this after fix duplicates in review-request history
    #  and added new database constraint (DOC-6209)
    if data.get('to_group_id'):
        tmp_query = (
            sa.select([review_request_table])
            .where(
                sa.and_(
                    review_request_table.c.document_id == data['document_id'],
                    review_request_table.c.to_role_id == data.get('to_role_id'),
                    review_request_table.c.to_group_id == data.get('to_group_id'),
                )
            )
            .order_by(review_request_table.c.date_created.desc())
        )
        latest_review_request_from_group = await select_one(conn, tmp_query)
        if latest_review_request_from_group:
            data = {
                'status': data['status'],
                'date_updated': sa.text('now()'),
                'order': data.get('order'),
                'source': data.get('source'),
            }
            return await select_one(
                conn,
                review_request_table.update()
                .values(data)
                .where(review_request_table.c.id == latest_review_request_from_group.id)
                .returning(
                    review_request_table.c.id,
                    review_request_table.c.document_id,
                    review_request_table.c.to_role_id,
                    review_request_table.c.to_group_id,
                ),
            )

    query = (
        insert(review_request_table)
        .values(**data)
        .returning(
            review_request_table.c.id,
            review_request_table.c.document_id,
            review_request_table.c.to_role_id,
        )
    )

    query = query.on_conflict_do_update(
        index_elements=REVIEW_REQUEST_INDEX_COLUMNS,
        set_={
            'status': query.excluded.status,
            'date_updated': sa.text('now()'),
            'order': data.get('order'),
            'source': data.get('source'),
        },
    )

    return await select_one(conn, query)


async def update_review_request(
    conn: DBConnection, review_request_id: str, data: DataDict
) -> DBRow:
    query = (
        review_request_table.update()
        .values(**data)
        .returning(review_request_table.c.id, review_request_table.c.document_id)
        .where(review_request_table.c.id == review_request_id)
    )
    return await select_one(conn, query)


async def update_review_requests_by_document(
    conn: DBConnection,
    *,
    document_id: str,
    data: DataDict,
) -> None:
    await conn.execute(
        review_request_table.update()
        .values(**data)
        .where(review_request_table.c.document_id == document_id)
    )


async def update_reviews_by_document(
    conn: DBConnection,
    *,
    document_id: str,
    data: DataDict,
) -> None:
    await conn.execute(
        review_table.update().values(**data).where(review_table.c.document_id == document_id)
    )


async def update_review_statuses_by_document(
    conn: DBConnection,
    *,
    document_id: str,
    data: DataDict,
) -> None:
    await conn.execute(
        review_status_table.update()
        .values(**data)
        .where(review_status_table.c.document_id == document_id)
    )


async def mark_as_deleted_review_requests(
    conn: DBConnection,
    document_id: str,
    roles_ids: list[str],
    group_ids: list[str],
) -> None:
    """
    Marks previous review requests as deleted before adding new ones
    """
    query = (
        review_request_table.update()
        .values(status=ReviewRequestStatus.deleted.value)
        .returning(review_request_table.c.id)
        .where(
            sa.and_(
                review_request_table.c.document_id == document_id,
                sa.or_(
                    review_request_table.c.to_role_id.in_(roles_ids),
                    review_request_table.c.to_group_id.in_(group_ids),
                ),
            )
        )
    )
    await select_one(conn, query)


async def select_review_request(
    conn: DBConnection,
    review_request_id: str,
) -> ReviewRequest | None:
    query = review_request_table.select().where(review_request_table.c.id == review_request_id)
    row = await select_one(conn, query)
    return ReviewRequest.from_row(row) if row else None


async def select_review_request_by_to_role_id_or_to_group_id(
    conn: DBConnection,
    document_id: str,
    *,
    to_role_id: str | None = None,
    to_group_id: str | None = None,
) -> ReviewRequest | None:
    filters = sa.and_(review_request_table.c.document_id == document_id)

    if to_role_id:
        filters = sa.and_(filters, review_request_table.c.to_role_id == to_role_id)
    elif to_group_id:
        filters = sa.and_(filters, review_request_table.c.to_group_id == to_group_id)
    else:
        raise ValueError('to_role_id or to_group_id must be provided')

    query = review_request_table.select().where(filters)
    row = await select_one(conn, query)
    return ReviewRequest.from_row(row) if row else None


async def select_review_requests_by_version_id(
    conn: DBConnection,
    document_version_id: str,
) -> list[ReviewRequest]:
    query = review_request_table.select().where(
        sa.and_(
            review_request_table.c.document_version_id == document_version_id,
        )
    )
    rows = await select_all(conn, query)
    return [ReviewRequest.from_row(row) for row in rows]


async def select_recipients_for_accepted_reviews_notification(
    conn: DBConnection,
    time_ago: datetime.datetime,
) -> list[FinishedReviewRecipient]:
    """
    Select documents that accepted by all coworkers before a specific date

    Recipients are already filtered by "can_receive" flags, so there is no need to check them
    again later in the code.
    """

    # This CTE will be used for both review assigners and document uploaders and expected to
    # execute only once
    _cte_approved = (
        sa.select([review_status_table.c.document_id, review_status_table.c.edrpou])
        .select_from(review_status_table)
        .where(
            sa.and_(
                review_status_table.c.status == ReviewStatus.approved,
                review_status_table.c.date_updated > time_ago,
            )
        )
        .cte('approved_documents')
    )

    selectable = [
        document_table.c.id.label('document_id'),
        document_table.c.title.label('document_title'),
        document_table.c.number.label('document_number'),
        user_table.c.id.label('user_id'),
        user_table.c.first_name.label('user_first_name'),
        user_table.c.second_name.label('user_language'),
        user_table.c.email.label('user_email'),
        role_table.c.id.label('role_id'),
        role_table.c.company_id.label('company_id'),
        company_table.c.edrpou.label('company_edrpou'),
        company_table.c.name.label('company_name'),
    ]

    # Users who assign someone to a review process
    review_assigners_query = (
        sa.select(selectable)
        .select_from(
            _cte_approved.join(
                review_request_table,
                review_request_table.c.document_id == _cte_approved.c.document_id,
            )
            .join(document_table, document_table.c.id == review_request_table.c.document_id)
            .join(role_table, role_table.c.id == review_request_table.c.from_role_id)
            .join(user_table, user_table.c.id == role_table.c.user_id)
            .join(
                company_table,
                sa.and_(
                    company_table.c.id == role_table.c.company_id,
                    company_table.c.edrpou == _cte_approved.c.edrpou,
                ),
            )
        )
        .where(
            sa.and_(
                role_table.c.status == RoleStatus.active,
                role_table.c.can_receive_review_process_finished_assigner.is_(True),
            )
        )
    )

    # Users who uploaded the document
    document_uploaders_query = (
        sa.select(selectable)
        .select_from(
            _cte_approved.join(document_table, document_table.c.id == _cte_approved.c.document_id)
            .join(role_table, role_table.c.id == document_table.c.uploaded_by)
            .join(user_table, user_table.c.id == role_table.c.user_id)
            .join(
                company_table,
                sa.and_(
                    company_table.c.id == role_table.c.company_id,
                    company_table.c.edrpou == _cte_approved.c.edrpou,
                ),
            )
        )
        .where(
            sa.and_(
                role_table.c.status == RoleStatus.active,
                role_table.c.can_receive_review_process_finished.is_(True),
            )
        )
    )

    query = sa.union(
        review_assigners_query,
        document_uploaders_query,
    )
    rows = await select_all(conn, query)

    return [FinishedReviewRecipient.from_row(row) for row in rows]


async def select_review_requests(
    conn: DBConnection,
    company_id: str,
    document_ids: StrList | None = None,
    *,
    is_all_requests: bool = False,
    sort_by_order: bool = False,
    group_ids: StrList | None = None,
) -> list[DBRow]:
    document_ids = document_ids or []

    filters = sa.or_(
        role_table.c.company_id == company_id,
        group_table.c.company_id == company_id,
    )

    if not is_all_requests:
        filters = sa.and_(filters, review_request_table.c.status == ReviewRequestStatus.active)

    if document_ids:
        filters = sa.and_(filters, review_request_table.c.document_id.in_(document_ids))

    if group_ids:
        filters = sa.and_(filters, review_request_table.c.to_group_id.in_(group_ids))

    query = (
        sa.select([review_request_table])
        .select_from(
            review_request_table.outerjoin(
                role_table, role_table.c.id == review_request_table.c.to_role_id
            ).outerjoin(group_table, group_table.c.id == review_request_table.c.to_group_id)
        )
        .where(filters)
    )

    if sort_by_order:
        query = query.order_by(review_request_table.c.order.asc())
    else:
        query = query.order_by(review_request_table.c.date_created.asc())

    return await select_all(conn, query)


async def select_review_requests_join_reviews(
    conn: DBConnection,
    document_ids: list[str] | set[str],
    company_id: str,
    document_versions_ids: list[str] | set[str],
) -> list[ReviewRequestsAndReviews]:
    filtered_review_requests_cte = (
        sa.select([review_request_table])
        .select_from(review_request_table)
        .where(
            sa.and_(
                review_request_table.c.document_id.in_(document_ids),
                review_request_table.c.status == ReviewRequestStatus.active,
            ),
        )
        .cte('filtered_review_requests')
    )

    reviews_filters = sa.and_(
        review_table.c.document_id.in_(document_ids),
        review_table.c.is_last.is_(True),
    )

    if document_versions_ids:
        reviews_filters = sa.and_(
            reviews_filters, review_table.c.document_version_id.in_(document_versions_ids)
        )

    filtered_reviews_cte = (
        sa.select([review_table])
        .select_from(review_table)
        .where(reviews_filters)
        .cte('filtered_reviews')
    )

    # Define the main query
    main_query = (
        sa.select(
            [
                filtered_review_requests_cte.c.id,
                filtered_review_requests_cte.c.document_id,
                filtered_review_requests_cte.c.document_version_id,
                filtered_review_requests_cte.c.from_role_id,
                filtered_review_requests_cte.c.to_role_id,
                filtered_review_requests_cte.c.to_group_id,
                filtered_review_requests_cte.c.order,
                filtered_review_requests_cte.c.status,
                filtered_review_requests_cte.c.date_created,
                filtered_review_requests_cte.c.date_updated,
                filtered_reviews_cte.c.id.label('review_id'),
                filtered_reviews_cte.c.role_id.label('review_role_id'),
                filtered_reviews_cte.c.group_id.label('review_group_id'),
                filtered_reviews_cte.c.type.label('review_type'),
            ]
        )
        .select_from(
            filtered_review_requests_cte.outerjoin(
                role_table,
                sa.and_(
                    role_table.c.id == filtered_review_requests_cte.c.to_role_id,
                    role_table.c.company_id == company_id,
                ),
            )
            .outerjoin(
                group_table,
                sa.and_(
                    group_table.c.id == filtered_review_requests_cte.c.to_group_id,
                    group_table.c.company_id == company_id,
                ),
            )
            .outerjoin(
                filtered_reviews_cte,
                sa.or_(
                    sa.and_(
                        filtered_review_requests_cte.c.to_group_id
                        == filtered_reviews_cte.c.group_id,
                        filtered_reviews_cte.c.group_id.isnot(None),
                        filtered_review_requests_cte.c.document_id
                        == filtered_reviews_cte.c.document_id,
                    ),
                    sa.and_(
                        filtered_review_requests_cte.c.to_role_id == filtered_reviews_cte.c.role_id,
                        filtered_reviews_cte.c.group_id.is_(None),
                        filtered_review_requests_cte.c.document_id
                        == filtered_reviews_cte.c.document_id,
                    ),
                ),
            )
        )
        .where(
            sa.or_(
                group_table.c.company_id == company_id,
                role_table.c.company_id == company_id,
            )
        )
        .order_by(filtered_review_requests_cte.c.order.asc())
    )

    result = await select_all(conn, main_query)
    return [ReviewRequestsAndReviews.from_row(item) for item in result]


async def select_review_request_for_history(
    conn: DBConnection,
    company_id: str,
    document_id: str,
    *,
    user_from_email: str | None = None,
    user_to_email: str | None = None,
    only_active: bool = False,
) -> list[DBRow]:
    role_to = sa.alias(role_table, name='role_to')
    role_from = sa.alias(role_table, name='role_from')

    user_to = sa.alias(user_table, name='user_to')
    user_from = sa.alias(user_table, name='user_from')

    filters = sa.and_(
        review_request_table.c.document_id == document_id,
        role_from.c.company_id == company_id,
    )

    if only_active:
        filters = sa.and_(filters, review_request_table.c.status == ReviewRequestStatus.active)

    if user_from_email:
        filters = sa.and_(filters, user_from.c.email == user_from_email)

    if user_to_email:
        filters = sa.and_(filters, user_to.c.email == user_to_email)

    # TODO: Try to optimize this query ?
    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    review_request_table.c.status,
                    review_request_table.c.date_created,
                    review_request_table.c.to_group_id,
                    group_table.c.name.label('group_name'),
                    user_to.c.first_name.label('user_to_first_name'),
                    user_to.c.second_name.label('user_to_second_name'),
                    user_to.c.last_name.label('user_to_last_name'),
                    user_to.c.email.label('user_to_email'),
                    user_from.c.first_name.label('user_from_first_name'),
                    user_from.c.second_name.label('user_from_second_name'),
                    user_from.c.last_name.label('user_from_last_name'),
                    user_from.c.email.label('user_from_email'),
                ]
            )
            .select_from(
                review_request_table.outerjoin(
                    role_to, role_to.c.id == review_request_table.c.to_role_id
                )
                .outerjoin(user_to, role_to.c.user_id == user_to.c.id)
                .outerjoin(group_table, review_request_table.c.to_group_id == group_table.c.id)
                .join(role_from, role_from.c.id == review_request_table.c.from_role_id)
                .join(user_from, role_from.c.user_id == user_from.c.id)
            )
            .where(filters)
        ),
    )


async def select_reviews_for_history(
    conn: DBConnection,
    company_id: str,
    document_id: str,
    *,
    user_email: str | None = None,
    only_last: bool = False,
) -> list[DBRow]:
    filters = sa.and_(
        review_table.c.document_id == document_id,
        role_table.c.company_id == company_id,
    )

    if only_last:
        filters = sa.and_(filters, review_table.c.is_last == sa.true())

    if user_email:
        filters = sa.and_(filters, user_table.c.email == user_email)

    return await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    review_table.c.type,
                    review_table.c.date_created,
                    review_table.c.is_last,
                    review_table.c.group_id,
                    group_table.c.name.label('group_name'),
                    user_table.c.email.label('user_email'),
                    user_table.c.first_name.label('user_first_name'),
                    user_table.c.second_name.label('user_second_name'),
                    user_table.c.last_name.label('user_last_name'),
                ]
            )
            .select_from(
                review_table.join(role_table, role_table.c.id == review_table.c.role_id)
                .join(user_table, role_table.c.user_id == user_table.c.id)
                .outerjoin(group_table, review_table.c.group_id == group_table.c.id)
            )
            .where(filters)
        ),
    )


async def update_review_setting(
    conn: DBConnection,
    user: User,
    data: DataDict,
) -> None:
    setting_data = dict(
        data,
        company_id=user.company_id,
    )
    await conn.execute(
        insert(review_setting_table)
        .values(setting_data)
        .on_conflict_do_update(
            constraint=REVIEW_SETTING_CONSTRAINT,
            set_=setting_data,
        )
    )


async def insert_review_settings(
    conn: DBConnection,
    data: list[DataDict],
) -> None:
    """
    Insert multiple review settings into DB.
    """
    await conn.execute(insert(review_setting_table).values(data))


async def select_recipients_for_rejected_reviews_notification(
    conn: DBConnection,
    company_id: str,
    document_ids: list[str],
) -> list[FinishedReviewRecipient]:
    """
    Select recipients for rejected reviews notification

    Recipients are already filtered by "can_receive" flags, so there is no need to check them
    again later in the code.
    """

    _documents_cte = (
        sa.select(
            [
                document_table.c.id,
                document_table.c.number,
                document_table.c.title,
                document_table.c.uploaded_by,
            ]
        )
        .select_from(document_table)
        .where(document_table.c.id.in_(document_ids))
        .cte('rejected_documents')
    )

    selectable = [
        _documents_cte.c.id.label('document_id'),
        _documents_cte.c.title.label('document_title'),
        _documents_cte.c.number.label('document_number'),
        user_table.c.id.label('user_id'),
        user_table.c.email.label('user_email'),
        user_table.c.first_name.label('user_first_name'),
        user_table.c.second_name.label('user_language'),
        role_table.c.id.label('role_id'),
        role_table.c.company_id.label('company_id'),
        company_table.c.edrpou.label('company_edrpou'),
        company_table.c.name.label('company_name'),
    ]

    # Users who assigned reviewers to the document
    review_assigners_query = (
        sa.select(selectable)
        .select_from(
            _documents_cte.join(
                review_request_table,
                review_request_table.c.document_id == _documents_cte.c.id,
            )
            .join(role_table, role_table.c.id == review_request_table.c.from_role_id)
            .join(user_table, user_table.c.id == role_table.c.user_id)
            .join(company_table, company_table.c.id == role_table.c.company_id)
        )
        .where(
            sa.and_(
                # Please note that can_receive_rejects is used both when the user rejects a
                # document in the review process and when the recipient rejects the signing of
                # the document. It's unclear why it is used for both cases, but if you plan to
                # change it, make sure it doesn't break the logic for the rejection of the
                # signing process
                role_table.c.can_receive_rejects.is_(True),
                role_table.c.company_id == company_id,
            )
        )
    )

    # Users who uploaded the document
    document_uploaders_query = (
        sa.select(selectable)
        .select_from(
            _documents_cte.join(role_table, role_table.c.id == _documents_cte.c.uploaded_by)
            .join(user_table, role_table.c.user_id == user_table.c.id)
            .join(company_table, company_table.c.id == role_table.c.company_id)
        )
        .where(
            sa.and_(
                role_table.c.can_receive_rejects.is_(True),
                role_table.c.company_id == company_id,
            )
        )
    )

    query = sa.union(
        review_assigners_query,
        document_uploaders_query,
    )
    rows = await select_all(conn, query)
    return [FinishedReviewRecipient.from_row(row) for row in rows]


async def select_review_statuses(
    conn: DBConnection,
    *,
    document_id: str,
) -> list[ReviewStatusDBExtended]:
    """
    Select review statuses for document.
    NOTE: every company has its own review status & settings.
    """
    rows = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    review_status_table,
                    review_setting_table.c.is_required,
                ]
            )
            .select_from(review_status_company_setting_join)
            .where(review_status_table.c.document_id == document_id)
        ),
    )
    return [ReviewStatusDBExtended.from_row(row) for row in rows]


async def select_reviews_requests_by_documents_ids(
    conn: DBConnection, documents_ids: list[str], sort_by_date: bool = False
) -> list[DBRow]:
    query = (
        sa.select(
            [
                review_request_table,
                company_table.c.edrpou,
            ]
        )
        .select_from(
            review_request_table.join(
                role_table, role_table.c.id == review_request_table.c.from_role_id
            ).join(company_table, company_table.c.id == role_table.c.company_id)
        )
        .where(review_request_table.c.document_id.in_(documents_ids))
    )
    if sort_by_date:
        query = query.order_by(review_request_table.c.date_created)
    return await select_all(conn, query)


async def select_review_requests_by_date(
    conn: DBConnection,
    company_id: str,
    date_from: datetime.datetime,
    date_to: datetime.datetime,
) -> list[DBRow]:
    filters = sa.and_(
        company_table.c.id == company_id,
        review_request_table.c.date_created > date_from,
        review_request_table.c.date_updated < date_to,
    )

    query = (
        sa.select([review_request_table])
        .select_from(
            review_request_table.join(
                role_table, role_table.c.id == review_request_table.c.from_role_id
            ).join(company_table, company_table.c.id == role_table.c.company_id)
        )
        .where(filters)
    )
    return await select_all(conn, query)


async def select_reviews_by_date(
    conn: DBConnection,
    company_id: str,
    date_from: datetime.datetime,
    date_to: datetime.datetime,
) -> list[DBRow]:
    filters = sa.and_(
        company_table.c.id == company_id,
        review_table.c.date_created > date_from,
        review_table.c.date_created < date_to,
    )

    query = (
        sa.select([review_table])
        .select_from(
            review_table.join(role_table, role_table.c.id == review_table.c.role_id).join(
                company_table, company_table.c.id == role_table.c.company_id
            )
        )
        .where(filters)
    )
    return await select_all(conn, query)


async def select_reviews_statuses_by_documents_ids(
    conn: DBConnection,
    documents_ids: list[str],
) -> list[DBRow]:
    query = (
        sa.select([review_status_table, review_setting_table.c.is_required])
        .select_from(
            review_status_table.outerjoin(
                review_setting_table,
                review_status_table.c.document_id == review_setting_table.c.document_id,
            ).outerjoin(
                company_table,
                review_setting_table.c.company_id == company_table.c.id,
            )
        )
        .where(
            sa.and_(
                review_status_table.c.document_id.in_(documents_ids),
                review_status_table.c.edrpou == company_table.c.edrpou,
            )
        )
    )
    return await select_all(conn, query)


async def select_reviews_settings(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    company_id: str,
) -> list[ReviewSetting]:
    """Get review settings for given documents"""
    query = review_setting_table.select().where(
        sa.and_(
            review_setting_table.c.document_id.in_(documents_ids),
            review_setting_table.c.company_id == company_id,
        )
    )
    rows = await select_all(conn, query)
    return [ReviewSetting.from_row(row) for row in rows]


async def select_review_setting(
    conn: DBConnection,
    *,
    document_id: str,
    company_id: str,
) -> ReviewSetting | None:
    """Get review settings for given document"""
    settings = await select_reviews_settings(
        conn=conn,
        documents_ids=[document_id],
        company_id=company_id,
    )
    return settings[0] if settings else None


async def upsert_reviews_statuses(conn: DBConnection, data: list[DataDict]) -> None:
    """
    Insert or update review status for multiple documents. Data should be a list
    of dicts with edrpou, document ID, status, date_updated and document_version_id keys
    """

    without_version_id = [row for row in data if row.get('document_version_id') is None]
    with_version_id = [row for row in data if row.get('document_version_id') is not None]

    update_dict = {
        'status': sa.text('excluded.status'),
        'date_updated': sa.text('now()'),
        'next_review_requests': sa.text('excluded.next_review_requests'),
        'is_last': sa.text('excluded.is_last'),
    }

    if without_version_id:
        stmt = insert(review_status_table).values(without_version_id)
        stmt = stmt.on_conflict_do_update(
            index_elements=[
                review_status_table.c.document_id,
                review_status_table.c.edrpou,
            ],
            index_where=review_status_table.c.document_version_id.is_(None),
            set_=update_dict,
        )
        await conn.execute(stmt)
    if with_version_id:
        stmt = insert(review_status_table).values(with_version_id)
        stmt = stmt.on_conflict_do_update(
            index_elements=[
                review_status_table.c.document_id,
                review_status_table.c.edrpou,
                review_status_table.c.document_version_id,
            ],
            index_where=review_status_table.c.document_version_id.isnot(None),
            set_=update_dict,
        )
        await conn.execute(stmt)


async def update_review_statuses(
    conn: DBConnection,
    *,
    statuses_ids: list[str],
    data: DataDict,
) -> None:
    await conn.execute(
        review_status_table.update().values(data).where(review_status_table.c.id.in_(statuses_ids))
    )


async def delete_review_for_document_version(conn: DBConnection, version_id: str) -> None:
    await conn.execute(
        review_table.delete().where(review_table.c.document_version_id == version_id)
    )


async def delete_review_requests_for_document_version(conn: DBConnection, version_id: str) -> None:
    await conn.execute(
        review_request_table.delete().where(
            review_request_table.c.document_version_id == version_id
        )
    )


async def select_pending_review_for_group(
    conn: DBConnection, *, group_ids: list[str], company_edrpou: str
) -> list[DBRow]:
    """
    Select list of document IDs
    where group is assigned as reviewer and review is not finished yet.
    """
    query = (
        sa.select([review_request_table.c.document_id, review_request_table.c.to_group_id])
        .select_from(
            review_request_table.join(
                review_status_table,
                review_request_table.c.document_id == review_status_table.c.document_id,
            )
        )
        .where(
            sa.and_(
                review_status_table.c.status == ReviewStatus.pending,
                review_request_table.c.to_group_id.in_(group_ids),
                review_status_table.c.edrpou == company_edrpou,
            )
        )
    )
    return await select_all(conn, query)
