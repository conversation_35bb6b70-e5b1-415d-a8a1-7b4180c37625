import copy
import logging
import typing as t
from http import HTTPStatus

import pydantic

from api.errors import AccessDenied, Code, DoesNotExist, Error, Object
from api.public.validators import DeleteReviewRequestsSchema
from app.auth.db import select_role
from app.auth.types import AuthUser, User
from app.auth.utils import has_permission
from app.auth.validators import (
    validate_review_request_groups,
    validate_review_request_order,
    validate_review_request_roles,
    validate_user_permission,
)
from app.billing.enums import CompanyPermission
from app.billing.utils import get_billing_company_config, is_allowed_by_rate
from app.documents.db import select_documents_by_ids, select_documents_ids_with_access
from app.documents.validators import (
    ReviewsSettingsSchema,
    validate_document_access,
    validate_document_exists,
)
from app.groups.db import select_group
from app.groups.utils import get_group_members_by_group_ids
from app.i18n import _
from app.i18n.types import <PERSON>zyI18nString
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection, DBRow
from app.lib.helpers import group_list
from app.lib.types import DataDict
from app.profile.validators import validate_company_permission
from app.reviews.db import (
    select_review_request_by_to_role_id_or_to_group_id,
    select_review_requests_join_reviews,
    select_reviews_without_request,
)
from app.reviews.enums import ReviewRequestSource, ReviewSource, ReviewType
from app.reviews.types import (
    ReplaceReviewRequestsCtx,
    ReviewRequest,
    ReviewRequestsAndReviews,
    ReviewsInfoCtx,
    ReviewState,
)
from app.reviews.utils import (
    convert_reviewers,
    get_next_review_request_order,
    get_review_state,
    is_required_reviews_approved,
    separate_reviewers,
)

logger = logging.getLogger(__name__)


class ReviewSchema(pydantic.BaseModel):
    document_id: pv.UUID
    role_id: pv.UUID
    user_email: pv.Email | None
    type: ReviewType | None = None

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json')


class DownloadReviewHistorySchema(pydantic.BaseModel):
    document_id: pv.UUID
    format: t.Literal['pdf', 'xlsx']


async def validate_review_requests_enabled(
    conn: DBConnection,
    *,
    company_id: str,
    company_edrpou: str,
) -> None:
    await validate_company_permission(
        conn=conn,
        company_id=company_id,
        company_edrpou=company_edrpou,
        permission=CompanyPermission.reviews,
    )


async def _validate_reviews_enabled(conn: DBConnection, company_id: str) -> None:
    """
    Validate company permission to do reviews.
    Ignore this validation if user has been already assigned for a review request.
    """
    billing_config = await get_billing_company_config(conn, company_id=company_id)
    reviews_enabled = is_allowed_by_rate(billing_config, CompanyPermission.reviews)
    if not reviews_enabled:
        raise AccessDenied(reason=_('Погодження документів не доступне на поточному тарифі'))


def _validate_review_request_delete_access(request: ReviewRequest, user: User) -> None:
    is_participant = user.role_id in {request.from_role_id, request.to_role_id}
    if not (is_participant or has_permission(user, {'can_change_document_signers_and_reviewers'})):
        raise AccessDenied(
            reason=_(
                'Видалити запит на погодження може лише адміністратор компанії, '
                'автор погодження або співробітник, якому цей запит було надіслано'
            ),
        )


async def validate_add_review(conn: DBConnection, data: DataDict, user: User) -> DataDict:
    valid_data = validators.validate_pydantic(ReviewSchema, data)
    # TODO: We need validate_document_exists? Same as validate_document_access,
    #  but another HTTPstatus ?
    await validate_document_exists(conn, data)
    await validate_document_access(conn, user, document_id=valid_data.document_id)
    await _validate_reviews_enabled(conn, company_id=user.company_id)
    return valid_data.to_dict()


async def prepare_next_reviews(
    conn: DBConnection,
    data: DataDict,
    user: User,
    document_version_id: str | None,
    source: ReviewSource | None = None,
) -> list[DataDict]:
    """
    Select related data from database and calculate next reviews data.
    Return list with single or multiple (if group presents in review-requests) reviews
    to single document
    """
    document_id: str = data['document_id']
    document_versions_ids = [document_version_id] if document_version_id else []
    reviews_without_request = []

    requests_with_reviews = await select_review_requests_join_reviews(
        conn, [document_id], user.company_id, document_versions_ids
    )
    if requests_with_reviews and requests_with_reviews[0].order:
        reviews_without_request = await select_reviews_without_request(
            conn,
            user_role_id=user.role_id,
            document_ids=[document_id],
        )

    next_reviewers = await validate_next_reviewers(
        conn,
        data=data,
        user=user,
        requests_with_reviews=requests_with_reviews,
        reviews_without_request=reviews_without_request,
    )

    for reviewer in next_reviewers:
        reviewer.update(**data, document_version_id=document_version_id, source=source)
    return next_reviewers


async def validate_next_reviewers(
    conn: DBConnection,
    data: DataDict,
    user: User,
    requests_with_reviews: list[ReviewRequestsAndReviews],
    reviews_without_request: list[DBRow],
) -> list[DataDict]:
    """
    Perform validation of document next reviewers

    Returns:
        List[DataDict]: list of next_reviewers

    Raises:
        AccessDenied: If there are no valid reviewers found
    """

    group_request_ids = [
        request.to_group_id for request in requests_with_reviews if request.to_group_id
    ]
    group_role_map = await get_group_members_by_group_ids(
        conn, company_id=user.company_id, group_ids=group_request_ids
    )
    next_reviewers = get_document_next_reviewers(
        user=user,
        requests=requests_with_reviews,
        group_role_map=group_role_map,
        reviews_without_request=reviews_without_request,
    )

    if not next_reviewers:
        raise AccessDenied(reason=_('Документ повинен погодити інший співробітник'))

    for reviewer in next_reviewers:
        reviewer.update(data)
    return next_reviewers


def get_document_next_reviewers(
    user: User,
    requests: list[ReviewRequestsAndReviews],
    group_role_map: DataDict,
    reviews_without_request: list[DBRow],
) -> list[DataDict]:
    """
    Obtain a list of reviewers for a given document in a review process.

    This function handles situations,
     where a user is a reviewer as an individual or as a group member.
    It's updating all user reviews with a single action.
    It also covers scenarios where a user is a group member,
     but the review request from that group originated from another user.

    Examples:
    Returns list of groups witch will be added to review on user's behalf.
    - [{'group_id': 'g1'}, {'group_id': 'g2'}]
      In database:
      reviews(role_id=r1, group_id=g1)
      reviews(role_id=r1, group_id=g2)
    - For [{'group_id': None}] it means that role is reviewer without any group
      In database:
      reviews(role_id=r1, group_id=None)
    - But when returns [] - it means that user can't add review

    Behaviour:
    - If there are no review requests, the user is allowed to add a review.
    - If a review request matches the user role or group, the user is listed as the next reviewer.
    - If there are no reviewers and no specific order is set, the user is allowed to add a review.

    Returns:
    next_reviewers: List[DataDict]
    Each dictionary contains only single key 'group_id',
        which could be None if the user is directly a reviewer.
    """
    if not requests:
        return [{'group_id': None}]

    next_reviewers: list[DataDict] = []
    order = None

    for request in requests:
        order = request.order

        if request.to_role_id == user.role_id:
            next_reviewers.append({'group_id': None})
        elif (
            request.to_group_id
            and user.role_id in group_role_map[request.to_group_id]
            and (request.review_role_id in (None, user.role_id) or not request.review_type)
        ):
            next_reviewers.append({'group_id': request.to_group_id})
        # in case when review ordered and item(review) exist in next_reviewers,
        # we don't stop iteration to process case when u1 is member of g1, g2
        # reviews given ordered review: g1, u2, g2
        # so u1 should review g1 and g2 only once
        # Remember: there review will be marked as reviewed by u1 even
        # if u1 had been removed from g2 when it's u2 turn to review
        elif request.order and not request.review_id and not next_reviewers:
            break

    for review_without_request in reviews_without_request:
        if review_without_request.role_id == user.role_id:
            next_reviewers.append({'group_id': None})
            break

    # Case where user not in reviewers, and order is None, we allow leave a review
    if not next_reviewers and order is None:
        next_reviewers.append({'group_id': None})

    return next_reviewers


def _validate_reviews_batch_data(
    raw_reviews_data: list[DataDict],
    user: User,
    source: ReviewSource | None = None,
) -> DataDict:
    """
    Raise api.errors.InvalidRequest exception (400 http status) if one of item is invalid
    """
    valid_reviews: DataDict = {}

    for item in raw_reviews_data:
        valid_data = validators.validate_pydantic(
            ReviewSchema,
            {
                'document_id': item.get('document_id'),
                'role_id': user.role_id,
                'user_email': user.email,
                'type': item.get('type'),
                'source': source,
            },
        )
        document_id: str = valid_data.document_id
        valid_reviews[document_id] = {'data': valid_data.to_dict()}

    return valid_reviews


async def _validate_documents_exist(conn: DBConnection, reviews: dict[str, DataDict]) -> set[str]:
    """
    Checks that each document in reviews data exists.

    Modifies:
    - Adds a 'result' field to each review associated with a non-existent document.
    Returns:
    - A set of ids of the existing documents.
    """
    all_document_ids = list(reviews.keys())
    exist_documents = await select_documents_by_ids(conn, all_document_ids)
    exist_document_ids = {item['id'] for item in exist_documents}

    # Add result info if document not exists
    for document_id, valid_review in reviews.items():
        if document_id not in exist_document_ids:
            valid_review['result'] = {
                'document_id': document_id,
                'status': HTTPStatus.NOT_FOUND,
            }
    return exist_document_ids


async def _validate_documents_access(
    conn: DBConnection,
    reviews: dict[str, DataDict],
    exist_document_ids: set[str],
    user: User,
) -> set[str]:
    """
    Checks the user's access to each document associated with a review.

    Returns:
    - A set of ids of the documents to which the user has access.

    Modifies:
    - Adds 'result' key to each review, which the user doesn't have access.
    """
    documents_ids_with_access = await select_documents_ids_with_access(
        conn=conn,
        user=user,
        documents_ids=list(exist_document_ids),
    )
    unique_documents_ids_with_access = set(documents_ids_with_access)

    # Add result info if user hasn't access to document
    for document_id in exist_document_ids:
        if document_id not in unique_documents_ids_with_access:
            reviews[document_id]['result'] = {
                'document_id': document_id,
                'status': HTTPStatus.FORBIDDEN,
            }
    return unique_documents_ids_with_access


async def validate_add_reviews_batch(
    conn: DBConnection,
    raw_reviews_data: list[DataDict],
    user: User,
    source: ReviewSource | None = None,
) -> tuple[DataDict, list[str]]:
    await _validate_reviews_enabled(conn, company_id=user.company_id)
    reviews = _validate_reviews_batch_data(
        raw_reviews_data=raw_reviews_data,
        user=user,
        source=source,
    )
    exist_document_ids = await _validate_documents_exist(conn, reviews)
    documents_ids_with_access = await _validate_documents_access(
        conn=conn,
        reviews=reviews,
        exist_document_ids=exist_document_ids,
        user=user,
    )

    return reviews, list(documents_ids_with_access)


async def validate_state_add_reviews_batch(
    conn: DBConnection,
    reviews_map: DataDict,
    previous_states: dict[str, ReviewState],
    *,
    document_versions_ids: list[str],
    documents_ids_with_access: list[str],
    user: User,
) -> DataDict:
    """
    Validates a batch of reviews.

    *Use consecutively, after app.reviews.validators.validate_add_reviews_batch

    The function handles next negative cases:
     - the user is not in the approvers list
     - it is not our turn to approve the document

    The function also handles approval for the groups.
    If there is a user as well as a group in the approvers, he is a member,
    then clicking agree will add 2 approvals (from user and from group)

    """
    # TODO: rewrite this function to strict validation or skip bad results,
    #       because as practice told no one wants to support the multi-status result
    # TODO: Need refactor for more readable and simple code
    # TODO: Return dataclass instead DataDict
    next_reviewers_map: dict[str, list[DataDict]] = {}

    requests_with_reviews = await select_review_requests_join_reviews(
        conn,
        document_ids=documents_ids_with_access,
        company_id=user.company_id,
        document_versions_ids=document_versions_ids,
    )
    requests_with_reviews_map = group_list(requests_with_reviews, lambda d: d.document_id)

    reviews_without_request = await select_reviews_without_request(
        conn, user_role_id=user.role_id, document_ids=documents_ids_with_access
    )
    reviews_without_request_map = group_list(reviews_without_request, lambda d: d.document_id)

    for document_id, valid_data in reviews_map.items():
        if valid_data.get('result'):
            continue  # Skip processed documents (non-existed and without user access)
        try:
            next_reviewers_map[document_id] = await validate_next_reviewers(
                conn,
                data=valid_data['data'],
                user=user,
                requests_with_reviews=requests_with_reviews_map[document_id],
                reviews_without_request=reviews_without_request_map[document_id],
            )
        except Exception as e:
            valid_data['result'] = {
                'document_id': document_id,
                'status': e.status,  # type: ignore
            }

        reviews_state: ReviewState = previous_states[document_id]

        reviewers = next_reviewers_map.get(document_id)
        if reviewers and reviews_state.document_version:
            for reviewer in reviewers:
                reviewer['document_version_id'] = reviews_state.document_version.id

    return next_reviewers_map


async def validate_order_review_request(state: ReviewState, is_parallel: bool) -> None:
    """Validate review request order."""
    if not is_parallel and state.reviews and not state.requests:
        raise AccessDenied(
            reason=_(
                'Для створення послідовного погодження необхідно видалити всі попередні погодження'
            )
        )
    if state.requests and state.is_parallel != is_parallel:
        raise AccessDenied(
            reason=_(
                'Для зміни типу погодження(послідовне/паралельне) необхідно '
                'видалити всі попередні погодження'
            )
        )


async def validate_add_review_request_state(
    user: User, state: ReviewState, is_parallel: bool
) -> int | None:
    await validate_order_review_request(state=state, is_parallel=is_parallel)
    order = None
    is_initiator = any(req.from_role_id == user.role_id for req in state.requests)
    if state.requests and not (
        is_initiator or has_permission(user, {'can_change_document_signers_and_reviewers'})
    ):
        raise AccessDenied(reason=_('Інший користувач вже призначив погодження для документу'))

    if not is_parallel:
        order = get_next_review_request_order(state.requests)
    return order


async def validate_entity_already_in_reviewers(
    state: ReviewState, reviewer_info: ReviewsInfoCtx
) -> None:
    if reviewer_info.is_role:
        entity_id = reviewer_info.role_id
        key = 'to_role_id'
    else:
        entity_id = reviewer_info.group_id
        key = 'to_group_id'

    for request in state.requests:
        to_entity_id = getattr(request, key)
        if to_entity_id == entity_id:
            raise AccessDenied(reason=_('Цей користувач/група вже є у списку погоджувачів'))


async def validate_delete_review_request(
    conn: DBConnection,
    data: DataDict,
    user: User,
) -> ReviewRequest:
    valid_data = validators.validate_pydantic(DeleteReviewRequestsSchema, data)
    document_id = valid_data.document_id
    to_role_id, to_group_id = None, None

    if user_email := valid_data.user_to_email:
        role = await select_role(conn, user_email=user_email, company_id=user.company_id)
        if not role:
            raise DoesNotExist(Object.user, email=user_email)
        to_role_id = role.id_

    else:
        group_name = valid_data.group_to_name
        group = await select_group(
            conn=conn,
            name=group_name,
            company_id=user.company_id,
        )
        if not group:
            raise DoesNotExist(Object.group, group_name=group_name)
        to_group_id = group.id

    review_request = await select_review_request_by_to_role_id_or_to_group_id(
        conn,
        document_id,
        to_role_id=to_role_id,
        to_group_id=to_group_id,
    )
    if not review_request:
        raise DoesNotExist(Object.review_request)

    _validate_review_request_delete_access(review_request, user=user)
    await validate_document_access(conn, user, document_id)

    return review_request


async def validate_required_review_for_document(
    conn: DBConnection,
    edrpou: str,
    document_id: str,
    action: LazyI18nString,
    version_id: str | None,
) -> None:
    """Validate the ability to sign a document with review request"""

    is_approved = await is_required_reviews_approved(
        conn=conn,
        edrpou=edrpou,
        document_id=document_id,
        version_id=version_id,
    )

    if not is_approved:
        raise Error(Code.review_is_required, details={'action': action})


def validate_delete_review_request_state(
    request: ReviewRequest, user: User, state: ReviewState
) -> None:
    """
    Validate ability to delete a review request.
    User cannot cancel review request with corresponding review existing.
    """
    # Duplicate
    review = state.get_review(role_id=request.to_role_id, group_id=request.to_group_id)
    if review:
        raise AccessDenied(
            reason=_('Неможливо видалити запит на погодження, якщо його вже погоджено'),
        )
    if request.order is not None:
        _validate_delete_ordered_review_request(request, user)
    else:
        _validate_delete_parallel_review_request(request, user)


def validate_user_removal_permissions(request: ReviewRequest, user: AuthUser | User) -> None:
    """
    Who can remove user from review request:
    - Author of review request
    - Administrator
    - User can remove himself (with can_remove_itself_from_approval)

    """
    is_initiator = user.role_id == request.from_role_id
    if has_permission(user, {'can_change_document_signers_and_reviewers'}) or is_initiator:
        return
    if user.role_id == request.to_role_id:
        validate_user_permission(user, {'can_remove_itself_from_approval'})
        return

    raise AccessDenied(
        reason=_(
            'Видалити запит на погодження може лише адміністратор компанії, або автор погодження'
        )
    )


def _validate_delete_ordered_review_request(request: ReviewRequest, user: User) -> None:
    """
    Validate ability to delete an ordered review request.
    User cannot cancel review request assigned to him if he is not the assigner.
    """
    if request.order is None:
        return
    if request.to_role_id == user.role_id and request.from_role_id != user.role_id:
        raise AccessDenied(
            reason=_(
                'Неможливо видалити себе з послідовного погодження, якщо Ви не є його ініціатором'
            ),
        )


def _validate_delete_parallel_review_request(request: ReviewRequest, user: User) -> None:
    """
    Validate ability to delete a parallel review request.
    User cannot cancel review request assigned to him.
    """
    if request.to_role_id == user.role_id and request.from_role_id != user.role_id:
        raise AccessDenied(
            reason=_(
                'Неможливо видалити себе з паралельного погодження, якщо Ви не є його ініціатором'
            ),
        )


def validate_review_request_deletion_possibility(
    state: ReviewState, role_ids: set[str], group_ids: set[str]
) -> None:
    """
    Raise AccessDenied: If review-request has been approved/reject
     and its role or group is not in the list of new approvers
    """
    for request in state.requests:
        review = state.get_review(role_id=request.to_role_id, group_id=request.to_group_id)
        if (
            review
            and review.type
            and (
                (review.group_id and review.group_id not in group_ids)
                or (not review.group_id and review.role_id not in role_ids)
            )
        ):
            raise AccessDenied(
                reason=_('Неможливо видалити запит на погодження, якщо його вже погоджено')
            )


def validate_requests_user_removal_permissions(
    state: ReviewState, role_ids: set[str], group_ids: set[str], user: User
) -> set[str]:
    """
    Validates user removal permissions in state.requests
     Returns:
    exist_requests_map - Set of identifiers for existing review requests.
    Each identifier is group_id or role_id
    """
    exist_requests_map = set()
    for request in state.requests:
        if request.to_group_id:
            exist_requests_map.add(request.to_group_id)
        else:
            exist_requests_map.add(request.to_role_id)

        # If request will be deleted - validate user removal permissions
        if (request.to_group_id and request.to_group_id not in group_ids) or (
            not request.to_group_id and request.to_role_id not in role_ids
        ):
            validate_user_removal_permissions(
                request=ReviewRequest.from_row(request),
                user=user,
            )
    return exist_requests_map


async def validate_replace_review_requests(
    conn: DBConnection,
    user: User,
    document_id: str,
    data: ReviewsSettingsSchema,
    reviewers_source: ReviewRequestSource,
) -> ReplaceReviewRequestsCtx:
    """
     - Validate enabled company reviews permissions if have new reviewer in review-request
    Validate user permissions to delete review-request:
     - Only author of review request or administrator can remove people from the review request.
     - A user cannot delete review-request if it's assigned to them and they are not the initiator.
    """
    is_required = data.is_required
    is_ordered = data.is_ordered
    reviewers = separate_reviewers(data.reviewers)
    raw_role_ids = set(reviewers[ReviewsInfoCtx.EntityType.role])
    group_ids = set(reviewers[ReviewsInfoCtx.EntityType.group])

    document = await validate_document_access(conn, user, document_id=document_id)
    group_role_map = await get_group_members_by_group_ids(
        conn, group_ids=group_ids, company_id=user.company_id
    )

    state = await get_review_state(
        conn=conn,
        document_id=document_id,
        company_id=user.company_id,
        company_edrpou=user.company_edrpou,
    )

    role_ids = await validate_review_request_roles(
        conn=conn,
        state=state,
        role_ids=raw_role_ids,
        user=user,
    )
    # Exclude deactivated user roles without review
    validated_reviewers = convert_reviewers(
        reviewers=[r for r in data.reviewers if r.id in (*role_ids, *group_ids)],
        group_role_map=copy.deepcopy(group_role_map),
    )

    await validate_review_request_groups(
        conn=conn, state=state, group_ids=group_ids, user=user, group_role_map=group_role_map
    )
    validate_review_request_order(is_ordered, state, validated_reviewers)
    validate_review_request_deletion_possibility(state, role_ids, group_ids)
    exist_requests_map = validate_requests_user_removal_permissions(
        state, role_ids, group_ids, user
    )

    if exist_requests_map - group_ids - role_ids:
        # This is mean we have new review_request
        await validate_review_requests_enabled(
            conn,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
        )

    return ReplaceReviewRequestsCtx(
        document=document,
        reviewers=validated_reviewers,
        reviewers_source=reviewers_source,
        is_ordered=is_ordered,
        is_required=is_required,
        state=state,
    )
