import logging
from http import HTTPStatus

from aiohttp import web

from api.public.validators import validate_add_review_request_api
from app.actions.utils import get_source
from app.auth.types import User
from app.es.utils import send_to_indexator
from app.lib import validators
from app.lib.enums import Source
from app.lib.types import DataDict
from app.reviews import db, utils
from app.reviews.enums import ReviewRequestStatus
from app.reviews.types import ReviewState
from app.reviews.validators import (
    prepare_next_reviews,
    validate_add_review,
    validate_add_review_request_state,
    validate_add_reviews_batch,
    validate_delete_review_request,
    validate_delete_review_request_state,
    validate_entity_already_in_reviewers,
    validate_state_add_reviews_batch,
)
from app.services import services
from app.uploads.types import ReviewRequestData

logger = logging.getLogger(__name__)


async def validate_and_add_review(request: web.Request, data: DataDict, user: User) -> None:
    """
    Add a review (or multiple reviews if the user is in a group involved in the approval process)
     to a single document
    """
    async with services.db.acquire() as conn:
        valid_data = await validate_add_review(conn, data, user)
        document_id: str = valid_data['document_id']
        source = utils.get_review_request_source_from_request(request)

        async with utils.start_reviews_update_transaction(
            conn=conn,
            documents_ids=[document_id],
            user=user,
            request_source=get_source(request),
        ) as previous_states:
            # In this case we add review to single document,
            # so we always have singe document(key) in previous_state.
            document_state: ReviewState = next(iter(previous_states.values()))
            latest_document_version = (
                document_state.document_version.id if document_state.document_version else None
            )

            next_reviews = await prepare_next_reviews(
                conn=conn,
                data=valid_data,
                user=user,
                document_version_id=latest_document_version,
                source=source,
            )

            await db.insert_document_reviews(conn, next_reviews)

    await send_to_indexator(request.app['redis'], [document_id], to_slow_queue=False)


async def add_review_handler(request: web.Request, user: User) -> web.Response:
    """
    Add review (or few reviews) to single document from user/group/both (approve, reject or null)
    """
    data = await validators.validate_json_request(request)
    data['role_id'] = user.role_id
    data['user_email'] = user.email
    await validate_and_add_review(request, data, user)
    return web.json_response(status=HTTPStatus.CREATED)


async def add_reviews_batch_handler(request: web.Request, user: User) -> web.Response:
    raw_data = await validators.validate_json_request(request, dict_only=False)

    source = utils.get_review_request_source_from_request(request)

    async with request.app['db'].acquire() as conn:
        reviews_map, documents_ids_with_access = await validate_add_reviews_batch(
            conn=conn,
            raw_reviews_data=raw_data,
            user=user,
            source=source,
        )

        async with utils.start_reviews_update_transaction(
            conn=conn,
            documents_ids=documents_ids_with_access,
            user=user,
            request_source=get_source(request),
        ) as previous_states:
            # Collect all document versions ids from review states
            document_versions_ids = {
                version.id
                for state in previous_states.values()
                if (version := state.document_version)
            }

            next_reviewers_map = await validate_state_add_reviews_batch(
                conn,
                reviews_map,
                previous_states,
                document_versions_ids=list(document_versions_ids),
                documents_ids_with_access=documents_ids_with_access,
                user=user,
            )
            if next_reviewers_map:
                for document_id, next_reviewers in next_reviewers_map.items():
                    await db.insert_document_reviews(conn=conn, data=next_reviewers)
                    # Add result for correctly inserted reviews
                    reviews_map[document_id]['result'] = {
                        'document_id': document_id,
                        'status': HTTPStatus.CREATED,
                    }

    if any(bool(item.get('result')) is False for item in reviews_map.values()):
        logger.warning('Not all reviews have been processed')

    document_ids = list(next_reviewers_map.keys())

    await send_to_indexator(request.app['redis'], document_ids, to_slow_queue=False)

    return web.json_response(
        [item.get('result') for item in reviews_map.values()],
        status=HTTPStatus.MULTI_STATUS,
    )


async def delete_review(request: web.Request, user: User) -> web.Response:
    data = {
        'document_id': request.match_info['document_id'],
        'role_id': user.role_id,
        'user_email': user.email,
        'type': None,
    }
    await validate_and_add_review(request, data, user)
    return web.json_response()


async def add_review_request_api(request: web.Request, user: User) -> None:
    """
    The function get `user email`/`group name` as customer input,
    validates the role/group and add new item to the review-request

    If need change type of review-requests - customer must first delete all review-requests,
    and then create new ones using not required param 'is_parallel'
    """

    async with services.db.acquire() as conn:
        data = await validators.validate_json_request(request)
        document_id = data['document_id'] = request.match_info['document_id']
        is_parallel, reviewer_info = await validate_add_review_request_api(conn, user, data)
        request_source = get_source(request)

        async with utils.start_reviews_update_transaction(
            conn=conn,
            documents_ids=[document_id],
            user=user,
            request_source=request_source,
        ) as previous_states:
            reviews_state: ReviewState = previous_states[document_id]
            await validate_entity_already_in_reviewers(reviews_state, reviewer_info)
            order = await validate_add_review_request_state(
                user=user, state=reviews_state, is_parallel=is_parallel
            )
            document_version_id = (
                reviews_state.document_version.id if reviews_state.document_version else None
            )
            review_data = ReviewRequestData(
                document_id=document_id,
                document_version_id=document_version_id,
                from_role_id=user.role_id,
                to_role_id=reviewer_info.role_id,
                to_group_id=reviewer_info.group_id,
                order=order,
                status=ReviewRequestStatus.active,
                source=utils.get_reviewers_source(request_source),
            )
            if reviewer_info.is_role:
                await utils.add_review_request(
                    conn,
                    user=user,
                    data=review_data,
                    is_parallel=is_parallel,
                )
            elif reviewer_info.is_group:
                await utils.add_group_review_request(
                    conn,
                    user=user,
                    data=review_data,
                    group_role_ids=reviewer_info.group_role_ids,
                    is_parallel=is_parallel,
                )
            else:
                raise NotImplementedError

    await send_to_indexator(
        redis=services.redis,
        document_ids=[document_id],
        to_slow_queue=False,
    )


async def delete_review_request_base(
    user: User,
    data: DataDict,
    request_source: Source,
) -> None:
    async with services.db.acquire() as conn:
        review_request = await validate_delete_review_request(
            conn=conn,
            data=data,
            user=user,
        )
        document_id = review_request.document_id

        async with utils.start_reviews_update_transaction(
            conn=conn,
            documents_ids=[document_id],
            user=user,
            request_source=request_source,
        ) as previous_states:
            reviews_state: ReviewState = previous_states[document_id]
            validate_delete_review_request_state(review_request, user, reviews_state)

            await db.update_review_request(
                conn=conn,
                review_request_id=review_request.id,
                data={'status': ReviewRequestStatus.deleted.value},
            )

    await send_to_indexator(
        redis=services.redis,
        document_ids=[document_id],
        to_slow_queue=False,
    )
