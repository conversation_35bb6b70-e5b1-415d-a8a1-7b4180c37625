from hiku.engine import Context, pass_context

from api.graph.constants import DB_READONLY_KEY
from api.graph.types import FieldList
from app.mobile.auth.db import select_users_has_mobile_app


@pass_context
async def resolve_has_active_mobile_app(
    ctx: Context, _: FieldList, users_ids: list[str]
) -> list[list[bool]]:
    """
    Returns if user has active app installed.

    NOTE: it returns true even if user has uninstalled app but still has
    active session. So be aware of that.
    """

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        mapping = await select_users_has_mobile_app(
            conn=conn,
            users_ids=users_ids,
            use_active_filter=True,
        )

    return [[mapping[user_id] for user_id in users_ids]]


@pass_context
async def resolve_has_mobile_app(
    ctx: Context, _: FieldList, users_ids: list[str]
) -> list[list[bool]]:
    """
    Returns if user has or had mobile app installed at all, tried to login at least once.
    """

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        mapping = await select_users_has_mobile_app(conn, users_ids=users_ids)

    return [[mapping[user_id] for user_id in users_ids]]
