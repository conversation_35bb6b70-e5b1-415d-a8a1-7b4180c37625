import secrets
from http import HTTPStatus

import pytest

from app.auth import db as auth_db
from app.auth import two_factor
from app.mobile import constants
from app.mobile.tests import common as mobile_tests_common
from app.registration import types as registration_types
from app.services import services
from app.tests import common

GQL_CURRENT_ROLE = '{ currentRole { status } }'

# URLs for phone auth API
MOBILE_SEND_PHONE_AUTH_CODE_URL = '/mobile-api/v1/phone-auth/send-code'
MOBILE_PROCESS_PHONE_AUTH_CODE_URL = '/mobile-api/v1/phone-auth/process-code'

# URLs for 2FA
MOBILE_VERIFY_PHONE_2FA_URL = '/mobile-api/v1/auth/verify-2fa'
MOBILE_RESEND_PHONE_2FA_URL = '/mobile-api/v1/auth/resend-2fa'
MOBILE_GET_HIDDEN_PHONE_URL = '/mobile-api/v1/auth/2fa/hidden-phone'
MOBILE_VERIFY_EMAIL_2FA_URL = '/mobile-api/v1/auth/2fa/email/verify'
MOBILE_GET_HIDDEN_EMAIL_URL = '/mobile-api/v1/auth/2fa/email/hidden-email'


async def test_login(aiohttp_client):
    """
    Given user without 2FA enabled
    When attempting to login
    Expected user to login successfully
    """
    # Arrange
    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.VCHASNO_EDRPOU,
        password=common.TEST_USER_PASSWORD,
        phone=common.TEST_USER_PHONE,
    )

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={
            'email': user.email,
            'password': common.TEST_USER_PASSWORD,
        },
    )

    # Assert
    data = await response.json()

    # Verify that user got access token and refresh token.
    assert response.status == 200, data

    assert 'access_token' in data
    assert 'refresh_token' in data
    assert 'expires_in' in data
    assert data['is_2fa_required'] is False


async def test_login_with_2fa(aiohttp_client, monkeypatch, evo_sender_mock):
    """
    Given a user with 2FA enabled
    When attempting to login
    Expected user to receive an SMS with 2FA
    """

    async def mock_enable_2fa(conn, user, second_factor):
        return True

    # Arrange
    otp_code = '123456'
    monkeypatch.setattr(two_factor, 'has_2fa_enabled', mock_enable_2fa)
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    # 2FA enabled due to internal user rule
    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.VCHASNO_EDRPOU,
        password=common.TEST_USER_PASSWORD,
        phone=common.TEST_USER_PHONE,
    )

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={
            'email': user.email,
            'password': common.TEST_USER_PASSWORD,
        },
    )

    # Assert
    data = await response.json()

    # Verify that user got access token and refresh token.
    assert response.status == 200, data

    assert 'access_token' in data
    assert 'refresh_token' in data
    assert 'expires_in' in data
    assert data['is_2fa_required'] is True

    # Verify that SMS with code was sent.
    assert otp_code in evo_sender_mock.message
    assert user.phone == evo_sender_mock.phone


async def test_2fa_verify(aiohttp_client, monkeypatch):
    """
    Given a user with 2FA enabled and access token
    When verifying 2FA code with valid code
    Expected user to get new access token
    """
    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/verify-2fa',
        json={'code': user_data['code']},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data
    assert 'access_token' in data
    assert 'expires_in' in data


async def test_2fa_verify_invalid_code(aiohttp_client, monkeypatch):
    """
    Given a user with 2FA enabled and access token
    When attempting to verify 2FA code with invalid code
    Expected an error to be raised
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/verify-2fa',
        json={'code': '000000'},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()

    assert response.status == 403, data


async def test_2fa_resend(aiohttp_client, evo_sender_mock, monkeypatch):
    """
    Given a user with 2FA enabled and access token
    When attempting to resend 2FA code
    Expected user to receive a new code
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']
    otp_code = '654321'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/resend-2fa',
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data

    # Verify that SMS with code was sent.
    assert otp_code in evo_sender_mock.message
    assert user_data['user'].phone == evo_sender_mock.phone


async def test_refresh_2fa_required(aiohttp_client, monkeypatch):
    """
    Given a user with 2FA enabled and refresh token
    When refreshing token
    Expected user to be unable to refresh the token
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/refresh',
        json={
            'token': user_data['refresh_token'],
        },
    )

    # Assert
    data = await response.json()

    assert response.status == 403, data


async def test_refresh(aiohttp_client, monkeypatch):
    """
    Given: user with 2FA verified
    When: refresh token
    Then: user gets new access token
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']
    firebase_id = 'fake_firebase_id'

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/refresh',
        json={
            'token': user_data['refresh_token'],
            'firebase_id': firebase_id,
        },
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data
    assert 'access_token' in data
    assert 'expires_in' in data


async def test_logout(aiohttp_client, monkeypatch):
    """
    Given: user with 2FA verified
    When: logout
    Then: user can't use access token or refresh token
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/logout',
        json={
            'token': user_data['refresh_token'],
        },
    )

    # Assert
    data = await response.json()

    assert response.status == 200, data

    # Verify that user can't use access token or refresh token.
    response = await client.post(
        '/mobile-api/v1/auth/refresh',
        json={
            'token': user_data['refresh_token'],
        },
    )
    data = await response.json()
    assert response.status == 403, data

    # Also not able to use access token.
    response = await client.post(
        '/mobile-api/v1/graphql',
        json={'query': GQL_CURRENT_ROLE},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )
    data = await response.json()
    assert response.status == 403, data


@pytest.mark.parametrize(
    'include_headers, expected_status',
    [
        (False, HTTPStatus.FORBIDDEN),
        (True, HTTPStatus.OK),
    ],
)
async def test_mobile_auth_required(
    aiohttp_client,
    monkeypatch,
    include_headers,
    expected_status,
):
    """
    Given a user with 2FA verified
    When accessing route with `mobile_auth_required` decorator with/without token
    Expected error to be raised / response returned
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_verified(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/graphql',
        json={'query': GQL_CURRENT_ROLE},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']}
        if include_headers
        else None,
    )

    # Assert
    data = await response.json()
    assert response.status == expected_status, data


async def test_mobile_auth_required_2fa_required(aiohttp_client, monkeypatch):
    """
    Given a user without 2FA verified
    When accessing route with `mobile_auth_required` decorator
    Expected error to be raised
    """

    # Arrange
    user_data = await mobile_tests_common.create_user_with_2fa_required(
        aiohttp_client,
        monkeypatch,
    )
    client = user_data['client']

    # Act
    response = await client.post(
        '/mobile-api/v1/graphql',
        json={'query': GQL_CURRENT_ROLE},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    # Assert
    data = await response.json()
    assert response.status == 403, data


@pytest.mark.parametrize(
    'source, redirect_url',
    [
        (
            registration_types.RegistrationSource.mobile_edo,
            None,
        ),
        (
            registration_types.RegistrationSource.mobile_kasa,
            'https://kasa.vchasno.ua?utm_source=vchasno&utm_campaign=vchasno',
        ),
        (
            registration_types.RegistrationSource.mobile_kep,
            'https://cap.vchasno.ua?utm_source=vchasno&utm_campaign=vchasno',
        ),
        (
            registration_types.RegistrationSource.mobile_ttn,
            'https://ttn.vchasno.ua?utm_source=vchasno&utm_campaign=vchasno',
        ),
    ],
)
async def test_registration(
    aiohttp_client, source, redirect_url, mailbox, email_templates_renderer
):
    """
    Given a new user with the mobile app
    When calling registration API request
    Expected registration process to be performed correctly
    """

    # Prepare
    _, client = await common.prepare_app_client(aiohttp_client)

    # Act
    response = await client.post(
        '/mobile-api/v1/auth/register',
        json={
            'email': common.TEST_USER_EMAIL,
            'password': common.TEST_USER_PASSWORD,
            'name': common.TEST_USER_FULL_NAME,
            'phone': common.TEST_USER_PHONE,
            'redirect': redirect_url,
            'source': source.value,
        },
    )

    # Assert
    assert response.status == HTTPStatus.OK
    data = await response.json()

    # Assert user exists
    async with services.db.acquire() as conn:
        user = await auth_db.select_base_user(conn=conn, email=common.TEST_USER_EMAIL)
    assert user is not None
    assert user.source == source.value

    # Assert response JSON is valid
    assert data['user'] == {
        'id': user.id,
        'first_name': None,
        'last_name': None,
        'email': common.TEST_USER_EMAIL,
        'phone': None,
        'is_email_confirmed': False,
    }
    assert 'access_token' in data


async def test_send_phone_auth_code_mobile(aiohttp_client, evo_sender_mock):
    """
    Test sending phone auth code for mobile API - valid phone number
    """

    app, client = await common.prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    response = await client.post(
        MOBILE_SEND_PHONE_AUTH_CODE_URL,
        json={'phone': phone},
    )

    assert response.status == HTTPStatus.OK
    from app.auth.phone_auth.utils import PHONE_AUTH_REDIS_KEY_TEMPLATE

    redis_key = PHONE_AUTH_REDIS_KEY_TEMPLATE.format(phone=phone)
    code = await services.redis.get(redis_key)
    assert code is not None
    assert len(code) == 6 and code.isdigit()

    assert evo_sender_mock.message_count == 1
    assert evo_sender_mock.only_sms is True
    assert evo_sender_mock.phone == phone


async def test_send_phone_auth_code_mobile_invalid_phone(aiohttp_client, evo_sender_mock):
    """
    Test sending phone auth code with invalid phone number
    """
    app, client = await common.prepare_app_client(aiohttp_client)

    response = await client.post(
        MOBILE_SEND_PHONE_AUTH_CODE_URL,
        json={'phone': '+38099123456711'},  # Invalid phone
    )

    assert response.status == HTTPStatus.BAD_REQUEST
    data = await response.json()
    assert data['code'] == 'invalid_request'
    assert 'phone' in data['details']


async def test_send_phone_auth_code_mobile_phone_already_used(aiohttp_client, evo_sender_mock):
    """
    Test sending phone auth code when phone is already used by another user (not for auth)
    """

    app, client, user = await common.prepare_client(
        aiohttp_client,
        phone='+380991234567',  # User has this phone but not for auth
    )
    phone = '+380991234567'

    response = await client.post(
        MOBILE_SEND_PHONE_AUTH_CODE_URL,
        json={'phone': phone},
    )

    # Actually, the API allows sending code to existing phone numbers
    # but it should be on the whitelist for phone auth to work
    assert response.status == HTTPStatus.OK


async def test_process_phone_auth_mobile_new_user(
    aiohttp_client, evo_sender_mock, concierge_emulation
):
    """
    Test processing phone auth code for new user registration
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    app, client = await common.prepare_app_client(aiohttp_client)
    phone = '+380991234567'
    code = '123456'

    await set_auth_phone_otp(phone=phone, otp=code)

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': code},
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert 'access_token' in data
    assert 'refresh_token' in data
    assert 'user' in data
    assert data['user']['phone'] == phone
    assert data['is_2fa_required'] is False  # New users don't have 2FA by default


async def test_process_phone_auth_mobile_existing_user(
    aiohttp_client, evo_sender_mock, concierge_emulation
):
    """
    Test processing phone auth code for existing user login
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    app, client, user = await common.prepare_client(
        aiohttp_client,
        auth_phone='+380991234567',
    )
    phone = '+380991234567'
    code = '123456'

    await set_auth_phone_otp(phone=phone, otp=code)

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': code},
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert 'access_token' in data
    assert 'refresh_token' in data
    assert data['user']['id'] == user.id


async def test_process_phone_auth_mobile_incorrect_code(aiohttp_client, evo_sender_mock):
    """
    Test processing phone auth with incorrect code
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    app, client = await common.prepare_app_client(aiohttp_client)
    phone = '+380991234567'

    await set_auth_phone_otp(phone=phone, otp='123456')

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': '654321'},
    )

    response_data = await response.json()
    assert response.status == HTTPStatus.BAD_REQUEST
    assert response_data['code'] == 'invalid_totp_code'
    assert response_data['reason'] == 'Код невірний. Спробуйте ще раз'


async def test_process_phone_auth_mobile_with_2fa_required(
    aiohttp_client, evo_sender_mock, monkeypatch, concierge_emulation
):
    """
    Test phone auth when user has email 2FA enabled (phone login -> email 2FA)
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    # Create user with auth phone
    app, client, user = await common.prepare_client(
        aiohttp_client,
        auth_phone='+380991234567',
        password=common.TEST_USER_PASSWORD,
        is_2fa_enabled=True,
    )
    phone = '+380991234567'
    code = '123456'

    await set_auth_phone_otp(phone=phone, otp=code)

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': code},
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert data['is_2fa_required'] is True


async def test_get_hidden_email_mobile(aiohttp_client, monkeypatch):
    """
    Test getting hidden email for 2FA verification
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    app, client, user = await common.prepare_client(
        aiohttp_client,
        auth_phone='+380991234567',
        password=common.TEST_USER_PASSWORD,
        is_2fa_enabled=True,
    )

    phone = '+380991234567'
    code = '123456'
    await set_auth_phone_otp(phone=phone, otp=code)

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': code},
    )
    data = await response.json()
    access_token = data['access_token']

    response = await client.get(
        MOBILE_GET_HIDDEN_EMAIL_URL,
        headers={constants.MOBILE_AUTH_HEADER: access_token},
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert 'email' in data
    assert data['email'] == 't****r@v****.ua'


async def test_verify_email_2fa_mobile(aiohttp_client, monkeypatch, concierge_emulation):
    """
    Test verifying email 2FA after phone login
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    app, client, user = await common.prepare_client(
        aiohttp_client,
        auth_phone='+380991234567',
        password=common.TEST_USER_PASSWORD,
        is_2fa_enabled=True,
    )

    # Login with phone to get access token with pending 2FA
    phone = '+380991234567'
    code = '123456'

    await set_auth_phone_otp(phone=phone, otp=code)

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': code},
    )
    data = await response.json()
    access_token = data['access_token']
    assert data['is_2fa_required'] is True

    response = await client.post(
        MOBILE_VERIFY_EMAIL_2FA_URL,
        json={'password': common.TEST_USER_PASSWORD},
        headers={constants.MOBILE_AUTH_HEADER: access_token},
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert 'access_token' in data
    assert 'expires_in' in data


async def test_verify_email_2fa_mobile_wrong_password(aiohttp_client, monkeypatch):
    """
    Test verifying email 2FA with wrong password
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    app, client, user = await common.prepare_client(
        aiohttp_client,
        auth_phone='+380991234567',
        password=common.TEST_USER_PASSWORD,
        is_2fa_enabled=True,
    )

    phone = '+380991234567'
    code = '123456'

    await set_auth_phone_otp(phone=phone, otp=code)

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': code},
    )
    data = await response.json()
    access_token = data['access_token']

    # Try to verify with wrong password (but valid format)
    response = await client.post(
        MOBILE_VERIFY_EMAIL_2FA_URL,
        json={'password': 'wrongpass123!'},  # Valid format but wrong password
        headers={constants.MOBILE_AUTH_HEADER: access_token},
    )
    response_data = await response.json()
    assert response.status == HTTPStatus.BAD_REQUEST
    assert response_data['code'] == 'invalid_credentials'
    assert response_data['reason'] == 'Помилка в логіні або паролі'


async def test_login_with_phone_2fa_required(aiohttp_client, monkeypatch, evo_sender_mock):
    """
    Test login when user has phone 2FA enabled (email login -> phone 2FA)
    """

    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.VCHASNO_EDRPOU,
        password=common.TEST_USER_PASSWORD,
        phone=common.TEST_USER_PHONE,
        is_2fa_enabled=True,
    )

    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={
            'email': user.email,
            'password': common.TEST_USER_PASSWORD,
        },
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert data['is_2fa_required'] is True
    assert 'access_token' in data
    assert 'refresh_token' in data

    assert evo_sender_mock.message_count == 1
    assert otp_code in evo_sender_mock.message


async def test_get_hidden_phone_after_email_login(aiohttp_client, monkeypatch):
    """
    Test getting hidden phone for 2FA verification after email login
    """

    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await common.prepare_client(
        aiohttp_client,
        phone=common.TEST_USER_PHONE,
        is_2fa_enabled=True,
    )

    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={'email': user.email, 'password': common.TEST_USER_PASSWORD},
    )
    data = await response.json()
    access_token = data['access_token']

    response = await client.get(
        MOBILE_GET_HIDDEN_PHONE_URL,
        headers={constants.MOBILE_AUTH_HEADER: access_token},
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert 'phone' in data
    assert data['phone'] == '+38********89'


async def test_verify_phone_2fa_mobile(aiohttp_client, monkeypatch):
    """
    Test verifying phone 2FA after email login
    """
    user_data = await mobile_tests_common.create_user_with_2fa_required(aiohttp_client, monkeypatch)
    client = user_data['client']

    response = await client.post(
        MOBILE_VERIFY_PHONE_2FA_URL,
        json={'code': user_data['code']},
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    data = await response.json()
    assert response.status == HTTPStatus.OK, data
    assert 'access_token' in data
    assert 'expires_in' in data


async def test_verify_phone_2fa_mobile_invalid_code(aiohttp_client, monkeypatch):
    """
    Test verifying phone 2FA with invalid code
    """
    user_data = await mobile_tests_common.create_user_with_2fa_required(aiohttp_client, monkeypatch)
    client = user_data['client']

    response = await client.post(
        MOBILE_VERIFY_PHONE_2FA_URL,
        json={'code': '000000'},  # Wrong code
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )
    response_data = await response.json()
    assert response.status == HTTPStatus.FORBIDDEN
    assert response_data['code'] == 'access_denied'
    assert response_data['reason'] == 'Доступ заборонено'


async def test_resend_phone_2fa_mobile(aiohttp_client, evo_sender_mock, monkeypatch):
    """
    Test resending phone 2FA code
    """
    user_data = await mobile_tests_common.create_user_with_2fa_required(aiohttp_client, monkeypatch)
    client = user_data['client']

    response = await client.post(
        MOBILE_RESEND_PHONE_2FA_URL,
        headers={constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )

    assert response.status == HTTPStatus.OK
    assert evo_sender_mock.message_count >= 1


async def test_wrong_2fa_type_verification(aiohttp_client, monkeypatch):
    """
    Test trying to verify wrong type of 2FA (e.g., phone 2FA when email 2FA is expected)
    """
    from app.auth.phone_auth.utils import set_auth_phone_otp

    app, client, user = await common.prepare_client(
        aiohttp_client,
        auth_phone='+380991234567',
        password=common.TEST_USER_PASSWORD,
        is_2fa_enabled=True,
    )

    # Login with phone to get access token with pending email 2FA
    phone = '+380991234567'
    code = '123456'

    await set_auth_phone_otp(phone=phone, otp=code)

    response = await client.post(
        MOBILE_PROCESS_PHONE_AUTH_CODE_URL,
        json={'phone': phone, 'code': code},
    )
    data = await response.json()
    access_token = data['access_token']

    # Try to verify phone 2FA when email 2FA is expected
    response = await client.post(
        MOBILE_VERIFY_PHONE_2FA_URL,
        json={'code': '123456'},
        headers={constants.MOBILE_AUTH_HEADER: access_token},
    )

    assert response.status == HTTPStatus.BAD_REQUEST
    data = await response.json()
    assert data['code'] == 'invalid_request'
    assert data['reason'] == 'Очікується інший тип двофакторної аутентифікації'
