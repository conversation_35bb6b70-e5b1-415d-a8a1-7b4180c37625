from typing import Any

import sqlalchemy as sa
from sqlalchemy import select

from app.lib.database import DBConnection
from app.mobile import tables
from app.mobile.auth import types
from app.models import select_all, select_one
from app.models.compat import SQLAlchemyValues


async def insert_mobile_auth_refresh_token(
    *,
    conn: DBConnection,
    **values: Any,
) -> types.MobileAuthRefreshToken:
    """
    Create new entry in `mobile_auth_refresh_tokens` table
    """
    query = (
        tables.mobile_auth_refresh_tokens_table.insert()
        .values(values)
        .returning(tables.mobile_auth_refresh_tokens_table)
    )

    row = await select_one(conn, query)
    if not row:
        raise Exception('Failed to insert row')

    return types.MobileAuthRefreshToken(
        id=row['id'],
        user_id=row['user_id'],
        token_hash=row['token_hash'],
        status=row['status'],
        date_created=row['date_created'],
        date_updated=row['date_updated'],
        access_token_hash=row['access_token_hash'],
    )


async def update_mobile_auth_refresh_tokens(
    *,
    conn: DBConnection,
    id: str | None = None,
    where_user_id: str | None = None,
    access_token_hash: str | None = None,
    status: types.MobileAuthRefreshTokenStatus | None = None,
    firebase_id: str | None = None,
) -> list[types.MobileAuthRefreshToken]:
    """
    Update an entry in `mobile_auth_refresh_tokens` table
    """
    where = []
    if id is not None:
        where.append(tables.mobile_auth_refresh_tokens_table.c.id == id)
    if where_user_id is not None:
        where.append(tables.mobile_auth_refresh_tokens_table.c.user_id == where_user_id)
    assert where, 'At least one where condition must be provided'

    values = {'date_updated': sa.text('now()')}
    if access_token_hash is not None:
        values['access_token_hash'] = access_token_hash
    if status is not None:
        values['status'] = status
    if firebase_id is not None:
        values['firebase_id'] = firebase_id

    query = (
        tables.mobile_auth_refresh_tokens_table.update()
        .values(values)
        .where(*where)
        .returning(tables.mobile_auth_refresh_tokens_table)
    )

    rows = await select_all(conn, query)
    return [types.MobileAuthRefreshToken.from_row(row) for row in rows]


async def select_mobile_auth_refresh_token(
    *,
    conn: DBConnection,
    token_hash: str | None = None,
    status: types.MobileAuthRefreshTokenStatus | None = None,
) -> types.MobileAuthRefreshToken | None:
    """
    Retrieve an entry of `mobile_auth_refresh_tokens` by hash and status
    """
    where = []
    if token_hash is not None:
        where.append(tables.mobile_auth_refresh_tokens_table.c.token_hash == token_hash)
    if status is not None:
        where.append(tables.mobile_auth_refresh_tokens_table.c.status == status)

    query = select([tables.mobile_auth_refresh_tokens_table]).where(sa.and_(*where))
    row = await select_one(conn, query=query)
    return types.MobileAuthRefreshToken.from_row(row) if row else None


async def select_mobile_auth_refresh_tokens(
    *,
    conn: DBConnection,
    user_id: str | None = None,
    status: types.MobileAuthRefreshTokenStatus | None = None,
) -> list[types.MobileAuthRefreshToken]:
    """
    Retrieve all tokens of a user with specific status from database
    """
    where = []
    if user_id is not None:
        where.append(tables.mobile_auth_refresh_tokens_table.c.user_id == user_id)
    if status is not None:
        where.append(tables.mobile_auth_refresh_tokens_table.c.status == status)
    assert where, 'At least one where condition must be provided'

    query = sa.select([tables.mobile_auth_refresh_tokens_table]).where(sa.and_(*where))
    rows = await select_all(conn, query)
    return [types.MobileAuthRefreshToken.from_row(row) for row in rows]


async def select_users_has_mobile_app(
    conn: DBConnection,
    *,
    users_ids: list[str],
    use_active_filter: bool = False,
) -> dict[str, bool]:
    """
    Returns a mapping of user IDs to whether they have or had a mobile app installed.
    """

    users_values = SQLAlchemyValues(
        [sa.column('user_id', sa.String)],
        *[(user_id,) for user_id in users_ids],
        alias_name='users_values',
    )

    filters = []

    if use_active_filter:
        filters.append(
            tables.mobile_auth_refresh_tokens_table.c.status
            == types.MobileAuthRefreshTokenStatus.ready
        )

    # User IDs that have or had a mobile app installed
    query = (
        sa.select([users_values.c.user_id])
        .select_from(users_values)
        .where(
            sa.exists(
                sa.select([sa.literal(1)])
                .select_from(tables.mobile_auth_refresh_tokens_table)
                .where(
                    sa.and_(
                        tables.mobile_auth_refresh_tokens_table.c.user_id == users_values.c.user_id,
                        *filters,
                    )
                )
            )
        )
    )
    rows = await select_all(conn, query)
    users_with_app = {row.user_id for row in rows}

    return {user_id: user_id in users_with_app for user_id in users_ids}


async def select_is_user_has_mobile_app(conn: DBConnection, *, user_id: str) -> bool:
    """
    Returns if a user has or had a mobile app installed.
    """
    mapping = await select_users_has_mobile_app(conn, users_ids=[user_id])
    return mapping[user_id]
