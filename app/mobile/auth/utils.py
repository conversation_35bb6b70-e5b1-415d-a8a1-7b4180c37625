import logging
from uuid import uuid4

from aiohttp import web

from app.auth import two_factor
from app.auth import types as auth_types
from app.auth import utils as auth_utils
from app.auth.enums import AuthFactor
from app.events.user_actions import types as user_actions_types
from app.events.user_actions import utils as user_actions_utils
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib import helpers
from app.lib.database import DBConnection
from app.mobile import constants as constants
from app.mobile.auth import db as mobile_auth_db
from app.mobile.auth import types as mobile_auth_types
from app.registration import emailing as registration_emailing
from app.registration import types as registration_types
from app.registration import utils as registration_utils
from app.services import services

logger = logging.getLogger(__name__)


def _get_redis_key(token_hash: str) -> str:
    """
    Get redis key for mobile auth token.
    """
    return f'{constants.MOBILE_REDIS_PREFIX}:{token_hash}'


async def save_access_token(
    *,
    user_id: str,
    access_token_hash: str,
    refresh_token_id: str,
    expires_in: int,
    pending_2fa: two_factor.Pending2FASchema | None,
) -> None:
    """
    Save access token to redis.
    """

    access_token = mobile_auth_types.MobileAuthAccessToken(
        user_id=user_id,
        auth_refresh_token_id=refresh_token_id,
        token_hash=access_token_hash,
        status=(
            mobile_auth_types.MobileAuthAccessTokenStatus.pending
            if pending_2fa
            else mobile_auth_types.MobileAuthAccessTokenStatus.ok
        ),
        pending_2fa=pending_2fa,
    )

    await services.redis.setex(
        _get_redis_key(token_hash=access_token_hash),
        value=access_token.to_redis(),
        time=expires_in,
    )


async def get_access_token(value: str) -> mobile_auth_types.MobileAuthAccessToken | None:
    """
    Try to get valid access token by token string.
    """

    token_hash = helpers.str_to_hash(value)
    auth_token = await services.redis.get(_get_redis_key(token_hash=token_hash))
    if not auth_token:
        return None

    return mobile_auth_types.MobileAuthAccessToken.from_redis(auth_token)


async def process_mobile_token_refresh(
    conn: DBConnection, ctx: mobile_auth_types.RefreshCtx
) -> None:
    """
    Remove old token, save new token.
    """

    # remove old token
    await services.redis.delete(
        _get_redis_key(token_hash=ctx.old_access_token_hash),
    )

    # save new token
    await save_access_token(
        user_id=ctx.user_id,
        access_token_hash=ctx.access_token.token_hash,
        refresh_token_id=ctx.auth_refresh_token_id,
        pending_2fa=None,
        expires_in=ctx.expires_in,
    )

    await mobile_auth_db.update_mobile_auth_refresh_tokens(
        conn=conn,
        id=ctx.auth_refresh_token_id,
        # mark as verified if needed on 2fa verified
        status=(
            mobile_auth_types.MobileAuthRefreshTokenStatus.ready if ctx.mark_as_verified else None
        ),
        # update access token hash to currently used
        access_token_hash=ctx.access_token.token_hash,
    )


async def process_mobile_logout_all(conn: DBConnection, user_id: str) -> int:
    """
    Remove all access tokens and change their status to `cancelled`.
    """

    tokens = await mobile_auth_db.select_mobile_auth_refresh_tokens(
        conn=conn,
        user_id=user_id,
    )
    for token in tokens:
        await services.redis.delete(
            _get_redis_key(token_hash=token.access_token_hash),
        )

    await mobile_auth_db.update_mobile_auth_refresh_tokens(
        conn=conn,
        where_user_id=user_id,
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.canceled,
    )

    return len(tokens)


async def process_mobile_logout(conn: DBConnection, ctx: mobile_auth_types.LogoutCtx) -> None:
    """
    Remove current access token and deprecate refresh token.
    """

    await services.redis.delete(
        _get_redis_key(token_hash=ctx.refresh_token.access_token_hash),
    )

    await mobile_auth_db.update_mobile_auth_refresh_tokens(
        conn=conn,
        id=ctx.refresh_token.id,
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.canceled,
    )


async def process_verify_phone_2fa(conn: DBConnection, ctx: mobile_auth_types.RefreshCtx) -> None:
    """
    Remove 2fa code and refresh access token.
    """

    await two_factor.reset_2fa_otp_mobile(user_id=ctx.user_id)
    await process_mobile_token_refresh(conn, ctx)


def get_mobile_request_access_token(
    request: web.Request,
) -> mobile_auth_types.MobileAuthAccessToken:
    """
    Extract information about mobile auth access token from request object
    """
    token = request.get(constants.AUTH_ACCESS_TOKEN)
    assert token, 'Mobile auth access token is not provided in request object'
    return token


def generate_refresh_token() -> mobile_auth_types.GeneratedToken:
    """
    Generate refresh token for mobile app session.
    """
    token = helpers.get_random_letters_and_digits(constants.REFRESH_TOKEN_NBYTES_LEN)
    return mobile_auth_types.GeneratedToken(token=token, token_hash=helpers.str_to_hash(token))


def generate_access_token() -> mobile_auth_types.GeneratedToken:
    """
    Generate access token for mobile app session.
    """
    token = str(uuid4())
    return mobile_auth_types.GeneratedToken(token=token, token_hash=helpers.str_to_hash(token))


async def save_mobile_login_tokens(
    conn: DBConnection,
    user: auth_types.BaseUser,
    access_token: mobile_auth_types.GeneratedToken,
    refresh_token: mobile_auth_types.GeneratedToken,
    pending_2fa: two_factor.Pending2FASchema | None,
    expires_in: int,
) -> None:
    """
    Save mobile access and refresh tokens to the storage
    """
    _refresh_token = await mobile_auth_db.insert_mobile_auth_refresh_token(
        conn=conn,
        user_id=user.id,
        token_hash=refresh_token.token_hash,
        access_token_hash=access_token.token_hash,
        status=(
            mobile_auth_types.MobileAuthRefreshTokenStatus.pending
            if pending_2fa
            else mobile_auth_types.MobileAuthRefreshTokenStatus.ready
        ),
    )
    await save_access_token(
        user_id=user.id,
        access_token_hash=access_token.token_hash,
        refresh_token_id=_refresh_token.id,
        pending_2fa=pending_2fa,
        expires_in=expires_in,
    )


async def mobile_login(
    conn: DBConnection,
    request: web.Request,
    user: auth_types.BaseUser,
    first_factor: AuthFactor,
) -> mobile_auth_types.MobileLoginContext:
    """
    Login to the mobile app, return access token context.
    """

    # Prepare new session data
    pending_2fa = await two_factor.get_required_2fa_mobile(
        conn=conn,
        user=user,
        first_factor=first_factor,
    )
    is_2fa_required = pending_2fa is not None
    expires_in = constants.ACCESS_TOKEN_EXPIRE_IN
    access_token = generate_access_token()
    refresh_token = generate_refresh_token()

    # Save tokens
    await save_mobile_login_tokens(
        conn=conn,
        user=user,
        access_token=access_token,
        refresh_token=refresh_token,
        pending_2fa=pending_2fa,
        expires_in=expires_in,
    )
    await auth_utils.update_user_is_logged_once(conn, user=user)

    if pending_2fa:
        await two_factor.start_2fa_mobile(user=user, pending_2fa=pending_2fa)

    if get_flag(FeatureFlags.ENABLE_ROLES_SYNC_ON_LOGIN):
        await auth_utils.try_to_syncronously_sync_user_roles(
            request_source=user_actions_utils.get_event_source(request),
            user=user,
        )

    # Register user action for login
    await user_actions_utils.add_user_action(
        user_action=user_actions_types.UserAction(
            action=user_actions_types.Action.login_successful,
            source=user_actions_utils.get_event_source(request),
            email=user.email,
            user_id=user.id,
            phone=getattr(user, 'auth_phone', None),
            company_id=getattr(user, 'company_id', None),
        )
    )

    return mobile_auth_types.MobileLoginContext(
        user=user,
        is_2fa_required=is_2fa_required,
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=constants.ACCESS_TOKEN_EXPIRE_IN,
    )


async def mobile_registration(
    conn: DBConnection,
    request: web.Request,
    ctx: registration_types.RegistrationCtx,
) -> mobile_auth_types.MobileLoginContext:
    """
    Perform the registration process from mobile app for a new user by email and password.

    1. Create user from given registration context.
    2. Login user and store the mobile auth & refresh token.
    3. Send registration jobs and email confirmation request.
    """

    # Get the ID of the dealer who referred the user
    referrer_id = await registration_utils.get_dealer_referrer_role_id(
        conn=conn,
        referrer_role_id=ctx.referrer,
    )
    user = await registration_utils.create_user_on_registration(
        conn=conn,
        email=ctx.email,
        password=ctx.password,
        phone=None,
        first_name=None,
        second_name=None,
        last_name=None,
        promo_code=ctx.promo_code,
        trial_auto_enable=ctx.trial_auto_enable,
        pending_referrer_role_id=referrer_id,
        source=ctx.source,
        is_email_confirmed=False,
        is_registration_completed=False,
        registration_method=ctx.registration_method,
    )

    login_ctx = await mobile_login(
        conn=conn,
        request=request,
        user=user,
        first_factor=AuthFactor.email,
    )

    # Send any necessary jobs related to the registration process
    await registration_utils.send_registration_jobs(user=user, cookies=request.cookies)

    await registration_emailing.send_confirmation_email(
        email=ctx.email,
        user_id=user.id,
        redirect_url=ctx.redirect_url,
    )

    return login_ctx
