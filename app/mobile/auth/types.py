from __future__ import annotations

import typing as t
from dataclasses import dataclass
from datetime import datetime
from enum import auto

import pydantic
import ujson

from app.auth import types as auth_types
from app.auth.enums import AuthFactor
from app.auth.two_factor import Pending2FASchema
from app.lib import validators_pydantic as pv
from app.lib.database import DBRow
from app.lib.enums import NamedEnum
from app.lib.types import DataDict
from app.mobile import constants


class MobileAuthAccessTokenStatus(NamedEnum):
    # can be used
    ok = auto()
    # 2FA verification is required
    pending = auto()


class MobileAuthRefreshTokenStatus(NamedEnum):
    ready = auto()
    canceled = auto()
    # 2FA verification is required
    pending = auto()


@dataclass(frozen=True)
class MobileAuthRefreshToken:
    id: str
    user_id: str
    token_hash: str
    access_token_hash: str
    status: MobileAuthRefreshTokenStatus
    date_created: datetime
    date_updated: datetime
    firebase_id: str | None = None

    @classmethod
    def from_row(cls, row: DBRow) -> MobileAuthRefreshToken:
        return cls(
            id=row.id,
            user_id=row.user_id,
            firebase_id=row.firebase_id,
            token_hash=row.token_hash,
            access_token_hash=row.access_token_hash,
            status=row.status,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )


@dataclass(frozen=True)
class MobileAuthAccessToken:
    user_id: str
    auth_refresh_token_id: str
    token_hash: str
    status: MobileAuthAccessTokenStatus
    pending_2fa: Pending2FASchema | None

    _key_mapping: t.ClassVar[dict[str, str]] = {
        'user_id': '1',
        'auth_refresh_token_id': '2',
        'token_hash': '3',
        'status': '4',
        'pending_2fa': '5',
    }

    def to_redis(self) -> str:
        """
        Serialize dict to json
        """

        if self.status == MobileAuthAccessTokenStatus.pending:
            assert self.pending_2fa is not None, 'Pending 2FA must be set for pending status'

        pending_2fa: DataDict | None = None
        if self.pending_2fa:
            pending_2fa = self.pending_2fa.model_dump(mode='json')

        return ujson.dumps(
            {
                self._key_mapping['user_id']: self.user_id,
                self._key_mapping['auth_refresh_token_id']: self.auth_refresh_token_id,
                self._key_mapping['token_hash']: self.token_hash,
                self._key_mapping['status']: self.status.value,
                self._key_mapping['pending_2fa']: pending_2fa,
            }
        )

    @classmethod
    def from_redis(cls, raw: str) -> MobileAuthAccessToken:
        """
        Deserialize json to dict
        """
        data = ujson.loads(raw)

        pending_2fa: Pending2FASchema | None = None
        if _raw_pending_2fa := data.get(cls._key_mapping['pending_2fa']):
            pending_2fa = Pending2FASchema.model_validate(_raw_pending_2fa)

        status = MobileAuthAccessTokenStatus(data[cls._key_mapping['status']])

        # TODO: remove after deployment + 1 hour. Previously mobile auth supported only 2FA
        # by phone, so no pending_2fa wasn't exist in redis storage for old tokens. Now when
        # 2FA supports both email and phone as second factor pending_2fa is always set.
        if pending_2fa is None and status == MobileAuthAccessTokenStatus.pending:
            pending_2fa = Pending2FASchema(
                first_factor=AuthFactor.email,
                second_factor=AuthFactor.phone,
            )

        return cls(
            user_id=data[cls._key_mapping['user_id']],
            auth_refresh_token_id=data[cls._key_mapping['auth_refresh_token_id']],
            token_hash=data[cls._key_mapping['token_hash']],
            status=status,
            pending_2fa=pending_2fa,
        )


@dataclass(frozen=True)
class GeneratedToken:
    token: str
    token_hash: str


@dataclass(frozen=True)
class MobileLoginContext:
    user: auth_types.BaseUser
    is_2fa_required: bool
    expires_in: int

    refresh_token: GeneratedToken
    access_token: GeneratedToken

    def to_dict(self) -> dict[str, t.Any]:
        # TODO: add when token is expiring
        return {
            'user': {
                'id': self.user.id,
                'first_name': self.user.first_name,
                'last_name': self.user.last_name,
                'email': self.user.email,
                'phone': self.user.phone,
                'is_email_confirmed': self.user.email_confirmed,
            },
            'access_token': self.access_token.token,
            'refresh_token': self.refresh_token.token,
            'is_2fa_required': self.is_2fa_required,
            'expires_in': self.expires_in,
        }


@dataclass(frozen=True)
class RefreshCtx:
    user_id: str
    user_email: str | None
    access_token: GeneratedToken
    expires_in: int

    auth_refresh_token_id: str
    old_access_token_hash: str

    mark_as_verified: bool = False

    def to_response_dict(self) -> dict[str, t.Any]:
        return {
            'access_token': self.access_token.token,
            'expires_in': self.expires_in,
        }


@dataclass(frozen=True)
class LogoutCtx:
    refresh_token: MobileAuthRefreshToken
    user: auth_types.BaseUser


class RefreshTokenValidator(pydantic.BaseModel):
    token: str = pydantic.Field(max_length=constants.REFRESH_TOKEN_NBYTES_LEN)


class VerifyPhone2faValidator(pydantic.BaseModel):
    code: str = pydantic.Field(min_length=6, max_length=6)


class VerifyEmail2faSchema(pydantic.BaseModel):
    password: pv.InsecurePassword
