from aiohttp import web

from api import errors
from app.auth import db as auth_db
from app.auth import two_factor
from app.auth import types as auth_types
from app.auth.enums import AuthFactor
from app.auth.validators import login_rate_limit, validate_is_user_login_password_valid
from app.i18n import _
from app.lib import helpers, validators
from app.lib.database import DBConnection
from app.lib.datetime_utils import ONE_HOUR_DELTA
from app.lib.types import DataDict
from app.mobile import constants
from app.mobile.auth import db as mobile_auth_db
from app.mobile.auth import types as mobile_auth_types
from app.mobile.auth.utils import generate_access_token


async def validate_refresh(conn: DBConnection, raw_data: DataDict) -> mobile_auth_types.RefreshCtx:
    """
    Validate request to refresh token.
    """
    valid_data = validators.validate_pydantic(mobile_auth_types.RefreshTokenValidator, raw_data)

    token_hash = helpers.str_to_hash(valid_data.token)
    auth_refresh_token = await mobile_auth_db.select_mobile_auth_refresh_token(
        conn=conn,
        token_hash=token_hash,
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.ready,
    )
    if not auth_refresh_token:
        raise errors.AccessDenied()

    user = await auth_db.select_base_user(conn, user_id=auth_refresh_token.user_id)
    if not user:
        raise errors.AccessDenied()

    return mobile_auth_types.RefreshCtx(
        user_id=user.id,
        user_email=user.email,
        auth_refresh_token_id=auth_refresh_token.id,
        access_token=generate_access_token(),
        expires_in=constants.ACCESS_TOKEN_EXPIRE_IN,
        old_access_token_hash=auth_refresh_token.access_token_hash,
    )


async def validate_logout(conn: DBConnection, raw_data: DataDict) -> mobile_auth_types.LogoutCtx:
    """
    Validate logout request for mobile app session.
    """
    valid_data = validators.validate_pydantic(mobile_auth_types.RefreshTokenValidator, raw_data)

    token_hash = helpers.str_to_hash(valid_data.token)
    auth_refresh_token = await mobile_auth_db.select_mobile_auth_refresh_token(
        conn=conn,
        token_hash=token_hash,
        status=mobile_auth_types.MobileAuthRefreshTokenStatus.ready,
    )
    if not auth_refresh_token:
        raise errors.AccessDenied()

    user = await auth_db.select_base_user(conn, user_id=auth_refresh_token.user_id)
    if not user:
        raise errors.AccessDenied()

    return mobile_auth_types.LogoutCtx(
        user=user,
        refresh_token=auth_refresh_token,
    )


async def validate_verify_phone_2fa(
    user: auth_types.BaseUser,
    access_token: mobile_auth_types.MobileAuthAccessToken,
    raw_data: DataDict,
) -> mobile_auth_types.RefreshCtx:
    """
    Verify 2fa code matches the stored one.
    """
    valid_data = validators.validate_pydantic(mobile_auth_types.VerifyPhone2faValidator, raw_data)

    await validate_pending_2fa_mobile(
        token=access_token,
        second_factor=AuthFactor.phone,
    )

    await helpers.validate_rate_limit(
        key=f'mobile_2fa:phone_verify:{user.email}',
        limit=15,
        delta=ONE_HOUR_DELTA,
    )

    otp = await two_factor.get_2fa_otp_mobile(user_id=user.id)
    if not otp or valid_data.code != otp or not user.id:
        raise errors.AccessDenied()

    return mobile_auth_types.RefreshCtx(
        user_id=user.id,
        user_email=user.email,
        auth_refresh_token_id=access_token.auth_refresh_token_id,
        access_token=generate_access_token(),
        expires_in=constants.ACCESS_TOKEN_EXPIRE_IN,
        old_access_token_hash=access_token.token_hash,
        mark_as_verified=True,
    )


async def validate_pending_2fa_mobile(
    token: mobile_auth_types.MobileAuthAccessToken,
    second_factor: AuthFactor,
) -> None:
    """
    Validate that pending 2FA factor matches the one requested by user.
    This is used to ensure that user is trying to verify the correct factor.
    """

    pending_2fa = token.pending_2fa
    if not pending_2fa:
        raise errors.InvalidRequest(
            reason=_('Процес двофакторної аутентифікації не розпочато'),
        )

    if pending_2fa.second_factor != second_factor:
        raise errors.InvalidRequest(
            reason=_('Очікується інший тип двофакторної аутентифікації'),
            details={'pending': pending_2fa.second_factor.name, 'current': second_factor.name},
        )


async def validate_verify_email_2fa(
    request: web.Request,
    conn: DBConnection,
    user: auth_types.BaseUser,
    access_token: mobile_auth_types.MobileAuthAccessToken,
    raw_data: DataDict,
) -> mobile_auth_types.RefreshCtx:
    """
    Validate that user enters valid email and password to verify email 2FA after login
    by phone number.
    """
    data = validators.validate_pydantic(mobile_auth_types.VerifyEmail2faSchema, raw_data)

    await helpers.validate_rate_limit(
        key=f'mobile_2fa:email_verify:{user.id}',
        limit=15,
        delta=ONE_HOUR_DELTA,
    )

    await validate_pending_2fa_mobile(
        token=access_token,
        second_factor=AuthFactor.email,
    )
    if not user.email:
        raise errors.InvalidRequest(reason=_('Користувач не має email'))

    async with login_rate_limit(email=user.email):
        await validate_is_user_login_password_valid(
            request=request,
            conn=conn,
            password=data.password,
            user=user,
        )

    return mobile_auth_types.RefreshCtx(
        user_id=user.id,
        user_email=user.email,
        auth_refresh_token_id=access_token.auth_refresh_token_id,
        access_token=generate_access_token(),
        expires_in=constants.ACCESS_TOKEN_EXPIRE_IN,
        old_access_token_hash=access_token.token_hash,
        mark_as_verified=True,
    )
