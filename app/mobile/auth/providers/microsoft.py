"""
Copy of app/registration/utils.py and app/auth/utils.py
with using mobile login instead of web login
"""

from aiohttp import web

from api.errors import Code, MicrosoftAuthorisationError
from app.auth import db as auth_db
from app.auth import types as auth_types
from app.auth.enums import AuthFactor
from app.auth.providers import microsoft
from app.auth.utils import update_user
from app.lib.database import DBConnection
from app.mobile.auth import types as mobile_auth_types
from app.mobile.auth import utils as mobile_auth_utils
from app.registration import emailing as registration_emailing
from app.registration import enums as registration_enums
from app.registration import utils as registration_utils


async def microsoft_mobile_auth(
    request: web.Request,
    conn: DBConnection,
    raw_access_token: str,
    raw_id_token: str,
    registration_source: registration_enums.RegistrationSource | None = None,
    invite_email: str | None = None,
) -> mobile_auth_types.MobileLoginContext:
    """
    Login/register user by JWT token from Microsoft Sign In button.

    It's OK to have request and response objects everywhere on the service
    level to simplify logic that depends on that objects.
    """
    token = await microsoft.verify_oauth2_token(
        access_token=raw_access_token,
        id_token=raw_id_token,
    )

    if registration_source == registration_enums.RegistrationSource.after_invite.value and (
        not invite_email or invite_email != token.email
    ):
        raise MicrosoftAuthorisationError(code=Code.invalid_email_provided)

    # Log in by Microsoft ID from token
    microsoft_id = token.id
    user = await auth_db.select_base_user(conn, microsoft_id=microsoft_id)
    if user:
        return await mobile_auth_utils.mobile_login(
            conn=conn,
            request=request,
            user=user,
            first_factor=AuthFactor.email,
        )

    # Log in by email from token
    if microsoft_email := token.email:
        user = await auth_db.select_base_user(conn, email=microsoft_email)
        if user:
            # Add Microsoft ID to user profile
            update_data: auth_types.UpdateBaseUserDict = {
                'microsoft_id': microsoft_id,
                'email_confirmed': True,
            }
            if registration_source == registration_enums.RegistrationSource.after_invite.value:
                update_data['registration_method'] = registration_enums.RegistrationMethod.microsoft
            await update_user(
                conn=conn,
                user_id=user.id,
                data=update_data,
            )
            return await mobile_auth_utils.mobile_login(
                conn=conn,
                request=request,
                user=user,
                first_factor=AuthFactor.email,
            )

    return await microsoft_mobile_registration(
        request=request,
        conn=conn,
        token=token,
    )


async def microsoft_mobile_registration(
    conn: DBConnection,
    token: microsoft.MicrosoftTokenPayload,
    request: web.Request,
) -> mobile_auth_types.MobileLoginContext:
    """
    Perform the registration process for a new user by "Sign with Microsoft".

    It's a combination of functions "app.registration.utils.registration"
    and "app.registration.utils.confirm_email".
    """

    from app.registration.validators import validate_microsoft_registration

    ms_ctx = await validate_microsoft_registration(
        conn=conn,
        request=request,
        token=token,
    )

    # Get the ID of the dealer who referred the user
    referrer_id = await registration_utils.get_dealer_referrer_role_id(
        conn=conn,
        referrer_role_id=ms_ctx.referrer,
    )

    async with conn.begin():
        user = await registration_utils.create_user_on_registration(
            conn=conn,
            email=ms_ctx.email,
            password=None,
            phone=token.mobile_phone,
            first_name=token.given_name,
            second_name=None,
            last_name=token.surname,
            promo_code=ms_ctx.promo_code,
            trial_auto_enable=ms_ctx.trial_auto_enable,
            pending_referrer_role_id=referrer_id,
            source=ms_ctx.source,
            # we trust microsoft with email verification
            is_email_confirmed=True,
            is_registration_completed=False,
            microsoft_id=token.id,
            registration_method=registration_enums.RegistrationMethod.microsoft,
        )
        ctx = await mobile_auth_utils.mobile_login(
            conn=conn,
            request=request,
            user=user,
            first_factor=AuthFactor.email,
        )

    # Send any necessary jobs related to the registration and email
    # confirmation process
    await registration_utils.send_registration_jobs(user=user, cookies=request.cookies)
    await registration_utils.send_confirm_email_jobs(user=user)

    await registration_emailing.send_user_welcome_email(user=user)

    return ctx
