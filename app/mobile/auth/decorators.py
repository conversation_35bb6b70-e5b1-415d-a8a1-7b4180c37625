import functools
import logging
from collections import abc

from aiohttp import web

from api import errors
from app.auth import constants as auth_constants
from app.auth import db as auth_db
from app.auth import enums as auth_enums
from app.auth import middlewares as auth_middlewares
from app.auth import types as auth_types
from app.auth.types import is_wide_user_type
from app.lib import types as core_types
from app.mobile import constants as constants
from app.mobile.auth import types as mobile_auth_types
from app.mobile.auth import utils as mobile_auth_utils
from app.services import services

logger = logging.getLogger(__name__)

UserHandler = abc.Callable[
    [web.Request, auth_types.User], abc.Awaitable[core_types.HandlerResponse]
]
UserHandlerWrapped = abc.Callable[
    [UserHandler], abc.Callable[[web.Request], abc.Awaitable[web.StreamResponse]]
]
BaseUserHandler = abc.Callable[
    [web.Request, auth_types.BaseUser | auth_types.User], abc.Awaitable[core_types.HandlerResponse]
]
BaseUserHandlerWrapped = abc.Callable[
    [BaseUserHandler], abc.Callable[[web.Request], abc.Awaitable[web.StreamResponse]]
]


async def _login_required_base(
    allow_2fa_unverified: bool,
    request: web.Request,
    is_role_required: bool = True,
) -> auth_types.User | auth_types.BaseUser:
    """
    Authenticate user by token, fill request object with required data
    """
    res = await _authenticate_by_mobile_token(request, is_role_required=is_role_required)
    if not res:
        raise errors.AccessDenied()

    user, token = res

    if (
        not allow_2fa_unverified
        and token.status != mobile_auth_types.MobileAuthAccessTokenStatus.ok
    ):
        raise errors.AccessDenied()

    logger.info(
        'Granted access to user by Authorization Mobile Token',
        extra={
            'refresh_id': token.auth_refresh_token_id,
            'user_id': user.id,
            'user_edrpou': user.company_edrpou if is_wide_user_type(user) else None,
            'user_email': user.email,
            'user_role': user.user_role if is_wide_user_type(user) else None,
        },
    )

    # Store user & token to current request and call actual handler
    request[auth_constants.USER_APP_KEY] = user
    request[auth_constants.AUTH_METHOD_APP_KEY] = auth_enums.AuthMethod.mobile_token
    request[
        auth_constants.AUTH_USER_COMPANY_CONFIG
    ] = await auth_middlewares.get_auth_user_company_config(user)

    # HINT: you can use "get_mobile_auth_access_token" to extract token from request
    request[constants.AUTH_ACCESS_TOKEN] = token

    return user


def mobile_base_login_required(allow_2fa_unverified: bool = False) -> BaseUserHandlerWrapped:
    """
    Decorator to check mobile token in Authorization header.
    This decorator does not require customer to have an active role.

    The handler should accept either:
     - user: BaseUser — if only base information is needed about the user
     - user: BaseUser | User - if there is a need to make distinction between user
       with role and without role
    """

    def decorator(handler: BaseUserHandler) -> core_types.Handler:
        @functools.wraps(handler)
        async def wrapper(request: web.Request) -> core_types.HandlerResponse:
            user = await _login_required_base(
                allow_2fa_unverified=allow_2fa_unverified,
                request=request,
                is_role_required=False,
            )

            return await handler(request, user)

        return wrapper

    return decorator


def mobile_login_required(allow_2fa_unverified: bool = False) -> UserHandlerWrapped:
    """
    Decorator to check mobile token in Authorization header.
    Enforces to have a role in headers.
    """

    def decorator(handler: UserHandler) -> core_types.Handler:
        @functools.wraps(handler)
        async def wrapper(request: web.Request) -> core_types.HandlerResponse:
            user = await _login_required_base(allow_2fa_unverified, request)

            if not isinstance(user, auth_types.User):
                logger.exception('Failed to create User instance from row')
                raise errors.Error(errors.Code.without_roles)

            return await handler(request, user)

        return wrapper

    return decorator


async def _authenticate_by_mobile_token(
    request: web.Request,
    is_role_required: bool,
) -> tuple[auth_types.User | auth_types.BaseUser, mobile_auth_types.MobileAuthAccessToken] | None:
    """
    Authenticate user by Authorization header if possible.
    """
    token_value = request.headers.get(constants.MOBILE_AUTH_HEADER)
    if not token_value:
        return None

    role_id = request.headers.get(constants.MOBILE_AUTH_ROLE_HEADER)
    if is_role_required and not role_id:
        raise errors.Error(errors.Code.without_roles)

    token = await mobile_auth_utils.get_access_token(token_value)
    if token:
        async with services.db.acquire() as conn:
            user: auth_types.BaseUser | auth_types.User | None = None
            if role_id:
                user = await auth_db.select_user(
                    conn=conn,
                    user_id=token.user_id,
                    role_id=role_id,
                )
            else:
                user = await auth_db.select_user_with_any_role(
                    conn=conn,
                    user_id=token.user_id,
                )
        if user:
            return user, token

    return None
