from http import HTTPStatus

from aiohttp import web

from api.utils import api_response
from app.auth import two_factor
from app.auth import types as auth_types
from app.auth import validators as auth_validators
from app.auth import views as auth_views
from app.auth.enums import AuthFactor
from app.auth.phone_auth import utils as phone_auth_utils
from app.auth.phone_auth import validators as phone_auth_validators
from app.auth.providers.apple import utils as apple_utils
from app.events.user_actions import types as user_actions_types
from app.events.user_actions import utils as user_actions_utils
from app.lib import validators as core_validators
from app.mobile.auth import decorators as mobile_decorators
from app.mobile.auth import utils as mobile_auth_utils
from app.mobile.auth import validators as mobile_auth_validators
from app.mobile.auth.providers import google, microsoft
from app.registration import emailing as registration_emailing
from app.registration import utils as registration_utils
from app.registration import validators as registration_validators
from app.registration import views as registration_views
from app.services import services


async def register(request: web.Request) -> web.Response:
    """
    Register new user.
    """
    async with services.db.acquire() as conn:
        valid_json = await core_validators.validate_json_request(request)
        registration_ctx = await registration_validators.validate_registration(conn, valid_json)
        ctx = await mobile_auth_utils.mobile_registration(
            request=request, conn=conn, ctx=registration_ctx
        )

    return api_response(request, ctx.to_dict())


async def login(request: web.Request) -> web.Response:
    """
    Login user to mobile application.
    """
    raw_data = await core_validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        login_context = await auth_validators.validate_login(
            conn=conn,
            data=raw_data,
            request=request,
        )
        ctx = await mobile_auth_utils.mobile_login(
            conn=conn,
            request=request,
            user=login_context.user,
            first_factor=AuthFactor.email,
        )

    return api_response(
        request,
        ctx.to_dict(),
    )


async def refresh(request: web.Request) -> web.Response:
    """
    Refresh access token for a user.
    """
    raw_data = await core_validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        ctx = await mobile_auth_validators.validate_refresh(conn=conn, raw_data=raw_data)
        await mobile_auth_utils.process_mobile_token_refresh(conn, ctx)

    return api_response(
        request,
        ctx.to_response_dict(),
    )


async def logout(request: web.Request) -> web.Response:
    """
    Logout user from application.
    Cancel all existing access tokens.
    """
    raw_data = await core_validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        ctx = await mobile_auth_validators.validate_logout(conn, raw_data)
        await mobile_auth_utils.process_mobile_logout(conn, ctx)

    await user_actions_utils.add_user_action(
        user_action=user_actions_types.UserAction(
            action=user_actions_types.Action.logout_successful,
            source=user_actions_utils.get_event_source(request),
            user_id=ctx.user.id,
            phone=ctx.user.auth_phone,
            email=ctx.user.email,
        )
    )

    return api_response(request, {})


@mobile_decorators.mobile_base_login_required(allow_2fa_unverified=True)
async def verify_phone_2fa(request: web.Request, user: auth_types.BaseUser) -> web.Response:
    """
    Verify 2fa verification code.
    """
    raw_data = await core_validators.validate_json_request(request)
    token = mobile_auth_utils.get_mobile_request_access_token(request)

    ctx = await mobile_auth_validators.validate_verify_phone_2fa(
        user=user, raw_data=raw_data, access_token=token
    )

    async with services.db.acquire() as conn:
        await mobile_auth_utils.process_verify_phone_2fa(conn, ctx)

    return api_response(
        request,
        ctx.to_response_dict(),
    )


@mobile_decorators.mobile_base_login_required(allow_2fa_unverified=True)
async def resend_phone_2fa(request: web.Request, user: auth_types.BaseUser) -> web.Response:
    """
    Send 2fa verification code again.
    """

    token = mobile_auth_utils.get_mobile_request_access_token(request)
    await mobile_auth_validators.validate_pending_2fa_mobile(
        token=token,
        second_factor=AuthFactor.phone,
    )

    await two_factor.start_phone_2fa_code_mobile(
        user_email=user.email,
        user_id=user.id,
        user_phone=user.phone,
        first_factor=AuthFactor.email,
    )
    return api_response(request, {})


@mobile_decorators.mobile_base_login_required(allow_2fa_unverified=True)
async def get_hidden_phone(request: web.Request, user: auth_types.BaseUser) -> web.Response:
    return await auth_views.base_get_hidden_phone(request, user)


async def google_auth(request: web.Request) -> web.Response:
    """
    Handle login/registration by Google.

    More information:
    https://developers.google.com/identity/gsi/web/guides/overview
    """

    google_auth = await auth_validators.validate_google_auth(request=request)
    async with services.db.acquire() as conn:
        ctx = await google.google_mobile_auth(
            request=request,
            conn=conn,
            raw_token=google_auth['token'],
            registration_source=google_auth.get('source'),
            invite_email=google_auth.get('invite_email'),
        )

    return api_response(request, ctx.to_dict())


async def microsoft_auth(request: web.Request) -> web.Response:
    """
    Handle login/registration by Microsoft.

    More information:
    https://learn.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow#redirect-uris-for-single-page-apps-spas

    Generate testing token:
    - https://github.com/Azure-Samples/ms-identity-javascript-v2

    Why we need access_token AND id_token:
    - access_token is used to get user info from Microsoft Graph API
    - id_token is used to verify that user is authenticated by Microsoft and issued for our app

    access token might be not jwt token [1], but id_token is always jwt token [2].

    1. https://learn.microsoft.com/en-us/entra/identity-platform/access-tokens
    2. https://learn.microsoft.com/en-us/entra/identity-platform/id-tokens
    """

    microsoft_auth = await auth_validators.validate_microsoft_auth(request=request)
    async with services.db.acquire() as conn:
        ctx = await microsoft.microsoft_mobile_auth(
            request=request,
            conn=conn,
            raw_access_token=microsoft_auth['access_token'],
            raw_id_token=microsoft_auth['id_token'],
            registration_source=microsoft_auth.get('source'),
            invite_email=microsoft_auth.get('invite_email'),
        )

    return api_response(request, ctx.to_dict())


async def apple_auth(request: web.Request) -> web.Response:
    """
    Handle login/registration by Apple.
    """

    ctx = await apple_utils.get_user_with_apple(request=request)

    async with services.db.acquire() as conn:
        response = await mobile_auth_utils.mobile_login(
            conn=conn,
            request=request,
            user=ctx.user,
            first_factor=AuthFactor.email,
        )

    if registration_ctx := ctx.registration_ctx:
        if not ctx.user.email_confirmed:
            await registration_emailing.send_confirmation_email(
                email=registration_ctx.email,
                user_id=ctx.user.id,
                redirect_url=registration_ctx.redirect_url,
            )
        else:
            # Send any necessary jobs related to the registration and email
            # confirmation process
            await registration_utils.send_registration_jobs(
                user=ctx.user,
                cookies=request.cookies,
            )
            await registration_utils.send_confirm_email_jobs(user=ctx.user)
            await registration_emailing.send_user_welcome_email(user=ctx.user)
        return web.json_response(data={**response.to_dict()})

    return web.json_response(data=response.to_dict())


@mobile_decorators.mobile_base_login_required()
async def change_registration_email(
    request: web.Request, user: auth_types.BaseUser
) -> web.Response:
    """
    Amend the email address before confirming it.
    """
    await registration_views.change_email(request, user)
    return api_response(request, {})


async def check_email_registration(request: web.Request) -> web.Response:
    """
    Check whether the email exists during registration to determine
    whether to redirect the customer to the login page or the registration page.
    """
    # Note that this view is rate-limited, no need to rate limit the mobile-api view.
    await registration_views.check_email_registration(request)
    return api_response(request, {})


@mobile_decorators.mobile_base_login_required()
async def resend_confirmation_email(
    request: web.Request,
    user: auth_types.BaseUser | auth_types.User,
) -> web.Response:
    """
    Resend registration confirmation email
    """

    if user.email and not user.email_confirmed:
        await registration_emailing.send_confirmation_email(email=user.email, user_id=user.id)

    return web.json_response()


async def send_phone_auth_code(request: web.Request) -> web.Response:
    """
    Start login or registration process by phone number
    """
    async with services.db.acquire() as conn:
        data = await phone_auth_validators.validate_send_phone_auth(conn, request)

    await phone_auth_utils.send_phone_auth_code(phone=data.phone, method=data.method)

    return web.json_response(status=HTTPStatus.OK)


async def process_phone_auth_mobile(request: web.Request) -> web.Response:
    """
    Verify phone OTP code and register a new user if it is not registered yet.
    """
    async with services.db.acquire() as conn:
        data = await phone_auth_validators.validate_process_phone_auth(conn, request)

        output = await phone_auth_utils.process_phone_auth(
            conn=conn,
            auth_phone=data.phone,
            code=data.code,
        )
        user = output.user

        ctx = await mobile_auth_utils.mobile_login(
            conn=conn,
            request=request,
            user=user,
            first_factor=AuthFactor.phone,
        )

        if output.is_created:
            await registration_utils.send_registration_jobs(user=user, cookies=request.cookies)

    return api_response(request, ctx.to_dict())


@mobile_decorators.mobile_base_login_required(allow_2fa_unverified=True)
async def get_hidden_email(request: web.Request, user: auth_types.BaseUser) -> web.Response:
    """Retrieve user email in hidden format in order to show it on 2fa flow screen"""
    return await auth_views.base_get_hidden_email(request, user)


@mobile_decorators.mobile_base_login_required(allow_2fa_unverified=True)
async def verify_email_2fa(request: web.Request, user: auth_types.BaseUser) -> web.Response:
    """
    Verify second factor email and password when first factor is phone
    """
    raw_data = await core_validators.validate_json_request(request)
    token = mobile_auth_utils.get_mobile_request_access_token(request)

    async with services.db.acquire() as conn:
        ctx = await mobile_auth_validators.validate_verify_email_2fa(
            request=request,
            conn=conn,
            user=user,
            access_token=token,
            raw_data=raw_data,
        )

        await mobile_auth_utils.process_mobile_token_refresh(conn, ctx)

    return api_response(request, ctx.to_response_dict())
