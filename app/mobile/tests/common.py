import secrets

from app.mobile import constants as mobile_constants
from app.tests import common


async def create_user_with_2fa_required(aiohttp_client, monkeypatch):
    """
    Create user with 2FA enabled and return data for further testing.
    """

    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    # 2FA enabled due to internal user rule
    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.VCHASNO_EDRPOU,
        password=common.TEST_USER_PASSWORD,
        phone=common.TEST_USER_PHONE,
        is_2fa_enabled=True,
    )

    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={
            'email': user.email,
            'password': common.TEST_USER_PASSWORD,
        },
    )
    data = await response.json()

    return {
        'access_token': data['access_token'],
        'refresh_token': data['refresh_token'],
        'user': user,
        'app': app,
        'client': client,
        'code': otp_code,
    }


async def create_user_with_2fa_verified(aiohttp_client, monkeypatch):
    user_data = await create_user_with_2fa_required(aiohttp_client, monkeypatch)
    client = user_data['client']

    response = await client.post(
        '/mobile-api/v1/auth/verify-2fa',
        json={'code': user_data['code']},
        headers={mobile_constants.MOBILE_AUTH_HEADER: user_data['access_token']},
    )
    data = await response.json()

    return {
        'access_token': data['access_token'],
        'refresh_token': user_data['refresh_token'],
        'user': user_data['user'],
        'app': user_data['app'],
        'client': user_data['client'],
    }


async def generate_mobile_tokens(client, user):
    response = await client.post(
        '/mobile-api/v1/auth/login',
        json={
            'email': user.email,
            'password': common.TEST_USER_PASSWORD,
        },
    )
    data = await response.json()

    return {
        'access_token': data['access_token'],
        'refresh_token': data['refresh_token'],
    }
