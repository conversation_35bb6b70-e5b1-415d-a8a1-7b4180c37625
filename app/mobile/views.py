from app.mobile.auth.views import (
    apple_auth,
    change_registration_email,
    check_email_registration,
    get_hidden_email,
    get_hidden_phone,
    google_auth,
    login,
    logout,
    microsoft_auth,
    process_phone_auth_mobile,
    refresh,
    register,
    resend_confirmation_email,
    resend_phone_2fa,
    send_phone_auth_code,
    verify_email_2fa,
    verify_phone_2fa,
)
from app.mobile.documents.views import upload, view_document
from app.mobile.graphql.views import graphql
from app.mobile.notifications.views import (
    count_mobile_notifications,
    list_mobile_notifications,
    mark_all_notifications_as_seen,
    set_firebase_id,
    update_mobile_notification_status,
)

__all__ = [
    'register',
    'login',
    'refresh',
    'logout',
    'verify_phone_2fa',
    'resend_phone_2fa',
    'get_hidden_phone',
    'google_auth',
    'microsoft_auth',
    'apple_auth',
    'view_document',
    'upload',
    'graphql',
    'set_firebase_id',
    'count_mobile_notifications',
    'mark_all_notifications_as_seen',
    'update_mobile_notification_status',
    'list_mobile_notifications',
    'change_registration_email',
    'check_email_registration',
    'resend_confirmation_email',
    'send_phone_auth_code',
    'process_phone_auth_mobile',
    'get_hidden_email',
    'verify_email_2fa',
]
