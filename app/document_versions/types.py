from __future__ import annotations

import datetime
import typing
from dataclasses import (
    asdict,
    dataclass,
)

from app.auth.types import User
from app.document_versions.enums import DocumentVersionType
from app.document_versions.schemas import VersionUpdateSchema
from app.lib.database import DBRow
from app.lib.enums import Source
from app.lib.types import DataDict

if typing.TYPE_CHECKING:
    from app.documents.types import Document


@dataclass
class DocumentVersion:
    id: str
    document_id: str
    type: DocumentVersionType
    role_id: str
    company_edrpou: str
    date_created: datetime.datetime
    is_sent: bool
    extension: str  # with a leading dot, e.g. ".pdf"
    description: str | None

    # Deprecated: now we calculate name on the fly based on previous versions
    name: str

    @staticmethod
    def from_row(row: DBRow) -> DocumentVersion:
        return DocumentVersion(
            id=row.id,
            document_id=row.document_id,
            type=row.type,
            description=row.description,
            name=row.name,
            role_id=row.role_id,
            company_edrpou=row.company_edrpou,
            date_created=row.date_created,
            is_sent=row.is_sent,
            extension=row.extension,
        )

    def to_dict(self) -> DataDict:
        return asdict(self)

    @property
    def is_new_upload(self) -> bool:
        return self.type == DocumentVersionType.new_upload

    @property
    def is_converted_format(self) -> bool:
        return self.type == DocumentVersionType.converted_format


@dataclass(frozen=True)
class InsertDocumentVersionParameters:
    document_id: str
    role_id: str
    company_edrpou: str
    name: str
    type: DocumentVersionType
    content_hash: str
    content_length: int
    extension: str

    # Optional fields
    version_id: str | None = None
    is_sent: bool | None = None
    date_created: datetime.datetime | None = None

    @staticmethod
    def prepare_upload_version(
        version_id: str,
        document_id: str,
        role_id: str,
        company_edrpou: str,
        content_hash: str,
        content_length: int,
        extension: str,
    ) -> InsertDocumentVersionParameters:
        """
        Prepare parameters for the first version of the document that is created during the
        upload process or when the document is switched from non-versioned to versioned state.

        :content_hash: is the result of "eusign_utils.generate_hash_base64" function
        """
        return InsertDocumentVersionParameters(
            version_id=version_id,
            document_id=document_id,
            type=DocumentVersionType.new_upload,
            name='#1',
            role_id=role_id,
            company_edrpou=company_edrpou,
            content_hash=content_hash,
            content_length=content_length,
            extension=extension,
        )

    def to_db_dict(self) -> DataDict:
        data: DataDict = {
            'document_id': self.document_id,
            'type': self.type,
            'name': self.name,
            'role_id': self.role_id,
            'company_edrpou': self.company_edrpou,
            'extension': self.extension,
            'content_hash': self.content_hash,
            'content_length': self.content_length,
        }

        if self.version_id is not None:
            data['id'] = self.version_id
        if self.date_created is not None:
            data['date_created'] = self.date_created
        if self.is_sent is not None:
            data['is_sent'] = self.is_sent

        return data


@dataclass(frozen=True)
class DocumentVersionIndexatorData:
    id: str
    document_id: str
    uploader_edrpou: str
    is_sent: bool

    @staticmethod
    def from_row(row: DBRow) -> DocumentVersionIndexatorData:
        return DocumentVersionIndexatorData(
            id=row.id,
            document_id=row.document_id,
            uploader_edrpou=row.company_edrpou,
            is_sent=row.is_sent,
        )


@dataclass(frozen=True)
class UpdateDocumentVersionSettingsOutput:
    is_version_deleted: bool
    is_version_created: bool
    version: DocumentVersion


@dataclass(frozen=True)
class VersionUploadCtx:
    document: Document
    content: bytes
    extension: str
    version_count: int

    # Optional fields, mostly for preparing test data
    date_created: datetime.datetime | None = None


@dataclass(frozen=True)
class VersionDeleteCtx:
    document: Document
    version: DocumentVersion
    user: User
    request_source: Source


@dataclass(frozen=True)
class VersionUpdateCtx:
    document: Document
    version: DocumentVersion
    user: User
    data: VersionUpdateSchema


class VersionUpdateDict(typing.TypedDict, total=False):
    description: str | None
    is_sent: bool
