from datetime import timed<PERSON><PERSON>

import pytest

from app.document_versions.enums import DocumentVersionType
from app.document_versions.resolvers import DocumentVersionRow, _calculate_version_number
from app.document_versions.utils import add_document_content_version
from app.lib.datetime_utils import utc_now
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
    with_elastic,
)

COMPANY_EDRPOU_1 = 'c1'
COMPANY_EDRPOU_2 = 'c2'
ID_1 = 'id1'
ID_2 = 'id2'
ID_3 = 'id3'
ID_4 = 'id4'
ID_5 = 'id5'
ID_6 = 'id6'


async def test_graphql_revolve_document_version(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    now = utc_now()
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document = await prepare_document_data(
        app,
        owner=user,
        recipient_email=recipient.email,
        recipient_edrpou=recipient.company_edrpou,
    )

    document_without_versions = await prepare_document_data(
        app,
        user,
        recipient_email=recipient.email,
        recipient_edrpou=recipient.company_edrpou,
    )

    # mark document as versioned
    async with app['db'].acquire() as conn:
        document_version_1 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now,
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        document_version_2 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now + timedelta(seconds=1),
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
            is_sent=True,
        )
        document_version_3 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now + timedelta(seconds=2),
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )
        document_version_4 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
            date_created=now + timedelta(seconds=3),
            role_id=user.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
            is_sent=True,
        )
        document_version_5 = await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=recipient.company_edrpou,
            company_id=recipient.company_id,
            date_created=now + timedelta(seconds=3),
            role_id=recipient.role_id,
            content=b'new version content',
            upload_type=DocumentVersionType.new_upload,
            name='Test name',
            extension='.pdf',
        )

    # Act
    query = """{
        allDocuments {
         documents {
          id
          canAddVersion
          versions {
           id
           number
          }
         }
        }
    }"""

    async with with_elastic(app, [document.id, document_without_versions.id]):
        response = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(user),
        )
        response_recipient = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(recipient),
        )

    # Assert
    graph_document = (
        response['allDocuments']['documents'][0]
        if response['allDocuments']['documents'][0]['id'] == document.id
        else response['allDocuments']['documents'][1]
    )
    graph_document_without_versions = (
        response['allDocuments']['documents'][0]
        if response['allDocuments']['documents'][0]['id'] == document_without_versions.id
        else response['allDocuments']['documents'][1]
    )

    assert graph_document['id'] == document.id
    assert sorted(graph_document['versions'], key=lambda x: x['number']) == sorted(
        [
            {
                'id': document_version_1.id,
                'number': '1.1',
            },
            {
                'id': document_version_2.id,
                'number': '1.2',
            },
            {
                'id': document_version_3.id,
                'number': '2.1',
            },
            {
                'id': document_version_4.id,
                'number': '2.2',
            },
        ],
        key=lambda x: x['number'],
    )
    assert graph_document_without_versions['id'] == document_without_versions.id
    assert graph_document_without_versions['canAddVersion'] is False
    assert graph_document_without_versions['versions'] == []

    # Assert for recipient
    # Recipient should see only published versions or version that was created by them
    graph_document = (
        response_recipient['allDocuments']['documents'][0]
        if response_recipient['allDocuments']['documents'][0]['id'] == document.id
        else response_recipient['allDocuments']['documents'][1]
    )
    graph_document_without_versions = (
        response_recipient['allDocuments']['documents'][0]
        if response_recipient['allDocuments']['documents'][0]['id'] == document_without_versions.id
        else response_recipient['allDocuments']['documents'][1]
    )

    assert graph_document['id'] == document.id
    assert sorted(graph_document['versions'], key=lambda x: x['number']) == sorted(
        [
            {
                'id': document_version_2.id,
                'number': '1.1',
            },
            {
                'id': document_version_4.id,
                'number': '2.1',
            },
            {
                'id': document_version_5.id,
                'number': '3.1',
            },
        ],
        key=lambda x: x['number'],
    )
    assert graph_document_without_versions['id'] == document_without_versions.id
    assert graph_document_without_versions['canAddVersion'] is True
    assert graph_document_without_versions['versions'] == []


@pytest.mark.parametrize(
    'data, res',
    [
        (
            [
                DocumentVersionRow(id=ID_1, is_sent=False, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_2, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_3, is_sent=True, type=DocumentVersionType.new_upload),
            ],
            {
                ID_1: '1.1',
                ID_2: '1.2',
                ID_3: '2.1',
            },
        ),
        (
            [
                DocumentVersionRow(id=ID_1, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_2, is_sent=True, type=DocumentVersionType.new_upload),
            ],
            {
                ID_1: '1.1',
                ID_2: '2.1',
            },
        ),
        (
            [
                DocumentVersionRow(id=ID_1, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_2, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_3, is_sent=False, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_4, is_sent=False, type=DocumentVersionType.new_upload),
            ],
            {
                ID_1: '1.1',
                ID_2: '2.1',
                ID_3: '3.1',
                ID_4: '3.2',
            },
        ),
        (
            [
                DocumentVersionRow(id=ID_1, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_2, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_3, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_4, is_sent=False, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_5, is_sent=False, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_6, is_sent=False, type=DocumentVersionType.new_upload),
            ],
            {
                ID_1: '1.1',
                ID_2: '2.1',
                ID_3: '3.1',
                ID_4: '4.1',
                ID_5: '4.2',
                ID_6: '4.3',
            },
        ),
        (
            [
                DocumentVersionRow(id=ID_1, is_sent=False, type=DocumentVersionType.new_upload),
                DocumentVersionRow(id=ID_2, is_sent=True, type=DocumentVersionType.new_upload),
                DocumentVersionRow(
                    id=ID_3, is_sent=True, type=DocumentVersionType.converted_format
                ),
                DocumentVersionRow(id=ID_4, is_sent=True, type=DocumentVersionType.new_upload),
            ],
            {
                ID_1: '1.1',
                ID_2: '1.2',
                ID_3: '1.3',
                ID_4: '2.1',
            },
        ),
    ],
)
def test_calculate_version_number(data, res):
    assert _calculate_version_number(data) == res
