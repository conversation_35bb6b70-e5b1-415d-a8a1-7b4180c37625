import typing

import pydantic
from pydantic.functional_validators import <PERSON><PERSON><PERSON><PERSON><PERSON>

from api.errors import (
    AccessDenied,
    Code,
    DoesNotExist,
    Error,
    Object,
)
from app.auth.db import (
    select_role_by_id,
)
from app.auth.schemas import CompanyConfig
from app.auth.types import User
from app.auth.utils import get_company_config, has_permission
from app.document_versions.const import (
    LATEST_DOCUMENT_VERSION_MARKER,
    MAX_VERSION_COUNT,
    ORIGINAL_DOCUMENT_VERSION_MARKER,
    VERSION_ALLOWED_EXTENSIONS,
)
from app.document_versions.db import (
    select_document_version,
)
from app.document_versions.schemas import VersionUpdateSchema
from app.document_versions.types import (
    DocumentVersion,
    VersionDeleteCtx,
    VersionUpdateCtx,
    VersionUploadCtx,
)
from app.document_versions.utils import (
    get_latest_document_version_available_for_company,
    get_version_count,
)
from app.documents.types import Document, DocumentWithUploader
from app.documents.validators import (
    validate_document_access,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import validators
from app.lib.database import DBConnection
from app.lib.enums import (
    DocumentStatus,
    Source,
)
from app.lib.result import GenericResult
from app.lib.types import DataDict
from app.lib.validators import validate_pydantic, validate_pydantic_adapter
from app.lib.validators_pydantic import UUIDAdapter
from app.uploads.constants import MB
from app.uploads.validators import (
    validate_file_extension,
    validate_file_size,
)


def _check_version_field(value: str) -> str:
    if value == LATEST_DOCUMENT_VERSION_MARKER:
        return value
    if value == ORIGINAL_DOCUMENT_VERSION_MARKER:
        return value

    return validators.UUID().check(value)


VersionFieldPydantic = typing.Annotated[
    str,
    pydantic.Field(..., description='Version marker or specific version id'),
    AfterValidator(_check_version_field),
]


async def validate_document_version_exists(
    conn: DBConnection,
    *,
    document_id: str,
    version_id: str,
) -> DocumentVersion:
    """Check if document version exists"""
    version = await select_document_version(
        conn=conn,
        document_id=document_id,
        version_id=version_id,
    )
    if not version:
        raise DoesNotExist(Object.document_version, version_id=version_id)

    return version


def validate_document_is_versioned(*, versions_count: int) -> None:
    """
    If a document is versioned, it has at least one version (original).
    """
    if versions_count == 0:
        raise Error(Code.access_denied, reason=_('Документ не є версійним'))


def validate_document_versions_limit(
    *,
    versions_count: int,
) -> None:
    """
    Check that the number of versions does not exceed the limit.
    """

    # TODO[version]: for now all companies can upload versioned documents
    #  after finding out how this feature will be used by companies.
    #  Use this ref (version_rate_limit) to find all places that should be uncommented.
    max_version_count = 50
    # owner_config = await select_company_config_by_edrpou(
    #     conn,
    #     edrpou=document.uploaded_by_edrpou,
    # )
    # max_version_count: int | None = owner_config.get(CompanyLimit.max_versions_count, 0)

    # do not allow upload more document that allowed by config(rate)
    if max_version_count is not None and versions_count >= max_version_count:
        raise Error(Code.overdraft)


def validate_document_version_extension(filename: str, company_config: CompanyConfig) -> str:
    """
    Check that the extension of the new version is allowed to upload for the company.

    The function returns the filename extension if it is allowed, otherwise raises an error.
    """
    uploads_config = company_config.uploads

    allowed_extensions = (
        uploads_config.allowed_extensions
        if get_flag(FeatureFlags.DOCUMENT_VERSIONS_UPLOAD_ANY)
        else VERSION_ALLOWED_EXTENSIONS
    )

    return validate_file_extension(
        filename=filename,
        allowed_extensions=allowed_extensions,
        error_details={},
    )


def validate_versioned_document_not_signed(document: Document) -> None:
    """
    Check that signing is not started for a given document because we currently don't support
    adding a new version to signed documents.
    """
    if document.status_id >= DocumentStatus.signed.value:
        raise Error(Code.invalid_document_status)


async def validate_version_upload(
    conn: DBConnection,
    content: bytes,
    filename: str,
    user: User,
    document_id: str,
) -> VersionUploadCtx:
    document_id = validators.validate_pydantic_adapter(UUIDAdapter, value=document_id)

    # {{ Check file
    if not content:
        raise Error(Code.empty_upload_file)

    company_config = await get_company_config(conn, company_edrpou=user.company_edrpou)

    extension = validate_document_version_extension(
        filename=filename,
        company_config=company_config,
    )

    validate_file_size(
        max_file_size=company_config.uploads.max_file_size * MB,
        content_length=len(content),
        error_details={},
    )
    # Check file }}

    # {{ Check user permission
    document = await validate_document_access(conn, user, document_id)
    # }}

    validate_versioned_document_not_signed(document=document)

    document_versions_count = await get_version_count(conn=conn, document_id=document.id)

    validate_document_is_versioned(versions_count=document_versions_count)

    validate_document_versions_limit(versions_count=document_versions_count)

    return VersionUploadCtx(
        document=document,
        content=content,
        extension=extension,
        version_count=document_versions_count,
    )


async def validate_version_delete(
    conn: DBConnection,
    user: User,
    document_id: str,
    version_id: str,
    source: Source,
) -> VersionDeleteCtx:
    document_id = validators.validate_pydantic_adapter(UUIDAdapter, value=document_id)
    version_id = validators.validate_pydantic_adapter(UUIDAdapter, value=version_id)

    document = await validate_document_access(conn, user, document_id)
    version = await validate_document_version_exists(
        conn=conn,
        document_id=document.id,
        version_id=version_id,
    )

    # {{ allow deleting only latest document
    latest_version = await get_latest_document_version_available_for_company(
        conn=conn,
        document_id=document_id,
        company_edrpou=user.company_edrpou,
    )
    if not latest_version:
        raise DoesNotExist(Object.document_version, id=version_id)
    if latest_version.id != version.id:
        raise Error(Code.invalid_action)
    # }}

    # do not allow upload for already signed documents
    if document.status_id >= DocumentStatus.signed.value:
        raise Error(Code.invalid_document_status)

    # do not allow deletion if another company tries to delete
    uploader_role = await select_role_by_id(conn=conn, role_id=version.role_id)
    if user.company_edrpou != (uploader_role and uploader_role.company_edrpou):
        raise Error(Code.access_denied)

    # do not allow deletion when it's first version
    document_versions_count = await get_version_count(conn, document_id=document.id)
    if document_versions_count == 1:
        raise Error(Code.invalid_action)

    return VersionDeleteCtx(
        document=document,
        version=version,
        user=user,
        request_source=source,
    )


async def validate_version_update(
    conn: DBConnection,
    user: User,
    document_id: str,
    version_id: str,
    raw_data: DataDict,
) -> VersionUpdateCtx:
    valid_data = validate_pydantic(VersionUpdateSchema, raw_data)
    document_id = validate_pydantic_adapter(UUIDAdapter, value=document_id)
    version_id = validate_pydantic_adapter(UUIDAdapter, value=version_id)

    document = await validate_document_access(conn, user, document_id)
    version = await validate_document_version_exists(
        conn=conn,
        document_id=document.id,
        version_id=version_id,
    )

    if (
        # do not allow update for already sent versions
        version.is_sent
        # only company that uploaded version
        or user.company_edrpou != version.company_edrpou
        # only user that uploaded version or admin
        or (user.role_id != version.role_id and not user.is_admin)
    ):
        raise Error(Code.invalid_action)

    return VersionUpdateCtx(
        document=document,
        version=version,
        user=user,
        data=valid_data,
    )


def can_upload_version(
    user: User,
    document: DocumentWithUploader,
    document_versions_count: int,
    document_owner_company_config: CompanyConfig,
    latest_sent_version: DocumentVersion | None,
) -> GenericResult[None, Error]:
    if not has_permission(user, {'can_add_document_versions'}):
        return GenericResult(error=AccessDenied())

    if document_versions_count >= MAX_VERSION_COUNT:
        return GenericResult(error=Error(Code.overdraft))

    # Only non-final documents can have versions
    if document.status.is_final:
        return GenericResult(
            error=Error(
                Code.invalid_action,
                reason=_('Неможливо додати версію до завершеного документу.'),
            )
        )
    # Check if given document.category is allowed for versioning
    if (
        document.category is not None
        and not document_owner_company_config.version_settings.category_config.is_allowed_category(
            document.category
        )
    ):
        return GenericResult(
            error=Error(
                Code.invalid_action,
                reason=_('Неможливо додати версію для документу з цією категорією.'),
            )
        )
    # Only version recipient can upload new version if document is sent
    if latest_sent_version and latest_sent_version.company_edrpou == user.company_edrpou:
        return GenericResult(
            error=Error(
                Code.invalid_action,
                reason=_('Неможливо додати версію до надісланого документу.'),
            )
        )

    return GenericResult(result=None)
