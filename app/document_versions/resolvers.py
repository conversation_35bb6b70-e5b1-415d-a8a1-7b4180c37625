from __future__ import annotations

from dataclasses import dataclass

import sqlalchemy as sa
from hiku.engine import (
    Context,
    pass_context,
)

from api.graph.constants import (
    DB_ENGINE_KEY,
)
from api.graph.types import FieldList
from api.graph.utils import get_graph_user
from app.auth.utils import (
    get_company_configs,
    get_default_company_config,
    has_permission,
)
from app.document_versions.db import (
    build_available_versions_filter,
    select_latest_document_versions,
)
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tables import document_version_table
from app.document_versions.validators import can_upload_version
from app.documents.db import (
    select_documents_by_ids_with_company_info,
)
from app.documents.types import DocumentWithUploader
from app.lib.database import DBRow
from app.lib.helpers import group_list
from app.lib.types import (
    StrList,
)
from app.models import select_all


@pass_context
async def resolve_document_versions_for_document(
    ctx: Context, fields_ids: StrList
) -> list[StrList]:
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in fields_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(
                sa.select([document_version_table.c.id, document_version_table.c.document_id])
                .select_from(document_version_table)
                .where(
                    sa.and_(
                        document_version_table.c.document_id.in_(fields_ids),
                        build_available_versions_filter(user.company_edrpou),
                    )
                )
                .order_by(document_version_table.c.date_created.desc())
            ),
        )

    mapping = group_list(rows, lambda r: r.document_id)

    return [[row.id for row in mapping[field_id]] for field_id in fields_ids]


@dataclass
class DocumentVersionRow:
    id: str
    is_sent: bool
    type: DocumentVersionType

    @classmethod
    def from_db_row(cls, row: DBRow) -> DocumentVersionRow:
        return cls(
            id=row.id,
            is_sent=row.is_sent,
            type=row.type,
        )


@pass_context
async def resolve_document_version_numbers(
    ctx: Context, fields: FieldList, version_ids: StrList
) -> list[StrList]:
    """
    Build version numbers for the given version IDs.
    """
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in version_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        all_versions_rows = await select_all(
            conn=conn,
            query=(
                sa.select(
                    [
                        document_version_table.c.id,
                        document_version_table.c.type,
                        document_version_table.c.is_sent,
                    ]
                )
                .select_from(document_version_table)
                .where(
                    sa.and_(
                        document_version_table.c.document_id.in_(
                            sa.select([document_version_table.c.document_id]).where(
                                document_version_table.c.id.in_(version_ids)
                            )
                        ),
                        build_available_versions_filter(user.company_edrpou),
                    )
                )
                .order_by(document_version_table.c.date_created.asc())
            ),
        )

    version_id_counter_mapping = _calculate_version_number(
        [DocumentVersionRow.from_db_row(row) for row in all_versions_rows],
    )
    return [[version_id_counter_mapping[version_id]] for version_id in version_ids]


def _calculate_version_number(
    rows: list[DocumentVersionRow],
) -> dict[str, str]:
    """
    Calculate version numbers based on the provided rows.
    The version counter is updated after each sent version.
    The subversion counter is incremented for each version until
     a new version is sent.


    1 version 1.1
    2 version 1.2
    3 version 1.3*
    4 version 2.1
    5 version 2.2*
    6 version 3.1*

    * - version is send
    """

    version_count = 1
    subversion_count = 1
    version_id_counter_mapping = {}
    for i, row in enumerate(rows):
        version_id_counter_mapping[row.id] = f'{version_count}.{subversion_count}'

        if row.is_sent:
            next_row = rows[i + 1] if i + 1 < len(rows) else None
            # conver version should be treated as subversion of the same version
            if next_row and next_row.type == DocumentVersionType.converted_format:
                # The next version is converted to pdf, so we should not increase the version count,
                # it should be treated as a subversion of the same version.
                # and will be increased on the next iteration.
                subversion_count += 1
                continue

            version_count += 1
            subversion_count = 1
        else:
            subversion_count += 1

    return version_id_counter_mapping


@pass_context
async def resolve_can_upload_version(
    ctx: Context, _: FieldList, document_ids: StrList
) -> list[list[bool]]:
    """
    Can given user upload a new version for the documents
    """
    user = get_graph_user(ctx)
    if not user:
        return [[False] for _ in document_ids]

    # No reason to check DB if user has no permission
    if not has_permission(user, {'can_add_document_versions'}):
        return [[False] for _ in document_ids]

    res = {}
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        documents = await select_documents_by_ids_with_company_info(
            conn=conn,
            document_ids=document_ids,
        )
        documents_mapping = {doc.id: DocumentWithUploader.from_row(doc) for doc in documents}

        documents_counter = await select_all(
            conn=conn,
            query=(
                sa.select(
                    [
                        document_version_table.c.document_id,
                        sa.func.count().label('version_count'),
                    ]
                )
                .where(document_version_table.c.document_id.in_(document_ids))
                .group_by(document_version_table.c.document_id)
            ),
        )
        documents_counter_mapping = {
            row.document_id: row.version_count for row in documents_counter
        }

        configs = await get_company_configs(
            conn=conn,
            company_edrpous=list({doc.edrpou_owner for doc in documents_mapping.values()}),
        )
        latest_sent_versions = await select_latest_document_versions(
            conn=conn,
            document_ids=document_ids,
        )
        latest_sent_versions_mapping = {row.document_id: row for row in latest_sent_versions}

        for document_id in document_ids:
            document = documents_mapping.get(document_id)
            if not document:
                res[document_id] = False
                continue

            check = can_upload_version(
                user=user,
                document=document,
                document_versions_count=documents_counter_mapping.get(document_id, 0),
                document_owner_company_config=configs.get(
                    document.edrpou_owner, get_default_company_config()
                ),
                latest_sent_version=latest_sent_versions_mapping.get(document_id),
            )
            res[document_id] = check.is_ok

    return [[res[document_id]] for document_id in document_ids]
