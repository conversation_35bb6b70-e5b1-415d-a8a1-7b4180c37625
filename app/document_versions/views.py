from http import HTT<PERSON>tatus

from aiohttp import web
from aiohttp.web_request import <PERSON><PERSON><PERSON>

from api.errors import InvalidRequest
from app.actions.utils import get_source
from app.auth.types import User
from app.auth.validators import validate_user_permission
from app.document_versions import utils
from app.document_versions.db import update_document_version
from app.document_versions.utils import (
    delete_latest_version,
)
from app.document_versions.validators import (
    validate_version_delete,
    validate_version_update,
    validate_version_upload,
)
from app.i18n import _
from app.lib.validators import validate_json_request, validate_post_request
from app.services import services


async def upload_version(request: web.Request, user: User) -> web.Response:
    """
    Handles upload of new version.
    """
    validate_user_permission(user, {'can_upload_document'})

    document_id = request.match_info['document_id']
    data = dict(await validate_post_request(request))

    file = data.get('file')
    if not isinstance(file, FileField):
        raise InvalidRequest(reason=_('Use multipart/form-data content-type'))

    async with services.db.acquire() as conn:
        validated = await validate_version_upload(
            conn=conn,
            content=file.file.read(),
            filename=file.filename,
            user=user,
            document_id=document_id,
        )
        await utils.add_new_upload_document_version(
            conn=conn,
            validated=validated,
            user=user,
            source=get_source(request),
        )

    return web.json_response(status=HTTPStatus.NO_CONTENT)


async def delete_version(request: web.Request, user: User) -> web.Response:
    """
    Handles deletion of version.
    """

    version_id = request.match_info['version_id']
    document_id = request.match_info['document_id']

    async with services.db.acquire() as conn:
        ctx = await validate_version_delete(
            conn=conn,
            user=user,
            document_id=document_id,
            version_id=version_id,
            source=get_source(request),
        )
        await delete_latest_version(
            conn=conn,
            ctx=ctx,
        )

    return web.json_response(status=HTTPStatus.NO_CONTENT)


async def update_version(request: web.Request, user: User) -> web.Response:
    """
    Handles update of version's metadata.
    """

    version_id = request.match_info['version_id']
    document_id = request.match_info['document_id']

    raw_data = await validate_json_request(request)

    async with services.db.acquire() as conn:
        ctx = await validate_version_update(
            conn=conn,
            user=user,
            document_id=document_id,
            version_id=version_id,
            raw_data=raw_data,
        )
        await update_document_version(
            conn=conn,
            version_id=ctx.version.id,
            data={
                'description': ctx.data.description,
            },
        )

    return web.json_response(status=HTTPStatus.NO_CONTENT)
