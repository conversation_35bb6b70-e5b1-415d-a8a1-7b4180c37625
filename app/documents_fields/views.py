from http import HTTPStatus

from aiohttp import web

from app.auth.db import select_roles_by_ids
from app.auth.decorators import login_required
from app.auth.types import User
from app.documents_fields import utils
from app.documents_fields.db import select_document_fields_by_ids
from app.documents_fields.validators import (
    validate_add_fields_access,
    validate_create_documents_field,
    validate_delete_documents_field,
    validate_swap_documents_fields,
    validate_update_documents_fields,
)
from app.events.user_actions import types
from app.events.user_actions.utils import add_user_action, add_user_actions, get_event_source
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.helpers import (
    json_response,
)


async def create(request: web.Request, user: User) -> web.Response:
    async with request.app['db'].acquire() as conn:
        ctx = await validate_create_documents_field(conn, request, user)
        field = await utils.create(conn, ctx)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await add_user_action(
            user_action=types.UserAction(
                action=types.Action.document_field_create,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={'name': ctx.name},
            )
        )

    return json_response(field.to_dict(), status=HTTPStatus.CREATED)


@login_required()
async def delete(request: web.Request, user: User) -> web.Response:
    async with request.app['db'].acquire() as conn:
        ctx = await validate_delete_documents_field(conn, request, user)
        await utils.delete(conn, ctx)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await add_user_action(
            user_action=types.UserAction(
                action=types.Action.document_field_delete,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={'name': ctx.field.name},
            )
        )

    return web.Response(status=HTTPStatus.OK)


@login_required()
async def swap(request: web.Request, user: User) -> web.Response:
    async with request.app['db'].acquire() as conn:
        ctx = await validate_swap_documents_fields(conn, request, user)
        await utils.swap(conn, ctx)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        events = [
            types.UserAction(
                action=types.Action.document_field_update,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={'name': ctx.first_field.name},
            ),
            types.UserAction(
                action=types.Action.document_field_update,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={'name': ctx.second_field.name},
            ),
        ]
        await add_user_actions(events)

    return web.Response(status=HTTPStatus.OK)


@login_required()
async def update(request: web.Request, user: User) -> web.Response:
    async with request.app['db'].acquire() as conn:
        ctx = await validate_update_documents_fields(conn, request, user)
        await utils.update(conn, ctx)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await add_user_action(
            user_action=types.UserAction(
                action=types.Action.document_field_update,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={'name': ctx.name},
            )
        )

    return web.Response(status=HTTPStatus.OK)


async def create_access(request: web.Request, user: User) -> web.Response:
    """Mass add fields access for roles"""

    async with request.app['db'].acquire() as conn:
        ctx = await validate_add_fields_access(conn, request, user)
        await utils.add_access(conn, ctx, user)

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            roles = await select_roles_by_ids(conn, ctx.roles_ids)
            fields = await select_document_fields_by_ids(conn, ctx.fields_ids)
            events = []

            for role in roles:
                for field in fields:
                    if role and field:
                        events.append(
                            types.UserAction(
                                action=types.Action.document_field_access_create,
                                source=get_event_source(request),
                                email=user.email,
                                user_id=user.id,
                                phone=user.auth_phone,
                                company_id=user.company_id,
                                extra={'name': field.name, 'affected_user_email': role.user_email},
                            )
                        )
            await add_user_actions(events)

    return web.Response(status=HTTPStatus.OK)


async def delete_access(request: web.Request, user: User) -> web.Response:
    """Mass delete fields access for roles"""

    async with request.app['db'].acquire() as conn:
        ctx = await validate_add_fields_access(conn, request, user)
        await utils.delete_access(conn, ctx, user)

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            roles = await select_roles_by_ids(conn, ctx.roles_ids)
            fields = await select_document_fields_by_ids(conn, ctx.fields_ids)
            events = []

            for role in roles:
                for field in fields:
                    if role and field:
                        events.append(
                            types.UserAction(
                                action=types.Action.document_field_access_delete,
                                source=get_event_source(request),
                                email=user.email,
                                user_id=user.id,
                                phone=user.auth_phone,
                                company_id=user.company_id,
                                extra={'name': field.name, 'affected_user_email': role.user_email},
                            )
                        )
            await add_user_actions(events)

    return web.Response(status=HTTPStatus.OK)
