import datetime
import json
import logging
from collections.abc import AsyncIterator, Sequence
from dataclasses import dataclass

from aiohttp import web

from app.auth import db as auth_db
from app.auth.types import User
from app.events import utils as events_utils
from app.events.constants import ACTIONS_REPORT_CHUNK_SIZE, ACTIONS_REPORT_ROWS_PER_FILE
from app.events.db import (
    insert_actions_report,
    insert_actions_report_file,
)
from app.events.enums import ActionsReportType
from app.events.types import DeleteTokenData
from app.events.user_actions import db, types
from app.events.user_actions.types import UserActionsReportOutput
from app.i18n import _
from app.lib import xlsx
from app.lib.database import DBConnection
from app.lib.datetime_utils import to_local_datetime
from app.lib.helpers import generate_uuid
from app.services import services
from app.uploads.constants import MB
from worker import topics

logger = logging.getLogger(__name__)


def get_event_source(request: web.Request) -> types.Source:
    """
    Get user event source from request object.
    """

    if request.rel_url.path[:5] == '/api/':
        return types.Source.public

    if request.rel_url.path[:21] == '/mobile-api/':
        return types.Source.mobile

    return types.Source.internal


async def build_token_create_user_actions(
    *,
    actor_user: User,
    role_ids: list[str],
    expire_date: datetime.datetime | None,
    request: web.Request,
) -> list[types.UserAction]:
    """
    Build UserAction records for token creation actions with affected user details.
    """
    from app.lib.helpers import get_client_ip

    source = get_event_source(request)

    async with services.db.acquire() as conn:
        affected_users = await auth_db.select_users_by_role_ids(conn, role_ids)

    user_actions: list[types.UserAction] = []
    for affected_user in affected_users:
        affected_user_email = affected_user.email if affected_user is not None else None
        formatted_expire = expire_date.strftime('%Y-%m-%d') if expire_date else None
        user_actions.append(
            types.UserAction(
                action=types.Action.token_create,
                source=source,
                email=affected_user.email if affected_user else None,
                user_id=affected_user.id if affected_user else None,
                phone=affected_user.phone if affected_user else None,
                company_id=actor_user.company_id,
                extra={
                    'affected_user_email': affected_user_email,
                    'expire_date': formatted_expire,
                    'ip': get_client_ip(request),
                    'user_agent': request.headers.get('User-Agent'),
                },
            )
        )

    return user_actions


async def build_token_delete_user_actions(
    *,
    actor_user: User,
    tokens: list[DeleteTokenData],
    request: web.Request,
) -> list[types.UserAction]:
    """
    Build UserAction records for token deletion actions with affected user details.
    """
    from app.lib.helpers import get_client_ip

    source = get_event_source(request)

    role_ids = [token.role_id for token in tokens]
    token_role_expiring_mapping = {token.role_id: token.expiring for token in tokens}

    async with services.db.acquire() as conn:
        affected_users = await auth_db.select_users_by_role_ids(conn, role_ids)

    user_actions: list[types.UserAction] = []
    for affected_user in affected_users:
        affected_user_email = affected_user.email if affected_user is not None else None
        expire_date = (
            token_role_expiring_mapping.get(affected_user.role_id) if affected_user else None
        )
        formatted_expire = expire_date.strftime('%Y-%m-%d') if expire_date else None
        user_actions.append(
            types.UserAction(
                action=types.Action.token_delete,
                source=source,
                email=affected_user.email if affected_user else None,
                user_id=affected_user.id if affected_user else None,
                phone=affected_user.phone if affected_user else None,
                company_id=actor_user.company_id,
                extra={
                    'affected_user_email': affected_user_email,
                    'expire_date': formatted_expire,
                    'ip': get_client_ip(request),
                    'user_agent': request.headers.get('User-Agent'),
                },
            )
        )

    return user_actions


_SUPPORTED_ROLE_UPDATE_KEYS: set[str] = {
    'can_view_document',
    'can_view_private_document',
    'can_comment_document',
    'can_upload_document',
    'can_download_document',
    'can_print_document',
    'can_delete_document',
    'can_sign_and_reject_document',
    'can_sign_and_reject_document_external',
    'can_sign_and_reject_document_internal',
    'can_invite_coworkers',
    'can_edit_company',
    'can_edit_roles',
    'can_create_tags',
    'can_edit_document_automation',
    'can_edit_document_fields',
    'can_edit_document_category',
    'can_extract_document_structured_data',
    'can_edit_document_structured_data',
    'can_archive_documents',
    'can_delete_archived_documents',
    'can_edit_templates',
    'can_edit_directories',
    'can_remove_itself_from_approval',
    'can_change_document_signers_and_reviewers',
    'can_delete_document_extended',
    'can_edit_company_contact',
    'can_edit_required_fields',
    'can_download_actions',
    'can_edit_security',
    'allowed_ips',
    'status',
    'user_role',
}


async def build_role_update_user_actions(
    *,
    actor_user: User,
    role_id: str,
    update_role_data: dict[str, object],
    request: web.Request,
) -> list[types.UserAction]:
    """
    Build UserAction records for role update with per-field changes as separate actions.

    Identity fields (email/user_id/phone) reflect the affected user of updated role.
    """
    from app.auth.enums import RoleStatus
    from app.lib.helpers import get_client_ip

    source = get_event_source(request)

    async with services.db.acquire() as conn:
        affected_user = await auth_db.select_user(conn, role_id=role_id)

    if not affected_user:
        logger.warning(
            'Could not find user for role update',
            extra={'role_id': role_id, 'actor_company_id': actor_user.company_id},
        )
        return []

    def _normalize_value(value: object) -> object:
        if isinstance(value, RoleStatus):
            return value.value
        return value

    actions: list[types.UserAction] = []
    for changed_setting, new_value in update_role_data.items():
        if changed_setting not in _SUPPORTED_ROLE_UPDATE_KEYS:
            logger.warning(
                'Skip unsupported role update key',
                extra={
                    'role_id': role_id,
                    'actor_company_id': actor_user.company_id,
                    'changed_setting': changed_setting,
                },
            )
            continue

        actions.append(
            types.UserAction(
                action=types.Action.role_update,
                source=source,
                email=affected_user.email,
                user_id=affected_user.id,
                phone=affected_user.phone,
                company_id=actor_user.company_id,
                extra={
                    'changed_permission': changed_setting,
                    'new_value': _normalize_value(new_value),
                    'affected_user_email': affected_user.email,
                    'ip': get_client_ip(request),
                    'user_agent': request.headers.get('User-Agent'),
                },
            )
        )

    return actions


@dataclass(kw_only=True)
class UserActionsReportPaginator:
    company_id: str
    date_to: datetime.datetime

    last_date_from: datetime.datetime
    last_record_id: str | None

    rows_count: int = 0
    has_more_rows: bool = False

    max_rows: int = ACTIONS_REPORT_ROWS_PER_FILE
    chunk_size: int = ACTIONS_REPORT_CHUNK_SIZE

    async def get_user_actions(self, conn: DBConnection) -> AsyncIterator[types.UserActionDB]:
        """
        Get user actions for the given company and date range.
        """

        actions_stream = db.stream_user_actions_for_report(
            conn=conn,
            company_id=self.company_id,
            last_date_from=self.last_date_from,
            date_to=self.date_to,
            last_record_id=self.last_record_id,
            chunk_size=self.chunk_size,
            limit=self.max_rows + 1,  # one more record for "has_more_rows" check
        )
        async for action in actions_stream:
            # Skip the extra record that we use as marker for "has_more_rows"
            if (self.rows_count + 1) > self.max_rows:
                self.has_more_rows = True
                # Do not use "break" here, uvloop in debug mode have bug that causes segfault.
                # The "continue" statement lets the generator finish gracefully.
                # https://github.com/MagicStack/uvloop/issues/611
                continue

            self.rows_count += 1
            self.last_date_from = action.date_created
            self.last_record_id = action.id
            yield action


async def build_report(
    company_id: str,
    company_edrpou: str,
    date_to: datetime.datetime,
    last_date_from: datetime.datetime,
    last_record_id: str | None,
) -> UserActionsReportOutput:
    """
    Build xlsx file with user actions for company within period
    """

    # Initialize builder and header
    writer = xlsx.XLSXWriter()
    writer.append_row(
        row=[
            _('Дія'),
            _('Джерело'),
            _('ID співробітника'),
            _('Телефон співробітника'),
            _('Пошта співробітника'),
            _('Дата і час'),
            _('Додаткова інфо про подію'),
        ],
        bold=True,
    )

    paginator = UserActionsReportPaginator(
        company_id=company_id,
        date_to=date_to,
        last_date_from=last_date_from,
        last_record_id=last_record_id,
    )
    async with services.events_db.acquire() as conn:
        async for action in paginator.get_user_actions(conn):
            writer.append_row(
                row=[
                    action.action.translation,
                    action.source.translation,
                    action.user_id or '',
                    action.phone or '',
                    action.email or '',
                    to_local_datetime(action.date_created).strftime('%d.%m.%Y %H:%M:%S'),
                    json.dumps(action.extra, ensure_ascii=False),
                ],
            )
    content = writer.to_bytes()

    logger.info(
        'Built report for company',
        extra={
            'company_id': company_id,
            'report_size_mb': len(content) / MB,
            'last_date_from': paginator.last_date_from,
            'last_record_id': paginator.last_record_id,
            'has_more_rows': paginator.has_more_rows,
            'rows_count': paginator.rows_count,
        },
    )

    # Example: user_actions_123456789_20231002120000_20231002120000.xlsx
    _date_1 = last_date_from.strftime('%Y%m%d%H%M%S')
    _date_2 = paginator.date_to.strftime('%Y%m%d%H%M%S')
    filename = f'user_actions_{company_edrpou}_{_date_1}_{_date_2}.xlsx'

    return UserActionsReportOutput(
        content=content,
        filename=filename,
        has_more_rows=paginator.has_more_rows,
        last_date_from=paginator.last_date_from,
        last_record_id=paginator.last_record_id,
    )


async def prepare_actions_report_file(
    company_id: str,
    company_edrpou: str,
    report_id: str,
    date_to: datetime.datetime,
    last_date_from: datetime.datetime,
    last_record_id: str | None,
) -> UserActionsReportOutput:
    output = await build_report(
        company_id=company_id,
        company_edrpou=company_edrpou,
        date_to=date_to,
        last_date_from=last_date_from,
        last_record_id=last_record_id,
    )

    file_id = generate_uuid()

    await events_utils.upload_actions_report_file(
        company_id=company_id,
        report_id=report_id,
        file_id=file_id,
        file_content=output.content,
    )

    async with services.events_db.acquire() as conn:
        await insert_actions_report_file(
            conn=conn,
            file_id=file_id,
            company_id=company_id,
            report_id=report_id,
            filename=output.filename,
            size=len(output.content),
        )

    return output


async def start_actions_report(
    *,
    user: User,
    date_from: datetime.datetime,
    date_to: datetime.datetime,
    from_public_api: bool = False,
) -> str:
    """
    The main responsibility of this function is to record user request to export actions and
    move to the next step. It is lightweight, so timeouts are not expected.

    This function is the first part of the 3-part process of exporting user actions.

    Returns:
        str: Report ID
    """

    async with services.events_db.acquire() as conn:
        report = await insert_actions_report(
            conn=conn,
            company_id=user.company_id,
            date_from=date_from,
            date_to=date_to,
            type=ActionsReportType.users,
            created_by=user.role_id,
        )

    await services.kafka.send_record(
        topic=topics.USER_ACTIONS_REPORT_PREPARE,
        value={
            'role_id': user.role_id,
            'company_id': user.company_id,
            'company_edrpou': user.company_edrpou,
            'report_id': report.id,
            'date_to': date_to,
            'last_date_from': date_from,
            'last_record_id': None,
            'from_public_api': from_public_api,
        },
    )
    return report.id


async def add_user_actions(
    user_actions: Sequence[types.UserAction],
) -> Sequence[types.UserActionDB]:
    """Add user actions to database"""
    if not user_actions:
        return []

    return await db.insert_user_actions(user_actions=user_actions)


async def add_user_action(
    user_action: types.UserAction,
) -> types.UserActionDB:
    """Add user action to database"""

    return await db.insert_user_action(user_action=user_action)
