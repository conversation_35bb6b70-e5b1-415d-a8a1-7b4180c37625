import abc
import collections.abc as col_abc
import datetime
import datetime as dt
import logging
import typing as t
import uuid
from dataclasses import dataclass

from typing_extensions import deprecated

import app.auth.db as auth_db
from app.auth.enums import RoleStatus
from app.auth.types import AuthUser, BaseUser, User, is_wide_user_type
from app.document_automation.enums import DocumentAutomationStatus
from app.documents_required_fields.enums import DocumentCategoryFields
from app.events import enums, reporting
from app.events.enums import ActionsReportType
from app.events.types import (
    DeleteTokenData,
    JsonSerializableData,
)
from app.groups.types import Group, GroupMember
from app.lib.database import DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import UserRole
from app.lib.types import (
    AnyDict,
    DataDict,
    StrList,
)
from app.services import services

logger = logging.getLogger(__name__)

_EventT = t.TypeVar('_EventT', bound=type['Event'])

_EVENT_CLASSES_BY_NAME: col_abc.MutableMapping[str, type['Event']] = {}

DOCUMENT_CATEGORY_FIELDS_DISPLAY_NAME_MAP = {
    DocumentCategoryFields.any.value: 'Всі вхідні документи',
    DocumentCategoryFields.act.value: 'Акт наданих послуг',
    DocumentCategoryFields.bill.value: 'Рахунок',
    DocumentCategoryFields.contract.value: 'Договір',
    DocumentCategoryFields.additional_contract.value: 'Додаткова угода до договору',
    DocumentCategoryFields.sales_invoice.value: 'Видаткова накладна',
    DocumentCategoryFields.ttn.value: 'Товарно-транспортна накладна',
    DocumentCategoryFields.other.value: 'Тип не обрано',
    DocumentCategoryFields.edi.value: 'EDI документи',
    DocumentCategoryFields.power_of_attorney.value: 'Довіреність',
    DocumentCategoryFields.specification.value: 'Специфікація',
    DocumentCategoryFields.statement.value: 'Заява',
    DocumentCategoryFields.act_of_acceptance_transfer.value: 'Акт приймання-передачі',
    DocumentCategoryFields.returning.value: 'Повернення',
    DocumentCategoryFields.order.value: 'Замовлення',
    DocumentCategoryFields.appendix.value: 'Додаток',
    DocumentCategoryFields.another.value: 'Інший',
    DocumentCategoryFields.verification_act.value: 'Акт звіряння',
    DocumentCategoryFields.differences_protocol.value: 'Протокол розбіжностей',
    DocumentCategoryFields.report.value: 'Звіт',
    DocumentCategoryFields.letter.value: 'Лист',
    DocumentCategoryFields.protocol.value: 'Протокол',
    DocumentCategoryFields.decree.value: 'Наказ',
    DocumentCategoryFields.act_of_return.value: 'Акт повернення',
    DocumentCategoryFields.adjustment_calculation.value: 'Розрахунок коригування',
    DocumentCategoryFields.act_of_adjustment.value: 'Акт коригування',
    DocumentCategoryFields.letter_of_guarantee.value: 'Гарантійний лист',
    DocumentCategoryFields.accounting_memo.value: 'Бухгалтерська довідка',
    DocumentCategoryFields.write_off_act.value: 'Акт списання',
    DocumentCategoryFields.adjustment_invoice.value: 'Коригуюча накладна',
    DocumentCategoryFields.advance_report.value: 'Авансовий звіт',
    DocumentCategoryFields.inventory_act.value: 'Акт інвентаризації',
    DocumentCategoryFields.budget.value: 'Кошторис',
    DocumentCategoryFields.tech_specification.value: 'Технічне завдання',
    DocumentCategoryFields.financial_certificate.value: 'Фінансова довідка',
    DocumentCategoryFields.financial_reporting.value: 'Фінансова звітність',
    DocumentCategoryFields.offer.value: 'Оферта',
    DocumentCategoryFields.receiving_note.value: 'Прибуткова накладна',
    DocumentCategoryFields.return_statement.value: 'Заява на повернення коштів',
    DocumentCategoryFields.return_delivery_note.value: 'Видаткова накладна на повернення',
    DocumentCategoryFields.commission_agent_report.value: 'Звіт комісіонера',
    DocumentCategoryFields.act_appendix.value: 'Додаток до акту',
    DocumentCategoryFields.transportation_request.value: 'Заявка на перевезення',
    DocumentCategoryFields.license.value: 'Ліцензія',
    DocumentCategoryFields.credit_application.value: 'Кредитна заява',
}


def event_type(cls: _EventT) -> _EventT:
    """Register an event type."""
    _EVENT_CLASSES_BY_NAME[cls.event_type.value] = cls
    return cls


class _EventDBRepr(t.TypedDict):
    """An event's database representation."""

    id: str
    actor_email: str | None
    actor_edrpou: str | None
    event: str
    actor_role_id: str | None
    request_source: enums.EventSource
    extra: dict[str, t.Any]
    date_created: dt.datetime


@deprecated('Migrate to UserAction')
class Event(abc.ABC):
    event_type: t.ClassVar[enums.EventType]
    event_type_reportable: t.ClassVar[str]
    feature_class: t.ClassVar[str]

    id: str
    actor_email: str | None
    actor_edrpou: str | None
    actor_role_id: str | None
    request_source: enums.EventSource
    extra: dict[str, t.Any]
    date_created: dt.datetime
    seqnum: int | None
    actor_company_id: str | None = None

    # Was not exists in original event implementation, but was added to keep compatibility with
    # new UserAction fields
    actor_user_id: str | None
    actor_auth_phone: str | None

    def __init__(
        self,
        *,
        id: str,
        actor_email: str | None,
        actor_edrpou: str | None,
        actor_role_id: str | None,
        request_source: enums.EventSource,
        extra: JsonSerializableData,
        date_created: dt.datetime,
        seqnum: int | None = None,
        actor_company_id: str | None = None,
        actor_user_id: str | None,
        actor_auth_phone: str | None,
    ) -> None:
        self.id = id
        self.actor_email = actor_email
        self.actor_edrpou = actor_edrpou
        self.actor_role_id = actor_role_id
        self.actor_company_id = actor_company_id
        self.request_source = request_source
        self.extra = extra
        self.date_created = date_created
        self.seqnum = seqnum
        self.actor_user_id = actor_user_id
        self.actor_auth_phone = actor_auth_phone

    def __repr__(self) -> str:
        return (
            f'<{self.__class__.__name__} {self.event_type.value} {self.actor_email} {self.extra}>'
        )

    @abc.abstractmethod
    def as_report_row(self) -> reporting.ReportRow:
        """Return the event as a row in the reporting table."""

    @classmethod
    def from_db_row(cls, row: DBRow) -> 'Event':
        event_cls = _EVENT_CLASSES_BY_NAME[row.event]
        return event_cls(
            id=row.id,
            actor_email=row.actor_email,
            actor_edrpou=row.actor_edrpou,
            actor_role_id=row.actor_role_id,
            request_source=row.request_source,
            extra=row.extra,
            date_created=row.date_created,
            seqnum=row.seqnum,
            # was added recently to user actions model, but was not in original event
            # implementation, so they are always set to None when we create an event from
            # DB row
            actor_user_id=None,
            actor_auth_phone=None,
        )

    @classmethod
    async def make(
        cls,
        user: t.Any,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        """Construct an Event."""
        _user: BaseUser | User | AuthUser = user
        extra = extra if extra is not None else {}
        date_created = date_created if date_created is not None else utc_now()

        async with services.db.acquire() as conn:
            last_role_details = await auth_db.select_last_role_details(conn=conn, user=_user)

        return cls(
            id=uuid.uuid4().hex,
            actor_email=_user.email,
            actor_role_id=last_role_details.role_id,
            actor_edrpou=last_role_details.company_edrpou,
            actor_company_id=last_role_details.company_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=_user.id,
            actor_auth_phone=getattr(_user, 'auth_phone', None),
        )

    def as_db_repr(self) -> _EventDBRepr:
        """Return a dictionary representation of the event."""
        return _EventDBRepr(
            id=self.id,
            event=t.cast(str, self.event_type.value),
            actor_email=self.actor_email,
            actor_edrpou=self.actor_edrpou,
            actor_role_id=self.actor_role_id,
            request_source=self.request_source,
            extra=self.extra,
            date_created=self.date_created,
        )


# TODO: remove this class after full migration to user_actions
@deprecated('Migrate to UserAction')
@event_type
class LoginEvent(Event):
    """A successful login event."""

    event_type = enums.EventType.LOGIN_SUCCESSFUL
    event_type_reportable = 'Вхід'
    feature_class = 'Автентифікація'

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            'Успішний',
        )


# TODO: remove this class after full migration to user_actions
@deprecated('Migrate to UserAction')
@event_type
class UnsuccessfulLoginEvent(Event):
    """An unsuccessful login event."""

    event_type = enums.EventType.LOGIN_UNSUCCESSFUL
    event_type_reportable = 'Вхід'
    feature_class = 'Автентифікація'

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            'Неуспішний',
        )


@deprecated('Migrate to UserAction')
@event_type
class LogoutEvent(Event):
    event_type = enums.EventType.LOGOUT_SUCCESSFUL
    event_type_reportable = 'Вихід із сервісу'
    feature_class = 'Автентифікація'

    @classmethod
    async def make_for_actor(
        cls,
        user: BaseUser,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
        actor_role_id: str | None = None,
        actor_company_edrpou: str | None = None,
        actor_company_id: str | None = None,
    ) -> 'Event':
        """
        Construct an Event.
        Where actor can be different from the user.
        """
        extra = extra if extra is not None else {}
        date_created = date_created if date_created is not None else utc_now()

        if not all([actor_role_id, actor_company_edrpou, actor_company_id]):
            async with services.db.acquire() as conn:
                last_role_details = await auth_db.select_last_role_details(conn=conn, user=user)
            actor_role_id = last_role_details.role_id
            actor_company_edrpou = last_role_details.company_edrpou
            actor_company_id = last_role_details.company_id

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_role_id=actor_role_id,
            actor_edrpou=actor_company_edrpou,
            actor_company_id=actor_company_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            'Успішний',
        )


@deprecated('Migrate to UserAction')
@event_type
class LoginToCompany(Event):
    event_type = enums.EventType.LOGIN_TO_COMPANY
    event_type_reportable = 'Вхід в кабінет компанії'
    feature_class = 'Автентифікація'

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
        )


# TODO: remove this class after full migration to user_actions
@deprecated('Migrate to UserAction')
@event_type
class LogoutFromCompany(Event):
    event_type = enums.EventType.LOGOUT_FROM_COMPANY
    event_type_reportable = 'Вихід з кабінету компанії'
    feature_class = 'Автентифікація'

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
        )


@deprecated('Migrate to UserAction')
@event_type
class GenerateTokenEvent(Event):
    """
    Generate token generation event.
    """

    event_type = enums.EventType.GENERATE_TOKEN
    feature_class = 'Інтеграція'
    event_type_reportable = 'Генерація токена для співробітника'

    @classmethod
    async def make_many(
        cls,
        user: User,
        source: enums.EventSource,
        target_role_ids: StrList,
        expire_date: datetime.datetime | None,
        ip: str | None = None,
        user_agent: str | None = None,
    ) -> list['GenerateTokenEvent']:
        events = []
        async with services.db.acquire() as db_conn:
            affected_users = await auth_db.select_users_by_role_ids(db_conn, target_role_ids)

        for affected_user in affected_users:
            affected_user_email = affected_user.email if affected_user is not None else ''
            events.append(
                cls(
                    id=uuid.uuid4().hex,
                    actor_email=user.email,
                    actor_edrpou=user.company_edrpou,
                    actor_company_id=user.company_id,
                    actor_role_id=user.role_id,
                    request_source=source,
                    extra={
                        'affected_user_email': affected_user_email,
                        'expire_date': expire_date and expire_date.strftime('%Y-%m-%d'),
                        'ip': ip,
                        'user_agent': user_agent,
                    },
                    date_created=utc_now(),
                    actor_user_id=user.id,
                    actor_auth_phone=user.auth_phone,
                )
            )

        return events

    @property
    def _display_expire(self) -> str:
        if self.extra['expire_date']:
            return f'Термін дії до: {self.extra["expire_date"]}'

        return 'Безстроковий'

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            self._display_expire,
            self.extra['affected_user_email'],
        )


@deprecated('Migrate to UserAction')
@event_type
class DeleteTokenEvent(Event):
    """
    Generate token generation event.
    """

    event_type = enums.EventType.DELETE_TOKEN
    feature_class = 'Інтеграція'
    event_type_reportable = 'Видалення токена для співробітника'

    @classmethod
    async def make_many(
        cls,
        user: User,
        source: enums.EventSource,
        tokens: list[DeleteTokenData],
        ip: str | None = None,
        user_agent: str | None = None,
    ) -> list['DeleteTokenEvent']:
        events = []
        async with services.db.acquire() as db_conn:
            affected_users = await auth_db.select_users_by_role_ids(
                db_conn, [token.role_id for token in tokens]
            )
        token_role_expiring_mapping = {token.role_id: token.expiring for token in tokens}

        for affected_user in affected_users:
            affected_user_email = affected_user.email if affected_user is not None else ''
            expire_date = token_role_expiring_mapping[affected_user.role_id]
            events.append(
                cls(
                    id=uuid.uuid4().hex,
                    actor_email=user.email,
                    actor_edrpou=user.company_edrpou,
                    actor_company_id=user.company_id,
                    actor_role_id=user.role_id,
                    request_source=source,
                    extra={
                        'affected_user_email': affected_user_email,
                        'expire_date': expire_date and expire_date.strftime('%Y-%m-%d'),
                        'ip': ip,
                        'user_agent': user_agent,
                    },
                    date_created=utc_now(),
                    actor_user_id=user.id,
                    actor_auth_phone=user.auth_phone,
                )
            )

        return events

    @property
    def _display_expire(self) -> str:
        if self.extra['expire_date']:
            return f'Термін дії до: {self.extra["expire_date"]}'

        return 'Безстроковий'

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            self._display_expire,
            self.extra['affected_user_email'],
        )


@deprecated('Migrate to UserAction')
@event_type
class UpdateRoleEvent(Event):
    """A role settings update event."""

    event_type = enums.EventType.UPDATE_ROLE_SUCCESSFUL
    feature_class = 'Зміна налаштувань співробітника'

    _UPDATE_KEYS_TO_DISPLAY_NAMES = {
        'can_view_document': 'Перегляд всіх документів компанії',
        'can_view_private_document': 'Перегляд всіх приватних документів',
        'can_comment_document': 'Коментування документів',
        'can_upload_document': 'Завантаження документів у сервіс',
        'can_download_document': 'Вивантаження документів на локальний комп’ютер',
        'can_print_document': 'Друк документів',
        'can_delete_document': 'Видалення документів',
        'can_sign_and_reject_document': 'Підписання та відхилення документів',
        'can_sign_and_reject_document_external': 'Підписання та відхилення зовнішніх документів',
        'can_sign_and_reject_document_internal': 'Підписання та відхилення внутрішніх документів',
        'can_invite_coworkers': 'Запрошення співробітників',
        'can_edit_company': 'Редагування інформації про компанію',
        'can_edit_roles': 'Редагування та видалення співробітників',
        'can_create_tags': 'Створення нових ярликів',
        'can_edit_document_automation': 'Налаштування сценаріїв документів',
        'can_edit_document_fields': 'Налаштування параметрів документів',
        'can_edit_document_category': 'Налаштування типів внутрішніх документів',
        'can_extract_document_structured_data': 'Витягування структурованих даних з документів',
        'can_edit_document_structured_data': 'Редагування структурованих даних документів',
        'can_archive_documents': 'Архівація документів',
        'can_delete_archived_documents': 'Налаштування видалення архівних документів',
        'can_edit_templates': 'Налаштування шаблонів документів',
        'can_edit_directories': 'Налаштування структури папок в архіві',
        'can_remove_itself_from_approval': 'Видалення себе зі списку погоджувачів',
        'allowed_ips': 'Налаштування доступу тільки з певних IP адрес',
        'status': 'Зміна статусу',
        'user_role': 'Адміністратор компанії',
        'can_edit_security': 'Редагування налаштувань безпеки',
        'can_download_actions': 'Завантаження звіту про дії',
        'can_change_document_signers_and_reviewers': 'Зміна підписантів та рецензентів документів',
        'can_delete_document_extended': 'Розширені права на видалення документів',
        'can_edit_company_contact': 'Редагування контактів компанії',
        'can_edit_required_fields': 'Редагування обов’язкових полів документів',
    }

    @property
    def _display_value(self) -> str:
        if self.extra['changed_permission'] == 'allowed_ips':
            return str(self.extra['new_value'])
        if self.extra['changed_permission'] == 'status':
            if self.extra['new_value'] == RoleStatus.active.value:
                return 'Відновлення видаленного співробітника'
            return str(self.extra['new_value'])
        if self.extra['changed_permission'] == 'user_role':
            if self.extra['new_value'] == UserRole.admin.value:
                return 'Активація'
            return 'Деактивація'

        if self.extra['new_value']:
            return 'Активація'
        return 'Деактивація'

    @classmethod
    async def make_many(
        cls,
        user: User,
        role_id: str,
        update_role_data: DataDict,
        source: enums.EventSource,
        *,
        date_created: dt.datetime | None = None,
        ip: str | None = None,
        user_agent: str | None = None,
    ) -> list['UpdateRoleEvent']:
        """Return all events associated with the provided role update data."""
        date_created = date_created if date_created is not None else utc_now()

        async with services.db.acquire() as db_conn:
            affected_user = await auth_db.select_user(db_conn, role_id=role_id)
            affected_user_email = affected_user.email if affected_user is not None else ''
            if affected_user is None:
                logger.warning(
                    'Could not find user for role ID',
                    extra={
                        'edrpou': user.company_edrpou if is_wide_user_type(user) else None,
                        'role_id': role_id,
                    },
                )

        produced_events: list[UpdateRoleEvent] = []
        supported_changes = (
            (
                changed_setting,
                new_value if not isinstance(new_value, RoleStatus) else new_value.value,
            )
            for changed_setting, new_value in update_role_data.items()
            if changed_setting in cls._UPDATE_KEYS_TO_DISPLAY_NAMES
        )
        for changed_setting, new_value in supported_changes:
            extra = {
                'changed_permission': changed_setting,
                'new_value': new_value,
                'affected_user_email': affected_user_email,
                'ip': ip,
                'user_agent': user_agent,
            }

            event = cls(
                id=uuid.uuid4().hex,
                actor_email=user.email,
                actor_edrpou=user.company_edrpou if is_wide_user_type(user) else None,
                actor_company_id=user.company_id if is_wide_user_type(user) else None,
                actor_role_id=user.role_id if is_wide_user_type(user) else None,
                request_source=source,
                extra=extra,
                date_created=date_created,
                actor_user_id=user.id,
                actor_auth_phone=user.auth_phone,
            )
            produced_events.append(event)

        return produced_events

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self._UPDATE_KEYS_TO_DISPLAY_NAMES[self.extra['changed_permission']],
            self._display_value,
            self.extra['affected_user_email'],
        )


@deprecated('Migrate to UserAction')
@event_type
class DeleteRoleEvent(Event):
    """Event handles role deletion"""

    event_type = enums.EventType.ROLE_DELETE
    event_type_reportable = 'Видалення користувача'
    feature_class = 'Зміна налаштувань співробітника'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'affected_user_email' in extra, 'affected_user_email is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
        )


@deprecated('Migrate to UserAction')
@event_type
class CreateRoleEvent(Event):
    """Event handles role creation"""

    event_type = enums.EventType.ROLE_CREATE
    event_type_reportable = 'Додавання користувача'
    feature_class = 'Зміна налаштувань співробітника'

    @classmethod
    async def make(
        cls,
        user: User | BaseUser,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'affected_user_email' in extra, 'affected_user_email is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
        )


@deprecated('Migrate to UserAction')
@event_type
class TemplateCreateEvent(Event):
    event_type = enums.EventType.TEMPLATE_CREATE
    event_type_reportable = 'Створення'
    feature_class = 'Сценарії'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'name' in extra, 'name is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class TemplateUpdateEvent(Event):
    event_type = enums.EventType.TEMPLATE_UPDATE
    event_type_reportable = 'Редагування'
    feature_class = 'Сценарії'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'name' in extra, 'name is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class TemplateToggleEvent(Event):
    event_type = enums.EventType.TEMPLATE_TOGGLE
    feature_class = 'Сценарії'
    required_extra_keys = [
        'status',  # str
        'name',  # str
    ]

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            'Активація'
            if self.extra['status'] == DocumentAutomationStatus.enabled.value
            else 'Деактивація',
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class TemplateDeleteEvent(Event):
    event_type = enums.EventType.TEMPLATE_DELETE
    event_type_reportable = 'Видалення'
    feature_class = 'Сценарії'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'name' in extra, 'name is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class FieldCreateEvent(Event):
    event_type = enums.EventType.FIELD_CREATE
    event_type_reportable = 'Створення'
    feature_class = 'Додаткові параметри'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'name' in extra, 'name is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class FieldUpdateEvent(Event):
    event_type = enums.EventType.FIELD_UPDATE
    event_type_reportable = 'Редагування'
    feature_class = 'Додаткові параметри'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'name' in extra, 'name is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class FieldDeleteEvent(Event):
    event_type = enums.EventType.FIELD_DELETE
    event_type_reportable = 'Видалення'
    feature_class = 'Додаткові параметри'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        assert extra and 'name' in extra, 'name is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class FieldAccessCreateEvent(Event):
    event_type = enums.EventType.FIELD_ACCESS_CREATE
    event_type_reportable = 'Надання доступу'
    feature_class = 'Додаткові параметри'
    required_extra_keys = ['name', 'affected_user_email']

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class FieldAccessDeleteEvent(Event):
    event_type = enums.EventType.FIELD_ACCESS_DELETE
    event_type_reportable = 'Видалення доступу'
    feature_class = 'Додаткові параметри'
    required_extra_keys = ['name', 'affected_user_email']

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class RequiredFieldCreateEvent(Event):
    event_type = enums.EventType.REQUIRED_FIELD_CREATE
    event_type_reportable = 'Створення'
    feature_class = "Обов'язкові поля"
    required_extra_keys = [
        'document_category',
        'is_name_required',
        'is_type_required',
        'is_number_required',
        'is_date_required',
        'is_amount_required',
    ]

    def _display_details(self) -> str:
        def toggle_value(value: str) -> str:
            return 'Активація' if value else 'Деактивація'

        category_name = DOCUMENT_CATEGORY_FIELDS_DISPLAY_NAME_MAP[self.extra['document_category']]

        details = (
            f'Категорія={category_name};'
            f'Назва={toggle_value(self.extra["is_name_required"])};'
            f'Тип={toggle_value(self.extra["is_type_required"])};'
            f'Номер={toggle_value(self.extra["is_number_required"])};'
            f'Дата={toggle_value(self.extra["is_date_required"])};'
        )
        # Amount was added later, so old events may not have it
        if 'is_amount_required' in self.extra:
            details += f'Сума={toggle_value(self.extra["is_amount_required"])};'

        return details

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self._display_details(),
        )


@deprecated('Migrate to UserAction')
@event_type
class RequiredFieldUpdateEvent(Event):
    event_type = enums.EventType.REQUIRED_FIELD_UPDATE
    event_type_reportable = 'Редагування'
    feature_class = "Обов'язкові поля"
    required_extra_keys = [
        'document_category',
        # Optional:
        #  'is_name_required',
        #  'is_type_required',
        #  'is_number_required',
        #  'is_date_required',
        #  'is_amount_required',
    ]

    def _display_details(self) -> str:
        def toggle_value(value: str) -> str:
            return 'Активація' if value else 'Деактивація'

        category_name = DOCUMENT_CATEGORY_FIELDS_DISPLAY_NAME_MAP[self.extra['document_category']]

        details = f'Категорія={category_name};'

        if 'is_name_required' in self.extra:
            details += f'Назва={toggle_value(self.extra["is_name_required"])};'
        if 'is_type_required' in self.extra:
            details += f'Тип={toggle_value(self.extra["is_type_required"])};'
        if 'is_number_required' in self.extra:
            details += f'Номер={toggle_value(self.extra["is_number_required"])};'
        if 'is_date_required' in self.extra:
            details += f'Дата={toggle_value(self.extra["is_date_required"])};'
        if 'is_amount_required' in self.extra:
            details += f'Сума={toggle_value(self.extra["is_amount_required"])};'

        return details

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self._display_details(),
        )


@deprecated('Migrate to UserAction')
@event_type
class RequiredFieldDeleteEvent(Event):
    event_type = enums.EventType.REQUIRED_FIELD_DELETE
    event_type_reportable = 'Видалення'
    feature_class = "Обов'язкові поля"
    required_extra_keys = [
        'document_category',
    ]

    def _display_details(self) -> str:
        category_name = DOCUMENT_CATEGORY_FIELDS_DISPLAY_NAME_MAP[self.extra['document_category']]

        return f'Категорія={category_name}'

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        return await super().make(
            user=user,
            source=source,
            extra=extra,
            date_created=date_created,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self._display_details(),
        )


@deprecated('Migrate to UserAction')
@event_type
class CompanyUpdateEvent(Event):
    event_type = enums.EventType.COMPANY_UPDATE
    event_type_reportable = 'Редагування'
    feature_class = 'Налаштування компанії'

    _ROLE_PERMISSIONS_DISPLAY_NAMES = {
        'role_permissions_admin': 'Адміністратор компанії',
        'role_permissions_can_view_documents': 'Перегляд всіх документів компанії',
        'role_permissions_can_view_private_document': 'Перегляд всіх приватних документів',
        'role_permissions_can_comment_document': 'Коментування документів',
        'role_permissions_can_upload_document': 'Завантаження документів у сервіс',
        'role_permissions_can_download_document': 'Вивантаження документів на локальний комп’ютер',
        'role_permissions_can_print_document': 'Друк документів',
        'role_permissions_can_delete_document': 'Видалення документів',
        'role_permissions_can_sign_and_reject_document': 'Підписання та відхилення документів',
        'role_permissions_can_remove_itself_from_approval': 'Видалення себе зі списку погоджувачів',
        'role_permissions_can_edit_company': 'Керування загальними налаштуваннями компанії',
        'role_permissions_can_edit_roles': 'Редагування та видалення співробітників',
        'role_permissions_can_invite_coworkers': 'Запрошення співробітників',
        'role_permissions_can_edit_document_automation': 'Налаштування сценаріїв документів',
        'role_permissions_can_edit_document_fields': (
            'Налаштування додаткових параметрів документів'
        ),
        'role_permissions_can_edit_templates': 'Створення та редагування шаблонів',
        'role_permissions_can_archive_documents': 'Переміщення документів у архів',
        'role_permissions_can_delete_archived_documents': 'Видалення архівних документів',
        'role_permissions_can_create_tags': 'Створення нових ярликів',
        'role_permissions_can_edit_directories': 'Керування папками',
        'role_permissions_can_edit_required_fields': (
            'Налаштування обовʼязкових полів для вхідних документів'
        ),
        'role_permissions_can_download_actions': 'Збереження історії документів/дій користувачів',
        'role_permissions_can_edit_company_contact': 'Керування контактами контрагентів',
        'role_permissions_can_edit_security': 'Зміна налаштувань безпеки',
        'role_permissions_can_change_document_signers_and_reviewers': (
            'Зміна розпочатого процесу підписання/погодження'
        ),
        'role_permissions_can_delete_document_extended': 'Видалення будь-якого документу компанії',
        'role_permissions_can_edit_document_category': 'Налаштування типів внутрішніх документів',
        'role_permissions_can_extract_document_structured_data': (
            'Витягування структурованих даних з документів'
        ),
        'role_permissions_can_edit_document_structured_data': (
            'Редагування структурованих даних документів'
        ),
    }
    _UPDATE_KEYS_TO_DISPLAY_NAMES = {
        'ms_viewer_enabled': 'Microsoft переглядач',
        'enable_2fa_for_internal_users': 'Налаштування 2FA для співробітників',
        'allow_suggesting_document_meta_with_ai:': (
            'Налаштування AI функціоналу при роботі з документами'
        ),
        'activity_field': 'Галузь діяльності',
        'employees_number': 'Телефон компанії',
        'ipn': 'ІПН',
        'phone': 'Телефон компанії',
        'email_domains': 'Домени електронної пошти',
        'allowed_ips': 'Дозволені IP адреси',
        'allowed_api_ips': 'Дозволені IP адреси для API',
        'inactivity_timeout': 'Таймаут неактивності',
        'is_download_pending_enabled': (
            'Дозволити завантажувати документи, які ще не пройшли перевірку антивірусом'
        ),
        'is_download_infected_enabled': 'Дозволити завантажувати потенційно небезпечні документи',
        **{
            key: f'Налаштування прав для нових співробітників: {name}'
            for key, name in _ROLE_PERMISSIONS_DISPLAY_NAMES.items()
        },
    }

    @property
    def _display_value(self) -> str:
        if self.extra['changed_field'] in {
            'activity_field',
            'employees_number',
            'ipn',
            'phone',
            'email_domains',
            'allowed_ips',
            'allowed_api_ips',
            'inactivity_timeout',
        }:
            return str(self.extra['new_value'])

        if self.extra['new_value']:
            return 'Активація'
        return 'Деактивація'

    @classmethod
    async def make_many(
        cls,
        user: User,
        data: AnyDict,
        source: enums.EventSource,
        *,
        date_created: dt.datetime | None = None,
    ) -> list['CompanyUpdateEvent']:
        """
        Return all events for each setting in data.
        possible keys are in _UPDATE_KEYS_TO_DISPLAY_NAMES.
        """

        date_created = date_created if date_created is not None else utc_now()

        produced_events: list[CompanyUpdateEvent] = []
        supported_changes = (
            (changed_setting, new_value)
            for changed_setting, new_value in data.items()
            if changed_setting in cls._UPDATE_KEYS_TO_DISPLAY_NAMES
        )
        for changed_field, new_value in supported_changes:
            extra = {
                'changed_field': changed_field,
                'new_value': new_value,
            }

            event = cls(
                id=uuid.uuid4().hex,
                actor_email=user.email,
                actor_edrpou=user.company_edrpou,
                actor_company_id=user.company_id,
                actor_role_id=user.role_id,
                request_source=source,
                extra=extra,
                date_created=date_created,
                actor_user_id=user.id,
                actor_auth_phone=user.auth_phone,
            )
            produced_events.append(event)

        return produced_events

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self._UPDATE_KEYS_TO_DISPLAY_NAMES[self.extra['changed_field']],
            self._display_value,
            '',
        )


@deprecated('Migrate to UserAction')
@event_type
class RoleTagCreateEvent(Event):
    event_type = enums.EventType.ROLE_TAG_CREATE
    event_type_reportable = 'Додавання ярлика'
    feature_class = 'Зміна налаштувань співробітника'
    required_extra_keys = ['tag_name', 'affected_user_email']

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
            self.extra['tag_name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class RoleTagDeleteEvent(Event):
    event_type = enums.EventType.ROLE_TAG_DELETE
    event_type_reportable = 'Видалення ярлика'
    feature_class = 'Зміна налаштувань співробітника'
    required_extra_keys = ['tag_name', 'affected_user_email']

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
            self.extra['tag_name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class GroupCreatedEvent(Event):
    """A successful group creation."""

    event_type = enums.EventType.GROUP_CREATE
    event_type_reportable = 'Створення групи'
    feature_class = 'Групи'
    required_extra_keys = {'name', 'group_id'}

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class GroupDeletedEvent(Event):
    """A successful group deletion."""

    event_type = enums.EventType.GROUP_DELETE
    event_type_reportable = 'Видалення групи'
    feature_class = 'Групи'
    required_extra_keys = {'name', 'group_id'}

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            '',
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class GroupRenameEvent(Event):
    """A successful group deletion."""

    event_type = enums.EventType.GROUP_RENAME
    event_type_reportable = 'Зміна назви групи'
    feature_class = 'Групи'
    required_extra_keys = {'name_old', 'name_new', 'group_id'}

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    @property
    def _details(self) -> str:
        return f'Попередня назва: {self.extra["name_old"]}. Нова назва: {self.extra["name_new"]}'

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            'Успішний',
            '',
            self._details,
        )


@deprecated('Migrate to UserAction')
@event_type
class GroupMemberCreated(Event):
    """A successful group member creation."""

    event_type = enums.EventType.GROUP_MEMBER_CREATE
    event_type_reportable = 'Додавання користувача до групи'
    feature_class = 'Групи'
    required_extra_keys = {'name', 'group_id', 'affected_user_email'}

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    @classmethod
    async def make_many(
        cls,
        user: User,
        source: enums.EventSource,
        group: Group,
        group_members: list[GroupMember],
        *,
        date_created: dt.datetime | None = None,
    ) -> list['GroupMemberCreated']:
        date_created = date_created if date_created is not None else utc_now()

        async with services.db.acquire() as db_conn:
            affected_users = await auth_db.select_users_by_role_ids(
                db_conn, [group_member.role_id for group_member in group_members]
            )

        events = []
        for affected_user in affected_users:
            events.append(
                cls(
                    id=uuid.uuid4().hex,
                    actor_email=user.email,
                    actor_edrpou=user.company_edrpou,
                    actor_company_id=user.company_id,
                    actor_role_id=user.role_id,
                    request_source=source,
                    extra={
                        'name': group.name,
                        'group_id': group.id,
                        'affected_user_email': affected_user.email,
                    },
                    date_created=date_created,
                    actor_user_id=user.id,
                    actor_auth_phone=user.auth_phone,
                )
            )

        return events

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
            self.extra['name'],
        )


@deprecated('Migrate to UserAction')
@event_type
class GroupMemberDeleted(Event):
    """A successful group member deletion."""

    event_type = enums.EventType.GROUP_MEMBER_DELETE
    event_type_reportable = 'Видалення користувача з групи'
    feature_class = 'Групи'
    required_extra_keys = {'name', 'group_id', 'affected_user_email'}

    @classmethod
    async def make(
        cls,
        user: User,
        source: enums.EventSource,
        extra: JsonSerializableData | None = None,
        *,
        date_created: dt.datetime | None = None,
    ) -> 'Event':
        extra = extra or {}
        for key in cls.required_extra_keys:
            assert extra and key in extra, f'{key} is required'

        date_created = date_created if date_created is not None else utc_now()

        return cls(
            id=uuid.uuid4().hex,
            actor_email=user.email,
            actor_edrpou=user.company_edrpou,
            actor_company_id=user.company_id,
            actor_role_id=user.role_id,
            request_source=source,
            extra=extra,
            date_created=date_created,
            actor_user_id=user.id,
            actor_auth_phone=user.auth_phone,
        )

    @classmethod
    async def make_many(
        cls,
        user: User,
        source: enums.EventSource,
        group: Group,
        group_members: list[GroupMember],
        *,
        date_created: dt.datetime | None = None,
    ) -> list['GroupMemberDeleted']:
        date_created = date_created if date_created is not None else utc_now()

        async with services.db.acquire() as db_conn:
            affected_users = await auth_db.select_users_by_role_ids(
                db_conn, [group_member.role_id for group_member in group_members]
            )

        events = []
        for affected_user in affected_users:
            events.append(
                cls(
                    id=uuid.uuid4().hex,
                    actor_email=user.email,
                    actor_edrpou=user.company_edrpou,
                    actor_company_id=user.company_id,
                    actor_role_id=user.role_id,
                    request_source=source,
                    extra={
                        'name': group.name,
                        'group_id': group.id,
                        'affected_user_email': affected_user.email,
                    },
                    date_created=date_created,
                    actor_user_id=user.id,
                    actor_auth_phone=user.auth_phone,
                )
            )

        return events

    def as_report_row(self) -> reporting.ReportRow:
        return reporting.ReportRow(
            self.date_created,
            self.actor_email,
            self.feature_class,
            self.event_type_reportable,
            '',
            self.extra['affected_user_email'],
            self.extra['name'],
        )


@dataclass
class ActionsReport:
    id: str
    company_id: str
    date_from: datetime.datetime
    date_to: datetime.datetime
    type: ActionsReportType
    created_by: str
    date_sent: datetime.datetime | None
    date_created: datetime.datetime

    @property
    def is_sent(self) -> bool:
        return self.date_sent is not None

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            id=row.id,
            company_id=row.company_id,
            date_from=row.date_from,
            date_to=row.date_to,
            type=row.type,
            created_by=row.created_by,
            date_sent=row.date_sent,
            date_created=row.date_created,
        )


@dataclass
class ActionsReportFile:
    id: str
    company_id: str
    report_id: str
    filename: str
    size: int  # in bytes
    date_created: datetime.datetime

    @classmethod
    def from_row(cls, row: DBRow) -> t.Self:
        return cls(
            id=row.id,
            company_id=row.company_id,
            report_id=row.report_id,
            filename=row.filename,
            size=row.size,
            date_created=row.date_created,
        )
