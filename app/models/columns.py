from enum import Enum as NativeEnum
from typing import Any

import sqlalchemy as sa
from citext import CIText
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql.type_api import TypeEngine
from typing_extensions import deprecated

from app.models.types import SoftEnum as SoftEnumType


class BaseColumn(sa.Column):
    def _constructor(self, name: str, type_: Any, **kwargs: Any) -> sa.Column:
        return sa.Column(name, type_, **kwargs)


class Boolean(BaseColumn):
    """Shortcut for boolean columns.

    This will allow to not set `false` as server default value for each field.
    But in case of necessity it will reuse passed `server_default` value.
    """

    def __init__(
        self,
        name: str,
        default_value: bool = False,
        *,
        nullable: bool = False,
        **kwargs: Any,
    ) -> None:
        kwargs['name'] = name
        kwargs['type_'] = sa.Boolean()
        kwargs.setdefault('nullable', nullable)
        kwargs.setdefault('server_default', str(int(default_value)))
        super().__init__(**kwargs)


class CompanyName(BaseColumn):
    """Column for creating company names."""

    default_name = 'name'

    def __init__(self, name: str | None = None, *, nullable: bool = True, **kwargs: Any) -> None:
        kwargs['name'] = name or self.default_name
        kwargs['type_'] = sa.Text()
        kwargs.setdefault('nullable', nullable)
        super().__init__(**kwargs)


class Counter(BaseColumn):
    """Column for storing counters."""

    default_name = 'counter'

    def __init__(
        self,
        name: str | None = None,
        nullable: bool = False,
        **kwargs: Any,
    ) -> None:
        kwargs['name'] = name or self.default_name
        kwargs['nullable'] = nullable
        kwargs['server_default'] = sa.text('0')
        kwargs.setdefault('type_', sa.Integer())
        super().__init__(**kwargs)


class Date(BaseColumn):
    """Column for storing date values."""

    auto_now: bool = False
    auto_now_add: bool = False
    name: str
    type_: TypeEngine = sa.Date()

    def __init__(
        self,
        name: str | None = None,
        auto_now: bool | None = None,
        auto_now_add: bool | None = None,
        **kwargs: Any,
    ) -> None:
        kwargs['name'] = name or self.name
        kwargs['type_'] = self.type_

        auto_now = auto_now or self.auto_now
        auto_now_add = auto_now_add or self.auto_now_add

        if auto_now_add or auto_now:
            kwargs['nullable'] = False
            kwargs['server_default'] = sa.text('now()')

        if auto_now:
            kwargs['server_onupdate'] = sa.text('now()')

        super().__init__(**kwargs)


class DateTime(Date):
    """Column for storing datetime values."""

    type_: TypeEngine = sa.DateTime(True)


class DateCreated(DateTime):
    """Column for storing date created value.

    This is similar to Django's ``models.DateField(auto_now_add=True)``.
    """

    auto_now_add = True
    name = 'date_created'


class DateDeleted(DateTime):
    """Column for storing date deleted value."""

    name = 'date_deleted'


class DateExpired(DateTime):
    """Column for storing date expired value."""

    name = 'date_expired'


class DateStarted(DateTime):
    """Column for storing date started value."""

    name = 'date_started'


class DateUpdated(DateTime):
    """Column for storing date updated value.

    This is similar to Django's ``models.DateField(auto_now=True)``.

    .. note:: For some reason ``server_onupdate`` may not be called by
       SQLAlchemy, so please manually update ``date_updated`` field on each
       update now.
    """

    auto_now = True
    name = 'date_updated'


class EDRPOU(BaseColumn):
    """Column for storing company EDRPOU.

    It may has different name, but always similar type: VARCHAR(64).
    """

    default_name = 'edrpou'

    def __init__(self, name: str | None = None, **kwargs: Any) -> None:
        kwargs['name'] = name or self.default_name
        kwargs['type_'] = sa.String(64)
        super().__init__(**kwargs)


class Email(BaseColumn):
    """Column for storing emails.

    Allow to store case-insensitive emails due to use of CITEXT type.
    """

    default_name = 'email'

    def __init__(self, name: str | None = None, **kwargs: Any) -> None:
        kwargs['name'] = name or self.default_name
        kwargs['type_'] = CIText()
        kwargs.setdefault('nullable', False)
        super().__init__(**kwargs)


class Enum(BaseColumn):
    """Column for storing enums.

    Simplify process of instantiating DB enums by seamlessy integrating with
    Python enums.

    NOTICE: Do not use Enum for new fields. Use SoftEnum instead, because it easier
    to change then NativeEnum
    """

    def __init__(
        self,
        name: str,
        native_enum: type[NativeEnum],
        default_value: NativeEnum | None = None,
        *,
        nullable: bool = False,
        enum_name: str | None = None,
        **kwargs: Any,
    ) -> None:
        # Prepare keyword arguments for enum
        enum_kwargs = {}
        if enum_name is not None:
            enum_kwargs['name'] = enum_name

        kwargs['type_'] = postgresql.ENUM(native_enum, **enum_kwargs)
        kwargs['name'] = name
        kwargs['nullable'] = nullable
        if default_value:
            kwargs['server_default'] = default_value.value
        super().__init__(**kwargs)


class SoftEnum(BaseColumn):
    """
    Text-based enum column type. In database, it is stored as TEXT, but it converts to and from
    python Enum automatically when reading or writing to the database.

    It's easier to extend and change than native PostgresSQL enum type, but it consumes more
    storage.
    """

    def __init__(
        self,
        name: str,
        enum_: type[NativeEnum],
        default_value: NativeEnum | None = None,
        *,
        nullable: bool = False,
        by_name: bool = False,
        enum_type: type = str,
        **kwargs: Any,
    ) -> None:
        kwargs['name'] = name
        kwargs['type_'] = SoftEnumType(enum=enum_, by_name=by_name, enum_type=enum_type)
        kwargs['nullable'] = nullable
        if default_value:
            kwargs['server_default'] = default_value.value
        super().__init__(**kwargs)


class ForeignKey(sa.Column):
    """Shortcut for foreign keys."""

    def __init__(
        self,
        name: str,
        mixed: Any,
        *,
        nullable: bool = True,
        ondelete: str | None = 'CASCADE',
        **kwargs: Any,
    ) -> None:
        fk = sa.ForeignKey(mixed, ondelete=ondelete) if isinstance(mixed, str) else mixed
        super().__init__(name, None, fk, nullable=nullable, **kwargs)

    def _constructor(self, name: str, type_: Any, *args: Any, **kwargs: Any) -> 'ForeignKey':
        mixed = args[0] if args else None
        if not mixed and self.foreign_keys:
            mixed = self.foreign_keys.pop().copy()
        kwargs['type_'] = type_

        return ForeignKey(name, mixed, **kwargs)


@deprecated('Use UUID column instead')
class ID(BaseColumn):
    """Column for table primary key AKA ID.

    This is old implementation. New implementation uses proper UUID type.
    """

    def __init__(self, **kwargs: Any) -> None:
        kwargs['name'] = 'id'
        kwargs['type_'] = sa.String(64)
        kwargs['primary_key'] = True
        kwargs['server_default'] = sa.text('uuid_generate_v4()')
        super().__init__(**kwargs)


class IPN(BaseColumn):
    """Column for storing company tax number."""

    default_name = 'ipn'

    def __init__(self, name: str | None = None, **kwargs: Any) -> None:
        kwargs['name'] = name or self.default_name
        kwargs['type_'] = sa.String(64)
        super().__init__(**kwargs)


class JSONLegacy(BaseColumn):
    """
    Column for storing data in JSON format.

    WARNING: Use JSONB instead, it's more efficient and has better indexing capabilities.
    """

    def __init__(self, name: str, *, array: bool = False, obj: bool = False, **kwargs: Any) -> None:
        kwargs['name'] = name
        kwargs['type_'] = postgresql.JSON()
        kwargs.setdefault('nullable', False)

        if not kwargs['nullable']:
            if array:
                kwargs['server_default'] = sa.text("'[]'")
            if obj:
                kwargs['server_default'] = sa.text("'{}'")

        super().__init__(**kwargs)


class JSONB(BaseColumn):
    """Column for storing data in JSONB format."""

    def __init__(self, name: str, *, nullable: bool = False, **kwargs: Any) -> None:
        kwargs['name'] = name
        kwargs['type_'] = postgresql.JSONB()
        kwargs['nullable'] = nullable
        super().__init__(**kwargs)


class JSONArrayLegacy(JSONLegacy):
    """
    Column for storing JSON arrays (Python lists).

    WARNING: Use JSONB instead, it's more efficient and has better indexing capabilities.
    """

    def __init__(self, name: str, **kwargs: Any) -> None:
        super().__init__(name, array=True, **kwargs)


class JSONObjectLegacy(JSONLegacy):
    """
    Column for storing JSON objects (Python dicts).

    WARNING: Use JSONB instead, it's more efficient and has better indexing capabilities.
    """

    def __init__(self, name: str, **kwargs: Any) -> None:
        super().__init__(name, obj=True, **kwargs)


class Array(BaseColumn):
    """Column for storing arrays."""

    def __init__(self, name: str, type_: Any, **kwargs: Any) -> None:
        kwargs['name'] = name
        kwargs['type_'] = postgresql.ARRAY(type_)
        super().__init__(**kwargs)


class Phone(BaseColumn):
    """Column for storing phone number.

    Main purpose: unified name `phone` & type `String(32)`.
    """

    def __init__(self, name: str = 'phone', nullable: bool = True, **kwargs: Any) -> None:
        kwargs['name'] = name
        kwargs['type_'] = sa.String(32)
        kwargs['nullable'] = nullable
        super().__init__(**kwargs)


class Price(BaseColumn):
    def __init__(self, name: str, precision: int = 5, **kwargs: Any) -> None:
        kwargs['name'] = name
        kwargs['type_'] = sa.Numeric(precision, 2)
        kwargs.setdefault('nullable', False)
        super().__init__(**kwargs)


class Seqnum(BaseColumn):
    """Column for auto increment sequences.

    The sequence itself should be created manually in alembic migration as:

        op.execute(CreateSequence(Sequence(seq_name)))

    like,

    - `tables.py` -> `columns.Seqnum('invoices_seqnum')`
    - `migrations/*.py` ->
      `op.execute(CreateSequence(Sequence('invoices_seqnum')))`

    Sequence should be created before `op.create_table` or `op.alter_table`.
    """

    def __init__(self, seq_name: str, **kwargs: Any) -> None:
        kwargs['type_'] = sa.BigInteger
        kwargs['server_default'] = sa.text(f"nextval('{seq_name}')")
        kwargs.setdefault('name', 'seqnum')
        kwargs.setdefault('nullable', False)
        kwargs.setdefault('index', True)
        super().__init__(**kwargs)


class Title(BaseColumn):
    """
    Column for storing titles

    Title is a nullable/not nullable string with fixed length
    """

    def __init__(self, nullable: bool = True, max_length: int = 100, **kwargs: Any) -> None:
        kwargs['name'] = 'title'
        kwargs['type_'] = sa.String(max_length)
        kwargs['nullable'] = nullable
        super().__init__(**kwargs)


class Text(BaseColumn):
    """Column for text fields."""

    def __init__(self, name: str, **kwargs: Any) -> None:
        kwargs['name'] = name
        kwargs['type_'] = sa.Text()
        kwargs.setdefault('nullable', False)
        super().__init__(**kwargs)


class SimpleUUID(BaseColumn):
    """
    Column for uuid fields which are not primary keys and not foreign keys
    """

    def __init__(
        self,
        name: str,
        *,
        nullable: bool = False,
        unique: bool = False,
        **kwargs: Any,
    ) -> None:
        kwargs['name'] = name
        kwargs['type_'] = postgresql.UUID()
        kwargs['nullable'] = nullable
        kwargs['unique'] = unique
        kwargs['primary_key'] = False
        super().__init__(**kwargs)


class UUID(BaseColumn):
    """Column for table primary key AKA ID.

    Built on top of UUID type. Also can be used for light FK (storing UUID),
    but without foreign key constraint.
    """

    def __init__(
        self,
        name: str = 'id',
        *,
        nullable: bool = False,
        primary_key: bool = True,
        **kwargs: Any,
    ) -> None:
        kwargs['name'] = name
        kwargs['type_'] = postgresql.UUID()
        kwargs['nullable'] = nullable
        kwargs['primary_key'] = primary_key
        kwargs.setdefault('server_default', sa.text('uuid_generate_v4()'))
        super().__init__(**kwargs)


class IntID(BaseColumn):
    """Column for table primary key AKA ID.

    Built on top of BigInteger type with autoincrement.
    In most cases prefer to use UUID Primary Key!
    Use it carefully if you know that you really need it.
    """

    def __init__(
        self,
        name: str = 'id',
        *,
        nullable: bool = False,
        primary_key: bool = True,
        **kwargs: Any,
    ) -> None:
        kwargs['name'] = name
        kwargs['type_'] = sa.BigInteger()
        kwargs['nullable'] = nullable
        kwargs['primary_key'] = primary_key
        kwargs['autoincrement'] = True
        super().__init__(**kwargs)


class KOATUUCode(BaseColumn):
    """Column type for storing a KOATUU code."""

    _KOATUU_CODE_LENGTH = 10
    default_name = 'koatuu_code'

    def __init__(self, name: str | None = None, nullable: bool = False, **kwargs: Any) -> None:
        """Construct a KOATUU code column object."""
        kwargs['name'] = name if name is not None else self.default_name
        kwargs['nullable'] = nullable
        kwargs['type_'] = sa.String(self._KOATUU_CODE_LENGTH)
        super().__init__(**kwargs)
