from typing import Any

from sqlalchemy.ext.compiler import compiles
from sqlalchemy.sql.expression import FromClause


class SQLAlchemyValues(FromClause):
    """
    Example of usage:

    Taken from: https://github.com/sqlalchemy/sqlalchemy/wiki/PGValues

    TODO: replace this class with the built-in "sa.values" after updating
    SQLAlchemy version to 1.4 or above.

    t2 = SQLAlchemyValues(
        [
            column("mykey", Integer),
            column("mytext", String),
            column("myint", Integer),
        ],
        (1, "textA", 99),
        (2, "textB", 88),
        alias_name="myvalues",
    )
    """

    named_with_column = True

    def __init__(self, columns: Any, *args: Any, **kw: Any) -> None:
        self._column_args = columns
        self.list = args
        self.alias_name = self.name = kw.pop('alias_name', None)

    def _populate_column_collection(self) -> None:
        for c in self._column_args:
            c._make_proxy(self)

    @property
    def _from_objects(self) -> list[FromClause]:
        return [self]


@compiles(SQLAlchemyValues)
def compile_values(
    element: SQLAlchemyValues,
    compiler: Any,
    asfrom: bool = False,
    **kw: Any,
) -> str:
    """
    Compile the values clause into a SQL string:

    Example output:
    1. VALUES (1, 'textA'), (2, 'textB') — without alias
    2. (VALUES (1, 'textA'), (2, 'textB')) AS myvalues (mykey, mytext) — with alias
    """
    columns = element.columns

    _values = 'VALUES {}'.format(
        ', '.join(
            '({})'.format(
                ', '.join(
                    compiler.render_literal_value(elem, column.type)
                    for elem, column in zip(tup, columns)
                )
            )
            for tup in element.list
        )
    )
    if asfrom:
        if element.alias_name:
            _columns = ', '.join(c.name for c in columns)
            _alias = element.alias_name
            _values = f'({_values}) AS {_alias} ({_columns})'
        else:
            _values = f'({_values})'

    return _values
