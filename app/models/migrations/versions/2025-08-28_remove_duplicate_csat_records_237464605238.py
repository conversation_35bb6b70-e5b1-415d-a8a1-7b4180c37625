"""remove_duplicate_csat_records

Revision ID: 237464605238
Revises: a9523a1d0626
Create Date: 2025-08-26 17:14:14.056377

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '237464605238'
down_revision = '0068afffe5cf'
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        delete from csat_surveys
        where (user_id, type) in (
            select user_id, type
            from csat_surveys
            group by user_id, type
            having count(*) >= 3
        );
        """
    )


def downgrade():
    pass
