"""document-structured-data-table

Revision ID: 7e0b5e89da01
Revises: 50ee9c08a92f
Create Date: 2025-08-29 19:43:36.104680

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.documents_ai.enums import ModelName, StructuredDataExtractionStatus
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = '7e0b5e89da01'
down_revision = '50ee9c08a92f'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'document_structured_data',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False
        ),
        sa.Column('document_id', sa.String(length=64), nullable=False),
        sa.Column('company_id', postgresql.UUID(), nullable=False),
        sa.Column(
            'status',
            SoftEnum(StructuredDataExtractionStatus),
            server_default=StructuredDataExtractionStatus.PENDING.value,
            nullable=False,
        ),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('llm_name', SoftEnum(ModelName), nullable=True),
        sa.Column('request_duration', sa.Float(), nullable=True),
        sa.Column('input_tokens', sa.Integer(), nullable=True),
        sa.Column('output_tokens', sa.Integer(), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(
        op.f('ix_document_structured_data_company_id'),
        'document_structured_data',
        ['company_id'],
        unique=False,
    )
    op.create_index(
        op.f('ix_document_structured_data_document_id'),
        'document_structured_data',
        ['document_id'],
        unique=False,
    )
    op.create_index(
        'ix_document_structured_data_document_id_company_id',
        'document_structured_data',
        ['company_id', 'document_id'],
        unique=True,
    )


def downgrade():
    op.drop_index(
        'ix_document_structured_data_document_id_company_id', table_name='document_structured_data'
    )
    op.drop_index(
        op.f('ix_document_structured_data_document_id'), table_name='document_structured_data'
    )
    op.drop_index(
        op.f('ix_document_structured_data_company_id'), table_name='document_structured_data'
    )
    op.drop_table('document_structured_data')
