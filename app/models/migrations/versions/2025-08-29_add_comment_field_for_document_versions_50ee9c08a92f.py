"""add description field for document_versions

Revision ID: 50ee9c08a92f
Revises: 237464605238
Create Date: 2025-08-27 16:28:39.295936

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '50ee9c08a92f'
down_revision = '237464605238'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('document_versions', sa.Column('description', sa.Text(), nullable=True))


def downgrade():
    op.drop_column('document_versions', 'description')