"""Add banner_promo table

Revision ID: 0068afffe5cf
Revises: eb2923bdc59f
Create Date: 2025-08-15 17:58:12.086113

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.types import SoftEnum
from app.models.migrations.utils import DummyEnum

# revision identifiers, used by Alembic.
revision = '0068afffe5cf'
down_revision = 'eb2923bdc59f'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'banner_promo',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False,
        ),
        sa.Column('user_id', sa.String(length=64), nullable=False),
        sa.Column('edrpou', sa.String(length=64), nullable=False),
        sa.Column('campaign', SoftEnum(DummyEnum), nullable=True),
        sa.Column('first_shown', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_shown', sa.DateTime(timezone=True), nullable=True),
        sa.Column('shown_count', sa.Integer(), server_default=sa.text('0'), nullable=False),
        sa.Column('date_expired', sa.DateTime(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'edrpou', name='uix_banner_promo_user_id_edrpou'),
    )
 


def downgrade():
    op.drop_table('banner_promo')
