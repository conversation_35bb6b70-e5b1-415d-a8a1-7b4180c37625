"""add free_rate_update_events_table

Revision ID: eb2923bdc59f
Revises: a9523a1d0626
Create Date: 2025-08-22 10:34:18.311277

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.migrations.utils import DummyEnum
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = 'eb2923bdc59f'
down_revision = 'a9523a1d0626'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'free_rate_update_events',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False
        ),
        sa.Column('role_id', postgresql.UUID(), nullable=True),
        sa.Column('type', SoftEnum(DummyEnum), nullable=False),
        sa.Column('user_type', SoftEnum(DummyEnum), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(
        op.f('ix_free_rate_update_events_role_id'),
        'free_rate_update_events',
        ['role_id'],
        unique=False,
    )
    op.create_index(
        op.f('ix_free_rate_update_events_type'), 'free_rate_update_events', ['type'], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_free_rate_update_events_type'), table_name='free_rate_update_events')
    op.drop_index(op.f('ix_free_rate_update_events_role_id'), table_name='free_rate_update_events')
    op.drop_table('free_rate_update_events')
    # ### end Alembic commands ###
