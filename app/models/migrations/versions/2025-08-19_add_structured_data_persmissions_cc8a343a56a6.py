"""add-structured-data-persmissions

Revision ID: cc8a343a56a6
Revises: e9d8e8706a3b
Create Date: 2025-08-19 11:19:03.222213

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cc8a343a56a6'
down_revision = 'e9d8e8706a3b'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'roles',
        sa.Column(
            'can_extract_document_structured_data', sa.<PERSON>(), server_default='0', nullable=False
        ),
    )
    op.add_column(
        'roles',
        sa.Column(
            'can_edit_document_structured_data', sa.<PERSON>(), server_default='0', nullable=False
        ),
    )


def downgrade():
    op.drop_column('roles', 'can_edit_document_structured_data')
    op.drop_column('roles', 'can_extract_document_structured_data')
