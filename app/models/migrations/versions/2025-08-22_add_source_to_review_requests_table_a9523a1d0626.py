"""add_source_to_review_requests_table

Revision ID: a9523a1d0626
Revises: cc8a343a56a6
Create Date: 2025-08-22 09:56:10.113532

"""

from alembic import op
import sqlalchemy as sa

from app.models.types import SoftEnum
from app.reviews.enums import ReviewRequestSource

# revision identifiers, used by Alembic.
revision = 'a9523a1d0626'
down_revision = 'cc8a343a56a6'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'review_requests',
        sa.Column('source', SoftEnum(ReviewRequestSource), nullable=True),
    )


def downgrade():
    op.drop_column('review_requests', 'source')
