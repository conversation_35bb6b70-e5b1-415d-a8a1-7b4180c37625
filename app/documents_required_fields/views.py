from http import HTTPStatus

from aiohttp import web

from app.auth.decorators import login_required
from app.auth.types import User
from app.auth.validators import validate_user_permission
from app.documents_required_fields import utils
from app.documents_required_fields.utils import get_changing_fields
from app.documents_required_fields.validators import (
    validate_required_field_create,
    validate_required_field_update,
)
from app.events.user_actions import types
from app.events.user_actions.utils import add_user_action, get_event_source
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib import validators
from app.lib.database import DBRow
from app.lib.types import DataDict
from app.services import services


def prepare_document_required_response_data(field: DBRow) -> DataDict:
    return {
        'id': field.id,
        'document_category': field.document_category.value,
        'is_name_required': field.is_name_required,
        'is_type_required': field.is_type_required,
        'is_number_required': field.is_number_required,
        'is_date_required': field.is_date_required,
        'is_amount_required': field.is_amount_required,
    }


@login_required()
async def create_document_required_field(
    request: web.Request,
    user: User,
) -> web.Response:
    raw_data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        options = await validate_required_field_create(conn=conn, user=user, raw_data=raw_data)
        field = await utils.add_document_required_field(conn=conn, field=options)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await add_user_action(
            user_action=types.UserAction(
                action=types.Action.document_field_required_create,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra=prepare_document_required_response_data(field),
            )
        )

    return web.json_response(
        data=prepare_document_required_response_data(field),
        status=HTTPStatus.CREATED,
    )


@login_required()
async def update_document_required_field(
    request: web.Request,
    user: User,
) -> web.Response:
    field_id = request.match_info['field_id']

    raw_data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        options = await validate_required_field_update(
            conn=conn, user=user, raw_data=raw_data, field_id=field_id
        )
        diff = await get_changing_fields(conn=conn, field=options)
        field = await utils.update_document_required_fields(conn=conn, field=options)

    if not field:
        return web.json_response(status=HTTPStatus.NOT_FOUND)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION) and diff:
        await add_user_action(
            user_action=types.UserAction(
                action=types.Action.document_field_required_update,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra={**diff, 'document_category': field.document_category.value},
            )
        )

    return web.json_response(
        data=prepare_document_required_response_data(field),
        status=HTTPStatus.OK,
    )


@login_required()
async def delete_document_required_fields(
    request: web.Request,
    user: User,
) -> web.Response:
    field_id = request.match_info['field_id']

    validate_user_permission(user, {'can_edit_required_fields'})
    async with services.db.acquire() as conn:
        field = await utils.delete_document_required_fields(
            conn=conn, field_id=field_id, company_id=user.company_id
        )

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await add_user_action(
            user_action=types.UserAction(
                action=types.Action.document_field_required_delete,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=user.company_id,
                extra=prepare_document_required_response_data(field),
            )
        )

    return web.json_response(
        status=HTTPStatus.OK,
    )
