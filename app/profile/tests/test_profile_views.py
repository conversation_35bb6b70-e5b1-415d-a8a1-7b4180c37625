import datetime
import secrets
import uuid
from collections import defaultdict
from http import H<PERSON><PERSON><PERSON><PERSON>
from pathlib import Path
from unittest import mock
from unittest.mock import AsyncMock

import aiohttp
import pytest
import sqlalchemy as sa
import ujson
from conciergelib import headers as concierge_headers
from vchasno_crm import CRMU<PERSON><PERSON>n<PERSON>Full
from vchasno_crm.enums import CRMSource
from vchasno_crm.types import CRMCompanyInfo, CRMRoleInfo, CRMRoleInfoWithCompany, CRMUserInfo

from api.errors import Code
from app.auth import concierge
from app.auth.db import (
    insert_company,
    select_base_user,
    select_company_by_edrpou,
    select_phone_usage_info,
    select_role_by,
    select_role_by_id,
    select_roles,
    update_company_config,
    update_company_entity_count,
    update_role,
)
from app.auth.enums import (
    RoleActivationSource,
    RoleStatus,
    StatEntity,
    UserEmailChangeStatus,
)
from app.auth.helpers import check_password_hash
from app.auth.schemas import (
    AntivirusSettings,
    ArchiveSettings,
    CompanyConfig,
    DefaultRolePermissionsKey,
    UploadsConfig,
)
from app.auth.tables import company_config_table, company_table, role_table
from app.auth.tests.utils import exists_role_by, get_base_user
from app.auth.utils import get_company_config
from app.billing.db import update_billing_company_config
from app.billing.enums import CompanyLimit
from app.contacts.tests.utils import get_contact_persons
from app.diia import utils as diia_utils
from app.documents.enums import FirstSignBy
from app.events import user_actions
from app.events.user_actions.db import select_user_actions_for
from app.lib import eusign_utils
from app.lib.datetime_utils import utc_now
from app.lib.enums import (
    DocumentListSortDate,
    DocumentStatus,
    Language,
    SignatureFormat,
    SignatureType,
    UserRole,
)
from app.lib.types import DataDict
from app.models import count, select_one
from app.profile.emailing import (
    USER_EMAIL_CHANGE_CONFIRM_TOKEN_ACTION,
    USER_EMAIL_CHANGE_VERIFY_EMAIL_TOKEN_ACTION,
    create_user_email_change_token,
)
from app.profile.tests.utils import get_user_email_changes, prepare_user_email_change
from app.profile.utils import save_phone_verification_otp, save_recover_password_jwt
from app.services import services
from app.tags.tests.common import get_company_tags, get_role_tags, prepare_role_tag, prepare_tag
from app.tests.common import (
    LOGIN_PAGE_URL,
    LOGIN_URL,
    ROLES_DENY_URL,
    SUPER_ADMIN_EDRPOU,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_USER_PASSWORD,
    TEST_USER_PHONE,
    TEST_USER_TELEGRAM_CHAT_ID,
    UPDATE_2FA_STATE_URL,
    cleanup_on_teardown,
    create_app,
    fetch_graphql,
    login,
    prepare_app_client,
    prepare_auth_headers,
    prepare_base_user_data,
    prepare_client,
    prepare_company_config,
    prepare_company_data,
    prepare_contact,
    prepare_coworker_role,
    prepare_document_data,
    prepare_form_data,
    prepare_referer_headers,
    prepare_role,
    prepare_signature_info,
    prepare_user_data,
    request_update_additional_company_config,
    request_update_company_config,
    send_document,
    set_company_config,
)
from app.tokens.utils import generate_jwt_token
from app.trigger_notifications.db import select_all_trigger_notifications
from app.trigger_notifications.enums import (
    TriggerNotificationStatus,
    TriggerNotificationType,
)
from worker import topics

DATA_PATH = Path(__file__).parents[2] / 'registration' / 'tests' / 'data'

ADD_COMPANY_URL = '/internal-api/companies'
AGREE_ON_ROLES_URL = '/internal-api/roles/agree'
RECOVER_PASSWORD_URL = '/internal-api/profile/password/recover'
UPDATE_PASSWORD_URL = '/internal-api/profile/password'
UPDATE_PHONE_URL = '/internal-api/profile/phone'
UPDATE_LANGUAGE_URL = '/internal-api/profile/language'
UPDATE_PHONE_USAGE_URL = '/internal-api/phone-usage'
UPDATE_USER_PROFILE_URL = '/internal-api/profile'

START_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/start'
VERIFY_PASSWORD_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/verify-password'
VERIFY_EMAIL_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/verify-email'
VERIFY_2FA_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/verify-2fa'
RESEND_2FA_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/resend-2fa'
RESEND_CONFIRMATION_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/resend-confirm'
CONFIRM_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/confirm'
CANCEL_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/cancel'
UPDATE_CONTACTS_ON_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/update-contacts'
CHECK_CONTACTS_ON_USER_EMAIL_CHANGE_URL = '/internal-api/profile/email-change/check-contacts'

TEST_ANOTHER_USER_EMAIL = '<EMAIL>'
TEST_COMPANY_EDRPOU = '12123451'
TEST_COMPANY_EDRPOU_2 = '12123452'
TEST_COMPANY_EDRPOU_3 = '12123453'
TEST_COMPANY_EDRPOU_4 = '12123454'
TEST_COMPANY_EDRPOU_5 = '12123455'
TEST_COMPANY_EDRPOU_6 = '12123456'
TEST_COMPANY_EDRPOU_7 = '12123457'
TEST_COMPANY_EDRPOU_8 = '12123458'
TEST_COMPANY_IPN = '123456789012'
TEST_COMPANY_NAME = 'Company Name'
TEST_COMPANY_PHONE = '+************'
TEST_IP = '127.0.0.1'
TEST_USER_NAME = 'User Name'
TEST_USER_EMAIL = '<EMAIL>'
TEST_UUID = '22b60f5f-5617-47cc-b476-7d1db455fa5e'
TEST_UUID_1 = '33eedfb4-9a1f-4d05-adbb-dfc92b573659'
TEST_COMPANY_NAME_WITH_LONG_PREFIX, TEST_COMPANY_NAME_WITH_SHORT_PREFIX = (
    'Товариство з обменежною відповідальністю Вчасно',
    'ТОВ Вчасно',
)

TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'
TEST_EMAIL_3 = '<EMAIL>'
TEST_EMAIL_4 = '<EMAIL>'
TEST_EMAIL_5 = '<EMAIL>'
TEST_EMAIL_6 = '<EMAIL>'
TEST_EMAIL_7 = '<EMAIL>'
TEST_EMAIL_8 = '<EMAIL>'

TEST_PASSWORD_1 = 'password1!'
TEST_PASSWORD_2 = 'password2!'
TEST_PASSWORD_3 = 'password3!'
TEST_PASSWORD_4 = 'password4!'
TEST_PASSWORD_5 = 'password5!'


async def request_start_user_email_change(client, user, new_email) -> DataDict:
    response = await client.post(
        path=START_USER_EMAIL_CHANGE_URL,
        json={'new_email': new_email},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    return await response.json()


async def request_verify_password_user_email_change(client, user, password) -> DataDict:
    response = await client.post(
        path=VERIFY_PASSWORD_USER_EMAIL_CHANGE_URL,
        json={'password': password},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    return await response.json()


async def _add_new_company_request(client, user, monkeypatch, edrpou):
    headers = prepare_auth_headers(user)
    # get registration token
    response = await client.post('/companies/registration/token', headers=headers)
    assert response.status == HTTPStatus.OK, await response.json()
    data = await response.json()
    token = data['token']

    # monkeypatch verify function
    sign = prepare_signature_info(SignatureType.signature, edrpou)
    monkeypatch.setattr(eusign_utils, 'verify_sync', lambda *_, **__: sign)

    response = await client.post(
        ADD_COMPANY_URL,
        json={
            'signature': 'SGVsbG8gZXZvZG9jIQ==',  # signature validation is mocked
            'original': token,
        },
        headers=headers,
    )
    assert response.status == HTTPStatus.CREATED


async def test_add_company_multiple(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    second_user = await prepare_user_data(app, email='<EMAIL>')

    try:
        response = await client.post(
            ADD_COMPANY_URL,
            data=ujson.dumps(
                {
                    'company_edrpou': TEST_COMPANY_EDRPOU,
                    'company_name': TEST_COMPANY_NAME,
                    'name': TEST_USER_NAME,
                }
            ),
            headers={
                aiohttp.hdrs.CONTENT_TYPE: 'application/json',
                **prepare_auth_headers(user),
            },
        )
        assert response.status == 201

        response = await client.post(
            ADD_COMPANY_URL,
            data=ujson.dumps(
                {
                    'company_edrpou': TEST_COMPANY_EDRPOU,
                    'company_name': TEST_COMPANY_NAME,
                    'name': TEST_USER_NAME,
                }
            ),
            headers={
                aiohttp.hdrs.CONTENT_TYPE: 'application/json',
                **prepare_auth_headers(second_user),
            },
        )
        assert response.status == 201

        response = await client.post(
            ADD_COMPANY_URL,
            data=ujson.dumps(
                {
                    'company_edrpou': TEST_COMPANY_EDRPOU,
                    'company_name': TEST_COMPANY_NAME,
                    'name': TEST_USER_NAME,
                    'is_legal': False,
                }
            ),
            headers={
                aiohttp.hdrs.CONTENT_TYPE: 'application/json',
                **prepare_auth_headers(user),
            },
        )
        assert response.status == 400

        async with app['db'].acquire() as conn:
            company = await select_company_by_edrpou(conn, TEST_COMPANY_EDRPOU)
            assert company.name == TEST_COMPANY_NAME
            assert company.full_name == TEST_COMPANY_NAME

            legal_role = await select_role_by(conn, TEST_COMPANY_EDRPOU, user.id, True)
            assert legal_role is not None
            assert legal_role.can_edit_company is True
            assert legal_role.can_edit_roles is True
            assert legal_role.user_role == UserRole.admin.value

            second_legal_role = await select_role_by(
                conn, TEST_COMPANY_EDRPOU, second_user.id, True
            )
            assert second_legal_role is not None
            assert second_legal_role.can_edit_company is False
            assert second_legal_role.can_edit_roles is False
            # By default, the company has settings in which coworkers who add
            # the company, will be also have the admin role
            assert second_legal_role.user_role == UserRole.admin.value

            natural_role = await select_role_by(conn, TEST_COMPANY_EDRPOU, user.id, False)
            assert natural_role is None

            notifications = await select_all_trigger_notifications(conn)
            assert len(notifications) == 4
            for notification in notifications:
                assert notification.status == TriggerNotificationStatus.new
                assert notification.role_id in (second_legal_role.id_, legal_role.id_)

    finally:
        await cleanup_on_teardown(app)


async def test_add_company_natural_role_exists(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_user_data(
        app, email=TEST_USER_EMAIL, company_edrpou=TEST_COMPANY_EDRPOU, is_legal=False
    )

    try:
        response = await client.post(
            ADD_COMPANY_URL,
            data=ujson.dumps(
                {
                    'company_edrpou': TEST_COMPANY_EDRPOU,
                    'company_name': TEST_COMPANY_NAME,
                    'name': TEST_USER_NAME,
                    'is_legal': False,
                }
            ),
        )
        assert response.status == 400

        async with app['db'].acquire() as conn:
            exists = await exists_role_by(conn, TEST_COMPANY_EDRPOU, user.id, False)
            assert exists is False
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'role_data, coworker_data, expected',
    [
        (
            # First user in company, with admin rights before activation
            {
                'company_id': TEST_UUID_1,
                'user_id': TEST_UUID,
                'status': RoleStatus.pending,
                'user_role': UserRole.admin.value,
                'can_edit_company': False,
                'can_view_document': False,
            },
            {},
            {
                # First user in company should be admin and
                # have rights to edit company and view all documents
                'status': RoleStatus.active,
                'user_role': UserRole.admin.value,
                'can_edit_company': True,
                'can_view_document': True,
            },
        ),
        (
            # First user, without admin rights before activation
            {
                'company_id': TEST_UUID_1,
                'user_id': TEST_UUID,
                'status': RoleStatus.pending,
                'user_role': UserRole.user.value,
                'can_edit_company': True,
                'can_view_document': True,
            },
            {},
            {
                'status': RoleStatus.active,
                'user_role': UserRole.admin.value,
                'can_edit_company': True,
                'can_view_document': True,
            },
        ),
        (
            # First user, without role at all
            {},
            {},
            {
                'status': RoleStatus.active,
                'user_role': UserRole.admin.value,
                'can_edit_company': True,
                'can_view_document': True,
            },
        ),
        (
            # Second user in company, but first active admin in company
            {
                'company_id': TEST_UUID_1,
                'user_id': TEST_UUID,
                'status': RoleStatus.pending,
                'user_role': UserRole.admin.value,
                'can_edit_company': False,
                'can_view_document': False,
            },
            {
                'role_status': RoleStatus.active,
                'user_role': UserRole.user.value,
            },
            {
                # First active admin in company become automaticaly admin
                'status': RoleStatus.active,
                'user_role': UserRole.admin.value,
                'can_edit_company': True,
                'can_view_document': True,
            },
        ),
        (
            # Second user in company, but first active admin in company
            {
                'company_id': TEST_UUID_1,
                'user_id': TEST_UUID,
                'status': RoleStatus.pending,
                'user_role': UserRole.admin.value,
                'can_edit_company': False,
                'can_view_document': False,
            },
            {
                'role_status': RoleStatus.pending,
                'user_role': UserRole.admin.value,
            },
            {
                # Even if company already has admin, but this admin is not active,
                # First activated user becomes admin in company
                'status': RoleStatus.active,
                'user_role': UserRole.admin.value,
                'can_edit_company': True,
                'can_view_document': True,
            },
        ),
        (
            # Second user, pending admin role
            {
                'company_id': TEST_UUID_1,
                'user_id': TEST_UUID,
                'status': RoleStatus.pending,
                'user_role': UserRole.admin.value,
                'can_edit_company': False,
                'can_view_document': False,
            },
            {
                'role_status': RoleStatus.active,
                'user_role': UserRole.admin.value,
            },
            {
                'status': RoleStatus.active,
                'user_role': UserRole.admin.value,  # left unchanged
                'can_edit_company': False,
                'can_view_document': False,
            },
        ),
        (
            # Second user, pending user role
            {
                'company_id': TEST_UUID_1,
                'user_id': TEST_UUID,
                'status': RoleStatus.pending,
                'user_role': UserRole.user.value,
                'can_edit_company': True,
                'can_view_document': True,
            },
            {
                'role_status': RoleStatus.active,
                'user_role': UserRole.admin.value,
            },
            {
                'status': RoleStatus.active,
                'user_role': UserRole.user.value,
                'can_edit_company': True,
                'can_view_document': True,  # left unchanged
            },
        ),
        (
            # Second user in company, no role
            {},
            {
                'role_status': RoleStatus.active,
                'user_role': UserRole.admin.value,
            },
            {
                'status': RoleStatus.active,
                'user_role': UserRole.user.value,
                'can_edit_company': False,
                'can_view_document': False,
            },
        ),
    ],
)
async def test_add_new_company(
    aiohttp_client,
    monkeypatch,
    role_data,
    coworker_data,
    expected,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
        feature_flags={'pass_sign_info_to_backend': False},
        create_billing_account=True,
        user_id=TEST_UUID,
    )
    edrpou = '**********'
    await prepare_company_data(app, id=TEST_UUID_1, edrpou=edrpou)

    await prepare_company_config(
        company_edrpou=edrpou,
        config=CompanyConfig(
            default_role_permissions_key=DefaultRolePermissionsKey(
                can_view_document=False,
                user_role=UserRole.user,
            )
        ),
    )

    async with app['db'].acquire() as conn:
        if role_data:
            await prepare_coworker_role(conn, role_data)
        if coworker_data:
            await prepare_user_data(
                app,
                email='c@t.c',
                company_edrpou=edrpou,
                **coworker_data,
            )

        await _add_new_company_request(client, user, monkeypatch, edrpou)

        role = await select_role_by(conn, edrpou, user.id)
        assert role is not None
        assert role.status == expected['status']
        assert role.user_role == expected['user_role']
        assert role.can_edit_company == expected['can_edit_company']
        assert role.can_view_document == expected['can_view_document']
        assert role.activated_by is None
        assert role.date_activated is not None
        assert role.activation_source == RoleActivationSource.signature


@pytest.mark.parametrize(
    'filename',
    [
        f'{TEST_UUID}.p7s',
        'invalid.p7s',
        'invalid-content.p7s',
        'invalid-uuid.p7s',
    ],
)
async def test_add_company_with_p7s_invalid(aiohttp_client, filename):
    app, client, user = await prepare_client(
        aiohttp_client,
        feature_flags={'pass_sign_info_to_backend': False},
        user_id=TEST_UUID,
    )

    try:
        response = await client.post(
            ADD_COMPANY_URL,
            data=prepare_form_data(p7s=DATA_PATH / filename),
            headers=prepare_auth_headers(user),
        )
        assert response.status == HTTPStatus.BAD_REQUEST

        async with app['db'].acquire() as conn:
            role = await select_role_by(conn, '**********', user.id, True)
            assert role is None
    finally:
        await cleanup_on_teardown(app)


async def test_agree_on_roles(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        True,
        can_edit_roles=True,
    )
    try:
        # Create pending role to agree
        async with app['db'].acquire() as conn:
            company_id = await insert_company(
                conn,
                {'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT, 'is_legal': True},
            )
            role = await prepare_coworker_role(
                conn,
                {'company_id': company_id, 'user_id': user.id},
            )
            assert role.status == RoleStatus.pending
            assert role.date_agreed is None

            # Agree pending role
            response = await client.post(
                AGREE_ON_ROLES_URL,
                data=ujson.dumps({'role_ids': [role.id]}),
                headers=prepare_auth_headers(user),
            )
            assert response.status == 200, await response.json()

            updated_role = await select_role_by_id(conn, role.id)
            assert updated_role.status == RoleStatus.pending
            assert updated_role.date_agreed is not None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('can_receive_notifications, ', [True, False])
async def test_delete_admin_role(
    mailbox,
    aiohttp_client,
    can_receive_notifications,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        True,
        can_receive_notifications=can_receive_notifications,
        can_edit_roles=True,
        can_receive_admin_role_deletion=True,
    )

    user_to_delete = await prepare_user_data(
        app,
        email=TEST_USER_EMAIL,
        company_edrpou=user.company_edrpou,
        user_role=UserRole.admin.value,
    )
    delete_id = user_to_delete.role_id

    await prepare_user_data(
        app,
        email=TEST_ANOTHER_USER_EMAIL,
        company_edrpou=user.company_edrpou,
        user_role=UserRole.admin.value,
        can_receive_notifications=can_receive_notifications,
        can_receive_admin_role_deletion=True,
    )

    assert len(mailbox) == 0
    response = await client.delete(
        f'/internal-api/roles/{delete_id}', headers=prepare_auth_headers(user)
    )
    assert response.status == 204
    if can_receive_notifications:
        assert len(mailbox) == 1
        assert mailbox[0]['To'] == TEST_ANOTHER_USER_EMAIL
    else:
        assert len(mailbox) == 0


@pytest.mark.parametrize(
    'is_admin, permission, status',
    [
        (True, True, 204),
        (True, False, 204),
        (False, True, 204),
        (False, False, 403),
    ],
)
async def test_delete_role(aiohttp_client, is_admin, permission, status):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin,
        can_edit_roles=permission,
    )

    user_to_delete = await prepare_user_data(
        app, email=TEST_USER_EMAIL, company_edrpou=user.company_edrpou
    )
    delete_id = user_to_delete.role_id

    try:
        response = await client.delete(
            f'/internal-api/roles/{delete_id}', headers=prepare_auth_headers(user)
        )
        assert response.status == status

        if status == 204:
            async with app['db'].acquire() as conn:
                deleted_role = await select_one(
                    conn, (role_table.select().where(role_table.c.id == delete_id))
                )
                assert deleted_role.status == RoleStatus.deleted
    finally:
        await cleanup_on_teardown(app)


async def test_delete_tags_on_delete_role(aiohttp_client):
    """
    Test that tags are properly deleted when a role is deleted
    """
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    coworker = await prepare_user_data(
        app=app,
        email=TEST_USER_EMAIL,
        company_edrpou=user.company_edrpou,
    )

    tag1 = await prepare_tag(name='tag1', company_id=user.company_id)
    await prepare_role_tag(tag_id=tag1.id, role_id=coworker.role_id, assigner_id=user.role_id)

    tag2 = await prepare_tag(name='tag2', company_id=user.company_id)
    await prepare_role_tag(tag_id=tag2.id, role_id=coworker.role_id, assigner_id=user.role_id)
    await prepare_role_tag(tag_id=tag2.id, role_id=user.role_id, assigner_id=user.role_id)

    response = await client.delete(
        path=f'/internal-api/roles/{coworker.role_id}',
        headers=prepare_auth_headers(user),
    )
    assert response.status == 204

    tags = await get_company_tags(company_id=user.company_id)
    assert len(tags) == 1
    assert 'tag2' in tags

    coworker_tags = await get_role_tags(role_id=coworker.role_id)
    assert len(coworker_tags) == 0

    user_tags = await get_role_tags(role_id=user.role_id)
    assert len(user_tags) == 1
    assert 'tag2' in user_tags


async def test_delete_role_access_denied(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    user_to_delete = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    delete_id = user_to_delete.role_id

    try:
        response = await client.delete(
            f'/internal-api/roles/{delete_id}', headers=prepare_auth_headers(user)
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('can_edit_roles', [True, False])
@pytest.mark.parametrize('is_admin', [True, False])
async def test_delete_role_itself(aiohttp_client, is_admin, can_edit_roles):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin,
        can_edit_roles=can_edit_roles,
    )
    role_id = user.role_id
    user_id = user.id

    async with app['db'].acquire() as conn:
        assert user.registration_completed is True

        response = await client.delete(
            f'/internal-api/roles/{role_id}',
            headers=prepare_auth_headers(user),
        )

        assert response.status == 204

        updated_user = await select_base_user(conn, user_id=user_id)
        assert updated_user.registration_completed is False

        deleted_role = await select_one(conn, (role_table.select()))
        assert deleted_role.user_id == user_id
        assert deleted_role.deleted_by == role_id
        assert deleted_role.date_deleted is not None


@pytest.mark.parametrize('can_edit_roles', [True, False])
@pytest.mark.parametrize('is_admin', [True, False])
async def test_delete_own_role_by_other_company_role_forbidden(
    aiohttp_client,
    is_admin,
    can_edit_roles,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=is_admin,
        can_edit_roles=can_edit_roles,
    )
    company_id = await prepare_company_data(app, edrpou=TEST_COMPANY_EDRPOU)
    role_to_delete = await prepare_role(
        user_id=user.id,
        company_id=company_id,
        role_status=RoleStatus.active,
    )
    response = await client.delete(
        f'/internal-api/roles/{role_to_delete.id}',
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.FORBIDDEN


async def test_deny_role(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)
    role_id = user.role_id

    try:
        async with app['db'].acquire() as conn:
            # Prepare pending role to deny
            await update_role(
                conn=conn,
                role_id=role_id,
                data={'status': RoleStatus.pending},
            )
            # Login user, allowing deny role
            await login(client, user.email, TEST_USER_PASSWORD)
            response = await client.post(
                ROLES_DENY_URL.format(role_id=user.role_id),
                headers=prepare_referer_headers(client),
            )
            assert response.status == 200

            updated_role = await select_role_by_id(conn, role_id)
            assert updated_role.status == RoleStatus.denied
    finally:
        await cleanup_on_teardown(app)


async def test_deny_role_access_denied(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    try:
        response = await client.post(
            ROLES_DENY_URL.format(role_id=recipient.role_id),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 403
    finally:
        await cleanup_on_teardown(app)


async def test_deny_role_does_not_exist(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    missing_role_id = str(uuid.uuid4())
    try:
        response = await client.post(
            ROLES_DENY_URL.format(role_id=missing_role_id),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 404
    finally:
        await cleanup_on_teardown(app)


async def test_deny_role_forbidden(aiohttp_client):
    client = await aiohttp_client(create_app())
    role_id = str(uuid.uuid4())
    response = await client.post(
        ROLES_DENY_URL.format(role_id=role_id), headers=prepare_referer_headers(client)
    )
    assert response.status == 403, await response.text()


@pytest.mark.parametrize(
    'role_status',
    [
        RoleStatus.active,
        RoleStatus.denied,
        RoleStatus.deleted,
    ],
)
async def test_deny_role_invalid(aiohttp_client, role_status):
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)
    role_id = user.role_id

    try:
        async with app['db'].acquire() as conn:
            # Prepare role to deny
            await update_role(
                conn=conn,
                role_id=role_id,
                data={'status': role_status},
            )
            # Login user, allowing deny role
            await login(client, user.email, TEST_USER_PASSWORD)
            response = await client.post(
                ROLES_DENY_URL.format(role_id=user.role_id),
                headers=prepare_referer_headers(client),
            )
            assert response.status == 400

            # Assert role status did not change
            unchanged_role = await select_role_by_id(conn, role_id)
            assert unchanged_role.status == role_status
    finally:
        await cleanup_on_teardown(app)


async def test_recover_password(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    expired_at = utc_now() + datetime.timedelta(hours=2)
    token = generate_jwt_token(
        {'email': user.email, 'exp': expired_at},
        services.config.tokens.private_key,
    )
    data = {
        'password': TEST_USER_PASSWORD,
        'token': token,
    }
    await save_recover_password_jwt(email=user.email, token=token)

    response = await client.post(
        RECOVER_PASSWORD_URL,
        data=ujson.dumps(data),
        headers=prepare_referer_headers(client),
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        user = await select_base_user(conn, user_id=user.id)
    assert check_password_hash(user.password, data['password']) is True
    assert user.is_autogenerated_password is False

    response = await client.get('/app', allow_redirects=False)
    assert response.status == 302
    assert response.headers['Location'] == LOGIN_PAGE_URL


@pytest.mark.parametrize(
    'is_admin, data, status',
    [
        (True, {'ipn': TEST_COMPANY_IPN, 'phone': TEST_COMPANY_PHONE}, 200),
        (True, {'ipn': TEST_COMPANY_IPN}, 200),
        (
            True,
            {
                'ipn': TEST_COMPANY_IPN,
                'render_signature_in_interface': False,
            },
            200,
        ),
        (False, {'ipn': TEST_COMPANY_IPN}, 403),
    ],
)
async def test_update_company(aiohttp_client, is_admin, data, status):
    app, client, user = await prepare_client(aiohttp_client, is_admin)

    response = await client.patch(
        f'/internal-api/companies/{user.company_edrpou}',
        json=data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == status

    if status == 200:
        async with app['db'].acquire() as conn:
            company = await select_company_by_edrpou(conn, user.company_edrpou)
            assert company.ipn == data['ipn']
            assert company.render_signature_in_interface == data.get(
                'render_signature_in_interface', True
            )
            assert company.render_signature_on_print_document == data.get(
                'render_signature_on_print_document', True
            )


@pytest.mark.parametrize(
    'user_data, request_data',
    [
        # Update password to new password
        (
            {'password': TEST_USER_PASSWORD},
            {
                'current_password': TEST_USER_PASSWORD,
                'new_password': 'pass_w0rd',
            },
        ),
        # Check that we can update autogenerated password without correct
        # current password
        (
            {'password': TEST_USER_PASSWORD, 'is_autogenerated_password': True},
            {
                'current_password': 'wrong-password',
                'new_password': 'pass_w0rd',
            },
        ),
        # Check that we can set password without correct password, if user doesn't
        # have password at all
        (
            {'password': None},
            {
                'current_password': 'wrong-password',
                'new_password': 'pass_w0rd',
            },
        ),
        # Check that for setting first password, current password can be None
        (
            {'password': None},
            {
                'new_password': 'pass_w0rd',
            },
        ),
        # For invalid cases, see "test_update_password_invalid"
    ],
)
async def test_update_password(aiohttp_client, user_data, request_data):
    app, client, user = await prepare_client(aiohttp_client, **user_data)

    response = await client.post(
        path=UPDATE_PASSWORD_URL,
        json=request_data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK

    user = await get_base_user(user_id=user.id)

    # Check that password has was updated in database
    new_password: str | None = request_data['new_password']
    assert check_password_hash(hash_str=user.password, password=new_password)

    # Check that is_autogenerated_password become False after update
    assert user.is_autogenerated_password is False


@pytest.mark.parametrize(
    'user_data, request_data, http_status, error_code',
    [
        (
            # New password should be not empty
            {'password': TEST_USER_PASSWORD},
            {
                'current_password': TEST_USER_PASSWORD,
                'new_password': '',
            },
            HTTPStatus.BAD_REQUEST,
            'invalid_request',
        ),
        (
            # New password should be not None
            {'password': TEST_USER_PASSWORD},
            {
                'current_password': TEST_USER_PASSWORD,
                'new_password': None,
            },
            HTTPStatus.BAD_REQUEST,
            'invalid_request',
        ),
        (
            # New password should be not equal to current password
            {'password': TEST_USER_PASSWORD},
            {
                'current_password': TEST_USER_PASSWORD,
                'new_password': TEST_USER_PASSWORD,
            },
            HTTPStatus.BAD_REQUEST,
            'invalid_request',
        ),
        (
            # New password should be not equal to current password
            {'password': TEST_USER_PASSWORD},
            {
                'current_password': 'trying-to-gess-password',
                'new_password': TEST_USER_PASSWORD,
            },
            HTTPStatus.BAD_REQUEST,
            'invalid_current_password',
        ),
    ],
)
async def test_update_password_invalid(
    aiohttp_client,
    user_data,
    request_data,
    http_status,
    error_code,
):
    """Check not valid cases of updating user password"""
    app, client, user = await prepare_client(aiohttp_client, **user_data)

    response = await client.post(
        path=UPDATE_PASSWORD_URL,
        json=request_data,
        headers=prepare_auth_headers(user),
    )
    response_json = await response.json()
    assert response.status == http_status, response_json
    assert response_json['code'] == error_code, response_json


@pytest.mark.parametrize(
    'is_admin, permission, key, value, status',
    [
        (True, False, 'allowed_ips', [TEST_IP], 200),
        (True, True, 'allowed_ips', [TEST_IP], 200),
        (False, False, 'allowed_ips', [TEST_IP], 403),
        (False, True, 'allowed_ips', [TEST_IP], 200),
        (True, False, 'allowed_api_ips', [TEST_IP], 200),
        (True, True, 'allowed_api_ips', [TEST_IP], 200),
        (False, False, 'allowed_api_ips', [TEST_IP], 403),
        (True, False, 'can_comment_document', True, 200),
        (True, True, 'can_comment_document', True, 200),
        (False, False, 'can_comment_document', True, 403),
        (False, True, 'can_comment_document', True, 200),
        (False, False, 'can_view_coworkers', True, 403),
        (False, True, 'can_view_coworkers', True, 200),
        (True, False, 'can_view_coworkers', True, 200),
        (True, False, 'show_invite_tooltip', False, 200),
        (True, True, 'show_invite_tooltip', False, 200),
        (False, False, 'show_invite_tooltip', False, 200),
        (False, True, 'show_invite_tooltip', False, 200),
        (False, False, 'position', '', 200),
        (True, False, 'position', '', 200),
        (False, False, 'position', 'ІТ', 200),
        (True, False, 'position', 'ІТ', 200),
        (
            True,
            False,
            'sort_documents',
            DocumentListSortDate.date_listing.value,
            200,
        ),
        (
            True,
            True,
            'sort_documents',
            DocumentListSortDate.date_listing.value,
            200,
        ),
        (
            False,
            False,
            'sort_documents',
            DocumentListSortDate.date_document.value,
            200,
        ),
        (
            False,
            True,
            'sort_documents',
            DocumentListSortDate.date_document.value,
            200,
        ),
    ],
)
async def test_update_role(aiohttp_client, is_admin, permission, status, key, value):
    app, client, user = await prepare_client(aiohttp_client, is_admin, can_edit_roles=permission)

    try:
        response = await client.patch(
            f'/internal-api/roles/{user.role_id}',
            data=ujson.dumps({key: value}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == status, await response.json()

        if status == 200:
            async with app['db'].acquire() as conn:
                role = await select_role_by_id(conn, user.role_id)
                role_value = getattr(role, key)
                assert value == (role_value.value if hasattr(role_value, 'value') else role_value)
    finally:
        await cleanup_on_teardown(app)


async def test_update_role_from_other_company(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    innocent_user = await prepare_user_data(
        app=app,
        company_edrpou=TEST_COMPANY_EDRPOU,
        email='<EMAIL>',
    )

    response = await client.patch(
        f'/internal-api/roles/{innocent_user.role_id}',
        data=ujson.dumps({'status': 'active'}),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 403


@pytest.mark.parametrize(
    'invalid_ips',
    [
        ['*'],
        ['invalid'],
        ['127.0.0.1.2'],
        [TEST_IP, '*'],
    ],
)
async def test_update_role_allowed_ips_invalid(aiohttp_client, invalid_ips):
    app, client, user = await prepare_client(aiohttp_client, True)

    try:
        response = await client.patch(
            f'/internal-api/roles/{user.role_id}',
            data=ujson.dumps({'allowed_ips': invalid_ips}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'invalid_value',
    [
        'date',
        'DATE_LISTING',
    ],
)
async def test_update_role_sort_documents_invalid(aiohttp_client, invalid_value):
    app, client, user = await prepare_client(aiohttp_client, True)

    try:
        response = await client.patch(
            f'/internal-api/roles/{user.role_id}',
            data=ujson.dumps({'sort_documents': invalid_value}),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
    finally:
        await cleanup_on_teardown(app)


async def test_phone_verification_sms(evo_sender_mock, monkeypatch, aiohttp_client):
    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await prepare_client(aiohttp_client, phone='+380500000000')

    new_phone = '+380501111111'
    data = {
        'password': TEST_USER_PASSWORD,
        'phone': new_phone,
        'verify': True,
    }

    try:
        response = await client.post(
            UPDATE_PHONE_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
        )
        assert response.status == 200
        # Check SMS sent.
        assert evo_sender_mock.phone == new_phone
        assert otp_code in evo_sender_mock.message

        # Check user was not updated.
        async with app['db'].acquire() as conn:
            user = await select_base_user(conn, user_id=user.id)
        assert user.phone == '+380500000000'
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'new_phone, expected_phone, is_phone_verified',
    [
        ('+380501111111', '+380501111111', True),
    ],
)
async def test_update_phone(
    aiohttp_client,
    monkeypatch,
    new_phone,
    expected_phone,
    is_phone_verified,
):
    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await prepare_client(
        aiohttp_client,
        phone='+380500000000',
    )
    data = {
        'password': TEST_USER_PASSWORD,
        'phone': new_phone,
        'code': otp_code,
    }

    assert user.phone == '+380500000000'
    assert not user.is_phone_verified
    await save_phone_verification_otp(user_id=user.id, otp=otp_code, phone=new_phone)
    response = await client.post(
        UPDATE_PHONE_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        user = await select_base_user(conn, user_id=user.id)
    assert user.phone == expected_phone
    assert user.is_phone_verified == is_phone_verified


async def test_update_phone_empty(aiohttp_client):
    """
    Test that do not allow to update phone to empty value
    """
    app, client, user = await prepare_client(aiohttp_client, phone='+380500000000')

    data = {
        'password': TEST_USER_PASSWORD,
        'phone': '',
        'verify': True,
    }

    response = await client.post(
        UPDATE_PHONE_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    response_json = await response.json()
    assert response_json == {
        'code': 'invalid_request',
        'reason': 'Виникла помилка, перевірте введені дані',
        'details': {'phone': 'blank value is not allowed'},
    }

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, user_id=user.id)
    assert user.phone == '+380500000000'


async def test_update_phone_daily_limit(aiohttp_client, monkeypatch):
    """
    Test that phone verification OTP sending is rate limited
    and user gets banned after daily limit
    """
    from app.lib.sender import utils as sender_utils

    monkeypatch.setattr(sender_utils, 'SMS_DAILY_BAN_LIMIT', 2)

    app, client, user = await prepare_client(aiohttp_client, phone='+380500000000')
    headers = prepare_auth_headers(user)

    new_phone = '+380501111111'
    data = {
        'password': TEST_USER_PASSWORD,
        'phone': new_phone,
        'verify': True,
    }

    response = await client.post(UPDATE_PHONE_URL, json=data, headers=headers)
    assert response.status == HTTPStatus.OK

    response = await client.post(UPDATE_PHONE_URL, json=data, headers=headers)
    assert response.status == HTTPStatus.OK

    # Third request should trigger daily ban limit and ban the user
    response = await client.post(UPDATE_PHONE_URL, json=data, headers=headers)
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS

    user = await get_base_user(user_id=user.id)
    assert user.is_banned

    # Try to make request as banned user
    response = await client.post(path=UPDATE_LANGUAGE_URL, json={'language': 'uk'}, headers=headers)
    assert response.status == HTTPStatus.FORBIDDEN
    response_json = await response.json()
    assert response_json == {
        'code': 'access_denied',
        'details': None,
        'reason': 'Доступ заборонено',
    }


@pytest.mark.parametrize(
    'new_phone, expected_phone, is_phone_verified, telegram_id',
    [
        ('+380501111111', '+380501111111', True, TEST_USER_TELEGRAM_CHAT_ID),
    ],
)
async def test_update_phone_telegram_chat_id(
    aiohttp_client,
    monkeypatch,
    new_phone,
    expected_phone,
    is_phone_verified,
    telegram_id,
):
    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await prepare_client(
        aiohttp_client, phone='+380500000000', is_phone_verified=False
    )
    await prepare_user_data(
        app=app,
        email='<EMAIL>',
        phone='+380501111111',
        is_phone_verified=True,
        telegram_chat_id=TEST_USER_TELEGRAM_CHAT_ID,
    )

    data = {
        'password': TEST_USER_PASSWORD,
        'phone': new_phone,
        'code': otp_code,
    }
    await save_phone_verification_otp(user_id=user.id, otp=otp_code, phone=new_phone)
    response = await client.post(
        UPDATE_PHONE_URL,
        json=data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK

    async with app['db'].acquire() as conn:
        user = await select_base_user(conn, user_id=user.id)
        assert user.phone == expected_phone
        assert user.is_phone_verified == is_phone_verified
        assert user.telegram_chat_id == telegram_id


@pytest.mark.parametrize(
    'data, error_code',
    [
        (
            {
                'password': 'invalid-password',
                'phone': '+380501111111',
                'code': '000111',
            },
            'invalid_current_password',
        ),
        (
            {
                'password': TEST_USER_PASSWORD,
                'phone': '+3805011111111',
                'code': '444444',
            },
            'invalid_request',
        ),
        (
            {
                'password': TEST_USER_PASSWORD,
                'phone': '+380501111111',
                'code': '234567',
            },
            'invalid_totp_code',
        ),
    ],
)
async def test_update_phone_invalid(aiohttp_client, monkeypatch, data, error_code):
    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await prepare_client(aiohttp_client, phone='+380500000000')

    try:
        response = await client.post(
            UPDATE_PHONE_URL, data=ujson.dumps(data), headers=prepare_auth_headers(user)
        )

        # Check correct response.
        assert response.status == 400
        assert (await response.json())['code'] == error_code

        # Check user was not updated.
        async with app['db'].acquire() as conn:
            user = await select_base_user(conn, user_id=user.id)
        assert user.phone == '+380500000000'
    finally:
        await cleanup_on_teardown(app)


async def test_update_phone_with_unused_auth_phone(aiohttp_client, monkeypatch):
    """
    Check that we can update phone to one that no-one is using as auth phone
    """
    phone_1 = '+380511111111'
    phone_2 = '+380522222222'

    otp_code = '123456'

    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await prepare_client(
        aiohttp_client,
        phone=phone_1,
        is_phone_verified=True,
        auth_phone=phone_1,
        password=TEST_USER_PASSWORD,
    )
    await save_phone_verification_otp(user_id=user.id, otp=otp_code, phone=phone_2)

    response = await client.post(
        path=UPDATE_PHONE_URL,
        json={
            'password': TEST_USER_PASSWORD,
            'phone': phone_2,
            'code': otp_code,
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, user_id=user.id)

    assert user.phone == phone_2
    assert user.is_phone_verified is True
    assert user.auth_phone == phone_2


async def test_update_phone_with_used_but_not_auth_phone(aiohttp_client, monkeypatch):
    """
    Check that we can update phone to one that is used by another user for 2FA or as contact phone,
    but not used for auth.
    """
    phone_1 = '+380511111111'
    phone_2 = '+380522222222'

    otp_code = '123456'

    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user1 = await prepare_client(
        aiohttp_client,
        phone=phone_1,
        is_phone_verified=True,
        auth_phone=None,  # not used for auth, but used for 2FA
        password=TEST_USER_PASSWORD,
    )

    user2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        phone=phone_2,
        is_phone_verified=True,
        auth_phone=phone_2,
        password=TEST_USER_PASSWORD,
    )

    await save_phone_verification_otp(user_id=user2.id, otp=otp_code, phone=phone_1)

    response = await client.post(
        path=UPDATE_PHONE_URL,
        json={
            'password': TEST_USER_PASSWORD,
            'phone': phone_1,
            'code': otp_code,
        },
        headers=prepare_auth_headers(user2),
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, user_id=user2.id)

    assert user.phone == phone_1
    assert user.is_phone_verified is True
    assert user.auth_phone == phone_1


async def test_update_phone_with_duplicated_auth_phone(aiohttp_client, monkeypatch):
    """
    Check that we do not allow to update phone to one that is already used by another user
    as auth phone.
    """
    phone_1 = '+380511111111'
    phone_2 = '+380522222222'

    otp_code = '123456'

    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user1 = await prepare_client(
        aiohttp_client,
        phone=phone_1,
        is_phone_verified=True,
        auth_phone=phone_1,
        password=TEST_USER_PASSWORD,
    )

    user2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        phone=phone_2,
        is_phone_verified=True,
        auth_phone=phone_2,
        password=TEST_USER_PASSWORD,
    )

    await save_phone_verification_otp(user_id=user2.id, otp=otp_code, phone=phone_1)

    response = await client.post(
        path=UPDATE_PHONE_URL,
        json={
            'password': TEST_USER_PASSWORD,
            'phone': phone_1,
            'code': otp_code,
        },
        headers=prepare_auth_headers(user2),
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    response_json = await response.json()
    assert response_json['reason'] == (
        'Телефон вже використовується іншим користувачем для входу в систему, тому ми не '
        'можемо його використовувати для входу у ваш профіль'
    )


@pytest.mark.parametrize(
    'enable, is_phone_verified, success',
    [
        (False, False, True),
        (True, False, False),
        (True, True, True),
    ],
)
async def test_update_2fa_state(aiohttp_client, enable, is_phone_verified, success):
    app, client, user = await prepare_client(
        aiohttp_client, phone='+380500000000', is_phone_verified=is_phone_verified
    )
    try:
        response = await client.post(
            UPDATE_2FA_STATE_URL,
            data=ujson.dumps({'enable': enable}),
            headers=prepare_auth_headers(user),
        )
        if success:
            assert response.status == 200
            async with app['db'].acquire() as conn:
                user = await select_base_user(conn, user_id=user.id)
            assert user.is_2fa_enabled == enable
        else:
            assert response.status == 400
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'user_edrpou, is_user_admin, role_status, expected',
    [
        pytest.param(
            TEST_COMPANY_EDRPOU,
            True,
            RoleStatus.deleted,
            {
                'http_status': HTTPStatus.OK,
                'activation_source': RoleActivationSource.restore,
                'has_date_activated': True,
                'has_activated_by': True,
            },
            id='restore_by_admin',
        ),
        pytest.param(
            TEST_COMPANY_EDRPOU,
            False,
            RoleStatus.deleted,
            {
                'http_status': HTTPStatus.FORBIDDEN,
                'reason': 'Доступ заборонено',
            },
            id='restore_by_user',
        ),
        pytest.param(
            TEST_DOCUMENT_EDRPOU_RECIPIENT,
            False,
            RoleStatus.deleted,
            {
                'http_status': HTTPStatus.FORBIDDEN,
                'reason': 'Доступ заборонено',
            },
            id='restore_by_recipient',
        ),
        pytest.param(
            TEST_DOCUMENT_EDRPOU_RECIPIENT,
            True,
            RoleStatus.deleted,
            {
                'http_status': HTTPStatus.FORBIDDEN,
                'reason': 'Доступ заборонено',
            },
            id='restore_by_admin_recipient',
        ),
        pytest.param(
            TEST_COMPANY_EDRPOU,
            True,
            RoleStatus.denied,
            {
                'http_status': HTTPStatus.BAD_REQUEST,
                'reason': 'Статус може бути змінений тільки з видаленого на активний',
            },
            id='restore_denied_by_admin',
        ),
        pytest.param(
            TEST_COMPANY_EDRPOU,
            True,
            RoleStatus.active,
            {
                'http_status': HTTPStatus.BAD_REQUEST,
                'reason': 'Статус може бути змінений тільки з видаленого на активний',
            },
            id='restore_active_by_admin',
        ),
        pytest.param(
            TEST_COMPANY_EDRPOU,
            True,
            RoleStatus.pending,
            {
                'http_status': HTTPStatus.BAD_REQUEST,
                'reason': 'Статус може бути змінений тільки з видаленого на активний',
            },
            id='restore_pending_by_admin',
        ),
        pytest.param(
            TEST_COMPANY_EDRPOU,
            True,
            RoleStatus.rate_deleted,
            {
                'http_status': HTTPStatus.OK,
                # Restoring from rate_deleted should not affect activation_source
                'activation_source': None,
                'has_date_activated': False,
                'has_activated_by': False,
            },
            id='restore_rate_deleted_by_admin',
        ),
    ],
)
async def test_restore_role(
    aiohttp_client,
    user_edrpou: str,
    is_user_admin: bool,
    role_status: RoleStatus,
    expected: DataDict,
):
    app, client, initiator = await prepare_client(
        aiohttp_client,
        company_edrpou=user_edrpou,
        is_admin=is_user_admin,
    )

    role_id = str(uuid.uuid4())

    await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
        role_id=role_id,
        role_status=role_status,
        registration_completed=False,
    )

    response = await client.patch(
        f'/internal-api/roles/{role_id}',
        json={'status': RoleStatus.active.value},
        headers=prepare_auth_headers(initiator),
    )
    assert response.status == expected['http_status'], await response.json()
    if expected['http_status'] == HTTPStatus.OK:
        async with app['db'].acquire() as conn:
            role = await select_role_by_id(conn, role_id=role_id)
            user = await select_base_user(conn, user_id=role.user_id)

        assert user.registration_completed
        assert role is not None
        assert role.is_active
        assert role.is_deleted is False
        assert role.activation_source == expected['activation_source']

        if expected['has_date_activated']:
            assert role.date_activated is not None
        else:
            assert role.date_activated is None

        if expected['has_activated_by']:
            assert role.activated_by == initiator.role_id
        else:
            assert role.activated_by is None
    else:
        assert (await response.json())['reason'] == expected['reason']


@pytest.mark.parametrize(
    'first_role, expected',
    [
        pytest.param(
            True,
            {
                'trigger_notifications': 1,
                'esputnik': [
                    ('https://esputnik.com/api/v1/contacts/', None),
                    ('https://esputnik.com/api/v1/contacts/', None),
                    (
                        'https://esputnik.com/api/v1/event/',
                        'EDO_First_employee_registration',
                    ),
                    (
                        'https://esputnik.com/api/v1/event/',
                        'EDO_Welcome_TOV',
                    ),
                ],
            },
            id='first_role',
        ),
    ],
)
async def test_async_jobs_on_new_company(
    monkeypatch,
    aiohttp_client,
    first_role,
    expected: DataDict,
    esputnik_box,
    mailbox,
):
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: '11111112')
    edrpou = '11111112'

    app, client, user = await prepare_client(
        aiohttp_client,
        feature_flags={'pass_sign_info_to_backend': False},
        user_id=TEST_UUID,
    )
    if not first_role:
        await prepare_user_data(app, company_edrpou=edrpou, email='<EMAIL>')

    async with app['db'].acquire() as conn:
        # First company, just have target company in contact list
        user1 = await prepare_user_data(
            app,
            company_edrpou='********',
            email='<EMAIL>',
        )
        await prepare_contact(conn, user1, {'edrpou': edrpou})

        # Second company have target company contact list and also sends documents
        # to this company
        user2 = await prepare_user_data(
            app,
            email='<EMAIL>',
            company_edrpou='********',
            create_billing_account=True,
        )
        await prepare_contact(conn, user2, {'edrpou': edrpou})
        document = await prepare_document_data(
            app,
            user2,
            status_id=DocumentStatus.uploaded.value,
            first_sign_by=FirstSignBy.recipient,
        )
        await send_document(
            client=client,
            document_id=document.id,
            recipient_edrpou=edrpou,
            recipient_email='<EMAIL>',
            headers=prepare_auth_headers(user2),
        )
        esputnik_box.clear()
        mailbox.clear()

        await _add_new_company_request(client, user, monkeypatch, edrpou)

        # Check trigger notification for company
        notifications = await select_all_trigger_notifications(conn)
        notifications = [
            notification
            for notification in notifications
            if notification.type == TriggerNotificationType.company_registration
            and notification.role_id == user1.role_id
        ]

        assert len(notifications) == expected['trigger_notifications']
        assert sorted([e['__key'] for e in esputnik_box]) == sorted(expected['esputnik'])


@pytest.mark.parametrize(
    'permission, expected_can_view_document, expected_user_role',
    [
        (
            DefaultRolePermissionsKey(user_role=UserRole.admin, can_view_document=False),
            False,
            UserRole.admin.value,
        ),
        (
            DefaultRolePermissionsKey(user_role=UserRole.admin, can_view_document=True),
            True,
            UserRole.admin.value,
        ),
        (
            DefaultRolePermissionsKey(user_role=UserRole.user, can_view_document=True),
            True,
            UserRole.user.value,
        ),
        (
            DefaultRolePermissionsKey(user_role=UserRole.user, can_view_document=False),
            False,
            UserRole.user.value,
        ),
        (
            DefaultRolePermissionsKey(),
            True,
            UserRole.admin.value,
        ),
    ],
)
async def test_add_company_role_permissions(
    aiohttp_client,
    permission: DefaultRolePermissionsKey,
    expected_can_view_document,
    expected_user_role,
):
    app, client, user1 = await prepare_client(
        aiohttp_client,
        feature_flags={'pass_sign_info_to_backend': True},
    )
    user2 = await prepare_user_data(app, email='<EMAIL>')

    data = {
        'company_edrpou': TEST_COMPANY_EDRPOU,
        'company_name': TEST_COMPANY_NAME,
        'name': TEST_COMPANY_NAME,
    }
    user1_headers = prepare_auth_headers(user1)
    user2_headers = prepare_auth_headers(user2)
    async with app['db'].acquire() as conn:
        response = await client.post(ADD_COMPANY_URL, json=data, headers=user1_headers)
        assert response.status == 201, await response.json()

        await prepare_company_config(
            company_edrpou=TEST_COMPANY_EDRPOU,
            config=CompanyConfig(default_role_permissions_key=permission),
        )

        response = await client.post(ADD_COMPANY_URL, json=data, headers=user2_headers)
        assert response.status == 201

        company = await select_company_by_edrpou(conn, TEST_COMPANY_EDRPOU)
        assert company.name == TEST_COMPANY_NAME
        assert company.full_name == TEST_COMPANY_NAME

        role1 = await select_role_by(conn, TEST_COMPANY_EDRPOU, user1.id)
        assert role1 is not None
        assert role1.can_edit_company is True
        assert role1.can_edit_roles is True
        assert role1.can_view_document is True
        assert role1.user_role == UserRole.admin.value

        role2 = await select_role_by(conn, TEST_COMPANY_EDRPOU, user2.id)
        assert role2 is not None
        assert role2.can_edit_company is False
        assert role2.can_edit_roles is False
        assert role2.can_view_document == expected_can_view_document
        assert role2.user_role == expected_user_role


@pytest.mark.parametrize(
    'edrpou, prev_config, prev_admin_config, update_data, expected_status, expected_config',
    [
        # user can not update config of own company
        pytest.param(
            TEST_DOCUMENT_EDRPOU_RECIPIENT,
            {},
            {},
            {'config': {'hide_sender_email_in_notifications': True}},
            HTTPStatus.FORBIDDEN,
            CompanyConfig(),
            id='can_not_update_own_company_config',
        ),
        # user not from company can not update company also
        pytest.param(
            '55555555',
            {},
            {},
            {'config': {'hide_sender_email_in_notifications': True}},
            HTTPStatus.FORBIDDEN,
            CompanyConfig(),
            id='can_not_update_other_company_config',
        ),
        # super admin can send empty request to server, that doesn't
        # affect on config results
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {},
            HTTPStatus.OK,
            CompanyConfig(),
            id='super_admin_can_send_empty_request',
        ),
        # even super admin doesn't allow to change some super secret configs
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {'admin_config': {'s3_encryption': 'SECURITY'}},
            HTTPStatus.BAD_REQUEST,
            CompanyConfig(),
            id='super_admin_can_not_change_sensitive_config',
        ),
        # other allowed settings can be easily changed by super admins
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {'admin_config': {'allow_parent_company_pay_for_documents': True}},
            HTTPStatus.OK,
            CompanyConfig(allow_parent_company_pay_for_documents=True),
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {'allow_parent_company_pay_for_documents': True},
            {'admin_config': {'allow_parent_company_pay_for_documents': True}},
            HTTPStatus.OK,
            CompanyConfig(allow_parent_company_pay_for_documents=True),
            id='super_admin_can_change_allowed_settings_1',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {'allow_parent_company_pay_for_documents': False},
            {'admin_config': {'allow_parent_company_pay_for_documents': True}},
            HTTPStatus.OK,
            CompanyConfig(allow_parent_company_pay_for_documents=True),
            id='super_admin_can_change_allowed_settings_2',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {
                'config': {
                    'allow_send_document_status': True,
                    'allow_send_document_finish_status': True,
                    'api': {
                        'retry_delay_min': 60,
                        'send_document_status_url': 'https://edo.vchasno.ua/',
                        'request_timeout': 50,
                        'retries': 3,
                    },
                },
            },
            HTTPStatus.OK,
            CompanyConfig(
                allow_send_document_status=True,
                allow_send_document_finish_status=True,
                api={
                    'retry_delay_min': 60,
                    'send_document_status_url': 'https://edo.vchasno.ua/',
                    'request_timeout': 50,
                    'retries': 3,
                },
            ),
            id='set_api_config',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {
                'config': {
                    'archive_settings': {
                        'allow_uploaded_documents': True,
                        'allow_partially_signed_documents': False,
                        'allow_fully_signed_documents': True,
                        'allow_rejected_documents': False,
                    },
                },
            },
            HTTPStatus.OK,
            CompanyConfig(
                archive_settings=ArchiveSettings(
                    allow_uploaded_documents=True,
                    allow_partially_signed_documents=False,
                    allow_fully_signed_documents=True,
                    allow_rejected_documents=False,
                )
            ),
            id='set_archive_settings',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {
                'config': {
                    'antivirus_settings': {
                        'is_download_infected_enabled': True,
                        'is_download_pending_enabled': False,
                    },
                },
            },
            HTTPStatus.OK,
            CompanyConfig(
                antivirus_settings=AntivirusSettings(
                    is_download_infected_enabled=True,
                    is_download_pending_enabled=False,
                )
            ),
            id='set_antivirus_settings',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {'uploads': {'max_file_size': 100, 'max_total_size': 200}},
            {
                'admin_config': {
                    'uploads': {
                        'max_total_size': 400,
                        'replace_owner_edrpou': True,
                    }
                }
            },
            HTTPStatus.OK,
            CompanyConfig(
                uploads=UploadsConfig(
                    max_file_size=100,
                    max_total_size=400,
                    replace_owner_edrpou=True,
                )
            ),
            id='deep_merge_nested_config',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {
                'hide_sender_email_in_notifications': True,
                'render_review_in_interface': False,
            },
            {
                'allow_parent_company_pay_for_documents': True,
            },
            {
                'config': {
                    'allow_send_document_status': True,
                    'allow_send_document_finish_status': True,
                }
            },
            HTTPStatus.OK,
            CompanyConfig(
                hide_sender_email_in_notifications=True,
                render_review_in_interface=False,
                allow_send_document_status=True,
                allow_send_document_finish_status=True,
                allow_parent_company_pay_for_documents=True,
            ),
            id='update_config_does_not_affect_admin_config',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {
                'hide_sender_email_in_notifications': True,
            },
            {
                'enable_2fa_for_internal_users': True,
                'allow_pay_as_recipient': True,
            },
            {
                'admin_config': {
                    'allow_pay_as_recipient': True,
                    'parent_company': '12345678',
                },
            },
            HTTPStatus.OK,
            CompanyConfig(
                hide_sender_email_in_notifications=True,
                allow_pay_as_recipient=True,
                enable_2fa_for_internal_users=True,
                parent_company='12345678',
            ),
            id='update_admin_config_does_not_affect_config',
        ),
        # Test update of both config and admin_config
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {'hide_sender_email_in_notifications': True},
            {'allow_parent_company_pay_for_documents': False},
            {
                'config': {'allow_send_document_status': True},
                'admin_config': {'allow_parent_company_pay_for_documents': True},
            },
            HTTPStatus.OK,
            CompanyConfig(
                hide_sender_email_in_notifications=True,
                allow_send_document_status=True,
                allow_parent_company_pay_for_documents=True,
            ),
            id='update_both_config_and_admin_config',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {'hide_sender_email_in_notifications': True},
            {'allow_parent_company_pay_for_documents': True},
            {},
            HTTPStatus.OK,
            CompanyConfig(
                hide_sender_email_in_notifications=True,
                allow_parent_company_pay_for_documents=True,
            ),
            id='empty_update_preserves_existing_data',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {'config': {'allowed_signature_types': [SignatureFormat.internal_separated.value]}},
            HTTPStatus.OK,
            CompanyConfig(allowed_signature_types=[SignatureFormat.internal_separated]),
            id='update_allowed_signature_types',
        ),
        pytest.param(
            SUPER_ADMIN_EDRPOU,
            {},
            {},
            {'config': {'allowed_signature_types': ['not-existing-type']}},
            HTTPStatus.BAD_REQUEST,
            CompanyConfig(),
            id='update_allowed_signature_types_invalid_type',
        ),
    ],
)
async def test_update_company_config(
    aiohttp_client,
    prev_config: dict,
    prev_admin_config: dict,
    edrpou: str,
    update_data: dict,
    expected_status: HTTPStatus,
    expected_config: CompanyConfig,
):
    app, client, admin = await prepare_client(
        aiohttp_client,
        company_edrpou=edrpou,
        is_admin=True,
        enable_pro_functionality=False,
        super_admin_permissions={'can_edit_client_data': True},
    )
    if edrpou == SUPER_ADMIN_EDRPOU:
        await set_company_config(
            app,
            company_id=admin.company_id,
            master=True,
            admin_is_superadmin=True,
        )

    user = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        enable_pro_functionality=False,
    )
    async with app['db'].acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=user.company_id,
            config=prev_config,
            admin_config=prev_admin_config,
        )

        await request_update_company_config(
            client=client,
            user=admin,
            company_id=user.company_id,
            config=update_data,
            status=expected_status,
        )

        config = await get_company_config(conn=conn, company_id=user.company_id)
        assert config.model_dump(mode='json') == expected_config.model_dump(mode='json')


async def test_update_nested_company_config(aiohttp_client):
    """
    Given a nested company config
    When updating single value in nested config
    Expected config to be updated correctly
    """
    # Arrange
    app, client, admin = await prepare_client(aiohttp_client, is_admin=True)

    # Arrange initial config
    initial_config = {
        'archive_settings': {
            'allow_rejected_documents': True,
            'allow_uploaded_documents': True,
        },
        'antivirus_settings': {
            'is_download_pending_enabled': True,
        },
    }
    async with services.db.acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=admin.company_id,
            config=initial_config,
        )

    # Act - add additional config
    additional_config = {
        'archive_settings': {
            'allow_partially_signed_documents': True,
        },
        'antivirus_settings': {
            'is_download_infected_enabled': True,
        },
    }
    await request_update_additional_company_config(
        client=client,
        user=admin,
        company_id=admin.company_id,
        config=additional_config,
        status=HTTPStatus.OK,
    )

    # Assert additional config inserted properly
    async with services.db.acquire() as conn:
        company_config = await select_one(
            conn=conn,
            query=sa.select([company_config_table.c.config, company_config_table.c.admin_config]),
        )

    assert company_config
    assert company_config.admin_config == {}
    assert company_config.config == {
        'archive_settings': {
            'allow_partially_signed_documents': True,
            'allow_rejected_documents': True,
            'allow_uploaded_documents': True,
        },
        'antivirus_settings': {
            'is_download_infected_enabled': True,
            'is_download_pending_enabled': True,
        },
    }

    # Act - amend existing config
    amended_existing_config = {
        'archive_settings': {
            'allow_uploaded_documents': False,
        },
        'antivirus_settings': {
            'is_download_pending_enabled': False,
        },
    }
    await request_update_additional_company_config(
        client=client,
        user=admin,
        company_id=admin.company_id,
        config=amended_existing_config,
        status=HTTPStatus.OK,
    )

    # Assert amended config inserted properly
    async with services.db.acquire() as conn:
        company_config = await select_one(
            conn=conn,
            query=sa.select([company_config_table.c.config, company_config_table.c.admin_config]),
        )

    assert company_config.admin_config == {}
    assert company_config.config == {
        'archive_settings': {
            'allow_partially_signed_documents': True,
            'allow_rejected_documents': True,
            'allow_uploaded_documents': False,
        },
        'antivirus_settings': {
            'is_download_pending_enabled': False,
            'is_download_infected_enabled': True,
        },
    }


@pytest.mark.parametrize(
    'is_admin, sa_perms, expected_status',
    [
        (True, {}, HTTPStatus.FORBIDDEN),
        (False, {}, HTTPStatus.FORBIDDEN),
        (False, {'can_view_client_data': True}, HTTPStatus.FORBIDDEN),
        (False, {'can_edit_client_data': True}, HTTPStatus.OK),
        (False, {'can_edit_special_features': True}, HTTPStatus.FORBIDDEN),
    ],
)
async def test_update_company_config_permissions(
    aiohttp_client, is_admin, sa_perms, expected_status
):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        is_admin=is_admin,
        super_admin_permissions=sa_perms,
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    await request_update_company_config(
        client=client,
        user=user,
        company_id=user.company_id,
        config={'config': {'soft_access_to_tags': True}},
        status=expected_status,
    )


async def test_diia_request(aiohttp_client, monkeypatch):
    async def mock_request(*args, **kwargs):
        return 'https://ukrainer.net'

    monkeypatch.setattr(diia_utils, '_request_token', mock_request)
    monkeypatch.setattr(diia_utils, '_request_deeplink', mock_request)

    app, client, owner = await prepare_client(aiohttp_client)

    resp = await client.post(
        '/internal-api/companies/diia',
        headers=prepare_auth_headers(owner),
    )
    assert resp.status == 200, await resp.json()

    data = await resp.json()
    assert data['qr']
    assert data['url'] == 'https://ukrainer.net'


@pytest.mark.parametrize('limit', [1, None])
async def test_add_company_with_overdraft(aiohttp_client, test_flags, limit):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(
        app, email=TEST_USER_EMAIL, company_edrpou=TEST_COMPANY_EDRPOU
    )

    cid = user.company_id
    async with services.db.acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=cid,
            config={CompanyLimit.employees.value: limit},
        )
        await update_company_entity_count(
            conn, cid, entity=StatEntity.role, count=limit if limit is not None else 1
        )

    data = {
        'company_edrpou': user.company_edrpou,
        'company_name': TEST_COMPANY_NAME,
        'name': TEST_COMPANY_NAME,
    }
    resp = await client.post(
        ADD_COMPANY_URL,
        json=data,
        headers=prepare_auth_headers(coworker),
    )
    if limit is not None:
        assert resp.status == 400
        json = await resp.json()
        assert json['code'] == Code.overdraft.name
    else:
        assert resp.status == 201


@pytest.mark.parametrize(
    'user_language, request_data, expected_language',
    [
        # Check that user can update preferred language
        (Language.uk, {'language': 'en'}, Language.en),
        # Check that user can set preferred language
        (None, {'language': 'uk'}, Language.uk),
    ],
)
async def test_update_language(
    aiohttp_client,
    user_language,
    request_data,
    expected_language,
):
    """Test that user can update its own language setting"""
    app, client, user = await prepare_client(aiohttp_client, language=user_language)

    response = await client.post(
        path=UPDATE_LANGUAGE_URL,
        json=request_data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    user = await get_base_user(user_id=user.id)
    assert user.language == expected_language


@pytest.mark.parametrize('mobile_usage', [True, False])
async def test_update_phone_usage(aiohttp_client, mobile_usage):
    """Test that user can update its own language setting"""
    app, client, user = await prepare_client(aiohttp_client)

    response = await client.post(
        path=UPDATE_PHONE_USAGE_URL,
        json={'mobile_usage': mobile_usage},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        result = await select_phone_usage_info(conn, user.id)
    assert result == mobile_usage

    query = '{ currentUser { userMeta { mobileUsage } } }'
    response = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert response['currentUser']['userMeta']['mobileUsage'] == mobile_usage


@pytest.mark.parametrize(
    'crm_response',
    [
        # 0. user doesn't exist in CRM
        None,
        # 1. user doesn't have any roles in CRM
        CRMUserInfoFull(
            user=CRMUserInfo(
                crm_id='123',
                vchasno_id=TEST_UUID,
                email=TEST_USER_EMAIL,
                surname=None,
                given_name=None,
                middle_name=None,
                mobile_phone=None,
            ),
            roles=[],
        ),
        # 2. user hasn't role in CRM with_signature_key=True
        CRMUserInfoFull(
            user=CRMUserInfo(
                crm_id='123',
                vchasno_id=TEST_UUID,
                email=TEST_USER_EMAIL,
                surname=None,
                given_name=None,
                middle_name=None,
                mobile_phone=None,
            ),
            roles=[
                CRMRoleInfoWithCompany(
                    role=CRMRoleInfo(
                        crm_id='123',
                        service_type=CRMSource.KASA,
                        type_=None,
                        position=None,
                        with_signature_key=False,
                    ),
                    company=CRMCompanyInfo(
                        crm_id='123',
                        edrpou='12345678',
                        name='company',
                        meta_edo=None,
                        meta_edi=None,
                        meta_kep=None,
                        meta_kasa=None,
                        meta_hrs=None,
                        meta_ttn=None,
                    ),
                )
            ],
        ),
    ],
)
async def test_role_sync_user_does_not_have_any_roles(aiohttp_client, monkeypatch, crm_response):
    """
    Given:
        - a user with no roles in system nor user doesn't exist in CRM
    When:
        - sync_roles is called
    Then:
        - no roles were created
    """
    app, client = await prepare_app_client(
        aiohttp_client,
    )
    headers = prepare_referer_headers(client)

    await prepare_base_user_data(
        app, user_id=TEST_UUID, password=TEST_USER_PASSWORD, email=TEST_USER_EMAIL
    )
    response = await client.post(
        path=LOGIN_URL,
        json={'email': TEST_USER_EMAIL, 'password': TEST_USER_PASSWORD},
        headers=headers,
    )

    monkeypatch.setattr(services.crm_client, 'get_user_info', AsyncMock(return_value=crm_response))

    response = await client.get(
        '/internal-api/registration/sync-roles',
        headers=response.headers,
    )
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        assert await count(conn, role_table) == 0
        assert await count(conn, company_table) == 1  # there is vchasno service company in db


@pytest.fixture()
def mock_crm_response(monkeypatch):
    """
    User has roles:
    - role in CRM with_signature_key=True
    - role in CRM with_signature_key=True
    - role in CRM with_signature_key=False
    """

    def call():
        monkeypatch.setattr(
            services.crm_client,
            'get_user_info',
            AsyncMock(
                return_value=CRMUserInfoFull(
                    user=CRMUserInfo(
                        crm_id='123',
                        vchasno_id=TEST_UUID,
                        email=TEST_USER_EMAIL,
                        surname=None,
                        given_name=None,
                        middle_name=None,
                        mobile_phone=None,
                    ),
                    roles=[
                        CRMRoleInfoWithCompany(
                            role=CRMRoleInfo(
                                crm_id='123',
                                service_type=CRMSource.KASA,
                                type_=None,
                                position=None,
                                with_signature_key=True,
                            ),
                            company=CRMCompanyInfo(
                                crm_id='123',
                                edrpou='12345678',
                                name='company',
                                meta_edo=None,
                                meta_edi=None,
                                meta_kep=None,
                                meta_kasa=None,
                                meta_hrs=None,
                                meta_ttn=None,
                            ),
                        ),
                        CRMRoleInfoWithCompany(
                            role=CRMRoleInfo(
                                crm_id='123',
                                service_type=CRMSource.KASA,
                                type_=None,
                                position=None,
                                with_signature_key=True,
                            ),
                            company=CRMCompanyInfo(
                                crm_id='123',
                                edrpou='01234567',
                                name='company',
                                meta_edo=None,
                                meta_edi=None,
                                meta_kep=None,
                                meta_kasa=None,
                                meta_hrs=None,
                                meta_ttn=None,
                            ),
                        ),
                        CRMRoleInfoWithCompany(
                            role=CRMRoleInfo(
                                crm_id='1234',
                                service_type=CRMSource.KASA,
                                type_=None,
                                position=None,
                                with_signature_key=False,
                            ),
                            company=CRMCompanyInfo(
                                crm_id='1234',
                                edrpou='87654321',
                                name='company',
                                meta_edo=None,
                                meta_edi=None,
                                meta_kep=None,
                                meta_kasa=None,
                                meta_hrs=None,
                                meta_ttn=None,
                            ),
                        ),
                    ],
                )
            ),
        )

    return call


async def test_role_sync_user_has_role_with_key(aiohttp_client, mock_crm_response):
    """
    Given:
        - a user with no roles in system
        - role in CRM with_signature_key=True
        - role in CRM with_signature_key=True
        - role in CRM with_signature_key=False
    When:
        - sync_roles is called
    Then:
        - two roles and two companies were created (for with_signature_key)
    """
    app, client = await prepare_app_client(
        aiohttp_client,
    )
    mock_crm_response()

    headers = prepare_referer_headers(client)

    await prepare_base_user_data(
        app,
        user_id=TEST_UUID,
        password=TEST_USER_PASSWORD,
        email=TEST_USER_EMAIL,
    )
    response = await client.post(
        path=LOGIN_URL,
        json={'email': TEST_USER_EMAIL, 'password': TEST_USER_PASSWORD},
        headers=headers,
    )

    response = await client.get(
        '/internal-api/registration/sync-roles',
        headers=response.headers,
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()

    assert response_json['errors'] == {}
    assert len(response_json['new_roles']) == 2

    async with services.db.acquire() as conn:
        roles = await select_roles(conn, user_id=TEST_UUID)
        assert len(roles) == 2
        assert all(role.status == RoleStatus.active for role in roles)
        assert all(role.activation_source == RoleActivationSource.sync_role for role in roles)
        assert all(role.activated_by is None for role in roles)
        assert all(role.date_activated is not None for role in roles)
        assert await count(conn, company_table) == 3  # there is vchasno service company in db


async def test_role_sync_user_has_role_with_key_has_role_or_company(
    aiohttp_client, mock_crm_response
):
    """
    Given:
        - a user with some roles in system
        - role in CRM with_signature_key=True (has role in system)
        - role in CRM with_signature_key=True (no role in system but has company)
        - role in CRM with_signature_key=False (no role in system and no company)
    When:
        - sync_roles is called
        - sync_roles is called again
    Then:
        - one role and one company were created (for with_signature_key)
        - no new roles were created on second call
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
        user_id=TEST_UUID,
        company_edrpou='12345678',
    )
    mock_crm_response()

    response = await client.get(
        '/internal-api/registration/sync-roles', headers=prepare_auth_headers(user)
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()

    assert response_json['errors'] == {}
    assert len(response_json['new_roles']) == 1

    async with services.db.acquire() as conn:
        assert await count(conn, role_table) == 2
        assert await count(conn, company_table) == 3  # there is vchasno service company in db

    # call it again to check that no new roles were created
    response = await client.get(
        '/internal-api/registration/sync-roles', headers=prepare_auth_headers(user)
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()

    assert response_json['errors'] == {}
    assert len(response_json['new_roles']) == 0


async def test_role_sync_user_has_role_with_key_no_space_in_company(
    aiohttp_client, mock_crm_response
):
    """
    Given:
        - a user with role in company1
        - a user without any roles in system
        - role in CRM with_signature_key=True (no space in company1)
        - role in CRM with_signature_key=True (no role in system nor company)
        - role in CRM with_signature_key=False (no role in system and no company)
    When:
        - sync_roles is called
    Then:
        - one role and one company were created
        - response returns error for company without space
    """

    app, client, coworker = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='12345678',
        email=TEST_USER_EMAIL + '1',
    )
    mock_crm_response()

    # set limit to 1 employee
    async with services.db.acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=coworker.company_id,
            config={CompanyLimit.employees.value: 1},
        )

    await prepare_base_user_data(
        app,
        user_id=TEST_UUID,
        password=TEST_USER_PASSWORD,
        email=TEST_USER_EMAIL,
    )
    headers = prepare_referer_headers(client)

    response = await client.post(
        path=LOGIN_URL,
        json={'email': TEST_USER_EMAIL, 'password': TEST_USER_PASSWORD},
        headers=headers,
    )

    response = await client.get('/internal-api/registration/sync-roles', headers=response.headers)
    assert response.status == HTTPStatus.OK
    response_json = await response.json()

    assert response_json['errors'] == {
        '12345678': {
            'code': 'overdraft',
            'details': None,
            'reason': 'Досягнуто максимальної кількості співробітників згідно поточного тарифу',
        }
    }
    assert len(response_json['new_roles']) == 1

    async with services.db.acquire() as conn:
        assert await count(conn, role_table) == 2  # coworker and new role
        assert (
            await count(conn, company_table) == 3
        )  # (vchasno + company without space + new company)


async def test_start_user_email_change_with_password(aiohttp_client, mailbox):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
    )

    new_email = TEST_EMAIL_2

    response = await request_start_user_email_change(client, user, new_email=new_email)
    assert response['status'] == UserEmailChangeStatus.verify_password.value

    assert len(mailbox) == 0

    changes = await get_user_email_changes()
    assert len(changes)

    change = changes[0]
    assert change.old_email == TEST_EMAIL_1
    assert change.new_email == TEST_EMAIL_2
    assert change.user_id == user.id
    assert change.status == UserEmailChangeStatus.verify_password
    assert change.date_created is not None


async def test_start_user_email_change_without_password(aiohttp_client, mailbox):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        password=None,
    )

    new_email = TEST_EMAIL_2

    response = await request_start_user_email_change(client, user, new_email=new_email)
    assert response['status'] == UserEmailChangeStatus.verify_email.value

    assert len(mailbox) == 1
    assert mailbox[0]['Subject'] == 'Підтвердження доступу до облікового запису'
    assert mailbox[0]['X-Template'] == 'user_email_change_verify_email'

    changes = await get_user_email_changes()
    assert len(changes)

    change = changes[0]
    assert change.old_email == TEST_EMAIL_1
    assert change.new_email == TEST_EMAIL_2
    assert change.user_id == user.id
    assert change.status == UserEmailChangeStatus.verify_email
    assert change.date_created is not None


async def test_verify_email_user_email_change(aiohttp_client, mailbox):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        password=None,
    )

    new_email = TEST_EMAIL_2

    response = await request_start_user_email_change(client, user, new_email=new_email)
    assert response['status'] == UserEmailChangeStatus.verify_email.value

    changes = await get_user_email_changes()
    assert len(changes) == 1
    change = changes[0]
    assert change.status == UserEmailChangeStatus.verify_email
    assert change.date_created is not None

    token = create_user_email_change_token(
        change_id=change.id,
        action=USER_EMAIL_CHANGE_VERIFY_EMAIL_TOKEN_ACTION,
    )

    response = await client.post(
        path=VERIFY_EMAIL_USER_EMAIL_CHANGE_URL,
        json={'token': token},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()
    assert response_json['status'] == UserEmailChangeStatus.pending.value
    assert response_json['old_email'] == TEST_EMAIL_1
    assert response_json['new_email'] == TEST_EMAIL_2

    changes = await get_user_email_changes()
    assert len(changes) == 1
    change = changes[0]
    assert change.status == UserEmailChangeStatus.pending


async def test_user_email_change_verify_password_wrong(aiohttp_client, mailbox):
    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
    )

    new_email = TEST_EMAIL_2

    await request_start_user_email_change(client, user, new_email=new_email)

    response = await client.post(
        path=VERIFY_PASSWORD_USER_EMAIL_CHANGE_URL,
        json={'password': TEST_PASSWORD_2},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    response_json = await response.json()
    assert response_json['code'] == 'invalid_current_password'
    assert response_json['reason'] == 'Помилка в паролі'


@pytest.mark.parametrize(
    'old_email, new_email, company_email_domains, expected_response',
    [
        pytest.param(
            '<EMAIL>',
            '<EMAIL>',
            ['vchasno.ua', 'vchasno.com.ua'],
            {
                'status': HTTPStatus.OK,
            },
            id='valid_domain',
        ),
        pytest.param(
            '<EMAIL>',
            '<EMAIL>',
            ['vchasno.ua'],
            {
                'status': HTTPStatus.FORBIDDEN,
                'reason': (
                    'Неможливо змінити електронну пошту на вказану, оскільки в одній або '
                    'декількох компаніях використовується корпоративна електронна пошта, '
                    'яка не співпадає з новою електронною поштою'
                ),
            },
            id='invalid_domain',
        ),
    ],
)
async def test_start_user_email_change_invalid_domain(
    aiohttp_client,
    old_email,
    new_email,
    company_email_domains,
    expected_response: dict,
):
    app, client, user = await prepare_client(aiohttp_client, email=old_email)

    company_id = await prepare_company_data(
        app,
        edrpou=TEST_COMPANY_EDRPOU_2,
        email_domains=company_email_domains,
    )
    await prepare_role(user_id=user.id, company_id=company_id, role_status=RoleStatus.active)

    response = await client.post(
        path=START_USER_EMAIL_CHANGE_URL,
        json={'new_email': new_email},
        headers=prepare_auth_headers(user),
    )
    assert response.status == expected_response['status'], await response.json()
    if response.status != HTTPStatus.OK:
        response_json = await response.json()
        assert response_json['reason'] == expected_response['reason']


async def test_start_user_email_change_another_email_with_password(aiohttp_client, mailbox):
    """
    When the user decided to enter different email during an email change process
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
    )

    response = await request_start_user_email_change(client, user, new_email=TEST_EMAIL_2)
    assert response['status'] == UserEmailChangeStatus.verify_password.value

    assert len(mailbox) == 0

    changes = await get_user_email_changes()
    assert len(changes)
    change_1 = changes[0]
    assert change_1.old_email == TEST_EMAIL_1
    assert change_1.new_email == TEST_EMAIL_2
    assert change_1.user_id == user.id
    assert change_1.status == UserEmailChangeStatus.verify_password
    assert change_1.date_created is not None

    mailbox.clear()

    response = await request_start_user_email_change(client, user, new_email=TEST_EMAIL_3)
    assert response['status'] == UserEmailChangeStatus.verify_password.value

    assert len(mailbox) == 0

    changes = await get_user_email_changes()
    assert len(changes) == 2

    # The first change should be canceled
    change_1_updated = next(c for c in changes if c.new_email == TEST_EMAIL_2)
    assert change_1_updated.old_email == TEST_EMAIL_1
    assert change_1_updated.new_email == TEST_EMAIL_2
    assert change_1_updated.user_id == user.id
    assert change_1_updated.status == UserEmailChangeStatus.cancelled

    # And new change should be created
    change_2 = next(c for c in changes if c.new_email == TEST_EMAIL_3)
    assert change_2.old_email == TEST_EMAIL_1
    assert change_2.new_email == TEST_EMAIL_3
    assert change_2.user_id == user.id
    assert change_2.status == UserEmailChangeStatus.verify_password
    assert change_2.date_created is not None


async def test_user_email_change_without_email(
    aiohttp_client, mailbox, evo_sender_mock, monkeypatch, crm_box
):
    otp_code = '666333'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)
    app, client, user = await prepare_client(
        aiohttp_client,
        email=None,
        email_confirmed=False,
        auth_phone=TEST_USER_PHONE,
        phone=TEST_USER_PHONE,
        is_phone_verified=True,
    )
    admin = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
        user_role=UserRole.admin,
        can_receive_email_change=True,
    )

    new_email = TEST_EMAIL_2

    # Start email change process
    response = await request_start_user_email_change(client, user, new_email=new_email)
    assert response['status'] == UserEmailChangeStatus.verify_2fa.value

    assert len(mailbox) == 0
    assert evo_sender_mock.phone == TEST_USER_PHONE
    assert otp_code in evo_sender_mock.message

    changes = await get_user_email_changes()
    assert len(changes) == 1

    change = changes[0]
    assert change.old_email is None
    assert change.new_email == TEST_EMAIL_2
    assert change.user_id == user.id
    assert change.status == UserEmailChangeStatus.verify_2fa
    assert change.date_created is not None

    # Verify 2FA code for email change
    response = await client.post(
        path=VERIFY_2FA_USER_EMAIL_CHANGE_URL,
        json={'code': otp_code},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()
    assert response_json['status'] == UserEmailChangeStatus.pending.value

    changes = await get_user_email_changes()
    assert len(changes) == 1
    assert changes[0].status == UserEmailChangeStatus.pending
    assert changes[0].old_email is None

    mailbox.clear()

    # Confirm email change by token from email
    update_user_profile_mock = mock.AsyncMock()
    monkeypatch.setattr(concierge, 'update_user_profile', update_user_profile_mock)

    token = create_user_email_change_token(
        change_id=changes[0].id,
        action=USER_EMAIL_CHANGE_CONFIRM_TOKEN_ACTION,
    )
    response = await client.post(
        path=CONFIRM_USER_EMAIL_CHANGE_URL,
        json={'token': token},
        headers={
            **prepare_auth_headers(user),
            concierge_headers.USER_ID: user.id,
            concierge_headers.ACCESS_TOKEN: 'fake-token',
        },
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()
    assert response_json['old_email'] is None
    assert response_json['new_email'] == TEST_EMAIL_2

    user_updated = await get_base_user(user_id=user.id)
    assert user_updated.email == TEST_EMAIL_2
    assert user_updated.email_confirmed is True

    changes = await get_user_email_changes()
    assert len(changes) == 1
    assert changes[0].status == UserEmailChangeStatus.confirmed

    update_user_profile_mock.assert_called_once()

    admin_mails = mailbox.by_email(admin.email)
    assert len(admin_mails) == 1
    assert admin_mails[0]['Subject'] == 'Електронну пошту користувача змінено'

    assert len(mailbox) == 1

    assert len(crm_box) == 1
    assert crm_box[0] == {
        'email': TEST_EMAIL_2,
        'vchasno_id': user.id,
        'mobile_phone': user.phone,
    }


async def test_resend_confirmation_user_email_change(aiohttp_client, mailbox):
    """
    Resend email change confirmation email to latest email change request
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
    )

    # completely different user
    user2 = await prepare_user_data(
        app=app,
        email=TEST_ANOTHER_USER_EMAIL,
        company_edrpou=TEST_COMPANY_EDRPOU_2,
        password=TEST_PASSWORD_2,
    )

    # Create 2 email change requests
    await request_start_user_email_change(client, user, new_email=TEST_EMAIL_2)
    await request_verify_password_user_email_change(client, user, password=TEST_PASSWORD_1)

    await request_start_user_email_change(client, user, new_email=TEST_EMAIL_3)
    await request_verify_password_user_email_change(client, user, password=TEST_PASSWORD_1)

    # Create 3rd email change request for another user, not related to the first one to test
    # that we are not sending emails to unrelated users
    await request_start_user_email_change(client, user2, new_email=TEST_EMAIL_4)
    await request_verify_password_user_email_change(client, user2, password=TEST_PASSWORD_2)

    mailbox.clear()

    response = await client.post(
        path=RESEND_CONFIRMATION_USER_EMAIL_CHANGE_URL,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    old_email_mails = mailbox.by_email(TEST_EMAIL_2)
    assert len(old_email_mails) == 0

    new_email_mails = mailbox.by_email(TEST_EMAIL_3)
    assert len(new_email_mails) == 1

    assert new_email_mails[0]['Subject'] == 'Підтвердження зміни email'
    assert new_email_mails[0]['X-Template'] == 'user_email_change_start'

    assert len(mailbox) == 1

    other_email_mails = mailbox.by_email(TEST_EMAIL_4)
    assert len(other_email_mails) == 0


async def test_cancel_user_email_change(aiohttp_client, mailbox) -> None:
    """
    Test check cancel user email change
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
    )

    # completely different user
    user2 = await prepare_user_data(
        app=app,
        email=TEST_ANOTHER_USER_EMAIL,
        company_edrpou=TEST_COMPANY_EDRPOU_2,
        password=TEST_PASSWORD_2,
    )

    # Create 2 email change requests
    await request_start_user_email_change(client, user, new_email=TEST_EMAIL_2)
    await request_verify_password_user_email_change(client, user, password=TEST_PASSWORD_1)

    await request_start_user_email_change(client, user, new_email=TEST_EMAIL_3)
    await request_verify_password_user_email_change(client, user, password=TEST_PASSWORD_1)

    # Create 3rd email change request for another user, not related to the first one to test
    # that we are not canceling emails to unrelated users
    await request_start_user_email_change(client, user2, new_email=TEST_EMAIL_4)
    await request_verify_password_user_email_change(client, user2, password=TEST_PASSWORD_2)

    mailbox.clear()

    response = await client.post(
        path=CANCEL_USER_EMAIL_CHANGE_URL,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    assert len(mailbox) == 0

    changes = await get_user_email_changes()
    assert len(changes) == 3

    user1_changes = [c for c in changes if c.user_id == user.id]
    user2_changes = [c for c in changes if c.user_id == user2.id]
    assert len(user1_changes) == 2
    assert all(c.status == UserEmailChangeStatus.cancelled for c in user1_changes)

    # This request was not changed
    assert len(user2_changes) == 1
    assert user2_changes[0].status == UserEmailChangeStatus.pending


async def test_verify_2fa_user_email_change(aiohttp_client, mailbox, evo_sender_mock, monkeypatch):
    otp_code = '666333'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code)

    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
        phone=TEST_USER_PHONE,
        is_phone_verified=True,
    )

    new_email = TEST_EMAIL_2

    await request_start_user_email_change(client, user, new_email=new_email)
    resp = await request_verify_password_user_email_change(client, user, password=TEST_PASSWORD_1)
    assert resp['status'] == UserEmailChangeStatus.verify_2fa.value

    assert evo_sender_mock.phone == TEST_USER_PHONE
    assert otp_code in evo_sender_mock.message

    # wrong code
    response = await client.post(
        path=VERIFY_2FA_USER_EMAIL_CHANGE_URL,
        json={'code': '000111'},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    response_json = await response.json()
    assert response_json['code'] == 'invalid_totp_code'
    assert response_json['reason'] == 'Код невірний. Спробуйте ще раз'

    # correct code
    response = await client.post(
        path=VERIFY_2FA_USER_EMAIL_CHANGE_URL,
        json={'code': otp_code},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()
    assert response_json['status'] == UserEmailChangeStatus.pending.value

    changes = await get_user_email_changes()
    assert len(changes) == 1
    assert changes[0].status == UserEmailChangeStatus.pending


async def test_resend_2fa_user_email_change(aiohttp_client, mailbox, evo_sender_mock, monkeypatch):
    otp_code_1 = '666333'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code_1)

    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
        phone=TEST_USER_PHONE,
        is_phone_verified=True,
    )

    new_email = TEST_EMAIL_2

    await request_start_user_email_change(client, user, new_email=new_email)
    await request_verify_password_user_email_change(client, user, password=TEST_PASSWORD_1)

    assert evo_sender_mock.phone == TEST_USER_PHONE
    assert otp_code_1 in evo_sender_mock.message

    # change otp code
    otp_code_2 = '111222'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: otp_code_2)

    response = await client.post(
        path=RESEND_2FA_USER_EMAIL_CHANGE_URL,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK

    assert evo_sender_mock.phone == TEST_USER_PHONE
    assert otp_code_2 in evo_sender_mock.message

    # try that old code is not working
    response = await client.post(
        path=VERIFY_2FA_USER_EMAIL_CHANGE_URL,
        json={'code': otp_code_1},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.BAD_REQUEST
    response_json = await response.json()
    assert response_json['code'] == 'invalid_totp_code'
    assert response_json['reason'] == 'Код невірний. Спробуйте ще раз'

    response = await client.post(
        path=VERIFY_2FA_USER_EMAIL_CHANGE_URL,
        json={'code': otp_code_2},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK
    response_json = await response.json()
    assert response_json['status'] == UserEmailChangeStatus.pending.value

    changes = await get_user_email_changes()
    assert len(changes) == 1
    assert changes[0].status == UserEmailChangeStatus.pending


async def test_confirm_user_email_change(aiohttp_client, mailbox, monkeypatch, crm_box):
    """
    Check a happy path of confirming email change:
    - a concierge should be called to update user profile
    - notification to the old email should be sent
    - notification to the admins of the companies where user has roles should be sent
    - document automation conditions should be updated
    - email in recipients should be updated
    - test deduplication: admins in multiple companies get only one notification
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        password=TEST_PASSWORD_1,
        can_receive_email_change=True,
    )

    admin1 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
        user_role=UserRole.admin,
        password=TEST_PASSWORD_2,
        can_receive_email_change=True,
    )

    # Second user's company with one admin
    admin2 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU_2,
        user_role=UserRole.admin,
        password=TEST_PASSWORD_3,
        can_receive_email_change=True,
    )
    await prepare_role(
        user_id=user.id,
        company_id=admin2.company_id,
        role_status=RoleStatus.active,
        can_receive_email_change=True,
    )

    # Create a multi-company admin who is in both the user's company and a third company
    # This tests the deduplication logic - admin should get only ONE email
    multi_company_admin = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU_4,
        user_role=UserRole.admin,
        password=TEST_PASSWORD_5,
        can_receive_email_change=True,
    )
    await prepare_role(
        user_id=multi_company_admin.id,
        company_id=user.company_id,
        role_status=RoleStatus.active,
        can_receive_email_change=True,
    )
    await prepare_role(
        user_id=user.id,
        company_id=multi_company_admin.company_id,
        role_status=RoleStatus.active,
        can_receive_email_change=True,
    )

    # Here, in this company, target user doesn't have any roles
    await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU_3,
        user_role=UserRole.admin,
        password=TEST_PASSWORD_4,
        can_receive_email_change=True,
    )

    await request_start_user_email_change(client, user, new_email=TEST_EMAIL_2)
    await request_verify_password_user_email_change(client, user, password=TEST_PASSWORD_1)

    mailbox.clear()

    changes = await get_user_email_changes()
    assert len(changes) == 1

    change_id = changes[0].id
    token = create_user_email_change_token(
        change_id=change_id,
        action=USER_EMAIL_CHANGE_CONFIRM_TOKEN_ACTION,
    )

    update_user_profile_mock = mock.AsyncMock()
    monkeypatch.setattr(concierge, 'update_user_profile', update_user_profile_mock)

    response = await client.post(
        path=CONFIRM_USER_EMAIL_CHANGE_URL,
        json={'token': token},
        headers={
            **prepare_auth_headers(user),
            concierge_headers.USER_ID: user.id,
            concierge_headers.ACCESS_TOKEN: 'fake-token',
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()
    response_json = await response.json()
    assert response_json['old_email'] == TEST_EMAIL_1
    assert response_json['new_email'] == TEST_EMAIL_2

    actions = await select_user_actions_for(company_id=user.company_id)
    assert len(actions) == 1
    assert actions[0].action == user_actions.Action.user_email_update
    assert actions[0].email == TEST_EMAIL_1  # old email
    assert actions[0].extra == {'new_email': TEST_EMAIL_2}
    assert actions[0].source == user_actions.Source.internal

    user_updated = await get_base_user(user_id=user.id)
    assert user_updated.email == TEST_EMAIL_2

    changes = await get_user_email_changes()
    assert len(changes) == 1
    assert changes[0].status == UserEmailChangeStatus.confirmed

    update_user_profile_mock.assert_called_once()

    # Fixate the fact that we have called this jobs, actual testing of the jobs is in other tests
    messages_topics = {msg[0] for msg in services.kafka.messages}
    assert topics.USER_EMAIL_CHANGE_UPDATE_AUTOMATIONS in messages_topics
    assert topics.USER_EMAIL_CHANGE_UPDATE_RECIPIENTS in messages_topics

    user1_mails = mailbox.by_email(TEST_EMAIL_1)
    assert len(user1_mails) == 1
    assert user1_mails[0]['Subject'] == 'Електронну пошту змінено'

    # Notify all admins in all companies where user has roles
    admin1_mails = mailbox.by_email(admin1.email)
    assert len(admin1_mails) == 1
    assert admin1_mails[0]['Subject'] == 'Електронну пошту користувача змінено'

    admin2_mails = mailbox.by_email(admin2.email)
    assert len(admin2_mails) == 1
    assert admin2_mails[0]['Subject'] == 'Електронну пошту користувача змінено'

    multi_admin_mails = mailbox.by_email(multi_company_admin.email)
    assert len(multi_admin_mails) == 1
    assert multi_admin_mails[0]['Subject'] == 'Електронну пошту користувача змінено'

    assert len(mailbox) == 4

    assert len(crm_box) == 1
    assert crm_box[0] == {
        'email': TEST_EMAIL_2,
        'vchasno_id': user.id,
        'mobile_phone': user.phone,
    }


async def test_check_contacts_on_user_email_change(aiohttp_client, mailbox, monkeypatch):
    """
    Test that after confirming email change, the user can update their email in recipient contacts.
    """
    # Prepare client and user with initial email
    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    headers = prepare_auth_headers(user)

    async def _check(*, is_exists: bool) -> None:
        # Check "Check contacts" endpoint before and after creating the contacts
        response = await client.post(
            path=CHECK_CONTACTS_ON_USER_EMAIL_CHANGE_URL,
            headers=headers,
        )
        assert response.status == HTTPStatus.OK, await response.json()
        assert (await response.json()) == {'exists': is_exists}

    await _check(is_exists=False)

    await prepare_user_email_change(
        user_id=user.id,
        old_email=TEST_EMAIL_1,
        new_email=TEST_EMAIL_2,
        status=UserEmailChangeStatus.confirmed,
    )
    # Prepare a company where the user has an active role
    company2_id = await prepare_company_data(app=app, edrpou=TEST_COMPANY_EDRPOU_2)
    await prepare_role(
        user_id=user.id,
        company_id=company2_id,
        role_status=RoleStatus.active,
    )

    # Prepare company where user does not have roles
    await prepare_company_data(app=app, edrpou=TEST_COMPANY_EDRPOU_3)

    # Two completely different recipients
    recipient1 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_8,
        company_edrpou=TEST_COMPANY_EDRPOU_8,
    )
    recipient2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_7,
        company_edrpou=TEST_COMPANY_EDRPOU_7,
    )

    await _check(is_exists=False)

    async with services.db.acquire() as conn:
        # User doesn't have roles in this company
        await prepare_contact(
            conn=conn,
            user=recipient2,
            contact={'edrpou': TEST_COMPANY_EDRPOU_4, 'name': 'Contact company name'},
            persons=[
                {'email': TEST_EMAIL_1},  # Old email
                {'email': TEST_EMAIL_6},  # Completely different email
            ],
        )

        await _check(is_exists=False)

        # Prepare contact only with new user email in contacts
        await prepare_contact(
            conn=conn,
            user=recipient2,
            contact={'edrpou': TEST_COMPANY_EDRPOU_2, 'name': 'Contact company name'},
            persons=[
                {'email': TEST_EMAIL_2},  # New email
                {'email': TEST_EMAIL_6},  # Completely different email
            ],
        )

        await _check(is_exists=False)

        # Prepare contact with old user email in contacts
        await prepare_contact(
            conn=conn,
            user=recipient1,
            contact={'edrpou': TEST_COMPANY_EDRPOU, 'name': 'Contact company name'},
            persons=[
                {'email': TEST_EMAIL_1},  # Old email
                {'email': TEST_EMAIL_5},  # Completely different email
            ],
        )

        await _check(is_exists=True)


async def test_update_contacts_on_user_email_change(aiohttp_client, mailbox, monkeypatch):
    """
    Test that after confirming email change, the user can update their email in recipient contacts.
    """
    # Prepare client and user with initial email
    app, client, user = await prepare_client(
        aiohttp_client,
        email=TEST_EMAIL_1,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    headers = prepare_auth_headers(user)

    await prepare_user_email_change(
        user_id=user.id,
        old_email=TEST_EMAIL_1,
        new_email=TEST_EMAIL_2,
        status=UserEmailChangeStatus.confirmed,
    )
    # Prepare a company where the user has an active role
    company2_id = await prepare_company_data(app=app, edrpou=TEST_COMPANY_EDRPOU_2)
    await prepare_role(
        user_id=user.id,
        company_id=company2_id,
        role_status=RoleStatus.active,
    )

    # Prepare company where user does not have roles
    await prepare_company_data(app=app, edrpou=TEST_COMPANY_EDRPOU_3)

    # Two completly different recipients
    recipient1 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_8,
        company_edrpou=TEST_COMPANY_EDRPOU_8,
    )
    recipient2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_7,
        company_edrpou=TEST_COMPANY_EDRPOU_7,
    )

    async with services.db.acquire() as conn:
        # Prepare contact with old user email in contacts
        contact1_id = await prepare_contact(
            conn=conn,
            user=recipient1,
            contact={
                'edrpou': TEST_COMPANY_EDRPOU,
                'name': 'Contact company name',
            },
            persons=[
                {
                    'email': TEST_EMAIL_1,  # Old email
                },
                {
                    'email': TEST_EMAIL_5,  # Completely different email
                },
            ],
        )

        # Prepare contact with new user email in contacts
        contact2_id = await prepare_contact(
            conn=conn,
            user=recipient2,
            contact={
                'edrpou': TEST_COMPANY_EDRPOU_2,
                'name': 'Contact company name',
            },
            persons=[
                {
                    'email': TEST_EMAIL_2,  # New email
                },
                {
                    'email': TEST_EMAIL_6,  # Completely different email
                },
            ],
        )

        # Old and new email is already in the contact
        contact3_id = await prepare_contact(
            conn=conn,
            user=recipient2,
            contact={
                'edrpou': TEST_COMPANY_EDRPOU_3,
                'name': 'Contact company name',
            },
            persons=[
                {
                    'email': TEST_EMAIL_1,  # Old email
                },
                {
                    'email': TEST_EMAIL_2,  # New email
                },
            ],
        )

        # User doesn't have roles in this company
        contact4_id = await prepare_contact(
            conn=conn,
            user=recipient2,
            contact={
                'edrpou': TEST_COMPANY_EDRPOU_4,
                'name': 'Contact company name',
            },
            persons=[
                {
                    'email': TEST_EMAIL_1,  # Old email
                },
                {
                    'email': TEST_EMAIL_6,  # Completely different email
                },
            ],
        )

    # Now, call the update_contacts_on_user_email_change endpoint
    response = await client.post(
        path=UPDATE_CONTACTS_ON_USER_EMAIL_CHANGE_URL,
        headers=headers,
    )
    assert response.status == HTTPStatus.OK, await response.json()

    changes = await get_user_email_changes()
    assert len(changes) == 1
    change1 = changes[0]
    assert change1.date_contacts_synced is not None

    # Check that the Kafka message was sent to schedule the update
    messages = [
        msg for msg in services.kafka.messages if msg[0] == topics.USER_EMAIL_CHANGE_UPDATE_CONTACTS
    ]
    assert len(messages) == 2
    message_value = messages[0][1]
    assert message_value['user_id'] == user.id
    assert message_value['old_email'] == TEST_EMAIL_1
    assert message_value['new_email'] == TEST_EMAIL_2

    persons = await get_contact_persons()
    mapping = defaultdict(set)
    for person in persons:
        mapping[person.contact_id].add(person.email)

    assert mapping[contact1_id] == {TEST_EMAIL_2, TEST_EMAIL_5}
    assert mapping[contact2_id] == {TEST_EMAIL_2, TEST_EMAIL_6}
    # Skip the contact where old and new email is already in the contact to prevent duplicates
    assert mapping[contact3_id] == {TEST_EMAIL_2, TEST_EMAIL_1}
    # User doesn't have roles in this company, so the contact should not be updated
    assert mapping[contact4_id] == {TEST_EMAIL_1, TEST_EMAIL_6}


async def test_update_user_profile(aiohttp_client, mailbox):
    """
    Test check update user profile
    """
    app, client, user = await prepare_client(aiohttp_client, email=TEST_EMAIL_1)
    admin = await prepare_user_data(app, email='<EMAIL>')

    response = await client.post(
        path=UPDATE_USER_PROFILE_URL,
        json={
            'first_name': 'John',
            'last_name': 'Doe',
            'second_name': 'Smith',
        },
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    user_updated = await get_base_user(user_id=user.id)
    assert user_updated.first_name == 'John'
    assert user_updated.last_name == 'Doe'
    assert user_updated.second_name == 'Smith'
    # no email sent because same user updated profile
    assert len(mailbox) == 0

    # Admin of company can update user profile as well
    response = await client.post(
        path=UPDATE_USER_PROFILE_URL,
        json={
            'first_name': 'Jane',
            'last_name': 'Doe',
            'second_name': 'Smith',
            'user_id': user.id,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    user_updated = await get_base_user(user_id=user.id)
    assert user_updated.first_name == 'Jane'
    assert user_updated.last_name == 'Doe'
    assert user_updated.second_name == 'Smith'
    # notify user that somebody changed his profile
    assert len(mailbox) == 1


class TestUpdateHRSRole:
    async def test_role_already_counts_for_billing(self, aiohttp_client):
        """
        Given an HRS role, which already counts for billing limit
        When trying to update the role with non-HRS fields
        Expected role to be updates successfully
        """
        # Arrange
        app, client, user = await prepare_client(aiohttp_client, is_admin=True)
        hrs_role = await prepare_user_data(
            app=app,
            email='<EMAIL>',
            company_edrpou=user.company_edrpou,
            has_hrs_role=True,
            is_counted_in_billing_limit=True,
        )

        # Act
        response = await client.patch(
            f'/internal-api/roles/{hrs_role.role_id}',
            data=ujson.dumps({'can_view_document': True}),
            headers=prepare_auth_headers(user),
        )

        # Assert
        assert response.status == 200

    async def test_employees_limit_reached(self, aiohttp_client):
        """
        Given an HRS role, which does not count for billing yet
        Given a company without space for new employee
        When trying to update the role with non-HRS fields
        Expected error to be raised
        """
        # Arrange
        app, client, user = await prepare_client(aiohttp_client, is_admin=True)
        hrs_role = await prepare_user_data(
            app=app,
            email='<EMAIL>',
            company_edrpou=user.company_edrpou,
            has_hrs_role=True,
            is_counted_in_billing_limit=False,
        )

        # Update employees limit to 1
        # Currently company already has 1 admin and 1 HRS role which does not count for limit
        async with services.db.acquire() as conn:
            await update_billing_company_config(
                conn=conn,
                company_id=user.company_id,
                config={CompanyLimit.employees.value: 1},
            )

        # Act
        response = await client.patch(
            f'/internal-api/roles/{hrs_role.role_id}',
            data=ujson.dumps({'can_view_document': True}),
            headers=prepare_auth_headers(user),
        )

        # Assert
        assert response.status == 400

        data = await response.json()
        assert data == {
            'code': 'overdraft',
            'details': None,
            'reason': 'Досягнуто максимальної кількості співробітників згідно поточного тарифу',
        }

    async def test_update_hrs_role_with_hrs_attributes(self, aiohttp_client):
        """
        Given an HRS role, which does not count for billing limit
        When trying to update the role with HRS fields
        Expected role to be updated successfully, and role remain not to be counted for billing
        """
        # Arrange
        app, client, user = await prepare_client(aiohttp_client, is_admin=True)
        hrs_role = await prepare_user_data(
            app=app,
            email='<EMAIL>',
            company_edrpou=user.company_edrpou,
            can_comment_document=False,
            has_hrs_role=True,
            is_counted_in_billing_limit=False,
        )

        # Act
        response = await client.patch(
            f'/internal-api/roles/{hrs_role.role_id}',
            data=ujson.dumps({'can_comment_document': True}),
            headers=prepare_auth_headers(user),
        )

        # Assert
        assert response.status == 200

        async with services.db.acquire() as conn:
            hrs_role = await select_role_by_id(conn=conn, role_id=hrs_role.role_id)

        assert hrs_role.can_comment_document is True
        assert hrs_role.is_counted_in_billing_limit is False

    async def test_update_hrs_role_with_non_hrs_attributes(self, aiohttp_client):
        """
        Given an HRS role, which does not count for billing limit
        When trying to update the role with non-HRS fields
        Expected role to be updated successfully and start to count towards billing limit
        """
        # Arrange
        app, client, user = await prepare_client(aiohttp_client, is_admin=True)
        hrs_role = await prepare_user_data(
            app=app,
            email='<EMAIL>',
            company_edrpou=user.company_edrpou,
            has_hrs_role=True,
            is_counted_in_billing_limit=False,
        )

        # Act
        response = await client.patch(
            f'/internal-api/roles/{hrs_role.role_id}',
            data=ujson.dumps({'can_edit_company': True}),
            headers=prepare_auth_headers(user),
        )

        # Assert
        assert response.status == 200

        async with services.db.acquire() as conn:
            hrs_role = await select_role_by_id(conn=conn, role_id=hrs_role.role_id)

        assert hrs_role.can_edit_company is True
        assert hrs_role.is_counted_in_billing_limit is True
