import logging
from http import HTT<PERSON>tatus

import pydantic
import sqlalchemy as sa
from aiohttp import web

from api.errors import (
    DoesNotExist,
    InvalidRequest,
)
from api.private.super_admin.db import insert_super_admin_action
from api.public.utils import process_role_update
from app import diia
from app.auth import (
    db,
    utils,
)
from app.auth.db import deny_role as deny_role_func
from app.auth.db import (
    update_roles,
    upsert_user_meta,
)
from app.auth.decorators import (
    base_login_required,
    login_required,
    redirect_to_app,
    super_admin_permission_required,
)
from app.auth.enums import (
    RoleStatus,
)
from app.auth.types import BaseUser, User
from app.auth.utils import (
    get_expected_base_user,
    update_company_roles_count,
    update_user,
)
from app.auth.validators import validate_is_user_current_password_valid
from app.crm.utils import (
    send_role_to_crm,
    send_user_to_crm,
)
from app.diia.schemas import AddRoleDiiaAction
from app.events.user_actions import Action, Source, types
from app.events.user_actions.utils import add_user_action, add_user_actions, get_event_source
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import (
    helpers,
    validators,
)
from app.lib import validators_pydantic as pv
from app.lib.datetime_utils import ONE_HOUR_DELTA
from app.lib.emailing import send_email
from app.lib.enums import (
    SuperAdminActionType,
    UserRole,
)
from app.lib.helpers import (
    not_none,
    read_csv_file,
)
from app.lib.locks import redis_lock
from app.profile import utils as profile
from app.profile.emailing import (
    send_admin_deleted_email,
    send_recover_password_email,
)
from app.profile.schemas import AddRoleDiiaRequestSchema
from app.profile.utils import (
    ChangeUserEmailWorkflow,
    invalidate_recover_password_jwt,
    update_esputnik_position,
)
from app.profile.utils import subscribe_to_esputnik as subscribe_to_esputnik_util
from app.profile.utils import update_default_recipient as update_default_recipient_util
from app.profile.validators import (
    UpdateMobileUsageValidator,
    UpdateTrialAutoEnabledValidator,
    validate_add_company,
    validate_agree_on_roles,
    validate_auth_phone_not_used,
    validate_delete_role,
    validate_deny_role,
    validate_phone_verification_otp,
    validate_recover_password,
    validate_remind_password,
    validate_sa_update_company_config,
    validate_send_verify_phone,
    validate_subscribe_to_esputnik,
    validate_update_2fa_state,
    validate_update_auth_phone,
    validate_update_company,
    validate_update_default_recipient,
    validate_update_language,
    validate_update_password,
    validate_update_role,
    validate_update_user,
    validate_update_user_phone,
    validate_user_email_change__confirm,
    validate_user_email_change__start,
    validate_user_email_change__update_contacts,
    validate_user_email_change__verify_2fa,
    validate_user_email_change__verify_email,
    validate_user_email_change__verify_password,
    validate_verify_phone,
)
from app.registration.utils import (
    add_active_role,
    create_company_registration_token,
    delete_company_registration_token,
    save_event_about_new_active_role,
    send_jobs_about_company_update,
    send_jobs_about_new_active_role,
)
from app.services import services
from worker import topics

ZAKUPKI_COMPANIES_FILE_ROUTE = 'static/files/zk-companies.csv'


logger = logging.getLogger(__name__)


@base_login_required()
async def add_company(request: web.Request, user: BaseUser | User) -> web.Response:
    """Add company to User Profile"""

    raw_data = await validators.validate_json_post_request(request=request)

    async with services.db.acquire() as conn:
        ctx = await validate_add_company(conn, user=user, data=raw_data)

        output = await add_active_role(conn, ctx)
        role = output.role

        await services.kafka.send_record(
            topic=topics.INITIATE_REMINDERS_FOR_NEW_SUCCESSFULLY_REGISTERED_EMPLOYEE,
            value={'role_id': role.id},
        )

    await send_jobs_about_new_active_role(
        user_id=user.id,
        user_email=user.email,
        role_id=role.id,
        company_id=role.company_id,
        company_edrpou=ctx.edrpou,
        company_name=None,
        is_company_registered=output.is_company_registered,
    )
    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await save_event_about_new_active_role(
            request_source=get_event_source(request),
            user=user,
            company_id=role.company_id,
            signature_info=ctx.signature_info,
        )

    await delete_company_registration_token(user_id=user.id, user_email=user.email)

    return web.json_response({'role_id': role.id}, status=HTTPStatus.CREATED)


@login_required()
async def agree_on_roles(request: web.Request, user: User) -> web.Response:
    """Agree that user notified on new roles (companies), he's added to."""
    async with request.app['db'].acquire() as conn:
        role_ids = await validate_agree_on_roles(
            conn, await validators.validate_json_request(request), user
        )

        await update_roles(conn, role_ids, {'date_agreed': sa.text('now()')})

    return web.json_response()


async def delete_role(request: web.Request, user: User) -> web.Response:
    """Delete role (user) from company by admin request."""
    app = request.app
    async with app['db'].acquire() as conn:
        data, role = await validate_delete_role(
            conn, {'role_id': request.match_info['role_id']}, user
        )
        is_current_role = role.id_ == user.role_id

        await utils.delete_role(
            conn=conn,
            role=role,
            by_user=user,
            status=RoleStatus.deleted,
        )

        # For deleted admin role need to notify other company admins
        # and log action
        if role.user_role == UserRole.admin.value and not is_current_role:
            await send_admin_deleted_email(conn, role, user)
            logger.info('Deleted admin from company', extra=data)

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        await add_user_action(
            user_action=types.UserAction(
                action=Action.role_delete,
                source=get_event_source(request),
                email=user.email,
                user_id=user.id,
                phone=user.auth_phone,
                company_id=role.company_id,
                extra={'affected_user_email': role.user_email},
            )
        )

    return web.json_response(status=204)


@base_login_required()
async def deny_role(request: web.Request, user: BaseUser | User) -> web.Response:
    async with request.app['db'].acquire() as conn:
        role = await validate_deny_role(conn, {'role_id': request.match_info['role_id']}, user)
        await deny_role_func(conn, role.id_)

    logger.info(
        'Role denied by user request',
        extra={
            'company_edrpou': user.company_edrpou if isinstance(user, User) else None,
            'user_email': user.email,
        },
    )
    return web.json_response()


@redirect_to_app
async def recover_password(request: web.Request) -> web.Response:
    """Recover password with token from email."""
    async with request.app['db'].acquire() as conn:
        try:
            ctx = await validate_recover_password(request, conn)
        except DoesNotExist:
            return web.json_response()

        await utils.update_user(
            conn=conn,
            user_id=ctx.user.id,
            data={
                'new_password': ctx.new_password,
                'email_confirmed': True,
                'is_autogenerated_password': False,
            },
        )

    await invalidate_recover_password_jwt(email=ctx.user_email)
    return web.json_response()


@redirect_to_app
async def remind_password(request: web.Request) -> web.Response:
    """Remind password if user is exists in database."""
    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        try:
            email = await validate_remind_password(conn, data)
        except DoesNotExist:
            return web.json_response()

    await helpers.validate_rate_limit(
        key=f'remind_password:{email}',
        limit=15,
        delta=ONE_HOUR_DELTA,
    )

    await send_recover_password_email(email)
    return web.json_response()


@login_required()
async def update_company(request: web.Request, user: User) -> web.Response:
    """
    Update current company data by company admin
    """

    ctx = await validate_update_company(request, user=user)

    company_data = ctx.to_company_data()

    async with services.db.acquire() as conn:
        company = await db.select_expected_company(conn, company_id=user.company_id)

        async with conn.begin():
            await db.update_company_by_id(
                conn=conn,
                company_id=user.company_id,
                data=company_data,
            )

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        event_data = ctx.to_event_data(company=company)
        if event_data:
            actions = [
                types.UserAction(
                    action=Action.company_update,
                    source=get_event_source(request),
                    email=user.email,
                    user_id=user.id,
                    phone=user.auth_phone,
                    company_id=user.company_id,
                    extra={'changed_field': field, 'new_value': value},
                )
                for field, value in event_data.items()
            ]
            await add_user_actions(actions)

    await send_jobs_about_company_update(company_id=user.company_id)

    return web.json_response()


@base_login_required()
async def update_password(
    request: web.Request,
    user: BaseUser | User,
) -> web.Response:
    """Update current user password."""

    data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        ctx = await validate_update_password(
            conn=conn,
            data=data,
            user=user,
        )
        await utils.update_user(
            conn=conn,
            user_id=user.id,
            data={
                'new_password': ctx.new_password,
                'is_autogenerated_password': False,
            },
        )

        logger.info(
            'Password updated by user',
            extra={'user_id': user.id},
        )

    return web.json_response()


async def update_role(request: web.Request, user: User) -> web.Response:
    """
    Update role settings
    """

    role_id = validators.validate_pydantic_adapter(
        pv.UUIDAdapter, value=request.match_info['role_id']
    )
    initiator = user
    data = await validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        ctx = await validate_update_role(
            conn=conn,
            initiator=initiator,
            role_id=role_id,
            raw_data=data,
        )
        update_data = ctx.data
        prev_role = ctx.prev_role

        role = await process_role_update(
            conn=conn,
            prev_role=prev_role,
            update_data=update_data,
            initiator=initiator,
            initiator_type=ctx.initiator_type,
        )

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            from app.events.user_actions import utils as user_actions_utils

            user_actions = await user_actions_utils.build_role_update_user_actions(
                actor_user=user,
                role_id=role.id,
                update_role_data=ctx.data.model_dump(exclude_none=True),
                request=request,
            )
            await user_actions_utils.add_user_actions(user_actions)

    if update_data.status and update_data.status.is_active:
        await update_company_roles_count(company_id=role.company_id)

    await update_esputnik_position(
        user_email=role.user_email,
        role_id=role.id,
        new_position=update_data.position,
        old_position=prev_role.position,
    )

    await send_role_to_crm(role_id=role.id)
    return web.json_response()


@login_required()
async def update_trial_auto_enabled(request: web.Request, user: User) -> web.Response:
    """Update trial auto enabled field for user."""
    data = validators.validate_pydantic(
        schema=UpdateTrialAutoEnabledValidator,
        data=await validators.validate_json_request(request),
    )

    async with services.db.acquire() as conn:
        await utils.update_user(
            conn=conn,
            user_id=user.id,
            data={'trial_auto_enabled': data.trial_auto_enabled},
        )

    return web.json_response()


@base_login_required()
async def update_user_profile(request: web.Request, user: User | BaseUser) -> web.Response:
    """Update current user profile."""
    async with services.db.acquire() as conn:
        ctx = await validate_update_user(
            conn=conn,
            data=await validators.validate_json_request(request),
            actor=user,
        )
        if ctx.db_data:
            await update_user(
                conn=conn,
                user_id=not_none(ctx.user.id),
                data=ctx.db_data,
            )
            updated_user = await get_expected_base_user(
                conn,
                user_id=ctx.user.id,
                only_with_email=False,
            )

    if ctx.is_on_behalf:
        assert isinstance(user, User), 'User instance is required for on behalf updates'

        # sent email to user that data was updated
        if ctx.user.email:
            await send_email(
                recipient_mixed=ctx.user.email,
                subject=_('Дані профілю оновлено'),
                template_name='user_data_updated',
                context={
                    'actor_email': user.email,
                    'actor_company_edrpou': user.company_edrpou,
                    'actor_company_name': user.company_name,
                    'new_fist_name': updated_user.first_name,
                    'new_second_name': updated_user.second_name,
                    'new_last_name': updated_user.last_name,
                },
            )

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            await add_user_action(
                user_action=types.UserAction(
                    action=Action.admin_user_update,
                    source=Source.internal,
                    user_id=user.id,
                    email=user.email,
                    phone=user.auth_phone,
                    company_id=user.company_id,
                    extra={'user_id': ctx.user.id, **ctx.db_data},
                )
            )

    await send_user_to_crm(user_id=not_none(ctx.user.id))

    return web.json_response()


@base_login_required()
async def send_verify_phone(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Send or resend a phone verification OTP code to the user's phone number.

    The user must have a phone number set before calling this endpoint.
    If the phone number is already verified, this is a no-op and returns 200 OK.
    """

    async with services.db.acquire() as conn:
        ctx = await validate_send_verify_phone(conn, request, user)

    await profile.send_phone_verification_otp(user=user, phone=ctx.phone)

    return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def verify_phone(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Verify the user's phone number using an OTP code sent to the phone.

    The user must have a phone number set and must provide a valid OTP code received via SMS/Viber.
    """
    await validate_verify_phone(request, user)

    async with services.db.acquire() as conn:
        await profile.verify_phone(conn, user=user)

    return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def update_phone(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Update and verify the user's phone number.

    When the user previously had a verified phone number, they must provide their password
    and OTP code to verify the new phone number. Otherwise, the phone number is updated
    without verification, and the user is expected to verify it later.

    """
    data = await validators.validate_json_request(request)

    async with services.db.acquire() as conn:
        ctx = await validate_update_user_phone(
            conn=conn,
            user=user,
            raw_data=data,
        )
        await profile.update_phone(
            conn=conn,
            user=user,
            phone=ctx.phone,
            is_phone_verified=ctx.is_phone_verified,
        )
        return web.json_response()


@base_login_required()
async def legacy_update_phone(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    TODO: you can remove this endpoint just right after the release
    """
    raw_data = await validators.validate_json_request(request)

    class _UpdateUserPhoneSchema(pydantic.BaseModel):
        phone: pv.Phone
        password: str = pydantic.Field(min_length=7, max_length=255)
        code: str | None = pydantic.Field(None, min_length=6, max_length=6)
        verify: bool | None = None

    data = validators.validate_pydantic(_UpdateUserPhoneSchema, raw_data)

    async with services.db.acquire() as conn:
        if user.auth_phone and data.phone:
            await validate_auth_phone_not_used(conn, auth_phone=data.phone)

        await validate_is_user_current_password_valid(conn=conn, password=data.password, user=user)

        if data.verify:
            await profile.send_phone_verification_otp(user=user, phone=data.phone)
            return web.json_response()

        await validate_phone_verification_otp(user=user, otp=data.code, phone=data.phone)

        # Update phone in database
        await profile.update_phone(
            conn=conn,
            user=user,
            phone=data.phone,
            is_phone_verified=True,
        )
        return web.json_response()


@base_login_required()
async def legacy_set_registration_phone(
    request: web.Request, user: BaseUser | User
) -> web.Response:
    """
    TODO: you can remove this endpoint just right after the release
    """
    raw_data = await validators.validate_json_request(request)

    class _SetRegistrationPhoneSchema(pydantic.BaseModel):
        phone: pv.Phone
        send_otp: bool = True

    data = validators.validate_pydantic(_SetRegistrationPhoneSchema, raw_data)
    if user.phone and user.is_phone_verified:
        raise InvalidRequest(reason=_('Для повторного встановлення телефону потрібен пароль'))

    async with services.db.acquire() as conn:
        await profile.update_phone(
            conn=conn,
            user=user,
            phone=data.phone,
            is_phone_verified=False,
        )

    if data.send_otp:
        await profile.send_phone_verification_otp(user=user, phone=data.phone)

    return web.json_response()


@base_login_required()
async def update_2fa_state(request: web.Request, user: User | BaseUser) -> web.Response:
    enabled = validate_update_2fa_state(
        await validators.validate_json_request(request),
        user,
    )
    async with request.app['db'].acquire() as conn:
        await utils.update_user(
            conn=conn,
            user_id=user.id,
            data={'is_2fa_enabled': enabled},
        )
    return web.json_response()


@base_login_required()
async def update_phone_auth(request: web.Request, user: BaseUser | User) -> web.Response:
    async with services.db.acquire() as conn:
        auth_phone = await validate_update_auth_phone(conn, request=request, user=user)
        await utils.update_user(
            conn=conn,
            user_id=user.id,
            # "phone" and "is_phone_verified" fields should be checked
            # by validator to match auth_phone
            data={'auth_phone': auth_phone},
        )

    return web.json_response()


@super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def sa_update_company_config_ui(request: web.Request, user: User) -> web.Response:
    """
    Update company config by super admins in admin UI
    """
    return await sa_update_company_config_common(request, user)


async def sa_update_company_config_common(request: web.Request, user: User) -> web.Response:
    """
    Common handler for updating company config by super admins in admin UI or API
    """
    async with services.db.acquire() as conn:
        ctx, company = await validate_sa_update_company_config(conn, request)

        async with conn.begin():
            await db.update_company_config(
                conn=conn,
                company_id=ctx.company_id,
                config=ctx.to_config_data(),
                admin_config=ctx.to_admin_config_data(),
            )
            extra = {
                'company_id': ctx.company_id,
                'config': ctx.to_config_data(),
                'admin_config': ctx.to_admin_config_data(),
            }
            await insert_super_admin_action(
                conn=conn,
                action=SuperAdminActionType.update_config,
                user=user,
                extra_details=[extra],
            )

    logger.info(
        msg='Company config was updated by super admin',
        extra={
            'company_id': ctx.company_id,
            'config': ctx.config,
            'admin_config': ctx.admin_config,
        },
    )

    return web.Response(status=HTTPStatus.OK)


@login_required()
async def update_default_recipient(request: web.Request, user: User) -> web.Response:
    async with (
        redis_lock(f'lock_default_recipient_change_{user.company_id}'),
        services.db.acquire() as conn,
    ):
        role_id = await validate_update_default_recipient(conn, request, user)
        await update_default_recipient_util(conn, edrpou=user.company_edrpou, role_id=role_id)
    return web.Response(status=HTTPStatus.OK)


async def subscribe_to_esputnik(request: web.Request) -> web.Response:
    ctx = await validate_subscribe_to_esputnik(request)
    await subscribe_to_esputnik_util(ctx)
    return web.json_response()


async def diia_request(request: web.Request, user: BaseUser) -> web.Response:
    raw_data = await validators.validate_json_request(request=request, allow_blank=True)
    valid_data = validators.validate_pydantic(AddRoleDiiaRequestSchema, raw_data)

    token = await create_company_registration_token(user_id=user.id, user_email=user.email)

    action_source = diia.get_diia_action_source(request)

    # It's not possible to add a company in sign session context, so we can safely assume
    # that user is authenticated and has a valid user ID.
    user_id: str | None = user.id
    assert user_id is not None, 'User ID must not be None'

    ctx = await diia.request_action(
        action=AddRoleDiiaAction(
            user_id=user_id,
            invited_type=valid_data.invited_type,
            invited_edrpou=valid_data.invited_edrpou,
            source=action_source,
        ),
        files=[
            diia.DiiaDeeplinkFile(title='Додавання компанії', hash=token.base64_hash),
        ],
    )

    return web.json_response(ctx.to_dict())


async def check_zakupki_company(request: web.Request) -> web.Response:
    data = await validators.validate_json_request(request)
    companies = read_csv_file(file_path=ZAKUPKI_COMPANIES_FILE_ROUTE)
    return web.json_response({edrpou: (edrpou,) in companies for edrpou in data['companies']})


@login_required()
async def update_language(request: web.Request, user: User) -> web.Response:
    """Update user's language."""

    language = await validate_update_language(request)

    async with services.db.acquire() as conn:
        await utils.update_user_language(conn=conn, user_id=user.id, language=language)

    return web.Response(status=HTTPStatus.OK)


@login_required()
async def update_phone_usage_info(request: web.Request, user: User) -> web.Response:
    """Update information about using Vchasno from mobile device"""
    raw_data = await validators.validate_json_request(request)
    data = validators.validate(UpdateMobileUsageValidator, raw_data)
    data['user_id'] = user.id

    async with services.db.acquire() as conn:
        await upsert_user_meta(conn, data)
    return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def user_email_change__validate_start(
    request: web.Request, user: BaseUser | User
) -> web.Response:
    """
    Stateless endpoint to validate the email change request before starting the process to show
    errors to the user early without actually starting the process.
    """
    async with services.db.acquire() as conn:
        await validate_user_email_change__start(request, conn, user)
        return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def user_email_change__start(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    First step of an email change process for a user with a confirmed email and company.

    On this step, we verify everything, save user request to change email and starting the process
    of verifying user identity by asking to enter password or follow the link from the old email.
    """
    async with services.db.acquire() as conn:
        ctx = await validate_user_email_change__start(request, conn, user)
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        next_status = await workflow.start_change(new_email=ctx.new_email)

    return web.json_response({'status': next_status})


@base_login_required()
async def user_email_change__verify_email(
    request: web.Request, user: BaseUser | User
) -> web.Response:
    """
    Verify the user identity by following the link from the old email
    """

    async with services.db.acquire() as conn:
        change = await validate_user_email_change__verify_email(request, conn, user)
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        next_status = await workflow.verify_email(change=change)

    response_data = {
        'status': next_status,
        'new_email': change.new_email,
        'old_email': change.old_email,
    }
    return web.json_response(response_data)


@base_login_required()
async def user_email_change__resend_verify_email(
    _: web.Request, user: BaseUser | User
) -> web.Response:
    """
    Sometimes verification email can be lost. In this case, user can click the button to resend it.
    """

    async with services.db.acquire() as conn:
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        await workflow.resend_verify_email()

    return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def user_email_change__verify_password(
    request: web.Request, user: BaseUser | User
) -> web.Response:
    async with services.db.acquire() as conn:
        change = await validate_user_email_change__verify_password(request, conn, user)
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        next_status = await workflow.verify_password(change=change)

    return web.json_response({'status': next_status})


@base_login_required()
async def user_email_change__verify_2fa(
    request: web.Request, user: BaseUser | User
) -> web.Response:
    async with services.db.acquire() as conn:
        change = await validate_user_email_change__verify_2fa(request, conn, user)
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        next_status = await workflow.verify_2fa(change=change)

    return web.json_response({'status': next_status})


@base_login_required()
async def user_email_change__resend_2fa(_: web.Request, user: BaseUser | User) -> web.Response:
    """
    Resend 2FA code to the user if they didn't receive it
    """

    async with services.db.acquire() as conn:
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        await workflow.resend_2fa()

    return web.json_response(status=HTTPStatus.OK)


@base_login_required()
async def user_email_change__resend_confirmation(
    _: web.Request, user: BaseUser | User
) -> web.Response:
    """
    Resend email with a confirmation link to new email
    """
    async with services.db.acquire() as conn:
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        await workflow.resend_confirmation()

    return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def user_email_change__cancel(_: web.Request, user: BaseUser | User) -> web.Response:
    """
    User can cancel their email change request at any time without
    any prior verification (unlike confirming the change or identity)
    """
    async with services.db.acquire() as conn:
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        await workflow.cancel_pending_changes()

    return web.Response(status=HTTPStatus.OK)


@base_login_required()
async def user_email_change__confirm(request: web.Request, user: BaseUser | User) -> web.Response:
    """
    Confirm email change by following the link from the new email

    WARN: This step should be final before changing the email in the database. Make sure that
    we checked the user identity before this change happened.
    """

    async with services.db.acquire() as conn:
        change = await validate_user_email_change__confirm(request, conn, user=user)
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        await workflow.confirm_change(change=change)

    response_data = {
        'old_email': change.old_email,
        'new_email': change.new_email,
    }
    return web.json_response(response_data, status=HTTPStatus.OK)


@base_login_required()
async def user_email_change__check_contacts(_: web.Request, user: BaseUser | User) -> web.Response:
    """
    Before offering user to update recipient contacts, we need to check if
    there are any contacts with the old email address
    """

    async with services.db.acquire() as conn:
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        exists = await workflow.check_contacts_exists()

    return web.json_response({'exists': exists})


@base_login_required()
async def user_email_change__update_contacts(_: web.Request, user: BaseUser | User) -> web.Response:
    """
    After confirming email change, user can optionally update his email
    in recipient contacts
    """

    async with services.db.acquire() as conn:
        change = await validate_user_email_change__update_contacts(conn, user=user)
        workflow = ChangeUserEmailWorkflow(conn=conn, user=user)
        await workflow.schedule_update_contacts(change=change)

    return web.Response(status=HTTPStatus.OK)
