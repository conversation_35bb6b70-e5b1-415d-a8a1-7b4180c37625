import logging

import sqlalchemy as sa
from sqlalchemy.sql import Select

from api.errors import Code, Error
from app import esputnik
from app.auth import concierge
from app.auth import utils as auth_utils
from app.auth.db import (
    cancel_pending_user_email_changes,
    insert_user_email_change,
    select_company_meta,
    select_default_recipient,
    select_latest_user_email_changes,
    select_telegram_chat_id_by_phone,
    select_user_roles,
    unblock_roles_2fa,
    update_role,
    update_user_email_change,
)
from app.auth.enums import RoleStatus, UserEmailChangeStatus
from app.auth.tables import role_table, user_role_join, user_table
from app.auth.types import AuthUser, BaseUser, User, UserEmailChange
from app.auth.utils import get_expected_base_user, is_fop, update_user
from app.billing.enums import AccountRate, CompanyRateStatus
from app.contacts.db import exists_email_in_recipients_contacts
from app.crm.utils import send_user_to_crm
from app.documents.types import DocumentWithUploader
from app.events import user_actions
from app.landing.utils import get_company_checked_information_for_user
from app.lib.brokers import KafkaClient
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import UserRole
from app.lib.sender.client import EvoSenderError
from app.lib.sender.enums import SenderMessageType
from app.lib.sender.utils import (
    generate_otp_code,
    send_phone_otp_code,
    validate_send_phone_otp_rate_limit,
)
from app.models import select_one
from app.profile.db import select_user_companies_ids
from app.profile.emailing import (
    send_about_email_change_notice_email,
    send_start_user_email_change_email,
    send_start_user_email_change_notice_email,
    send_user_email_change_verify_email,
)
from app.profile.types import EsputnikSubscriptionCtx
from app.services import services
from worker import topics
from worker.profile.schemas import (
    UserEmailChangeAsyncUpdateCtx,
    UserEmailChangeNotifyAdminsCtx,
    UserEmailChangeUpdateContactsCtx,
)

logger = logging.Logger(__name__)


async def update_esputnik_registration_completed(email: str | None) -> None:
    if not email:
        return

    registration_completed = esputnik.FieldId.registration_completed.value
    data = {
        'email': email,
        registration_completed: '1',
    }
    await services.kafka.send_record(topics.ESPUTNIK_UPDATE_CONTACT, data)


async def update_esputnik_email_confirmation(email: str | None) -> None:
    if not email:
        return
    email_confirmed = esputnik.FieldId.email_confirmed.value
    data = {
        'email': email,
        email_confirmed: '1',
    }
    await services.kafka.send_record(topics.ESPUTNIK_UPDATE_CONTACT, data)


async def add_esputnik_contact(
    *,
    email: str | None,
    kafka: KafkaClient | None = None,
) -> None:
    if not email:
        return

    data = {'email': email}
    await services.kafka.send_record(topics.ESPUTNIK_ADD_CONTACT, data)


async def generate_esputnik_registration_event(email: str | None) -> None:
    if not email:
        return
    data = {'email': email, 'event': esputnik.Event.registration.name}
    await services.kafka.send_record(topics.ESPUTNIK_GENERATE_EVENT, data)


async def generate_esputnik_first_employee_event(email: str | None) -> None:
    if not email:
        return

    data = {'email': email, 'event': esputnik.Event.first_employee.name}
    await services.kafka.send_record(topics.ESPUTNIK_GENERATE_EVENT, data)


async def generate_esputnik_check_companies_event(user_id: str) -> None:
    data = {'user_id': user_id}
    await services.kafka.send_record(topics.ESPUTNIK_COMPANY_CHECK_EVENT, data)


async def update_esputnik_company_rate(
    *,
    kafka: KafkaClient,
    users: list[DBRow],
    rate: AccountRate,
    status: CompanyRateStatus,
    is_on: bool,
    check_active_status: bool = False,
) -> None:
    # set True on calls from API (skip future activation 'new' status)
    if check_active_status and status != CompanyRateStatus.active:
        return

    if rate == AccountRate.pro:
        field = esputnik.FieldId.pro_rate_on.value
    elif rate == AccountRate.pro_trial:
        field = esputnik.FieldId.pro_rate_trial_on.value
    else:
        return

    value = '1' if is_on else '0'

    for user in users:
        data = {
            'email': user.email,
            field: value,
        }
        await kafka.send_record(topics.ESPUTNIK_UPDATE_CONTACT, data)


async def update_esputnik_company_check(email: str | None, user_id: str) -> None:
    """
    Send information to ESputnik that the user has used counterparty
    verification on the landing page.
    """
    if not email:
        return

    company_checking = esputnik.FieldId.company_checking.value

    checking_data = await get_company_checked_information_for_user(user_id)

    if not checking_data.total:
        logger.info(
            'Checking company for user not found',
            extra={'user_id': user_id, 'data': checking_data},
        )
        return

    existing_companies_percentage = round(checking_data.existing * 100 / checking_data.total)

    label = '0'
    for start, end in [(1, 10), (11, 30), (31, 50), (51, 100)]:
        if start <= existing_companies_percentage <= end:
            label = f'{start}-{end}'
            break

    data = {'email': email, company_checking: label}

    await services.kafka.send_record(topics.ESPUTNIK_UPDATE_CONTACT, data)


async def update_esputnik_position(
    *,
    user_email: str | None,
    role_id: str,
    new_position: str | None,
    old_position: str | None,
) -> None:
    """
    Send information to ESputnik about the user's position change.
    """
    if new_position:
        # This job synchronizes the position with ESputnik
        position_field = esputnik.FieldId.position.value
        data = {'email': user_email, position_field: new_position}
        await services.kafka.send_record(topics.ESPUTNIK_UPDATE_CONTACT, data)

    if new_position and not old_position:
        # We send this event only after an initial position was set for marketing purposes
        await _send_new_position_esputnik_event(old_position='', role_id=role_id)


async def _send_new_position_esputnik_event(old_position: str, role_id: str) -> None:
    data = {'role_id': role_id, 'old_position': old_position}
    await services.kafka.send_record(topics.ESPUTNIK_NEW_POSITION_SET_EVENT, data)


async def save_recover_password_jwt(*, email: str, token: str, ttl: int = 7200) -> None:
    """Store last relevant recover JWT to invalidate older tokens"""
    await services.redis.setex(f'rec_{email}', value=token, time=ttl)


async def invalidate_recover_password_jwt(*, email: str) -> None:
    await services.redis.delete(f'rec_{email}')


async def get_recover_password_jwt(*, email: str) -> str | None:
    return await services.redis.get(f'rec_{email}')


async def get_phone_verification_otp(*, email: str | None, user_id: str, phone: str) -> str | None:
    otp = await services.redis.get(f'phone_verification:{user_id}_{phone}')
    if otp:
        return otp

    # TODO: remove this block after release (TTL 3 minutes)
    if email:
        return await services.redis.get(f'phone_{email}_{phone}')

    return None


async def reset_phone_verification_otp(*, email: str | None, user_id: str, phone: str) -> None:
    # TODO: remove this line after release (TTL 3 minutes)
    await services.redis.delete(f'phone_{email}_{phone}')

    await services.redis.delete(f'phone_verification:{user_id}_{phone}')


async def save_phone_verification_otp(
    *,
    user_id: str,
    phone: str,
    otp: str,
) -> None:
    """
    We use both email and phone number to store the OTP in Redis to avoid situations when
    the OTP code was sent to another phone number than the one that was used to verify it.

    Also, email helps to avoid conflicts when the same phone number is used by different users.
    """
    ttl = services.config.auth.totp_interval
    await services.redis.setex(f'phone_verification:{user_id}_{phone}', value=otp, time=ttl)


async def send_phone_verification_otp(user: BaseUser, phone: str) -> None:
    """
    Send an OTP (one-time password) to the specified phone number to verify
    the phone number for existing user.

    WARN: don't use this function for 2FA OTPs, use functions from "app.auth.two_factor"
    module instead because they have additional logic specific to 2FA.
    """

    await validate_send_phone_otp_rate_limit(user_id=user.id)

    otp = generate_otp_code()

    await save_phone_verification_otp(user_id=user.id, phone=phone, otp=otp)

    # OTP message will be sent to the user's phone number via Viber or SMS
    try:
        await send_phone_otp_code(
            phone=phone,
            otp=otp,
            message_type=SenderMessageType.phone_verification,
        )
    except EvoSenderError:
        raise Error(Code.invalid_phone_number, details={'phone': phone})


async def update_default_recipient(conn: DBConnection, *, edrpou: str, role_id: str | None) -> None:
    """Update, set or reset company default recipient."""
    current_def_recipient = await select_default_recipient(conn, edrpou)
    if not current_def_recipient and not role_id:
        return

    if current_def_recipient and not role_id:
        await update_role(
            conn=conn,
            role_id=current_def_recipient.role_id,
            data={'is_default_recipient': False},
        )
    elif role_id and not current_def_recipient:
        await update_role(
            conn=conn,
            role_id=role_id,
            data={'is_default_recipient': True},
        )
    elif current_def_recipient and role_id:
        async with conn.begin():
            await update_role(
                conn=conn,
                role_id=current_def_recipient.role_id,
                data={'is_default_recipient': False},
            )
            await update_role(
                conn=conn,
                role_id=role_id,
                data={'is_default_recipient': True},
            )


async def subscribe_to_esputnik(ctx: EsputnikSubscriptionCtx) -> None:
    config = services.config.esputnik
    if not config:
        logging.info('Esputnik config is not set')
        return

    client = esputnik.Client(services.http_client, config)

    query = build_esputnik_users_query(1, 1, ctx.email)

    async with services.db.acquire() as conn:
        db_contact = await select_one(conn, query)
        if db_contact:
            contact = esputnik.Contact.from_db(db_contact)
        else:
            contact = esputnik.Contact.get_guest_contact(email=ctx.email, name=ctx.name)

        if not await client.subscribe(contact, ctx.source):
            return

        if db_contact:
            await update_user(
                conn=conn,
                user_id=db_contact.user_id,
                data={'is_subscribed_esputnik': True},
            )


async def generate_esputnik_first_incoming_signing_event(
    user: AuthUser | User | None, document: DBRow | DocumentWithUploader
) -> None:
    if not user:
        return
    if user.company_edrpou == document.edrpou_owner:
        return
    await services.kafka.send_record(
        topics.ESPUTNIK_FIRST_INCOMING_SIGNING_EVENT, {'email': user.email}
    )


async def generate_esputnik_documents_sent_event(
    conn: DBConnection, user: User | None, document: DocumentWithUploader
) -> None:
    if not user:
        return
    if user.company_edrpou != document.edrpou_owner:
        return
    meta = await select_company_meta(conn, company_id=user.company_id)
    if meta and meta.sent_documents_count_synced == 50:
        return
    await services.kafka.send_record(
        topics.ESPUTNIK_DOCUMENTS_SENT_EVENT,
        {'email': user.email, 'company_id': user.company_id},
    )


async def unblock_user_roles_2fa(conn: DBConnection, user_id: str) -> None:
    roles = await select_user_roles(conn, user_id)
    if not any(r.status == RoleStatus.blocked_2fa for r in roles):
        return
    await unblock_roles_2fa(conn, user_id)


async def update_esputnik_company_type(email: str | None, edrpou: str) -> None:
    is_natural = is_fop(edrpou)
    field = esputnik.FieldId.is_natural if is_natural else esputnik.FieldId.is_legal
    data = {'email': email, field.value: '1'}
    await services.kafka.send_record(topics.ESPUTNIK_UPDATE_CONTACT, data)


async def update_phone(
    conn: DBConnection,
    user: BaseUser,
    phone: str,
    is_phone_verified: bool,
) -> None:
    """
    Update a user's phone number in the database.
    """

    # For not verified phone number update we just set the phone number and don't update anything
    # else that requires phone verification.
    if not is_phone_verified:
        await auth_utils.update_user_phone(
            conn=conn,
            user_id=user.id,
            phone=phone,
            auth_phone=None,
            is_phone_verified=False,
            telegram_chat_id=None,
        )
        await send_user_to_crm(user_id=user.id)
        return

    # If there are telegram chat ID by this phone (even for another user), we set it
    # to the current user to send notifications to the same chat.
    telegram_chat_id = await select_telegram_chat_id_by_phone(conn, phone=phone)

    # When user has phone authentication enabled, we need to update auth phone too on update
    auth_phone: str | None = phone if user.auth_phone else None

    async with conn.begin():
        await auth_utils.update_user_phone(
            conn=conn,
            user_id=user.id,
            phone=phone,
            auth_phone=auth_phone,
            is_phone_verified=is_phone_verified,
            telegram_chat_id=telegram_chat_id,
        )

        # Unblock all roles that were previously blocked due because 2FA was enabled for all
        # coworkers in the company, but user had not validated the phone number.
        await unblock_user_roles_2fa(conn, user_id=user.id)

    await reset_phone_verification_otp(user_id=user.id, email=user.email, phone=phone)
    await send_user_to_crm(user_id=user.id)


async def verify_phone(conn: DBConnection, user: BaseUser) -> None:
    """
    Mark the user's phone number as verified in the database.

    Quite similar to "update_phone" function, but without updating the phone number
    """

    assert user.phone, 'User has no phone number'

    async with conn.begin():
        await update_user(
            conn=conn,
            user_id=user.id,
            data={'is_phone_verified': True},
        )

        await unblock_user_roles_2fa(conn=conn, user_id=user.id)

    await reset_phone_verification_otp(email=user.email, user_id=user.id, phone=user.phone)
    await send_user_to_crm(user_id=user.id)


async def send_create_initial_free_rate_job(company_id: str) -> None:
    """
    Logic related to company activation, after first active was added to the company.
    """
    await services.kafka.send_record(
        topic=topics.CREATE_INITIAL_FREE_RATE,
        value={'company_id': company_id},
    )


def build_esputnik_users_query(
    limit: int,
    offset: int,
    email: str | None = None,
) -> Select:
    """Only select those who confirmed email.

    ESputnik `Contact` is unique by email, so all custom metrics treated as:
    if at least one of user's companies satisfies the rule - result is true.

    If `email` parameter was given, query will return only one contact.
    `company_id` is needed for selecting proper user's company name
    """
    is_true = sa.text('1')
    is_false = sa.text('0')

    _role_table_alias = role_table.alias('role_table')

    query = sa.select(
        [
            user_table.c.id.label('user_id'),
            user_table.c.email,
            user_table.c.first_name,
            user_table.c.last_name,
            user_table.c.second_name,
            user_table.c.phone,
            user_table.c.date_created,
            sa.case(
                [
                    (
                        user_table.c.source == 'edi',
                        esputnik.RegistrationGroup.edi.name,
                    ),
                    (
                        user_table.c.source == 'kasa',
                        esputnik.RegistrationGroup.kasa.name,
                    ),
                ],
                else_=esputnik.RegistrationGroup.edo.name,
            ).label('registration_group'),
            # is admin in at least one company
            sa.and_(
                user_table.c.registration_completed,
                sa.exists(
                    sa.select([_role_table_alias.c.id])
                    # Alias makes it private to the subquery and prevent an automatic assumption
                    # by SQLAlchemy that "role_table" in the main query is the same as
                    # "role_table" in this subquery
                    .select_from(_role_table_alias)
                    .where(
                        sa.and_(
                            _role_table_alias.c.user_role == UserRole.admin.value,
                            _role_table_alias.c.status == RoleStatus.active,
                            _role_table_alias.c.user_id == user_table.c.id,
                        )
                    )
                ),
            )
            .cast(sa.Integer)
            .label('is_admin'),
            sa.case([(user_table.c.email_confirmed.is_(True), is_true)], else_=is_false).label(
                'email_confirmed'
            ),
            sa.case(
                [(user_table.c.registration_completed.is_(True), is_true)],
                else_=is_false,
            ).label('registration_completed'),
            sa.case([(user_table.c.email.isnot(None), is_true)], else_=is_false).label(
                'is_registered'
            ),
        ]
    ).select_from(user_role_join)

    if email:
        return query.where(user_table.c.email == email)

    return query.order_by(user_table.c.date_created).limit(limit).offset(offset)


class ChangeUserEmailWorkflow:
    def __init__(self, conn: DBConnection, user: BaseUser) -> None:
        self.conn = conn
        self.user = user

    @staticmethod
    def can_verify_phone(user: BaseUser) -> bool:
        """
        For all users (even without enabled 2FA) we should send an OTP to confirm the email change
        """
        return bool(user.phone and user.is_phone_verified)

    async def start_change(self, *, new_email: str) -> UserEmailChangeStatus:
        """
        Start the process of changing the user's email address
        """

        logger.info(
            msg='Starting user email change',
            extra={
                'user_id': self.user.id,
                'old_email': self.user.email,
                'new_email': new_email,
            },
        )

        async with self.conn.begin():
            await self.cancel_pending_changes()
            change = await insert_user_email_change(
                conn=self.conn,
                data={
                    'user_id': self.user.id,
                    'old_email': self.user.email,
                    'new_email': new_email,
                    'status': UserEmailChangeStatus.started,
                },
            )

        # Changing email is a highly sensitive operation, so we need to verify the user's
        # identity before proceeding with the change
        next_status: UserEmailChangeStatus
        if self.user.email:
            # For users with passwords, it is easier to
            # just ask to enter the password. For users without passwords (Signed Up with Google),
            # we send a link to the current email to confirm identity.
            #
            # If a user lost access to the previous email and doesn't have a password, they can
            # contact support to change the email manually.
            if self.user.password:
                next_status = await self._start_change__verify_password(change)
            else:
                next_status = await self._start_change__verify_email(change)
        else:
            # If user registered by phone and doesn't have email, we ask them to verify their
            # phone with OTP code and then move to the step to confirm new email
            if self.can_verify_phone(self.user):
                next_status = await self._move_to__verify_2fa(change=change)
            else:
                raise AssertionError('User has no email and no phone number to verify identity')

        return next_status

    async def _start_change__verify_email(self, change: UserEmailChange) -> UserEmailChangeStatus:
        """
        Verify previous email address by sending a link to the user's email with a confirmation
        token.
        """
        assert self.user.email, 'User has no email to verify'

        next_status = UserEmailChangeStatus.verify_email

        await update_user_email_change(
            conn=self.conn,
            change_id=change.id,
            data={'status': next_status},
        )

        await send_user_email_change_verify_email(
            user=self.user,
            old_email=self.user.email,
            change_id=change.id,
        )

        return next_status

    async def _start_change__verify_password(
        self, change: UserEmailChange
    ) -> UserEmailChangeStatus:
        """
        Move to the next status to in the email change process to inform the frontend to
        ask the user for their password
        """
        next_status = UserEmailChangeStatus.verify_password

        await update_user_email_change(
            conn=self.conn,
            change_id=change.id,
            data={'status': next_status},
        )

        return next_status

    async def verify_email(self, change: UserEmailChange) -> UserEmailChangeStatus:
        """
        Verify the user's identity by checking the token sent to the previous email address.
        """
        logger.info(
            msg='User email verified',
            extra={'user_id': self.user.id, 'change_id': change.id},
        )

        await update_user_email_change(
            conn=self.conn,
            change_id=change.id,
            data={'date_email_verified': utc_now()},
        )

        if self.can_verify_phone(self.user):
            next_status = await self._move_to__verify_2fa(change=change)
        else:
            next_status = await self._move_to__pending(change=change)

        return next_status

    async def resend_verify_email(self) -> None:
        """
        Resend the email with the confirmation link for the user's email change.
        """
        latest_change = await select_latest_user_email_changes(self.conn, user_id=self.user.id)

        logger.info(
            msg='Resending user email change',
            extra={
                'user_id': self.user.id,
                'latest_change_status': latest_change and latest_change.status,
            },
        )

        if latest_change and latest_change.status == UserEmailChangeStatus.verify_email:
            assert latest_change.old_email, 'Old email is required for verification'

            await send_user_email_change_verify_email(
                user=self.user,
                old_email=latest_change.old_email,
                change_id=latest_change.id,
            )

    async def verify_password(self, change: UserEmailChange) -> UserEmailChangeStatus:
        """
        After the user has entered their password, we verify it in the validation function and
        move to the next step in the email change process
        """
        logger.info(
            msg='User password verified',
            extra={'user_id': self.user.id, 'change_id': change.id},
        )
        await update_user_email_change(
            conn=self.conn,
            change_id=change.id,
            data={'date_password_verified': utc_now()},
        )

        if self.can_verify_phone(self.user):
            next_status = await self._move_to__verify_2fa(change=change)
        else:
            next_status = await self._move_to__pending(change=change)

        return next_status

    async def _move_to__verify_2fa(self, change: UserEmailChange) -> UserEmailChangeStatus:
        """
        Ask user to confirm the email change by sending an OTP to their phone number
        """

        user_phone = self.user.phone
        assert user_phone, 'User has no phone number'

        next_status = UserEmailChangeStatus.verify_2fa

        await send_phone_verification_otp(user=self.user, phone=user_phone)

        await update_user_email_change(
            conn=self.conn,
            change_id=change.id,
            data={'status': next_status},
        )

        return next_status

    async def _move_to__pending(self, change: UserEmailChange) -> UserEmailChangeStatus:
        """
        Move the email change to the pending status and send the user an email with a link to
        confirm the email change.
        """
        next_status = UserEmailChangeStatus.pending

        async with self.conn.begin():
            await update_user_email_change(
                conn=self.conn,
                change_id=change.id,
                data={'status': next_status},
            )

        await send_start_user_email_change_email(
            user=self.user,
            new_email=change.new_email,
            change_id=change.id,
        )
        await send_start_user_email_change_notice_email(
            user=self.user,
            old_email=change.old_email,
            new_email=change.new_email,
        )

        return next_status

    async def verify_2fa(self, change: UserEmailChange) -> UserEmailChangeStatus:
        """
        After verifying user identity and 2FA, only one step left - send an email with
        a confirmation link to the new email address
        """
        logger.info(
            msg='User 2FA verified',
            extra={'user_id': self.user.id, 'change_id': change.id},
        )
        await update_user_email_change(
            conn=self.conn,
            change_id=change.id,
            data={'date_2fa_verified': utc_now()},
        )
        return await self._move_to__pending(change=change)

    async def resend_2fa(self) -> None:
        """
        Resend the email with the confirmation link for the user's email change.
        """
        latest_change = await select_latest_user_email_changes(self.conn, user_id=self.user.id)

        logger.info(
            msg='Resending user email change',
            extra={
                'user_id': self.user.id,
                'user_phone': self.user.phone,
                'latest_change_status': latest_change and latest_change.status,
            },
        )

        if not latest_change or latest_change.status != UserEmailChangeStatus.verify_2fa:
            logger.info('No pending 2FA verification', extra={'user_id': self.user.id})
            return

        user_phone = self.user.phone
        if not user_phone:
            logger.info('User has no phone number', extra={'user_id': self.user.id})
            return

        await send_phone_verification_otp(user=self.user, phone=user_phone)

    async def resend_confirmation(self) -> None:
        """
        Resend the email with the confirmation link for the user's email change.
        """
        latest_change = await select_latest_user_email_changes(self.conn, user_id=self.user.id)

        logger.info(
            msg='Resending user email change',
            extra={
                'user_id': self.user.id,
                'latest_change_status': latest_change and latest_change.status,
            },
        )

        if latest_change and latest_change.status == UserEmailChangeStatus.pending:
            await send_start_user_email_change_email(
                user=self.user,
                new_email=latest_change.new_email,
                change_id=latest_change.id,
            )

    async def confirm_change(self, change: UserEmailChange) -> None:
        """
        Confirm the user's email change and start using the new email address.
        """
        logger.info(
            msg='Confirming user email change',
            extra={'user_id': self.user.id, 'change_id': change.id},
        )

        async with self.conn.begin():
            await update_user(
                conn=self.conn,
                user_id=self.user.id,
                data={
                    'email': change.new_email,
                    # Usually when user change email from existing email to new one, email is
                    # already confirmed, but in case when email is changed from non-existing
                    # email (e.g. user registered by phone) we need to set email_confirmed to True
                    'email_confirmed': True,
                },
            )
            await update_user_email_change(
                conn=self.conn,
                change_id=change.id,
                data={
                    'status': UserEmailChangeStatus.confirmed,
                    'date_confirmed': utc_now(),
                },
            )

            # Update profile on concierge with the new email to make new email visible for other
            # Vchasno services, like EDI or TTN. Each service should compare the email by user_id
            # and update the email if it's different.
            #
            # NOTE: this code is executed in transaction, so potentially transaction can fail on
            # commit and the email won't be updated in a database, but will be updated in
            # concierge. Currently, I don't know how to handle this case properly,
            # because request to concierge requires having a user session token from headers,
            # which is harder to obtain in the worker context to implement an outbox pattern.
            # Leaving it as is for now, with hope that transaction will almost always be successful.
            updated_user = await get_expected_base_user(self.conn, user_id=self.user.id)
            await concierge.update_user_profile(
                self.conn,
                user_id=self.user.id,
                user=updated_user,
            )

        # Update related objects
        if old_email := self.user.email:
            async_update_ctx = UserEmailChangeAsyncUpdateCtx(
                user_id=self.user.id,
                old_email=old_email,
                new_email=change.new_email,
            )
            await services.kafka.send_record(
                topic=topics.USER_EMAIL_CHANGE_UPDATE_RECIPIENTS,
                value=async_update_ctx.to_dict(),
            )
            await services.kafka.send_record(
                topic=topics.USER_EMAIL_CHANGE_UPDATE_AUTOMATIONS,
                value=async_update_ctx.to_dict(),
            )

        await self.save_user_email_change_action(new_email=change.new_email)

        # Send notifications
        await services.kafka.send_record(
            topic=topics.USER_EMAIL_CHANGE_NOTIFY_ADMINS,
            value=UserEmailChangeNotifyAdminsCtx(
                user_id=self.user.id,
                old_email=self.user.email,
                new_email=change.new_email,
            ).to_dict(),
        )
        await send_about_email_change_notice_email(
            user=self.user,
            new_email=change.new_email,
            old_email=change.old_email,
        )

        await send_user_to_crm(user_id=self.user.id)

    async def cancel_pending_changes(self) -> None:
        """
        Cancel all pending email changes for the user.
        """
        logger.info('Cancelling user email change', extra={'user_id': self.user.id})
        await cancel_pending_user_email_changes(self.conn, user_id=self.user.id)
        if user_phone := self.user.phone:
            await reset_phone_verification_otp(
                user_id=self.user.id, email=self.user.email, phone=user_phone
            )

    async def save_user_email_change_action(self, *, new_email: str) -> None:
        """
        Save user action about email change for all companies where the user has active roles.
        """
        companies = await select_user_companies_ids(self.conn, user_id=self.user.id)

        actions = [
            user_actions.UserAction(
                action=user_actions.Action.user_email_update,
                source=user_actions.Source.internal,
                user_id=self.user.id,
                phone=self.user.auth_phone,
                email=self.user.email,  # old email
                company_id=company_id,
                extra={'new_email': new_email},
            )
            for company_id in companies
        ]
        await user_actions.insert_user_actions(user_actions=actions)

    async def schedule_update_contacts(self, change: UserEmailChange) -> None:
        """
        Schedule a job that will update email in all contacts across the system
        """

        if not change.old_email:
            return

        await services.kafka.send_record(
            topic=topics.USER_EMAIL_CHANGE_UPDATE_CONTACTS,
            value=UserEmailChangeUpdateContactsCtx(
                user_id=change.user_id,
                old_email=change.old_email,
                new_email=change.new_email,
            ).to_dict(),
        )
        await update_user_email_change(
            conn=self.conn,
            change_id=change.id,
            data={'date_contacts_synced': utc_now()},
        )

    async def check_contacts_exists(self) -> bool:
        change = await select_latest_user_email_changes(self.conn, user_id=self.user.id)
        if not change or not change.old_email:
            return False

        return await exists_email_in_recipients_contacts(
            conn=self.conn,
            email=change.old_email,
            user_id=change.user_id,
        )
