from enum import Enum
from typing import Any

from hiku.expr.core import define
from hiku.graph import Nothing
from hiku.result import Proxy
from hiku.types import Any as HikuAny
from hiku.types import Optional as HikuOptional
from hiku.types import Record, RecordMeta

from api.graph.high_level.utils import is_input as is_input_func
from api.uploads.utils import unarchive_file_name
from app.auth import utils as auth
from app.auth.schemas import CompanyConfig
from app.comments.enums import CommentType
from app.documents.enums import DocumentSource, FirstSignBy
from app.documents.utils import get_document_status_text
from app.lib.enums import DocumentStatus, Language
from app.lib.helpers import safe_enum_value as base_enum_value
from app.lib.types import DataDict


@define(HikuAny)
def comment_is_rejection(type_: CommentType | None) -> bool:
    return type_ == CommentType.rejection


@define(HikuAny, HikuAny, HikuAny, HikuAny, HikuAny)
def document_display_company_edrpou(
    current_company_edrpou: str,
    current_user_email: str,
    owner_edrpou: str,
    recipient_edrpou: str | None,
    recipient_email: str | None,
) -> str | None:
    if is_input_func(
        current_company_edrpou,
        current_user_email,
        owner_edrpou,
        recipient_edrpou,
        recipient_email,
    ):
        return owner_edrpou
    return recipient_edrpou


@define(HikuAny, HikuAny, HikuAny, HikuAny, HikuAny, HikuAny)
def document_display_company_email(
    current_company_edrpou: str,
    current_user_email: str,
    owner_edrpou: str,
    owner_email: str,
    recipient_edrpou: str | None,
    recipient_email: str | None,
) -> str | None:
    if is_input_func(
        current_company_edrpou,
        current_user_email,
        owner_edrpou,
        recipient_edrpou,
        recipient_email,
    ):
        return owner_email
    return recipient_email


def _get_company_display_name(company: Proxy | None, contact: Proxy | None) -> str:
    try:
        if company and company['name']:
            return company['name']

        if contact:
            if contact['short_name']:
                return contact['short_name']
            if contact['name']:
                return contact['name']
    except KeyError:
        ...
    # Return empty line on fail and if or not contact or company
    return ''


@define(
    HikuAny,
    HikuAny,
    HikuAny,
    HikuAny,
    HikuAny,
    # Define which fields to fetch from provided Ref
    HikuOptional[Record[{'name': HikuAny}]],
    HikuOptional[Record[{'name': HikuAny}]],
    HikuOptional[Record[{'name': HikuAny, 'short_name': HikuAny}]],
    HikuOptional[Record[{'name': HikuAny, 'short_name': HikuAny}]],
)
def document_display_company_name(
    current_company_edrpou: str,
    current_user_email: str,
    owner_edrpou: str,
    recipient_edrpou: str | None,
    recipient_email: str | None,
    company_owner: Proxy | None,
    company_recipient: Proxy | None,
    contact_recipient: Proxy | None,
    contact_owner: Proxy | None,
) -> str | None:
    # TODO[KG]: After update Hiku v4.* `is not None` check should be replaced
    # with original `if_some`, already fixed for PY36
    if is_input_func(
        current_company_edrpou,
        current_user_email,
        owner_edrpou,
        recipient_edrpou,
        recipient_email,
    ):
        return _get_company_display_name(company_owner, contact_owner)
    return _get_company_display_name(company_recipient, contact_recipient)


@define(HikuAny)
def document_is_imported(source: DocumentSource | None) -> bool | None:
    return not source.is_internal if source else None


@define(HikuAny, HikuAny, HikuAny, HikuAny, HikuAny)
def document_is_input(
    current_company_edrpou: str,
    current_user_email: str,
    owner_edrpou: str,
    recipient_edrpou: str | None,
    recipient_email: str | None,
) -> bool:
    return is_input_func(
        current_company_edrpou,
        current_user_email,
        owner_edrpou,
        recipient_edrpou,
        recipient_email,
    )


@define(
    HikuAny,
    HikuAny,
    HikuAny,
    HikuAny,
    HikuAny,
    HikuAny,
    HikuAny,
    HikuAny,
)
def document_status_text(
    is_internal: bool,
    is_multilateral: bool,
    expected_owner_signatures: int,
    expected_recipient_signatures: int,
    edrpou_owner: str,
    status_id: int,
    first_sign_by: FirstSignBy,
    current_company_edrpou: str,
) -> str:
    is_owner = edrpou_owner == current_company_edrpou
    status_text = get_document_status_text(
        is_internal=is_internal,
        is_multilateral=is_multilateral,
        expected_owner_signatures=expected_owner_signatures,
        expected_recipient_signatures=expected_recipient_signatures,
        is_owner=is_owner,
        status=DocumentStatus(status_id),
        first_sign_by=first_sign_by,
    )
    return status_text.value


@define(HikuAny, HikuAny, HikuAny, HikuAny)
def document_is_one_sign(
    first_sign_by: FirstSignBy,
    is_internal: bool,
    expected_owner_signatures: int,
    expected_recipient_signatures: int,
) -> bool:
    if is_internal:
        return False
    if first_sign_by == FirstSignBy.owner:
        return expected_recipient_signatures == 0
    return expected_owner_signatures == 0


@define(HikuAny)
def enum_value(native_enum: Enum | None) -> str | None:
    return base_enum_value(native_enum)


@define(HikuAny)
def is_admin(user_role: int | None) -> bool:
    return auth.is_admin(user_role)


@define(HikuAny, HikuAny, HikuAny)
def is_master_admin(
    user_role: int | None, edrpou: str | None, raw_company_config: DataDict
) -> bool:
    # raw_company_config - company config_dict, prepared in
    # api.graph.low_level.app_resolvers.resolve_company_config_from_role_node
    company_config = CompanyConfig(**raw_company_config)
    return auth.is_master_admin(user_role=user_role, edrpou=edrpou, company_config=company_config)


@define(HikuAny)
def registration_referral_url(role_id: str) -> str:
    return f'/auth/registration?ref={role_id}'


@define(HikuAny, HikuAny)
def sign_session_url(sign_session_id: str, type_: str) -> str:
    return f'/sign-sessions/{sign_session_id}/{type_}'


@define(HikuAny)
def unarchive_document_extension(extension: str | None) -> str | None:
    if not extension:
        return extension
    return unarchive_file_name(extension)


@define(HikuAny, HikuAny, HikuAny, HikuAny, HikuAny)
def delete_request_is_input(
    current_role_email: str,
    recipients_emails: list[str],
    current_edrpou: str,
    receiver_edrpou: str,
    is_admin_user: bool,
) -> bool:
    return (current_role_email in recipients_emails and current_edrpou == receiver_edrpou) or (
        current_edrpou == receiver_edrpou and is_admin_user
    )


@define(HikuAny, HikuAny)
def is_flow_complete(pending_signatures_count: int, meta: DataDict) -> bool:
    return pending_signatures_count == 0 and not meta.get('unfinished')


@define(HikuAny, HikuAny, HikuAny, HikuAny, HikuAny, HikuAny)
def is_flow_can_be_signed(
    meta: dict[str, list[str]],
    pending_signatures_count: int,
    edrpou: str,
    current_role: str,
    current_role_email: str,
    current_edrpou: str,
) -> bool:
    """
    Get boolean status about ability to sign document flow.
    Note: that function doesn't validate access for document, because
    only user with document access can view `DocumentFlow` node and this status
    """
    if not current_role_email:
        return False

    if not bool(pending_signatures_count):
        return False

    if current_edrpou != edrpou:
        return False

    if current_role in meta.get('role_ids', []):
        return False

    emails = [email.lower() for email in meta.get('emails', [])]
    if current_role_email.lower() in emails:
        return False

    return True


@define(HikuAny, HikuAny, HikuAny)
def get_banner_content_field(content: list[DataDict] | None, language: str, field: str) -> Any:
    if not content:
        return None
    lang = Language(language)
    value: DataDict = next((item for item in content if item['language'] == lang), {})
    return value.get(field)


@define(RecordMeta)
def format_flow_receiver(receiver: DataDict) -> DataDict:
    if receiver is Nothing:
        return {}
    return {
        **receiver,
        'emails': None if receiver['is_emails_hidden'] else receiver['emails'],
    }


@define(HikuAny)
def is_not_none(field: Any) -> bool:
    return field is not None


@define(HikuAny)
def is_fop(edrpou: str) -> bool:
    return auth.is_fop(edrpou)


@define(HikuAny, HikuAny, HikuAny, HikuAny, HikuAny)
def is_user_registered(
    is_logged_once: bool,
    email: str | None,
    is_email_confirmed: bool,
    auth_phone: str | None,
    is_registration_completed: bool,
) -> bool:
    return auth.is_user_registered(
        is_logged_once=is_logged_once,
        email=email,
        is_email_confirmed=is_email_confirmed,
        auth_phone=auth_phone,
        is_registration_completed=is_registration_completed,
    )
