from http import HTTPStatus

from app.auth.types import User
from app.document_versions.types import VersionUploadCtx
from app.document_versions.utils import add_new_upload_document_version
from app.lib.enums import Source
from app.services import services
from app.tests.common import (
    datetime_test,
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_review,
    prepare_user_data,
    request_document_update,
    send_document,
    with_elastic,
)


async def test_wait_my_sign_and_wait_my_review_with_versioned_review(aiohttp_client):
    """
    Check that we properly filter documents by the `isWaitMySign` flag when reqview is required
    and one of the document versions is unsent

    https://vchasno-group.atlassian.net/browse/DOC-7390
    """

    edrpou1 = '********'
    edrpou2 = '********'

    app, client, owner = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=edrpou1,
        create_billing_account=True,
    )
    coworker1 = await prepare_user_data(app, company_edrpou=edrpou1, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, company_edrpou=edrpou1, email='<EMAIL>')

    recipient1 = await prepare_user_data(app, company_edrpou=edrpou2, email='<EMAIL>')
    recipient2 = await prepare_user_data(app, company_edrpou=edrpou2, email='<EMAIL>')
    recipient3 = await prepare_user_data(app, company_edrpou=edrpou2, email='<EMAIL>')

    document = await prepare_document_data(app=app, owner=owner)

    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients=[recipient1, owner],
        signers=[coworker1],
        reviewers=[coworker2],
        reviews_is_required=True,
        version_settings={'is_versioned': True},
    )
    assert response.status == HTTPStatus.OK

    await send_document(client, document_id=document.id, sender=owner)

    async with services.db.acquire() as conn:
        await add_new_upload_document_version(
            conn=conn,
            validated=VersionUploadCtx(
                document=document,
                content=b'Second version content',
                extension='.pdf',
                version_count=2,
                date_created=datetime_test('2021-01-01T00:00:00Z'),
            ),
            user=User.from_row(owner),
            source=Source.api_internal,
        )

    response = await request_document_update(
        client=client,
        user=recipient1,
        document=document,
        signers=[recipient2],
        reviewers=[recipient3],
        reviews_is_required=True,
    )
    assert response.status == HTTPStatus.OK

    async def _is_wait_my_sign(_user):
        result = await fetch_graphql(
            client=client,
            query='{ allDocuments(isWaitMySign: true) { documents { id }  } }',
            headers=prepare_auth_headers(_user),
        )
        _documents = result['allDocuments']['documents']
        if _documents:
            assert len(_documents) == 1
            assert _documents[0]['id'] == document.id
        return bool(_documents)

    async def _is_wait_my_review(_user):
        result = await fetch_graphql(
            client=client,
            query='{ allDocuments(reviewFolder: "wait_my_review") { documents { id }  } }',
            headers=prepare_auth_headers(_user),
        )
        _documents = result['allDocuments']['documents']
        if _documents:
            assert len(_documents) == 1
            assert _documents[0]['id'] == document.id

        return bool(_documents)

    async with with_elastic(app, [document.id]):
        # no one is expected to sign because on both sides a review is required
        assert await _is_wait_my_sign(owner) is False
        assert await _is_wait_my_sign(coworker1) is False
        assert await _is_wait_my_sign(coworker2) is False
        assert await _is_wait_my_sign(recipient1) is False
        assert await _is_wait_my_sign(recipient2) is False
        assert await _is_wait_my_sign(recipient3) is False

        # coworker2 and recipient3 are expected to review
        assert await _is_wait_my_review(owner) is False
        assert await _is_wait_my_review(coworker1) is False
        assert await _is_wait_my_review(coworker2) is True  # requested review
        assert await _is_wait_my_review(recipient1) is False
        assert await _is_wait_my_review(recipient2) is False
        assert await _is_wait_my_review(recipient3) is True  # requested review

    await prepare_review(
        client=client,
        document=document,
        user=recipient3,
        review_type='approve',
    )

    async with with_elastic(app, [document.id]):
        assert await _is_wait_my_sign(owner) is False
        assert await _is_wait_my_sign(coworker1) is False
        assert await _is_wait_my_sign(coworker2) is False
        assert await _is_wait_my_sign(recipient1) is False
        assert await _is_wait_my_sign(recipient2) is True  # signing started after review
        assert await _is_wait_my_sign(recipient3) is False

        assert await _is_wait_my_review(owner) is False
        assert await _is_wait_my_review(coworker1) is False
        assert await _is_wait_my_review(coworker2) is True  # is still waiting to review
        assert await _is_wait_my_review(recipient1) is False
        assert await _is_wait_my_review(recipient2) is False
        assert await _is_wait_my_review(recipient3) is False  # finished review

    await prepare_review(
        client=client,
        document=document,
        user=coworker2,
        review_type='approve',
    )

    async with with_elastic(app, [document.id]):
        assert await _is_wait_my_sign(owner) is False
        assert await _is_wait_my_sign(coworker1) is False  # waiting the recipient to sign
        assert await _is_wait_my_sign(coworker2) is False
        assert await _is_wait_my_sign(recipient1) is False
        assert await _is_wait_my_sign(recipient2) is True  # signing started after review

        assert await _is_wait_my_review(owner) is False
        assert await _is_wait_my_review(coworker1) is False
        assert await _is_wait_my_review(coworker2) is False  # finished review
        assert await _is_wait_my_review(recipient1) is False
        assert await _is_wait_my_review(recipient2) is False
        assert await _is_wait_my_review(recipient3) is False  # finished review
