from hiku.graph import (
    <PERSON>,
    Graph,
    Link,
    Node,
)
from hiku.types import (
    <PERSON>olean,
    Integer,
    Optional,
    Record,
    Sequence,
    String,
    TypeRef,
)

from api.graph.data import (
    BANNER_SRC,
    BILLING_ACCOUNT_SRC,
    BILLING_TRANSACTION_SRC,
    BONUS_SRC,
    CLOUD_SIGNER_SRC,
    COMMENT_SRC,
    COMPANY_SRC,
    CONTACT_PERSON_PHONE_SRC,
    CONTACT_PERSON_SRC,
    CONTACT_SRC,
    CSAT_SURVEY_SRC,
    DELETE_REQUEST_SRC,
    DIRECTORY_SRC,
    DOCUMENT_ANTIVIRUS_CHECK_SRC,
    DOCUMENT_AUTOMATION_CONDITION_SRC,
    DOCUMENT_AUTOMATION_TEMPLATE_SRC,
    DOCUMENT_CATEGORY_SRC,
    DOCUMENT_FLOW_SRC,
    DOCUMENT_META_SRC,
    DOCUMENT_PARAMETERS_SRC,
    DOCUMENT_RECIPIENT_SRC,
    DOCUMENT_REQUIRED_FIELD_SRC,
    DOCUMENT_REVOKE_SIGNATURE_SRC,
    DOCUMENT_REVOKE_SRC,
    DOCUMENT_SIGNER_SRC,
    DOCUMENT_SRC,
    DOCUMENT_VERSION_SRC,
    DOCUMENTS_FIELD_SRC,
    DRAFT_ANTIVIRUS_CHECK_SRC,
    DRAFT_SRC,
    GROUP_DOCUMENT_ACCESS_SRC,
    GROUP_MEMBER_SRC,
    GROUP_SRC,
    RATE_EXTENSION_SRC,
    REVIEW_REQUEST_SRC,
    REVIEW_SETTING_SRC,
    REVIEW_SRC,
    ROLE_SRC,
    SIGN_SESSION_SRC,
    SIGNATURE_SRC,
    TAG_SRC,
    TEMPLATE_SRC,
    TRIGGER_NOTIFICATION_SRC,
    USER_META_SRC,
    USER_SRC,
)
from api.graph.low_level import (
    app_resolvers,
    resolvers,
)

ROLE_FIELDS = [
    Field('id', String, ROLE_SRC),
    Field('company_id', String, ROLE_SRC),
    Field('user_id', String, ROLE_SRC),
    Field('user_role', String, ROLE_SRC),
    Field('can_view_document', Boolean, ROLE_SRC),
    Field('can_comment_document', Boolean, ROLE_SRC),
    Field('can_upload_document', Boolean, ROLE_SRC),
    Field('can_download_document', Boolean, ROLE_SRC),
    Field('can_print_document', Boolean, ROLE_SRC),
    Field('can_delete_document', Boolean, ROLE_SRC),
    Field('can_sign_and_reject_document', Boolean, ROLE_SRC),
    Field('can_sign_and_reject_document_external', Boolean, ROLE_SRC),
    Field('can_sign_and_reject_document_internal', Boolean, ROLE_SRC),
    Field('can_invite_coworkers', Boolean, ROLE_SRC),
    Field('can_edit_company', Boolean, ROLE_SRC),
    Field('can_edit_roles', Boolean, ROLE_SRC),
    Field('can_create_tags', Boolean, ROLE_SRC),
    Field('can_edit_document_automation', Boolean, ROLE_SRC),
    Field('can_edit_document_fields', Boolean, ROLE_SRC),
    Field('can_edit_document_category', Boolean, ROLE_SRC),
    Field('can_extract_document_structured_data', Boolean, ROLE_SRC),
    Field('can_edit_document_structured_data', Boolean, ROLE_SRC),
    Field('can_archive_documents', Boolean, ROLE_SRC),
    Field('can_edit_templates', Boolean, ROLE_SRC),
    Field('can_edit_directories', Boolean, ROLE_SRC),
    Field('can_remove_itself_from_approval', Boolean, ROLE_SRC),
    Field('can_delete_archived_documents', Boolean, ROLE_SRC),
    Field('can_change_document_signers_and_reviewers', Boolean, ROLE_SRC),
    Field('can_delete_document_extended', Boolean, ROLE_SRC),
    Field('can_download_actions', Boolean, ROLE_SRC),
    Field('can_edit_company_contact', Boolean, ROLE_SRC),
    Field('can_edit_required_fields', Boolean, ROLE_SRC),
    Field('can_edit_security', Boolean, ROLE_SRC),
    Field('can_view_private_document', Boolean, ROLE_SRC),
    Field('can_receive_inbox', Boolean, ROLE_SRC),
    Field('can_receive_inbox_as_default', Boolean, ROLE_SRC),
    Field('can_receive_comments', Boolean, ROLE_SRC),
    Field('can_receive_rejects', Boolean, ROLE_SRC),
    Field('can_receive_reminders', Boolean, ROLE_SRC),
    Field('can_receive_reviews', Boolean, ROLE_SRC),
    Field('can_receive_review_process_finished', Boolean, ROLE_SRC),
    Field('can_receive_review_process_finished_assigner', Boolean, ROLE_SRC),
    Field('can_receive_sign_process_finished', Boolean, ROLE_SRC),
    Field('can_receive_sign_process_finished_assigner', Boolean, ROLE_SRC),
    Field('can_receive_notifications', Boolean, ROLE_SRC),
    Field('can_receive_access_to_doc', Boolean, ROLE_SRC),
    Field('can_receive_delete_requests', Boolean, ROLE_SRC),
    Field('can_receive_finished_docs', Boolean, ROLE_SRC),
    Field('can_receive_new_roles', Boolean, ROLE_SRC),
    Field('can_receive_token_expiration', Boolean, ROLE_SRC),
    Field('can_receive_email_change', Boolean, ROLE_SRC),
    Field('can_receive_admin_role_deletion', Boolean, ROLE_SRC),
    Field('can_view_coworkers', Boolean, ROLE_SRC),
    Field('sort_documents', String, ROLE_SRC),
    Field('show_invite_tooltip', Boolean, ROLE_SRC),
    Field('show_child_documents', Boolean, ROLE_SRC),
    Field('position', Optional[String], ROLE_SRC),
    Field('has_signed_documents', Boolean, ROLE_SRC),
    Field('status', String, ROLE_SRC),
    Field('allowed_ips', Sequence[String], ROLE_SRC),
    Field('allowed_api_ips', Sequence[String], ROLE_SRC),
    Field('date_created', String, ROLE_SRC),
    Field('date_updated', String, ROLE_SRC),
    Field('date_deleted', Optional[String], ROLE_SRC),
    Field('deleted_by', Optional[String], ROLE_SRC),
    Field('invited_by', Optional[String], ROLE_SRC),
    Field('activated_by', Optional[String], ROLE_SRC),
    Field('date_agreed', String, ROLE_SRC),
    Field('date_invited', Optional[String], ROLE_SRC),
    Field('date_activated', Optional[String], ROLE_SRC),
    Field('activation_source', Optional[String], ROLE_SRC),
    Field('is_default_recipient', Boolean, ROLE_SRC),
    Field('has_few_signatures', Boolean, ROLE_SRC),
    Field('has_few_reviews', Boolean, ROLE_SRC),
    Field('has_hrs_role', Boolean, ROLE_SRC),
    Field('company_edrpou', String, app_resolvers.resolve_company_edrpou_from_role_node),
    Field('company_config', Record, app_resolvers.resolve_company_config_from_role_node),
    Field(
        'can_view_client_data',
        Boolean,
        app_resolvers.resolve_super_admin_permissions,
    ),
    Field(
        'can_edit_client_data',
        Boolean,
        app_resolvers.resolve_super_admin_permissions,
    ),
    Field(
        'can_edit_special_features',
        Boolean,
        app_resolvers.resolve_super_admin_permissions,
    ),
]


LOW_LEVEL_GRAPH = Graph(
    [
        # Auth models
        Node(
            'Company',
            [
                Field('id', String, COMPANY_SRC),
                Field('edrpou', String, COMPANY_SRC),
                Field('ipn', String, COMPANY_SRC),
                Field('name', Optional[String], COMPANY_SRC),
                Field('full_name', Optional[String], COMPANY_SRC),
                Field('is_legal', Boolean, COMPANY_SRC),
                Field('is_dealer', Boolean, COMPANY_SRC),
                Field('render_signature_in_interface', Boolean, COMPANY_SRC),
                Field('render_signature_on_print_document', Boolean, COMPANY_SRC),
                Field(
                    'allow_unregistered_document_view',
                    Boolean,
                    app_resolvers.resolve_unregistered_document_view,
                ),
                Field('activity_field', Optional[String], COMPANY_SRC),
                Field('employees_number', Optional[String], COMPANY_SRC),
                Field('phone', Optional[String], COMPANY_SRC),
                Field('date_created', String, COMPANY_SRC),
                Field('date_updated', String, COMPANY_SRC),
                Field('email_domains', Sequence[String], COMPANY_SRC),
                Field('allowed_ips', Sequence[String], COMPANY_SRC),
                Field('allowed_api_ips', Sequence[String], COMPANY_SRC),
                Field('inactivity_timeout', Optional[Integer], COMPANY_SRC),
            ],
        ),
        Node(
            'Review',
            [
                Field('id', String, REVIEW_SRC),
                Field('document_id', String, REVIEW_SRC),
                Field('role_id', String, REVIEW_SRC),
                Field('group_id', Optional[String], REVIEW_SRC),
                Field('document_version_id', String, REVIEW_SRC),
                Field('type', String, REVIEW_SRC),
                Field('user_email', Optional[String], REVIEW_SRC),
                Field('date_created', String, REVIEW_SRC),
            ],
        ),
        Node(
            'ReviewRequest',
            [
                Field('id', String, REVIEW_REQUEST_SRC),
                Field('document_id', String, REVIEW_REQUEST_SRC),
                Field('document_version_id', Optional[String], REVIEW_REQUEST_SRC),
                Field('from_role_id', String, REVIEW_REQUEST_SRC),
                Field('to_role_id', String, REVIEW_REQUEST_SRC),
                Field('to_group_id', Optional[String], REVIEW_REQUEST_SRC),
                Field('status', String, REVIEW_REQUEST_SRC),
                Field('order', Optional[Integer], REVIEW_REQUEST_SRC),
                Field('date_created', String, REVIEW_REQUEST_SRC),
                Field('date_updated', String, REVIEW_REQUEST_SRC),
            ],
        ),
        Node(
            'ReviewSetting',
            [
                Field('id', String, REVIEW_SETTING_SRC),
                Field('document_id', String, REVIEW_SETTING_SRC),
                Field('company_id', String, REVIEW_SETTING_SRC),
                Field('is_required', Boolean, REVIEW_SETTING_SRC),
                Field('is_parallel', Optional[Boolean], REVIEW_SETTING_SRC),
                Field('date_created', String, REVIEW_SETTING_SRC),
                Field('date_updated', String, REVIEW_SETTING_SRC),
            ],
        ),
        Node('CoworkerRole', [*ROLE_FIELDS]),
        Node(
            'Role',
            [
                *ROLE_FIELDS,
                Field('hasToken', Boolean, app_resolvers.resolve_has_token),
                Link(
                    'company',
                    TypeRef['Company'],
                    resolvers.direct_link,
                    requires='company_id',
                ),
                # TODO: DOC-5280: Remove after FE update. This is for backward-compatibility only
                Field(
                    'is_super_admin',
                    Boolean,
                    app_resolvers.resolve_is_super_admin,
                ),
            ],
        ),
        Node(
            'User',
            [
                Field('id', String, USER_SRC),
                Field('email', Optional[String], USER_SRC),
                Field('phone', Optional[String], USER_SRC),
                Field('auth_phone', Optional[String], USER_SRC),
                Field('first_name', Optional[String], USER_SRC),
                Field('second_name', Optional[String], USER_SRC),
                Field('last_name', Optional[String], USER_SRC),
                Field('is_logged_once', Boolean, USER_SRC),
                Field('email_confirmed', Boolean, USER_SRC),
                Field('registration_completed', Boolean, USER_SRC),
                Field('is_autogenerated_password', Boolean, USER_SRC),
                Field('is_phone_verified', Boolean, USER_SRC),
                Field('is_2fa_enabled', Boolean, USER_SRC),
                Field('is_subscribed_esputnik', Boolean, USER_SRC),
                Field('trial_auto_enabled', Boolean, USER_SRC),
                Field('registration_method', String, USER_SRC),
                Field('created_by', String, USER_SRC),
                Field('date_created', String, USER_SRC),
                Field('date_updated', String, USER_SRC),
                Field('source', Optional[String], USER_SRC),
                Field('language', Optional[String], USER_SRC),
                Field('password', Optional[String], USER_SRC),
            ],
        ),
        Node(
            'UserOnboarding',
            [
                Field('user_id', String, resolvers.get_attrib_fields),
                Field('has_checked_companies', Boolean, resolvers.get_attrib_fields),
                Field('has_invited_recipient', Boolean, resolvers.get_attrib_fields),
                Field('has_invited_coworker', Boolean, resolvers.get_attrib_fields),
                Field('has_uploaded_document', Boolean, resolvers.get_attrib_fields),
                Field('has_seen_new_uploading', Boolean, resolvers.get_attrib_fields),
                Field('is_skipped', Boolean, resolvers.get_attrib_fields),
                Field('extra', String, resolvers.get_attrib_fields),
            ],
        ),
        # Groups
        Node(
            'Group',
            [
                Field('id', String, GROUP_SRC),
                Field('name', String, GROUP_SRC),
                Field('date_created', String, GROUP_SRC),
                Field('created_by', String, GROUP_SRC),
            ],
        ),
        Node(
            'GroupMember',
            [
                Field('id', String, GROUP_MEMBER_SRC),
                Field('group_id', String, GROUP_MEMBER_SRC),
                Field('role_id', String, GROUP_MEMBER_SRC),
                Field('date_created', String, GROUP_MEMBER_SRC),
                Field('created_by', String, GROUP_MEMBER_SRC),
            ],
        ),
        Node(
            'GroupDocumentAccess',
            [
                Field('id', String, GROUP_DOCUMENT_ACCESS_SRC),
                Field('group_id', String, GROUP_DOCUMENT_ACCESS_SRC),
                Field('document_id', String, GROUP_DOCUMENT_ACCESS_SRC),
                Field('created_by', String, GROUP_DOCUMENT_ACCESS_SRC),
                Field('date_created', String, GROUP_DOCUMENT_ACCESS_SRC),
            ],
        ),
        Node(
            'UserMeta',
            [
                Field('user_id', String, USER_META_SRC),
                Field('mobile_usage', Boolean, USER_META_SRC),
            ],
        ),
        # Billing models
        Node(
            'BillingAccount',
            [
                Field('id', String, BILLING_ACCOUNT_SRC),
                Field('company_id', String, BILLING_ACCOUNT_SRC),
                Field('initiator_id', String, BILLING_ACCOUNT_SRC),
                Field('type', String, BILLING_ACCOUNT_SRC),
                Field('rate', String, BILLING_ACCOUNT_SRC),
                Field('status', String, BILLING_ACCOUNT_SRC),
                Field('amount', Integer, BILLING_ACCOUNT_SRC),
                Field('amount_left', Integer, BILLING_ACCOUNT_SRC),
                Field('source', String, BILLING_ACCOUNT_SRC),
                Field('units', Integer, BILLING_ACCOUNT_SRC),
                Field('units_left', Integer, BILLING_ACCOUNT_SRC),
                Field('date_created', String, BILLING_ACCOUNT_SRC),
                Field('date_expired', String, BILLING_ACCOUNT_SRC),
                Field('date_deleted', String, BILLING_ACCOUNT_SRC),
                Field('price_per_user', String, BILLING_ACCOUNT_SRC),
            ],
        ),
        Node(
            'BillingCompanyConfig',
            [
                Field(
                    name='max_archive_documents_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_additional_fields_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_employees_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_documents_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_tags_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_automation_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_required_fields_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_versions_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='api_enabled',
                    type_=Boolean,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='can_enforce_2fa',
                    type_=Boolean,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='external_comments_enabled',
                    type_=Boolean,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='internal_comments_enabled',
                    type_=Boolean,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='internal_document_enabled',
                    type_=Boolean,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='can_manage_employee_access',
                    type_=Boolean,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='reviews_enabled',
                    type_=Boolean,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='max_visible_documents_count',
                    type_=Optional[Integer],
                    func=resolvers.get_attrib_fields,
                ),
            ],
        ),
        Node(
            'CSATSurvey',
            [
                Field('id', String, CSAT_SURVEY_SRC),
                Field('type', String, CSAT_SURVEY_SRC),
                Field('estimate', Optional[Integer], CSAT_SURVEY_SRC),
                Field('date_created', String, CSAT_SURVEY_SRC),
            ],
        ),
        Node(
            'LoginSession',
            [
                Field('id', String, resolvers.get_attrib_fields),
                Field('accessed_at', String, resolvers.get_attrib_fields),
                Field('ip', Optional[String], resolvers.get_attrib_fields),
                Field('browser', String, resolvers.get_attrib_fields),
                Field('browser_version', Optional[String], resolvers.get_attrib_fields),
                Field('os', String, resolvers.get_attrib_fields),
                Field('os_version', Optional[String], resolvers.get_attrib_fields),
                Field('device', String, resolvers.get_attrib_fields),
                Field('country', Optional[String], resolvers.get_attrib_fields),
                Field('city', Optional[String], resolvers.get_attrib_fields),
                Field('is_current', Boolean, resolvers.get_attrib_fields),
            ],
        ),
        Node(
            'Bill',
            [
                Field('id', String, resolvers.get_attrib_fields),
                Field('seqnum', String, resolvers.get_attrib_fields),
                Field('email', String, resolvers.get_attrib_fields),
                Field('source', String, resolvers.get_attrib_fields),
                Field('document_id', String, resolvers.get_attrib_fields),
                Field('company_id', String, resolvers.get_attrib_fields),
                Field('status_id', String, resolvers.get_attrib_fields),
                Field('date_created', String, resolvers.get_attrib_fields),
                Field('payment_status', String, resolvers.get_attrib_fields),
                Field('services_type', String, resolvers.get_attrib_fields),
            ],
        ),
        Node(
            name='BillService',
            fields=[
                # Common fields
                Field('type', String, resolvers.get_attrib_fields),
                Field('units', Integer, resolvers.get_attrib_fields),
                Field('unit_price', Integer, resolvers.get_attrib_fields),
                # Different fields for different services:
                #  - BillServiceRate: rate, date_from, limits_employees_count
                #  - BillServiceExtension: extension, date_from
                #  - BillServiceUnits: (no additional fields)
                Field('rate', Optional[String], resolvers.get_attrib_fields_optional),
                Field('date_from', Optional[String], resolvers.get_attrib_fields_optional),
                Field(
                    'limits_employees_count',
                    Optional[Integer],
                    resolvers.get_attrib_fields_optional,
                ),
                Field('extension', Optional[String], resolvers.get_attrib_fields_optional),
            ],
        ),
        Node(
            'BillingTransaction',
            [
                Field('id', String, BILLING_TRANSACTION_SRC),
                Field('from_', String, BILLING_TRANSACTION_SRC),
                Field('to_', String, BILLING_TRANSACTION_SRC),
                Field('operator_id', String, BILLING_TRANSACTION_SRC),
                Field('initiator_id', String, BILLING_TRANSACTION_SRC),
                Field('type', String, BILLING_TRANSACTION_SRC),
                Field('amount', Integer, BILLING_TRANSACTION_SRC),
                Field('units', Integer, BILLING_TRANSACTION_SRC),
                Field('comment', String, BILLING_TRANSACTION_SRC),
                Field('date_created', String, BILLING_TRANSACTION_SRC),
            ],
        ),
        Node(
            'Bonus',
            [
                Field('id', String, BONUS_SRC),
                Field('created_by', String, BONUS_SRC),
                Field('key', String, BONUS_SRC),
                Field('title', String, BONUS_SRC),
                Field('description', String, BONUS_SRC),
                Field('type', String, BONUS_SRC),
                Field('units', Integer, BONUS_SRC),
                Field('period', Integer, BONUS_SRC),
                Field('date_created', String, BONUS_SRC),
                Field('date_expired', String, BONUS_SRC),
                Field('date_deleted', String, BONUS_SRC),
            ],
        ),
        Node(
            'CompanyRate',
            [
                Field('id', String, BILLING_ACCOUNT_SRC),
                Field('rate', String, BILLING_ACCOUNT_SRC),
                Field('status', String, BILLING_ACCOUNT_SRC),
                Field('activation_date', String, BILLING_ACCOUNT_SRC),
                Field('date_expired', String, BILLING_ACCOUNT_SRC),
                Field('amount', Integer, BILLING_ACCOUNT_SRC),
                Field('source', String, BILLING_ACCOUNT_SRC),
            ],
        ),
        Node(
            'RateExtension',
            [
                Field('id', String, RATE_EXTENSION_SRC),
                Field('type', String, RATE_EXTENSION_SRC),
                Field('status', String, RATE_EXTENSION_SRC),
                Field('bill_id', String, RATE_EXTENSION_SRC),
                Field('date_expiring', Optional[String], RATE_EXTENSION_SRC),
            ],
        ),
        # Documents models
        Node(
            'Document',
            [
                Field('id', String, DOCUMENT_SRC),
                Field('seqnum', Integer, DOCUMENT_SRC),
                Field('user_id', String, DOCUMENT_SRC),
                Field('uploaded_by', String, DOCUMENT_SRC),
                Field('edrpou_owner', String, DOCUMENT_SRC),
                # Document owner role_id
                Field('role_id', String, DOCUMENT_SRC),
                # Must be deprecated for document recipients usage
                Field(
                    'edrpou_recipient',
                    Optional[String],
                    app_resolvers.resolve_document_recipient_fields,
                ),
                Field(
                    'email_recipient',
                    Optional[String],
                    app_resolvers.resolve_document_recipient_fields,
                ),
                Field(
                    'is_recipient_email_hidden',
                    Optional[String],
                    app_resolvers.resolve_document_recipient_fields,
                ),
                Field('title', String, DOCUMENT_SRC),
                Field('extension', String, DOCUMENT_SRC),
                Field('archive_name', Optional[String], DOCUMENT_SRC),
                Field('status_id', Integer, DOCUMENT_SRC),
                Field('date_document', Optional[String], DOCUMENT_SRC),
                Field('amount', Optional[Integer], DOCUMENT_SRC),
                Field('type', Optional[String], DOCUMENT_SRC),
                Field('category', Optional[Integer], DOCUMENT_SRC),
                Field('number', Optional[String], DOCUMENT_SRC),
                Field('source', String, DOCUMENT_SRC),
                Field('first_sign_by', String, DOCUMENT_SRC),
                Field('is_internal', Boolean, DOCUMENT_SRC),
                Field('is_multilateral', Boolean, DOCUMENT_SRC),
                Field('expected_owner_signatures', Integer, DOCUMENT_SRC),
                Field('expected_recipient_signatures', Integer, DOCUMENT_SRC),
                Field('s3_xml_to_pdf_key', Optional[String], DOCUMENT_SRC),
                Field('is_protected', Boolean, DOCUMENT_SRC),
                Field('date_created', String, DOCUMENT_SRC),
                Field('date_updated', String, DOCUMENT_SRC),
                Field('date_delivered', Optional[String], DOCUMENT_SRC),
                Field('date_finished', Optional[String], DOCUMENT_SRC),
                Field('signature_format', String, DOCUMENT_SRC),
                Field('is_invalid_signed', Optional[Boolean], DOCUMENT_SRC),
                Field(
                    'current_company_edrpou',
                    String,
                    app_resolvers.resolve_current_company_edrpou,
                ),
                Field(
                    'current_user_email',
                    String,
                    app_resolvers.resolve_current_user_email,
                ),
                Link(
                    'company_owner',
                    # This ref is Optional. Document's `edrpou_owner` may be missing in
                    # `companies` table for different reasons.
                    Optional[TypeRef['Company']],
                    app_resolvers.resolve_company_from_document_node,
                    requires='edrpou_owner',
                ),
                Link(
                    'company_recipient',
                    Optional[TypeRef['Company']],
                    app_resolvers.resolve_company_from_document_node,
                    requires='edrpou_recipient',
                ),
                Link('user', TypeRef['User'], resolvers.direct_link, requires='user_id'),
                Link(
                    'contact_recipient',
                    Optional[TypeRef['Contact']],
                    app_resolvers.resolve_contact_from_document_node,
                    requires='edrpou_recipient',
                ),
                Link(
                    'contact_owner',
                    Optional[TypeRef['Contact']],
                    app_resolvers.resolve_contact_from_document_node,
                    requires='edrpou_owner',
                ),
            ],
        ),
        Node(
            'Tag',
            [
                Field('id', String, TAG_SRC),
                Field('name', String, TAG_SRC),
                Field('date_created', String, TAG_SRC),
                Field('date_updated', String, TAG_SRC),
                Field('company_id', String, TAG_SRC),
                Field('can_assign', Boolean, app_resolvers.resolve_tag_access),
            ],
        ),
        Node(
            'DocumentSigner',
            [
                Field('id', String, DOCUMENT_SIGNER_SRC),
                Field('document_id', String, DOCUMENT_SIGNER_SRC),
                Field('company_id', String, DOCUMENT_SIGNER_SRC),
                Field('role_id', Optional[String], DOCUMENT_SIGNER_SRC),
                Field('group_id', Optional[String], DOCUMENT_SIGNER_SRC),
                Field('group_signer_id', Optional[String], DOCUMENT_SIGNER_SRC),
                Field('order', Optional[Integer], DOCUMENT_SIGNER_SRC),
                Field('assigner', Optional[String], DOCUMENT_SIGNER_SRC),
                Field('date_created', String, DOCUMENT_SIGNER_SRC),
                Field('date_signed', Optional[String], DOCUMENT_SIGNER_SRC),
            ],
        ),
        # Comments models
        Node(
            'Comment',
            [
                Field('id', String, COMMENT_SRC),
                Field('document_id', String, COMMENT_SRC),
                Field('document_version_id', String, COMMENT_SRC),
                Field('access_company_id', String, COMMENT_SRC),
                Field('role_id', String, COMMENT_SRC),
                Field('type', String, COMMENT_SRC),
                Field('text', String, COMMENT_SRC),
                Field('date_created', String, COMMENT_SRC),
                Field('date_edited', String, COMMENT_SRC),
                # TODO[ID]: To delete after `v9.0.0` deploy
                Field('user_id', String, COMMENT_SRC),
                Field('status_id', String, COMMENT_SRC),
            ],
        ),
        # Signatures models
        Node(
            'Signature',
            [
                Field('id', String, SIGNATURE_SRC),
                Field('document_id', String, SIGNATURE_SRC),
                Field('user_id', String, SIGNATURE_SRC),
                Field('user_email', Optional[String], SIGNATURE_SRC),
                Field('role_id', String, SIGNATURE_SRC),
                Field('is_internal', Optional[Boolean], SIGNATURE_SRC),
                Field('key_acsk', String, SIGNATURE_SRC),
                Field('key_timemark', String, SIGNATURE_SRC),
                Field('key_serial_number', String, SIGNATURE_SRC),
                Field('key_company_fullname', Optional[String], SIGNATURE_SRC),
                Field('key_owner_edrpou', String, SIGNATURE_SRC),
                Field('key_owner_fullname', String, SIGNATURE_SRC),
                Field('key_owner_position', Optional[String], SIGNATURE_SRC),
                Field('key_is_legal', Boolean, SIGNATURE_SRC),
                Field('stamp_acsk', Optional[String], SIGNATURE_SRC),
                Field('stamp_serial_number', Optional[String], SIGNATURE_SRC),
                Field('stamp_timemark', Optional[String], SIGNATURE_SRC),
                Field('stamp_company_fullname', Optional[String], SIGNATURE_SRC),
                Field('stamp_owner_edrpou', Optional[String], SIGNATURE_SRC),
                Field('stamp_owner_fullname', Optional[String], SIGNATURE_SRC),
                Field('stamp_owner_position', Optional[String], SIGNATURE_SRC),
                Field('stamp_is_legal', Optional[Boolean], SIGNATURE_SRC),
                Field('date_created', String, SIGNATURE_SRC),
                Field('is_valid', Boolean, SIGNATURE_SRC),
            ],
        ),
        # Contacts models
        Node(
            'Contact',
            [
                Field('id', String, CONTACT_SRC),
                Field('company_id', String, CONTACT_SRC),
                Field('name', Optional[String], CONTACT_SRC),
                Field('short_name', Optional[String], CONTACT_SRC),
                Field('edrpou', String, CONTACT_SRC),
                Field('date_created', String, CONTACT_SRC),
            ],
        ),
        Node(
            'ContactPerson',
            [
                Field('id', String, CONTACT_PERSON_SRC),
                Field('contact_id', String, CONTACT_PERSON_SRC),
                Field(
                    'email',
                    Optional[String],
                    app_resolvers.resolve_contact_person_email,
                ),
                Field('is_email_hidden', String, CONTACT_PERSON_SRC),
                Field('main_recipient', Boolean, CONTACT_PERSON_SRC),
                Field('first_name', Optional[String], CONTACT_PERSON_SRC),
                Field('second_name', Optional[String], CONTACT_PERSON_SRC),
                Field('last_name', Optional[String], CONTACT_PERSON_SRC),
            ],
        ),
        Node('ContactPersonPhone', [Field('phone', String, CONTACT_PERSON_PHONE_SRC)]),
        # Sessions models
        Node(
            'SignSession',
            [
                Field('id', String, SIGN_SESSION_SRC),
                Field('document_id', String, SIGN_SESSION_SRC),
                Field('role_id', Optional[String], SIGN_SESSION_SRC),
                Field('edrpou', Optional[String], SIGN_SESSION_SRC),
                Field('email', Optional[String], SIGN_SESSION_SRC),
                Field('is_legal', Optional[Boolean], SIGN_SESSION_SRC),
                Field('type', String, SIGN_SESSION_SRC),
                Field('status', String, SIGN_SESSION_SRC),
                Field('source', Optional[String], SIGN_SESSION_SRC),
                Field('document_status', String, SIGN_SESSION_SRC),
                Field('on_finish_url', String, SIGN_SESSION_SRC),
                Field('on_cancel_url', String, SIGN_SESSION_SRC),
                Field('sign_parameters', Record, SIGN_SESSION_SRC),
            ],
        ),
        # DeleteRequests models
        Node(
            'DeleteRequest',
            [
                Field('id', String, DELETE_REQUEST_SRC),
                Field('document_id', String, DELETE_REQUEST_SRC),
                Field('initiator_role_id', String, DELETE_REQUEST_SRC),
                Field('recipients_emails', Sequence[String], DELETE_REQUEST_SRC),
                Field('status', String, DELETE_REQUEST_SRC),
                Field('message', String, DELETE_REQUEST_SRC),
                Field('reject_message', String, DELETE_REQUEST_SRC),
                Field('receiver_edrpou', String, DELETE_REQUEST_SRC),
                Field('initiator_edrpou', String, DELETE_REQUEST_SRC),
                Field(
                    'current_role_email',
                    String,
                    app_resolvers.resolve_current_user_email,
                ),
                Field(
                    'current_edrpou',
                    String,
                    app_resolvers.resolve_current_company_edrpou,
                ),
                Field('is_admin_user', Boolean, app_resolvers.resolve_is_user_role_admin),
            ],
        ),
        Node(
            'DocumentFlow',
            [
                Field('id', String, DOCUMENT_FLOW_SRC),
                Field('company_id', String, DOCUMENT_FLOW_SRC),
                Field('edrpou', String, DOCUMENT_FLOW_SRC),
                Field('order', String, DOCUMENT_FLOW_SRC),
                Field('signatures_count', String, DOCUMENT_FLOW_SRC),
                Field('pending_signatures_count', String, DOCUMENT_FLOW_SRC),
                Field('meta', String, DOCUMENT_FLOW_SRC),
                Field('receivers_id', String, DOCUMENT_FLOW_SRC),
                Field('current_role', String, app_resolvers.resolve_current_roles),
                Field('date_sent', String, DOCUMENT_FLOW_SRC),
                Field(
                    'current_role_email',
                    String,
                    app_resolvers.resolve_current_user_email,
                ),
                Field(
                    'current_edrpou',
                    String,
                    app_resolvers.resolve_current_company_edrpou,
                ),
                Field('is_admin_user', Boolean, app_resolvers.resolve_is_user_role_admin),
                # Must be deprecated, better to use Link recipients
                Field('receivers', Record, app_resolvers.resolve_flow_receivers),
            ],
        ),
        Node(
            'DocumentRecipient',
            [
                Field('id', String, DOCUMENT_RECIPIENT_SRC),
                Field('edrpou', String, DOCUMENT_RECIPIENT_SRC),
                Field('document_id', String, DOCUMENT_RECIPIENT_SRC),
                Field(
                    'emails',
                    Sequence[String],
                    app_resolvers.resolve_document_recipients_emails,
                ),
                Field('is_emails_hidden', String, DOCUMENT_RECIPIENT_SRC),
                Field('date_sent', String, DOCUMENT_RECIPIENT_SRC),
                Field('date_received', String, DOCUMENT_RECIPIENT_SRC),
                Field('date_delivered', String, DOCUMENT_RECIPIENT_SRC),
            ],
        ),
        # TriggerNotification
        Node(
            'TriggerNotification',
            [
                Field('id', String, TRIGGER_NOTIFICATION_SRC),
                Field('url', Optional[String], TRIGGER_NOTIFICATION_SRC),
                Field('type', String, TRIGGER_NOTIFICATION_SRC),
                Field('status', String, TRIGGER_NOTIFICATION_SRC),
                Field('display_date', String, TRIGGER_NOTIFICATION_SRC),
                Field('context', Optional[Record], TRIGGER_NOTIFICATION_SRC),
            ],
        ),
        Node(
            'DocumentAutomationTemplate',
            [
                Field('id', String, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('name', String, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('assigned_to', String, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('is_active', Boolean, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('set_review', Boolean, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('review_settings', Record, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('set_signers', Boolean, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('signers_settings', Record, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('set_viewers', Boolean, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('viewers_settings', Record, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('set_fields', Boolean, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('fields_settings', Record, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('set_tags', Boolean, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
                Field('tags_settings', Record, DOCUMENT_AUTOMATION_TEMPLATE_SRC),
            ],
        ),
        Node(
            'DocumentAutomationCondition',
            [
                Field('id', String, DOCUMENT_AUTOMATION_CONDITION_SRC),
                Field('conditions', Record, DOCUMENT_AUTOMATION_CONDITION_SRC),
                Field('status', String, DOCUMENT_AUTOMATION_CONDITION_SRC),
            ],
        ),
        Node(
            'DocumentsField',
            [
                Field('id', String, DOCUMENTS_FIELD_SRC),
                Field('name', String, DOCUMENTS_FIELD_SRC),
                Field('type', String, DOCUMENTS_FIELD_SRC),
                Field('is_required', Boolean, DOCUMENTS_FIELD_SRC),
                Field('order', Optional[Integer], DOCUMENTS_FIELD_SRC),
                Field('enum_options', Sequence[String], DOCUMENTS_FIELD_SRC),
            ],
        ),
        Node(
            name='DocumentCategory',
            fields=[
                Field('id', String, DOCUMENT_CATEGORY_SRC),
                Field('company_id', String, DOCUMENT_CATEGORY_SRC),
                Field('title', String, DOCUMENT_CATEGORY_SRC),
                Field('date_created', String, DOCUMENT_CATEGORY_SRC),
                Field('date_updated', String, DOCUMENT_CATEGORY_SRC),
                Field('date_deleted', String, DOCUMENT_CATEGORY_SRC),
            ],
        ),
        Node(
            'DocumentParameter',
            [
                Field('id', String, DOCUMENT_PARAMETERS_SRC),
                Field('field_id', String, DOCUMENT_PARAMETERS_SRC),
                Field('value', String, DOCUMENT_PARAMETERS_SRC),
                Field('is_required', Boolean, DOCUMENT_PARAMETERS_SRC),
            ],
        ),
        Node(
            'Banner',
            [
                Field('id', String, BANNER_SRC),
                Field('start_date', String, BANNER_SRC),
                Field('end_date', String, BANNER_SRC),
                Field('color', String, BANNER_SRC),
                Field('status', String, BANNER_SRC),
                Field('positions', Optional[Sequence[String]], BANNER_SRC),
                Field('outgoing_documents_count', Optional[Sequence[String]], BANNER_SRC),
                Field('incoming_documents_sign_count', Optional[Sequence[String]], BANNER_SRC),
                Field('activity_period', Optional[String], BANNER_SRC),
                Field('employees_count', Optional[String], BANNER_SRC),
                Field('rates', Optional[Sequence[String]], BANNER_SRC),
                Field('analytics_category', String, BANNER_SRC),
                Field('audience_type', Optional[String], BANNER_SRC),
                Field('days_before_signature_expires', Optional[Integer], BANNER_SRC),
                Field('content', Sequence[Record], app_resolvers.resolve_banner_content),
            ],
        ),
        Node(
            'DocumentVersion',
            [
                Field('id', String, DOCUMENT_VERSION_SRC),
                Field('description', String, DOCUMENT_VERSION_SRC),
                Field('type', String, DOCUMENT_VERSION_SRC),
                Field('is_sent', String, DOCUMENT_VERSION_SRC),
                Field('date_created', String, DOCUMENT_VERSION_SRC),
                Field('role_id', String, DOCUMENT_VERSION_SRC),
                Field('extension', String, DOCUMENT_VERSION_SRC),
                Field('content_hash', String, DOCUMENT_VERSION_SRC),
                Field('content_length', Integer, DOCUMENT_VERSION_SRC),
                # Deprecated
                Field('name', String, DOCUMENT_VERSION_SRC),
            ],
        ),
        Node(
            'DocumentRequiredField',
            [
                Field('id', String, DOCUMENT_REQUIRED_FIELD_SRC),
                Field('document_category', String, DOCUMENT_REQUIRED_FIELD_SRC),
                Field('is_name_required', Boolean, DOCUMENT_REQUIRED_FIELD_SRC),
                Field('is_type_required', Boolean, DOCUMENT_REQUIRED_FIELD_SRC),
                Field('is_number_required', Boolean, DOCUMENT_REQUIRED_FIELD_SRC),
                Field('is_date_required', Boolean, DOCUMENT_REQUIRED_FIELD_SRC),
                Field('is_amount_required', Boolean, DOCUMENT_REQUIRED_FIELD_SRC),
                Field('company_id', String, DOCUMENT_REQUIRED_FIELD_SRC),
            ],
        ),
        Node(
            'AntivirusCheck',
            [
                Field('id', String, DOCUMENT_ANTIVIRUS_CHECK_SRC),
                Field('provider', String, DOCUMENT_ANTIVIRUS_CHECK_SRC),
                Field('status', Boolean, DOCUMENT_ANTIVIRUS_CHECK_SRC),
                Field('date_created', Boolean, DOCUMENT_ANTIVIRUS_CHECK_SRC),
                Field('date_updated', Boolean, DOCUMENT_ANTIVIRUS_CHECK_SRC),
                Field('document_version_id', String, DOCUMENT_ANTIVIRUS_CHECK_SRC),
            ],
        ),
        Node(
            'DraftAntivirusCheck',
            [
                Field('draft_id', String, DRAFT_ANTIVIRUS_CHECK_SRC),
                Field('provider', String, DRAFT_ANTIVIRUS_CHECK_SRC),
                Field('status', Boolean, DRAFT_ANTIVIRUS_CHECK_SRC),
                Field('date_created', Boolean, DRAFT_ANTIVIRUS_CHECK_SRC),
                Field('date_updated', Boolean, DRAFT_ANTIVIRUS_CHECK_SRC),
            ],
        ),
        Node(
            'CloudSigner',
            [
                Field('operation_id', String, CLOUD_SIGNER_SRC),
                Field('document_id', String, CLOUD_SIGNER_SRC),
            ],
        ),
        Node(
            'DocumentMeta',
            [
                Field('content_hash', String, DOCUMENT_META_SRC),
                Field('content_length', Integer, DOCUMENT_META_SRC),
            ],
        ),
        Node(
            'ContactRecipient',
            [
                Field('edrpou', String, resolvers.get_attrib_fields),
                Field('name', Optional[String], resolvers.get_attrib_fields),
                Field('email', Optional[String], resolvers.get_attrib_fields),
                Field('user_name', Optional[String], resolvers.get_attrib_fields),
                Field('main_recipient', Boolean, resolvers.get_attrib_fields),
            ],
        ),
        Node(
            'Draft',
            [
                Field('id', String, DRAFT_SRC),
                Field('type', String, DRAFT_SRC),
                Field('date_created', String, DRAFT_SRC),
                Field('date_updated', Optional[String], DRAFT_SRC),
                Field('company_id', String, DRAFT_SRC),
                Field('creator_role_id', String, DRAFT_SRC),
                Field('document_id', Optional[String], DRAFT_SRC),
                Field('document_version_id', Optional[String], DRAFT_SRC),
                Field('template_id', Optional[String], DRAFT_SRC),
            ],
        ),
        Node(
            'Template',
            [
                Field('id', String, TEMPLATE_SRC),
                Field('title', String, TEMPLATE_SRC),
                Field('extension', String, TEMPLATE_SRC),
                Field('date_created', String, TEMPLATE_SRC),
                Field('date_updated', String, TEMPLATE_SRC),
                Field('company_id', Optional[String], TEMPLATE_SRC),
                Field('created_by', String, TEMPLATE_SRC),
                Field('category', Optional[String], TEMPLATE_SRC),
            ],
        ),
        Node(
            'DocumentDirectory',
            [
                Field('id', Integer, DIRECTORY_SRC),
                Field('name', String, DIRECTORY_SRC),
                Field('parent_id', Optional[Integer], DIRECTORY_SRC),
                Field('date_created', String, DIRECTORY_SRC),
                Field('date_updated', String, DIRECTORY_SRC),
            ],
        ),
        Node(
            'DocumentRevoke',
            [
                Field('id', String, DOCUMENT_REVOKE_SRC),
                Field('initiator_role_id', String, DOCUMENT_REVOKE_SRC),
                Field('initiator_company_id', String, DOCUMENT_REVOKE_SRC),
                Field('reason', String, DOCUMENT_REVOKE_SRC),
                Field('status', String, DOCUMENT_REVOKE_SRC),
                Field('document_id', String, DOCUMENT_REVOKE_SRC),
                Field('signature_format', String, DOCUMENT_REVOKE_SRC),
            ],
        ),
        Node(
            'DocumentRevokeSignature',
            [
                Field('id', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('revoke_id', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('user_email', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('role_id', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('is_internal', Optional[Boolean], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('key_acsk', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('key_timemark', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('key_serial_number', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('key_company_fullname', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('key_owner_edrpou', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('key_owner_fullname', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('key_owner_position', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('stamp_acsk', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('stamp_serial_number', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('stamp_timemark', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('stamp_company_fullname', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('stamp_owner_edrpou', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('stamp_owner_fullname', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('stamp_owner_position', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SRC),
                Field('date_created', String, DOCUMENT_REVOKE_SIGNATURE_SRC),
            ],
        ),
    ]
)
