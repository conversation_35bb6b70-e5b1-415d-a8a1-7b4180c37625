import typing as t

from hiku.sources import aiopg as sa

from api.graph.constants import DB_ENGINE_KEY
from app.auth.tables import (
    company_table,
    role_table,
    user_meta_table,
    user_table,
)
from app.banner.tables import banner_content_table, banner_table
from app.billing.tables import (
    billing_account_table,
    billing_transaction_table,
    bonus_table,
    rate_extension_table,
)
from app.comments.tables import comment_table
from app.contacts.tables import (
    contact_person_phone_table,
    contact_person_table,
    contact_table,
)
from app.csat.tables import csat_survey_table
from app.directories.tables import directories_table
from app.document_antivirus.tables import (
    document_antivirus_check_table,
    draft_antivirus_check_table,
)
from app.document_automation.tables import (
    document_automation_condition_table,
    document_automation_template_table,
)
from app.document_categories.tables import document_categories_table
from app.document_revoke.tables import document_revoke_signature_table, document_revoke_table
from app.document_versions.tables import document_version_table
from app.documents.tables import (
    delete_request_table,
    document_meta_table,
    document_recipients_table,
    document_table,
    listing_table,
)
from app.documents_fields.tables import (
    document_parameters_table,
    documents_fields_table,
)
from app.documents_required_fields.tables import document_required_field_table
from app.drafts.tables import draft_table
from app.flow.tables import doc_flow_table
from app.groups.tables import group_document_access_table, group_member_table, group_table
from app.reviews.tables import review_request_table, review_setting_table, review_table
from app.sign_sessions.tables import sign_session_table
from app.signatures.tables import (
    document_cloud_signer_table,
    document_signer_table,
    signature_table,
)
from app.tags.tables import document_tag_table, tag_table
from app.templates.tables import template_table
from app.trigger_notifications.tables import trigger_notification_table


class FieldsQuery(sa.FieldsQuery):
    def in_impl(self, column: t.Any, values: t.Any) -> t.Any:
        return column.in_(values)


class LinkQuery(sa.LinkQuery):
    def in_impl(self, column: t.Any, values: t.Any) -> t.Any:
        return column.in_(values)


BILLING_ACCOUNT_SRC = FieldsQuery(DB_ENGINE_KEY, billing_account_table)
RATE_EXTENSION_SRC = FieldsQuery(DB_ENGINE_KEY, rate_extension_table)
BILLING_TRANSACTION_SRC = FieldsQuery(DB_ENGINE_KEY, billing_transaction_table)
BONUS_SRC = FieldsQuery(DB_ENGINE_KEY, bonus_table)
COMMENT_SRC = FieldsQuery(DB_ENGINE_KEY, comment_table)
COMPANY_SRC = FieldsQuery(DB_ENGINE_KEY, company_table)
CONTACT_SRC = FieldsQuery(DB_ENGINE_KEY, contact_table)
CONTACT_PERSON_PHONE_SRC = FieldsQuery(DB_ENGINE_KEY, contact_person_phone_table)
CONTACT_PERSON_SRC = FieldsQuery(DB_ENGINE_KEY, contact_person_table)
DOCUMENT_SRC = FieldsQuery(DB_ENGINE_KEY, document_table)
TAG_SRC = FieldsQuery(DB_ENGINE_KEY, tag_table)
DOCUMENT_SIGNER_SRC = FieldsQuery(DB_ENGINE_KEY, document_signer_table)
DOCUMENT_ACCESS_SRC = FieldsQuery(DB_ENGINE_KEY, listing_table)
REVIEW_SRC = FieldsQuery(DB_ENGINE_KEY, review_table)
REVIEW_REQUEST_SRC = FieldsQuery(DB_ENGINE_KEY, review_request_table)
REVIEW_SETTING_SRC = FieldsQuery(DB_ENGINE_KEY, review_setting_table)
ROLE_SRC = FieldsQuery(DB_ENGINE_KEY, role_table)
SIGN_SESSION_SRC = FieldsQuery(DB_ENGINE_KEY, sign_session_table)
SIGNATURE_SRC = FieldsQuery(DB_ENGINE_KEY, signature_table)
USER_SRC = FieldsQuery(DB_ENGINE_KEY, user_table)
DELETE_REQUEST_SRC = FieldsQuery(DB_ENGINE_KEY, delete_request_table)
DOCUMENT_FLOW_SRC = FieldsQuery(DB_ENGINE_KEY, doc_flow_table)
DOCUMENT_RECIPIENT_SRC = FieldsQuery(DB_ENGINE_KEY, document_recipients_table)
TRIGGER_NOTIFICATION_SRC = FieldsQuery(DB_ENGINE_KEY, trigger_notification_table)
DOCUMENT_AUTOMATION_TEMPLATE_SRC = FieldsQuery(DB_ENGINE_KEY, document_automation_template_table)
DOCUMENT_AUTOMATION_CONDITION_SRC = FieldsQuery(DB_ENGINE_KEY, document_automation_condition_table)
DOCUMENTS_FIELD_SRC = FieldsQuery(DB_ENGINE_KEY, documents_fields_table)
DOCUMENT_CATEGORY_SRC = FieldsQuery(DB_ENGINE_KEY, document_categories_table)
DOCUMENT_PARAMETERS_SRC = FieldsQuery(DB_ENGINE_KEY, document_parameters_table)
BANNER_SRC = FieldsQuery(DB_ENGINE_KEY, banner_table)
DOCUMENT_VERSION_SRC = FieldsQuery(DB_ENGINE_KEY, document_version_table)
DOCUMENT_REQUIRED_FIELD_SRC = FieldsQuery(DB_ENGINE_KEY, document_required_field_table)
DOCUMENT_ANTIVIRUS_CHECK_SRC = FieldsQuery(DB_ENGINE_KEY, document_antivirus_check_table)
DRAFT_ANTIVIRUS_CHECK_SRC = FieldsQuery(DB_ENGINE_KEY, draft_antivirus_check_table)
CLOUD_SIGNER_SRC = FieldsQuery(DB_ENGINE_KEY, document_cloud_signer_table)
DOCUMENT_META_SRC = FieldsQuery(DB_ENGINE_KEY, document_meta_table)
GROUP_SRC = FieldsQuery(DB_ENGINE_KEY, group_table)
GROUP_MEMBER_SRC = FieldsQuery(DB_ENGINE_KEY, group_member_table)
GROUP_DOCUMENT_ACCESS_SRC = FieldsQuery(DB_ENGINE_KEY, group_document_access_table)
DRAFT_SRC = FieldsQuery(DB_ENGINE_KEY, draft_table)
TEMPLATE_SRC = FieldsQuery(DB_ENGINE_KEY, template_table)
DIRECTORY_SRC = FieldsQuery(DB_ENGINE_KEY, directories_table)
DOCUMENT_REVOKE_SRC = FieldsQuery(DB_ENGINE_KEY, document_revoke_table)
DOCUMENT_REVOKE_SIGNATURE_SRC = FieldsQuery(DB_ENGINE_KEY, document_revoke_signature_table)
CSAT_SURVEY_SRC = FieldsQuery(DB_ENGINE_KEY, csat_survey_table)

USER_META_SRC = FieldsQuery(
    engine_key=DB_ENGINE_KEY,
    from_clause=user_meta_table,
    # table doesn't have primary key, so we need to specify it manually
    primary_key=user_meta_table.c.user_id,
)

LINKS = {
    'billing_account': {
        'billing_transaction_from': LinkQuery(
            DB_ENGINE_KEY,
            from_column=billing_transaction_table.c.from_,
            to_column=billing_transaction_table.c.id,
        ),
        'billing_transaction_to': LinkQuery(
            DB_ENGINE_KEY,
            from_column=billing_transaction_table.c.to_,
            to_column=billing_transaction_table.c.id,
        ),
    },
    'company': {
        'contact': LinkQuery(
            DB_ENGINE_KEY,
            from_column=contact_table.c.company_id,
            to_column=contact_table.c.id,
        ),
        'role': LinkQuery(
            DB_ENGINE_KEY,
            from_column=role_table.c.company_id,
            to_column=role_table.c.id,
        ),
    },
    'contact': {
        'contact_person': LinkQuery(
            DB_ENGINE_KEY,
            from_column=contact_person_table.c.contact_id,
            to_column=contact_person_table.c.id,
        )
    },
    'contact_person': {
        'contact_person_phone': LinkQuery(
            DB_ENGINE_KEY,
            from_column=contact_person_phone_table.c.contact_person_id,
            to_column=contact_person_phone_table.c.id,
        )
    },
    'document': {
        'comment': LinkQuery(
            DB_ENGINE_KEY,
            from_column=comment_table.c.document_id,
            to_column=comment_table.c.id,
        ),
        'listing': LinkQuery(
            DB_ENGINE_KEY,
            from_column=listing_table.c.document_id,
            to_column=listing_table.c.id,
        ),
        'signature': LinkQuery(
            DB_ENGINE_KEY,
            from_column=signature_table.c.document_id,
            to_column=signature_table.c.id,
        ),
        'document_tags': LinkQuery(
            DB_ENGINE_KEY,
            from_column=document_tag_table.c.document_id,
            to_column=document_tag_table.c.id,
        ),
        'recipients': LinkQuery(
            DB_ENGINE_KEY,
            from_column=document_recipients_table.c.document_id,
            to_column=document_recipients_table.c.id,
        ),
        'versions': LinkQuery(
            DB_ENGINE_KEY,
            from_column=document_version_table.c.document_id,
            to_column=document_version_table.c.id,
        ),
        'antivirus_check': LinkQuery(
            DB_ENGINE_KEY,
            from_column=document_antivirus_check_table.c.document_id,
            to_column=document_antivirus_check_table.c.id,
        ),
    },
    'tag': {
        'document_tags': LinkQuery(
            DB_ENGINE_KEY,
            from_column=document_tag_table.c.tag_id,
            to_column=document_tag_table.c.id,
        )
    },
    'user': {
        'role': LinkQuery(
            DB_ENGINE_KEY, from_column=role_table.c.user_id, to_column=role_table.c.id
        )
    },
    'banner': {
        'banner_content': LinkQuery(
            DB_ENGINE_KEY,
            from_column=banner_content_table.c.banner_id,
            to_column=banner_content_table.c.id,
        )
    },
}
