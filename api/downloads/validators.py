import datetime
import logging
from typing import Any

import pydantic
import yarl
from aiohttp import web
from multidict import MultiMapping

from api.downloads.constants import MAX_ARCHIVED_DOCUMENTS_COUNT
from api.downloads.db import select_download_document_archive
from api.downloads.enums import ArchiveFilenamesMode, SignatureArchiveFormat
from api.downloads.schemas import DownloadArchivedDocumentsSchema
from api.downloads.types import (
    ArchiveConfig,
    ArchivedDocumentsDownloadOptions,
    Document,
    DownloadArchiveOptions,
    DownloadMultiArchiveCtx,
    FileOptions,
    MultiDownloadOptions,
    XmlToJsonOptions,
    to_document,
)
from api.downloads.utils import (
    get_document_download_options,
    get_documents_archive_s3_key,
)
from api.errors import AccessDenied, Code, DoesNotExist, Error, InvalidRequest, Object
from app.auth.constants import (
    USER_AGENT_HEADER,
    USER_AGENT_PLATFORM_HEADER,
)
from app.auth.types import Auth<PERSON><PERSON>, <PERSON><PERSON>ser, User, is_wide_user_type
from app.auth.utils import get_company_config
from app.auth.validators import validate_user_permission
from app.directories.db import select_all_subtree_directories, select_document_ids_by_directories
from app.document_antivirus.validators import (
    validate_antivirus_check_download_document,
    validate_antivirus_check_download_documents,
)
from app.document_versions import utils as document_versions
from app.document_versions.const import LATEST_DOCUMENT_VERSION_MARKER
from app.document_versions.types import DocumentVersion
from app.document_versions.validators import VersionFieldPydantic
from app.documents.db import select_document_date_rejected as select_app_document_date_rejected
from app.documents.db import select_documents_ids_with_access
from app.documents.utils import get_xml_to_pdf_key
from app.documents.validators import (
    validate_document_access,
    validate_documents_access,
)
from app.documents.validators import validate_document_exists as validate_app_document_exists
from app.i18n import _
from app.lib import s3_utils, validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.enums import DocumentFolder, DocumentStatus
from app.lib.helpers import translit
from app.lib.types import (
    DataDict,
)
from app.reviews.types import DownloadReviewHistoryCtx
from app.reviews.validators import DownloadReviewHistorySchema
from app.signatures.db import select_last_internal_signature
from app.signatures.types import Signature

logger = logging.getLogger(__name__)


class CreateXMLToPDFSchema(pydantic.BaseModel):
    document_id: pv.UUID
    force: bool = False


class DownloadDocumentSchema(pydantic.BaseModel):
    document_id: pv.UUID
    version: VersionFieldPydantic | None = None

    # This field allows controlling whether we need to check if for "version" marker we can find
    # a document version in the database or not.
    #
    # By default, we should check it even for "latest" and "original" markers. It can be
    # useful to disable this check for cases, when you are not sure if the document version
    # is versioned or not and just want to download the latest content of the document.
    version_required: bool = True


class XmlToJsonSchema(pydantic.BaseModel):
    first: int = pydantic.Field(default=1, ge=1)
    limit: int | None = pydantic.Field(default=None, ge=1)


class MultiDownloadDocumentsSchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(extra='allow')

    documents_ids: list[pv.UUID] = pydantic.Field(default_factory=list, min_length=1)
    date_from: datetime.date | None = None
    date_to: datetime.date | None = None
    q: str | None = None
    folder_id: pv.IntEnum[DocumentFolder] | None = None
    status_ids: list[pv.IntEnum[DocumentStatus]] | None = None

    # ArchiveConfig fields
    in_one_folder: bool | None = None
    with_signatures: bool | None = None
    with_xml_preview: bool | None = None
    with_xml_original: bool | None = None
    with_instruction: bool | None = None
    convert_to_signature_format: SignatureArchiveFormat | None = None
    filenames_mode: ArchiveFilenamesMode | None = None


class DownloadMultiArchiveSchema(pydantic.BaseModel):
    archive_id: pv.UUID


class DownloadArchiveSchema(pydantic.BaseModel):
    with_instruction: pv.BoolNullToTrue = True
    with_xml_preview: pv.BoolNullToTrue = True


def _validate_archive_config(
    *,
    raw_data: dict[str, Any],
    headers: MultiMapping[str],
) -> ArchiveConfig:
    """
    Validate archive config by providing the correct context to the validator
    """

    # For Windows OS users set 'windows' filename mode by default
    if 'filenames_mode' not in raw_data:
        ua_raw = headers.get(USER_AGENT_HEADER, '').lower()
        # Pay attention that header value can be double-quoted (RFC8941)
        ua_platform = headers.get(USER_AGENT_PLATFORM_HEADER, '').lower().strip('"')
        if ua_platform == 'windows' or 'windows' in ua_raw:
            raw_data['filenames_mode'] = ArchiveFilenamesMode.windows

    return validators.validate_pydantic(schema=ArchiveConfig, data=raw_data)


async def _validate_last_internal_signature_exists(
    conn: DBConnection, document_id: str
) -> Signature:
    signature = await select_last_internal_signature(conn, document_id)
    if not signature:
        raise DoesNotExist(Object.signature, document_id=document_id)
    return signature


def validate_create_xml_to_pdf(data: DataDict) -> CreateXMLToPDFSchema:
    return validators.validate_pydantic(CreateXMLToPDFSchema, data)


# High order validators
async def validate_download_original(
    request: web.Request, conn: DBConnection, user: AuthUser | User
) -> FileOptions:
    raw_data = {
        'document_id': request.match_info['document_id'],
        'version': request.rel_url.query.get('version'),
        'version_required': request.rel_url.query.get('version_required', '1'),
    }

    data = validators.validate_pydantic(DownloadDocumentSchema, raw_data)

    document = await validate_app_download_document(
        conn=conn,
        document_id=data.document_id,
        user=user,
        version_marker=data.version,
        version_required=data.version_required,
    )

    download_options = await get_document_download_options(
        conn=conn,
        document_id=document.id_,
        version_id=document.version_id,
        version=document.version,
    )

    return FileOptions(
        document=document,
        download_file=download_options,
        raw_file_name=document.raw_file_name,
        file_name=document.file_name,
    )


async def validate_download_file_document(
    request: web.Request, conn: DBConnection, user: AuthUser | User
) -> Document:
    document = await validate_app_download_document(
        conn=conn,
        document_id=request.match_info['document_id'],
        user=user,
        # Try to find the "latest" version of the document. But in case of a non-versioned
        # document, we should ignore that "latest" version is not resolved to any version.
        # INFO: by default, the "latest" version raises an error if the document is not versioned
        version_marker=LATEST_DOCUMENT_VERSION_MARKER,
        version_required=False,
    )
    await validate_antivirus_check_download_document(
        conn=conn,
        document_id=request.match_info['document_id'],
        company_id=user.company_id,
    )
    return document


async def validate_download_archive(
    request: web.Request, conn: DBConnection, user: AuthUser | User
) -> DownloadArchiveOptions:
    """Validate ability to download archive with one document"""

    archive_config = _validate_archive_config(
        raw_data=dict(request.rel_url.query),
        headers=request.headers,
    )

    document = await validate_download_file_document(request, conn, user)

    return DownloadArchiveOptions(document=document, archive_config=archive_config)


async def validate_download_xml_to_json(
    request: web.Request, conn: DBConnection, user: AuthUser | User
) -> XmlToJsonOptions:
    options = await validate_download_original(request, conn, user)
    await validate_antivirus_check_download_document(
        conn=conn,
        document_id=options.document.id_,
        document_version_id=options.document.version_id,
        company_id=user.company_id,
    )

    file_name = options.file_name
    if file_name[-4:] != '.xml':
        raise Error(Code.invalid_xml_to_json_request)

    valid_data = validators.validate_pydantic(XmlToJsonSchema, request.rel_url.query)

    document = options.document
    original_options = FileOptions(
        document=document,
        download_file=options.download_file,
        raw_file_name=options.raw_file_name,
        file_name=f'{options.file_name[:-4]}.json',
    )

    config = await get_company_config(conn, company_edrpou=document.created_by_edrpou)
    default_rows_limit = config.downloads.xml_to_json_limit_rows

    return XmlToJsonOptions(
        original_options=original_options,
        first_row=valid_data.first,
        limit_rows=valid_data.limit or default_rows_limit,
    )


async def validate_download_xml_to_pdf(
    request: web.Request, conn: DBConnection, user: AuthUser | User
) -> FileOptions:
    options = await validate_download_original(request, conn, user)
    await validate_antivirus_check_download_document(
        conn=conn,
        document_id=options.document.id_,
        document_version_id=options.document.version_id,
        company_id=user.company_id,
    )

    file_name = options.file_name
    if file_name[-4:] != '.xml':
        raise Error(Code.invalid_preview_as_pdf)

    document = options.document
    document_id = document.id_
    s3_xml_to_pdf_key = document.s3_xml_to_pdf_key

    if not s3_xml_to_pdf_key:
        raise DoesNotExist(
            Object.document,
            id=document_id,
            reason=_('PDF файл не сформовано для вибраного документу'),
        )

    return FileOptions(
        document=document,
        download_file=s3_utils.DownloadFile(key=get_xml_to_pdf_key(document_id, s3_xml_to_pdf_key)),
        raw_file_name=s3_xml_to_pdf_key,
        file_name=translit(''.join((file_name[:-4], '.pdf'))),
    )


# App validators
async def validate_app_download_document(
    conn: DBConnection,
    *,
    document_id: str,
    user: AuthUser | User,
    version_marker: str | None,
    version_required: bool,
) -> Document:
    document = await validate_app_document_exists(conn, {'document_id': document_id})
    await validate_document_access(conn, user, document.id)

    document_version: DocumentVersion | None = None
    if user.company_edrpou:
        document_version = await document_versions.convert_document_version_marker(
            conn=conn,
            document_id=document.id,
            version_marker=version_marker,
            company_edrpou=user.company_edrpou,
        )
        # Check if version_marker is resolved to some version
        if (version_required and version_marker) and not document_version:
            raise DoesNotExist(Object.document_version, version_id=version_marker)

    date_rejected = await select_app_document_date_rejected(conn, document_id)
    return to_document(document, date_rejected=date_rejected, version=document_version)


async def validate_download_archived_documents(
    conn: DBConnection,
    user: User,
    request: web.Request,
) -> ArchivedDocumentsDownloadOptions:
    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(DownloadArchivedDocumentsSchema, data)
    if not valid_data.document_ids and not valid_data.directory_ids:
        raise InvalidRequest(reason=_('Потрібно вказати хоча б один документ або не порожню папку'))

    archive_config = _validate_archive_config(
        raw_data=valid_data.to_dict(),
        headers=request.headers,
    )

    # Directories, from which we should extract documents for archive
    directories = await select_all_subtree_directories(
        conn=conn, root_ids=valid_data.directory_ids, company_id=user.company_id
    )
    directory_ids = list({d.id for d in directories})

    document_ids = await select_document_ids_by_directories(
        conn=conn, directory_ids=directory_ids, company_id=user.company_id
    )
    document_ids = document_ids + valid_data.document_ids

    documents_ids_with_access = await select_documents_ids_with_access(
        conn=conn, user=user, documents_ids=document_ids
    )
    document_ids = list(set(documents_ids_with_access))

    # Additionally we will check total size of documents during generating archive
    if len(document_ids) > MAX_ARCHIVED_DOCUMENTS_COUNT:
        raise InvalidRequest(
            reason=_('Загальна кількість документів перевищує ліміт в {limit}').bind(
                limit=MAX_ARCHIVED_DOCUMENTS_COUNT
            )
        )
    if not document_ids:
        raise InvalidRequest(reason=_('Потрібно вказати хоча б один документ або не порожню папку'))

    return ArchivedDocumentsDownloadOptions(
        document_ids=document_ids,
        directory_ids=directory_ids,
        archive_name='Doc_Vchasno.zip',
        filenames_mode=archive_config.filenames_mode,
    )


def _gen_arch_file_name(data: MultiDownloadDocumentsSchema) -> str:
    """
    build archive name from the document list filter
    :param data: dict
    :return: str
    """
    file_name = 'Doc_Vchasno'
    if date_from_raw := data.date_from:
        date_form = date_from_raw.strftime('%d.%m.%Y')
        file_name = f'{file_name}_{date_form}'
    if date_to_raw := data.date_to:
        date_to = date_to_raw.strftime('%d.%m.%Y')
        file_name = f'{file_name}-{date_to}'
    if folder := data.folder_id:
        attr_name = folder.name
        file_name = f'{file_name}_{attr_name.capitalize()}'
    if statuses := data.status_ids:
        attr_name = '-'.join([s.name for s in statuses])
        file_name = f'{file_name}_{attr_name}'
    if data.q:
        query_translit = translit(data.q)
        file_name = f'{file_name}_{query_translit}'
    return f'{file_name}.zip'


async def validate_multi_download_documents(
    conn: DBConnection,
    user: User,
    request: web.Request,
) -> MultiDownloadOptions:
    data = await validators.validate_json_request(request)

    # "search" is raw query string like "?status_id=1&status_id=2&folder_id=1"
    url_query = yarl.URL(data.get('search', ''))
    clear_data: DataDict = {
        'status_ids': url_query.query.getall('status_id', []),
        'date_from': url_query.query.get('date_from', None),
        'date_to': url_query.query.get('date_to', None),
        'q': url_query.query.get('q', None),
        'folder_id': url_query.query.get('folder_id', None),
    }
    clear_data = {k: v for k, v in clear_data.items() if v}

    data['documents_ids'] = data.pop('docIds', [])
    valid_data = validators.validate_pydantic(
        schema=MultiDownloadDocumentsSchema,
        data={
            **clear_data,
            **data,
        },
    )

    # You can request to have all documents to be in one folder, but by default
    # the archive will have separate folders for every document
    archive_config_raw = valid_data.model_dump(mode='json', exclude_unset=True)
    archive_config_raw.setdefault('in_one_folder', False)
    archive_config = _validate_archive_config(
        raw_data=archive_config_raw,
        headers=request.headers,
    )

    documents_ids = valid_data.documents_ids
    documents = await validate_documents_access(conn, user, documents_ids)

    # Validate documents exists
    documents_ids_set = {document.id for document in documents}
    if documents_ids_set != set(documents_ids):
        raise DoesNotExist(
            Object.documents, document_ids=list(set(documents_ids) - documents_ids_set)
        )

    await validate_antivirus_check_download_documents(conn, documents_ids, user.company_id)

    arch_file_name = _gen_arch_file_name(valid_data)
    return MultiDownloadOptions(
        documents=documents,
        archive_name=arch_file_name,
        archive_config=archive_config,
        date_from=valid_data.date_from,
        date_to=valid_data.date_to,
        query=valid_data.q,
        folder_id=valid_data.folder_id,
        status_ids=valid_data.status_ids,
    )


async def validate_download_multi_archive(
    conn: DBConnection,
    request: web.Request,
    user: BaseUser | User,
) -> DownloadMultiArchiveCtx:
    """
    Validate the ability to download archive with multiple documents
    """
    if not is_wide_user_type(user):
        raise AccessDenied()

    validate_user_permission(user, {'can_download_document'})

    raw_data = {'archive_id': request.match_info['archive_id']}
    ctx = validators.validate_pydantic(DownloadMultiArchiveSchema, raw_data)
    archive_id = ctx.archive_id

    archive = await select_download_document_archive(conn, archive_id=archive_id)
    if not archive:
        raise DoesNotExist(Object.archive, archive_id=archive_id)

    log_extra = {
        'user_id': user.id,
        'role_id': user.role_id,
        'archive_id': archive_id,
        'archive_role_id': archive.role_id,
    }

    if archive.role_id != user.role_id:
        logger.info('User tried to download archive that does not belong to him', extra=log_extra)
        raise AccessDenied(
            reason=_(
                'Доступ до архіву заборонено. Лише користувач, який створив архів, '
                'може його завантажити.',
            )
        )

    s3_key = get_documents_archive_s3_key(archive_id=archive_id)
    return DownloadMultiArchiveCtx(
        s3_key=s3_key,
        archive_name=archive.filename,
    )


async def validate_download_last_internal_signature(
    conn: DBConnection, request: web.Request, user: AuthUser | User
) -> Signature:
    document_id = validators.validate_pydantic_adapter(
        pv.UUIDAdapter, value=request.match_info['document_id']
    )

    await validate_document_access(conn, user, document_id)

    signature = await _validate_last_internal_signature_exists(conn, document_id)

    return signature


async def validate_download_review_history(
    conn: DBConnection, user: User, request: web.Request
) -> DownloadReviewHistoryCtx:
    raw_data = {
        'document_id': request.match_info['document_id'],
        'format': request.match_info['format'],
    }
    valid_data = validators.validate_pydantic(DownloadReviewHistorySchema, raw_data)

    document = await validate_document_access(
        conn=conn, user=user, document_id=valid_data.document_id
    )

    return DownloadReviewHistoryCtx(format_=valid_data.format, request=request, document=document)


async def validate_download_signatures_details(
    request: web.Request, user: AuthUser | User, conn: DBConnection
) -> Document:
    document_id = validators.validate_pydantic_adapter(
        pv.UUIDAdapter, value=request.match_info['document_id']
    )

    document = await validate_app_document_exists(conn, document_id=document_id)
    await validate_document_access(conn=conn, user=user, document_id=document_id)

    return to_document(document)
