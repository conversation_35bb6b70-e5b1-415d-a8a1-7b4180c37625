from __future__ import annotations

import datetime
import io
import os
from dataclasses import dataclass, field
from enum import auto
from typing import (
    IO,
    TYPE_CHECKING,
    NamedTuple,
    cast,
)

import pydantic

from api.downloads.constants import (
    GENERATE_JSON_EXTENSIONS,
    MAX_FILENAME_LENGTH,
    MS_OFFICE_EXTENSIONS,
)
from api.downloads.enums import ArchiveFilenamesMode, SignatureArchiveFormat
from api.uploads.utils import unarchive_file_name
from app.auth.schemas import ReviewRenderConfig
from app.document_revoke.types import DocumentRevoke, DocumentRevokeSignature
from app.document_versions.types import DocumentVersion
from app.documents.enums import FirstSignBy
from app.documents.types import DocumentDetails, DocumentRecipient
from app.lib import s3_utils
from app.lib.database import DBRow
from app.lib.datetime_utils import (
    soft_isoformat,
    soft_to_local_datetime,
)
from app.lib.enums import (
    DocumentFolder,
    DocumentStatus,
    NamedEnum,
    SignatureType,
)
from app.lib.helpers import (
    normalize_to_composed,
    safe_enum_value,
    safe_filename,
)
from app.lib.types import DataDict
from app.reviews.enums import ReviewType
from app.signatures.enums import CertificatePowerType, SignaturePowerType
from app.signatures.types import Signature

if TYPE_CHECKING:
    from app.documents import types as documents_types


BytesIO = IO[bytes]


class ArchiveFileType(NamedEnum):
    original = auto()
    original_preview = auto()
    signature_preview = auto()
    signature = auto()
    revoke = auto()
    revoke_signature = auto()


class ArchiveFile(NamedTuple):
    file_name: str
    content: bytes
    type: ArchiveFileType

    @property
    def is_signature(self) -> bool:
        return self.type == ArchiveFileType.signature

    @property
    def is_original(self) -> bool:
        return self.type == ArchiveFileType.original

    @property
    def is_revoke(self) -> bool:
        return self.type == ArchiveFileType.revoke

    @property
    def is_revoke_signature(self) -> bool:
        return self.type == ArchiveFileType.revoke_signature


@dataclass
class ArchiveFilePending:
    # filename that should be downloaded and converted to ArchiveFile
    file_name: str
    options: FileOptions
    type: ArchiveFileType
    ignore_last_byte: bool = False


class ArchiveOptions(NamedTuple):
    """
    Container that represents archive files for a single document. Potentially it can contain:
     - original file
     - xml preview
     - signatures
     - signatures preview
    """

    # Safe filename of the archive with extension.
    # Example: "Zvit-12.zip"
    archive_filename: str

    # Safe filename of the original document with extension.
    # Example: "Zvit-12.pdf"
    document_filename: str

    # Safe filename of the original document without an extension.
    # Example: "Zvit-12"
    document_filename_base: str

    files: tuple[ArchiveFile, ...]

    @property
    def archive_size(self) -> int:
        return sum(len(f.content) for f in self.files)


class GeneratedArchiveCtx(NamedTuple):
    archive_id: str
    files_count: int
    documents: list[Document]


@dataclass
class GeneratedDocumentsArchive:
    filename: str
    buffer: io.BytesIO


class ArchiveConfig(pydantic.BaseModel):
    # if equals to True create archive without subdirectories for each document
    in_one_folder: bool = True

    # if equals to True download p7s signatures
    with_signatures: bool = True

    # if equals to True download xml preview pdf
    with_xml_preview: bool = True

    # if equals to True download xml original file
    with_xml_original: bool = True

    # if equal to True download Instruction file
    with_instruction: bool = True

    # if equal to True download original file
    with_original: bool = True

    # if equal to True download with signatures preview
    with_signatures_preview: bool = True

    # if equal to True download revoke document
    with_revoke_original: bool = True

    # if equal to True download revoke signatures
    with_revoke_signatures: bool = True

    # if equal to 'internal_appended' - convert all signatures to container
    # (original file and all signatures download in one file with *.p7s extension)
    signature_format: SignatureArchiveFormat | None = pydantic.Field(
        default=None,
        validation_alias='convert_to_signature_format',
    )

    # How to handle filenames in archive and name of archive itself
    filenames_mode: ArchiveFilenamesMode = ArchiveFilenamesMode.default

    # Should shorten filenames to this length
    filenames_max_length: int = pydantic.Field(MAX_FILENAME_LENGTH, ge=10, le=MAX_FILENAME_LENGTH)

    @classmethod
    def from_dict(cls, data: DataDict) -> ArchiveConfig:
        return cls.model_validate(data)

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json')


class Document(NamedTuple):
    """
    Container that represents document for downloading.

    Do not confuse with "app.documents.types.Document" that represents record in a database.
    """

    id_: str
    created_by_edrpou: str
    owned_by_edrpou: str

    # document.title + document.extension
    # ex: "Хіба ревуть воли як ясла повні_№12_(копія).pdf"
    raw_file_name: str

    version: DocumentVersion | None

    number: str | None

    status: DocumentStatus

    date_created: datetime.datetime
    date_delivered: datetime.datetime | None = None
    date_rejected: datetime.datetime | None = None

    recipient_edrpou: str | None = None

    type_: str | None = None
    s3_xml_to_pdf_key: str | None = None

    first_sign_by: FirstSignBy | None = None

    @property
    def id(self) -> str:
        return self.id_

    @property
    def version_id(self) -> str | None:
        return self.version.id if self.version else None

    @property
    def uploaded_by_edrpou(self) -> str:
        return self.created_by_edrpou

    @property
    def extension(self) -> str:
        return os.path.splitext(self.file_name)[1]

    @property
    def file_name(self) -> str:
        """
        Filename with an extension (but without an archive extension, if it was archived).

        Example: some_document.pdf, some_document.xml
        """
        return unarchive_file_name(self.raw_file_name)

    @property
    def is_pdf(self) -> bool:
        return self.extension.lower() == '.pdf'

    @property
    def is_xml(self) -> bool:
        return self.extension.lower() == '.xml'

    @property
    def is_office(self) -> bool:
        return self.extension.lower() in MS_OFFICE_EXTENSIONS

    @property
    def title(self) -> str:
        return os.path.splitext(self.file_name)[0]

    @property
    def is_3p(self) -> bool:
        return self.first_sign_by == FirstSignBy.recipient

    def to_document_details(self) -> DocumentDetails:
        return DocumentDetails(self.id_, self.title)


class FileOptions(NamedTuple):
    document: Document
    download_file: s3_utils.DownloadFile
    raw_file_name: str
    file_name: str


@dataclass
class ViewerSignature:
    id_: str
    date_created: datetime.datetime | None
    is_owner: bool

    def to_api(self) -> DataDict:
        """
        Convert to json api format. See also subclasses for more details.
        """
        return {
            'id': self.id_,
            'is_owner': self.is_owner,
        }


@dataclass
class ViewerSignatureEusign(ViewerSignature):
    """
    Container that represent set of data for one signature or stamp for visualization
    by viewer.
    """

    type_: SignatureType

    company_name: str
    edrpou: str
    name: str
    position: str
    serial_number: str
    is_legal: bool
    time_mark: datetime.datetime | None
    signature_power_type: SignaturePowerType | None
    certificate_power_type: CertificatePowerType | None

    def to_api(self) -> DataDict:
        """Convert to json api format"""
        data = super().to_api()
        return {
            **data,
            'type': safe_enum_value(self.type_),
            'company_name': self.company_name,
            'edrpou': self.edrpou,
            'name': self.name,
            'position': self.position,
            'serial_number': self.serial_number,
            'is_legal': self.is_legal,
            'time_mark': soft_isoformat(self.time_mark),
            'signature_power_type': safe_enum_value(self.signature_power_type),
            'certificate_power_type': safe_enum_value(self.certificate_power_type),
        }

    @staticmethod
    def key_from_db(
        signature: Signature | DocumentRevokeSignature,
        document_owner_edrpou: str,
    ) -> ViewerSignatureEusign:
        type_ = SignatureType.signature
        date_created = soft_to_local_datetime(signature.date_created)
        time_mark = soft_to_local_datetime(signature.key_timemark)
        is_owner = signature.key_owner_edrpou == document_owner_edrpou
        return ViewerSignatureEusign(
            id_=f'{signature.id}-{type_.value}',
            type_=type_,
            company_name=cast(str, signature.key_company_fullname),
            edrpou=cast(str, signature.key_owner_edrpou),
            name=cast(str, signature.key_owner_fullname),
            position=cast(str, signature.key_owner_position),
            serial_number=cast(str, signature.key_serial_number),
            is_legal=signature.key_is_legal or False,
            is_owner=is_owner,
            date_created=date_created,
            time_mark=time_mark,
            signature_power_type=signature.key_power_type,
            certificate_power_type=signature.key_certificate_power_type,
        )

    @staticmethod
    def stamp_from_db(
        signature: Signature | DocumentRevokeSignature,
        document_owner_edrpou: str,
    ) -> ViewerSignatureEusign:
        type_ = SignatureType.stamp
        date_created = soft_to_local_datetime(signature.date_created)
        time_mark = soft_to_local_datetime(signature.stamp_timemark)
        is_owner = signature.stamp_owner_edrpou == document_owner_edrpou
        return ViewerSignatureEusign(
            id_=f'{signature.id}-{type_.value}',
            type_=type_,
            company_name=cast(str, signature.stamp_company_fullname),
            edrpou=cast(str, signature.stamp_owner_edrpou),
            name=cast(str, signature.stamp_owner_fullname),
            position=cast(str, signature.stamp_owner_position),
            serial_number=cast(str, signature.stamp_serial_number),
            is_legal=signature.stamp_is_legal or False,
            is_owner=is_owner,
            date_created=date_created,
            time_mark=time_mark,
            signature_power_type=signature.stamp_power_type,
            certificate_power_type=signature.stamp_certificate_power_type,
        )


SignaturesMatrix = list[list[ViewerSignatureEusign | None]]


class Review(NamedTuple):
    type_: ReviewType
    email: str
    first_name: str | None
    second_name: str | None
    last_name: str | None
    position: str | None


@dataclass(frozen=True)
class ViewerOptions:
    original_options: FileOptions
    signatures: list[ViewerSignature]
    file_ext: str
    review_render_config: ReviewRenderConfig

    render_signature_in_interface: bool = True
    render_signature_on_print_document: bool = True
    render_review_in_interface: bool = True
    render_review_on_print_document: bool = True
    sign_session_id: str | None = None
    reviews: list[Review] = field(default_factory=list)
    use_xml_to_json: bool = False
    version_marker: str | None = None

    revoke_signatures: list[ViewerSignature] = field(default_factory=list)
    revoke: DocumentRevoke | None = None
    recipients: list[DocumentRecipient] | None = None

    @property
    def document(self) -> Document:
        return self.original_options.document

    def is_generate_json(self) -> bool:
        return self.file_ext in GENERATE_JSON_EXTENSIONS


class XmlToJsonOptions(NamedTuple):
    original_options: FileOptions
    first_row: int
    limit_rows: int


class ArchivedDocumentsDownloadOptions(NamedTuple):
    directory_ids: list[int]
    document_ids: list[str]
    archive_name: str
    filenames_mode: ArchiveFilenamesMode | None = None


class MultiDownloadOptions(NamedTuple):
    documents: list[documents_types.Document]
    archive_name: str

    archive_config: ArchiveConfig

    # url parameters
    date_from: datetime.date | None = None
    date_to: datetime.date | None = None
    query: str | None = None
    folder_id: DocumentFolder | None = None
    status_ids: list[DocumentStatus] | None = None

    @property
    def documents_ids(self) -> list[str]:
        return [document.id for document in self.documents]

    @property
    def request_data(self) -> DataDict:
        data: DataDict = {}
        if self.date_from:
            data['date_from'] = self.date_from
        if self.date_to:
            data['date_to'] = self.date_to
        if self.date_from:
            data['query'] = self.query
        if self.folder_id:
            data['folder_id'] = self.folder_id
        if self.status_ids:
            data['status_ids'] = self.status_ids
        return data


def to_document(
    document: documents_types.DocumentWithUploader | DBRow,
    *,
    date_rejected: datetime.datetime | None = None,
    version: DocumentVersion | None = None,
) -> Document:
    extension = document.extension

    if version and version.extension:
        extension = version.extension

    normalized_filename = normalize_to_composed(f'{document.title}{extension}')

    return Document(
        id_=document.id,
        created_by_edrpou=document.uploaded_by_edrpou,
        owned_by_edrpou=document.edrpou_owner,
        raw_file_name=safe_filename(normalized_filename),
        date_created=document.date_created,
        date_delivered=document.date_delivered,
        date_rejected=date_rejected,
        recipient_edrpou=document.edrpou_recipient,
        type_=document.type,
        s3_xml_to_pdf_key=document.s3_xml_to_pdf_key,
        first_sign_by=document.first_sign_by,
        version=version,
        number=document.number,
        status=DocumentStatus(document.status_id),
    )


class DownloadArchiveOptions(NamedTuple):
    document: Document
    archive_config: ArchiveConfig


@dataclass
class DownloadDocumentsArchiveRow:
    id: str
    role_id: str
    edrpou: str
    documents_ids: list[str]
    filename: str
    size: int

    @staticmethod
    def from_row(row: DBRow) -> DownloadDocumentsArchiveRow:
        return DownloadDocumentsArchiveRow(
            id=row.id,
            role_id=row.role_id,
            edrpou=row.edrpou,
            documents_ids=row.documents_ids,
            filename=row.filename,
            size=row.size,
        )


@dataclass
class DownloadMultiArchiveCtx:
    s3_key: str
    archive_name: str | None

    def to_log_extra(self) -> DataDict:
        return {
            's3_key': self.s3_key,
            'archive_name': self.archive_name,
        }
