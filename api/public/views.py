import asyncio
import logging
from collections import defaultdict
from datetime import datetime
from http import HTT<PERSON>tatus
from typing import Any

import sqlalchemy as sa
import ujson
from aiohttp import (
    web,
)

from api.db import get_document_listing_date_mapping
from api.errors import (
    AccessDenied,
    InvalidRequest,
)
from api.graph.utils import (
    process_query_graph,
    query_graph,
)
from api.public import utils as public_utils
from api.public.data import (
    to_comment_data,
    to_delete_requests_data,
    to_document_additional_fields_data,
    to_document_fields,
    to_document_signature_data,
    to_download_document_data,
    to_flows_data,
    to_incoming_documents_response,
    to_owner_document_response,
    to_owner_documents_response,
    to_review_requests_data,
    to_review_status_data,
    to_reviews_data,
    to_signatures_data,
    to_token_data,
    to_user_data,
)
from api.public.db import (
    get_all_company_accessible_fields,
    select_available_document_additional_fields,
    select_recipient_summary,
)
from api.public.decorators import (
    api_handler,
    api_super_admin_permission_required,
)
from api.public.enums import (
    Recipient<PERSON><PERSON><PERSON>,
    TokenVendor,
)
from api.public.utils import create_token as create_token_func
from api.public.utils import (
    list_owner_documents_es,
    list_owner_documents_updates,
    prepare_delete_requests_for_response,
    prepare_document_categories_for_response,
    prepare_document_fields_for_response,
    prepare_document_versions_for_response,
    prepare_owner_info_for_response,
    prepare_recipients_for_response,
    resolve_incoming_documents_from_es,
    update_processed_status,
    upsert_processed_status,
)
from api.public.validators import (
    DocumentIdsSchema,
    UpdateSignersSchema,
    validate_add_document_field,
    validate_add_signature_api,
    validate_create_coworker,
    validate_create_log,
    validate_create_token,
    validate_document_exists,
    validate_get_delete_document_requests,
    validate_invite_coworkers,
    validate_list_documents,
    validate_list_download_documents,
    validate_list_incoming_documents,
    validate_retrieve_document,
    validate_retrieve_flows,
    validate_retrieve_recipient,
    validate_retrieve_review_requests,
    validate_retrieve_reviews,
    validate_retrieve_reviews_status,
    validate_set_signers,
    validate_signatures,
)
from api.utils import api_response
from api.validators import validate_api_vendor_config
from app.actions.utils import get_source
from app.archive.utils import is_documents_archived
from app.auth import utils as auth
from app.auth.tables import (
    company_table,
    role_company_join,
    role_table,
)
from app.auth.types import User
from app.auth.utils import (
    sync_listing_job,
)
from app.auth.validators import validate_user_permission
from app.comments.utils import handle_add_comment, list_comments_by_document
from app.documents import utils as documents_utils
from app.documents.db import (
    delete_document_settings,
    select_delete_requests_by_user,
    select_document_access_level,
    select_documents_access_level,
    select_documents_by_ids,
    upsert_delete_document_settings,
)
from app.documents.enums import DocumentAccessLevel
from app.documents.utils import (
    DocumentsDelete,
    accept_delete_request_util,
    cancel_delete_request_util,
    create_delete_request,
    get_expected_document,
    handle_document_update,
    reject_delete_request_util,
    update_documents_date_delivered_job,
)
from app.documents.validators import (
    UpdateDocumentAccessSettingsSchema,
    UpdateDocumentInfoSchema,
    validate_company_documents_access,
    validate_delete_document,
)
from app.documents_fields.db import upsert_document_parameters
from app.documents_fields.types import ParameterSettingSchema
from app.es.utils import (
    send_to_indexator,
)
from app.events import document_actions
from app.events.user_actions import utils as user_actions_utils
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.flow import utils as flow
from app.i18n import _
from app.lib import (
    tracking,
    validators,
)
from app.lib.chunks import iter_by_chunks
from app.lib.database import DBRow
from app.lib.enums import SignersSource
from app.lib.helpers import get_client_ip
from app.lib.logging import log_duration
from app.lib.types import DataDict
from app.models import select_all
from app.onboarding import utils as onboarding_utils
from app.profile.validators import validate_company_exists
from app.registration.utils import (
    send_jobs_about_company_update,
)
from app.reviews import db as reviews_db
from app.reviews import views as reviews_views
from app.reviews.enums import ReviewRequestSource
from app.services import services
from app.sign_sessions import utils as sign_sessions
from app.signatures import utils as signatures_utils
from app.signatures.db import (
    select_signatures,
    select_signatures_by_documents_for_api,
)
from app.signatures.utils import add_signature_schedule_async_jobs
from app.tags.db import select_tags_by_documents
from app.uploads.utils import process_web_upload
from worker import topics

INVITE_USER_CHUNK_SIZE = 20

GQL_LIST_DOWNLOAD_DOCUMENTS = (
    '{{allDocuments(ids: {document_ids}) {{documents {{id extension s3XmlToPdfKey }} }} }}'
)
GQL_LIST_ROLES = """
{
    currentCompanyRoles {
        id
        status
        dateCreated
        position
        user {
            email
        }
    }
}
"""

default_logger = logging.getLogger('api.public.log')
logger = logging.getLogger(__name__)


@api_handler
async def get_document_additional_fields(request: web.Request, user: User) -> web.Response:
    """Retrieve documents additional fields by ID"""

    document_id = request.match_info['document_id']
    async with request.app['db'].acquire() as conn:
        await validate_document_exists(conn, document_id)
        document_fields = await select_available_document_additional_fields(
            conn=conn,
            company_id=user.company_id,
            document_ids=[document_id],
        )

    return web.json_response(to_document_additional_fields_data(document_fields))


@api_handler
async def create_comment(request: web.Request, user: User) -> web.Response:
    """Add new comment for document."""
    comment, options = await handle_add_comment(request, user)
    await send_to_indexator(request.app['redis'], [options.document.id], to_slow_queue=True)
    return api_response(request, to_comment_data(comment, options.author), status=201)


async def get_fields_by_company_id(request: web.Request, user: User) -> web.Response:
    """Get all document fields from company"""
    async with request.app['db'].acquire() as conn:
        company_id = user.company_id
        await validate_company_exists(conn, company_id)
        raw_data = await get_all_company_accessible_fields(
            conn=conn,
            company_id=company_id,
        )
        document_fields = to_document_fields(raw_data)
    return web.json_response(document_fields)


@api_handler
async def add_additional_fields_to_document(request: web.Request, user: User) -> web.Response:
    """Add additional field to document already sent to counterparty"""

    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(ParameterSettingSchema, data)

    document_id = request.match_info['document_id']

    async with request.app['db'].acquire() as conn:
        parameters = await validate_add_document_field(
            conn=conn,
            user=user,
            data=valid_data,
            document_id=document_id,
        )
        await upsert_document_parameters(conn, parameters)

    logger.info(
        'Add fields to document',
        extra={
            'company_id': user.company_id,
            'user_email': user.email,
            'document_id': document_id,
            'field_id': valid_data.field_id,
        },
    )
    return web.HTTPCreated()


@api_handler
async def get_delete_requests_by_company(request: web.Request, user: User) -> web.Response:
    db = (
        services.db_readonly
        if get_flag(FeatureFlags.READONLY_DB_FOR_DELETE_REQUEST_API)
        else services.db
    )
    request_options = validate_get_delete_document_requests(
        data={
            'statuses': request.rel_url.query.getall('status', None),
            'cursor': request.rel_url.query.get('cursor'),
            'ids': request.rel_url.query.getall('ids', None),
            'with_outgoing': request.rel_url.query.get('with_outgoing'),
        }
    )
    async with db.acquire() as conn:
        delete_requests = await select_delete_requests_by_user(
            conn=conn,
            user=user,
            statuses=request_options.statuses,
            cursor=request_options.cursor,
            ids=request_options.ids,
            with_outgoing=request_options.with_outgoing,
        )
    return api_response(request, to_delete_requests_data(delete_requests), status=200)


@api_handler
async def create_log(request: web.Request, user: User) -> web.Response:
    """Log message from external system (like VBox or 1C plugin)."""
    options = validate_create_log(await validators.validate_json_request(request, dict_only=False))

    logger = logging.getLogger()
    for record in options.records:
        logger.handle(record)

    return api_response(request, None, status=201 if options.records else 200)


@api_handler
async def create_token(request: web.Request, user: User) -> web.Response:
    """Create new token for existed user.

    In case if token already created for given vendor - return details of
    token from database.
    """

    status = 201
    raw_token: str | None = None

    async with request.app['db'].acquire() as conn:
        await validate_api_vendor_config(conn, edrpou=user.company_edrpou, vendor_enum=TokenVendor)
        options = await validate_create_token(conn, await validators.validate_json_request(request))

        async with conn.begin():
            if options.existed_token is None:
                token, raw_token = await create_token_func(conn, options)
            else:
                status = 200
                token = options.existed_token

    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        user_actions = await user_actions_utils.build_token_create_user_actions(
            actor_user=user,
            role_ids=[token.role_id],
            expire_date=None,
            request=request,
        )
        await user_actions_utils.add_user_actions(user_actions)

    return api_response(request, to_token_data(token, raw_token), status=status)


@api_handler
async def create_coworker(request: web.Request, user: User) -> web.Response:
    """
    API for creating coworkers with default permissions and
    autogenerated password
    """
    async with request.app['db'].acquire() as conn:
        data = await validators.validate_json_request(request)
        options = await validate_create_coworker(conn, data, user)

        coworker = await public_utils.create_coworker(conn, options)

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            await user_actions_utils.add_user_action(
                user_action=user_actions_utils.types.UserAction(
                    action=user_actions_utils.types.Action.role_create,
                    source=user_actions_utils.get_event_source(request),
                    email=coworker.email,
                    user_id=coworker.id,
                    phone=coworker.phone,
                    company_id=user.company_id,
                    extra={
                        'affected_user_email': coworker.email,
                        'ip': get_client_ip(request),
                        'user_agent': request.headers.get('User-Agent'),
                    },
                )
            )

    return api_response(request, data=to_user_data(coworker), status=HTTPStatus.CREATED)


@api_handler
async def delete_document(request: web.Request, user: User) -> web.Response:
    """Delete document owned by current logged in role."""
    document_id = request.match_info['document_id']
    data: dict[str, Any] = {
        'document_id': document_id,
        'user_company_id': user.company_id,
        'user_id': user.id,
        'user_is_legal': user.is_legal,
        'user_edrpou': user.company_edrpou,
    }
    logger.info('Delete document', extra=data)

    async with services.db.acquire() as conn:
        document = await validate_delete_document(
            conn=conn,
            data=data,
            user=user,
        )

        async with conn.begin():
            documents_deleter = DocumentsDelete(documents=[document])
            await documents_deleter.perform_main_transaction(conn)

        await document_actions.add_document_action(
            document_action=document_actions.DocumentAction(
                action=document_actions.Action.document_delete,
                document_edrpou_owner=document.edrpou_owner,
                document_id=document.id,
                document_title=document.title,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                email=user.email,
                role_id=user.role_id,
            )
        )

        await documents_deleter.perform_async_actions()

    return api_response(request, None, status=204)


@api_handler
async def graphql(request: web.Request, user: User) -> web.Response:
    """Query GraphQL for users authorized via token."""
    return await process_query_graph(
        request=request,
        user=user,
    )


async def list_document_comments(request: web.Request, user: User) -> web.Response:
    """List all document comments."""
    document_id = request.match_info['document_id']

    # Get list of comments for the document
    formatted_comments = await list_comments_by_document(document_id, user)

    return api_response(request, {'comments': formatted_comments})


@api_handler
async def list_roles(request: web.Request, user: User) -> web.Response:
    """List all company roles."""

    if not user.can_view_coworkers:
        return api_response(request, {'roles': []})

    data = await query_graph(
        request=request,
        user=user,
        query=GQL_LIST_ROLES,
    )
    roles = [
        {
            'id': r['id'],
            'status': r['status'],
            'date_created': r['dateCreated'].isoformat(),
            'email': r['user']['email'],
            'position': r['position'],
        }
        for r in data.get('currentCompanyRoles', [])
    ]

    return api_response(request, {'roles': roles})


@api_handler
async def list_owner_documents(request: web.Request, user: User) -> web.Response:
    """List of documents owned by current company"""

    force_all = request.match_info['version'] == 'v1'

    use_readonly = get_flag(FeatureFlags.USE_READONLY_DB_FOR_LIST_OWNER_DOCUMENTS_API)
    db = services.db_readonly if use_readonly else services.db

    async with db.acquire() as conn:
        async with log_duration('validate list_owner_documents'):
            options = await validate_list_documents(
                conn=conn,
                user=user,
                data=dict(
                    request.rel_url.query,
                    ids=request.rel_url.query.getall('ids', None),
                    edrpou=user.company_edrpou,
                    tags_ids=request.rel_url.query.getall('tag_id', []),
                    documents_fields=request.rel_url.query.getall('document_field', []),
                    statuses=request.rel_url.query.getall('status', []),
                    categories=request.rel_url.query.getall('category', []),
                ),
            )

        documents: list[DBRow]
        next_cursor: str | None
        if get_flag(FeatureFlags.ES_SEARCH_API):
            documents, next_cursor = await list_owner_documents_es(
                conn=conn, options=options, user=user
            )
        else:
            # We don't support selecting documents from a database
            documents = []
            next_cursor = None

        async with log_duration('get view_sessions'):
            view_sessions_mapping = await sign_sessions.get_view_sessions_ids(
                conn=conn, user=user, documents_ids=[item.id for item in documents]
            )

        async with log_duration('get documents_archived'):
            archive_mapping = await is_documents_archived(
                conn=conn,
                company_id=user.company_id,
                document_ids=[item.id for item in documents],
            )

        # Prepare signatures data
        document_ids = {item.id for item in documents}
        async with log_duration('get documents_signatures'):
            db_signatures = await select_signatures_by_documents_for_api(conn, document_ids)

        signatures: defaultdict[str, list[DataDict]] = defaultdict(list)
        for row in db_signatures:
            signatures[row.document_id].append(to_document_signature_data(row))

        recipients_mapping: defaultdict[str, list[DBRow]] | None = None
        uploader_info_mapping = None
        if options.with_recipients:
            async with log_duration('get document_recipients'):
                recipients_mapping = await prepare_recipients_for_response(
                    conn=conn, documents_ids=list(document_ids)
                )
            async with log_duration('get document_owner'):
                uploader_info_mapping = await prepare_owner_info_for_response(
                    conn=conn, documents_ids=list(document_ids)
                )

        document_fields_mapping: defaultdict[str, list[DBRow]] | None = None
        if options.with_document_fields:
            async with log_duration('get document_fields'):
                document_fields_mapping = await prepare_document_fields_for_response(
                    conn=conn, user=user, document_ids=document_ids
                )

        delete_requests_mapping: defaultdict[str, list[DBRow]] | None = None
        if options.with_delete_requests:
            async with log_duration('get document_del_requests'):
                delete_requests_mapping = await prepare_delete_requests_for_response(
                    conn=conn, document_ids=document_ids
                )

        parent_to_children: defaultdict[str, set[str]] | None = None
        child_to_parent: dict[str, str] | None = None
        if options.with_connections:
            async with log_duration('get document child_to_parent'):
                child_to_parent = await public_utils.prepare_parents_links_for_response(
                    conn=conn,
                    user=user,
                    documents_ids=document_ids,
                )
            async with log_duration('get document parent_to_child'):
                parent_to_children = await public_utils.prepare_children_links_for_response(
                    conn=conn,
                    user=user,
                    documents_ids=document_ids,
                )

        versions_mapping = None
        if options.with_versions:
            async with log_duration('get document versions'):
                versions_mapping = await prepare_document_versions_for_response(
                    conn=conn, user=user, document_ids=document_ids
                )

        access_settings_mapping: dict[str, DocumentAccessLevel] = {}
        if options.with_access_settings:
            access_settings_mapping = await select_documents_access_level(
                conn=conn,
                documents_ids=list(document_ids),
                edrpou=user.company_edrpou,
            )

        async with log_duration('get document categories'):
            categories_mapping = await prepare_document_categories_for_response(
                conn=conn,
                documents=documents,
            )

        response = to_owner_documents_response(
            documents=documents,
            categories_mapping=categories_mapping,
            view_sessions_mapping=view_sessions_mapping,
            signatures=signatures,
            recipients_mapping=recipients_mapping,
            document_fields_mapping=document_fields_mapping,
            delete_requests_mapping=delete_requests_mapping,
            company_edrpou=user.company_edrpou,
            parent_to_children=parent_to_children,
            child_to_parent=child_to_parent,
            uploader_info_mapping=uploader_info_mapping,
            versions_mapping=versions_mapping,
            archive_mapping=archive_mapping,
            access_settings_mapping=access_settings_mapping,
        )
        if not force_all:
            response['next_cursor'] = next_cursor

        changed_doc_ids = [d.id for d in documents if d.has_changed_for_public_api]

        if not use_readonly:
            async with log_duration('update document has_changed_for_public_api'):
                await list_owner_documents_updates(conn, changed_doc_ids)

    if use_readonly:
        # updates on master DB
        async with services.db.acquire() as conn:
            async with log_duration('update document has_changed_for_public_api'):
                await list_owner_documents_updates(conn, changed_doc_ids)

    if changed_doc_ids:
        await send_to_indexator(
            redis=services.redis, document_ids=changed_doc_ids, to_slow_queue=True
        )

    return api_response(request, response)


@api_handler
async def list_download_documents(request: web.Request, user: User) -> web.Response:
    """List links to download documents by ID."""
    options = validate_list_download_documents(
        dict(request.rel_url.query, ids=request.rel_url.query.getall('ids', []))
    )

    query = GQL_LIST_DOWNLOAD_DOCUMENTS.format(document_ids=ujson.dumps(options.ids))
    data = await query_graph(
        request=request,
        user=user,
        query=query,
    )

    ready, pending, total = 0, 0, 0
    documents = []

    for item in data['allDocuments']['documents']:
        if item['extension'] == '.xml':
            if item['s3XmlToPdfKey']:
                ready += 1
            else:
                pending += 1
        else:
            ready += 1

        total += 1
        documents.append(to_download_document_data(item))

    status = 'pending' if pending else 'ready'
    if not total:
        status = 'empty'

    return api_response(
        request,
        {
            'status': status,
            'ready': ready,
            'pending': pending,
            'total': total,
            'documents': documents,
        },
    )


@api_handler
async def list_incoming_documents(request: web.Request, user: User) -> web.Response:
    """List incoming documents."""

    force_all = request.match_info['version'] == 'v1'
    listing_date_mapping: dict[str, datetime] | None = None

    use_readonly = get_flag(FeatureFlags.USE_READONLY_DB_FOR_LIST_INCOMING_DOCUMENTS)
    db = services.db_readonly if use_readonly else services.db

    async with db.acquire() as conn:
        options = await validate_list_incoming_documents(
            conn=conn,
            user=user,
            data=dict(
                request.rel_url.query,
                ids=request.rel_url.query.getall('ids', []),
                tags_ids=request.rel_url.query.getall('tag_id', []),
                edrpou=user.company_edrpou,
                documents_fields=request.rel_url.query.getall('document_field', []),
                edrpou_owners=request.rel_url.query.getall('edrpou_owner', []),
                statuses=request.rel_url.query.getall('status', []),
                categories=request.rel_url.query.getall('category', []),
            ),
        )

        documents: list[DBRow]
        next_cursor: str | None
        if get_flag(FeatureFlags.ES_SEARCH_API):
            documents, next_cursor = await resolve_incoming_documents_from_es(
                es=services.es.documents, conn=conn, user=user, options=options
            )
        else:
            # We don't support selecting documents from a database
            documents = []
            next_cursor = None

        documents_ids = [item.id for item in documents]

        view_sessions_mapping = await sign_sessions.get_view_sessions_ids(
            conn=conn,
            user=user,
            documents_ids=documents_ids,
        )

        archive_mapping = await is_documents_archived(
            conn=conn,
            company_id=user.company_id,
            document_ids=documents_ids,
        )

        delete_requests_mapping: dict[str, list[DBRow]] | None = None
        if options.with_delete_requests:
            delete_requests_mapping = await prepare_delete_requests_for_response(
                conn=conn, document_ids=documents_ids
            )

        if options.with_listing_date:
            listing_date_mapping = await get_document_listing_date_mapping(
                conn=conn, documents_ids=documents_ids, user=user
            )

        recipients_mapping: defaultdict[str, list[DBRow]] | None = None
        uploader_info_mapping = None
        if options.with_recipients:
            recipients_mapping = await prepare_recipients_for_response(
                conn=conn, documents_ids=documents_ids
            )
            uploader_info_mapping = await prepare_owner_info_for_response(
                conn=conn, documents_ids=documents_ids
            )

        parent_to_children: defaultdict[str, set[str]] | None = None
        child_to_parent: dict[str, str] | None = None
        if options.with_connections:
            child_to_parent = await public_utils.prepare_parents_links_for_response(
                conn=conn,
                user=user,
                documents_ids=set(documents_ids),
            )
            parent_to_children = await public_utils.prepare_children_links_for_response(
                conn=conn,
                user=user,
                documents_ids=set(documents_ids),
            )

        document_fields_mapping: dict[str, list[DBRow]] | None = None
        if options.with_document_fields:
            document_fields_mapping = await prepare_document_fields_for_response(
                conn=conn, user=user, document_ids=documents_ids
            )

        versions_mapping = None
        if options.with_versions:
            versions_mapping = await prepare_document_versions_for_response(
                conn=conn, user=user, document_ids=documents_ids
            )

        access_settings_mapping: dict[str, DocumentAccessLevel] = {}
        if options.with_access_settings:
            access_settings_mapping = await select_documents_access_level(
                conn=conn,
                documents_ids=documents_ids,
                edrpou=user.company_edrpou,
            )

        categories_mapping = await prepare_document_categories_for_response(
            conn=conn,
            documents=documents,
        )

    response = to_incoming_documents_response(
        documents=documents,
        categories_mapping=categories_mapping,
        view_sessions_mapping=view_sessions_mapping,
        listing_date_mapping=listing_date_mapping,
        document_fields_mapping=document_fields_mapping,
        delete_requests_mapping=delete_requests_mapping,
        recipients_mapping=recipients_mapping,
        company_edrpou=user.company_edrpou,
        parent_to_children=parent_to_children,
        child_to_parent=child_to_parent,
        uploader_info_mapping=uploader_info_mapping,
        versions_mapping=versions_mapping,
        archive_mapping=archive_mapping,
        access_settings_mapping=access_settings_mapping,
    )

    if not force_all:
        response['next_cursor'] = next_cursor
    return api_response(request, response)


@api_handler
async def retrieve_document(request: web.Request, user: User) -> web.Response:
    """Retrieve document info.

    It might be useful, when external client need to check single document
    status via public API.
    """
    app = request.app
    async with app['db'].acquire() as conn:
        options = await validate_retrieve_document(
            conn,
            user,
            dict(request.rel_url.query, document_id=request.match_info['document_id']),
        )
        document = options.document

        # Select tags if user request this field
        tags: list[DBRow] | None = None
        if options.with_tags:
            tags = await select_tags_by_documents(
                conn=conn,
                documents_ids=[document.id],
                companies_ids=[user.company_id],
            )

        access_settings_level: DocumentAccessLevel | None = None
        if options.with_access_settings:
            access_settings_level = await select_document_access_level(
                conn=conn,
                document_id=document.id,
                edrpou=user.company_edrpou,
            )

    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.document_export,
            document_id=document.id,
            document_edrpou_owner=document.edrpou_owner,
            document_title=document.title,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            email=user.email,
            role_id=user.role_id,
        )
    )

    await update_documents_date_delivered_job(user.role_id, [document.id])
    document_data = to_owner_document_response(
        document=document,
        tags=tags,
        access_settings_level=access_settings_level,
    )
    return api_response(request=request, data=document_data)


@api_handler
async def retrieve_recipient(request: web.Request, user: User) -> web.Response:
    """Retrieve summary information about current company's recipient."""

    async with request.app['db'].acquire() as conn:
        await validate_api_vendor_config(
            conn, edrpou=user.company_edrpou, vendor_enum=RecipientVendor
        )
        options = await validate_retrieve_recipient(
            conn,
            dict(
                request.rel_url.query,
                edrpou=request.match_info['edrpou'],
                user_company_id=user.company_id,
                user_edrpou=user.company_edrpou,
            ),
        )

        recipient_data = await select_recipient_summary(conn, options)

    return api_response(request, recipient_data)


@api_handler
async def retrieve_flows(request: web.Request, user: User) -> web.Response:
    """Retrieve multilateral document flows"""
    async with request.app['db'].acquire() as conn:
        document = await validate_retrieve_flows(conn, request, user)
        flows = await flow.get_flows_with_recipients(conn, document_id=document.id)

    return api_response(request, data=to_flows_data(flows))


@api_handler
async def retrieve_signatures(request: web.Request, user: User) -> web.Response:
    """Retrieve documents signatures"""
    async with request.app['db'].acquire() as conn:
        document = await validate_signatures(conn, request, user)
        signatures = await select_signatures(conn, [document.id])

    return api_response(request, data=to_signatures_data(signatures))


@api_handler
async def retrieve_review_status(request: web.Request, user: User) -> web.Response:
    """Retrieve document review status"""
    async with request.app['db'].acquire() as conn:
        document = await validate_retrieve_reviews_status(conn, request, user)
        review_status = await reviews_db.select_review_status_by_document_id(
            conn,
            company_edrpou=user.company_edrpou,
            document_id=document.id,
            # TODO: consider to add version_id to query params in case when someone
            #   from our clients will use this API in combination with document versions
            version_id=None,
        )
    return api_response(request, data=to_review_status_data(review_status))


@api_handler
async def retrieve_reviews(request: web.Request, user: User) -> web.Response:
    """Retrieve document reviews"""
    async with request.app['db'].acquire() as conn:
        options = await validate_retrieve_reviews(
            conn,
            user,
            dict(request.rel_url.query, document_id=request.match_info['document_id']),
        )
        reviews = await reviews_db.select_reviews_for_history(
            conn,
            company_id=user.company_id,
            document_id=options.document_id,
            user_email=options.user_email,
            only_last=options.only_last,
        )
    return api_response(request, data=to_reviews_data(reviews))


@api_handler
async def retrieve_review_requests(request: web.Request, user: User) -> web.Response:
    """Retrieve document review requests"""
    async with request.app['db'].acquire() as conn:
        options = await validate_retrieve_review_requests(
            conn,
            user,
            dict(request.rel_url.query, document_id=request.match_info['document_id']),
        )
        reviews_requests = await reviews_db.select_review_request_for_history(
            conn,
            company_id=user.company_id,
            document_id=options.document_id,
            user_from_email=options.user_from_email,
            user_to_email=options.user_to_email,
            only_active=options.only_active,
        )
    return api_response(request, data=to_review_requests_data(reviews_requests))


@api_handler
async def add_review_request_api_handler(request: web.Request, user: User) -> web.Response:
    """Add document review request"""
    await reviews_views.add_review_request_api(request=request, user=user)
    return api_response(request, data=None, status=201)


@api_handler
async def delete_review_request_api_handler(request: web.Request, user: User) -> web.Response:
    """Delete document review request"""
    data = await validators.validate_json_request(request)
    data['document_id'] = request.match_info['document_id']

    await reviews_views.delete_review_request_base(
        user=user,
        data=data,
        request_source=get_source(request),
    )
    return api_response(request, data=None, status=204)


@api_handler
async def upload_documents(request: web.Request, user: User) -> web.Response:
    """Batch upload documents to Vchasno service.

    This view allows to upload up to X files with total size Y and max file
    size of Z, where X/Y/Z is a numbers from configuration file for token's
    company.

    By default backend try to understand amount of signatures to mark document
    as finished from its type as well as other metadata. But endpoint allows to
    customize things via GET parameters.

    For example, to mark all documents in request first signed by recipient:

    - ``POST /api/v1/documents?first_sign_by=recipient``
    """

    async with asyncio.timeout(services.config.app.upload_timeout):
        files, options = await process_web_upload(request, user)
        document_ids = [item.id for item in files]
        await send_to_indexator(document_ids=document_ids, to_slow_queue=True)
        tracking.documents_count_api.inc(len(document_ids))

    async with services.db.acquire() as conn:
        # Create view sessions for current uploader while uploading documents
        # via public API, this allows uploader to preview documents in Vchasno
        # without logging in to Vchasno
        view_sessions_mapping = await sign_sessions.create_view_sessions_on_upload_api(
            conn=conn,
            user=user,
            documents_ids=document_ids,
        )

        documents = await select_documents_by_ids(conn, document_ids)

        recipients_mapping, uploader_info_mapping = None, None
        if options.show_recipients:
            recipients_mapping = await prepare_recipients_for_response(
                conn=conn, documents_ids=document_ids
            )
            uploader_info_mapping = await prepare_owner_info_for_response(
                conn=conn, documents_ids=document_ids
            )

        access_settings_mapping: dict[str, DocumentAccessLevel] = {}
        if options.with_access_settings:
            access_settings_mapping = await select_documents_access_level(
                conn=conn,
                documents_ids=document_ids,
                edrpou=user.company_edrpou,
            )

        categories_mapping = await prepare_document_categories_for_response(
            conn=conn,
            documents=documents,
        )

    return api_response(
        request=request,
        data=to_owner_documents_response(
            documents=documents,
            categories_mapping=categories_mapping,
            view_sessions_mapping=view_sessions_mapping,
            recipients_mapping=recipients_mapping,
            uploader_info_mapping=uploader_info_mapping,
            company_edrpou=user.company_edrpou,
            access_settings_mapping=access_settings_mapping,
        ),
        status=201,
    )


@api_handler
async def invite_coworkers(request: web.Request, user: User) -> web.Response:
    """Mass invite coworkers"""
    validate_user_permission(user, {'can_invite_coworkers'})

    json = await validators.validate_json_request(request)
    async with request.app['db'].acquire() as conn:
        emails, bad_emails = await validate_invite_coworkers(conn, json, user)

        await onboarding_utils.record_user_onboarding(
            conn=conn,
            user_id=user.id,
            has_invited_coworker=True,
        )

    # Creating records in queue
    for chunk in iter_by_chunks(emails, INVITE_USER_CHUNK_SIZE):
        await services.kafka.send_record(
            topics.INVITE_COWORKERS,
            value={
                'emails': chunk,
                'role_id': user.role_id,
                'source': get_source(request).value,
            },
        )

    logger.info(
        'Invite coworkers',
        extra={
            'company_id': user.company_id,
            'user_email': user.email,
            'emails': emails,
        },
    )
    # Providing feedback for user
    if bad_emails:
        logger.info(
            'Some coworkers inviting were ignored',
            extra={
                'company_id': user.company_id,
                'user_email': user.email,
                'bad_emails_count': len(bad_emails),
                'valid_emails': len(emails),
                'emails': bad_emails,
            },
        )
        return web.json_response(
            {
                'status': 'warning',
                'details': 'Декілька email має неправильний домен в імені.',
                'emails': bad_emails,
            }
        )
    return web.json_response(
        {
            'status': 'ok',
            'details': ('Впродовж декількох хвилин ми надішлемо запрошення на вказані email.'),
        }
    )


@api_handler
async def add_flow(request: web.Request, user: User) -> web.Response:
    document_id = request.match_info['doc_id']
    data = {
        'docs': [document_id],
        'receivers': await validators.validate_json_request(request, dict_only=False),
    }
    await flow.add_flow(user, data, source=get_source(request))
    return web.json_response(status=201)


@api_handler
async def create_delete_request_handler(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)
    data = {
        'document_ids': [request.match_info['document_id']],
        'message': data.get('message'),
    }
    delete_requests = await create_delete_request(request, user, data)
    return api_response(request, to_delete_requests_data(delete_requests), status=200)


@api_handler
async def cancel_delete_requests(request: web.Request, user: User) -> web.Response:
    document_id = request.match_info['document_id']
    data = {'documents_ids': [document_id]}
    await cancel_delete_request_util(request, user, data)
    return web.json_response(data={'status': 'ok'})


@api_handler
async def accept_delete_request_api(request: web.Request, user: User) -> web.Response:
    document_id = request.match_info['document_id']
    await accept_delete_request_util(request, user, {'documents_ids': [document_id]})
    return web.json_response(data={'status': 'ok'})


@api_handler
async def reject_delete_request(request: web.Request, user: User) -> web.Response:
    document_id = request.match_info['document_id']
    request_data = await validators.validate_json_request(request)
    data = {
        'documents_ids': [document_id],
        'reject_message': request_data.get('reject_message'),
    }
    await reject_delete_request_util(request, user, data)
    return web.json_response(data={'status': 'ok'})


async def add_signature(request: web.Request, user: User) -> web.Response:
    data = await validate_add_signature_api(request)

    async with services.db.acquire() as conn:
        ctx = await signatures_utils.add_signature(
            conn=conn,
            user=user,
            data=data,
        )
        await add_signature_schedule_async_jobs(
            conn=conn,
            ctx=ctx,
            request_source=get_source(request),
        )
        await update_processed_status(
            conn,
            ctx.document_with_uploader.id,
            status_id=ctx.document_with_uploader.status_id,
            processed=False,
            user_company_id=user.company_id,
        )
        await documents_utils.autosend_document_on_sign(
            conn=conn,
            document_id=ctx.document_with_uploader.id,
            user=user,
            company_edrpou=user.company_edrpou,
            request_source=get_source(request),
        )

    await send_to_indexator(document_ids=[ctx.document_with_uploader.id], to_slow_queue=True)

    tracking.signatures_count_api.inc()
    return web.json_response(status=HTTPStatus.CREATED)


@api_handler
async def set_signers(request: web.Request, user: User) -> web.Response:
    document_id = request.match_info['document_id']
    raw_data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(UpdateSignersSchema, raw_data)

    if valid_data.signer_entities is not None:
        signer_entities = [
            {
                'type': item.type,
                'id': item.id,
            }
            for item in valid_data.signer_entities
        ]
    elif valid_data.roles is not None:
        signer_entities = [
            {
                'type': 'role',
                'id': role_id,
            }
            for role_id in valid_data.roles
        ]
    else:
        raise InvalidRequest(reason=_('signer_entities або roles повинні бути заповнені'))

    data = {
        'document_id': document_id,
        'signers_settings': {
            'parallel_signing': valid_data.is_parallel,
            'entities': signer_entities,
        },
    }
    async with services.db.acquire() as conn:
        await validate_set_signers(conn, signer_entities, user.company_id)

        await handle_document_update(
            conn=conn,
            user=user,
            data=data,
            request_source=get_source(request),
            signers_source=SignersSource.api,
            reviewers_source=ReviewRequestSource.api,
        )

    await send_to_indexator(document_ids=[document_id], to_slow_queue=True)
    return web.json_response()


@api_handler
async def update_document_info(request: web.Request, user: User) -> web.Response:
    """
    Update basic information about a document using public API, like title, number, date, etc.
    """

    raw_data = await validators.validate_json_request(request)
    raw_data['document_id'] = request.match_info['document_id']
    data = validators.validate_pydantic(UpdateDocumentInfoSchema, raw_data)

    async with services.db.acquire() as conn:
        await handle_document_update(
            conn=conn,
            user=user,
            data=data.to_update_dict(),
            request_source=get_source(request),
            signers_source=SignersSource.api,
            reviewers_source=ReviewRequestSource.api,
        )

        document = await get_expected_document(conn, document_id=data.document_id)

    await send_to_indexator(document_ids=[data.document_id], to_slow_queue=True)

    response_data = to_owner_document_response(document=document._row)
    return api_response(request=request, data=response_data)


@api_handler
async def update_document_access_settings(request: web.Request, user: User) -> web.Response:
    """
    Update document access settings using public API.
    """

    raw_data = await validators.validate_json_request(request)
    raw_data['document_id'] = request.match_info['document_id']
    data = validators.validate_pydantic(UpdateDocumentAccessSettingsSchema, raw_data)

    async with services.db.acquire() as conn:
        await handle_document_update(
            conn=conn,
            user=user,
            data=data.to_update_dict(),
            request_source=get_source(request),
            signers_source=SignersSource.api,
            reviewers_source=ReviewRequestSource.api,
        )

    await send_to_indexator(document_ids=[data.document_id], to_slow_queue=True)

    return web.json_response()


@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_company(request: web.Request, user: User) -> web.Response:
    edrpou = request.match_info['edrpou']
    data = await validators.validate_json_request(request)
    async with request.app['db'].acquire() as conn:
        company = await auth.update_company_by_edrpou(
            conn=conn,
            edrpou=edrpou,
            data={
                'name': data['name'],
                'full_name': data['full_name'],
            },
        )

    if company:
        await send_jobs_about_company_update(company_id=company.id)

    logger.info('Company changed by super admin', extra={'admin': user.email, 'edrpou': edrpou})
    return web.Response()


@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def check_flag(request: web.Request, user: User) -> web.Response:
    flag: str = request.match_info['flag']

    value = get_flag(FeatureFlags(flag))
    return web.json_response({flag: value})


@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def sync_listing(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)

    role_id = data.get('role_id')
    if role_id:
        await sync_listing_job(role_id)
        return web.Response()

    edrpou = data['edrpou']

    async with request.app['db'].acquire() as conn:
        roles = await select_all(
            conn,
            (
                sa.select([role_table.c.id])
                .select_from(role_company_join)
                .where(company_table.c.edrpou == edrpou)
            ),
        )

    for role in roles:
        await sync_listing_job(role.id, delay=0)

    return web.Response()


@api_handler
async def mark_as_processed(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(DocumentIdsSchema, data)

    async with services.db.acquire() as conn:
        updated_ids = await upsert_processed_status(
            conn,
            document_ids=set(valid_data.document_ids),
            processed=True,
            user=user,
        )
    if updated_ids:
        await send_to_indexator(services.redis, updated_ids, to_slow_queue=True)
    return web.json_response({'updated_ids': updated_ids})


@api_handler
async def lock_delete_request(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(DocumentIdsSchema, data)

    if not user.company_edrpou:
        raise AccessDenied()

    if not valid_data.document_ids:
        return web.json_response({'updated_ids': []})

    async with request.app['db'].acquire() as conn:
        await validate_company_documents_access(
            conn=conn,
            documents_ids=valid_data.document_ids,
            company_edrpou=user.company_edrpou,
        )
        updated_ids = await upsert_delete_document_settings(
            conn=conn,
            document_ids=valid_data.document_ids,
            is_delete_request_required=True,
        )

    return web.json_response({'updated_ids': updated_ids})


@api_handler
async def unlock_delete_request(request: web.Request, user: User) -> web.Response:
    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(DocumentIdsSchema, data)

    if not user.company_edrpou:
        raise AccessDenied()

    if not valid_data.document_ids:
        return web.json_response({'updated_ids': []})

    async with request.app['db'].acquire() as conn:
        await validate_company_documents_access(
            conn=conn,
            documents_ids=valid_data.document_ids,
            company_edrpou=user.company_edrpou,
        )
        updated_ids = await delete_document_settings(
            conn=conn,
            document_ids=valid_data.document_ids,
        )

    return web.json_response({'updated_ids': updated_ids})
