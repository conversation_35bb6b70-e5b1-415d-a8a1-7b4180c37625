import datetime
from http import HTTPStatus
from unittest.mock import AsyncMock

import pytest

from api.private.super_admin.constants import SURVEY_TO_REDIS_KEY
from app.auth.db import select_role_by_id, update_role
from app.auth.enums import RoleActivationSource, RoleStatus
from app.auth.utils import get_company_config, update_user
from app.billing.db import (
    select_active_ultimate_rate,
)
from app.billing.enums import (
    AccountRate,
    AccountStatus,
    AccountType,
    CompanyLimit,
)
from app.billing.types import (
    BillingCompanyConfig,
)
from app.billing.utils import get_billing_company_config
from app.document_automation.db import select_automation
from app.document_automation.enums import DocumentAutomationStatus
from app.document_automation.tables import (
    document_automation_condition_table,
    document_automation_template_table,
)
from app.documents.db import (
    select_document,
    select_document_recipients,
)
from app.lib.datetime_utils import local_now
from app.lib.enums import Source, SuperAdminActionType, UserRole
from app.reviews.db import (
    insert_review_request,
    select_review_requests,
    select_review_status_by_document_id,
)
from app.reviews.enums import ReviewStatus, ReviewType
from app.reviews.tests.utils import insert_review
from app.reviews.types import ReviewRequest
from app.reviews.utils import start_reviews_update_transaction
from app.services import services
from app.templates.tests.utils import prepare_template
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    TEST_COMPANY_EDRPOU_2,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    TEST_USER_PHONE,
    TEST_UUID,
    VCHASNO_EDRPOU,
    fetch_graphql,
    get_raw_user,
    get_super_admin_actions,
    insert_values,
    prepare_auth_headers,
    prepare_billing_account,
    prepare_client,
    prepare_document_data,
    prepare_group,
    prepare_review_requests,
    prepare_user_data,
    set_company_config,
)

PRIVATE_API_CREATE_USER = '/api/private/users/autogenerate'
PRIVATE_API_SOFT_DELETE_USER = '/api/private/users/soft-delete'
PRIVATE_API_LINK_BALANCE = '/api/private/companies/{parent_edrpou}/link-balance'
PRIVATE_API_CHANGE_EMAIL = '/api/private/change-email'
PRIVATE_API_ACTIVATE_ROLE = '/api/private/roles/{role_id}/activate'
PRIVATE_API_UPDATE_AUTH_PHONE = '/api/private/auth/update-auth-phone'
UPDATE_BILLING_CONFIG_URL = '/api/private/billing/configs'
PRIVATE_API_MANAGE_AWS_SES_BLACKLIST_URL = '/api/private/manage-aws-ses-blacklist'


@pytest.mark.parametrize(
    'role_status, expected_status',
    [
        (
            RoleStatus.active,
            HTTPStatus.BAD_REQUEST,
        ),
        (
            RoleStatus.deleted,
            HTTPStatus.OK,
        ),
        (
            RoleStatus.user_deleted,
            HTTPStatus.OK,
        ),
        (
            RoleStatus.pending,
            HTTPStatus.OK,
        ),
        (
            RoleStatus.blocked_2fa,
            HTTPStatus.OK,
        ),
    ],
)
async def test_activate_user_role(aiohttp_client, role_status, expected_status):
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )
    email = TEST_USER_EMAIL + 'test1'
    user = await prepare_user_data(
        app=app,
        company_edrpou=TEST_COMPANY_EDRPOU,
        email=email,
        role_status=RoleStatus.active,
    )
    async with services.db.acquire() as conn:
        await update_role(
            conn=conn,
            role_id=user.role_id,
            data={'status': role_status},
        )

    response = await client.post(
        path=PRIVATE_API_ACTIVATE_ROLE.format(role_id=user.role_id),
        json={'status': 'active'},
        headers=prepare_auth_headers(admin),
    )
    assert response.status == expected_status
    if expected_status == HTTPStatus.OK:
        async with services.db.acquire() as conn:
            role = await select_role_by_id(conn, user.role_id)
            assert role.status == RoleStatus.active
            assert role.activated_by == admin.role_id
            assert role.activation_source == RoleActivationSource.super_admin
            assert role.date_activated is not None


@pytest.mark.parametrize(
    'status_code, edrpous, expected_found, expected_not_found',
    [
        (200, [], [], None),
        (400, ['1'], None, None),
        (400, [1], None, None),
        (200, ['41231992'], ['41231992'], None),
        (200, ['55555555'], [], ['55555555']),
    ],
)
async def test_link_companies_balance(
    aiohttp_client,
    status_code,
    edrpous,
    expected_found,
    expected_not_found,
):
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_USER_EMAIL,
        company_edrpou=VCHASNO_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )

    response = await client.patch(
        path=PRIVATE_API_LINK_BALANCE.format(parent_edrpou=VCHASNO_EDRPOU),
        json={
            'edrpous': edrpous,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == status_code
    data = await response.json()

    assert data.get('data') == expected_found
    assert data.get('not_found') == expected_not_found

    if status_code == 200 and expected_found:
        async with app['db'].acquire() as conn:
            config = await get_company_config(conn, company_edrpou=VCHASNO_EDRPOU)
        assert config.parent_company == VCHASNO_EDRPOU
        assert config.allow_parent_company_pay_for_documents is True


@pytest.mark.parametrize('is_email_confirmed', [True, False])
async def test_sa_create_user(aiohttp_client, is_email_confirmed, mailbox):
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_USER_EMAIL,
        company_edrpou=VCHASNO_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )

    response = await client.post(
        path=PRIVATE_API_CREATE_USER,
        json={
            'email': '<EMAIL>',
            'is_email_confirmed': is_email_confirmed,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.CREATED, await response.json()
    data = await response.json()

    user = await get_raw_user(user_id=data['id'])
    assert user.email == '<EMAIL>'
    assert user.is_autogenerated_password is True
    assert user.email_confirmed == is_email_confirmed
    assert user.registration_completed is False

    assert len(mailbox) == 1


async def test_delete_user(aiohttp_client, concierge_emulation, crm_box):
    """
    Test that SA is able to correctly soft delete user.
    """
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        first_name='Test',
        second_name='Test',
        last_name='Test',
        phone='+380123456789',
        google_id='123',
        microsoft_id='321',
        apple_id='789',
        telegram_chat_id='456',
    )
    admin = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )

    concierge_session_id = concierge_emulation.generate_session_id()
    concierge_emulation.sign_in(session_id=concierge_session_id, user_id=user.id)
    concierge_emulation.update_vchasno_profile(
        user_id=user.id,
        profile={
            'email': user.email,
            'first_name': user.first_name,
            'second_name': user.second_name,
            'last_name': user.last_name,
            'phone': user.phone,
            'is_email_confirmed': user.email_confirmed,
            'google_id': user.google_id,
            'microsoft_id': user.microsoft_id,
            'apple_id': user.apple_id,
            'telegram_chat_id': user.telegram_chat_id,
        },
    )

    # Act
    response = await client.post(
        path=PRIVATE_API_SOFT_DELETE_USER,
        json={'email': user.email},
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK, await response.json()

    # Assert
    async with app['db'].acquire() as conn:
        role = await select_role_by_id(conn=conn, role_id=user.role_id)
    new_user = await get_raw_user(user_id=user.id)

    assert new_user.email != user.email
    assert new_user.first_name == ''
    assert new_user.second_name == ''
    assert new_user.last_name == ''
    assert new_user.phone is None
    assert new_user.google_id is None
    assert new_user.microsoft_id is None
    assert new_user.apple_id is None
    assert new_user.telegram_chat_id is None

    assert role.status == RoleStatus.user_deleted

    assert concierge_emulation.count_user_session(user_id=user.id) == 0
    assert concierge_emulation.get_user_profile(user_id=user.id) == {
        'email': new_user.email,
        'first_name': '',
        'last_name': '',
        'phone': None,
        'is_email_confirmed': False,
        'is_super_admin': False,
        'auth_phone': None,
    }

    assert len(crm_box) == 2


async def test_change_email(aiohttp_client, concierge_emulation, crm_box):
    app, client, coworker1 = await prepare_client(
        aiohttp_client, email=TEST_DOCUMENT_EMAIL_RECIPIENT
    )
    coworker2 = await prepare_user_data(app, email=TEST_DOCUMENT_EMAIL_RECIPIENT + 'test1')
    recipient = await prepare_user_data(
        app,
        company_edrpou=VCHASNO_EDRPOU,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT + 'test2',
    )

    admin = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )

    document = await prepare_document_data(
        app,
        admin,
        create_document_access_for_recipients=True,
        document_recipients=[
            {
                'edrpou': coworker1.company_edrpou,
                'emails': [coworker1.email, coworker2.email],
            },
            {
                'edrpou': recipient.company_edrpou,
                'emails': [recipient.email],
            },
        ],
    )

    template1 = await insert_values(
        app=app,
        table=document_automation_template_table,
        company_id=coworker1.company_id,
        name='Template1',
        is_active=True,
        set_review=True,
        review_settings={
            'is_required': True,
            'reviewers_ids': [coworker1.role_id, coworker2.role_id, TEST_UUID],
        },
        set_signers=True,
        signers_settings={
            'is_ordered': False,
            'signers_ids': [coworker2.role_id],
        },
        created_by=coworker1.role_id,
    )
    automation = await insert_values(
        app=app,
        table=document_automation_condition_table,
        company_id=coworker1.company_id,
        conditions={
            'and': [
                {'or': [{'eq': ['#document_recipients.edrpou', '21681926']}]},
                {'or': [{'eq': ['#document_category', '5']}]},
                {
                    'or': [
                        {
                            'eq': [
                                '#document_recipients.email',
                                coworker1.email,
                            ],
                        },
                        {
                            'eq': [
                                '#document_recipients.email',
                                coworker2.email,
                            ],
                        },
                    ]
                },
            ]
        },
        template_id=template1.id,
        order=1,
        status=DocumentAutomationStatus.disabled,
    )

    concierge_session_id = concierge_emulation.generate_session_id()
    concierge_emulation.sign_in(session_id=concierge_session_id, user_id=coworker1.id)
    concierge_emulation.update_vchasno_profile(
        user_id=coworker1.id,
        profile={'email': coworker1.email},
    )
    assert concierge_emulation.count_user_session(user_id=coworker1.id) == 1

    # Act
    new_email = coworker1.email + 'aa'
    response = await client.post(
        path=PRIVATE_API_CHANGE_EMAIL,
        json={
            'email': coworker1.email,
            'new_email': new_email,
        },
        headers=prepare_auth_headers(admin),
    )

    # Assert
    new_user = await get_raw_user(user_id=coworker1.id)
    async with app['db'].acquire() as conn:
        document_recipients = sorted(
            await select_document_recipients(
                conn=conn,
                document_id=document.id,
            ),
            key=lambda x: x.edrpou,
        )
        document_automation = await select_automation(conn, automation.id)
        document_updated = await select_document(conn, document_id=document.id)

        assert response.status == HTTPStatus.OK, await response.json()

    # email is changed for user
    assert new_user.email == new_email

    # check that email changed for document.recipient_email
    assert document_updated.email_recipient == ', '.join([new_email, coworker2.email])

    # check that email changed and no other document_recipients has been changed
    assert sorted(document_recipients[0].emails) == sorted([new_email, coworker2.email])
    assert document_recipients[1].emails == [recipient.email]

    # check that email changed
    assert document_automation.conditions == {
        'and': [
            {'or': [{'eq': ['#document_recipients.edrpou', '21681926']}]},
            {'or': [{'eq': ['#document_category', '5']}]},
            {
                'or': [
                    {
                        'eq': [
                            '#document_recipients.email',
                            new_email,
                        ]
                    },
                    {
                        'eq': [
                            '#document_recipients.email',
                            coworker2.email,
                        ],
                    },
                ]
            },
        ]
    }

    # check that email changed for concierge
    assert concierge_emulation.count_user_session(user_id=coworker1.id) == 1
    assert concierge_emulation.get_user_profile(user_id=coworker1.id) == {
        'email': new_email,  # updated email
        'first_name': coworker1.first_name,
        'last_name': coworker1.last_name,
        'phone': coworker1.phone,
        'is_email_confirmed': True,
        'is_super_admin': False,
        'auth_phone': None,
    }

    assert len(crm_box) == 1
    assert crm_box[0] == {
        'email': new_email,
        'vchasno_id': coworker1.id,
        'mobile_phone': coworker1.phone,
    }


async def test_change_email_by_user_id(aiohttp_client, concierge_emulation, crm_box):
    app, client, coworker = await prepare_client(
        aiohttp_client,
        email='** Якийсь неправильний імейл **',
    )
    admin = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    concierge_session = concierge_emulation.generate_session_id()
    concierge_emulation.sign_in(session_id=concierge_session, user_id=coworker.id)
    concierge_emulation.update_vchasno_profile(
        user_id=coworker.id,
        profile={'email': coworker.email},
    )
    assert concierge_emulation.count_user_session(user_id=coworker.id) == 1

    new_email = '<EMAIL>'
    response = await client.post(
        path=PRIVATE_API_CHANGE_EMAIL,
        json={'user_id': coworker.id, 'new_email': new_email},
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK

    new_user = await get_raw_user(user_id=coworker.id)
    assert new_user.email == new_email

    assert concierge_emulation.get_user_profile(user_id=coworker.id) == {
        'email': new_email,
        'first_name': coworker.first_name,
        'last_name': coworker.last_name,
        'phone': coworker.phone,
        'auth_phone': None,
        'is_email_confirmed': True,
        'is_super_admin': False,
    }


async def test_change_email_validation(aiohttp_client):
    app, client, coworker1 = await prepare_client(aiohttp_client)

    admin = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )

    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )

    # Try to update on already exist email
    response = await client.post(
        path=PRIVATE_API_CHANGE_EMAIL,
        json={
            'email': coworker1.email,
            'new_email': coworker1.email,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.BAD_REQUEST, await response.json()

    # Try to update on old email, which not exist in system
    response = await client.post(
        path=PRIVATE_API_CHANGE_EMAIL,
        json={
            'email': '<EMAIL>',
            'new_email': '<EMAIL>',
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.NOT_FOUND, await response.json()


async def test_update_billing_company_config(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    admin = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app=app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    async with services.db.acquire() as conn:
        # By default company has default config options
        billing_config = await get_billing_company_config(conn, company_id=user.company_id)

        assert billing_config.model_dump() == BillingCompanyConfig().model_dump()

        response = await client.patch(
            path=UPDATE_BILLING_CONFIG_URL,
            json={
                'company_id': user.company_id,
                'config': {
                    'can_manage_employee_access': True,
                    'reviews_enabled': True,
                    'max_versions_count': 10,
                    'max_required_fields_count': 20,
                },
            },
            headers=prepare_auth_headers(admin),
        )
        assert response.status == HTTPStatus.OK, await response.json()

        billing_config = await get_billing_company_config(conn, company_id=user.company_id)

        assert (
            billing_config.model_dump()
            == BillingCompanyConfig(
                can_manage_employee_access=True,
                reviews_enabled=True,
                max_versions_count=10,
                max_required_fields_count=20,
            ).model_dump()
        )

        # Check that further updates only fields that are passed
        response = await client.patch(
            path=UPDATE_BILLING_CONFIG_URL,
            json={
                'company_id': user.company_id,
                'config': {
                    'can_manage_employee_access': False,
                    'max_versions_count': 100,
                },
            },
            headers=prepare_auth_headers(admin),
        )

        assert response.status == HTTPStatus.OK, await response.json()

        billing_config = await get_billing_company_config(conn, company_id=user.company_id)

        assert (
            billing_config.model_dump()
            == BillingCompanyConfig(
                can_manage_employee_access=False,
                reviews_enabled=True,
                max_versions_count=100,
                max_required_fields_count=20,
            ).model_dump()
        )


async def test_update_billing_account_price_per_user(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    admin = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app=app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    async with services.db.acquire() as conn:
        # By default company has default config options
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_ultimate(),
            status=AccountStatus.active,
            date_expired=local_now() + datetime.timedelta(days=365),
            billing_company_config={CompanyLimit.employees.value: 10},
            type=AccountType.client_rate,
        )

        response = await client.post(
            path='/api/private/billing/update-price-per-user',
            json={
                'edrpou': user.company_edrpou,
                'price_per_user': 100052,
            },
            headers=prepare_auth_headers(admin),
        )
        assert response.status == HTTPStatus.OK
        resp = await response.json()
        assert resp['status'] == 'ok'
        async with services.db.acquire() as conn:
            ultimate_rate = await select_active_ultimate_rate(conn, edrpou=user.company_edrpou)
        assert ultimate_rate.price_per_user == 100052


async def test_recalculate_review_status(aiohttp_client):
    async def assert_review_status(conn, document_id, user, status):
        review = await select_review_status_by_document_id(
            conn=conn,
            document_id=document_id,
            company_edrpou=user.company_edrpou,
            version_id=None,
        )
        assert review.status == status

    app, client, user = await prepare_client(aiohttp_client)
    # add recipient without review to make sure recalculation works as well
    recipient = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_COMPANY_EDRPOU_2
    )
    document = await prepare_document_data(app, owner=user, another_recipients=[recipient])

    admin = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app=app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    # add preview request and review but do not update review status
    async with services.db.acquire() as conn:
        async with start_reviews_update_transaction(
            conn=conn,
            documents_ids=[document.id],
            user=user,
            request_source=Source.api_internal,
        ):
            await insert_review_request(
                conn,
                {
                    'document_id': document.id,
                    'from_role_id': user.role_id,
                    'to_role_id': user.role_id,
                },
            )
        # this insert does not trigger recalculation because it's out of
        # start_reviews_update_transaction
        # so we can make recalculation manually later
        await insert_review(
            conn,
            {
                'document_id': document.id,
                'role_id': user.role_id,
                'type': ReviewType.approve,
            },
        )
        await assert_review_status(conn, document.id, user, ReviewStatus.pending)

    # trigger recalculation
    resp = await client.post(
        f'/api/private/documents/{document.id}/recalculate-review-status',
        headers=prepare_auth_headers(admin),
    )
    assert resp.status == HTTPStatus.OK
    async with services.db.acquire() as conn:
        await assert_review_status(conn, document.id, user, ReviewStatus.approved)


async def test_manage_aws_ses_manage_blacklist(aiohttp_client, monkeypatch):
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    mock_put_suppressed_destination = AsyncMock()
    mock_get_suppressed_destination = AsyncMock()
    mock_delete_suppressed_destination = AsyncMock()

    monkeypatch.setattr(
        services.aws_ses_client,
        'put_suppressed_destination',
        mock_put_suppressed_destination,
    )
    monkeypatch.setattr(
        services.aws_ses_client,
        'get_suppressed_destination',
        mock_get_suppressed_destination,
    )
    monkeypatch.setattr(
        services.aws_ses_client,
        'delete_suppressed_destination',
        mock_delete_suppressed_destination,
    )

    test_email = '<EMAIL>'

    # Test block action
    block_response = await client.post(
        path=PRIVATE_API_MANAGE_AWS_SES_BLACKLIST_URL,
        json={'action': 'block', 'email': test_email},
        headers=prepare_auth_headers(admin),
    )
    assert block_response.status == HTTPStatus.OK
    block_data = await block_response.json()
    assert block_data == {'status': 'success'}
    mock_put_suppressed_destination.assert_awaited_once_with(
        EmailAddress=test_email,
        Reason='COMPLAINT',
    )
    actions = await get_super_admin_actions()
    actions = [a for a in actions if a.type == SuperAdminActionType.manage_aws_ses_blacklist]
    assert len(actions) == 1
    assert actions[0].extra_details == [{'action': 'block', 'email': test_email}]

    # Test check action (blocked)
    mock_get_suppressed_destination.return_value = {'SomeKey': 'SomeValue'}  # Dummy response
    check_blocked_response = await client.post(
        path=PRIVATE_API_MANAGE_AWS_SES_BLACKLIST_URL,
        json={'action': 'check', 'email': test_email},
        headers=prepare_auth_headers(admin),
    )
    assert check_blocked_response.status == HTTPStatus.OK
    check_blocked_data = await check_blocked_response.json()
    assert check_blocked_data['status'] == 'blocked'
    mock_get_suppressed_destination.assert_awaited_once_with(EmailAddress=test_email)
    actions = await get_super_admin_actions()
    actions = [a for a in actions if a.type == SuperAdminActionType.manage_aws_ses_blacklist]
    assert len(actions) == 2

    # Test unblock action
    unblock_response = await client.post(
        path=PRIVATE_API_MANAGE_AWS_SES_BLACKLIST_URL,
        json={'action': 'unblock', 'email': test_email},
        headers=prepare_auth_headers(admin),
    )
    assert unblock_response.status == HTTPStatus.OK
    unblock_data = await unblock_response.json()
    assert unblock_data == {'status': 'success'}
    mock_delete_suppressed_destination.assert_awaited_once_with(EmailAddress=test_email)
    actions = await get_super_admin_actions()
    actions = [a for a in actions if a.type == SuperAdminActionType.manage_aws_ses_blacklist]
    assert len(actions) == 3

    # Test check action (unblocked) - should throw NotFound
    def _raise_not_found(*args, **kwargs):
        raise services.aws_ses_client.exceptions.NotFoundException(
            error_response={},
            operation_name='GetSuppressedDestination',
        )

    mock_get_suppressed_destination.side_effect = _raise_not_found

    check_unblocked_response = await client.post(
        path=PRIVATE_API_MANAGE_AWS_SES_BLACKLIST_URL,
        json={'action': 'check', 'email': test_email},
        headers=prepare_auth_headers(admin),
    )
    assert check_unblocked_response.status == HTTPStatus.OK
    check_unblocked_data = await check_unblocked_response.json()
    assert check_unblocked_data['status'] == 'not_blocked'
    actions = await get_super_admin_actions()
    actions = [a for a in actions if a.type == SuperAdminActionType.manage_aws_ses_blacklist]
    assert len(actions) == 4


async def test_template_migration(aiohttp_client):
    """Just test that migration handler works"""
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )
    user = await prepare_user_data(app, email='<EMAIL>', company_edrpou=TEST_COMPANY_EDRPOU_2)
    template = await prepare_template(user)

    resp = await client.post(
        f'/api/private/templates-migrate/{template.id}',
        headers=prepare_auth_headers(admin),
    )
    assert resp.status == 200


async def test_sa_store_survey_user_list(aiohttp_client):
    if not SURVEY_TO_REDIS_KEY:
        pytest.skip('SURVEY_TO_REDIS_KEY is not set')

    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_special_features': True},
    )
    await set_company_config(
        app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )
    survey = list(SURVEY_TO_REDIS_KEY.keys())[0]
    email = '<EMAIL>'
    resp = await client.post(
        '/api/private/survey-user',
        headers=prepare_auth_headers(admin),
        json={'emails': [email], 'survey_id': survey},
    )
    assert resp.status == 200

    is_member = await services.redis.sismember(SURVEY_TO_REDIS_KEY[survey], email)
    assert bool(is_member) is True

    # just to make sure that graphql query works
    # to not create one more test
    user = await prepare_user_data(app, email=email)
    user2 = await prepare_user_data(app, email=email + '42')

    query = '{ currentRole { user { activeSurveys } } }'

    # user1 should see survey
    headers = prepare_auth_headers(user)
    result = await fetch_graphql(client, query, headers)
    assert result['currentRole']['user']['activeSurveys'] == [survey]

    # check that user2 does not have survey
    headers = prepare_auth_headers(user2)
    result = await fetch_graphql(client, query, headers)
    assert result['currentRole']['user']['activeSurveys'] == []


# TODO: Remove test after migration.
#  Check/add test for history review-requests
async def test_delete_review_requests_duplicates(aiohttp_client):
    """Just test that job"""
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )

    await set_company_config(
        app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    user1 = await prepare_user_data(app, email='<EMAIL>')
    user2 = await prepare_user_data(app, email='<EMAIL>')

    group1 = await prepare_group(app, 'test group', user1, [user1])
    group2 = await prepare_group(app, 'test group2', user1, [user2])
    document = await prepare_document_data(app, admin)
    document2 = await prepare_document_data(app, admin)
    await prepare_document_data(app, admin)

    # Add reviewer to review, make review required
    await prepare_review_requests(client, document, admin, reviewers=[group1])
    await prepare_review_requests(client, document, admin, reviewers=[group1, group2])
    await prepare_review_requests(client, document, admin, reviewers=[group1, group2, user2])

    await prepare_review_requests(client, document2, admin, reviewers=[group1])
    await prepare_review_requests(client, document2, admin, reviewers=[group1, user1])

    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(
            conn,
            company_id=admin.company_id,
            document_ids=[document.id, document2.id],
            is_all_requests=True,
        )
        assert len(review_requests) == 5

    resp = await client.post(
        '/api/private/jobs/vchasno-test-delete-review-requests-duplicates',
        headers=prepare_auth_headers(admin),
    )
    assert resp.status == 200

    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(
            conn,
            company_id=admin.company_id,
            document_ids=[document.id, document2.id],
            is_all_requests=True,
        )

        review_requests = [ReviewRequest.from_row(rr) for rr in review_requests]

        assert len(review_requests) == 5

    resp = await client.post(
        '/api/private/jobs/vchasno-test-delete-review-requests-duplicates',
        headers=prepare_auth_headers(admin),
    )
    assert resp.status == 200

    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(
            conn,
            company_id=admin.company_id,
            document_ids=[document.id, document2.id],
            is_all_requests=True,
        )
        assert len(review_requests) == 5
    assert resp.status == 200


async def test_block_user(aiohttp_client, mailbox, email_templates_renderer):
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )

    await set_company_config(
        app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    user = await prepare_user_data(
        app,
        email='<EMAIL>',
        password=TEST_USER_PASSWORD,
    )

    response = await client.post(
        '/api/private/block-user',
        json={'email': user.email},
        headers=prepare_auth_headers(admin),
    )
    assert response.status == 200

    assert len(mailbox) == 1
    assert mailbox[0]['To'] == user.email

    assert len(email_templates_renderer) == 1
    reset_url = email_templates_renderer[0]['context'].pop('reset_password_url')
    assert '/password/recover' in reset_url

    assert email_templates_renderer[0] == {
        'context': {'first_name': user.first_name},
        'template_name': 'your_account_is_blocked',
    }

    actions = await get_super_admin_actions()
    actions = [action for action in actions if action.type == SuperAdminActionType.block_user]
    assert len(actions) == 1
    assert actions[0].email == admin.email
    assert actions[0].extra_details == [{'email': user.email}]


async def test_ban_user(aiohttp_client):
    """Test banning and unbanning a user"""
    app, client, admin = await prepare_client(
        aiohttp_client=aiohttp_client,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )

    await set_company_config(
        app,
        company_id=admin.company_id,
        master=True,
        admin_is_superadmin=True,
    )

    user = await prepare_user_data(
        app,
        email='<EMAIL>',
        password=TEST_USER_PASSWORD,
    )

    # Test banning user by email
    response = await client.post(
        path='/api/private/ban-user',
        json={
            'email': user.email,
            'reason': 'Test ban reason',
            'is_banned': True,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK

    raw_user = await get_raw_user(user_id=user.id)
    assert raw_user.is_banned

    actions = await get_super_admin_actions()
    ban_actions = [action for action in actions if action.type == SuperAdminActionType.ban_user]
    assert len(ban_actions) == 1
    assert ban_actions[0].email == admin.email
    assert ban_actions[0].extra_details == [
        {
            'user_id': user.id,
            'email': user.email,
            'is_banned': True,
            'reason': 'Test ban reason',
        }
    ]

    # Test unbanning user by user_id
    response = await client.post(
        path='/api/private/ban-user',
        json={
            'user_id': user.id,
            'reason': 'Test unban reason',
            'is_banned': False,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK

    raw_user = await get_raw_user(user_id=user.id)
    assert not raw_user.is_banned

    # Verify second super admin action was logged
    actions = await get_super_admin_actions()
    ban_actions = [action for action in actions if action.type == SuperAdminActionType.ban_user]
    assert len(ban_actions) == 2
    assert ban_actions[1].email == admin.email
    assert ban_actions[1].extra_details == [
        {
            'user_id': user.id,
            'email': user.email,
            'is_banned': False,
            'reason': 'Test unban reason',
        }
    ]


async def test_update_auth_phone(aiohttp_client):
    """
    Test that super admin can enable/disable phone authentication for a user.
    """
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email='<EMAIL>',
        phone=TEST_USER_PHONE,
    )

    admin = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        company_edrpou=VCHASNO_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )

    async with app['db'].acquire() as conn:
        await update_user(conn, user_id=user.id, data={'is_phone_verified': True})

    response = await client.post(
        path=PRIVATE_API_UPDATE_AUTH_PHONE,
        json={
            'email': user.email,
            'enable': True,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK

    updated_user = await get_raw_user(user_id=user.id)
    assert updated_user.auth_phone == TEST_USER_PHONE

    actions = await get_super_admin_actions()
    phone_auth_actions = [
        action for action in actions if action.type == SuperAdminActionType.update_auth_phone
    ]
    assert len(phone_auth_actions) == 1
    assert phone_auth_actions[0].email == admin.email
    assert phone_auth_actions[0].extra_details[0]['auth_phone'] == TEST_USER_PHONE
    assert phone_auth_actions[0].extra_details[0]['prev_auth_phone'] is None
    assert phone_auth_actions[0].extra_details[0]['user_id'] == user.id

    response = await client.post(
        path=PRIVATE_API_UPDATE_AUTH_PHONE,
        json={
            'email': user.email,
            'enable': False,
        },
        headers=prepare_auth_headers(admin),
    )
    assert response.status == HTTPStatus.OK

    updated_user = await get_raw_user(user_id=user.id)
    assert updated_user.auth_phone is None

    actions = await get_super_admin_actions()
    phone_auth_actions = [
        action for action in actions if action.type == SuperAdminActionType.update_auth_phone
    ]
    assert len(phone_auth_actions) == 2
    assert phone_auth_actions[1].email == admin.email
    assert phone_auth_actions[1].extra_details[0]['auth_phone'] is None
    assert phone_auth_actions[1].extra_details[0]['prev_auth_phone'] == TEST_USER_PHONE
    assert phone_auth_actions[1].extra_details[0]['user_id'] == user.id
