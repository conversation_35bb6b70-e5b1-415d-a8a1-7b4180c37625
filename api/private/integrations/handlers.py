import asyncio
import logging
from http import HTTPStatus

from aiohttp import web

from api.errors import (
    DoesNotExist,
    InvalidRequest,
    Object,
)
from api.private.integrations.db import select_registered_companies
from api.private.integrations.enums import PrivateIntegration
from api.private.integrations.schemas import CreateVchasnoUserSchema
from api.private.integrations.utils import invite_user, prepare_signature_response_data_for_kep
from api.private.integrations.validators import (
    GetActiveRolesSchema,
    InviteUserSchema,
    KasaRegistrationValidator,
    ProcessPhoneAuth,
    SendPhoneAuthCodeSchema,
    validate_create_vchasno_user_common,
    validate_get_companies,
    validate_get_company_billing_accounts,
    validate_get_document_signatures,
    validate_process_phone_auth,
    validate_registration_kasa,
    validate_send_phone_auth,
)
from api.utils import api_response
from app.auth.db import select_base_user, select_roles
from app.auth.enums import RoleStatus
from app.auth.phone_auth import utils as phone_auth
from app.auth.types import Role
from app.auth.utils import create_user
from app.auth.validators import validate_login
from app.billing.db import select_accounts_raw
from app.i18n import _
from app.lib import validators
from app.lib.helpers import json_response
from app.lib.integrations import private_integration
from app.lib.sender.enums import SenderProject
from app.openapi.decorators import openapi_docs, openapi_docs_ignore
from app.registration import utils as registration_utils
from app.registration.enums import RegistrationSource
from app.services import services
from app.vchasno_profile.types import CreateVchasnoUserResponse

logger = logging.getLogger(__name__)

OPENAPI_TAG_KASA = 'KASA'
OPENAPI_TAG_KEP = 'KEP'
OPENAPI_TAG_TTN = 'TTN'


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='zakupki')
async def get_companies(request: web.Request) -> web.Response:
    date_from, date_to = validate_get_companies(request)
    async with services.db.acquire() as conn:
        companies = await select_registered_companies(conn, date_from=date_from, date_to=date_to)

    return web.json_response({'companies': companies})


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='kep')
async def get_user_companies(request: web.Request) -> web.Response:
    """
    Return a list of companies the user is registered with, to synchronize with KEP.

    Currently, we usually use the CRM proxy to synchronize companies with other services.
    However, this endpoint was created before that feature was introduced, so it is still
    used by KEP, despite duplicating the functionality of the CRM proxy.
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(GetActiveRolesSchema, raw_data)

    async with services.db_readonly.acquire() as conn:
        roles: list[Role]
        if data.vchasno_id:
            # vchasno_id and user_id are the same in this context
            roles = await select_roles(conn, user_id=data.vchasno_id)
        elif data.email:
            roles = await select_roles(conn, user_email=data.email)
        else:
            raise InvalidRequest(raw_reason='Provide either vchasno_id or email')

        if not roles:
            raise DoesNotExist(Object.email, email=data.email)

    return web.json_response(
        [
            {
                'company_edrpou': role.company_edrpou,
                'company_name': role.company_name,
                'company_full_name': role.company_full_name,
                'position': role.position or '',
            }
            for role in roles
            if role.status == RoleStatus.active
        ]
    )


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='kep')
async def get_document_signatures(request: web.Request) -> web.Response:
    signatures = await validate_get_document_signatures(request)

    coroutines = [prepare_signature_response_data_for_kep(signature) for signature in signatures]
    response_data = await asyncio.gather(*coroutines)

    return api_response(request, data=response_data)


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='kep')
async def get_company_billing_accounts(request: web.Request) -> web.Response:
    raw_data = await validators.validate_json_request(request)
    async with services.db.acquire() as conn:
        company_id = await validate_get_company_billing_accounts(conn, raw_data)
        accounts_raw = await select_accounts_raw(conn, company_id)

    accounts = [
        {
            'id': account.id,
            'rate': account.rate,
            'type': account.type,
            'status': account.status,
            'employee_amount': account.employee_amount,
            'activation_date': account.activation_date,
            'date_created': account.date_created,
            'date_expired': account.date_expired,
        }
        for account in accounts_raw
    ]
    return json_response({'accounts': accounts})


async def _check_credentials(request: web.Request) -> web.Response:
    app = request.app
    data = await validators.validate_json_request(request)

    log_extra = {'email': data.get('email')}
    logger.info('Check credentials request', extra=log_extra)

    async with app['db'].acquire() as conn:
        ctx = await validate_login(conn, data, request)
        user = ctx.user

    return web.json_response(
        {
            'ok': True,
            # extra data for autocreate user
            'id': user.id,
            'email': user.email,
            'phone': user.phone,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email_confirmed': user.email_confirmed,
        }
    )


@openapi_docs(
    summary='Реєстрація користувача з мобільного додатку Каси',
    request_json=KasaRegistrationValidator,
)
@private_integration(integration='kasa')
async def registration_kasa(request: web.Request) -> web.Response:
    valid_json = await validators.validate_json_request(request)
    valid_json['source'] = RegistrationSource.kasa.value

    async with services.db.acquire() as conn:
        ctx = await validate_registration_kasa(conn, valid_json)
        user = await registration_utils.registration_kasa(
            conn=conn,
            request=request,
            ctx=ctx,
        )

    return web.json_response(
        data={
            'id': user.id,
        },
        status=HTTPStatus.CREATED,
    )


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='kep')
async def check_credentials_kep(request: web.Request) -> web.Response:
    return await _check_credentials(request)


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='kasa')
async def check_credentials_kasa(request: web.Request) -> web.Response:
    return await _check_credentials(request)


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='ttn')
async def check_credentials_ttn(request: web.Request) -> web.Response:
    return await _check_credentials(request)


@openapi_docs(
    summary=_('Запросити користувача по email'),
    request_json=InviteUserSchema,
    tags=[OPENAPI_TAG_TTN],
)
@private_integration(integration='ttn')
async def invite_user_ttn(request: web.Request) -> web.Response:
    return await invite_user_common(
        request=request,
        source=RegistrationSource.ttn,
    )


@openapi_docs(
    summary='Відправити код підтвердження на телефон для входу',
    request_json=SendPhoneAuthCodeSchema,
    tags=[OPENAPI_TAG_TTN],
)
@private_integration(integration='ttn')
async def send_phone_auth_code_ttn(request: web.Request) -> web.Response:
    """
    Send one time password (OTP) to the phone number for the user to log in or register
    with it.

    It can be also used for resending the OTP code.
    """
    async with services.db.acquire() as conn:
        data = await validate_send_phone_auth(conn, request)

    await phone_auth.send_phone_auth_code(
        phone=data.phone,
        method=data.method,
        billing_project=SenderProject.ttn,
    )

    return web.json_response(status=HTTPStatus.OK)


@openapi_docs(
    summary='Підтвердження OTP коду і реєстрація нового користувача',
    request_json=ProcessPhoneAuth,
    tags=[OPENAPI_TAG_TTN],
)
@private_integration(integration='ttn')
async def process_phone_auth_ttn(request: web.Request) -> web.Response:
    """
    Verify phone OTP code and register a new user if it is not registered yet.

    It can be used for both login and registration by our Vchasno products
    """
    async with services.db.acquire() as conn:
        data = await validate_process_phone_auth(conn, request)

    async with services.db.acquire() as conn:
        output = await phone_auth.process_phone_auth(
            conn=conn,
            auth_phone=data.phone,
            code=data.code,
        )

    response = CreateVchasnoUserResponse.build(is_created=output.is_created, user=output.user)

    return web.json_response(response.to_api(), status=HTTPStatus.OK)


async def invite_user_common(
    request: web.Request,
    source: RegistrationSource,
) -> web.Response:
    """
    Invite user by email and return the registration url that should be sent to the user
    in the email.

    Each integration should create its own email template and send it to the user.
    """
    raw_data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(InviteUserSchema, raw_data)
    logger.info('Invite user request', extra={'email': valid_data.email})

    registration_url, user = await invite_user(
        email=valid_data.email,
        first_name=valid_data.first_name,
        second_name=valid_data.second_name,
        last_name=valid_data.last_name,
        contact_phone=valid_data.contact_phone,
        source=source,
    )

    data = {'user_id': user.id, 'vchasno_id': user.id}

    if not registration_url:
        return web.json_response(
            data=data,
            status=HTTPStatus.OK,
        )

    data['url'] = registration_url

    return web.json_response(
        data=data,
        status=HTTPStatus.CREATED,
    )


@openapi_docs(
    summary=_('Створити користувача'),
    request_json=CreateVchasnoUserSchema,
    response=CreateVchasnoUserResponse,
    tags=[OPENAPI_TAG_KEP],
)
@private_integration(integration='kep')
async def create_vchasno_user_kep(request: web.Request) -> web.Response:
    return await create_vchasno_user_common(
        request=request,
        source=PrivateIntegration.kep,
    )


@openapi_docs(
    summary=_('Створити користувача'),
    request_json=CreateVchasnoUserSchema,
    response=CreateVchasnoUserResponse,
    tags=[OPENAPI_TAG_KASA],
)
@private_integration(integration='kasa')
async def create_vchasno_user_kasa(request: web.Request) -> web.Response:
    return await create_vchasno_user_common(
        request=request,
        source=PrivateIntegration.kasa,
    )


@openapi_docs(
    summary=_('Створити користувача'),
    request_json=CreateVchasnoUserSchema,
    response=CreateVchasnoUserResponse,
    tags=[OPENAPI_TAG_TTN],
)
@private_integration(integration='ttn')
async def create_vchasno_user_ttn(request: web.Request) -> web.Response:
    return await create_vchasno_user_common(
        request=request,
        source=PrivateIntegration.ttn,
    )


async def create_vchasno_user_common(
    request: web.Request,
    source: PrivateIntegration,
) -> web.Response:
    """
    Create and return base user by email
    """
    data = await validate_create_vchasno_user_common(request)
    async with services.db.acquire() as conn:
        user = await select_base_user(conn, email=data.email, exclude_placeholder=False)
        if user:
            is_created = False
        else:
            is_created = True
            user = await create_user(
                conn=conn,
                email=data.email,
                is_placeholder=True,
                first_name=data.first_name,
                second_name=data.second_name,
                last_name=data.last_name,
                phone=None,
                auth_phone=None,
                is_registration_completed=False,
                is_email_confirmed=False,
                password=None,
                is_autogenerated_password=False,
                created_by=None,
                promo_code=None,
                trial_auto_enabled=False,
                pending_referrer_role_id=None,
                is_phone_verified=False,
                source=None,
                google_id=None,
                apple_id=None,
                microsoft_id=None,
                registration_method=None,
            )

    response = CreateVchasnoUserResponse.build(is_created=is_created, user=user)

    logger.info(
        msg='Vchasno user created',
        extra={
            'is_created': response.is_created,
            'user_email': response.user.email,
            'user_id': response.user.id,
            'source': source,
        },
    )

    return web.json_response(response.to_api(), status=HTTPStatus.OK)
