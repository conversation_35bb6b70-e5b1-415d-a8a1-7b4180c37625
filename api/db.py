import logging
from datetime import datetime

import sqlalchemy as sa

from app.auth.types import Auth<PERSON><PERSON>, User
from app.auth.utils import can_user_view_all_company_documents, has_direct_permission, is_admin
from app.documents.db import build_private_document_access_filter
from app.documents.tables import (
    company_listing_table,
    listing_table,
)
from app.lib.database import DBConnection
from app.models import select_all

logger = logging.getLogger(__name__)


async def legacy_get_document_listing_date_mapping(
    conn: DBConnection,
    documents_ids: list[str],
    user: AuthUser,
) -> dict[str, datetime]:
    """
    todo: remove after enabling new listing date logic on production
    """

    if is_admin(user.user_role) or user.can_view_document:
        # For admins and users with access to all document in company, we can simply
        # find listing date in table that contains company access to documents
        query = (
            sa.select([company_listing_table.c.document_id, company_listing_table.c.date_created])
            .select_from(company_listing_table)
            .where(
                sa.and_(
                    company_listing_table.c.document_id.in_(documents_ids),
                    company_listing_table.c.edrpou == user.company_edrpou,
                )
            )
        )
    else:
        # For other users, we need to find date in table that contains access to
        # documents for current user
        query = (
            sa.select([listing_table.c.document_id, listing_table.c.date_created])
            .select_from(listing_table)
            .where(
                sa.and_(
                    listing_table.c.document_id.in_(documents_ids),
                    listing_table.c.role_id == user.role_id,
                )
            )
        )

    listings = await select_all(conn, query)

    return {item.document_id: item.date_created for item in listings}


async def get_document_listing_date_mapping(
    conn: DBConnection,
    documents_ids: list[str],
    user: AuthUser | User,
) -> dict[str, datetime]:
    """
    Get dict with document ID as key and date when a document was listed for company
    or current user as value

    todo: add tests for this function

    TODO: review realization, seems like we dont need to
     check private filters for resolving listing dates
    """

    if not documents_ids:
        return {}

    # For admins and users with access to all documents in the company, we need to find the
    # date when the company first gained access to the document. Instead of searching for the
    # first access date in the "listing_table", we can look up this date directly in the
    # "company_listing_table", which was initially created specifically for this purpose.
    if is_admin(user.user_role) or can_user_view_all_company_documents(user):
        query = (
            sa.select([company_listing_table.c.document_id, company_listing_table.c.date_created])
            .select_from(company_listing_table)
            .where(
                sa.and_(
                    company_listing_table.c.document_id.in_(documents_ids),
                    company_listing_table.c.edrpou == user.company_edrpou,
                )
            )
        )
        listings = await select_all(conn, query)
        return {item.document_id: item.date_created for item in listings}

    # For users with limited access, we need to find:
    # 1. All documents to which the user has direct access — role_filter
    # 2. Documents the user is allowed to view through role-based permissions:
    #    - extended_filter
    #    - private_filter
    # If a user has access to a document through both methods, we should prefer the date
    # when the company first gained access to the document (2nd method).

    role_filter = listing_table.c.role_id == user.role_id
    private_filter = build_private_document_access_filter(user)
    extended_filter = ~private_filter

    can_view_document = has_direct_permission(user, permission='can_view_document')
    can_view_private_document = has_direct_permission(user, permission='can_view_private_document')

    filters = []
    if can_view_document and not can_view_private_document:
        # User can see extended documents but not private documents
        filters.append(sa.or_(role_filter, extended_filter))
    elif not can_view_document and can_view_private_document:
        # User can see private documents but not extended documents
        filters.append(sa.or_(role_filter, private_filter))
    elif not can_view_document and not can_view_private_document:
        filters.append(role_filter)
    else:
        # This is the case where user.can_view_document and user.can_view_private_document are
        # both True This likely represents an invalid permission state in the system, such as
        # we've already handled this case above
        raise ValueError('Invalid user permissions')

    query = (
        sa.select(
            [
                listing_table.c.document_id,
                sa.func.min(listing_table.c.date_created).label('date_created'),
            ]
        )
        .select_from(listing_table)
        .where(
            sa.and_(
                listing_table.c.access_edrpou == user.company_edrpou,
                listing_table.c.document_id.in_(documents_ids),
                *filters,
            )
        )
        .group_by(listing_table.c.document_id)
    )

    listings = await select_all(conn, query)

    return {item.document_id: item.date_created for item in listings}
