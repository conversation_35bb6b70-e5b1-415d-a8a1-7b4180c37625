# English translations for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2000-01-01 00:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: en\n"
"Language-Team: en <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.14.0\n"

#: app/billing/sa_utils.py
msgid ""
"\n"
"                Параметра із файлом у запиті не знайдено або він має невірну назву.\n"
"                Правильна назва - 'file'.\n"
"                "
msgstr ""

#: app/billing/constants.py
msgid ""
"),\n"
"        Упс. Щось пішло не так. Перевірте налаштування вашої картки або зв'яжіться з вашим банком.\n"
"        Можливо, по вашій карті:\n"
"        - перевищено ліміт на оплати в інтернеті\n"
"        - перевищено кредитний ліміт\n"
"        - закінчився строк дії\n"
"        - інша проблема\n"
"        "
msgstr ""

#: app/archive/validators.py
msgid ". Ви можете додати до архіву ще {n} документ"
msgid_plural ". Ви можете додати до архіву ще {n} документи"
msgstr[0] ""
msgstr[1] ""

#: api/downloads/pdf/builders.py
msgid "...і ще {count}"
msgstr ""

#: app/jinja_templates/email/user_email_change_confirm_notice.jinja2
#, python-format
msgid "<b>Електронну адресу вашого облікового запису було змінено.</b> Будь ласка, використовуйте %(new_email)s для входу у «%(brand)s»"
msgstr ""

#: app/billing/validators.py
msgid "Bill has old price (created before April 2025)"
msgstr ""

#: app/billing/validators.py
msgid "Bill has old price (created before May 2025)"
msgstr ""

#: app/billing/validators.py
msgid "Bill has old price (created before October 2024)"
msgstr ""

#: app/billing/validators.py
msgid "Bill is already canceled or refunded"
msgstr ""

#: worker/billing/utils.py
msgid "Bill seqnum is not found in database"
msgstr ""

#: app/events/user_actions/types.py
msgid "Blackbox"
msgstr ""

#: app/csat/validators.py
msgid "CSAT опитування вже було проведено для Вас."
msgstr ""

#: app/billing/sa_utils.py
msgid "CSV потрібно передавати у форматі File."
msgstr ""

#: app/billing/utils.py
msgid "Cannot deactivate rate extension during active web trial"
msgstr ""

#: app/auth/views.py
msgid "Colbert config not found"
msgstr "Colbert config not found"

#: worker/billing/utils.py
msgid "Company does not have unpaid bills for this amount"
msgstr ""

#: worker/billing/utils.py
msgid "Company has multiple unpaid bills with different rates for this amount"
msgstr ""

#: api/errors.py
msgid "E-Mail"
msgstr "Email"

#: api/private/integrations/hrs/handlers.py
msgid "EDRPOU не може бути пустим"
msgstr ""

#: api/private/integrations/hrs/handlers.py
msgid "EDRPOU та назва компанії не можуть бути пустими"
msgstr ""

#: app/auth/providers/apple/utils.py
msgid "Email from Apple ID is not verified"
msgstr ""

#: app/events/document_actions/utils.py
msgid "Email виконавця"
msgstr ""

#: app/billing/utils.py
msgid "Employee amount is required to deactivate rate extension"
msgstr ""

#: api/graph/tests/test_graph_utils.py
msgid "Error"
msgstr ""

#: app/csat/validators.py
msgid "Estimate є обов'язковим при вказанні feedback."
msgstr ""

#: app/diia/validators.py
msgid "Expected encodeData in request body"
msgstr "Expected encodeData in request body"

#: api/errors.py
msgid "FeatureFlag"
msgstr "The FeatureFlag"

#: app/diia/validators.py
msgid "Functionality is not supported yet"
msgstr "Functionality is not supported yet"

#: api/downloads/pdf/builders.py api/downloads/utils.py
msgid "ID"
msgstr "ID"

#: app/events/document_actions/utils.py
msgid "ID документа"
msgstr "Document ID"

#: app/events/document_actions/utils.py
msgid "ID ролі виконавця"
msgstr ""

#: app/events/user_actions/utils.py
msgid "ID співробітника"
msgstr ""

#: api/public/decorators.py
msgid "IP адреси немає у списку дозволених для виконання запиту"
msgstr "IP address is not in the list of allowed to execute the request"

#: app/lib/integrations.py app/notifications/validators.py
msgid "Invalid token"
msgstr "Invalid token"

#: app/profile/validators.py
msgid "Invalid value. Only \"canceled\" / \"refund\" allowed"
msgstr ""

#: app/banner/validators.py
msgid "Language must be unique"
msgstr "Language must be unique"

#: api/private/edi/handlers.py
msgid "Missing original_file"
msgstr ""

#: app/billing/views.py
msgid "Not allowed to change employee amount for not ultimate rate"
msgstr ""

#: api/debug/utils.py
msgid "Only emails with debug domains is allowed.See DEBUG_DOMAINS_ALLOWED constant for details."
msgstr "Only emails with debug domains is allowed.See DEBUG_DOMAINS_ALLOWED constant for details."

#: app/signatures/utils.py
msgid "P7s файл без підписів"
msgstr "P7s file without signatures"

#: api/downloads/validators.py
msgid "PDF файл не сформовано для вибраного документу"
msgstr "PDF file has not been generated for the selected document"

#: app/lib/integrations.py
msgid "Private integrations are disabled by config"
msgstr "Private integrations are disabled by config"

#: api/private/integrations/hrs/handlers.py
msgid "Provide at least one filter."
msgstr ""

#: api/private/super_admin/tmp_csp/views.py
msgid "Provide valid policies dictionary"
msgstr ""

#: app/billing/utils.py
msgid "Rate extension config is not found"
msgstr ""

#: app/billing/validators.py
msgid "Target bill already processed (payment completed / cancelled / refunded)"
msgstr ""

#: app/billing/validators.py
msgid "Target bill is older than 6 months"
msgstr ""

#: api/private/super_admin/views.py
msgid "Temp template migration"
msgstr ""

#: app/lib/tests/test_lib_middlewares.py
msgid "Test invalid request"
msgstr ""

#: app/lib/integrations.py
msgid "Token is missing"
msgstr "Token is missing"

#: worker/billing/utils.py
msgid "Transaction does not contain customer account number and edrpou"
msgstr ""

#: app/billing/validators.py
msgid "Transaction sender is not bill owner"
msgstr ""

#: api/private/edi/handlers.py api/private/super_admin/views.py
#: app/document_versions/views.py app/templates/views.py
msgid "Use multipart/form-data content-type"
msgstr "Use multipart/form-data content-type"

#: worker/emailing/jobs.py
msgid "Vchasno_EDO_feedbacks_{yesterday}"
msgstr ""

#: app/lib/validators.py
msgid "XML should contain single root tag."
msgstr "XML should contain single root tag."

#: api/errors.py
msgid "XML-документ не відповідає очікуваній схемі даних"
msgstr "The XML document does not match the expected data schema"

#: app/diia/validators.py
msgid "You should provide RequestID"
msgstr "You should provide RequestID"

#: app/documents/emailing.py
msgid "[ВАЖЛИВО] Документ погоджено для видалення з сервісу Вчасно"
msgstr "[IMPORTANT] Document approved for deletion from the Vchasno service"

#: app/documents/emailing.py
msgid "[ВАЖЛИВО] Документи погоджено для видалення з сервісу Вчасно"
msgstr "[IMPORTANT] Documents approved for deletion from the Vchasno service"

#: app/profile/emailing.py
msgid "[ВАЖЛИВО] З вашої компанії {company_name} був видалений адміністратор"
msgstr ""

#: worker/document_revoke/utils.py
msgid "[ВАЖЛИВО] Контрагент відхилив анулювання документу в сервісі Вчасно"
msgstr "[IMPORTANT] The counterparty has rejected the revocation of the document in the Vchasno service"

#: worker/document_revoke/utils.py
msgid "[ВАЖЛИВО] Контрагент відхилив анулювання документів в сервісі Вчасно"
msgstr "[IMPORTANT] The counterparty has rejected the revocation of the documents in the Vchasno service"

#: app/documents/emailing.py
msgid "[ВАЖЛИВО] Контрагент відхилив видалення документу з сервісу Вчасно"
msgstr "[IMPORTANT] The counterparty has rejected the deletion of the document from the Vchasno service"

#: app/documents/emailing.py
msgid "[ВАЖЛИВО] Контрагент відхилив видалення документів з сервісу Вчасно"
msgstr "[IMPORTANT] The counterparty has rejected the deletion of the documents from the Vchasno service"

#: worker/document_revoke/utils.py
msgid "[ВАЖЛИВО] Контрагент погодив анулювання документу в сервісі Вчасно"
msgstr "[IMPORTANT] The counterparty has approved the revocation of the document in the Vchasno service"

#: worker/document_revoke/utils.py
msgid "[ВАЖЛИВО] Контрагент погодив анулювання документів в сервісі Вчасно"
msgstr "[IMPORTANT] The counterparty has approved the revocation of the documents in the Vchasno service"

#: app/documents/emailing.py
msgid "[ВАЖЛИВО] Контрагент погодив видалення документу з сервісу Вчасно"
msgstr "[IMPORTANT] The counterparty has approved the deletion of the document from the Vchasno service"

#: app/documents/emailing.py
msgid "[ВАЖЛИВО] Контрагент погодив видалення документів з сервісу Вчасно"
msgstr "[IMPORTANT] The counterparty has approved the deletion of the documents from the Vchasno service"

#: api/errors.py
msgid "delete_request_by_doc_does_not_exist"
msgstr "delete_request_by_doc_does_not_exist"

#: api/errors.py
msgid "delete_requests_mismatch"
msgstr ""

#: api/errors.py
msgid "not_all_delete_request_allowed"
msgstr "not_all_delete_request_allowed"

#: api/errors.py
msgid "not_all_delete_requests_are_new"
msgstr "not_all_delete_requests_are_new"

#: app/document_automation/validators.py
msgid "reviewers_ids чи reviewer_entities має бути заповнено"
msgstr ""

#: app/document_automation/utils.py app/uploads/validators.py
msgid "reviewers_ids чи reviewers має бути заповнено"
msgstr ""

#: api/public/views.py
msgid "signer_entities або roles повинні бути заповнені"
msgstr ""

#: app/document_automation/validators.py
msgid "signers_ids чи singer_entities має бути заповнено"
msgstr ""

#: app/document_automation/validators.py
msgid "viewers_ids чи viewers_group_ids має бути заповнено"
msgstr ""

#: app/reviews/emailing.py
msgid "{n} документ компанії {company} очікує вашого погодження"
msgid_plural "{n} документи компанії {company} очікують вашого погодження"
msgstr[0] "{n} document of company {company} is waiting for your approval"
msgstr[1] "{n} documents of company {company} are waiting for your approval"

#: app/reviews/emailing.py
msgid "{n}+ документ компанії {company} очікує вашого погодження"
msgid_plural "{n}+ документи компанії {company} очікують вашого погодження"
msgstr[0] "{n}+ document of company {company} is waiting for your approval"
msgstr[1] "{n}+ documents of company {company} are waiting for your approval"

#: api/errors.py
msgid "{object} вже існує у базі даних"
msgstr "{object} already exists in the database"

#: api/errors.py
msgid "{object} не знайдено у базі даних"
msgstr "{object} does not exists in the database"

#: app/lib/sender/utils.py
msgid "{otp} - код підтвердження {domain}"
msgstr "{otp} - verification code {domain}"

#: app/jinja_templates/email/invite_document.jinja2
msgid "«Вчасно.ЕДО» — лідер серед ЕДО-сервісів, якому довіряють більше мільйона компаній"
msgstr "«Vchasno.EDO» is a leader among EDM services, trusted by over a million companies."

#: app/signatures/validators.py
msgid "ЄДРПОУ чи email мають бути заповненими"
msgstr "Tax ID or email must be filled"

#: api/downloads/pdf/helpers.py app/documents/emailing.py
#: app/jinja_templates/email/blocks/document_macros.jinja2
#: app/jinja_templates/email/notification_comment.jinja2
msgid "ЄДРПОУ/ІПН"
msgstr "Tax ID"

#: worker/emailing/utils.py
msgid "ЄДРПОУ/ІПН {company_edrpou}"
msgstr ""

#: app/jinja_templates/email/notification_document.jinja2
msgid "І додав коментар"
msgstr "And added a comment"

#: api/downloads/pdf/helpers.py
msgid "Ідентифікаційний код"
msgstr "Identity code"

#: app/directories/validators.py
msgid "Імʼя папки містить заборонені символи, будь-ласка користуйтесь загальноприйнятими правилами для операційних систем при створенні папок"
msgstr ""

#: app/documents/utils.py
msgid "Імпортований"
msgstr ""

#: app/archive/validators.py
msgid "Імпортовані документи не можна розархівувати"
msgstr ""

#: api/downloads/archives.py
msgid "Інструкція.pdf"
msgstr "Instruction.pdf"

#: app/events/user_actions/types.py
msgid "Інтеграція"
msgstr ""

#: api/private/super_admin/views.py
msgid "Інтеграція з ПУМБ недоступна"
msgstr ""

#: api/private/super_admin/views.py
msgid "Інтеграція з Приватбанком недоступна"
msgstr "Integration with PrivatBank is not available"

#: app/jinja_templates/email/invite_document.jinja2
msgid "Інтуїтивний сервіс для роботи з документами"
msgstr "An intuitive service for working with documents."

#: app/reviews/validators.py
msgid "Інший користувач вже призначив погодження для документу"
msgstr "Another user has already assigned approval to the document"

#: api/downloads/utils.py
msgid "ІсторіяПогодження-{document_id}.pdf"
msgstr "HistoryConsent-{document_id}.pdf"

#: api/downloads/utils.py
msgid "ІсторіяПогодження-{document_id}.xlsx"
msgstr "HistoryConsent-{document_id}.xlsx"

#: app/jinja_templates/email/invite_document.jinja2
msgid "Автоматизуйте рутину, щоб зосередитися на головному — вашому бізнесі."
msgstr "Automate routine tasks to focus on what matters most — your business."

#: app/events/document_actions/types.py
msgid "Автоматичне застосування сценарію"
msgstr ""

#: api/errors.py
msgid "Акт анулювання"
msgstr "Revoke"

#: app/document_revoke/validators.py
msgid "Акт анулювання ще не підписано іншою стороною"
msgstr "Revoke has not yet been signed by the other party"

#: app/events/user_actions/types.py
msgid "Активація/деактивація сценарію"
msgstr ""

#: api/private/super_admin/views.py
msgid "Активувати перемикач функціоналу"
msgstr ""

#: api/private/super_admin/views.py
msgid "Активувати роль в компанії"
msgstr ""

#: api/private/super_admin/views.py
msgid "Активувати тріал для компаній"
msgstr ""

#: app/documents/utils.py
msgid "Анульований"
msgstr "Revoked"

#: app/document_revoke/validators.py
msgid "Анулювання завершено"
msgstr "Revoke process completed"

#: api/errors.py
msgid "Архів"
msgstr "Archive"

#: app/documents/emailing.py
msgid "Архів документів компанії {edrpou}, {name}"
msgstr ""

#: api/downloads/views.py
msgid "Архів формується, на вашу пошту буде відправлено листа з посиланням для завантаження"
msgstr "The archive is being created, an email will be sent to your with a download link"

#: app/events/document_actions/types.py
msgid "Архівація документа"
msgstr "Archiving a document"

#: api/errors.py
msgid "Архівний документ"
msgstr "Archived document"

#: app/documents/validators.py
msgid "Багатосторонній документ повинен бути між двома або більше компаніями"
msgstr ""

#: api/errors.py
msgid "Банер"
msgstr "The banner"

#: api/errors.py
msgid "Банера"
msgstr "Banners"

#: app/billing/constants.py
msgid "Банк відхилив операцію. Будь ласка, зверніться в банк для отримання подробиць"
msgstr ""

#: app/billing/constants.py
msgid "Банк не підтвердив операцію. Будь ласка, зверніться в банк для отримання подробиць"
msgstr ""

#: app/billing/constants.py
msgid "Банк отклонил операцию. Пожалуйста, обратитесь в банк для получения подробностей"
msgstr ""

#: app/billing/constants.py
msgid "Банк-еквайєр не підтримує 3D Secure. Спробуйте скористатися іншою картою"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Безпека та надійність"
msgstr "Security and reliability"

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Безпечно зберігайте документи визначений законодавством час та налаштовуйте права та доступи для командної роботи."
msgstr "Safely store documents for the defined period of time and set rights and access for teamwork."

#: api/errors.py
msgid "Бонус"
msgstr "Bonus"

#: app/events/validators.py
msgid "Будь ласка зачекайте, поки сформується попередній звіт"
msgstr "Please wait for the preview report to be generated"

#: app/jinja_templates/email/user_email_change_confirm_admin.jinja2
msgid "Будь ласка, <b>використовуйте новий email для коректної роботи інтеграції.</b>"
msgstr ""

#: api/errors.py
msgid "Будь ласка, авторизуйтесь для доступу до сторінки"
msgstr "Please log in to access the page"

#: app/auth/validators.py
msgid "Будь ласка, авторизуйтесь, використовуючи пароль від Вчасно.ЕДО"
msgstr "Please log in using your Vchasno.EDO password"

#: app/billing/constants.py
msgid "Будь ласка, введіть CVV-код карти"
msgstr ""

#: app/billing/constants.py
msgid "Будь ласка, введіть дані відправника"
msgstr ""

#: app/billing/constants.py
msgid "Будь ласка, введіть дані одержувача"
msgstr ""

#: app/billing/utils.py
msgid "Будь ласка, вкажіть ЄДРПОУ/ІПН компанії, до якої Ви маєте доступ"
msgstr "Please indicate the Tax ID of the company you have access to"

#: app/billing/constants.py
msgid "Будь ласка, вкажіть іншу карту"
msgstr ""

#: app/billing/constants.py
msgid "Будь ласка, заповніть обов'язкові поля і спробуйте ще раз оплати"
msgstr ""

#: api/errors.py
msgid "Будь ласка, підтвердіть себе за допомогою двофакторної автентифікації"
msgstr "Please confirm yourself using two-factor authentication"

#: app/billing/constants.py
msgid "Будь-ласка, введіть код підтвердження з повідомлення від вашого банку"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Більше нема необхідності користуватися поштовими та кур’єрськими послугами. Надсилайте та отримуйте документи онлайн."
msgstr "No more need to use postal and courier services. Send and receive documents online."

#: app/billing/validators.py
msgid "В компанії відсутній ліміт користувачів"
msgstr ""

#: app/documents/utils.py
msgid "В процесі підписання"
msgstr "In the process of signing"

#: app/documents/utils.py
msgid "В процесі підписання вашою компанією"
msgstr "In the process of signing by your company"

#: api/errors.py
msgid "В систему вже завантажено аналогічний документ"
msgstr "A similar document has already been uploaded to the system"

#: app/profile/validators.py
msgid "Вами було скасовано даний запит на зміну електронної пошти. Щоб виконати заміну, будь ласка, розпочніть новий процес зміни пошти"
msgstr ""

#: app/jinja_templates/email/auto_welcome_letter.jinja2
#: app/registration/emailing.py
msgid "Вас було зареєстровано у \"{brand}\""
msgstr "You were registered in \"{brand}\""

#: app/comments/emailing.py
msgid "Ваш Партнер"
msgstr "Your counterparty"

#: api/errors.py
msgid "Ваш ключ не має ЄДРПОУ/ІПН. Зверніться до свого АЦСК"
msgstr "Your key does not have a company tax ID. Contact your certification authority (CA)."

#: app/registration/emailing.py
msgid "Ваш колега"
msgstr "Your colleague"

#: app/registration/emailing.py
msgid "Ваш колега запрошує вас до сервісу {brand}"
msgstr "Your colleague invites you to the {brand} service"

#: app/jinja_templates/email/notification_document.jinja2
#: app/jinja_templates/email/notification_document_version_add.jinja2
#: app/jinja_templates/email/zakupki_notification_document.jinja2
#: app/registration/emailing.py
msgid "Ваш контрагент"
msgstr "Your counterparty"

#: app/trigger_notifications/utils.py
msgid "Ваш контрагент {company_title} зареєструвався у Вчасно та готовий обмінюватись з вами електронними документами!"
msgstr "Your counterparty {company_title} has registered in Vchasno and is ready to exchange electronic documents with you!"

#: app/registration/emailing.py
msgid "Ваш контрагент запрошує вас до сервісу {brand}"
msgstr "Your partner invites you to the {brand} service"

#: app/jinja_templates/email/auto_welcome_letter.jinja2
msgid "Ваш логін:"
msgstr "Your login:"

#: app/jinja_templates/email/auto_welcome_letter.jinja2
msgid "Ваш пароль:"
msgstr "Your password:"

#: app/jinja_templates/email/delete_request_accept.jinja2
#: app/jinja_templates/email/delete_request_new.jinja2
#: app/jinja_templates/email/delete_request_reject.jinja2
#: app/jinja_templates/email/revoke_completed.jinja2
#: app/jinja_templates/email/revoke_initiated.jinja2
#: app/jinja_templates/email/revoke_rejected.jinja2
msgid "Ваш партнер"
msgstr "Your partner"

#: app/documents/emailing.py
msgid "Ваш партнер відхилив документ"
msgstr "Your partner rejected the document"

#: api/errors.py
msgid "Ваш профіль на стороні провайдеру не містить імейл адреси. Ми не можемо продовжити реєстрацію без імейл адреси, тому перевірте налаштування на стороні провайдера або оберіть інший спосіб реєстрації"
msgstr ""

#: app/jinja_templates/email/documents_for_signer.jinja2
#: app/jinja_templates/email/review_request.jinja2
msgid "Ваш співробітник"
msgstr "Your employee"

#: app/documents/emailing.py app/mobile/notifications/notifications.py
msgid "Ваш співробітник відхилив документ"
msgstr "Your employee rejected the document"

#: app/document_categories/validators.py
msgid "Ваш тариф не передбачає використання внутрішніх типів документа."
msgstr ""

#: app/documents_required_fields/validators.py
msgid "Ваш тариф передбачає не більше {limit} обов'язкових полів"
msgstr "Your tariff includes no more than {limit} required fields"

#: app/jinja_templates/email/user_email_change_start_notice.jinja2
#, python-format
msgid "Ваша електронна адреса буде змінена з %(old_email)s на %(new_email)s. Перейдіть за посиланням, яке ми відправили на %(new_email)s, щоб підтвердити нову електронну адресу."
msgstr ""

#: app/billing/constants.py
msgid "Ваша карта не підтримує 3DSecure. Будь ласка, скористайтеся іншою картою для оплати"
msgstr ""

#: app/billing/constants.py
msgid "Ваша карта не підтримує такий вид транзакції. Будь ласка, скористайтеся іншою картою для оплати"
msgstr ""

#: app/billing/constants.py
msgid "Ваша карта прострочена. Будь ласка, скористайтеся іншою картою для оплати"
msgstr ""

#: app/billing/constants.py
msgid "Ваша карта числиться як загублена або вкрадена. Будь ласка, зверніться до свого банку для отримання подробиць"
msgstr ""

#: app/billing/constants.py
msgid "Ваша картка не підтримується. Будь ласка, скористайтеся іншою картою для оплати"
msgstr ""

#: app/registration/views.py
msgid "Вашим контактам буде надіслано запрошення приєднатися до \"{brand}\"."
msgstr "Your contacts will be invited to join \"{brand}\"."

#: app/billing/constants.py
msgid "Введіть номер телефону і спробуйте сплатити ще раз"
msgstr ""

#: app/events/user_actions/types.py
msgid "Веб"
msgstr ""

#: app/uploads/validators.py
msgid "Версійний документ може бути тільки з паралельним підписуванням"
msgstr "A version document can only be signed in parallel"

#: app/uploads/validators.py
msgid "Версійний документ може бути тільки з першим підписом від контрагента"
msgstr "A versioned document can only be signed with the first signature from the counterparty."

#: app/documents/validators.py
msgid "Версійний документ не може бути багатостороннім"
msgstr ""

#: api/errors.py
msgid "Версію документу"
msgstr "Document version"

#: app/jinja_templates/email/notification_document_version_add.jinja2
msgid "Версія"
msgstr "Version"

#: app/documents/validators.py
msgid "Версія документа не підтримує конвертацію в PDF"
msgstr "The document version does not support conversion to PDF"

#: api/errors.py
msgid "Версія документу"
msgstr "Document version"

#: app/banner/validators.py
msgid "Вже є банери, що пересікається по часу з вказаним: {banners}"
msgstr ""

#: app/billing/validators.py
msgid "Вже існує тариф, що пересікається по часу"
msgstr "There is already a tariff that overlaps in time"

#: app/profile/validators.py
msgid "Ви ввели неправильний домен. Будь ласка, перевірте правильність введення домену та спробуйте ще раз."
msgstr "You entered an incorrect domain. Please check that the domain is correct and try again."

#: api/errors.py
msgid "Ви вже надсилали запрошення вашим контактам. Спробуйте трохи згодом."
msgstr "You have already sent invitations to your contacts. Try it later."

#: app/document_categories/validators.py
msgid "Ви досягли ліміту створень категорій документів."
msgstr ""

#: app/jinja_templates/email/user_email_change_start_notice.jinja2
#, python-format
msgid "Ви запросили зміну електронної адреси в обліковому записі «%(brand)s»"
msgstr ""

#: app/jinja_templates/email/user_email_change_start.jinja2
msgid "Ви запросили зміну електронної адреси в обліковому записі."
msgstr ""

#: app/registration/emailing.py
msgid "Ви зареєструвались у Вчасно. Ласкаво просимо!"
msgstr ""

#: app/jinja_templates/email/invite_document.jinja2
#, python-format
msgid "Ви маєте вхідний документ <span style=\"font-weight: bold;\">%(document_title)s</span> на перегляд до компанії %(recipient_company_edrpou)s від %(sender_company_label)s у сервісі електронного документообігу «Вчасно.ЕДО» 💛"
msgstr "You have an incoming document <span style=\"font-weight: bold;\">%(document_title)s</span> for review to company %(recipient_company_edrpou)s from %(sender_company_label)s in the electronic document management service «Vchasno.EDO» 💛"

#: app/jinja_templates/email/invite_document.jinja2
#, python-format
msgid "Ви маєте вхідний документ <span style=\"font-weight: bold;\">%(document_title)s</span> на підпис до компанії %(recipient_company_edrpou)s від %(sender_company_label)s у сервісі електронного документообігу «Вчасно.ЕДО» 💛"
msgstr "You have an incoming document <span style=\"font-weight: bold;\">%(document_title)s</span> for signing to company %(recipient_company_edrpou)s from %(sender_company_label)s in the electronic document management service «Vchasno.EDO» 💛"

#: api/errors.py
msgid "Ви не можете видалити себе з компанії, де ви є адміністратором"
msgstr "You can't remove yourself from a company where you are an administrator"

#: api/errors.py
msgid "Ви нещодавно завантажували досьє цього контрагента. Спробуйте ще раз за {seconds} секунд."
msgstr "You recently uploaded this counterparty's dossier. Try again in {seconds} seconds."

#: app/documents/emailing.py app/mobile/notifications/notifications.py
msgid "Ви отримали документ"
msgstr "You received a document"

#: app/documents/emailing.py
msgid "Ви отримали документ для підписання"
msgstr "You received a document for signing"

#: app/jinja_templates/email/review_request.jinja2
#: app/mobile/notifications/notifications.py
#: worker/emailing/reminders_for_new_review_request/utils.py
msgid "Ви отримали документ на погодження"
msgstr "You received a document for approval"

#: app/documents/emailing.py
#: app/jinja_templates/email/documents_for_signer.jinja2
#: app/mobile/notifications/notifications.py
msgid "Ви отримали документ на підпис"
msgstr "You received a document for signing"

#: app/mobile/notifications/notifications.py
msgid "Ви отримали документ на підпис для компанії"
msgstr ""

#: app/documents/emailing.py
#: app/jinja_templates/email/documents_for_signer.jinja2
#: app/mobile/notifications/notifications.py
msgid "Ви отримали документи на підпис"
msgstr "You received documents for signing"

#: app/documents/emailing.py
msgid "Ви отримали доступ до документу"
msgstr ""

#: worker/document_revoke/utils.py
msgid "Ви отримали запит на анулювання документа"
msgstr "You received a request to revoke the document"

#: worker/document_revoke/utils.py
msgid "Ви отримали запит на анулювання документів"
msgstr "You received a request to revoke the documents"

#: worker/documents/utils.py
msgid "Ви отримали запит на видалення документа"
msgstr "You received a request to delete the document"

#: worker/documents/utils.py
msgid "Ви отримали запит на видалення документів"
msgstr "You received a request to delete the documents"

#: app/registration/emailing.py
msgid "Ви отримали запрошення від вашого колеги"
msgstr "You received an invitation from your colleague"

#: app/registration/emailing.py
msgid "Ви отримали запрошення від вашого контрагента"
msgstr "You received an invitation from your partner"

#: app/mobile/notifications/notifications.py
msgid "Ви отримали запрошення від колеги"
msgstr ""

#: app/trigger_notifications/utils.py
msgid "Ви отримали коментар від {partner}"
msgstr "You received a comment from {partner}"

#: app/comments/emailing.py
#: app/jinja_templates/email/notification_comment.jinja2
#: app/mobile/notifications/notifications.py
msgid "Ви отримали коментар до документу"
msgstr "You received a comment on the document"

#: worker/emailing/reminders_for_new_document_version/jobs.py
msgid "Ви отримали нову версію документу"
msgstr "You have received a new version of the document"

#: app/jinja_templates/email/registration_confirmation.jinja2
msgid "Ви отримали цей лист, тому що вам необхідно підтвердити свою електронну адресу. Будь ласка, натисніть на наступну кнопку для підтвердження."
msgstr "You received this email because you need to confirm your email address. Please click the button below to confirm."

#: app/auth/utils.py
msgid "Ви перевищили ліміт спроб вводу пароля. Ваш обліковий запис було заблоковано. Для відновлення доступу відновіть пароль."
msgstr "You have exceeded the limit of password attempts. Your account has been locked. To restore access, reset the password."

#: api/errors.py
msgid "Ви перейшли за застарілим посиланням. Сформуйте новий запит на зміну паролю."
msgstr ""

#: app/jinja_templates/email/delete_request_accept_self.jinja2
msgid "Ви прийняли запит на видалення документу"
msgstr "You accepted the request to delete the document"

#: app/jinja_templates/email/delete_request_accept_self.jinja2
msgid "Ви прийняли запит на видалення документів"
msgstr "You accepted the request to delete the documents"

#: app/jinja_templates/email/auto_welcome_letter.jinja2
msgid "Ви підписали документи від"
msgstr "You signed documents from"

#: app/jinja_templates/email/recover_password.jinja2
msgid "Ви сказали нам, що забули свій пароль. Якщо це так, натисніть нижче, щоб створити новий."
msgstr ""

#: app/events/document_actions/types.py
msgid "Вивантаження документу"
msgstr ""

#: app/events/document_actions/types.py
msgid "Видалення версії"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення групи"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення додаткового параметру"
msgstr ""

#: app/events/document_actions/types.py
msgid "Видалення документу"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення доступу до додаткового параметру"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення користувача з компанії"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення обовязкового додаткового параметру"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення папки"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення ролі в ЕДІ"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення сценарію"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення токена для співробітника"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення учасника групи"
msgstr ""

#: app/events/document_actions/types.py
msgid "Видалення чернетки версії"
msgstr "Version draft deletion"

#: app/events/user_actions/types.py
msgid "Видалення шаблону"
msgstr ""

#: app/events/user_actions/types.py
msgid "Видалення ярлику ролі"
msgstr ""

#: app/events/document_actions/types.py
msgid "Видалення ярликів"
msgstr ""

#: app/reviews/validators.py
msgid "Видалити запит на погодження може лише адміністратор компанії, або автор погодження"
msgstr "Only the company administrator or the author of the consent can delete the consent request"

#: app/reviews/validators.py
msgid "Видалити запит на погодження може лише адміністратор компанії, автор погодження або співробітник, якому цей запит було надіслано"
msgstr ""

#: api/private/super_admin/views.py
msgid "Видалити користувача, замінивши його email на випадковий"
msgstr ""

#: app/document_categories/views.py
msgid "Видалити публічний тип документу"
msgstr ""

#: api/private/super_admin/views.py
msgid "Видалити публічний шаблон"
msgstr ""

#: api/private/super_admin/views.py
msgid "Видалити роль користувача"
msgstr ""

#: app/billing/sa_views.py
msgid "Видалити цільову аудиторію для тріалів"
msgstr ""

#: app/documents_ai/views.py
msgid "Визначити реквізити документа по document_id"
msgstr ""

#: app/documents_ai/views.py
msgid "Визначити реквізити документа по його вмісту"
msgstr ""

#: api/private/super_admin/views.py
msgid "Виконати дію /send для документа від імені даного користувача"
msgstr ""

#: app/lib/validators.py
msgid "Використовуйте заголовок content-type: multipart/form-data"
msgstr "Use the title content-type: multipart/form-data"

#: api/errors.py
msgid "Використовується застарілий токен доступу в API.Сформуйте будь ласка новий токен."
msgstr "An outdated API access token is used. Please create a new token."

#: app/lib/s3_utils.py
msgid "Виникла помилка при завантаженні документа, спробуйте повторити спробу"
msgstr ""

#: api/errors.py
msgid "Виникла помилка, перевірте введені дані"
msgstr "If an error occurs, check the entered data"

#: app/events/user_actions/types.py
msgid "Вихід з кабінету компанії"
msgstr ""

#: app/events/user_actions/types.py
msgid "Вихід із сервісу"
msgstr ""

#: app/flow/validators.py
msgid "Вкажіть більш ніж одну компанію для багатостороннього документу.Для створення документу в межах однієї компанії завантажте документ з типом \"Внутрішній\""
msgstr "Specify more than one company for a multiparty document. To create a document within one company, upload a document with the type \"Internal\""

#: app/billing/validators.py
msgid "Вкажіть дати активації тарифу або оберіть подовження поточного."
msgstr "Choose activation dates or extend current rate."

#: app/document_automation/validators.py
msgid "Вкажіть хоча б одне налаштування для шаблону"
msgstr "Specify at least one setting for the template"

#: app/billing/validators.py
msgid "Вказана валюта оплати не підтримується сервісом"
msgstr "Currency is not supported by service"

#: app/billing/validators.py
msgid "Вказаний тариф не є тестовим"
msgstr "The specified tariff is not a test"

#: api/errors.py
msgid "Вказаний хост для завантаження файлу не вказано в переліку налаштувань поточної компанії"
msgstr "The specified host for uploading the file is not specified in the list of settings of the current company"

#: app/signatures/validators.py
msgid "Власник версії документа не може підписати документ першим"
msgstr "The owner of the document version cannot sign the document first"

#: app/documents/validators.py
msgid "Власник документу не може бути одержувачем"
msgstr "The document owner cannot be the recipient"

#: api/downloads/pdf/builders.py
msgid "Власник ключа"
msgstr "Key owner"

#: api/downloads/pdf/builders.py
msgid "Внутрішнє погодження документу"
msgstr "Internal document approval"

#: app/jinja_templates/email/review_request.jinja2
msgid "Внутрішнє погодження – це функціональність, яка дозволяє отримати підтвердження від Ваших колег, що документ відповідає вимогам і готовий до підписання. В погодженні можуть брати участь тільки співробітники Вашої компанії, а результати будуть недоступні контрагенту."
msgstr "Internal approval is a feature that allows you to get confirmation from your colleagues that the document meets the requirements and is ready for signing. Only employees of your company can participate in the approval, and the results will not be available to the counterparty."

#: app/trigger_notifications/utils.py
msgid "Вони точно готові обмінюватись електронними документами з вами"
msgstr "They are definitely ready to exchange electronic documents with you"

#: app/jinja_templates/email/your_account_is_blocked.jinja2
msgid "Встановити новий пароль"
msgstr ""

#: app/billing/sa_views.py
msgid "Встановити цільову аудиторію для тріалів"
msgstr ""

#: api/private/super_admin/utils.py
msgid "Встановлення паролю"
msgstr ""

#: app/documents/validators.py
msgid "Всі одержувачі, крім власника, повинні мати заповнений email"
msgstr ""

#: app/flow/validators.py
msgid "Всі підписанти повинні немати порядку або всі підписанти мають мати порядок "
msgstr "All signatories must have no order or all signatories must have order"

#: app/events/user_actions/types.py
msgid "Вхід в кабінет компанії"
msgstr ""

#: app/events/user_actions/types.py
msgid "Вхід у сервіс"
msgstr ""

#: app/jinja_templates/email/new_employee_failed_registration.jinja2
#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
msgid "Вчасно"
msgstr ""

#: app/jinja_templates/email/review_request.jinja2
msgid "Відкрийте блок \"Погодження\" в бічній панелі праворуч"
msgstr "Open the \"Approval\" block in the right sidebar"

#: app/events/document_actions/types.py
msgid "Відкриття сесії підписання"
msgstr ""

#: app/reviews/utils.py
msgid "Відміняє погодження"
msgstr "Withdraws consent"

#: api/private/super_admin/views.py
msgid "Відновити видалений документ"
msgstr ""

#: app/jinja_templates/email/recover_password.jinja2
msgid "Відновлення вашого паролю"
msgstr ""

#: api/private/super_admin/utils.py
msgid "Відновлення документу"
msgstr ""

#: api/private/super_admin/views.py
msgid "Відновлюється лише вміст документу, інші метадані не будуть відновлені. УВАГА: Перш ніж робити цю дію потрібно отримати підтвердження від користувача підписане ключем компанії"
msgstr ""

#: api/private/super_admin/views.py
msgid "Відправити push-сповіщення в додатоки для конкретного користувача"
msgstr ""

#: app/events/document_actions/types.py
msgid "Відправлення документа"
msgstr ""

#: api/downloads/pdf/builders.py
msgid "Відправник документу"
msgstr "Document sender"

#: api/errors.py
msgid "Відсутній обов'язковий параметр для запиту до серверу"
msgstr "There is no mandatory parameter for requesting the server"

#: app/documents/utils.py
msgid "Відхилений"
msgstr "Rejected"

#: app/events/document_actions/types.py
msgid "Відхилення акту анулювання"
msgstr "Rejection of the revocation"

#: app/events/document_actions/types.py
msgid "Відхилення документу"
msgstr ""

#: app/reviews/utils.py
msgid "Відхиляє"
msgstr "Declines"

#: app/jinja_templates/email/blocks/greeting.jinja2
#: app/jinja_templates/email/blocks/greeting_with_header.jinja2
#: app/jinja_templates/email/blocks/table_blocks.jinja2
#: app/jinja_templates/email/macros.jinja2
msgid "Вітаємо"
msgstr "Hello"

#: app/events/user_actions/types.py
msgid "Генерація токена для співробітника"
msgstr "Token generation for employee"

#: app/documents/utils.py
msgid "Готовий для підпису"
msgstr "Ready for signing"

#: app/documents/utils.py
msgid "Готовий для підпису та надсилання"
msgstr "Ready for signing and sending"

#: app/jinja_templates/email/invite_document.jinja2
msgid "Готово! Ви успішно підписали свій перший документ у «Вчасно.ЕДО»!"
msgstr "Done! You have successfully signed your first document in «Vchasno.EDO»!"

#: api/errors.py
msgid "Груп"
msgstr "Teams"

#: api/errors.py
msgid "Група"
msgstr "Team"

#: app/documents/validators.py
msgid "Група вже має доступ до документу"
msgstr "The group already has access to the document"

#: api/errors.py
msgid "Групи"
msgstr "Teams"

#: app/documents/utils.py
msgid "Даний емейл не зареєстрований на Вчасно, вкажіть іншу адресу"
msgstr "This email is not registered in Vchasno, please provide another address"

#: app/billing/constants.py
msgid "Дані карти вказані невірно. Будь ласка, спробуйте ще раз"
msgstr "Data card is incorrect. Please try again"

#: app/jinja_templates/email/user_data_updated.jinja2 app/profile/views.py
msgid "Дані профілю оновлено"
msgstr "Profile data updated"

#: api/downloads/utils.py
msgid "Дата"
msgstr "The date"

#: api/downloads/pdf/builders.py api/downloads/utils.py
msgid "Дата завантаження або отримання"
msgstr "Date downloaded or received"

#: app/events/validators.py
msgid "Дата завершення звіту має бути не меншою за дату початку"
msgstr ""

#: app/billing/validators.py
msgid "Дата завершення тарифу не може бути меншою за сьогодні"
msgstr "The tariff end date cannot be less than today"

#: app/billing/validators.py
msgid "Дата початку дії тарифу не може бути меншою за сьогодні"
msgstr "The tariff start date cannot be less than today"

#: app/events/validators.py
msgid "Дата початку звіту не може бути більшою за поточну дату"
msgstr ""

#: app/events/validators.py
msgid "Дата початку звіту не може бути раніше ніж за рік від поточної дати"
msgstr ""

#: app/billing/schemas.py
msgid "Дата початку тарифу повинна бути меншою за дату завершення"
msgstr "The start date of the tariff must be less than the end date"

#: app/events/document_actions/utils.py app/events/user_actions/utils.py
msgid "Дата і час"
msgstr "Date and time"

#: app/documents/validators.py
msgid "Двосторонній документ повинен бути між двома різними компаніями"
msgstr "Dual-sided document must be between two different companies"

#: app/auth/validators.py
msgid "Декілька або всі вказані ролі не є співробітниками вашої компанії"
msgstr "Some or all of the listed roles are not co-performers of your company"

#: app/auth/validators.py
msgid "Деякі співробітники мають неактивну роль"
msgstr "Some employees have an inactive role"

#: app/events/user_actions/utils.py
msgid "Джерело"
msgstr ""

#: app/billing/schemas.py
msgid "Для активації тарифу потрібно вказати start_date i end_date"
msgstr ""

#: app/billing/views.py
msgid "Для активації тарифу потрібно завершити реєстрацію"
msgstr "You need to complete registration to activate the tariff"

#: app/jinja_templates/email/actions_report_documents.jinja2
#: app/jinja_templates/email/actions_report_users.jinja2
msgid "Для вивантаження історії дій перейдіть за посиланням нижче:"
msgstr "To download the action history, follow the link below:"

#: app/document_automation/validators.py
msgid "Для вхідних документів можна вказувати лише ЄДРПОУ ваших контрагентів"
msgstr "For incoming documents, you can specify only your counterparties' Tax ID"

#: api/public/decorators.py api/public/validators.py
msgid "Для доступу до API потрібно оплатити тариф 'Інтеграція'"
msgstr "To access the API, you must pay the 'Integration' tariff"

#: app/jinja_templates/email/user_email_change_verify_email.jinja2
msgid "Для зміни адреси електронної пошти вам необхідно підтвердити доступ до облікового запису. Щоб це зробити, натисніть кнопку нижче."
msgstr ""

#: app/reviews/validators.py
msgid "Для зміни типу погодження(послідовне/паралельне) необхідно видалити всі попередні погодження"
msgstr ""

#: app/documents/validators.py
msgid "Для компанії, що вже підписала документ, не можливо змінити налаштування чи може вона підписувати чи ні"
msgstr ""

#: app/documents/validators.py
msgid "Для конвертації документа в PDF остання версія документа повинна мати розширення docx або xlsx"
msgstr "The document must have the extension docx or xlsx to convert the document to PDF"

#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
msgid "Для ознайомлення із можливостями налаштувань співробітників можете переглянути нашу"
msgstr ""

#: app/profile/views.py
msgid "Для повторного встановлення телефону потрібен пароль"
msgstr ""

#: app/uploads/validators.py
msgid "Для призначення сценарію необхідно авторизуватися"
msgstr "To assign a scenario, you must be authorized"

#: app/jinja_templates/email/email_2fa_token.jinja2
msgid "Для підтвердження двофакторної аутентифікації, будь ласка, перейдіть за посиланням нижче."
msgstr "To confirm two-factor authentication, please follow the link below."

#: app/profile/validators.py
msgid "Для підтвердження телефону потрібно спочатку встановити його"
msgstr ""

#: app/reviews/validators.py
msgid "Для створення послідовного погодження необхідно видалити всі попередні погодження"
msgstr "To create a sequential consent, all previous consents must be deleted"

#: app/sign_sessions/validators.py
msgid "Для створення сесії підписання користувач повинен мати email"
msgstr "To create a signing session, the user must have an email"

#: app/jinja_templates/email/notification_document.jinja2
msgid "Для цього документа увімкнена можливість додавати нові версії"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Для чого ми?"
msgstr "What for?"

#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
msgid "До вашої компанії додався новий співробітник"
msgstr ""

#: app/jinja_templates/email/new_employee_failed_registration.jinja2
msgid "До вашої компанії у Вчасно"
msgstr ""

#: worker/emailing/utils.py
msgid "До всіх відхилених документів"
msgstr ""

#: worker/emailing/utils.py
msgid "До всіх документів з коментарями"
msgstr ""

#: worker/emailing/utils.py
msgid "До всіх документів, що не потребують вашого підпису"
msgstr ""

#: worker/emailing/utils.py
msgid "До всіх документів, що очікують вашого підпису"
msgstr ""

#: app/jinja_templates/email/notification_comment.jinja2
msgid "До компанії"
msgstr "To the company"

#: app/jinja_templates/email/delete_request_accept.jinja2
#: app/jinja_templates/email/delete_request_accept_self.jinja2
#: app/jinja_templates/email/delete_request_new.jinja2
#: app/jinja_templates/email/delete_request_reject.jinja2
#: app/jinja_templates/email/revoke_completed.jinja2
#: app/jinja_templates/email/revoke_initiated.jinja2
#: app/jinja_templates/email/revoke_rejected.jinja2
msgid "Доброго дня!"
msgstr "Hello!"

#: app/events/user_actions/types.py
msgid "Додавання користувача у компанію"
msgstr ""

#: app/events/user_actions/types.py
msgid "Додавання обовязкового додаткового параметру"
msgstr ""

#: app/events/user_actions/types.py
msgid "Додавання учасника групи"
msgstr ""

#: app/events/user_actions/types.py
msgid "Додавання ярлику ролі"
msgstr ""

#: app/events/document_actions/types.py
msgid "Додавання ярликів"
msgstr ""

#: app/jinja_templates/email/invite_document.jinja2
msgid "Додайте ваш ключ КЕП/ЕЦП та введіть пароль"
msgstr "Add your digital signature (KEP/ECP) key and enter the password."

#: app/events/document_actions/utils.py app/events/user_actions/utils.py
msgid "Додаткова інфо про подію"
msgstr "Additional info on the event"

#: api/public/validators.py app/documents_fields/validators.py
msgid "Додаткове поле не існує або було видалено"
msgstr "Additional field does not exist or has been deleted"

#: app/billing/schemas.py
msgid "Додатковий тариф може бути тільки на архівні тарифи"
msgstr ""

#: app/profile/validators.py
msgid "Дозволено застосовувати тільки до компаній, що очікують Вашого підтвердження"
msgstr "It is allowed to apply only to companies awaiting your confirmation"

#: api/private/super_admin/views.py
msgid "Дозволені топіки дивись в коді на бекенді, змінна ALLOWED_TOPICS"
msgstr ""

#: api/downloads/utils.py api/errors.py
msgid "Документ"
msgstr "Document"

#: api/downloads/pdf/builders.py
msgid "Документ анульовано"
msgstr "Document is revoked"

#: app/document_revoke/validators.py
msgid "Документ вже має Акт анулювання"
msgstr ""

#: app/jinja_templates/email/restore_document.jinja2
msgid "Документ відновлено"
msgstr ""

#: api/downloads/pdf/builders.py
msgid "Документ відправлено: {datetime}"
msgstr "Document sent: {datetime}"

#: api/downloads/pdf/builders.py
msgid "Документ відхилено: {datetime}"
msgstr "Document rejected: {datetime}"

#: app/documents/resolvers.py
msgid "Документ доступний для користувача в іншій компанії"
msgstr ""

#: app/signatures/validators.py
msgid "Документ знаходиться в архіві"
msgstr ""

#: app/documents/utils.py
msgid "Документ має неопрацьований запит на видалення"
msgstr ""

#: app/documents/utils.py
msgid "Документ можна видалити тільки через запит на видалення"
msgstr ""

#: api/private/super_admin/utils.py
msgid "Документ не видалений"
msgstr ""

#: app/comments/validators.py
msgid "Документ не відправлений. Зовнішні коментарі можна залишати тільки до відправлених документів."
msgstr "The document was not sent. You can only leave external comments on submitted documents."

#: api/shared/validators.py app/documents/resolvers.py app/documents/utils.py
msgid "Документ не знайдено"
msgstr ""

#: app/document_revoke/validators.py
msgid "Документ не може бути анулюваний"
msgstr "The document cannot be revoked"

#: api/errors.py
msgid "Документ не може бути відправлений на власний Email/ЄДРПОУ"
msgstr "The document cannot be sent to your own Email address/company tax ID"

#: app/uploads/validators.py
msgid "Документ не може бути одночасно і внутрішнім і багатостороннім"
msgstr "The document cannot be both internal and multilateral at the same time"

#: app/signatures/validators.py
msgid "Документ не очікує підписів від вашої компанії"
msgstr "The document does not require signatures from your company"

#: app/document_versions/validators.py
msgid "Документ не є версійним"
msgstr "The document is not versioned"

#: api/downloads/pdf/builders.py
msgid "Документ отримано ({edrpou}): {datetime}"
msgstr "Document received ({edrpou}): {datetime}"

#: api/downloads/pdf/builders.py
msgid "Документ переглянуто ({edrpou}): {datetime}"
msgstr "Document viewed ({edrpou}): {datetime}"

#: api/downloads/pdf/builders.py
msgid "Документ переглянуто: {datetime}"
msgstr "Document viewed: {datetime}"

#: app/documents/validators.py
msgid "Документ повинен мати хоча б одного одержувача"
msgstr ""

#: app/documents/validators.py
msgid "Документ повинен мати хоча б одного підписанта"
msgstr ""

#: app/reviews/validators.py
msgid "Документ повинен погодити інший співробітник"
msgstr "Another employee must agree to the document"

#: app/signatures/validators.py
msgid "Документ повинен підписати інший співробітник"
msgstr "Another employee should sign the document"

#: app/trigger_notifications/utils.py
msgid "Документ погоджено"
msgstr "Document approved"

#: app/trigger_notifications/utils.py
msgid "Документ підписано"
msgstr "Document signed"

#: api/downloads/pdf/builders.py
msgid "Документ підписано у сервісі Вчасно"
msgstr "Document is signed on Vchasno service"

#: api/downloads/pdf/builders.py
msgid "Документ підписано у сервісі Вчасно (початок)"
msgstr "Document is signed on Vchasno service (beginning)"

#: api/downloads/pdf/builders.py
msgid "Документ підписано у сервісі Вчасно (продовження)"
msgstr "Document is signed on Vchasno service (continuation)"

#: app/mobile/notifications/notifications.py app/reviews/emailing.py
msgid "Документ, надісланий вами на погодження, було відхилено"
msgstr "The document you submitted for approval was rejected"

#: api/errors.py
msgid "Документи"
msgstr "Documents"

#: app/jinja_templates/email/review_reminder.jinja2
msgid "Документи очікують вашого погодження"
msgstr "Documents are waiting for your approval"

#: worker/emailing/reminders_for_unsigned_documents/jobs.py
msgid "Документи очікують вашого підпису"
msgstr ""

#: app/reviews/emailing.py
msgid "Документи погоджено"
msgstr "Documents approved"

#: app/documents/utils.py
msgid "Документи, до яких створюється запит на видалення, повинні мати отримувачів"
msgstr "Documents for which a deletion request is created must have recipients"

#: api/downloads/validators.py
msgid "Доступ до архіву заборонено. Лише користувач, який створив архів, може його завантажити."
msgstr "Access to the archive is prohibited. Only the user who created the archive can download it."

#: app/documents/validators.py
msgid "Доступ до деяких документів заборонено"
msgstr "Access to some documents is prohibited"

#: app/document_antivirus/validators.py
msgid "Доступ до документа забороненно адміністратором. Документ не перевірено або містить вірус."
msgstr "Access to the document is prohibited by the administrator. The document is not verified or contains a virus."

#: app/documents/validators.py app/signatures/validators.py
msgid "Доступ до документу заборонено"
msgstr "Access to the document is prohibited"

#: app/tags/validators.py
msgid "Доступ до одного або декількох ярликів заборонено"
msgstr "Access to one or more shortcuts is prohibited"

#: api/errors.py
msgid "Доступ до функціоналу не активований"
msgstr "Access to the functionality is not activated"

#: api/errors.py
msgid "Доступ заборонено"
msgstr "Access denied"

#: app/documents_ai/validators.py
msgid "Доступ обмежено адміністратором компанії"
msgstr ""

#: api/errors.py
msgid "Доступ по IP заборонено"
msgstr "Access by IP is denied"

#: app/onboarding/views.py
msgid "Досягнута максимальної кількості доступних ключів у полі extra."
msgstr ""

#: app/billing/constants.py
msgid "Досягнуто денний ліміт оплат по карті. Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку"
msgstr ""

#: app/billing/constants.py
msgid "Досягнуто ліміт оплат по карті. Будь ласка, скористайтеся іншою картою або змініть ліміт звернувшись до свого банку"
msgstr ""

#: app/auth/validators.py
msgid "Досягнуто максимальної кількості співробітників згідно поточного тарифу"
msgstr ""

#: api/downloads/views.py
msgid "Дочекайтеся закінчення генерації попереднього архіву"
msgstr "Wait for the generation of the previous archive to finish"

#: app/documents/validators.py
msgid "Дочірні документи мають інші головні документи"
msgstr "Child documents have other parent documents"

#: app/documents/validators.py
msgid "Дочірній документ може мати лише один головний документ. Видаліть наявні зв'язки та створіть нові."
msgstr "A child document can have only one parent document. Delete existing connections and create new ones."

#: app/documents/emailing.py
msgid "Друзі"
msgstr "Friends"

#: app/events/document_actions/types.py
msgid "Друк документу"
msgstr ""

#: app/document_automation/validators.py
msgid "Дублюючі поля в сценарії"
msgstr "Duplicate fields in the script"

#: api/private/blackbox/validators.py
msgid "Дублікати в електронних адресах отримувача"
msgstr "Duplicates in recipient email addresses"

#: app/flow/validators.py
msgid "Дублікати в кодах ЄДРПОУ отримувачів"
msgstr ""

#: api/private/super_admin/views.py
msgid "Дістати корпоративні домени компанії"
msgstr ""

#: app/document_categories/views.py
msgid "Дістати список публічних типів документів"
msgstr ""

#: api/downloads/utils.py app/events/document_actions/utils.py
#: app/events/user_actions/utils.py
msgid "Дія"
msgstr "DIIA"

#: api/errors.py
msgid "Е-Mail"
msgstr "Email"

#: app/signatures/validators.py
msgid "Еmail не може бути заповнено двічі"
msgstr ""

#: app/jinja_templates/email/bill_reminder_start.jinja2
msgid "Економити до 90 грн на одному документі"
msgstr ""

#: api/downloads/utils.py
msgid "Електронна адреса"
msgstr "E-mail address"

#: api/downloads/pdf/builders.py
msgid "Електронна печатка"
msgstr "Electronic seal"

#: api/downloads/pdf/builders.py
msgid "Електронний підпис"
msgstr "Electronic signature"

#: app/jinja_templates/email/user_email_change_confirm_notice.jinja2
msgid "Електронну адресу змінено"
msgstr ""

#: app/jinja_templates/email/user_email_change_confirm_admin.jinja2
msgid "Електронну адресу користувача змінено"
msgstr ""

#: app/profile/emailing.py
msgid "Електронну пошту змінено"
msgstr ""

#: app/profile/emailing.py
msgid "Електронну пошту користувача змінено"
msgstr ""

#: app/jinja_templates/email/invite_document.jinja2
msgid "Електронні документи мають таку ж силу, як і паперові. Вони не згорять, не загубляться та не зникнуть."
msgstr "Electronic documents have the same legal force as paper ones. They won't burn, get lost, or disappear."

#: api/errors.py
msgid "Електронні пошти не збігаються"
msgstr "Emails do not match"

#: app/jinja_templates/email/new_employee_failed_registration.jinja2
#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
msgid "З турботою, команда"
msgstr ""

#: app/jinja_templates/email/invite_document.jinja2
msgid "З турботою, команда <a href=\"https://vchasno.ua/\">«Вчасно.ЕДО»</a> 💙💛"
msgstr "With care, the team <a href=\"https://vchasno.ua/\">«Vchasno.EDO»</a> 💙💛"

#: app/trigger_notifications/utils.py
msgid "За нашим досвідом це допомагає швидше та легше почати обмінюватись електронними документами."
msgstr "In our experience, this makes it faster and easier to start exchanging electronic documents."

#: app/jinja_templates/email/notification_document_version_add.jinja2
msgid "Завантажена"
msgstr ""

#: app/documents/utils.py
msgid "Завантажений"
msgstr "Uploaded"

#: app/events/document_actions/types.py
msgid "Завантаження документу"
msgstr ""

#: app/events/document_actions/types.py
msgid "Завантаження документу через blackbox"
msgstr ""

#: app/events/document_actions/types.py
msgid "Завантаження нової версії"
msgstr ""

#: app/uploads/validators.py
msgid "Завантаження файлу, вказуючи URL."
msgstr "Download a file by specifying a URL."

#: worker/emailing/reminders_for_new_document_version/jobs.py
msgid "Завантажено нову версію документу \"{title}\""
msgstr ""

#: api/private/super_admin/views.py
msgid "Завантажити дії користувачів для налагодження"
msgstr ""

#: api/private/super_admin/views.py
msgid "Завантажити підписи документа для налагодження"
msgstr ""

#: app/trigger_notifications/utils.py
msgid "Завантажте перелік ваших контрагентів та отримуйте сповіщення, коли вони реєструються!"
msgstr "Download a list of your counterparties and get notified when they sign up!"

#: api/downloads/validators.py
msgid "Загальна кількість документів перевищує ліміт в {limit}"
msgstr ""

#: app/jinja_templates/email/invite_document.jinja2
msgid "Законність і безпека"
msgstr "Legality and security"

#: app/billing/constants.py
msgid "Закінчився термін дії картки."
msgstr ""

#: app/billing/constants.py
msgid "Закінчився час очікування. Будь ласка, спробуйте сплатити ще раз"
msgstr ""

#: app/billing/constants.py
msgid "Закінчився час очікування. Будь ласка, спробуйте ще раз оплати"
msgstr ""

#: app/billing/constants.py
msgid "Закінчився час підтвердження оплати. Будь ласка, спробуйте ще раз оплати"
msgstr ""

#: worker/emailing/jobs.py
msgid "Закінчується термін дії токену у «Вчасно»"
msgstr ""

#: worker/emailing/reminders_for_unpaid_bills/emailing.py
msgid "Залишився 1 день, щоб оплатити рахунок"
msgstr "You have 1 day left to pay the bill"

#: api/errors.py
msgid "Занадто багато файлів, кількість файлів для завантаження не має бути більше ніж {max_total_count}"
msgstr "Too many files, the number of files to upload should not be greater than {max_total_count}"

#: api/errors.py api/graph/exceptions.py
msgid "Запит було завершено за таймаутом"
msgstr "The request was completed by timeout"

#: app/events/document_actions/types.py
#: app/mobile/notifications/notifications.py
msgid "Запит на видалення"
msgstr ""

#: api/errors.py
msgid "Запит на внутрішнє погодження"
msgstr "Request for internal approval"

#: app/jinja_templates/email/user_email_change_start_notice.jinja2
#: app/profile/emailing.py
msgid "Запит на зміну електронної пошти"
msgstr ""

#: app/profile/validators.py
msgid "Запит на зміну електронної пошти не був підтверджений"
msgstr ""

#: app/profile/validators.py
msgid "Запит на зміну електронної пошти не може бути виконаний, оскільки він має неправильний статус. Сформуйте новий запит на зміну електронної пошти"
msgstr ""

#: app/profile/emailing.py
msgid "Запит на зміну пароля"
msgstr "Password change request"

#: api/errors.py
msgid "Запит не містить файлів для завантаження, які підтримуються системою. Перевірте файли, вибрані до завантаження"
msgstr "The request contains no files to download that are supported by the system. Check the files selected before downloading"

#: app/sign_sessions/validators.py
msgid "Запит не містить ідентифікатора сесії підписання"
msgstr "The request does not contain the signature session ID"

#: app/events/document_actions/types.py
msgid "Запит стану документа"
msgstr ""

#: app/reviews/utils.py
msgid "Запросив співробітника {0}"
msgstr "Invited employee {0}"

#: api/private/integrations/handlers.py
msgid "Запросити користувача по email"
msgstr ""

#: app/registration/views.py
msgid "Запросити співробітника або контрагента у сервіс"
msgstr "Invite an employee or counterparty to the service"

#: app/trigger_notifications/utils.py
msgid "Запросіть контрагентів у Вчасно"
msgstr "Invite counterparties on Vchasno"

#: app/reviews/enums.py
msgid "Запрошення на погодження видалено"
msgstr "The request for approval has been removed"

#: app/reviews/enums.py
msgid "Запрошення на погодження створено"
msgstr "The request for approval has been created"

#: app/jinja_templates/email/review_request.jinja2
msgid "Запрошує вас погодити документ у компанії"
msgstr "Asks you to approve the document in the company"

#: app/jinja_templates/email/invite_simple.jinja2
msgid "Запрошує вас почати працювати з документами онлайн у сервісі \"{brand}\"."
msgstr ""

#: app/jinja_templates/email/invite_existing_user.jinja2
msgid ""
"Запрошує вас почати працювати з документами онлайн у сервісі \"{brand}\". \n"
"Щоб почати працювати з документами нової компанії, у верхньому правому куті сервісу переключіться на компанію, в котру вас додали"
msgstr ""

#: app/jinja_templates/email/review_request.jinja2
msgid "Запрошує вас,"
msgstr "Asks you,"

#: app/events/validators.py
msgid "Зараз ми тимчасово вимкнули завантаження звітів про події. Будь ласка, спробуйте трохи пізніше"
msgstr "Now we have temporarily disabled the download of event reports. Please try again later"

#: app/signatures/validators.py
msgid "Зараз черга іншої компанії підписувати послідовний документ"
msgstr "Now it is the turn of another company to sign a ordered document"

#: worker/emailing/utils.py
msgid "Зареєструватись"
msgstr ""

#: app/jinja_templates/email/invite_simple.jinja2
msgid "Зареєструватися"
msgstr "Sign Up"

#: app/jinja_templates/email/invite_simple.jinja2
msgid "Зареєструйтеся, щоб почати."
msgstr ""

#: app/events/document_actions/types.py
msgid "Застосування сценарію"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Зберігання"
msgstr "Storing"

#: app/jinja_templates/email/bill_reminder_start.jinja2
msgid "Зберігати 400 документів"
msgstr ""

#: api/errors.py app/events/validators.py
msgid "Звіт недоступний для завантаження"
msgstr "Report unavailable for download"

#: app/jinja_templates/email/daily_notification_about_finished_documents.jinja2
msgid "Звіт по завершеним документам"
msgstr ""

#: worker/documents/jobs.py
msgid "Звіт по завершеним документам {company_name}"
msgstr ""

#: worker/events/jobs.py
msgid "Звіт про події для ЄДРПОУ {edrpou}"
msgstr "The event report for the EDRPOU {edrpou}"

#: api/private/super_admin/views.py
msgid "Згенерувати токени для всіх компаній користувача"
msgstr ""

#: app/events/document_actions/types.py
msgid "Зміна налаштувань доступу"
msgstr "Access settings change"

#: app/events/user_actions/types.py
msgid "Зміна налаштувань співробітника"
msgstr ""

#: app/events/document_actions/types.py
msgid "Зміна отримувача"
msgstr ""

#: api/private/super_admin/views.py
msgid "Змінити email користувача і оновити всі пов'язані дані, які мають посилання на цей email"
msgstr ""

#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
msgid "Змінити доступи та відправку сповіщень для співробітників ви можете в будь-який час в налаштуваннях, розділ «Співробітники»."
msgstr ""

#: app/documents/validators.py
msgid "Змінити загальні реквізити документа може тільки компанія власник документа"
msgstr ""

#: app/documents/validators.py
msgid "Змінити налаштування доступу до документа може тільки власник документа, отримувач документа або адміністратор компанії"
msgstr "Сhange access settings to the document can only be done by the document owner, the document recipient or the company administrator"

#: app/jinja_templates/email/recover_password.jinja2
msgid "Змінити пароль"
msgstr ""

#: app/billing/views.py
msgid "Змінює статус рахунку на canceled або refund, а також скасовує усі надані рахунком ресурси (тариф, додатковий тариф, документи, розширення користувачів)"
msgstr ""

#: app/billing/validators.py
msgid "Знайдено безтерміновий інтеграційний тариф"
msgstr "Found a integration tariff without date limits"

#: app/documents_fields/validators.py
msgid "Значення параметра не входить в перелік можливих значень"
msgstr ""

#: app/lib/validators.py
msgid "Значення параметра не є рядком"
msgstr ""

#: app/documents_fields/validators.py
msgid "Значення параметра перевищує максимальну довжину"
msgstr ""

#: app/documents_fields/validators.py
msgid "Значення параметра повинно бути рядком"
msgstr ""

#: app/documents_fields/validators.py
msgid "Значення параметра повинно бути числом"
msgstr ""

#: app/lib/validators.py
msgid "Значення повинно бути рядком"
msgstr "Value must be string"

#: app/lib/validators.py
msgid "Значення є невалідним JSON"
msgstr "Value is invalid JSON"

#: api/errors.py
msgid "Зовнішню роль"
msgstr "External role"

#: api/errors.py
msgid "Зовнішня роль"
msgstr "External role"

#: app/documents/validators.py
msgid "Зробити двосторонній документ без одержувачів можна тільки з внутрішнього документа"
msgstr "You can only make a bilateral document without recipients from an internal document"

#: app/documents_ai/views.py
msgid "Зробити коротке резюме документа по document_id"
msgstr ""

#: app/lib/validators.py
msgid "Ймовірна помилка у заголовку Content-Disposition в тілі запиту. Перевірте формування тіла запиту, додайте екранування спеціальних символів (;) при формуванні заголовків"
msgstr ""

#: api/errors.py
msgid "Категорія документу"
msgstr "Document category"

#: app/document_categories/validators.py
msgid "Категорія документу з такою назвою вже існує."
msgstr "A document category with this name already exists."

#: api/errors.py
msgid "Категорії документу"
msgstr "Document categories"

#: api/downloads/archives.py
msgid "Квитанція.pdf"
msgstr "Receipt.pdf"

#: api/errors.py
msgid "Ключ КЕП/ЕЦП"
msgstr "Key QES/EDS"

#: api/errors.py
msgid "Код невірний. Спробуйте ще раз"
msgstr "The code is incorrect. Try again"

#: api/errors.py
msgid "Кодування файлу не підтримується. Спробуйте зберегти файл у кодуванні UTF-8"
msgstr "File encoding is not supported. Try saving the file in UTF-8 encoding"

#: app/jinja_templates/email/layout.jinja2
#, python-format
msgid "Команда %(brand)s бажає вам гарного дня"
msgstr "The %(brand)s team wishes you a good day"

#: app/events/document_actions/types.py
#: app/jinja_templates/email/notification_reject.jinja2
msgid "Коментар"
msgstr "Comment"

#: app/comments/validators.py
msgid "Коментар з ідентичним вмістом вже доданий на документ"
msgstr "The comment with the same content has already been added to the document"

#: api/errors.py
msgid "Компанію"
msgstr "Company"

#: api/shared/validators.py
msgid "Компанію власника не знайдено"
msgstr ""

#: app/documents/validators.py
msgid "Компанію не знайдено"
msgstr ""

#: app/sign_sessions/validators.py
msgid "Компанію не знайдено серед підписантів"
msgstr "Company not found among signatories"

#: api/errors.py
msgid "Компанія"
msgstr "Company"

#: app/documents/emailing.py
msgid "Компанія {company} надіслала вам документи"
msgstr "Company {company} sent you the documents"

#: app/documents/emailing.py
msgid "Компанія {company} надіслала вам документи на підпис"
msgstr "Company {company} sent you the documents for signature"

#: app/documents/emailing.py
msgid "Компанія {name} надіслала вам документи"
msgstr "{name} has sent you the documents"

#: app/documents/emailing.py
msgid "Компанія {name} надіслала вам документи на підпис"
msgstr "The {name} has sent you the documents for signing"

#: app/sign_sessions/validators.py
msgid "Компанія вже підписала документ"
msgstr "The company already signed the document"

#: app/documents/validators.py
msgid "Компанія відправник має бути однією з компаній-отримувачів документа"
msgstr ""

#: api/errors.py
msgid "Компанія не має достатньої кількості документів"
msgstr "The company does not have enough documents"

#: api/private/super_admin/views.py
msgid "Компанія, яка задається в шляху, стає батьківською для компаній, які задаються в тілі запиту, і з її балансу будуть списуватися документи за дочірні компанії"
msgstr ""

#: app/documents/validators.py
msgid "Компанії, що не підписували документ мають бути після компаній, що вже підписали документ"
msgstr "Companies that have not signed the document must be after companies that have already signed the document"

#: app/events/document_actions/types.py
msgid "Конвертація формату версії"
msgstr "Version format conversion"

#: api/errors.py
msgid "Контакт"
msgstr "Contact"

#: app/contacts/validators.py
msgid "Контакт з таким email і ЄДРПОУ вже існує у Вас у контактах."
msgstr "Contact with such an email and tax ID already exists in your contacts."

#: api/errors.py
msgid "Контактна особа"
msgstr "Contact person"

#: app/contacts/validators.py
msgid "Контактна особа не належить поточній компанії"
msgstr "Contact person does not belong to the current company"

#: api/errors.py
msgid "Контактну особу"
msgstr "Contact person"

#: app/documents/validators.py
msgid "Контаргент не призначено на документ"
msgstr "Counterparty not assigned to the document"

#: app/signatures/utils.py
msgid "Контейнер підписано більше ніж {wraps}"
msgstr "The container is signed more than {wraps}"

#: app/jinja_templates/email/inbox_documents.jinja2
#: app/jinja_templates/email/review_reminder.jinja2
msgid "Контрагент:"
msgstr "Counterparty:"

#: api/errors.py
msgid "Конфіг"
msgstr "Config"

#: app/billing/integrations/pumb.py
msgid "Конфіг для ПУМБ не знайдено."
msgstr ""

#: app/billing/integrations/evopay.py
msgid "Конфіг для оплати карткою не знайдено."
msgstr "Config for card payment is not found"

#: app/billing/integrations/privat.py
msgid "Конфіг для приватбанку не знайдено."
msgstr "Config for PrivatBank not found."

#: app/diia/utils.py
msgid "Конфігурація Дії не встановлена"
msgstr ""

#: api/errors.py
msgid "Користувач"
msgstr "User"

#: app/registration/validators.py
msgid "Користувач вже підтвердив поштову адресу."
msgstr "The user has already confirmed the mailing address."

#: api/private/super_admin/views.py
msgid "Користувач вже є активним у компанії"
msgstr ""

#: api/private/integrations/hrs/validators.py
msgid "Користувач вжє має кадрову роль."
msgstr ""

#: api/private/super_admin/utils.py
msgid "Користувач з вказаною поштою не знайдений"
msgstr ""

#: api/private/super_admin/utils.py
msgid "Користувач з вказаною поштою не є адміном"
msgstr ""

#: app/registration/validators.py
msgid "Користувач з таким email вже існує. "
msgstr "User with such email already exists."

#: app/registration/validators.py
msgid "Користувач з таким email вже існує. Вірогідно, ви вже зареєструвались у якомусь з сервісів Вчасно"
msgstr ""

#: app/sign_sessions/utils.py
msgid "Користувач не зареєстрований у Вчасно.ЕДО або не є співробітником даної компанії"
msgstr "The user is not registered in the system or is not an employee of the company provided"

#: app/auth/views.py app/mobile/auth/validators.py
msgid "Користувач не має email"
msgstr "The user does not have an email"

#: api/private/super_admin/views.py
msgid "Користувач не має жодної активної мобільної сесії"
msgstr ""

#: api/errors.py
msgid "Користувач не має жодної ролі в компанії"
msgstr ""

#: api/private/integrations/hrs/validators.py
msgid "Користувач не має кадрової ролі."
msgstr ""

#: app/auth/views.py
msgid "Користувач не має номеру телефона"
msgstr ""

#: app/profile/validators.py
msgid "Користувач не має підтвердженого номера телефону"
msgstr ""

#: app/auth/validators.py
msgid "Користувач не має імейлу для верифікації"
msgstr "The user does not have an email for verification"

#: app/documents/validators.py
msgid "Користувач не може надсилати цей документ"
msgstr "User cannot send this document"

#: api/errors.py
msgid "Користувача"
msgstr "User"

#: app/billing/constants.py
msgid "Користувача даної картки не знайдено"
msgstr ""

#: api/private/super_admin/views.py
msgid "Користувача не знайдено"
msgstr ""

#: app/billing/constants.py
msgid "Кошти зарезервовані для проведення повернення"
msgstr ""

#: app/jinja_templates/email/daily_notification_about_finished_documents.jinja2
msgid "Кількість завершених документів за"
msgstr ""

#: app/billing/utils.py
msgid "Кількість користувачів не може бути меншою за вказану у тарифі"
msgstr ""

#: app/banner/validators.py
msgid "Кінцева дата має бути більшою за початкову"
msgstr "End date must be greater than start date"

#: app/jinja_templates/email/auto_welcome_letter.jinja2
#, python-format
msgid "Ласкаво просимо у %(brand)s!"
msgstr "Welcome to %(brand)s!"

#: app/directories/validators.py
msgid "Максимальна вкладеність папок не має перевищувати {max_depth}"
msgstr ""

#: app/directories/validators.py
msgid "Максимальна кількість папок не має перевищувати {max_count}"
msgstr ""

#: api/private/super_admin/views.py
msgid "Ми згенерували токени для ваших компаній"
msgstr ""

#: app/jinja_templates/email/actions_report_documents.jinja2
msgid "Ми сформували звіт про дії з документами вашої компанії"
msgstr "We have generated a report on the actions with the documents of your company"

#: app/jinja_templates/email/actions_report_users.jinja2
msgid "Ми сформували звіт про дії користувачів вашої компанії"
msgstr "We have generated a report on the actions of the users of your company"

#: app/events/user_actions/types.py
msgid "Мобільний застосунок"
msgstr ""

#: app/documents/validators.py
msgid "Містить недопустимі символи"
msgstr "Contains forbidden characters"

#: worker/emailing/reminders_for_low_billing_balance/utils.py
msgid "На балансі компанії закінчились документи для надсилання"
msgstr ""

#: worker/emailing/reminders_for_low_billing_balance/utils.py
msgid "На балансі компанії закінчуються документи для надсилання"
msgstr ""

#: app/billing/constants.py
msgid "На вашій карті недостатньо коштів"
msgstr ""

#: app/billing/constants.py
msgid "На жаль, оплати заблоковані для даної країни"
msgstr ""

#: app/billing/constants.py
msgid "На картці недостатньо коштів. Будь ласка, скористайтеся іншою картою"
msgstr ""

#: app/auth/phone_auth/validators.py
msgid "На цей номер телефону неможливо зареєструватися, оскільки його вже додано до системи. Якщо це ваш номер телефону і у вас вже є обліковий запис, увійдіть у ваш акаунт, використовуючи інший спосіб автентифікації, та увімкніть вхід за номером телефону в налаштуваннях профілю"
msgstr ""

#: worker/emailing/jobs.py
msgid "Нагадуємо, партнери компанії {company_label} чекають на опрацювання документів"
msgstr ""

#: worker/emailing/reminders_for_unpaid_bills/emailing.py
msgid "Нагадуємо, що вам потрібно оплатити рахунок до {date}, щоб користувачі, яких ви додали могли користуватися можливостями Вчасно."
msgstr "We remind you that you need to pay the bill by {date} so that the users you added can use the Vchasno features."

#: app/events/document_actions/types.py
msgid "Надання доступу"
msgstr ""

#: app/events/user_actions/types.py
msgid "Надання доступу до додаткового параметру"
msgstr ""

#: app/trigger_notifications/utils.py
msgid "Надсилайте безкоштовно перші 25 документів!"
msgstr "Send the first 25 documents for free!"

#: app/jinja_templates/email/notification_document.jinja2
#: app/jinja_templates/email/notification_document_version_add.jinja2
#: app/jinja_templates/email/zakupki_notification_document.jinja2
msgid "Надіслав вам"
msgstr "Sent you"

#: app/jinja_templates/email/zakupki_notification_document.jinja2
msgid "Надіслав вам документ"
msgstr "Sent you a document"

#: app/jinja_templates/email/notification_document.jinja2
#: app/jinja_templates/email/notification_document_version_add.jinja2
msgid "Надіслав вам документ до компанії"
msgstr "Sent you a document to the company"

#: app/jinja_templates/email/notification_comment.jinja2
msgid "Надіслав коментар до документу"
msgstr "Sent a comment to the document"

#: app/documents/utils.py
msgid "Надісланий контрагенту"
msgstr "Sent to the counterparty"

#: app/documents/utils.py
msgid "Надісланий на перший підпис контрагенту"
msgstr "Sent to the counterparty for the first signature"

#: api/downloads/pdf/builders.py api/downloads/utils.py
msgid "Назва"
msgstr "Name"

#: app/events/document_actions/utils.py
msgid "Назва документа"
msgstr "Document name"

#: app/jinja_templates/email/inbox_documents.jinja2
#: app/jinja_templates/email/review_reminder.jinja2
msgid "Назва:"
msgstr "Name:"

#: api/shared/validators.py
msgid "Налаштування компанії не дозволяють перегляд документа"
msgstr "Company settings do not allow viewing the document"

#: app/jinja_templates/email/new_employee_failed_registration.jinja2
msgid "Намагався додатися співробітник"
msgstr "Tried to add an employee"

#: app/billing/validators.py
msgid "Наразі комбіновані рахунки не підтримують більше ніж два сервіси"
msgstr ""

#: app/documents/validators.py
msgid "Наразі конвертація в PDF доступна тільки для тієї компанії, що повинна підписати останню версію документа. Зазвичай має підписувати та сторона, які надіслали версію документа"
msgstr "Currently, PDF conversion is only available for the company that should sign the latest version of the document. Usually, the company that received the document version should sign it"

#: app/documents/validators.py
msgid "Наразі не можливо змінити двосторонній документ на багатосторонній після того як розпочато підписання"
msgstr "Currently, it is not possible to change a bilateral document to a multilateral one after signing has started"

#: app/documents/validators.py
msgid "Наразі не можливо змінити двосторонній документ на внутрішній після того як розпочато підписання"
msgstr "Currently, it is not possible to change a bilateral document to an internal one after signing has started"

#: app/documents/validators.py
msgid "Наразі не можливо змінити зовнішній документ на внутрішній після того як розпочато підписання"
msgstr "Currently, it is not possible to change an external document to an internal one after signing has started"

#: app/documents/validators.py app/uploads/validators.py
msgid "Наразі не підтримується звʼязування для приватних документів. Змініть налаштування приватності документів та спробуйте ще раз"
msgstr "Currently, linking for private documents is not supported. Change the document privacy settings and try again"

#: app/billing/validators.py
msgid "Наразі не підтримується комбінація сервісів для купівлі інтеграції і документів"
msgstr ""

#: app/jinja_templates/email/review_request.jinja2
msgid "Натисніть кнопку \"Погодити\", якщо Вас все влаштовує в документі, або \"Відхилити\", якщо щось не влаштовує."
msgstr "Click the \"Approve\" button if you are satisfied with the document, or \"Reject\" if something is wrong."

#: app/jinja_templates/email/invite_document.jinja2
msgid "Натисніть на кнопку «Підписати документ» у цьому листі"
msgstr "Click the «Sign Document» button in this email."

#: api/private/super_admin/views.py
msgid "Не валідний survey_id"
msgstr ""

#: api/errors.py
msgid "Не валідний zip-архів"
msgstr "Invalid zip archive"

#: api/errors.py
msgid "Не валідні часові рамки. Можна отримати інформацію не більше як за {days} днів"
msgstr "Invalid time frame. You can get information for no more than {days} days"

#: app/billing/validators.py
msgid "Не вдалось визначити суму рахунку"
msgstr ""

#: app/billing/constants.py
msgid "Не вдалося відправити смс. Будь ласка, спробуйте ще раз"
msgstr ""

#: app/billing/constants.py
msgid "Не вдалося завершити операцію, нам дуже шкода. Будь ласка, спробуйте ще раз оплати"
msgstr ""

#: app/billing/constants.py
msgid "Не вдалося завершити операцію. Будь ласка, спробуйте ще раз"
msgstr ""

#: api/errors.py
msgid "Не вдалося згенерувати PDF файл через помилку стороннього сервісу"
msgstr "Could not generate PDF file due to third-party service error"

#: app/billing/views.py
msgid "Не вдалося згенерувати файл рахунку"
msgstr "Failed to generate invoice file"

#: app/signatures/validators.py
msgid "Не вдалося знайти останню версію, завантажену і відправлену іншою стороною"
msgstr "Could not find the last version uploaded and sent by the other company"

#: app/lib/gotenberg.py
msgid "Не вдалося конвертувати файл в PDF"
msgstr "Failed to convert file to PDF"

#: app/billing/validators.py
msgid "Не вказано жодного сервісу для рахунку"
msgstr ""

#: app/documents_fields/validators.py
msgid "Не вказані можливі значення параметра"
msgstr ""

#: api/errors.py
msgid "Не всі документи в статусі \"Завершено\""
msgstr "Not all documents in the \"Completed\" status"

#: app/reviews/emailing.py
msgid "Не забудьте погодити документи в компанії {company_label}"
msgstr ""

#: app/jinja_templates/email/user_email_change_start_notice.jinja2
#, python-format
msgid "Не змінювали електронну адресу? Перейдіть за <a href=\"%(cancel_url)s\">посиланням<a/>, щоб відмінити зміни."
msgstr ""

#: api/private/super_admin/utils.py
msgid "Не знайдено дію завантаження документа"
msgstr ""

#: app/profile/validators.py
msgid "Не знайдено запит на зміну електронної пошти"
msgstr ""

#: app/documents/validators.py
msgid "Не знайдено останньої надісланої версії документа"
msgstr "The last sent version of the document was not found"

#: api/errors.py
msgid "Не знайдено правильно підготовлених рядків даних"
msgstr "No properly prepared data rows found"

#: app/registration/validators.py
msgid "Не знайдено токен для підтвредження компанії"
msgstr "No token found for company verification"

#: app/documents_ai/validators.py
msgid "Не знайдено файл у запиті"
msgstr "File not found in the request"

#: app/documents/validators.py
msgid "Не можемо знайти підібраний email. Спробуйте підібрати його знову."
msgstr "We cannot find the selected email. Try to select it again."

#: app/documents/validators.py
msgid "Не можливо додати контрагентів до внутрішнього документу після того як розпочалося підписання документу"
msgstr "It is not possible to add counterparties to an internal document after signing has started"

#: app/documents/validators.py
msgid "Не можливо змінити загальні реквізити документа, якщо процес підписання вже розпочато"
msgstr ""

#: app/banner/validators.py
msgid "Не можливо змінити статус банера"
msgstr "Banner status cannot be changed"

#: app/directories/validators.py
msgid "Не можливо перемістити папку {directory} в одну із свої дочірніх папок {child}"
msgstr ""

#: app/directories/validators.py
msgid "Не можливо перемістити папку саму в себе"
msgstr ""

#: app/sign_sessions/validators.py
msgid "Не можливо створити сесію підписання цією компанією"
msgstr "Unable to create a signing session with this company"

#: app/billing/validators.py
msgid "Не можна запланувати більш ніж один той самий тариф"
msgstr "You can not plan more than one rate at a time."

#: app/profile/validators.py
msgid "Не можна змінювати свою роль в компанії"
msgstr ""

#: app/billing/validators.py
msgid "Не можна оплачувати рахунок нижче за тарифом активного"
msgstr "Payment to lower rate than actual is restricted"

#: app/billing/validators.py
msgid "Не можна подовжувати поточний тариф раніше ніж за 45 днів до кінця"
msgstr ""

#: app/billing/validators.py
msgid "Не можна подовжувати поточний інтеграційний тариф раніше ніж за 45 днів до кінця"
msgstr ""

#: app/billing/integrations/evopay.py
msgid "Неавторизований сервіс."
msgstr "Unauthorized service"

#: app/uploads/validators.py
msgid "Невалідна кількість підписантів"
msgstr "Invalid number of signatories"

#: api/errors.py
msgid "Невалідний XML"
msgstr "Invalid XML"

#: app/lib/validators.py
msgid "Невалідний заголовок запиту або тіла у форматі multipart/form-data. Перші рядки тіла зпиту повинні бути валідними заголовками multipart/form-data"
msgstr "Invalid request header or body in multipart/form-data format. The first lines of the request body must be valid multipart/form-data headers"

#: api/errors.py
msgid "Невалідний запит до серверу"
msgstr "Invalid request to the server"

#: api/errors.py
msgid "Невалідний метод аутентифікації"
msgstr "Invalid authentication method"

#: api/errors.py
msgid "Невалідний номер телефону, вкажіть інший, будь ласка"
msgstr "Invalid phone number, please enter a different one"

#: api/errors.py
msgid "Невалідний статус відповіді з вказаного серверу"
msgstr "Invalid response status from the specified server"

#: api/errors.py
msgid "Невалідний статус документу"
msgstr "Invalid document status"

#: api/shared/validators.py api/shared/views.py
msgid "Невалідний токен сесії підписання"
msgstr ""

#: api/public/validators.py
msgid "Невалідний формат логів"
msgstr "Invalid log format"

#: app/documents/utils.py
msgid "Невизначений статус"
msgstr "Undefined status"

#: app/auth/providers/apple/client.py
msgid "Невідома помилка автентифікації за допомогою Apple"
msgstr ""

#: app/auth/providers/google.py
msgid "Невідома помилка автентифікації за допомогою Google"
msgstr "Unknown Google Authentication Error"

#: api/graph/exceptions.py
msgid "Невідома помилка під час виконання запиту."
msgstr "Unknown error during request execution."

#: app/lib/validators.py
msgid "Невідома проблема під час розбору multipart-form/data"
msgstr ""

#: api/public/validators.py
msgid "Невідомий статус документа"
msgstr ""

#: app/billing/views.py
msgid "Невідомий статус оплати від EvoPay"
msgstr "Unknown payment status from EvoPay"

#: app/mobile/notifications/views.py
msgid "Невідомий статус сповіщення"
msgstr ""

#: app/billing/constants.py
msgid "Невірний код CVV (вказано на зворотній стороні вашої картки)"
msgstr ""

#: app/billing/constants.py
msgid "Невірний код підтвердження. Будь ласка, перевірте і спробуйте ще раз"
msgstr ""

#: app/billing/constants.py
msgid "Невірний номер телефону. Перевірте його ще раз"
msgstr ""

#: app/documents/validators.py
msgid "Невірний формат даних для тегів"
msgstr ""

#: app/sign_sessions/validators.py
msgid "Невірний ідентифікатор сесії підписання"
msgstr ""

#: app/documents_ai/validators.py
msgid "Недопустимий формат файлу"
msgstr "Invalid file format"

#: app/auth/validators.py
msgid "Недійсний токен підтвердження електронної пошти"
msgstr "Invalid email confirmation token"

#: app/billing/constants.py
msgid "Некоректна сума зарахування. Будь ласка, перевірте і спробуйте ще раз"
msgstr ""

#: app/auth/validators.py
msgid "Некоректний порядок учасників у послідовному погодженні. Не можна додати користувача без погодження перед існуючим запитом з погодженням"
msgstr "Incorrect order of participants in sequential agreement. Cannot add a user without approval before an existing approval request"

#: app/profile/validators.py
msgid "Немає доступу до запиту на зміну електронної пошти. Увійдіть у систему як користувач з яким був створений запит або сформуйте новий запит на зміну електронної пошти"
msgstr ""

#: app/documents/validators.py
msgid "Немає прав для видалення документу."
msgstr "No rights to delete document."

#: app/documents/utils.py
msgid "Немає прав на видалення документу"
msgstr ""

#: app/registration/validators.py
msgid "Неможилво виконати дію використовуючи даний метод автентифікації"
msgstr "Unable to perform the action using the given authentication method"

#: api/errors.py
msgid "Неможливо {action} документ, що знаходиться на обов'язковому погодженні Вашими співробітниками."
msgstr "It is not possible to {action} a document that is subject to mandatory approval by your employees."

#: app/billing/validators.py
msgid "Неможливо активувати тестовий період для тарифу повторно"
msgstr ""

#: app/billing/validators.py
msgid "Неможливо активувати тестовий період для інтеграційного тарифу повторно"
msgstr ""

#: app/billing/validators.py
msgid "Неможливо активувати тестовий період для інтеграційного тарифу, якщо для компанії цей тариф вже було активовано"
msgstr ""

#: api/errors.py
msgid "Неможливо веріфікувати внутрішній підпис документу"
msgstr "The internal signature of the document cannot be verified"

#: api/errors.py
msgid "Неможливо веріфікувати внутрішній підпис документу через невідповідність ключів всередині контейнера"
msgstr "The internal signature of the document cannot be verified due to a key mismatch inside the container"

#: api/errors.py
msgid "Неможливо веріфікувати внутрішній підпис документу через помилку читання змісту документу з контейнера"
msgstr "The internal signature of a document cannot be verified due to an error reading the document content from the container"

#: api/errors.py
msgid "Неможливо веріфікувати внутрішній підпис документу, кількість очікуваних підписів не співпадає з кількістю підписів в контейнері"
msgstr "The internal signature of the document cannot be verified. The number of expected signatures does not match the number of signatures in the container"

#: api/errors.py
msgid "Неможливо веріфікувати підпис документу"
msgstr "Document signature cannot be verified"

#: app/documents/utils.py
msgid "Неможливо видалити архівований документ"
msgstr ""

#: app/documents/utils.py
msgid "Неможливо видалити документ"
msgstr "Unable to delete document"

#: app/documents/utils.py
msgid "Неможливо видалити документ, оскільки ваша компанія не є власником цього документу"
msgstr ""

#: app/documents/utils.py
msgid "Неможливо видалити документ, оскільки він підписаний одним з отримувачів"
msgstr ""

#: app/documents/validators.py
msgid "Неможливо видалити документу"
msgstr ""

#: app/documents/utils.py
msgid "Неможливо видалити завершений документ"
msgstr ""

#: app/reviews/validators.py
msgid "Неможливо видалити запит на погодження, якщо його вже погоджено"
msgstr "It is not possible to delete a consent request if it has already been consented"

#: app/documents/validators.py
msgid "Неможливо видалити контрагента двостороннього документа, коли вже розпочато підписання. Натомість ви можете змінити чи додати нового контрагента до такого документа"
msgstr ""

#: app/documents/validators.py
msgid "Неможливо видалити контрагента, який вже підписав документ"
msgstr "It is not possible to delete a counterparty who has already signed the document"

#: app/documents/validators.py
msgid "Неможливо видалити контрагентів на документі, після того як розпочалося підписання. Натомість ви можете змінити чи додати нового контрагента до такого документа"
msgstr "It is not possible to delete counterparties on a document after signing has started. Instead, you can change or add a new counterparty to such a document"

#: app/documents/validators.py
msgid "Неможливо видалити контрагенів, які вже підписали документ. ЄДРПОУ компаній, яких немає в оновленому списку контрагентів: {removed_edrpous}"
msgstr "It is not possible to delete counterparties who have already signed the document. Tax ID of companies that are not in the updated list of counterparties: {removed_edrpous}"

#: app/directories/validators.py
msgid "Неможливо видалити папки, оскільки вони містять документи"
msgstr ""

#: app/documents/validators.py
msgid "Неможливо видалити підписанта документа, коли він вже підписав документ"
msgstr "Cannot delete a document signer when they have already signed the document"

#: api/errors.py
msgid "Неможливо видалити рахунок"
msgstr "Unable to delete account"

#: app/billing/validators.py
msgid "Неможливо видалити рахунок данного типу."
msgstr "Unable to delete account of this type."

#: app/reviews/validators.py
msgid "Неможливо видалити себе з паралельного погодження, якщо Ви не є його ініціатором"
msgstr "It is not possible to remove yourself from the parallel consent, if you are not its initiator"

#: app/reviews/validators.py
msgid "Неможливо видалити себе з послідовного погодження, якщо Ви не є його ініціатором"
msgstr "It is not possible to remove yourself from the sequential consent, if you are not its initiator"

#: api/errors.py
msgid "Неможливо видалити єдину роль користувача з компанії"
msgstr "Unable to remove a single user role from the company"

#: app/lib/validators.py
msgid "Неможливо визначити кінець тіла запиту. Ймовірні причини помилки: неправильне використання boundary або заголовку запиту Content-Length (загловок не обов'язковий)"
msgstr "Unable to determine the end of the request body. Possible causes of the error: incorrect use of boundary or Content-Length request header (the header is optional)"

#: app/documents/validators.py
msgid "Неможливо виконати дію над документом, оскільки компанія не є власником документа"
msgstr ""

#: api/errors.py
msgid "Неможливо виконати запит до версії {version}. Доступні версії: {expected}"
msgstr "The {version} request cannot be executed. Available versions: {expected}"

#: api/errors.py
msgid "Неможливо виконати запит, функціонал не реалізовано"
msgstr "The request cannot be executed, the functionality is not implemented"

#: api/errors.py
msgid "Неможливо виконати цю дію"
msgstr "Unable to perform this action"

#: app/uploads/validators.py
msgid "Неможливо вказати власний ЄДРПОУ як одержувача"
msgstr "Cannot specify your own tax ID as a recipient"

#: api/errors.py
msgid "Неможливо відправити повідомлення через помилку e-mail серверу"
msgstr "Unable to send message due to server email error"

#: app/documents/validators.py
msgid "Неможливо відхилити документ після підписання"
msgstr "It is impossible to reject the document after signing"

#: app/documents/validators.py
msgid "Неможливо відхилити документ, використовуючи ЄДРПОУ поточного користувача"
msgstr "It is not possible to reject a document using the current user's tax ID"

#: app/documents/validators.py
msgid "Неможливо відхилити документ, не достатньо прав"
msgstr "Unable to reject the document, not enough rights"

#: app/billing/validators.py
msgid "Неможливо додати більше 10 000 співробітників у компанію"
msgstr ""

#: app/auth/validators.py
msgid "Неможливо додати в погодження видалену групу"
msgstr "Unable to add deleted group to approval"

#: app/auth/validators.py
msgid "Неможливо додати в погодження групу без учасників"
msgstr ""

#: app/document_versions/validators.py
msgid "Неможливо додати версію для документу з цією категорією."
msgstr "Unable to add a version for a document with this category."

#: app/document_versions/validators.py
msgid "Неможливо додати версію до завершеного документу."
msgstr "Unable to add a version to a completed document."

#: app/document_versions/validators.py
msgid "Неможливо додати версію до надісланого документу."
msgstr "Unable to add a version to a sent document."

#: app/documents_fields/validators.py
msgid "Неможливо додати додатковий параметр без користувача"
msgstr "Cannot add additional parameter without user"

#: app/documents/validators.py
msgid "Неможливо додати підписантом групу без учасників"
msgstr ""

#: app/signatures/validators.py
msgid "Неможливо допідписати документ анонімно"
msgstr "It is not possible anonymously to add extra sign to the document"

#: api/errors.py
msgid "Неможливо завантажити порожній файл"
msgstr "Unable to upload an empty file"

#: api/errors.py
msgid "Неможливо завантажити підпис іншої компанії"
msgstr "Unable to upload another company's signature"

#: api/errors.py
msgid "Неможливо завантажити файли іншої компанії. Будь ласка, переконайтеся, що всі файли належать до вашої поточної компанії"
msgstr "You can't upload files from another company. Please make sure that all files belong to your current company"

#: api/errors.py
msgid "Неможливо запросити користувача з вказаним email. У компанії {edrpou} використовується email адреса, що закінчується на {domains}"
msgstr "You can't invite a user with the specified email address. {Edrpou} uses an email address ending in {domains}"

#: api/errors.py
msgid "Неможливо зареєструватися в цій компанії, використовуючи Ваш email. Спробуйте змінити email на валідний для компанії, яка надіслала Вам запрошення у сервіс або пройдіть реєстрацію самостійно."
msgstr "You can't register with this company using your email address. Try changing your email address to a valid one for the company that sent you the invitation to the service, or register yourself."

#: app/documents/validators.py
msgid "Неможливо змінити версійність документу, після початку процесу підписання"
msgstr ""

#: app/profile/validators.py
msgid "Неможливо змінити електронну пошту на вказану, оскільки в одній або декількох компаніях використовується корпоративна електронна пошта, яка не співпадає з новою електронною поштою"
msgstr ""

#: app/documents/validators.py
msgid "Неможливо змінити налаштування чи компанія підписувати чи ні для компаній, що вже підписали документ. ЄДРПОУ компаній, що вже підписали: {edrpou_list}"
msgstr ""

#: app/documents/validators.py
msgid "Неможливо змінити порядок підписання для контрагентів, що вже підписали документ"
msgstr "It is not possible to change the signing order for counterparties who have already signed the document"

#: app/documents/validators.py
msgid "Неможливо змінити порядок підписання, якщо один з контрагентів вже підписав документ"
msgstr ""

#: app/profile/validators.py
msgid "Неможливо змінити суперадмінські дозволи для користувача іншої компанії"
msgstr ""

#: app/profile/validators.py
msgid "Неможливо змінити телефон, якщо він не верифікований. Будь ласка, спочатку виконайте верифікацію телефону."
msgstr "Unable to change the phone number if it is not verified. Please verify your phone number first."

#: app/uploads/validators.py
msgid "Неможливо знайти погоджувача"
msgstr "Unable to find conciliator"

#: app/uploads/validators.py
msgid "Неможливо знайти підписанта"
msgstr "Unable to find signer"

#: api/errors.py
msgid "Неможливо зчитати підпис документу"
msgstr "Document signature cannot be read"

#: api/errors.py
msgid "Неможливо конвертувати підпис формату {signature_format}"
msgstr ""

#: app/billing/validators.py
msgid "Неможливо купити тариф \"Максимальний\" через веб. Зверніться до менеджера з продажу"
msgstr ""

#: app/sign_sessions/validators.py
msgid "Неможливо надати доступ до сесії підписання по запиту користувача"
msgstr "Unable to grant access to the signing session requested by the user"

#: app/documents_fields/validators.py
msgid "Неможливо одночасно видалити і оновити параметр документа"
msgstr "Unable to delete and update a document parameter at the same time"

#: app/sign_sessions/validators.py
msgid "Неможливо оновити сесію підписання іншого користувача"
msgstr "Unable to refresh another user's signing session"

#: api/errors.py
msgid "Неможливо опрацювати даний тип акаунта"
msgstr "This type of account cannot be processed"

#: api/errors.py
msgid "Неможливо опрацювати даний тип бонуса"
msgstr "This type of bonus cannot be processed"

#: api/validators.py
msgid "Неможливо отримати доступ до API, використовуючи поточний токен"
msgstr "Unable to access the API using the current token"

#: app/lib/validators.py
msgid "Неможливо отримати значення рядка у форматі base64"
msgstr "Unable to get string value in base64 format"

#: app/documents/validators.py
msgid "Неможливо пов'язати документ з самим собою"
msgstr "It is not possible to link the document to itself"

#: app/billing/utils.py
msgid "Неможливо подовжити поточний тариф через відсутність кінцевої дати."
msgstr "Unable to extend current rate due to end_date is not exist."

#: app/documents/validators.py
msgid "Неможливо прибрати версійність документу, бо версія документу вже була відправлена контрагенту"
msgstr ""

#: app/documents/validators.py
msgid "Неможливо прибрати версійність документу, бо він має більше однієї версії"
msgstr ""

#: app/uploads/validators.py
msgid "Неможливо призначити прихований емейл для запиту без користувача"
msgstr "Unable to assign a hidden email to a request without a user"

#: app/signatures/validators.py
msgid "Неможливо підписати відхилений документ"
msgstr "Unable to sign a rejected document"

#: app/diia/handlers.py
msgid "Неможливо підписати документ без користувача"
msgstr ""

#: app/signatures/validators.py
msgid "Неможливо підписати документ лише печаткою"
msgstr "It is not possible to sign a document with a seal only"

#: app/signatures/validators.py
msgid "Неможливо підписати документ, використовуючи ЄДРПОУ поточного користувача"
msgstr "It is not possible to sign a document using the current user's tax ID"

#: api/errors.py
msgid "Неможливо підписати документ, використовуючи вказане ЄДРПОУ"
msgstr "It is not possible to sign a document using the company tax ID"

#: app/signatures/validators.py
msgid "Неможливо підписати документ, використовуючи вказаний ключ КЕП/ЕЦП. Документ вже підписаний цим ключем КЕП/ЕЦП"
msgstr "It is not possible to sign the document using the specified QES/EDS key. The document has already been signed with this QES/EDS"

#: app/signatures/validators.py
msgid "Неможливо підписати документ, використовуючи вказану печатку. Документ вже підписано цією печаткою"
msgstr "It is not possible to sign a document using the specified seal. The document has already been signed with this seal."

#: app/signatures/validators.py
msgid "Неможливо підписати документ, використовуючи сесію підписання"
msgstr "It is not possible to sign a document using a signing session"

#: app/signatures/validators.py
msgid "Неможливо підписати порожній файл"
msgstr "Unable to sign an empty file"

#: app/documents/validators.py
msgid "Неможливо редагувати підписантів документа, коли документ знаходиться на підписанні у іншій компанії"
msgstr "It is not possible to edit the signatories of the document when the document is being signed in another company"

#: app/documents/validators.py
msgid "Неможливо редагувати підписантів завершеного документа"
msgstr "Unable to edit the signatories of the completed document"

#: api/errors.py
msgid "Неможливо розархівувати файл"
msgstr "Unable to unzip file"

#: app/lib/validators.py
msgid "Неможливо розкодувати дані у base64"
msgstr "Cannot decode data to base64"

#: api/errors.py
msgid "Неможливо розібрати завантаженний XML через помилку: {err}"
msgstr "Unable to parse loaded XML due to error: {err}"

#: app/documents/validators.py
msgid "Неможливо створити більше ніж {limit} дочірніх документів для одного документу"
msgstr "Cannot create more than {limit} child documents for a single document"

#: api/public/validators.py
msgid "Неможливо створити користувача з вказаною адресою електронної пошти, оскільки у компанії з ЄДРПОУ {edrpou} використовується корпоративна електронна пошта"
msgstr "It is not possible to create a user with the specified email address, because the company with tax ID {edrpou} uses a corporate email"

#: app/registration/validators.py
msgid "Неможливо створити нового користувача"
msgstr "Неможливо створити нового користувача"

#: app/billing/validators.py
msgid "Неможливо створити рахунок"
msgstr ""

#: app/registration/validators.py
msgid "Неможливо створити роль для користувача без email. Компанія {edrpou} має налаштування яке вимагає, щоб email адреса користувача закінчувалась на {domains}"
msgstr "You can't create a role for a user without an email. The company {edrpou} has settings that require the user's email address to end with {domains}"

#: api/errors.py app/registration/validators.py
msgid "Неможливо створити роль для користувача з вказаним email. У компанії {edrpou} використовується email адреса, що закінчується на {domains}"
msgstr "You can't create a role for a user with the specified email address. {Edrpou} uses an email address ending in {domains}"

#: app/registration/validators.py
msgid "Неможливо створити роль підписувача без ЄДРПОУ компанії"
msgstr ""

#: app/tags/validators.py
msgid "Неможливо створити ярлики без авторизації"
msgstr ""

#: app/tags/validators.py
msgid "Неможливо створити ярлики, які вже існують в компанії: {names}. Зверніться до адміністратора компанії, щоб він надав доступ до цих ярликів."
msgstr "Unable to create shortcuts that already exist in the company: {names}. Contact your company administrator to grant access to these shortcuts."

#: api/errors.py
msgid "Неможливо сформувати архів, один або більше файлів не знайдено"
msgstr "Unable to create the archive, one or more files not found"

#: api/errors.py
msgid "Неможливо сформувати запит на видалення, один або більше документів не знайдено"
msgstr "Unable to generate deletion request, one or more documents not found"

#: api/errors.py
msgid "Необроблена помилка серверу"
msgstr "Unhandled server error"

#: app/billing/constants.py
msgid "Необхідна перевірка даних карти"
msgstr ""

#: app/billing/constants.py
msgid "Необхідна перевірка платежу. Будь ласка, очікуйте"
msgstr ""

#: app/profile/validators.py
msgid "Необхідно вказати email для активації 2FA"
msgstr "Email is required to activate 2FA"

#: app/profile/validators.py
msgid "Необхідно вказати електронну пошту, щоб вимкнути вхід за номером телефону"
msgstr "Email is required to disable phone number login"

#: app/profile/validators.py
msgid "Необхідно вказати електронну пошту, щоб змінити пароль"
msgstr "Email is required to change the password"

#: app/billing/constants.py
msgid "Необхідно підтвердження карти"
msgstr ""

#: app/billing/constants.py
msgid "Необхідно підтвердження клієнта. Одноразовий пароль відправлений на номер телефону клієнта"
msgstr ""

#: app/billing/constants.py
msgid "Непередбачена помилка, нам дуже шкода. Будь ласка, спробуйте сплатити ще раз"
msgstr ""

#: app/billing/validators.py
msgid "Неправильна комбінація сервісів для формування рахунку. Наразі можливо формувати рахунок для купівля тарифу інтеграція і поповнення балансу документів або купівля веб і архів тарифу"
msgstr ""

#: app/billing/constants.py
msgid "Неправильна сума оплати або валюта. Будь ласка, перевірте і спробуйте ще раз"
msgstr ""

#: app/billing/validators.py
msgid "Неправильна сума оплати рахунку"
msgstr ""

#: app/billing/constants.py
msgid "Неправильне ім'я одержувача. Будь ласка, перевірте і повторіть оплату"
msgstr ""

#: app/billing/constants.py
msgid "Неправильний PIN. Будь ласка, перевірте і спробуйте ще раз"
msgstr ""

#: app/billing/validators.py
msgid "Неправильний запит на формування рахунку"
msgstr ""

#: api/graph/exceptions.py
msgid "Неправильний запит."
msgstr "Incorrect request."

#: app/flow/validators.py
msgid "Неправильний порядок підписання. Дублікати в кодах ЄДРПОУ послідовних отримувачів"
msgstr ""

#: app/flow/validators.py
msgid "Неправильний порядок підписання. Має бути послідовність цілих невідємних чисел, без дублікатів чи пропусків."
msgstr "Incorrect signature order. Must be a sequence of non-negative integers, with no duplicates or gaps."

#: app/registration/validators.py
msgid "Неправильно підписано токен реєстрації"
msgstr ""

#: api/private/super_admin/views.py
msgid "Новий email користувача буде виглядати наступним чином: {uuid}@deleted.vchasno.ua"
msgstr ""

#: app/drafts/types.py app/drafts/validators.py
#: app/mobile/notifications/notifications.py
msgid "Новий документ"
msgstr "New document"

#: app/profile/validators.py
msgid "Новий пароль співпадає з поточним"
msgstr "The new password matches the current one"

#: app/auth/views.py
msgid "Новий список IP-адрес не містить поточної IP-адреси.Переконайтеся, що у вас є доступ до системи з новими IP-адресами."
msgstr "The new list of IP addresses does not contain the current IP address. Make sure you have access to the system with the new IP addresses."

#: worker/emailing/reminders_for_inbox_documents/jobs.py
msgid "Нові документи в компанії"
msgstr ""

#: api/downloads/pdf/builders.py api/downloads/utils.py
msgid "Номер"
msgstr "Number"

#: api/downloads/pdf/builders.py
msgid "Номер документу: {0}"
msgstr "Document number: {0}"

#: api/errors.py
msgid "Номер телефону поки що не підтверджено"
msgstr "The phone number has not yet been confirmed"

#: app/jinja_templates/email/review_reminder.jinja2
msgid "Номер:"
msgstr "Number:"

#: api/private/super_admin/views.py
msgid "Обʼєднати баланси компанії"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Обмін"
msgstr "Exchange"

#: app/documents_fields/validators.py
msgid "Обовʼязковий параметр незаповнений"
msgstr "Required parameter is not filled"

#: app/billing/constants.py
msgid "Обробка операції"
msgstr ""

#: api/public/validators.py
msgid "Один з параметрів \"user_to_email\" і \"group_to_name\" повинен бути заповненим"
msgstr ""

#: api/errors.py
msgid "Один чи більше з вибраних файлів мають однаковий тип та номер. Перевірте своє завантаження"
msgstr "One or more of the selected files have the same type and number. Check your download"

#: app/jinja_templates/email/new_employee_failed_registration.jinja2
msgid "Однак ваш поточний тариф вичерпав можливість додавання нових співробітників."
msgstr ""

#: app/jinja_templates/email/daily_notification_about_finished_documents.jinja2
msgid "Ознайомитись із документами"
msgstr ""

#: api/private/super_admin/tmp_csp/views.py
msgid "Оновити CSP policy для застосунку. Тимчасовий ендпоінт"
msgstr ""

#: api/private/super_admin/views.py app/auth/views.py
msgid "Оновити дозволи для компанії"
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновити корпоративні домени компанії"
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновити налаштування білінгу в компанії (BillingComapnyConfig)"
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновити налаштування компанії (CompanyConfig)"
msgstr ""

#: app/document_categories/views.py
msgid "Оновити публічний тип документу"
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновити статус погодження для документа"
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновити ціну за документ для компанії"
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновити ціну за користувача для компанії на тарифі \"Максимальний\""
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновити інформацію про тариф в CRM"
msgstr ""

#: app/events/user_actions/types.py
msgid "Оновлення email користувача"
msgstr "User email update"

#: app/events/user_actions/types.py
msgid "Оновлення даних користувача"
msgstr ""

#: app/events/user_actions/types.py
msgid "Оновлення налаштувань антивірусу"
msgstr ""

#: app/events/user_actions/types.py
msgid "Оновлення налаштувань компанії"
msgstr ""

#: api/private/super_admin/utils.py
#: app/jinja_templates/email/your_account_is_blocked.jinja2
msgid "Оновлення пароля для безпеки вашого акаунта у Вчасно"
msgstr ""

#: app/events/user_actions/types.py
msgid "Оновлення сценарію"
msgstr ""

#: app/events/user_actions/types.py
msgid "Оновлення шаблону"
msgstr ""

#: app/events/document_actions/types.py
msgid "Оновлення ярликів"
msgstr ""

#: app/events/user_actions/types.py
msgid "Оновлення імені папки"
msgstr ""

#: api/private/super_admin/views.py
msgid "Оновлюється інформація про тариф і рахунок тарифу в CRMВарто використовувати, якщо сталася помилка/збій і потрібно надіслати у CRM коректні дані про тарифи"
msgstr ""

#: app/billing/constants.py
msgid "Операцію відхилено. Будь ласка, спробуйте сплатити ще раз"
msgstr ""

#: app/billing/constants.py
msgid "Операцію відхилено. Будь-ласка зв'яжіться з службою підтримки"
msgstr ""

#: app/billing/constants.py
msgid "Операцію відхилено. Будь-ласка зверніться до свого банку."
msgstr ""

#: app/billing/constants.py
msgid "Операцію відхилено. Будь-ласка спробуйте оплату іншою карткою."
msgstr ""

#: app/billing/constants.py
msgid "Операція повернення успішна"
msgstr ""

#: app/billing/constants.py
msgid "Оплата була відхилена. Будь ласка, скористайтесь іншою картою"
msgstr ""

#: app/billing/constants.py
msgid "Оплата заборонена. Будь-ласка зверніться до банку."
msgstr ""

#: app/billing/views.py
msgid "Оплата карткою наразі недоступна."
msgstr "Card payment is unavailable now."

#: app/billing/constants.py
msgid "Оплата можлива тільки в гривні. Будь ласка, скористайтеся гривневою карткою"
msgstr ""

#: app/billing/constants.py
msgid "Оплата скасована. Банк не дозволив оплату. Будь-ласка зверніться до банку."
msgstr ""

#: app/billing/constants.py
msgid "Оплата скасована. Будь-ласка перевірте дані карти."
msgstr ""

#: app/jinja_templates/email/bill_reminder_archive.jinja2
#: app/jinja_templates/email/bill_reminder_pro.jinja2
#: app/jinja_templates/email/bill_reminder_ultimate.jinja2
msgid "Оплатіть тариф у «Вчасно.ЕДО»"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Оптимізація витрат"
msgstr "Cost optimization"

#: app/documents/validators.py
msgid "Оскільки контрагент вже підписав документ, то не можливо змінити його на іншого контрагента"
msgstr ""

#: app/documents/utils.py
msgid "Отриманий вами"
msgstr "Received by you"

#: app/billing/views.py
msgid "Отримати HTML рахунку"
msgstr ""

#: app/billing/views.py
msgid "Отримати HTML рахунку по його ідентифікатору"
msgstr ""

#: app/billing/views.py
msgid "Отримати властивості тарифів"
msgstr ""

#: app/billing/views.py
msgid "Отримати дані рахунку по його ідентифікатору"
msgstr ""

#: api/private/super_admin/views.py
msgid "Отримати документ з ElasticSearch для налагодження"
msgstr ""

#: api/private/super_admin/tmp_csp/views.py
msgid "Отримати поточні CSP полісі. Тимчасовий ендпоінт"
msgstr ""

#: app/billing/views.py
msgid "Отримати рахунок"
msgstr ""

#: app/tags/views.py
msgid "Отримати список ролей прив'язаних до тегу"
msgstr ""

#: app/tags/views.py
msgid "Отримати список тегів"
msgstr ""

#: app/billing/sa_views.py
msgid "Отримати цільову аудиторію для тріалів"
msgstr ""

#: app/landing/views.py
msgid "Отримати інформацію про користувача"
msgstr ""

#: api/errors.py
msgid "Отримувач"
msgstr "The recipient"

#: api/downloads/pdf/builders.py
msgid "Отримувач документу"
msgstr "Document recipient"

#: api/errors.py
msgid "Отримувача"
msgstr "Recipient"

#: app/billing/constants.py
msgid "Очікування уточнень"
msgstr ""

#: app/documents/utils.py
msgid "Очікують підпису вашої компанії"
msgstr "Waiting for your company's signature"

#: app/documents/utils.py
msgid "Очікує підпису вашої компанії"
msgstr "Waiting for your company's signature"

#: app/documents/utils.py
msgid "Очікує підпису контрагента"
msgstr "Waiting for counterparty signature"

#: app/diia/validators.py
msgid "Очікується два підписи"
msgstr ""

#: app/billing/constants.py
msgid "Очікується додаткова інформація, спробуйте пізніше"
msgstr ""

#: app/billing/constants.py
msgid "Очікується завершення платежу в Приват24"
msgstr ""

#: app/billing/constants.py
msgid "Очікується завершення платежу в гаманці MasterPass"
msgstr ""

#: app/billing/constants.py
msgid "Очікується оплата"
msgstr ""

#: app/billing/constants.py
msgid "Очікується оплата готівкою. Будь ласка, скористайтеся терміналом самообслуговування"
msgstr ""

#: app/billing/constants.py
msgid "Очікується підтвердження в додатку SENDER"
msgstr ""

#: app/billing/constants.py
msgid "Очікується підтвердження оплати"
msgstr ""

#: app/billing/constants.py
msgid "Очікується підтвердження оплати в додатку Privat24 / SENDER"
msgstr ""

#: app/billing/constants.py
msgid "Очікується підтвердження пароля додатка Приват24"
msgstr ""

#: api/private/super_admin/views.py
msgid "Очікується ціна типу int в копійках. В разі, якщо немає активного Максимального або ціна за користувача на тарифі вже існує - дані не оновлюються"
msgstr ""

#: app/auth/validators.py app/mobile/auth/validators.py
msgid "Очікується інший тип двофакторної аутентифікації"
msgstr "Expected a different type of two-factor authentication"

#: api/errors.py
msgid "Папка"
msgstr ""

#: app/directories/validators.py
msgid "Папка з такою назвою вже існує на цьому рівні вкладеності"
msgstr ""

#: api/errors.py
msgid "Папки"
msgstr ""

#: api/private/integrations/validators.py
msgid "Параметр \"date_from\" має бути менший ніж \"date_to\""
msgstr "The \"date_from\" parameter must be less than \"date_to\""

#: app/billing/validators.py
msgid "Параметр source не підтримується для інтеграційного тарифу"
msgstr ""

#: api/errors.py
msgid "Параметр документа"
msgstr "Document parameter"

#: api/errors.py
msgid "Параметр документу"
msgstr "Document parameter"

#: api/public/validators.py
msgid "Параметри \"user_to_email\" і \"group_to_name\" неможливо використовувати разом"
msgstr ""

#: app/billing/constants.py
msgid "Пароль з смс вказано невірно. Будь ласка, перевірте і спробуйте ще раз"
msgstr ""

#: app/lib/validators.py
msgid "Пароль має містити щонайменше 8 символів, хоча б одну цифру і один символ"
msgstr "Password must contain at least 8 characters, at least one number and one symbol"

#: app/profile/validators.py
msgid "Пароль має містити щонайменше {number} символів, хоча б одну цифру і один символ."
msgstr "Password must contain at least {number} characters, at least one number and one symbol."

#: app/auth/validators.py
msgid "Пароль не встановлено, щоб продовжити дію спочатку встановіть пароль"
msgstr "Password is not set, to continue the action first set the password"

#: app/lib/validators.py
msgid "Пароль не може бути пустим"
msgstr "Password cannot be blank"

#: app/billing/constants.py
msgid "Перевищена кількість невірно веденого ПІН коду."
msgstr ""

#: api/errors.py
msgid "Перевищено кількість завантажених документів, дозволених Вашій компанії"
msgstr "Exceeded the number of uploaded documents allowed by your company"

#: api/errors.py
msgid "Перевищено кількість підписаних та відправлених документів, дозволених Вашим поточним тарифом"
msgstr "Exceeded the number of signed and sent documents allowed by your current pricing"

#: app/auth/validators.py
msgid "Перевищено кількість спроб увійти до сервісу. Спробуйте увійти пізніше."
msgstr ""

#: app/billing/constants.py
msgid "Перевищено кількість спроб. Будь ласка, повторіть пізніше"
msgstr ""

#: api/errors.py
msgid "Перевищено ліміт запитів. Спробуйте трохи згодом."
msgstr "The request limit has been exceeded. Try it later."

#: app/billing/constants.py
msgid "Перевищено ліміт на оплати карткою. Скористайтесь іншою карткою."
msgstr ""

#: api/errors.py
msgid "Перевищено ліміт, зазначений тарифом"
msgstr "The limit specified by the pricing has been exceeded"

#: app/billing/constants.py
msgid "Перевищено число спроб введення PIN - коду. Будь ласка, повторіть пізніше"
msgstr ""

#: api/private/super_admin/views.py
msgid "Перевірити та видалити email з чорного списку AWS SES"
msgstr ""

#: app/trigger_notifications/utils.py
msgid "Перевірте, хто з ваших контрагентів вже зареєстрований у Вчасно."
msgstr "Check which of your counterparties are already registered in Vchasno."

#: app/jinja_templates/email/review_accepted_documents.jinja2
msgid "Переглянути всі погоджені документи"
msgstr "See all approved documents"

#: app/documents/emailing.py
msgid "Переглянути документ"
msgstr ""

#: app/trigger_notifications/utils.py
msgid "Перегляньте документ від контрагента {partner}"
msgstr "Review the document from the counterparty {partner}"

#: app/events/user_actions/types.py
msgid "Перейменування групи"
msgstr ""

#: app/jinja_templates/email/user_email_change_confirm_notice.jinja2
msgid "Перейти в кабінет"
msgstr ""

#: app/jinja_templates/email/blocks/document_macros.jinja2
#: app/jinja_templates/email/delete_request_new.jinja2
#: app/jinja_templates/email/delete_request_reject.jinja2
#: app/jinja_templates/email/notification_comment.jinja2
#: app/jinja_templates/email/notification_document.jinja2
#: app/jinja_templates/email/notification_document_version_add.jinja2
#: app/jinja_templates/email/notification_reject.jinja2
#: app/jinja_templates/email/notification_review_reject.jinja2
#: app/jinja_templates/email/review_request.jinja2
#: app/jinja_templates/email/revoke_completed.jinja2
#: app/jinja_templates/email/revoke_initiated.jinja2
#: app/jinja_templates/email/revoke_rejected.jinja2
msgid "Перейти до документа"
msgstr "Go to the document"

#: app/jinja_templates/email/auto_welcome_letter.jinja2
#: app/jinja_templates/email/blocks/document_macros.jinja2
#: app/jinja_templates/email/daily_notification_about_finished_documents.jinja2
#: app/jinja_templates/email/inbox_documents.jinja2
#: app/jinja_templates/email/unsigned_documents.jinja2
msgid "Перейти до документів"
msgstr "Go to the documents"

#: app/jinja_templates/email/review_request.jinja2
msgid "Перейти на сторінку документа"
msgstr "Go to the document page"

#: app/jinja_templates/email/invite_existing_user.jinja2
msgid "Перейти у Вчасно"
msgstr "Go to Vchasno"

#: app/i18n/tests/test_all.py
msgid "Переклад, який не існує"
msgstr ""

#: app/i18n/tests/test_all.py
msgid "Переклад, який не існує {lang}"
msgstr ""

#: app/jinja_templates/email/review_accepted_documents.jinja2
msgid "Перелік погоджених документів у компанії"
msgstr "List of approved documents in the company"

#: app/events/document_actions/types.py
msgid "Переміщення архівного документа в папку"
msgstr ""

#: app/events/user_actions/types.py
msgid "Переміщення папки"
msgstr ""

#: app/events/document_actions/types.py
msgid "Пересилання сесії підписання"
msgstr ""

#: app/events/validators.py
msgid "Період звіту не може перевищувати 30 днів"
msgstr ""

#: api/downloads/archives.py api/errors.py
msgid "Печатка"
msgstr "The stamp"

#: app/billing/constants.py
msgid "Платіж в обробці"
msgstr ""

#: app/billing/constants.py
msgid "Платіж відхилений. Будь ласка, зв'яжіться зі службою підтримки"
msgstr ""

#: app/billing/constants.py
msgid "Платіж відхилений. Будь ласка, спробуйте пізніше"
msgstr ""

#: app/billing/constants.py
msgid "Платіж не знайдений, спробуйте ще раз"
msgstr ""

#: app/billing/constants.py
msgid "Платіж не може бути проведений в цьому магазині"
msgstr ""

#: app/billing/constants.py
msgid "Платіж не є регулярним"
msgstr ""

#: app/billing/constants.py
msgid "Платіж скасований платником"
msgstr ""

#: app/billing/constants.py
msgid "Платіжна система картки не підтримується. Будь ласка, скористайтеся іншою картою"
msgstr ""

#: api/errors.py
msgid "Платіжний рахунок"
msgstr "Payment account"

#: app/billing/validators.py
msgid "По цьому рахунку вже була проведена успішна оплата"
msgstr "This account has already been successfully paid"

#: app/billing/integrations/evopay.py
msgid "По цьому рахунку вже була проведена успішна оплата."
msgstr "This bill is already payed."

#: api/private/super_admin/views.py
msgid "Повернути стан документа для налагодження"
msgstr ""

#: app/es/handlers.py
msgid "Повернути схему маппінгу Elasticsearch"
msgstr ""

#: app/es/handlers.py
msgid "Повертає схему маппінгу Elasticsearch для основних моделей проєкту"
msgstr ""

#: app/jinja_templates/email/user_email_change_confirm_admin.jinja2
#, python-format
msgid "Повідомляємо, що електронну адресу %(user_full_name)s було змінено з %(old_email)s на %(new_email)s."
msgstr ""

#: app/jinja_templates/email/user_email_change_confirm_admin.jinja2
#, python-format
msgid "Повідомляємо, що електронну адресу %(user_full_name)s було змінено на %(new_email)s."
msgstr ""

#: api/downloads/utils.py
#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Погодження"
msgstr "Review"

#: app/jinja_templates/email/notification_review_reject.jinja2
msgid "Погодження було відхилено"
msgstr "Approval rejected"

#: app/reviews/enums.py
msgid "Погодження видалено"
msgstr "Approval removed"

#: app/reviews/enums.py
msgid "Погодження відмовлено"
msgstr "Approval denied"

#: app/trigger_notifications/utils.py
msgid "Погодження відхилено"
msgstr "Approval rejected"

#: app/reviews/validators.py
msgid "Погодження документів не доступне на поточному тарифі"
msgstr "Approval of documents is not available at the current pricing plan"

#: app/reviews/enums.py
msgid "Погодження підтверджено"
msgstr "Approval confirmed"

#: api/downloads/pdf/builders.py
msgid "Погоджено:"
msgstr "Agreed:"

#: api/downloads/tests/test_downloads_pdf.py app/reviews/utils.py
msgid "Погоджує"
msgstr "Agrees"

#: app/jinja_templates/email/review_reminder.jinja2
msgid "Погодити всі"
msgstr "Approve all"

#: app/trigger_notifications/utils.py
msgid "Погодьте документ від {partner}"
msgstr "Approve the document from {partner}"

#: app/contacts/validators.py app/documents/validators.py
msgid "Поле email не може бути пустим."
msgstr "The email field cannot be blank."

#: app/documents/validators.py
msgid "Поле для вводу не містить жодних символів."
msgstr "There are no characters in the input field."

#: api/errors.py
msgid "Поле обов'язкове"
msgstr "Field required"

#: api/errors.py
msgid "Помилка"
msgstr "Error"

#: api/errors.py
msgid "Помилка в логіні або паролі"
msgstr "Error in your username or password"

#: api/errors.py
msgid "Помилка в паролі"
msgstr "Password error"

#: api/graph/exceptions.py
msgid "Помилка валідації запиту."
msgstr "Request validation error."

#: api/graph/exceptions.py
msgid "Помилка виконання запиту."
msgstr ""

#: app/lib/validators.py
msgid "Помилка з міткою boundary у тілі запиті"
msgstr "Error with boundary label in the request body"

#: api/errors.py
msgid "Помилка завантаження зашифрованої сесії"
msgstr "Error loading an encrypted session"

#: app/auth/providers/google.py
msgid "Помилка завантаження сертифікатів Google"
msgstr "Error loading Google certificates"

#: api/errors.py
msgid "Помилка запиту до YouControl"
msgstr ""

#: api/errors.py
msgid "Помилка запиту до proxy сервісу"
msgstr "Error requesting a proxy service"

#: api/errors.py
msgid "Помилка запиту до сервісу підписання"
msgstr "Error requesting the signing service"

#: api/errors.py
msgid "Помилка запиту до стороннього сервісу"
msgstr "Error requesting a third-party service"

#: app/billing/constants.py
msgid "Помилка оплати. Будь ласка, зверніться в підтримку для усунення проблеми"
msgstr ""

#: api/errors.py
msgid "Помилка отримання інформації по підписувачу"
msgstr "Error receiving information by subscriber"

#: app/billing/constants.py
msgid "Помилка при введенні номера карти. Будь ласка, перевірте номер карти і спробуйте ще раз"
msgstr ""

#: app/billing/constants.py
msgid "Помилка при оплаті. Будь-ласка зверніться до банку."
msgstr ""

#: app/auth/providers/apple/client.py
msgid "Помилка при перевірці токену авторизації Apple"
msgstr ""

#: app/auth/providers/google.py
msgid "Помилка при перевірці токену авторизації Google"
msgstr "Error when checking the Google authorization token"

#: app/auth/providers/microsoft.py
msgid "Помилка при перевірці токену авторизації Microsoft"
msgstr ""

#: app/billing/constants.py
msgid "Помилка підтвердження оплати. Будь ласка, спробуйте сплатити ще раз"
msgstr ""

#: api/errors.py
msgid "Помилка роботи зі стороннім сервісом зберігання файлів"
msgstr "Error working with a third-party file storage service"

#: api/errors.py
msgid "Помилка серверу"
msgstr "Server error"

#: api/errors.py
msgid "Помилка створення зашифрованої сесії"
msgstr "Error creating an encrypted session"

#: api/errors.py
msgid "Помилка, для вирішення повторіть спробу"
msgstr ""

#: app/billing/constants.py
msgid "Помилка. Будь ласка, зверніться в підтримку для усунення проблеми"
msgstr ""

#: app/billing/constants.py
msgid "Помилка. Будь ласка, зверніться в технічну підтримку"
msgstr ""

#: app/billing/constants.py
msgid "Помилка. Будь ласка, перевірте дані карти"
msgstr ""

#: app/billing/constants.py
msgid "Помилка. Вкажіть іншу карту відправника"
msgstr ""

#: app/billing/constants.py
msgid "Помилка. Сума списання не може бути більше суми платежу"
msgstr ""

#: app/flow/validators.py
msgid "Порядок підписання має починатися з нуля"
msgstr "The signing order must start from scratch"

#: api/downloads/pdf/builders.py
msgid "Посада не вказана"
msgstr ""

#: worker/events/jobs.py
msgid "Посилання {idx}"
msgstr "Link {idx}"

#: app/auth/validators.py
msgid "Посилання для підтвердження електронної пошти має містити токен"
msgstr "The email confirmation link must contain a token"

#: api/private/super_admin/views.py
msgid "Поставити в чергу задачу, яка синхронізує транзакції з ПУМБ"
msgstr ""

#: api/private/super_admin/views.py
msgid "Поставити в чергу задачу, яка синхронізує транзакції з Приватбанку"
msgstr ""

#: api/private/super_admin/views.py
msgid "Поставити задачу в чергу"
msgstr ""

#: api/public/validators.py
msgid "Поточна компанія не відправляла документи до компанії з зазначеним ЄДРПОУ"
msgstr "The current company did not send documents to the company with the specified tax ID"

#: app/profile/validators.py
msgid "Поточний акаунт не має прав внесення змін до вказаної компанії"
msgstr "The current account has no rights to make changes to the specified company"

#: app/profile/validators.py
msgid "Поточний акаунт не має прав внесення змін до вказаної ролі"
msgstr "The current account has no rights to make changes to the specified role"

#: api/errors.py
msgid "Поточний документ не може мати PDF файлу для відображення"
msgstr "The current document cannot have a PDF file to display"

#: api/errors.py
msgid "Поточний документ не є XML документом"
msgstr "The current document is not an XML document"

#: app/jinja_templates/email/user_email_change_confirm_admin.jinja2
#, python-format
msgid "Потрібна допомога? Зв'яжіться з нами %(support_email)s."
msgstr "Need help? Contact us %(support_email)s."

#: app/jinja_templates/email/blocks/about_macros.jinja2
msgid "Потрібна допомога? Зв'яжіться з нами!"
msgstr "Need help? Contact us!"

#: api/downloads/validators.py
msgid "Потрібно вказати хоча б один документ або не порожню папку"
msgstr ""

#: app/banner/validators.py
msgid "Початкова і кінцева дата мають бути або заповнені, або пусті"
msgstr "Start date and end date must be either filled or empty"

#: app/documents/emailing.py
msgid "Почніть обмінюватися документами онлайн - легко і просто"
msgstr "Start exchanging documents online – easy and simple."

#: app/events/user_actions/utils.py
msgid "Пошта співробітника"
msgstr ""

#: app/cloud_signer/validators.py
msgid "При підписанні документа потрібно вказати auth_session_token або access_token"
msgstr ""

#: app/billing/schemas.py
msgid "При створенні Максимального рахунку потрібно вказати ліміт користувачів"
msgstr ""

#: app/billing/schemas.py
msgid "При створенні Максимального рахунку потрібно вказати ціну"
msgstr ""

#: app/billing/schemas.py
msgid "При створенні рахунку для поповнення користувачів потрібно вказати ціну за користувача і ліміт користувачів"
msgstr ""

#: app/billing/schemas.py
msgid "При створенні рахунку з додатковим тарифом потрібно вказати основний тариф"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Прискорення виплат від клієнтів"
msgstr "Accelerating payments from customers"

#: api/downloads/pdf/builders.py
msgid "Причина: {reason}"
msgstr "Reason: {reason}"

#: app/lib/validators.py
msgid "Проблема з заголовками multipart-form/data"
msgstr "Problem with multipart-form/data headers"

#: app/lib/url2pdf.py
msgid "Проблема зі стороннім сервісом для генерації PDF"
msgstr ""

#: app/lib/validators.py
msgid "Проблема із кодуванням multipart-form/data"
msgstr ""

#: app/billing/constants.py
msgid "Проблеми з карткою. Перевірте стан картки у банку."
msgstr ""

#: app/jinja_templates/email/documents_for_signer.jinja2
msgid "Просить вас"
msgstr "Asks you"

#: app/billing/constants.py
msgid "Проскануйте QR-коду для завершення транзакції"
msgstr ""

#: app/jinja_templates/email/invite_document.jinja2
msgid "Простий, зрозумілий інтерфейс дозволяє швидко знаходити, підписувати та надсилати документи без зайвого клопоту."
msgstr "The simple, user-friendly interface allows you to quickly find, sign, and send documents without any hassle."

#: app/auth/validators.py app/mobile/auth/validators.py
msgid "Процес двофакторної аутентифікації не розпочато"
msgstr "Two-factor authentication process has not been started"

#: api/errors.py
msgid "Процес підписання"
msgstr "Signing process"

#: api/errors.py
msgid "Пуш повідомлення"
msgstr ""

#: app/jinja_templates/email/daily_notification_about_finished_documents.jinja2
msgid "Підготували для вас звіт по завершеним документам компанії"
msgstr ""

#: api/downloads/archives.py api/errors.py
msgid "Підпис"
msgstr "The signature"

#: app/documents/utils.py
msgid "Підписаний всіма"
msgstr "Signed by all"

#: app/registration/validators.py
msgid "Підписаний файл містить неправильні дані"
msgstr "Signed file contains incorrect data"

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Підписання"
msgstr "Signing"

#: app/events/document_actions/types.py
msgid "Підписання акту анулювання"
msgstr "Signing of the revocation"

#: app/jinja_templates/email/notification_reject.jinja2
#: app/mobile/notifications/notifications.py
msgid "Підписання було відхилено"
msgstr "Signing was declined"

#: app/trigger_notifications/utils.py
msgid "Підписання відхилено"
msgstr "Signing has been declined"

#: app/events/document_actions/types.py
msgid "Підписання документу"
msgstr ""

#: app/registration/validators.py
msgid "Підписано невалідний токен реєстрації"
msgstr "Signed invalid registration token"

#: app/documents/emailing.py
msgid "Підписати документ"
msgstr "Sign document"

#: app/jinja_templates/email/zakupki_notification_document.jinja2
msgid "Підписати у Zakupki.Prom "
msgstr ""

#: app/jinja_templates/email/zakupki_notification_document.jinja2
msgid "Підписати у Вчасно"
msgstr ""

#: app/billing/constants.py
msgid "Підписку успішно деактивовано"
msgstr ""

#: app/billing/constants.py
msgid "Підписку успішно оформлено"
msgstr ""

#: app/jinja_templates/email/bill_reminder_start.jinja2
msgid "Підписувати та надсилати 250 вихідних документів на рік"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Підписуйте договори, акти, видаткові накладні миттєво. Прискорюйте закриття угод та отримуйте оплати швидше."
msgstr "Sign contracts, acts, invoices instantly. Speed up the closing of deals and get paid faster."

#: app/jinja_templates/email/bill_reminder_start.jinja2
msgid "Підписуйте у 10 разів більше документів із тарифом «Старт»"
msgstr ""

#: app/documents/utils.py
msgid "Підписується контрагентом"
msgstr "Signing by the counterparty"

#: app/trigger_notifications/utils.py
msgid "Підпишіть документ від {partner}"
msgstr "Sign a document from {partner}"

#: app/jinja_templates/email/invite_document.jinja2
msgid "Підпишіть його всього за 2 хвилини – це легко та просто:"
msgstr "Sign it in just 2 minutes – it's easy and simple:"

#: app/jinja_templates/email/registration_confirmation.jinja2
#: app/jinja_templates/email/user_email_change_start.jinja2
msgid "Підтвердження email"
msgstr "Email confirmation"

#: app/events/document_actions/types.py
msgid "Підтвердження видалення"
msgstr ""

#: app/auth/two_factor.py app/jinja_templates/email/email_2fa_token.jinja2
msgid "Підтвердження двофакторної аутентифікації"
msgstr "Two-factor authentication confirmation"

#: app/jinja_templates/email/user_email_change_verify_email.jinja2
#: app/profile/emailing.py
msgid "Підтвердження доступу до облікового запису"
msgstr ""

#: app/profile/emailing.py
msgid "Підтвердження зміни email"
msgstr ""

#: app/registration/emailing.py
msgid "Підтвердження реєстрації"
msgstr "Confirmation of registration"

#: app/jinja_templates/email/email_2fa_token.jinja2
msgid "Підтвердити"
msgstr "Confirm"

#: app/jinja_templates/email/user_email_change_start.jinja2
msgid "Підтвердити Email"
msgstr ""

#: app/jinja_templates/email/registration_confirmation.jinja2
msgid "Підтвердити email"
msgstr "Confirm email"

#: app/jinja_templates/email/user_email_change_verify_email.jinja2
msgid "Підтвердити доступ"
msgstr ""

#: app/drafts/schemas.py app/templates/schemas.py
msgid "Підтримуються тільки формати {allowed_extensions}"
msgstr ""

#: app/jinja_templates/email/bill_reminder_pro.jinja2
msgid "Після оплати рахунку вам будуть доступні:"
msgstr ""

#: app/jinja_templates/email/bill_reminder_start.jinja2
msgid "Після оплати рахунку ви зможете:"
msgstr ""

#: app/jinja_templates/email/bill_reminder_ultimate.jinja2
msgid "Після оплати рахунку ви отримаєте доступ до:"
msgstr ""

#: app/jinja_templates/email/bill_reminder_archive.jinja2
msgid "Після оплати рахунку ви:"
msgstr ""

#: app/jinja_templates/email/invite_document.jinja2
msgid "Після підписання документу він зберігся у кабінеті. Також ви автоматично пройшли реєстрацію «Вчасно.ЕДО»!"
msgstr "After signing the document, it is saved in your account. You have also automatically registered in «Vchasno.EDO»!"

#: api/errors.py
msgid "Рахунок"
msgstr "Invoice"

#: app/billing/emailing.py
msgid "Рахунок на оплату послуг Вчасно"
msgstr "Invoice for Vchasno services"

#: app/billing/validators.py
msgid "Рахунок не знайдено у БД"
msgstr "Bill is not found in DB"

#: app/events/user_actions/types.py
msgid "Редагування додаткового параметру"
msgstr ""

#: app/events/user_actions/types.py
msgid "Редагування обовязкового додаткового параметру"
msgstr ""

#: api/errors.py
msgid "Результат внутрішнього погодження"
msgstr "Result of internal approval"

#: app/profile/validators.py
msgid "Реєстрація користувачів з домену .ru заборонена"
msgstr "Registration of users from the .ru domain is prohibited"

#: app/jinja_templates/email/invite_document.jinja2
msgid "Робимо складне простим"
msgstr "Making the complex simple."

#: app/events/document_actions/types.py
msgid "Розархівація документа"
msgstr "Unarchiving a document"

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Розвивайте свій бізнес, замість того, щоб витрачати час персоналу на друк, сканування, очікування доставки документів."
msgstr "Develop your business instead of spending staff's time on printing, scanning, waiting for document delivery."

#: api/errors.py
msgid "Розмір файла занадто великий, оберіть файл з розміром менше ніж {max_file_size}МБ"
msgstr "The file size is too large, select a file with a size less than {max_file_size}MB"

#: api/errors.py
msgid "Розмір файлів занадто великий, оберіть файли з розміром менше ніж {max_total_size}МБ"
msgstr "The file size is too large, select files with a size less than {max_total_size}MB"

#: app/billing/validators.py
msgid "Розширення вже активовано"
msgstr ""

#: app/mobile/documents/utils.py
msgid "Розширення не підтримується."
msgstr ""

#: api/errors.py
msgid "Роль"
msgstr "The role"

#: app/diia/validators.py
msgid "Роль користувача повинна бути вказана для підпису"
msgstr ""

#: app/auth/validators.py
msgid "Роль не знайдено у БД"
msgstr "Role has not been found in DB"

#: api/public/utils.py
msgid "Роль не знайдено."
msgstr ""

#: api/errors.py
msgid "Ролі"
msgstr "Roles"

#: app/jinja_templates/email/invite_document.jinja2
msgid "Сервіс електронного документообігу №1 в Україні"
msgstr "№1 electronic document management service in Ukraine."

#: api/downloads/pdf/builders.py
msgid "Серійний номер: {0}"
msgstr "Serial number: {0}"

#: api/errors.py
msgid "Сесію підписання документу"
msgstr "Document signing session"

#: app/diia/validators.py
msgid "Сесію підписання не знайдено"
msgstr ""

#: api/errors.py
msgid "Сесія закінчилася, будь ласка, авторизуйтесь знову"
msgstr "The session has expired, please sign in again"

#: api/errors.py
msgid "Сесія підписання документу"
msgstr "Document signing session"

#: api/errors.py
msgid "Сесія підписання має невірний статус. Будь ласка, переконайтеся, що ви робите вірний запит на оновлення сесії підписання"
msgstr "The signing session has an incorrect status. Please make sure that you are making the correct request to update the signing session"

#: app/profile/validators.py
msgid "Синхронізація контактів вже була розпочата"
msgstr ""

#: app/es/handlers.py
msgid "Синхронізувати маппінг Elasticsearch з моделями в коді"
msgstr ""

#: api/private/super_admin/views.py
msgid "Синхронізувати інформацію про компанію з YouControl"
msgstr ""

#: app/billing/views.py
msgid "Скасувати рахунок"
msgstr ""

#: api/private/super_admin/views.py
msgid "Скинути 2FA для користувача"
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Скорочення часу на роботу з документами"
msgstr "Reducing the time spent on document work"

#: app/jinja_templates/email/bill_reminder_archive.jinja2
#: app/jinja_templates/email/bill_reminder_pro.jinja2
#: app/jinja_templates/email/bill_reminder_start.jinja2
#: app/jinja_templates/email/bill_reminder_ultimate.jinja2
msgid "Сплатіть рахунок просто зараз"
msgstr ""

#: worker/emailing/reminders_for_unpaid_bills/emailing.py
msgid "Сплатіть рахунок у кабінеті «Вчасно.ЕДО»"
msgstr ""

#: app/events/user_actions/types.py
msgid "Спроба входу у сервіс"
msgstr ""

#: api/errors.py
msgid "Спроба додати печатку на місце підпису, будь-ласка завантажуйте печатку у полі \"stamp\""
msgstr "If you are trying to add a seal to the signature location, please load the seal in the \"stamp\" field"

#: api/errors.py
msgid "Спроба додати підпис на місце печатки, будь-ласка завантажуйте підпис у полі \"key\""
msgstr "Trying to add a signature instead of a seal, please upload the signature in the \"key\" field"

#: app/events/document_actions/types.py
msgid "Спроба створити існуючу сесію підписання"
msgstr ""

#: app/trigger_notifications/utils.py
msgid "Спробуйте прямо зараз!"
msgstr "Try it now!"

#: api/downloads/utils.py
msgid "Співробітник"
msgstr "Employee"

#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
#: worker/emailing/reminders_for_new_employee/jobs.py
msgid "Співробітник вашої компанії зареєструвався у «Вчасно»!"
msgstr ""

#: app/jinja_templates/email/new_employee_failed_registration.jinja2
#: worker/emailing/reminders_for_new_employee/jobs.py
msgid "Співробітник вашої компанії не зміг зареєструватися у «Вчасно»"
msgstr ""

#: app/comments/emailing.py
#: app/jinja_templates/email/notification_document_version_add.jinja2
msgid "Співробітник компанії"
msgstr "Company employee"

#: api/private/super_admin/validators.py app/profile/validators.py
msgid "Співробітник має кадрову роль. Видаліть роль із сервісу Вчасно.Кадри"
msgstr ""

#: app/documents/emailing.py
msgid "Співробітники компанії {company_title} підписали документи"
msgstr "Сompany employees {company_title} signed documents"

#: app/documents/emailing.py
msgid "Співробітники компанії {company_title} підписали документи, які ви призначили або завантажили"
msgstr "Сompany employees {company_title} signed documents that you assigned or uploaded"

#: app/documents/emailing.py
msgid "Співробітники компанії {company_title} підписали призначені вами документи"
msgstr "Сompany employees {company_title} signed documents assigned by you"

#: app/billing/integrations/pumb.py
msgid "Сталася помилка під час виконання запиту до PUMB"
msgstr ""

#: app/billing/integrations/evopay.py
msgid "Сталася помилка під час відправлення запиту в EvoPay"
msgstr "Unknown error during request execution to EvoPay"

#: app/billing/integrations/privat.py
msgid "Сталася помилка під час відправлення запиту в Privatbank"
msgstr "An error occurred while sending a request to PrivatBank"

#: app/diia/utils.py
msgid "Сталася помилка під час запиту до сервісу Дія"
msgstr "An error occurred when requesting the Diia Service"

#: app/profile/validators.py
msgid "Статус може бути змінений тільки з видаленого на активний"
msgstr "Status can be changed only from deleted to active"

#: api/downloads/pdf/builders.py
msgid "Статус перевірки сертифікату: Сертифікат діє"
msgstr "Certificate verification status: The certificate is valid"

#: app/mobile/notifications/views.py
msgid "Статус сповіщення повинен бути переданий"
msgstr ""

#: app/events/document_actions/types.py
msgid "Створення версії з чернетки"
msgstr "Version creation from draft"

#: app/events/user_actions/types.py
msgid "Створення групи"
msgstr ""

#: app/events/user_actions/types.py
msgid "Створення додаткового параметру"
msgstr ""

#: app/events/user_actions/types.py
msgid "Створення папки"
msgstr ""

#: app/events/user_actions/types.py
msgid "Створення ролі в ЕДІ"
msgstr ""

#: app/events/document_actions/types.py
msgid "Створення сесії підписання"
msgstr ""

#: app/events/user_actions/types.py
msgid "Створення сценарію"
msgstr ""

#: app/events/document_actions/types.py
msgid "Створення чернетки з версії"
msgstr "Draft creation from version"

#: app/events/user_actions/types.py
msgid "Створення шаблону"
msgstr ""

#: api/private/integrations/handlers.py
msgid "Створити користувача"
msgstr ""

#: api/private/super_admin/views.py
msgid "Створити користувача з автогенерованим паролем"
msgstr ""

#: api/private/super_admin/views.py
msgid "Створити новий публічний шаблон"
msgstr ""

#: app/billing/views.py
msgid "Створити новий рахунок"
msgstr ""

#: app/document_categories/views.py
msgid "Створити публічний тип документу"
msgstr ""

#: app/tags/validators.py
msgid "Створювати, видаляти чи призначати ярлики для компаній можуть лише адміністратори компанії."
msgstr ""

#: app/tags/validators.py
msgid "Створювати, видаляти чи призначати ярлики для ролей можуть лише адміністратори компанії або користувачі з правами переглядати документи в компанії."
msgstr "Only company administrators or users with rights to view documents within the company can create, delete, or assign shortcuts to roles."

#: api/errors.py
msgid "Сторінку не знайдено"
msgstr "Page not found"

#: app/documents_ai/views.py
msgid "Структуровані дані для документа не знайдено"
msgstr ""

#: app/billing/constants.py
msgid "Сума не відповідає доступному ліміту"
msgstr ""

#: app/billing/validators.py
msgid "Сума рахунку не збігається із тарифним множником"
msgstr "Bill price is not the same with rate multiplier"

#: app/billing/constants.py
msgid "Сума успішно заморожена"
msgstr ""

#: app/billing/constants.py
msgid "Сума успішно заморожена на рахунку відправника"
msgstr ""

#: app/billing/constants.py
msgid "Суми розділення платежу не збігаються із загальною сумою. Будь ласка, перевірте значення"
msgstr ""

#: api/errors.py
msgid "Сценарій"
msgstr "Flow"

#: app/document_automation/validators.py
msgid "Сценарій видалено"
msgstr "Script removed"

#: api/errors.py
msgid "Сценарію"
msgstr "Flow"

#: app/jinja_templates/email/layout.jinja2
#: app/jinja_templates/email/layout_v1.1.jinja2
#: app/jinja_templates/email/layout_v2.jinja2
msgid "ТЕСТОВІ ЛИСТИ"
msgstr ""

#: api/errors.py
msgid "ТТН"
msgstr "TTN"

#: app/jinja_templates/email/inbox_documents.jinja2
#: app/jinja_templates/email/review_reminder.jinja2
msgid "Та інші..."
msgstr "And others..."

#: app/jinja_templates/email/invite_document.jinja2
msgid "Також підготували <a href=\"https://www.youtube.com/watch?v=E7jcwdI4cEA\">коротку відеоінструкцію</a>, що допоможе підписати ваш перший документ!"
msgstr "We've also prepared a <a href=\"https://www.youtube.com/watch?v=E7jcwdI4cEA\">short video tutorial</a> to help you sign your first document!"

#: app/billing/validators.py
msgid "Тариф \"Максимальний\" для якого сформовано рахунок на поповнення користувачів не активний"
msgstr ""

#: api/errors.py
msgid "Тариф компанії"
msgstr "Company pricing"

#: app/billing/validators.py
msgid "Тариф не містить ціни за користувача. Зверніться до менеджера"
msgstr ""

#: app/archive/validators.py
msgid "Тариф не передбачає можливість архівувати документи"
msgstr "The rate does not provide the ability to archive documents"

#: app/auth/validators.py
msgid "Тариф не передбачає можливість вимагати 2FA для співробітників"
msgstr "The rate does not provide the ability to require 2FA for employees"

#: app/billing/validators.py
msgid "Тариф є застарілим."
msgstr "The pricing plan is outdated."

#: api/errors.py
msgid "Тарифу компанії"
msgstr "The company's pricing"

#: api/private/super_admin/validators.py app/profile/validators.py
msgid "Телефон вже використовується іншим користувачем для входу в систему, тому ми не можемо його використовувати для входу у ваш профіль"
msgstr "The phone number is already used by another user to log in, so we cannot use it to log into your profile"

#: app/events/user_actions/utils.py
msgid "Телефон співробітника"
msgstr ""

#: app/billing/validators.py
msgid "Тестовий період для тарифу вже активовано"
msgstr "The trial period for the rate has already been activated"

#: app/lib/url2pdf.py
msgid "Тимчасова проблема зі стороннім сервісом для генерації PDF"
msgstr ""

#: app/documents_ai/validators.py
msgid "Тип документів не підтримується. Доступні типи документів: акти, рахунки, накладні"
msgstr ""

#: app/billing/constants.py
msgid "Тип карти не підтримується. Будь ласка, скористайтеся іншою картою"
msgstr ""

#: api/downloads/pdf/builders.py app/i18n/tests/test_all.py
msgid "Тип підпису: {0}"
msgstr "Signature type: {0}"

#: api/downloads/pdf/builders.py
msgid "Тип сертифікату: {0}"
msgstr ""

#: api/errors.py app/i18n/tests/test_all.py
msgid "Токен"
msgstr "The token"

#: api/errors.py
msgid "Токен вже було використано для реєстрації користувача у сервісі"
msgstr "The token has already been used to register the user in the service"

#: api/errors.py
msgid "Токен недійсний. Будь ласка зателефонуйте до служби підтримки"
msgstr "The token is invalid. Please call the support service"

#: api/shared/views.py app/document_revoke/views.py app/signatures/views.py
msgid "Токен реєстрації"
msgstr ""

#: api/errors.py
msgid "Транзакція"
msgstr ""

#: app/billing/constants.py
msgid "Транзакція перебуває на перевірці на протидії шахрайству"
msgstr ""

#: app/billing/constants.py
msgid "Транзакція успішна"
msgstr ""

#: app/profile/validators.py
msgid "Тільки адміністратор може виконати цю дію"
msgstr "Only an administrator can perform this action"

#: app/documents/validators.py
msgid "Тільки адміністратор чи автор процесу підписання може редагувати підписантів документа"
msgstr "Only the administrator or the author of the signing process can edit the signatories of the document"

#: api/public/validators.py
msgid "Тільки адміністратори компанії можуть створювати співробітників."
msgstr "Only company administrators can create employees."

#: app/document_revoke/validators.py
msgid "Тільки документи з EDI можуть бути відкликані"
msgstr ""

#: app/documents/validators.py
msgid "Тільки компанія власник документу може змінювати загальні реквізити"
msgstr "Only the company that owns the document can change the general details"

#: app/documents/validators.py
msgid "Тільки компанія відправник може бути одержувачем внутрішнього документа"
msgstr ""

#: api/public/validators.py
msgid "Тільки компанії з корпоративним доменом електронної пошти можуть автоматично створювати користувачів."
msgstr "Only companies with a corporate email domain can automatically create users."

#: app/jinja_templates/email/inbox_documents.jinja2
#, python-format
msgid "У вас %(inbox_documents_count)s нових документів в компанії"
msgstr "You have %(inbox_documents_count)s new documents in company"

#: worker/emailing/reminders_for_abandoned_registration/jobs.py
msgid "У вас виникли труднощі з перевіркою по КЕП/ЕЦП?"
msgstr ""

#: app/auth/validators.py
msgid "У вас недостатньо прав, щоб змінювати налаштування ролі"
msgstr "You do not have enough rights to change the role settings"

#: app/registration/validators.py
msgid "У вас недостатньо прав, щоб надати ці права для запрошеного співробітника"
msgstr "You do not have enough rights to grant these rights to the invited employee"

#: app/auth/validators.py
msgid "У вас недостатньо прав, щоб увімкнути це налаштування"
msgstr "You do not have enough rights to enable this setting"

#: app/registration/validators.py
msgid "У вас нема прав наділяти правами адміністратора співробітників при запрошенні"
msgstr "You do not have the right to grant administrator rights to employees when inviting them"

#: app/documents/validators.py
msgid "У вас немає доступу до всіх або деяких документів"
msgstr "You do not have access to all or some documents"

#: app/document_automation/validators.py
msgid "У вас немає доступу до сценарія"
msgstr "You do not have access to the script"

#: app/document_automation/validators.py
msgid "У вас немає доступу до шаблону"
msgstr "You do not have access to the template"

#: app/jinja_templates/email/bill_reminder_archive.jinja2
#: app/jinja_templates/email/bill_reminder_pro.jinja2
#: app/jinja_templates/email/bill_reminder_start.jinja2
#: app/jinja_templates/email/bill_reminder_ultimate.jinja2
msgid "У вашому кабінеті «Вчасно.ЕДО» є виставлений рахунок на оплату тарифу."
msgstr ""

#: app/profile/validators.py
msgid "У вашій компанії створена максимальна кількість {entity}, що доступна на поточному тарифі"
msgstr "Your company has created the maximum number of {entities} available at the current rate"

#: app/billing/views.py
msgid "У вебхуку від EvoPay відсутні необхідні поля"
msgstr "The EvoPay webhook is missing required fields"

#: app/billing/integrations/evopay.py
msgid "У відповіді від сервісу EvoPay відсутній url"
msgstr "There is no url in the response from the EvoPay service"

#: app/diia/utils.py
msgid "У відповіді від сервісу Дія відсутній deeplink"
msgstr "There is no deeplink in the response from the Diia service"

#: app/jinja_templates/email/invite_document.jinja2
msgid "У вікні, що відкрилося, погодьтеся із офертою та натисніть кнопку «Підписати та надіслати»"
msgstr "In the window that opens, agree to the offer and click the «Sign and Send» button."

#: app/billing/constants.py
msgid "У картки є обмеження. Скористайтесь іншою карткою."
msgstr ""

#: app/jinja_templates/email/review_reminder.jinja2
msgid "У компанії"
msgstr "In the company"

#: app/billing/validators.py
msgid "У компанії немає активного Максимального тарифу"
msgstr ""

#: app/billing/constants.py
msgid "У одержувача не встановлена карта для прийому платежів"
msgstr ""

#: app/jinja_templates/email/your_account_is_blocked.jinja2
msgid "У рамках заходів із підвищення безпеки ми оновили налаштування доступу до вашого акаунта — поточний пароль було скинуто."
msgstr ""

#: app/billing/sa_utils.py
msgid "У файлі знайдено невалідний ЄДРПОУ"
msgstr ""

#: app/billing/sa_utils.py
msgid "У файлі знайдено невалідний емейл"
msgstr ""

#: api/private/super_admin/views.py
msgid "УВАГА: Перш ніж робити цю дію потрібно отримати підтвердження від користувача підписане ключем компанії"
msgstr ""

#: app/jinja_templates/email/auto_welcome_letter.jinja2
msgid "Увійти в кабінет"
msgstr "Log in to the account"

#: app/jinja_templates/email/blocks/about_macros.jinja2
msgid "Усе, що вам потрібно для ваших документів – підписуйте, затверджуйте, зберігайте та робіть багато іншого!"
msgstr "All you need for your documents - sign, approve, store and much more!"

#: worker/emailing/jobs.py
msgid "Успішна оплата рахунку Вчасно"
msgstr ""

#: app/billing/constants.py
msgid "Успішне підтвердження операції"
msgstr ""

#: app/directories/validators.py
msgid "Усі переміщувані папки мають знаходитись в спільній папці"
msgstr ""

#: api/errors.py
msgid "Учасник групи"
msgstr ""

#: app/jinja_templates/email/notification_review_reject.jinja2
msgid "Учасник погодження відхилив документ"
msgstr "Reviewer rejected the document"

#: app/jinja_templates/email/notification_reject.jinja2
msgid "Учасник підписання"
msgstr "Signer"

#: api/errors.py
msgid "Учасника групи"
msgstr ""

#: api/errors.py
msgid "Учасники групи"
msgstr ""

#: api/errors.py
msgid "Учасників групи"
msgstr ""

#: app/registration/utils.py
msgid "Файл підпису містить неправильний тип"
msgstr "The signature file contains the wrong type"

#: app/documents_ai/validators.py
msgid "Формат документів не підтримується. Доступні формати документів: {extensions}"
msgstr ""

#: app/registration/validators.py
msgid "Формат підпису не підтримується для реєстрації"
msgstr ""

#: api/errors.py
msgid "Формат файлу не підтримується, поки що ми приймаємо тільки {allowed_extensions}"
msgstr "The file format is not supported, so far we only accept {allowed_extensions}"

#: app/events/document_actions/types.py
msgid "Формування архіву з документами на вивантаження"
msgstr ""

#: app/documents_ai/views.py
msgid "Функціонал не доступний"
msgstr ""

#: app/profile/validators.py
msgid "Функціонал не доступний для компанії"
msgstr "The functionality is not available for the company"

#: app/crm/utils.py
msgid "Функціонал недоступний."
msgstr ""

#: api/private/super_admin/views.py
msgid "Функціонал перемикачів не доступний, поки хтось не перевірить його статус хоча б один раз. Цей API якраз це й робить — перевіряє статус перемикача функціоналу"
msgstr ""

#: api/errors.py
msgid "Функціонал тимчасово недоступний"
msgstr "The functionality is temporarily unavailable"

#: app/lib/locks.py
msgid "Функціональність тимчасово заблоковано, повторіть спробу"
msgstr "Functionality is temporarily blocked. Please try again"

#: app/billing/views.py
msgid "Це дані для сторінки де ми показуємо список тарифів для купівлі чи продовження"
msgstr ""

#: app/profile/validators.py
msgid "Цей запит на зміну електронної пошти застарів, будь ласка, сформуйте новий запит"
msgstr ""

#: app/reviews/validators.py
msgid "Цей користувач/група вже є у списку погоджувачів"
msgstr ""

#: app/jinja_templates/email/your_account_is_blocked.jinja2
msgid "Цей крок є частиною наших стандартних заходів із захисту даних користувачів."
msgstr ""

#: app/documents/validators.py
msgid "Цей тип документа можна використовувати лише для внутрішніх документів"
msgstr ""

#: api/private/super_admin/views.py
msgid "Ця дія може бути корисною в тих випадках, коли документ не був відправлений автоматично після підписання через якісь технічні причини"
msgstr ""

#: app/sign_sessions/validators.py
msgid "Ця сесія підписання не може бути використана для цього запиту"
msgstr "This signing session cannot be used for this request"

#: api/downloads/pdf/builders.py
msgid "Час перевірки КЕП/ЕЦП: {0}"
msgstr "QES/EDS check time: {0}"

#: app/billing/constants.py
msgid "Чекайте підтвердження через телефонний дзвінок"
msgstr ""

#: app/jinja_templates/email/auto_welcome_letter.jinja2
msgid "Чому мене було зареєстровано?"
msgstr "Why was I registered?"

#: app/jinja_templates/email/invite_document.jinja2
msgid "Чому саме «Вчасно.ЕДО»:"
msgstr "Why «Vchasno.EDO»:"

#: api/errors.py
msgid "Чорновик"
msgstr "Draft"

#: api/errors.py
msgid "Шаблон"
msgstr ""

#: app/document_automation/validators.py
msgid "Шаблон видалено"
msgstr "Template removed"

#: api/errors.py
msgid "Шаблону"
msgstr ""

#: app/jinja_templates/email/review_request.jinja2
msgid "Що таке внутрішнє погодження?"
msgstr "What is internal approval?"

#: app/jinja_templates/email/auto_welcome_letter.jinja2
#, python-format
msgid "Щоб вони не загубилися, ми створили для вас кабінет, де знаходяться всі документи, які були надіслані вам через “%(brand)s”."
msgstr "To prevent them from getting lost, we have created an account for you, where all the documents sent to you through “%(brand)s” are stored."

#: app/jinja_templates/email/daily_notification_about_finished_documents.jinja2
msgid "Щоб ознайомитись із переліком документів перейдіть у кабінет вашої компанії на Вчасно.ЕДО."
msgstr ""

#: app/jinja_templates/email/your_account_is_blocked.jinja2
msgid "Щоб продовжити користування сервісом, будь ласка, встановіть новий пароль:"
msgstr ""

#: app/jinja_templates/email/new_employee_failed_registration.jinja2
msgid "Щоб цей співробітник міг додатись до компанії, вам потрібно перейти на тариф із більшою кількістю співробітників або деактивувати один із поточних акаунтів в компанії."
msgstr ""

#: api/downloads/pdf/builders.py
msgid "Юр. назва: "
msgstr "Legal name:"

#: app/jinja_templates/email/review_request.jinja2
msgid "Як погодити?"
msgstr "How to approve?"

#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
msgid "Якщо ви бажаєте змінити новому співробітнику налаштування доступів до документів і функцій у «Вчасно», то перейдіть до налаштувань компанії."
msgstr ""

#: app/jinja_templates/email/delete_request_new.jinja2
#: app/jinja_templates/email/revoke_initiated.jinja2
msgid "Якщо потрібно, виправте документ, завантажте та повторно відправте партнеру."
msgstr "If necessary, correct the document, upload and resend it to the partner."

#: app/jinja_templates/email/your_account_is_blocked.jinja2
msgid "Якщо у вас виникнуть питання — звертайтесь до нашої служби підтримки."
msgstr ""

#: app/jinja_templates/email/blocks/about_v2_macros.jinja2
msgid "Які наші переваги?"
msgstr "What are our advantages?"

#: api/errors.py
msgid "Ярлик"
msgstr "The tag"

#: api/errors.py
msgid "Ярлики"
msgstr "Tags"

#: app/jinja_templates/email/delete_request_reject.jinja2
#: app/jinja_templates/email/documents_for_signer.jinja2
#: app/jinja_templates/email/revoke_rejected.jinja2
msgid "в компанії"
msgstr "in the company"

#: app/jinja_templates/email/notification_reject.jinja2
#: app/jinja_templates/email/notification_review_reject.jinja2
msgid "від компанії"
msgstr "from the company"

#: app/jinja_templates/email/revoke_rejected.jinja2
msgid "відхилив анулювання документу"
msgstr "rejected the document revoke request"

#: app/jinja_templates/email/revoke_rejected.jinja2
msgid "відхилив анулювання документів"
msgstr "rejected the documents revoke request"

#: app/jinja_templates/email/delete_request_reject.jinja2
msgid "відхилив запит на видалення документу"
msgstr "rejected the document deletion request"

#: app/jinja_templates/email/delete_request_reject.jinja2
msgid "відхилив запит на видалення документів"
msgstr "rejected the documents deletion request"

#: app/jinja_templates/email/notification_reject.jinja2
msgid "відхилив підписання документа"
msgstr "declined signing of the document"

#: app/jinja_templates/email/delete_request_new.jinja2
#: app/jinja_templates/email/revoke_initiated.jinja2
msgid "до компанії"
msgstr "to the company"

#: app/jinja_templates/email/new_employee_successfull_registration.jinja2
msgid "довідку з відеоінструкцією."
msgstr ""

#: app/jinja_templates/email/notification_document.jinja2
#: app/jinja_templates/email/zakupki_notification_document.jinja2
msgid "документ"
msgstr "a document"

#: app/jinja_templates/email/notification_document_version_add.jinja2
msgid "документ до компанії"
msgstr "document to the company"

#: app/jinja_templates/email/review_reminder.jinja2
msgid "документи очікують на ваше погодження."
msgstr "documents are waiting for your approval."

#: app/signatures/validators.py
msgid "електронної печатки"
msgstr "electronic seal"

#: app/jinja_templates/email/blocks/table_blocks.jinja2
msgid "з"
msgstr "from"

#: app/lib/emailing.py
msgid "з ЄДРПОУ/ІПН {edrpou}"
msgstr "with Tax ID {edrpou}"

#: app/jinja_templates/email/notification_reject.jinja2
msgid "з компанії"
msgstr "from the company"

#: api/downloads/pdf/constants.py
msgid "кваліфікований"
msgstr "qualified"

#: app/registration/validators.py
msgid "ключа Mobile ID"
msgstr "Mobile ID key"

#: app/signatures/validators.py
msgid "ключа КЕП/ЕЦП"
msgstr "QES/EDS key"

#: app/jinja_templates/email/revoke_initiated.jinja2
msgid "надіслав запит на анулювання документу"
msgstr "sent a document revoke request"

#: app/jinja_templates/email/revoke_initiated.jinja2
msgid "надіслав запит на анулювання документів"
msgstr "sent document revoke requests"

#: app/jinja_templates/email/delete_request_new.jinja2
msgid "надіслав запит на видалення документу"
msgstr "sent a deletion request for the document"

#: app/jinja_templates/email/delete_request_new.jinja2
msgid "надіслав запит на видалення документів"
msgstr "sent a deletion request for the documents"

#: app/jinja_templates/email/review_request.jinja2
msgid "погодити документ у компанії"
msgstr "approve the document in the company"

#: app/jinja_templates/email/revoke_completed.jinja2
msgid "прийняв запит на анулювання документу"
msgstr "accepted the document revoke request"

#: app/jinja_templates/email/revoke_completed.jinja2
msgid "прийняв запит на анулювання документів"
msgstr "accepted the documents revoke request"

#: app/jinja_templates/email/delete_request_accept.jinja2
msgid "прийняв запит на видалення документу"
msgstr "accepted the document deletion request"

#: app/jinja_templates/email/delete_request_accept.jinja2
msgid "прийняв запит на видалення документів"
msgstr "accepted the documents deletion request"

#: app/reviews/tests/test_reviews_validators.py app/signatures/validators.py
msgid "підписати"
msgstr "sign"

#: app/jinja_templates/email/documents_for_signer.jinja2
msgid "підписати документ"
msgstr "sign the document"

#: app/jinja_templates/email/documents_for_signer.jinja2
msgid "підписати документи"
msgstr "sign the documents"

#: app/jinja_templates/email/delete_request_new.jinja2
#: app/jinja_templates/email/delete_request_reject.jinja2
#: app/jinja_templates/email/revoke_initiated.jinja2
#: app/jinja_templates/email/revoke_rejected.jinja2
msgid "та вказав таку причину:"
msgstr "and specified the following reason:"

#: app/jinja_templates/email/bill_reminder_archive.jinja2
msgid "та отримайте доступ до усіх можливостей архіву. В архіві швидко можна знайти та відкрити документ, що дозволить легко пройти перевірки."
msgstr ""

#: app/jinja_templates/email/blocks/document_macros.jinja2
msgid "та ще"
msgstr "and more"

#: api/downloads/pdf/constants.py
msgid "удосконалений"
msgstr "improved"

#: app/jinja_templates/email/bill_reminder_ultimate.jinja2
msgid "щоб отримати доступ до усіх можливостей тарифу «Максимальний»."
msgstr ""

#: app/jinja_templates/email/bill_reminder_pro.jinja2
msgid "щоб отримати доступ до усіх можливостей тарифу «Професійний»."
msgstr ""

#: app/jinja_templates/email/bill_reminder_start.jinja2
msgid "щоб отримати доступ до усіх можливостей тарифу «Старт»."
msgstr ""

#: app/jinja_templates/email/documents_for_signer.jinja2
#: app/jinja_templates/email/review_request.jinja2
msgid "як учасника групи"
msgstr "as a group member of"

#: app/jinja_templates/email/notification_document.jinja2
#: app/jinja_templates/email/notification_document_version_add.jinja2
#: app/jinja_templates/email/zakupki_notification_document.jinja2
msgid "як учаснику групи"
msgstr "as a group member of"

#~ msgid "Підписаний контрагентом"
#~ msgstr "Signed by the counterparty"

#~ msgid "Номер телефону вже було встановлено"
#~ msgstr "The phone number has already been set"

#~ msgid "Target bill already processed and rate activated"
#~ msgstr ""

#~ msgid "Змінює статус рахунку на canceled або refund, а також скасовує усі надалі рахунком ресурси (тариф, додатковий тариф, документи, розширення користувачів)"
#~ msgstr ""

#~ msgid "Новий список IP адрес не містить поточну IP адресу.Переконайтеся що у вас є доступ до системи із новими IP адресами."
#~ msgstr "The new list of IP addresses does not contain the current IP address. Make sure you have access to the system with the new IP addresses."

#~ msgid "Тільки адмністратор може оновлювати права співробітників при реєстрації"
#~ msgstr "Only the administrator can update the rights of employees during registration"

#~ msgid "Unsupported service type"
#~ msgstr ""

#~ msgid "Rate extension is not active"
#~ msgstr ""

#~ msgid "Змінити статус рахунку"
#~ msgstr ""

#~ msgid "Вас було запрошено до нової компанії"
#~ msgstr ""

#~ msgid "Ваш документ відхилено"
#~ msgstr ""

#~ msgid "Документ очікує на ваш підпис"
#~ msgstr ""

#~ msgid "Документи очікують на ваш підпис"
#~ msgstr ""

#~ msgid "ЄДРПОУ для розшифрування не співпадає з ЄДРПОУ компанії, яка завантажила документ"
#~ msgstr ""

#~ msgid "Неможливо видалити контрагента двохстороннього документа, коли вже розпочато підписання. Натомість ви можете змінити чи додати нового контрагента до такого документа"
#~ msgstr ""

#~ msgid "Користувач не має право видаляти співробітників."
#~ msgstr ""

#~ msgid "Користувач не має право запрошувати співробітників."
#~ msgstr ""

#~ msgid "Співробітник має кадрову роль. Видаліть роль із застосунку Вчасно.Кадри"
#~ msgstr ""

#~ msgid "Не вдалося створити HRS роль."
#~ msgstr ""

#~ msgid "File not provided"
#~ msgstr ""

#~ msgid "Invalid file upload"
#~ msgstr ""

#~ msgid "Імпортувати юзерів з Vchasno.Zvit"
#~ msgstr ""

#~ msgid "Період завантаження не повинен перевищувати 365 діб."
#~ msgstr "The download period must not exceed 365 days."

#~ msgid "Відхилення анулювання"
#~ msgstr ""

#~ msgid "Підписання анулювання"
#~ msgstr ""

#~ msgid "Зареєструйтеся, щоб переглянути документ."
#~ msgstr "Sign up to view the document."

#~ msgid "Надіслав вам документ в компанію"
#~ msgstr "Sent you a document to the company"

#~ msgid "Надіслав вам,"
#~ msgstr "Sent you,"

#~ msgid "Перегляньте та підпишіть документ прямо зараз"
#~ msgstr "View and sign the document right now"

#~ msgid "Підпишіть документ"
#~ msgstr "Sign the document"

#~ msgid "Ви маєте вхідний документ"
#~ msgstr "You have an incoming document."

#~ msgid "від"
#~ msgstr "from"

#~ msgid "на"
#~ msgstr "on"

#~ msgid "перегляд"
#~ msgstr "view"

#~ msgid "підпис"
#~ msgstr "sign"

#~ msgid "у сервісі електронного документообігу «Вчасно.ЕДО» 💛"
#~ msgstr "in the electronic document management service «Vchasno.EDO» 💛"

#~ msgid "Зателефонуйте нам"
#~ msgstr "Call us"

#~ msgid "або напишіть за адресою"
#~ msgstr "or write to"

#~ msgid "пн-пт"
#~ msgstr "Mon-Fri"

#~ msgid "Delete duplicated group review requests"
#~ msgstr ""

#~ msgid "Звіт для завантаження не знайдено. Можливо його вже видалено."
#~ msgstr "The report for download was not found. It may have already been deleted."

#~ msgid "Рахунок не знайдено"
#~ msgstr "Invoice is not found"

#~ msgid "Визначити метадані документа за допомогою ШІ"
#~ msgstr "Determine document metadata using AI"

#~ msgid "Очікується, що документ буде текстовим у PDF форматі. Тут звертаємось до зовнішнього сервісу (AWS BedRock), для визначення мета-даних по документу виходячи тільки із контенту документа"
#~ msgstr ""

#~ msgid "Компанія {company_label}"
#~ msgstr ""

#~ msgid "У Вас оновлення у документах"
#~ msgstr ""

#~ msgid "Відкликаний"
#~ msgstr "Revoked"

#~ msgid "Документ не може бути відкликаний"
#~ msgstr ""

#~ msgid "Документ відправлено: {datetime}"
#~ msgstr "Document sent: {datetime}"

#~ msgid "Документ відхилено: {datetime}"
#~ msgstr "Document rejected: {datetime}"

#~ msgid "Документ отримано: {datetime}"
#~ msgstr "Document received: {datetime}"

#~ msgid "Не можливо згенерувати токен для реєстрації компанії"
#~ msgstr "Unable to generate company registration token"

#~ msgid "Учасник підписання відхилив документ"
#~ msgstr "Signer rejected the document"

#~ msgid "reviewers_ids чи reviewer_entities має бути заповнено"
#~ msgstr ""

#~ msgid "signers_ids чи signer_entities має бути заповнено"
#~ msgstr ""

#~ msgid "Не знайдено завантаженої версії документа"
#~ msgstr ""

#~ msgid "Не знайдено доступної версії документа"
#~ msgstr ""

#~ msgid "Некоректний формат запиту"
#~ msgstr ""

#~ msgid "Помилка завантаження структурованих даних"
#~ msgstr ""

#~ msgid "Розширення документів не підтримується. Доступні розширення: {extensions}"
#~ msgstr ""

