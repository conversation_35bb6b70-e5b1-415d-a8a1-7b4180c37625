import asyncio
import json
import logging
import warnings
from collections.abc import <PERSON><PERSON><PERSON><PERSON>ator, Generator
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from contextlib import asynccontextmanager
from email.mime.multipart import MIMEMultipart
from functools import partial
from io import BytesIO
from smtplib import SM<PERSON><PERSON>x<PERSON>
from typing import Any
from unittest import mock
from unittest.mock import AsyncMock, MagicMock, create_autospec, patch
from uuid import uuid4

import aiohttp
import conciergelib
import pytest
import uvloop
from aiohttp import web
from aiopg.sa import SAConnection
from aiopg.utils import _ContextManager
from conciergelib.aiohttp.client import ConciergeBackendClient
from fakeredis.aioredis import FakeRedis
from logevo.formatter import ConsoleJsonFormatter
from reportlab.pdfgen import canvas
from vchasno_crm.clients.aiohttp import AiohttpVchasnoCrmClient
from yarl import URL

from app import signals
from app.analytics.google.client import GoogleAnalyticsClient
from app.billing.integrations.evopay import EvopayClient
from app.billing.types import Transaction
from app.config import init_eusign, read_app_config
from app.document_antivirus import utils
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_revoke.utils import get_document_revoke_s3_key
from app.documents.utils import (
    _get_document_original_s3_key,
    get_external_key_signature_s3_key,
    get_external_stamp_signature_s3_key,
    get_internal_signature_s3_key,
)
from app.drafts.utils import get_draft_s3_key
from app.editor.utils import CollaboraDiscoveryClient
from app.esputnik.utils import Client
from app.events.utils import build_actions_report_file_s3_key
from app.flags import FeatureFlags
from app.flags.managers import TestFlagsManager
from app.lib import (
    edi,
    emailing,
    eusign_utils,
    s3_utils,
)
from app.lib.brokers import KafkaClient
from app.lib.database import DBConnection, create_database_engine
from app.lib.enums import SignatureType
from app.lib.gotenberg import GotenbergClient
from app.lib.helpers import generate_uuid
from app.lib.s3_utils import CopyFile, DownloadFile, UploadFile
from app.lib.sender.client import EvoSender
from app.lib.sender.enums import SenderMessageType, SenderProject
from app.lib.types import DataDict
from app.mobile.fcm import utils as fcm_utils
from app.mobile.fcm.client import FCMClient
from app.models import metadata
from app.services import services
from app.signatures.tests.test_signatures_views import TEST_STAMP_BYTES
from app.telegram.enums import DispatcherStatus
from app.templates.utils import get_templates_s3_key
from app.tests import linters
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    AsyncBytesIO,
    flush_db,
    flush_es,
    flush_redis,
    prepare_billing_schema,
    prepare_signature_info,
)
from app.youcontrol.client import (
    YouControlClient,
    YouControlResource,
)
from worker.jobs import TOPIC_JOBS

logger = logging.getLogger(__name__)


class Mailbox(list[MIMEMultipart]):
    fail = False

    def by_email(self, email: str) -> list[MIMEMultipart]:
        return [msg for msg in self if msg['To'] == email]

    def by_template(self, template: str) -> list[MIMEMultipart]:
        return [msg for msg in self if msg['X-Template'] == template]

    @property
    def emails_set(self) -> set[str]:
        return {msg['To'] for msg in self}


class EmailTemplatesRenderer(list): ...


class FCMMessageHandler(list): ...


class GoogleAnalyticsEventsPublisher(list): ...


class EvoSenderMock:
    message = ''
    phone = ''
    only_sms = False
    message_count = 0

    def reset(self):
        self.message = ''
        self.phone = ''
        self.only_sms = False
        self.message_count = 0

    async def __call__(
        self,
        *,
        phone: str,
        message: str,
        message_type: SenderMessageType,
        only_sms: bool = False,
        billing_project: SenderProject | None = None,
    ) -> None:
        self.phone = phone
        self.message = message
        self.only_sms = only_sms
        self.message_count += 1


class KafkaClientMock:
    def __init__(self, app):
        self.app = app
        self.messages = []
        self.is_configured = False

    async def send_record(self, topic, value=None):
        value = value or {}
        value = KafkaClient.serializer(value).decode('utf-8')
        value = KafkaClient.deserializer(value)
        self.messages.append((topic, value))
        await TOPIC_JOBS[topic](self.app, value, logger)

    async def update_cluster_metadata(self):
        """Used for health check"""

    async def add_task(
        self,
        topic: str,
        conn: DBConnection | None = None,
        delay_min: int | None = None,
        data: DataDict | None = None,
    ) -> None:
        if delay_min:
            assert conn, 'Database connection is required for delayed task'

        await self.send_record(topic, data)

    async def send_records(self, topic, values):
        for value in values:
            await self.send_record(topic, value)


@pytest.fixture
def email_templates_renderer():
    return EmailTemplatesRenderer()


@pytest.fixture
def mailbox():
    return Mailbox()


@pytest.fixture
def evo_sender_mock():
    return EvoSenderMock()


@pytest.fixture
def telegrambox():
    return []


@pytest.fixture
def crm_box():
    return []


@pytest.fixture
def esputnik_box():
    return []


@pytest.fixture
def test_flags() -> dict[str, bool]:
    return {}


@pytest.fixture
def fcm_message_handler():
    return FCMMessageHandler()


@pytest.fixture
def google_analytics_events_publisher():
    return GoogleAnalyticsEventsPublisher()


class S3Emulation:
    def __init__(self) -> None:
        # all files
        self.files: dict[str, UploadFile] = {}

        # track download calls
        self.download_calls: list[DownloadFile] = []

        # track copy calls
        self.copy_calls: list[CopyFile] = []

        # track delete calls
        self.delete_calls: list[str] = []

    # === Mocked S3 functions ===
    async def upload(
        self,
        item: UploadFile,
    ) -> None:
        self.files[item.key] = item

    async def download(self, item: DownloadFile) -> tuple[bytes, dict]:
        self.download_calls.append(item)

        item = self.files[item.key]
        return item.body, item.extra_metadata or {}

    async def exists(self, key: str) -> bool:
        return key in self.files

    async def delete(self, key: str) -> None:
        self.delete_calls.append(key)
        self.files.pop(key, None)

    async def get_object(self, item: DownloadFile) -> dict:
        self.download_calls.append(item)

        item = self.files.get(item.key)
        body = item.body if item else b''
        return {'Body': AsyncBytesIO(body), 'Metadata': {}, 'ContentLength': len(body)}

    async def get_keys_with_prefix(self, prefix: str) -> list[str]:
        assert prefix.endswith('/'), 'Prefix must end with a slash'

        return [key for key in self.files if key.startswith(prefix)]

    async def copy(self, item: CopyFile) -> None:
        self.copy_calls.append(item)

        source = self.files[item.source_key]
        self.files[item.destination_key] = UploadFile(
            key=item.destination_key,
            body=source.body,
        )

    async def head_object(self, item: DownloadFile) -> dict:
        item = self.files.get(item.key)
        body = item.body if item else b''
        return {'ContentLength': len(body)}

    # === Dict-like interface ===
    def __getitem__(self, key: str) -> UploadFile:
        return self.files[key]

    def __setitem__(self, key: str, value: UploadFile) -> None:
        self.files[key] = value

    def __contains__(self, item: str) -> bool:
        return item in self.files

    def __len__(self) -> int:
        return len(self.files)

    def __iter__(self) -> Generator[UploadFile, None, None]:
        return (item for item in self.files.values())

    # === Custom methods for preparing test data ===
    def upload_document_content(
        self,
        *,
        document_id: str,
        content: bytes,
        encrypt: bool = True,
    ) -> None:
        key = _get_document_original_s3_key(document_id=document_id)
        self.files[key] = UploadFile(
            key=key,
            body=content,
            encrypt=encrypt,
        )

    def upload_internal_signature_content(
        self,
        *,
        document_id: str,
        signature_id: str,
        content: bytes,
        encrypt: bool = True,
    ) -> None:
        key = get_internal_signature_s3_key(
            document_id=document_id,
            signature_id=signature_id,
        )
        self.files[key] = UploadFile(
            key=key,
            body=content,
            encrypt=encrypt,
        )

    def upload_external_signature_key_content(
        self,
        *,
        document_id: str,
        signature_id: str,
        content: bytes,
        encrypt: bool = True,
    ):
        key = get_external_key_signature_s3_key(
            document_id=document_id,
            signature_id=signature_id,
        )
        self.files[key] = UploadFile(
            key=key,
            body=content,
            encrypt=encrypt,
        )

    def upload_external_signature_stamp_content(
        self,
        *,
        document_id: str,
        signature_id: str,
        content: bytes,
        encrypt: bool = True,
    ):
        key = get_external_stamp_signature_s3_key(
            document_id=document_id,
            signature_id=signature_id,
        )
        self.files[key] = UploadFile(
            key=key,
            body=content,
            encrypt=encrypt,
        )

    def get_document_original(self, *, document_id: str) -> UploadFile:
        key = _get_document_original_s3_key(document_id=document_id)
        return self.files.get(key)

    def get_draft(self, *, draft_id: str) -> UploadFile:
        key = get_draft_s3_key(draft_id=draft_id)
        return self.files.get(key)

    def get_template(self, *, template_id: str) -> UploadFile:
        key = get_templates_s3_key(template_id=template_id)
        return self.files.get(key)

    def get_internal_signature(self, *, document_id: str, signature_id: str) -> UploadFile:
        key = get_internal_signature_s3_key(
            document_id=document_id,
            signature_id=signature_id,
        )
        return self.files.get(key)

    def get_external_signature_key(self, *, document_id: str, signature_id: str) -> UploadFile:
        key = get_external_key_signature_s3_key(
            document_id=document_id,
            signature_id=signature_id,
        )
        return self.files.get(key)

    def get_external_signature_stamp(self, *, document_id: str, signature_id: str) -> UploadFile:
        key = get_external_stamp_signature_s3_key(
            document_id=document_id,
            signature_id=signature_id,
        )
        return self.files.get(key)

    def get_actions_report_file(
        self,
        *,
        company_id: str,
        report_id: str,
        file_id: str,
    ) -> UploadFile:
        key = build_actions_report_file_s3_key(
            company_id=company_id,
            report_id=report_id,
            file_id=file_id,
        )
        return self.files.get(key)

    def get_document_revoke_file(self, revoke_id: str) -> UploadFile:
        return self.files.get(get_document_revoke_s3_key(revoke_id))


@pytest.fixture
def s3_emulation(monkeypatch) -> S3Emulation:
    """
    Mock all sets of s3_utils functions and store uploaded files in dict.
    """
    s3_emulation = S3Emulation()

    monkeypatch.setattr(s3_utils, 'upload', s3_emulation.upload)
    monkeypatch.setattr(s3_utils, 'download', s3_emulation.download)
    monkeypatch.setattr(s3_utils, 'exists', s3_emulation.exists)
    monkeypatch.setattr(s3_utils, 'delete', s3_emulation.delete)
    monkeypatch.setattr(s3_utils, 'get_object', s3_emulation.get_object)
    monkeypatch.setattr(s3_utils, 'copy', s3_emulation.copy)
    monkeypatch.setattr(s3_utils, 'head_object', s3_emulation.head_object)
    monkeypatch.setattr(s3_utils, 'get_keys_with_prefix', s3_emulation.get_keys_with_prefix)

    return s3_emulation


class ConciergeBackendClientEmulation:
    def __init__(self):
        # { session_id: user_id }
        self.sessions: dict[str, str | None] = {}

        # { user_id: profile }
        self.profiles: dict[str, dict[str, Any]] = {}

    @staticmethod
    def generate_session_id() -> str:
        return str(uuid4())

    def generate_new_session_token(self) -> str:
        session = self.generate_session_id()
        self.sessions[session] = None
        return conciergelib.util.encode({'session_id': session})

    def count_user_session(self, user_id: str) -> int:
        return len([u for u in self.sessions.values() if u == user_id])

    def get_user_profile(self, user_id: str) -> dict[str, Any]:
        return self.profiles.get(user_id, {})

    def sign_in(self, session_id: str, user_id: str) -> dict:
        self.sessions[session_id] = user_id
        new_token = conciergelib.util.encode({'session_id': session_id, 'user_id': user_id})
        return {'data': {'concierge': {'sign_in': {'access_token': new_token}}}}

    def sign_out(self, session_id: str) -> dict:
        self.sessions.pop(session_id, None)
        return {'data': {'concierge': {'sign_out': {'access_token': None}}}}

    def sign_out_sessions(self, user_id: str) -> dict:
        sessions_items = list(self.sessions.items())
        for session_id, session_user_id in sessions_items:
            if session_user_id == user_id:
                self.sessions.pop(session_id)
        return {'data': {'concierge': {'sign_out_sessions': None}}}

    def update_vchasno_profile(self, user_id: str, profile: dict) -> dict:
        self.profiles[user_id] = profile
        return {'data': {'vchasno': {'update': profile}}}

    @asynccontextmanager
    async def graphql(self, *, user_token: str, payload: dict) -> AsyncGenerator[dict, None]:
        operation = payload['operationName']
        token_data = conciergelib.util.decode(user_token)
        if operation == 'SignIn':
            data = self.sign_in(
                session_id=token_data['session_id'],
                user_id=payload['variables']['user_id'],
            )
        elif operation == 'SignOut':
            data = self.sign_out(
                session_id=token_data['session_id'],
            )
        elif operation == 'UpdateVchasnoProfile':
            data = self.update_vchasno_profile(
                user_id=token_data['user_id'],
                profile=payload['variables']['data'],
            )
        elif operation == 'SignOutSessions':
            data = self.sign_out_sessions(
                user_id=token_data['user_id'],
            )
        else:
            data = {'errors': 'Unknown operation'}

        response = MagicMock()
        response.status = 200
        response.text = AsyncMock(return_value=json.dumps(data))
        response.json = AsyncMock(return_value=data)
        yield response


@pytest.fixture
def concierge_emulation(monkeypatch):
    backend = ConciergeBackendClientEmulation()
    monkeypatch.setattr(ConciergeBackendClient, 'graphql', backend.graphql)
    yield backend


@pytest.fixture(autouse=True, scope='session')
def sql_alchemy_linter():
    # Create only one linter for all tests
    linters.create_cross_join_linter()


@pytest.fixture(autouse=True)
async def fake_redis(monkeypatch):
    """
    Mock redis for some test cases for better isolation
    """
    redis = FakeRedis(decode_responses=True)

    async def fake_redis_connect(app):
        app['redis'] = redis
        services.ctx_redis.set(redis)

    monkeypatch.setattr('app.signals.connect_redis', fake_redis_connect)

    yield

    await redis.close()


@pytest.fixture(scope='session', autouse=True)
def event_loop():
    uvloop.install()
    loop = asyncio.new_event_loop()

    # Use debug mode only for debugging purposes locally
    # loop.set_debug(True)

    asyncio.set_event_loop(loop)

    # init eusign for all test sessions
    init_eusign()

    try:
        yield loop
    finally:
        if not loop.is_closed():
            # cleanup the event loop if it hasn't been cleaned up already
            pending = asyncio.all_tasks(loop)
            if pending:
                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

            try:
                loop.run_until_complete(loop.shutdown_asyncgens())
                loop.run_until_complete(loop.shutdown_default_executor())
            except Exception as e:
                warnings.warn(f'Error cleaning up asyncio loop: {e}', RuntimeWarning, stacklevel=2)
            finally:
                loop.close()


@pytest.fixture(scope='session')
async def thread_pool():
    loop = asyncio.get_running_loop()
    executor = ThreadPoolExecutor(max_workers=10)
    loop.set_default_executor(executor)

    yield executor

    executor.shutdown()


@pytest.fixture(scope='session')
async def db_engine_main():
    config = read_app_config()
    engine = await create_database_engine(
        dsn=config.db.url,
        timeout=config.db.timeout,
        minsize=config.db.minsize,
        maxsize=config.db.maxsize,
    )

    # - Prepare billing schema once for all test session
    # - Drop document categories from db
    try:
        async with engine.acquire() as conn:
            await prepare_billing_schema(conn)
            await conn.execute('DELETE FROM document_categories;')

            yield conn
    finally:
        async with engine.acquire() as conn:
            await flush_db(conn, metadata)

        await engine.dispose()


@pytest.fixture(scope='session')
async def db_engine_events():
    config = read_app_config()
    engine = await create_database_engine(
        dsn=config.events_db.url,
        timeout=config.events_db.timeout,
        minsize=config.events_db.minsize,
        maxsize=config.events_db.maxsize,
    )
    try:
        async with engine.acquire() as conn:
            yield conn
    finally:
        await engine.dispose()


class FakeDBConnection(DBConnection):
    def __init__(
        self,
        conn: DBConnection,
        conn_id: str,
        transaction: Transaction,
        fake_engine: 'FakeDbEngine',
    ):
        self._conn_id = conn_id
        self._conn = conn
        self._transaction = transaction
        self._fake_engine = fake_engine

        super().__init__(
            conn=SAConnection(
                connection=conn._conn._connection,
                engine=conn._conn._engine,
            ),
        )

    async def _fake_close(self, c):
        pass

    async def _get_fake_tr_coro(self):
        return self._transaction

    def begin(self, isolation_level=None, readonly=False, deferrable=False):
        return _ContextManager[Transaction](self._get_fake_tr_coro(), self._fake_close)

    async def execute(self, *args, **kwargs):
        if self._fake_engine.is_closed(self._conn_id):
            raise RuntimeError('Used db_conn outside of context manager')

        while self._conn._connection._waiter:
            await asyncio.sleep(0.01)
        return await super().execute(*args, **kwargs)


class FakeDbEngine:
    def __init__(self, conn: DBConnection):
        self._conn = conn
        self._trans = None
        self._opened_connections = set()

    async def start(self, app):
        app['db'] = self
        app['db_readonly'] = self
        services.ctx_db.set(self)
        services.ctx_db_readonly.set(self)
        self._trans = await self._conn._conn.begin()

    async def stop(self):
        if self._trans:
            await self._trans.rollback()
            self._trans = None

    async def _get_fake_conn_coro(self, conn_id):
        return FakeDBConnection(
            conn=self._conn,
            conn_id=conn_id,
            transaction=self._trans,
            fake_engine=self,
        )

    async def _fake_close(self, conn_id, c):
        self._opened_connections.discard(conn_id)

    def is_closed(self, conn_id) -> bool:
        return conn_id not in self._opened_connections

    def acquire(self):
        conn_id = generate_uuid()
        self._opened_connections.add(conn_id)
        coro = self._get_fake_conn_coro(conn_id)
        close = partial(self._fake_close, conn_id)
        return _ContextManager[DBConnection](coro, close)


class FakeDbEngineEvents(FakeDbEngine):
    async def start(self, app):
        app['events_db'] = self
        services.ctx_events_db.set(self)
        self._trans = await self._conn._conn.begin()


@pytest.fixture
def edi_request_mock(monkeypatch):
    async def _mock_side_effect(method: str, **data: Any) -> DataDict:
        """
        Here you can add default responses for all tests.

        Use `edi_request_mock.return_value` to set custom response for specific test.
        """
        if method == 'handle_document_revoke_new':
            return {'content': 'c29tZSB4bWwgY29udGVudA==', 'signature_format': 'internal_wrapped'}

        return {}

    # You can use `edi_request_mock.assert_called_once_with(...)` to assert that a specific
    # method was called with specific arguments. Autospec here helps to avoid typos in
    # method arguments.
    _mock = create_autospec(edi.Client.raw_request, side_effect=_mock_side_effect)
    monkeypatch.setattr(edi.Client, 'raw_request', staticmethod(_mock))
    yield _mock


@pytest.fixture(autouse=True)
def mock_all(  # noqa: C901
    db_engine_main,
    db_engine_events,
    mailbox,
    email_templates_renderer,
    fcm_message_handler,
    google_analytics_events_publisher,
    s3_emulation,
    evo_sender_mock,
    telegrambox,
    monkeypatch,
    crm_box,
    esputnik_box,
    test_flags,
    thread_pool,
    edi_request_mock,
):
    # Mock db connection, use one connection per test
    # start transaction before test starts - rollback after finish
    db_helper = FakeDbEngine(db_engine_main)
    db_helper_events = FakeDbEngineEvents(db_engine_events)

    async def mocked_connect_db(app):
        await db_helper.start(app)
        await db_helper_events.start(app)

    async def mocked_disconnect_db(app):
        await db_helper.stop()
        await db_helper_events.stop()

    monkeypatch.setattr('app.signals.connect_db', mocked_connect_db)
    monkeypatch.setattr('app.signals.disconnect_db', mocked_disconnect_db)

    # Mock aws services, create mocked one if needed
    async def mocked_aws_init(app):
        fake_client = mock.Mock()
        fake_client.exceptions.NotFoundException = Exception
        app['aws_ses_client'] = fake_client
        app['bedrock_client'] = fake_client
        app['textract_client'] = fake_client
        services.ctx_aws_ses_client.set(fake_client)
        services.ctx_bedrock_client.set(fake_client)
        services.ctx_textract_client.set(fake_client)
        yield

    monkeypatch.setattr('app.signals.init_aws_ses_client', mocked_aws_init)
    monkeypatch.setattr('app.signals.init_s3_client', mocked_aws_init)
    monkeypatch.setattr('app.signals.init_bedrock_client', mocked_aws_init)

    # Mock threadpool
    async def mocked_thread_pool(_):
        yield thread_pool

    monkeypatch.setattr('app.signals.init_thread_pool', mocked_thread_pool)

    # Mock aiohttp.web.Application for flushing redis and es on cleanup
    class FakeApplication(aiohttp.web.Application):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self._on_cleanup.append(flush_redis)
            self._on_cleanup.append(flush_es)

    monkeypatch.setattr('aiohttp.web.Application', FakeApplication)

    async def mock_email_templates_renderer(template_name, context):
        email_templates_renderer.extend(
            [{'template_name': template_name, 'context': context}],
        )

        return await emailing._render_template(
            template_name=template_name,
            context=context,
        )

    monkeypatch.setattr(emailing, 'render_email_template', mock_email_templates_renderer)

    async def mock_fcm_message_handler(
        self, notification_id, title, description, firebase_id, payload=None
    ):
        body = fcm_utils.build_request_body(
            title=title,
            notification_id=notification_id,
            description=description,
            firebase_id=firebase_id,
            payload=payload,
        )
        if body is not None:
            fcm_message_handler.extend([body])

    monkeypatch.setattr(FCMClient, 'send_push_notification', mock_fcm_message_handler)

    async def mock_google_analytics_events_publisher(self, event, analytics):
        measurement = GoogleAnalyticsClient.get_measurement(event=event)
        body = GoogleAnalyticsClient.build_body(measurement=measurement, analytics=analytics)
        if body is not None:
            google_analytics_events_publisher.extend([body])

    monkeypatch.setattr(
        GoogleAnalyticsClient, 'publish_event', mock_google_analytics_events_publisher
    )

    # Mock emails.
    async def mock_send_emails(config, emails):
        if mailbox.fail:
            raise SMTPException
        mailbox.extend(emails)

    monkeypatch.setattr(emailing, 'send_emails', mock_send_emails)

    # Mock kafka consumer
    async def mock_kafka_consumer(app):
        kafka_mock = KafkaClientMock(app)
        app['kafka'] = kafka_mock
        services.ctx_kafka.set(kafka_mock)

    monkeypatch.setattr(signals, 'connect_kafka', mock_kafka_consumer)

    def flags_manager_get(self, flag):
        # default flags for all tests
        default_flags: dict[str, bool] = {
            FeatureFlags.ES_SEARCH.value: True,
            FeatureFlags.ES_SEARCH_API.value: True,
            FeatureFlags.USE_ES_FOR_COMMENT_LIST.value: True,
            FeatureFlags.AUTOSEND_DOCUMENTS.value: True,
            FeatureFlags.ENABLE_FILES_FULL_DOWNLOAD.value: True,
            FeatureFlags.ENABLE_SYNC_RECIPIENTS_DATE_RECEIVED.value: True,
            FeatureFlags.ENABLE_LISTING_DATE_FROM_ES.value: True,
            FeatureFlags.SET_MIN_17_PDF_VERSION.value: True,
            FeatureFlags.ENABLE_AI_STRUCTURED_DATA_EXTRACTION.value: True,
        }

        _flags_dict = default_flags | test_flags

        assert all(isinstance(k, str) for k in _flags_dict), 'All keys must be strings'

        value = _flags_dict.get(flag)
        if value is None:
            return None
        return lambda *args, **kwargs: bool(value)

    monkeypatch.setattr(TestFlagsManager, 'get_flag', flags_manager_get)

    # Mock EVOSender.
    monkeypatch.setattr(EvoSender, 'send_viber_or_sms', evo_sender_mock)

    # Mock VchasnoCRM
    async def crm_mock_request(_, url: URL, data: DataDict):
        crm_box.append(data)

    monkeypatch.setattr(AiohttpVchasnoCrmClient, 'post', crm_mock_request)

    # Mock telegram send message
    async def mock_send_telegram(chat_id, data):
        telegrambox.append({'chat_id': chat_id, 'data': data})
        return web.json_response({'status': DispatcherStatus.ok.value})

    monkeypatch.setattr('app.telegram.utils.send_request_to_telegram', mock_send_telegram)

    # Mock ESputnik requests
    async def mock_esputnik_requests(self, method, url, data=None, raise_for_status=False):
        esputnik_box.append(
            {
                '__key': (url, data.get('eventTypeKey')),
                'method': method,
                'url': url,
                'data': data,
            },
        )
        return {'asyncSessionId': 1, 'id': '2'}

    monkeypatch.setattr(Client, '_send_request', mock_esputnik_requests)

    # Mock bill pdf generation
    async_mock = mock.AsyncMock(return_value=None)
    monkeypatch.setattr('worker.billing.jobs.bill_to_pdf', async_mock)

    # Mock url2pdf requests for converting xml -> pdf
    async def mock_xml2pdf_request(user, document_id, *args, **kwargs):
        content = b'I am PDF file'
        return s3_utils.UploadFile(
            key=document_id,
            body=content,
        )

    monkeypatch.setattr('api.downloads.utils.convert_xml_to_pdf', mock_xml2pdf_request)
    monkeypatch.setattr(utils, 'check_document', AsyncMock(return_value=AntivirusCheckStatus.clean))

    async def mock_youcontrol_request(edrpou: str, resource: YouControlResource):
        if resource == YouControlResource.usr:
            return {'mainEconomicActivity': {'code': '63.11'}}
        if resource == YouControlResource.staff:
            return [
                {'year': 2021, 'max': 20, 'min': 10},
                {'year': 2018, 'max': 15, 'min': 10},
                {'year': 2019, 'max': 15, 'min': 10},
            ]
        raise ValueError('Unknown resource')

    monkeypatch.setattr(YouControlClient, 'get_resource_for_edrpou', mock_youcontrol_request)

    # Mock EVOPay.
    async def mock_evopay_initiate_payment_process(*args, **kwargs):
        return 'https://stub.com'

    monkeypatch.setattr(
        EvopayClient, 'initiate_payment_process', mock_evopay_initiate_payment_process
    )

    # Mock collabora requests
    async def fetch_collabora_discovery(*args, **kwargs):
        with open('app/editor/tests/discovery.xml') as file:
            return file.read()

    monkeypatch.setattr(
        CollaboraDiscoveryClient,
        'fetch_collabora_discovery',
        fetch_collabora_discovery,
    )

    # Mock gotenberg
    async def mock_convert_office_file_to_pdf(*args, **kwargs):
        """Generate a simple PDF file with one page and some text"""
        buffer = BytesIO()
        c = canvas.Canvas(buffer)
        c.drawString(0, 0, 'I am PDF file')
        c.save()
        return buffer.getvalue()

    monkeypatch.setattr(
        GotenbergClient, 'convert_office_file_to_pdf', mock_convert_office_file_to_pdf
    )

    # override the default logger formatter
    # to show extra context in logs as well
    logging.getLogger().handlers[0].setFormatter(
        ConsoleJsonFormatter('%(levelname)s %(name)s:%(lineno)d %(message)s')
    )


@pytest.fixture()
def send_email_mock(monkeypatch) -> Generator[AsyncMock, None, None]:
    """
    Create wrapper mock for emailing.send_email function. The function will be called
    otherwise, but calls will be tracked using mock object.
    """
    send_email = emailing.send_email
    mock = create_autospec(spec=send_email, side_effect=send_email)
    monkeypatch.setattr(emailing, 'send_email', mock)
    yield mock


@pytest.fixture()
def eusign_mock(monkeypatch):
    class EusignMock:
        def __init__(self):
            self.edrpou = TEST_COMPANY_EDRPOU
            # mocked functions
            self.verify = mock.AsyncMock(side_effect=self._verify)
            self.get_cert_info = mock.AsyncMock(side_effect=self._get_cert_info)
            self.get_edrpou = mock.Mock(side_effect=self._get_edrpou)

        def _get_edrpou(self, cert: bytes):
            return self.edrpou

        def _verify(self, *args, **kwargs):
            return prepare_signature_info(SignatureType.signature, self.edrpou)

        def _get_cert_info(self, signature: bytes):
            val = 'Цифрова печатка' if signature == TEST_STAMP_BYTES else 'Цифровий підпис'
            return mock.Mock(ext_key_usages=val)

    _mock = EusignMock()
    monkeypatch.setattr(eusign_utils, 'verify', _mock.verify)
    monkeypatch.setattr(eusign_utils, 'get_cert_info', _mock.get_cert_info)
    monkeypatch.setattr(eusign_utils, 'get_edrpou', _mock.get_edrpou)

    yield _mock


@pytest.fixture
def mock_local_now():
    with patch('app.billing.validators.local_now') as mock:
        yield mock
