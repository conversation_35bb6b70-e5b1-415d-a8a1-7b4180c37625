import logging

import pytest

from app.document_automation.enums import DocumentAutomationStatus
from app.document_automation.tables import (
    document_automation_condition_table,
    document_automation_template_table,
)
from app.document_categories.types import PublicDocumentCategory
from app.documents.db import select_listings
from app.documents.enums import AccessSource, FirstSignBy
from app.documents.tests.utils import prepare_private_document
from app.documents.types import UpdateSignersDataSignerType
from app.documents_fields.db import select_document_parameters
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.tables import documents_fields_table
from app.groups.db import insert_group, insert_group_member
from app.lib.enums import DocumentStatus
from app.reviews.db import select_review_requests
from app.reviews.enums import ReviewType
from app.signatures.db import select_document_signers
from app.tags.db import select_tags_by_documents
from app.tags.utils import create_new_tags_for_document_templates
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    assert_list_any_order,
    insert_values,
    prepare_app_client,
    prepare_company_data,
    prepare_document_data,
    prepare_public_document_categories,
    prepare_review,
    prepare_user_data,
    set_billing_company_config,
)
from worker import topics
from worker.document_automation.jobs import (
    apply_document_authomation_template,
    find_document_automation,
    start_document_automation,
)
from worker.jobs import TOPIC_JOBS

logger = logging.getLogger(__name__)

COMPANY_ID = 'f52a1720-8bb2-4aee-a6b0-b89ce13b3355'
DOCUMENT_ID = '0cc27377-b598-4243-bd64-b35736720f72'

COMPANY_ID_1 = '10000000-0000-0000-0000-000*********'
COMPANY_ID_2 = '10000000-0000-0000-0000-000*********'

COMPANY_EDRPOU_1 = '*********'
COMPANY_EDRPOU_2 = '*********'

TEST_UUID_1 = '14b7066b-cd16-4af4-88c5-58285c6c8644'
TEST_UUID_2 = 'b7b18b79-fe7f-47d9-9122-c16a0421b3b2'
TEST_UUID_3 = '57fabbd0-f085-4c11-890b-74a5892272a6'
TEST_UUID_4 = '2c705883-70ad-4ff2-833f-d72bd06d4ee7'

ROLE_ID_1 = '9c617ab2-b269-495a-aae4-45a750dfa9e1'
ROLE_ID_2 = '0e8d0798-a986-40c1-b766-e87b5cb13777'
ROLE_ID_3 = '70628b6e-e6f3-47b5-a98c-ea91cbb41b1f'

FILED_ID_1 = '4244ca02-65db-463a-a5e3-6478d1a78f00'
FILED_ID_2 = '27a79ce7-065e-431b-83e3-ae195a921a48'

TAG_ID_1 = '7f0dce16-32ea-4052-a822-ba687f7e873b'
TAG_ID_2 = '0e0cac97-464e-40cd-ae99-7d81ed375926'

USER_EMAIL_1 = '<EMAIL>'
USER_EMAIL_2 = '<EMAIL>'
USER_EMAIL_3 = '<EMAIL>'

GROUP_ID_1 = 'b7b18b79-fe7f-47d9-9122-c16a0421b3b2'
GROUP_ID_2 = '57fabbd0-f085-4c11-890b-74a5892272a6'


@pytest.mark.parametrize(
    'expected_data, document_parameters',
    [
        # Different types of sign process on uploading
        (
            {
                'document_side': 'outbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [TEST_COMPANY_EDRPOU],
                'document_sign_process': 'bilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': 100_00,
                'document_uploaded_by_edrpou': TEST_COMPANY_EDRPOU,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'status_id': DocumentStatus.uploaded.value,
                'amount': 100_00,
            },
        ),
        (
            {
                'document_side': 'outbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [TEST_COMPANY_EDRPOU],
                'document_sign_process': 'bilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': 100_00,
                'document_uploaded_by_edrpou': TEST_COMPANY_EDRPOU,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'first_sign_by': FirstSignBy.recipient.value,
                'status_id': DocumentStatus.uploaded.value,
                'amount': 100_00,
            },
        ),
        (
            {
                'document_side': 'internal',
                'document_recipients.email': [],
                'document_recipients.edrpou': [TEST_COMPANY_EDRPOU],
                'document_sign_process': 'internal',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': 100_00,
                'document_uploaded_by_edrpou': TEST_COMPANY_EDRPOU,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'is_internal': True,
                'status_id': DocumentStatus.uploaded.value,
                'amount': 100_00,
            },
        ),
        (
            {
                'document_side': 'outbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [TEST_COMPANY_EDRPOU],
                'document_sign_process': 'multilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': 100_00,
                'document_uploaded_by_edrpou': TEST_COMPANY_EDRPOU,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'is_multilateral': True,
                'status_id': DocumentStatus.uploaded.value,
                'amount': 100_00,
            },
        ),
        (
            {
                'document_side': 'outbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [TEST_COMPANY_EDRPOU],
                'document_sign_process': 'bilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': None,
                'document_uploaded_by_edrpou': TEST_COMPANY_EDRPOU,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {'status_id': DocumentStatus.uploaded.value},
        ),
        # Different types of inbox documents
        (
            {
                'document_side': 'inbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [
                    TEST_COMPANY_EDRPOU,
                    TEST_DOCUMENT_EDRPOU_RECIPIENT,
                ],
                'document_sign_process': 'bilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': None,
                'document_uploaded_by_edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'edrpou_owner': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'document_recipients': [{'edrpou': TEST_COMPANY_EDRPOU, 'emails': []}],
                'status_id': DocumentStatus.signed_and_sent.value,
            },
        ),
        (
            {
                'document_side': 'inbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [
                    TEST_COMPANY_EDRPOU,
                    TEST_DOCUMENT_EDRPOU_RECIPIENT,
                ],
                'document_sign_process': 'bilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': None,
                'document_uploaded_by_edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'edrpou_owner': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'document_recipients': [{'edrpou': TEST_COMPANY_EDRPOU, 'emails': []}],
                'status_id': DocumentStatus.signed_and_sent.value,
            },
        ),
        (
            {
                'document_side': 'inbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [
                    TEST_COMPANY_EDRPOU,
                    TEST_DOCUMENT_EDRPOU_RECIPIENT,
                ],
                'document_sign_process': 'bilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': None,
                'document_uploaded_by_edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'first_sign_by': FirstSignBy.recipient.value,
                'edrpou_owner': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'document_recipients': [
                    {'edrpou': TEST_COMPANY_EDRPOU, 'emails': []},
                    {'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT, 'emails': []},
                ],
                'status_id': DocumentStatus.signed_and_sent.value,
            },
        ),
        (
            {
                'document_side': 'inbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [
                    TEST_COMPANY_EDRPOU,
                    TEST_DOCUMENT_EDRPOU_RECIPIENT,
                ],
                'document_sign_process': 'multilateral',
                'document_category': PublicDocumentCategory.other.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': None,
                'document_uploaded_by_edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'is_multilateral': True,
                'edrpou_owner': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'document_recipients': [
                    {'edrpou': TEST_COMPANY_EDRPOU, 'emails': []},
                ],
                'status_id': DocumentStatus.flow.value,
            },
        ),
        (
            {
                'document_side': 'outbox',
                'document_recipients.email': [],
                'document_recipients.edrpou': [TEST_COMPANY_EDRPOU],
                'document_sign_process': 'bilateral',
                'document_category': PublicDocumentCategory.act.value,
                'document_uploaded_by': USER_EMAIL_1,
                'document_amount': None,
                'document_uploaded_by_edrpou': TEST_COMPANY_EDRPOU,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                'status_id': DocumentStatus.uploaded.value,
                'category': PublicDocumentCategory.act.value,
            },
        ),
    ],
)
async def test_start_document_automation(aiohttp_client, expected_data, document_parameters):
    app, client = await prepare_app_client(aiohttp_client)
    await prepare_public_document_categories(amount=10)
    await prepare_company_data(app, id=COMPANY_ID)
    await set_billing_company_config(company_id=COMPANY_ID, document_templates_enabled=True)

    user = await prepare_user_data(app, email=USER_EMAIL_1, company_id=COMPANY_ID)

    document = await prepare_document_data(
        app=app, owner=user, id=DOCUMENT_ID, **document_parameters
    )

    data = {'document_id': document.id, 'company_edrpou': TEST_COMPANY_EDRPOU}
    await start_document_automation(app, data, logger)
    messages = app['kafka'].messages
    assert len(messages) == 1
    topic, data = messages[0]
    assert topic == topics.FIND_DOCUMENT_AUTOMATION
    assert data['company_id'] == COMPANY_ID
    assert data['document_id'] == DOCUMENT_ID

    # Sort recipients for ability to compare
    expected_data['document_recipients.edrpou'] = sorted(
        expected_data['document_recipients.edrpou'],
    )
    expected_data['document_recipients.email'] = sorted(
        expected_data['document_recipients.email'],
    )
    data['context']['document_recipients.edrpou'] = sorted(
        data['context']['document_recipients.edrpou'],
    )
    data['context']['document_recipients.email'] = sorted(
        data['context']['document_recipients.email'],
    )

    assert data['context'] == expected_data


async def test_start_document_automation__skip_private_on_upload(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)

    await prepare_public_document_categories(amount=10)
    await prepare_company_data(app, id=COMPANY_ID, edrpou=TEST_COMPANY_EDRPOU)
    await set_billing_company_config(company_id=COMPANY_ID, document_templates_enabled=True)

    user = await prepare_user_data(app, email=USER_EMAIL_1, company_id=COMPANY_ID)

    document = await prepare_document_data(app=app, owner=user, id=DOCUMENT_ID)
    await prepare_private_document(document_id=DOCUMENT_ID, company_edrpou=TEST_COMPANY_EDRPOU)

    data = {'document_id': document.id, 'company_edrpou': TEST_COMPANY_EDRPOU}
    await start_document_automation(app, data, logger)
    messages = app['kafka'].messages
    assert len(messages) == 0


async def test_start_document_automation__dont_skip_private_for_recipient(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)

    await prepare_public_document_categories(amount=10)

    await prepare_company_data(app, id=COMPANY_ID_1, edrpou=COMPANY_EDRPOU_1)
    await set_billing_company_config(company_id=COMPANY_ID_1, document_templates_enabled=True)

    await prepare_company_data(app, id=COMPANY_ID_2, edrpou=COMPANY_EDRPOU_2)
    await set_billing_company_config(company_id=COMPANY_ID_2, document_templates_enabled=True)

    owner = await prepare_user_data(app, email=USER_EMAIL_1, company_id=COMPANY_ID_1)
    recipient = await prepare_user_data(app, email=USER_EMAIL_2, company_id=COMPANY_ID_2)

    document = await prepare_document_data(
        app=app,
        owner=owner,
        id=DOCUMENT_ID,
        another_recipients=[recipient],
    )
    # Document is private only for the owner, not for the recipient, so we should start automation
    await prepare_private_document(document_id=DOCUMENT_ID, company_edrpou=COMPANY_EDRPOU_1)

    data = {'document_id': document.id, 'company_edrpou': COMPANY_EDRPOU_2}
    await start_document_automation(app, data, logger)
    messages = app['kafka'].messages
    assert len(messages) == 1
    assert messages[0][0] == topics.FIND_DOCUMENT_AUTOMATION


@pytest.mark.parametrize(
    'context, conditions, expected_template_id',
    [
        # Just verify that conditions selecting correctly works
        (
            {
                'document_side': 'outbox',
                'document_recipients': [{'edrpou': TEST_COMPANY_EDRPOU}],
                'document_sign_process': 'bilateral',
                'document_uploaded_by': USER_EMAIL_1,
            },
            {
                TEST_UUID_1: {'and': [{'eq': ['#document_side', 'outbox']}]},
                TEST_UUID_2: {'and': [{'eq': ['#document_side', 'inbox']}]},
            },
            TEST_UUID_1,
        ),
        (
            {
                'document_side': 'outbox',
                'document_recipients': [{'edrpou': TEST_COMPANY_EDRPOU}],
                'document_sign_process': 'bilateral',
                'document_uploaded_by': USER_EMAIL_1,
            },
            {
                TEST_UUID_1: {'and': [{'eq': ['#document_side', 'inbox']}]},
                TEST_UUID_2: {'and': [{'eq': ['#document_side', 'outbox']}]},
            },
            TEST_UUID_2,
        ),
        (
            {
                'document_side': 'outbox',
                'document_recipients': [{'edrpou': TEST_COMPANY_EDRPOU}],
                'document_sign_process': 'bilateral',
                'document_uploaded_by': USER_EMAIL_2,
            },
            {
                TEST_UUID_1: {
                    'and': [
                        {'eq': ['#document_side', 'outbox']},
                        {'eq': ['#document_uploaded_by', USER_EMAIL_1]},
                    ]
                },
                TEST_UUID_2: {
                    'and': [
                        {'eq': ['#document_side', 'outbox']},
                        {'eq': ['#document_uploaded_by', USER_EMAIL_2]},
                    ]
                },
            },
            TEST_UUID_2,
        ),
        # Check that template with highest score will be selected
        (
            {
                'document_side': 'outbox',
                'document_recipients': [{'edrpou': TEST_COMPANY_EDRPOU}],
                'document_sign_process': 'bilateral',
                'document_uploaded_by': USER_EMAIL_1,
            },
            {
                TEST_UUID_1: {'and': [{'eq': ['#document_side', 'outbox']}]},
                TEST_UUID_2: {
                    'and': [
                        {'eq': ['#document_side', 'outbox']},
                        {'eq': ['#document_sign_process', 'bilateral']},
                        {'eq': ['#document_recipients.edrpou', TEST_COMPANY_EDRPOU]},
                    ]
                },
                TEST_UUID_3: {'and': [{'eq': ['#document_sign_process', 'bilateral']}]},
            },
            TEST_UUID_2,
        ),
        # More complex conditions also works
        (
            {
                'document_side': 'inbox',
                'document_recipients': [{'edrpou': TEST_COMPANY_EDRPOU}],
                'document_sign_process': 'bilateral',
                'document_uploaded_by': USER_EMAIL_1,
            },
            {
                TEST_UUID_1: {'and': [{'eq': ['#document_side', 'outbox']}]},
                TEST_UUID_2: {
                    'and': [
                        {'eq': ['#document_side', 'inbox']},
                        {'eq': ['#document_recipients.edrpou', TEST_COMPANY_EDRPOU]},
                    ]
                },
                TEST_UUID_3: {'and': [{'eq': ['#document_side', 'inbox']}]},
            },
            TEST_UUID_2,
        ),
        (
            {
                'document_side': 'inbox',
                'document_recipients': [
                    {'email': USER_EMAIL_1},
                ],
                'document_sign_process': 'bilateral',
                'document_uploaded_by': TEST_UUID_1,
            },
            {
                TEST_UUID_1: {
                    'and': [
                        {'eq': ['#document_side', 'inbox']},
                        {'eq': ['#document_recipients.email', USER_EMAIL_2]},
                    ],
                },
                TEST_UUID_2: {
                    'and': [
                        {'eq': ['#document_side', 'inbox']},
                        {'eq': ['#document_recipients.email', USER_EMAIL_1]},
                    ]
                },
                TEST_UUID_3: {'and': [{'eq': ['#document_side', 'inbox']}]},
            },
            TEST_UUID_2,
        ),
        (
            {
                'document_side': 'inbox',
                'document_recipients': [
                    {'email': USER_EMAIL_1},
                ],
                'document_sign_process': 'bilateral',
                'document_uploaded_by': USER_EMAIL_1,
                'document_uploaded_by_edrpou': TEST_COMPANY_EDRPOU,
                'template_owned_by_edrpou': TEST_COMPANY_EDRPOU,
            },
            {
                # email doesn't match
                TEST_UUID_1: {
                    'and': [
                        {'eq': ['#document_side', 'inbox']},
                        {'eq': ['#document_recipients.email', USER_EMAIL_2]},
                    ],
                },
                # ignore if user upload document and same user presents in document_recipients
                TEST_UUID_2: {
                    'and': [
                        {'eq': ['#document_side', 'inbox']},
                        {'eq': ['#document_recipients.email', USER_EMAIL_1]},
                    ]
                },
                TEST_UUID_3: {'and': [{'eq': ['#document_side', 'inbox']}]},
            },
            TEST_UUID_3,
        ),
    ],
)
async def test_find_document_automation(
    aiohttp_client, context, conditions, expected_template_id, monkeypatch
):
    app, client = await prepare_app_client(aiohttp_client)
    await prepare_company_data(app, id=COMPANY_ID)
    user = await prepare_user_data(app, role_id=TEST_UUID_1, email='<EMAIL>', company_id=COMPANY_ID)
    await prepare_document_data(
        app=app,
        owner=user,
        id=DOCUMENT_ID,
    )

    # prevent sending message to the next job
    async def do_nothing(*_) -> None:
        pass

    monkeypatch.setitem(TOPIC_JOBS, topics.APPLY_DOCUMENT_AUTOMATION_TEMPLATE, do_nothing)

    for idx, (template_id, condition) in enumerate(conditions.items(), start=1):
        await insert_values(
            app=app,
            table=document_automation_template_table,
            id=template_id,
            company_id=COMPANY_ID,
            name=f'name_{idx}',
            created_by=user.role_id,
        )
        await insert_values(
            app=app,
            table=document_automation_condition_table,
            company_id=user.company_id,
            conditions=condition,
            template_id=template_id,
            status=DocumentAutomationStatus.enabled,
            order=idx,
        )

    data = {'company_id': COMPANY_ID, 'document_id': DOCUMENT_ID, 'context': context}
    await find_document_automation(app, data, logger)

    messages = app['kafka'].messages
    assert len(messages) == 2

    topic_, _ = messages[0]
    assert topic_ == topics.FIND_DOCUMENT_AUTOMATION

    topic_, data = messages[-1]  # last topic
    assert topic_ == topics.APPLY_DOCUMENT_AUTOMATION_TEMPLATE
    assert data['company_id'] == COMPANY_ID
    assert data['document_id'] == DOCUMENT_ID
    assert data['template_id'] == expected_template_id


@pytest.mark.parametrize(
    'template,'
    'document,'
    'expected_listings,'
    'expected_signers,'
    'expected_reviewers,'
    'expected_parameters,'
    'expected_tags,'
    'expected_emails',
    [
        # Apply template without any settings
        (
            {
                'set_review': False,
                'review_settings': None,
                'set_signers': False,
                'signers_settings': None,
                'set_viewers': False,
                'viewers_settings': None,
                'set_tags': False,
                'tags_settings': None,
            },
            {},
            [{'role_id': ROLE_ID_1, 'sources': AccessSource.default}],
            [],
            [],
            [],
            [],
            [],
        ),
        # Template with unordered settings
        (
            {
                'set_review': True,
                'review_settings': {
                    'reviewers_ids': [ROLE_ID_3],
                    'is_ordered': False,
                    'is_required': False,
                },
                'set_signers': True,
                'signers_settings': {
                    'signers_ids': [ROLE_ID_2, ROLE_ID_3],
                    'is_ordered': False,
                },
                'set_viewers': True,
                'viewers_settings': {'viewers_ids': [ROLE_ID_2]},
            },
            {},
            [
                {'role_id': ROLE_ID_1, 'sources': AccessSource.default},
                {
                    'role_id': ROLE_ID_2,
                    'sources': AccessSource.signer | AccessSource.viewer,
                },
                {
                    'role_id': ROLE_ID_3,
                    'sources': AccessSource.signer | AccessSource.reviewer,
                },
            ],
            [
                {'role_id': ROLE_ID_2, 'order': None, 'group_id': None, 'assigner': ROLE_ID_1},
                {'role_id': ROLE_ID_3, 'order': None, 'group_id': None, 'assigner': ROLE_ID_1},
            ],
            [{'to_role_id': ROLE_ID_3, 'order': None, 'from_role_id': ROLE_ID_1}],
            [],
            [],
            [
                {'subject': 'Ви отримали документ на погодження', 'to': USER_EMAIL_3},
                {'subject': 'Ви отримали документ на підпис', 'to': USER_EMAIL_2},
                {'subject': 'Ви отримали документ на підпис', 'to': USER_EMAIL_3},
                {'subject': 'Ви отримали доступ до документу', 'to': USER_EMAIL_2},
            ],
        ),
        # Template with unordered settings
        (
            {
                'set_review': True,
                'review_settings': {
                    'reviewers_ids': [ROLE_ID_2, ROLE_ID_3],
                    'is_ordered': True,
                    'is_required': False,
                },
                'set_signers': True,
                'signers_settings': {
                    'signers_ids': [ROLE_ID_2, ROLE_ID_3],
                    'is_ordered': True,
                },
                'set_viewers': True,
                'viewers_settings': {'viewers_ids': [ROLE_ID_2]},
            },
            {},
            [
                {'role_id': ROLE_ID_1, 'sources': AccessSource.default},
                {
                    'role_id': ROLE_ID_2,
                    'sources': AccessSource.signer | AccessSource.viewer | AccessSource.reviewer,
                },
                {
                    'role_id': ROLE_ID_3,
                    'sources': AccessSource.signer | AccessSource.reviewer,
                },
            ],
            [
                {'role_id': ROLE_ID_2, 'order': 1, 'group_id': None, 'assigner': ROLE_ID_1},
                {'role_id': ROLE_ID_3, 'order': 2, 'group_id': None, 'assigner': ROLE_ID_1},
            ],
            [
                {'to_role_id': ROLE_ID_2, 'order': 1, 'from_role_id': ROLE_ID_1},
                {'to_role_id': ROLE_ID_3, 'order': 2, 'from_role_id': ROLE_ID_1},
            ],
            [],
            [],
            [
                {'subject': 'Ви отримали документ на погодження', 'to': USER_EMAIL_2},
                {'subject': 'Ви отримали документ на підпис', 'to': USER_EMAIL_2},
                {'subject': 'Ви отримали доступ до документу', 'to': USER_EMAIL_2},
            ],
        ),
        # Template with fields settings
        (
            {
                'set_review': False,
                'set_signers': False,
                'set_viewers': False,
                'set_fields': True,
                'fields_settings': {
                    'fields': [
                        {'field_id': FILED_ID_1, 'value': '1', 'is_required': False},
                        {'field_id': FILED_ID_2, 'value': None, 'is_required': False},
                    ]
                },
            },
            {},
            [{'role_id': ROLE_ID_1, 'sources': AccessSource.default}],
            [],
            [],
            [
                {
                    'field_id': FILED_ID_1,
                    'value': '1',
                    'is_required': False,
                    'updated_by': ROLE_ID_1,
                },
                {
                    'field_id': FILED_ID_2,
                    'value': None,
                    'is_required': False,
                    'updated_by': ROLE_ID_1,
                },
            ],
            [],
            [],
        ),
        # Template with tags
        (
            {
                'set_review': False,
                'review_settings': None,
                'set_signers': False,
                'signers_settings': None,
                'set_viewers': False,
                'viewers_settings': None,
            },
            {},
            [
                {
                    'role_id': ROLE_ID_1,
                    'sources': AccessSource.default | AccessSource.tag,
                }
            ],
            [],
            [],
            [],
            ['tag1', 'tag2'],
            [],
        ),
        # Template with groups
        (
            {
                'set_review': False,
                'review_settings': None,
                'set_signers': True,
                'signers_settings': {
                    'signers_ids': [ROLE_ID_2, ROLE_ID_3],
                    'is_ordered': True,
                    'signer_entities': [
                        {
                            'id': GROUP_ID_1,
                            'type': UpdateSignersDataSignerType.group,
                        },
                        {
                            'id': ROLE_ID_3,
                            'type': UpdateSignersDataSignerType.role,
                        },
                    ],
                },
                'set_viewers': False,
                'viewers_settings': None,
            },
            {},
            [
                {'role_id': ROLE_ID_1, 'sources': AccessSource.default | AccessSource.signer},
                {
                    'role_id': ROLE_ID_2,
                    'sources': AccessSource.signer,
                },
                {
                    'role_id': ROLE_ID_3,
                    'sources': AccessSource.signer,
                },
            ],
            [
                {'role_id': None, 'order': 1, 'group_id': GROUP_ID_1, 'assigner': ROLE_ID_1},
                {'role_id': ROLE_ID_3, 'order': 2, 'group_id': None, 'assigner': ROLE_ID_1},
            ],
            [],
            [],
            [],
            [
                {'subject': 'Ви отримали документ на підпис', 'to': USER_EMAIL_1},
                {'subject': 'Ви отримали документ на підпис', 'to': USER_EMAIL_2},
            ],
        ),
        # Template with `assigned_to` field
        (
            {
                'set_review': True,
                'review_settings': {
                    'reviewers_ids': [ROLE_ID_2],
                    'is_ordered': True,
                    'is_required': False,
                },
                'set_signers': True,
                'signers_settings': {
                    'signers_ids': [ROLE_ID_1],
                    'is_ordered': True,
                },
                'set_viewers': False,
                'viewers_settings': None,
                'set_fields': True,
                'fields_settings': {
                    'fields': [
                        {'field_id': FILED_ID_1, 'value': '1', 'is_required': False},
                        {'field_id': FILED_ID_2, 'value': None, 'is_required': False},
                    ]
                },
                'assigned_to': ROLE_ID_3,
            },
            {},
            [
                {'role_id': ROLE_ID_1, 'sources': AccessSource.default | AccessSource.signer},
                {'role_id': ROLE_ID_2, 'sources': AccessSource.reviewer},
            ],
            [
                {'role_id': ROLE_ID_1, 'order': 1, 'group_id': None, 'assigner': ROLE_ID_3},
            ],
            [
                {'to_role_id': ROLE_ID_2, 'order': 1, 'from_role_id': ROLE_ID_3},
            ],
            [
                {
                    'field_id': FILED_ID_1,
                    'value': '1',
                    'is_required': False,
                    'updated_by': ROLE_ID_3,
                },
                {
                    'field_id': FILED_ID_2,
                    'value': None,
                    'is_required': False,
                    'updated_by': ROLE_ID_3,
                },
            ],
            [],
            [
                {'subject': 'Ви отримали документ на погодження', 'to': USER_EMAIL_2},
                {'subject': 'Ви отримали документ на підпис', 'to': USER_EMAIL_1},
            ],
        ),
        # Template with viewers from role and group
        (
            {
                'set_review': False,
                'review_settings': None,
                'set_signers': False,
                'signers_settings': None,
                'set_viewers': True,
                'viewers_settings': {
                    'viewers_ids': [ROLE_ID_3],
                    'viewers_group_ids': [GROUP_ID_1],
                },
            },
            {},
            [
                {'role_id': ROLE_ID_1, 'sources': AccessSource.default | AccessSource.group_viewer},
                {'role_id': ROLE_ID_2, 'sources': AccessSource.group_viewer},
                {'role_id': ROLE_ID_3, 'sources': AccessSource.viewer},
            ],
            [],
            [],
            [],
            [],
            [
                {'subject': 'Ви отримали доступ до документу', 'to': USER_EMAIL_1},
                {'subject': 'Ви отримали доступ до документу', 'to': USER_EMAIL_2},
                {'subject': 'Ви отримали доступ до документу', 'to': USER_EMAIL_3},
            ],
        ),
        # Template where the same role is assigned both as assigned_to and reviewer/signer
        (
            {
                'set_review': True,
                'review_settings': {
                    'reviewers_ids': [ROLE_ID_1],
                    'is_ordered': True,
                    'is_required': False,
                },
                'set_signers': True,
                'signers_settings': {
                    'signers_ids': [ROLE_ID_1],
                    'is_ordered': True,
                },
                'set_viewers': False,
                'viewers_settings': None,
                'set_fields': False,
                'fields_settings': None,
                'assigned_to': ROLE_ID_1,
            },
            {},
            [
                {
                    'role_id': ROLE_ID_1,
                    'sources': AccessSource.default | AccessSource.signer | AccessSource.reviewer,
                },
            ],
            [
                {'role_id': ROLE_ID_1, 'order': 1, 'group_id': None, 'assigner': ROLE_ID_1},
            ],
            [
                {'to_role_id': ROLE_ID_1, 'order': 1, 'from_role_id': ROLE_ID_1},
            ],
            [],
            [],
            [
                {'subject': 'Ви отримали документ на погодження', 'to': USER_EMAIL_1},
                {'subject': 'Ви отримали документ на підпис', 'to': USER_EMAIL_1},
            ],
        ),
    ],
)
async def test_apply_document_automation_template(
    aiohttp_client,
    mailbox,
    template,
    document,
    expected_listings,
    expected_signers,
    expected_reviewers,
    expected_parameters,
    expected_tags,
    expected_emails,
):
    app, client = await prepare_app_client(aiohttp_client)
    company_id = await prepare_company_data(app, id=COMPANY_ID)
    await set_billing_company_config(company_id=COMPANY_ID, custom_documents_fields_enabled=True)

    user = await prepare_user_data(
        app, email=USER_EMAIL_1, role_id=ROLE_ID_1, company_id=COMPANY_ID
    )
    await prepare_user_data(app, email=USER_EMAIL_2, role_id=ROLE_ID_2, company_id=COMPANY_ID)
    await prepare_user_data(app, email=USER_EMAIL_3, role_id=ROLE_ID_3, company_id=COMPANY_ID)

    await insert_values(
        app=app,
        table=document_automation_template_table,
        id=TEST_UUID_1,
        company_id=COMPANY_ID,
        name='TEST_1',
        is_active=True,
        created_by=ROLE_ID_1,
        **template,
    )

    await insert_values(
        app=app,
        table=documents_fields_table,
        id=FILED_ID_1,
        company_id=COMPANY_ID,
        name='TEST_1',
        type=DocumentFieldType.text,
        is_required=False,
        created_by=ROLE_ID_1,
    )
    await insert_values(
        app=app,
        table=documents_fields_table,
        id=FILED_ID_2,
        company_id=COMPANY_ID,
        name='TEST_2',
        type=DocumentFieldType.text,
        is_required=False,
        created_by=ROLE_ID_1,
    )
    if expected_tags:
        async with app['db'].acquire() as conn:
            await create_new_tags_for_document_templates(
                conn=conn,
                tags_names=expected_tags,
                document_template_ids=[TEST_UUID_1],
                company_id=user.company_id,
                assigner_role_id=user.role_id,
            )

    async with app['db'].acquire() as conn:
        await insert_group(
            conn=conn,
            name='test',
            id=GROUP_ID_1,
            created_by=user.role_id,
            company_id=user.company_id,
        )
        await insert_group_member(
            conn=conn,
            group_id=GROUP_ID_1,
            role_id=ROLE_ID_1,
        )
        await insert_group_member(
            conn=conn,
            group_id=GROUP_ID_1,
            role_id=ROLE_ID_2,
        )
        await insert_group(
            conn=conn,
            name='test2',
            id=GROUP_ID_2,
            created_by=user.role_id,
            company_id=user.company_id,
        )
        await insert_group_member(
            conn=conn,
            group_id=GROUP_ID_2,
            role_id=ROLE_ID_1,
        )
        await insert_group_member(
            conn=conn,
            group_id=GROUP_ID_2,
            role_id=ROLE_ID_3,
        )

    await prepare_document_data(app, owner=user, id=DOCUMENT_ID, **document)

    data = {
        'company_id': COMPANY_ID,
        'document_id': DOCUMENT_ID,
        'template_id': TEST_UUID_1,
    }
    await apply_document_authomation_template(app, data, logger)

    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [DOCUMENT_ID])

        signers = await select_document_signers(
            conn=conn,
            document_id=DOCUMENT_ID,
        )
        review_requests = await select_review_requests(
            conn=conn, company_id=company_id, document_ids=[DOCUMENT_ID]
        )
        parameters = await select_document_parameters(
            conn=conn, company_id=COMPANY_ID, document_id=DOCUMENT_ID
        )
        tags = await select_tags_by_documents(conn=conn, documents_ids=[DOCUMENT_ID])

    result_listings = [
        {'role_id': item.role_id, 'sources': item.sources}
        for item in sorted(listings, key=lambda item: item.role_id)
    ]
    expected_listings = sorted(expected_listings, key=lambda i: i['role_id'])
    assert result_listings == expected_listings, result_listings

    result_signers = [
        {
            'role_id': item.role_id,
            'order': item.order,
            'group_id': item.group_id,
            'assigner': item.assigner,
        }
        for item in sorted(signers, key=lambda item: item.role_id or item.group_id)
    ]
    expected_signers = sorted(expected_signers, key=lambda i: i['role_id'] or i['group_id'])
    assert result_signers == expected_signers

    result_reviews = [
        {'to_role_id': item.to_role_id, 'order': item.order, 'from_role_id': item.from_role_id}
        for item in sorted(review_requests, key=lambda item: item.to_role_id)
    ]
    expected_reviewers = sorted(expected_reviewers, key=lambda i: i['to_role_id'])
    assert result_reviews == expected_reviewers

    expected_parameters = sorted(expected_parameters, key=lambda p: p['field_id'])
    result_parameters = [
        {
            'field_id': i.field_id,
            'value': i.value,
            'is_required': i.is_required,
            'updated_by': i.updated_by,
        }
        for i in sorted(parameters, key=lambda p: p.field_id)
    ]

    assert expected_parameters == result_parameters

    expected_tags = sorted(expected_tags)
    result_tags = [i.name for i in sorted(tags, key=lambda p: p.name)]

    assert expected_tags == result_tags

    sent_emails = [
        {
            'subject': mail['Subject'],
            'to': mail['To'],
        }
        for mail in mailbox
    ]
    assert assert_list_any_order(sent_emails, expected_emails)


@pytest.mark.parametrize(
    'template, expected_reviewers',
    [
        pytest.param(
            {
                'set_review': True,
                'review_settings': {
                    'reviewers_ids': [ROLE_ID_1, ROLE_ID_2, ROLE_ID_3],
                    'is_ordered': True,
                    'is_required': False,
                },
                'assigned_to': ROLE_ID_1,
            },
            [
                {'to_role_id': ROLE_ID_1, 'from_role_id': ROLE_ID_1, 'order': 1},
                {'to_role_id': ROLE_ID_2, 'from_role_id': ROLE_ID_1, 'order': 2},
                {'to_role_id': ROLE_ID_3, 'from_role_id': ROLE_ID_1, 'order': 3},
            ],
            id="Template 'assigned_to' role is first reviewer",
        ),
        pytest.param(
            {
                'set_review': True,
                'review_settings': {
                    'reviewers_ids': [ROLE_ID_2, ROLE_ID_1, ROLE_ID_3],
                    'is_ordered': True,
                    'is_required': False,
                },
                'assigned_to': ROLE_ID_1,
            },
            [
                {'to_role_id': ROLE_ID_2, 'from_role_id': ROLE_ID_1, 'order': 1},
                {'to_role_id': ROLE_ID_1, 'from_role_id': ROLE_ID_1, 'order': 2},
                {'to_role_id': ROLE_ID_3, 'from_role_id': ROLE_ID_1, 'order': 3},
            ],
            id="Template 'assigned_to' role is second reviewer",
        ),
    ],
)
async def test_automation_review_notifications_ordered(
    aiohttp_client,
    mailbox,
    template,
    expected_reviewers,
):
    """
    Check that the role which is assigned both as 'assigned_to' and reviewer
    receives review notification in case of ordered reviews.
    """
    app, client = await prepare_app_client(aiohttp_client)
    await prepare_company_data(app, id=COMPANY_ID)

    initiator = await prepare_user_data(
        app, email=USER_EMAIL_1, role_id=ROLE_ID_1, company_id=COMPANY_ID
    )
    user2 = await prepare_user_data(
        app, email=USER_EMAIL_2, role_id=ROLE_ID_2, company_id=COMPANY_ID
    )
    user3 = await prepare_user_data(
        app, email=USER_EMAIL_3, role_id=ROLE_ID_3, company_id=COMPANY_ID
    )
    users = [initiator, user2, user3]

    await insert_values(
        app=app,
        table=document_automation_template_table,
        id=TEST_UUID_1,
        company_id=COMPANY_ID,
        name='TEST_1',
        is_active=True,
        created_by=ROLE_ID_1,
        **template,
    )

    document = await prepare_document_data(app, owner=initiator, id=DOCUMENT_ID)

    data = {
        'company_id': COMPANY_ID,
        'document_id': DOCUMENT_ID,
        'template_id': TEST_UUID_1,
    }
    await apply_document_authomation_template(app, data, logger)

    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(
            conn=conn,
            company_id=COMPANY_ID,
            document_ids=[DOCUMENT_ID],
            sort_by_order=True,
        )

    result_reviewers = [
        {
            'to_role_id': review_request.to_role_id,
            'from_role_id': review_request.from_role_id,
            'order': review_request.order,
        }
        for review_request in review_requests
    ]
    expected_reviewers = sorted(expected_reviewers, key=lambda i: i['order'])
    assert result_reviewers == expected_reviewers

    for expected_reviewer in expected_reviewers:
        assert len(mailbox) == 1
        assert mailbox[0]['subject'] == 'Ви отримали документ на погодження'
        # Match reviewer by received notification email
        email = mailbox[0]['To']
        reviewer = next((user for user in users if user.email == email), None)
        assert reviewer, f'User with email {email} not found'

        assert expected_reviewer['to_role_id'] == reviewer.role_id
        mailbox.clear()

        await prepare_review(
            client=client,
            user=reviewer,
            document=document,
            review_type=ReviewType.approve,
        )
