import logging

from aiohttp import web

from api.errors import Error
from app.auth.db import select_company_by_edrpou, select_company_by_id, select_user
from app.document_automation.db import (
    select_active_document_automations,
    select_document_automation_template,
)
from app.document_automation.utils import prepare_update_data_from_template
from app.documents.db import (
    exists_document,
    select_documents_by_ids,
)
from app.documents.tables import document_table
from app.documents.utils import handle_document_update
from app.es.utils import send_to_indexator
from app.events import document_actions
from app.lib.enums import SignersSource, Source
from app.lib.types import DataDict
from app.reviews.enums import ReviewRequestSource
from app.tags.db import (
    select_tags_by_document_templates_ids,
)
from worker import topics
from worker.document_automation.utils import (
    get_automations_scores,
    prepare_document_context,
)

APPLY_AUTOMATION_BUCKET_SIZE = 20


async def start_document_automation(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    The first part of the three-step task that automatically applies a document
    template based on user predefined conditions. This part prepares the document
    and sends it to the second part.
    """
    document_id: str = data['document_id']
    company_edrpou: str = data['company_edrpou']
    source: Source | None = data.get('source')

    async with app['db'].acquire() as conn:
        company = await select_company_by_edrpou(conn, company_edrpou)
        if not company:
            return

        context = await prepare_document_context(
            conn=conn, document_id=document_id, company_edrpou=company_edrpou
        )
        if not context:
            return

    await app['kafka'].send_record(
        topic=topics.FIND_DOCUMENT_AUTOMATION,
        value={
            'context': context,
            'company_id': company.id,
            'document_id': document_id,
            'source': source,
        },
    )


async def find_document_automation(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    The second part of the three-step job of document template automation. In this
    part we try to find document template for applying based on conditions.
    """
    context: DataDict = data['context']
    company_id: str = data['company_id']
    document_id: str = data['document_id']
    iteration: int = data.get('iteration', 0)
    source: Source | None = data.get('source')

    max_score: int = data.get('max_score', 0)
    template_id: str | None = data.get('template_id')

    async with app['db'].acquire() as conn:
        # a document may be deleted between steps, so we need to check it again
        document_still_exists = await exists_document(conn, document_id=document_id)
        if not document_still_exists:
            logger.info('Document for automation not found [3]')
            return

        company = await select_company_by_id(conn, company_id)
        if not company:
            logger.info('Company for automation not found [3]')
            return

        context['template_owned_by_edrpou'] = company.edrpou

        automations = await select_active_document_automations(
            conn=conn,
            company_id=company_id,
            limit=APPLY_AUTOMATION_BUCKET_SIZE,
            offset=APPLY_AUTOMATION_BUCKET_SIZE * iteration,
        )
    if not len(automations):
        if template_id is None:
            logger.info(
                'Automation to apply not found',
                extra={
                    'document_id': document_id,
                    'company_id': company_id,
                },
            )
            return
        # max_score was found. We finished this step.
        # Send template_id to next step
        await app['kafka'].send_record(
            topic=topics.APPLY_DOCUMENT_AUTOMATION_TEMPLATE,
            value={
                'document_id': document_id,
                'template_id': template_id,
                'company_id': company_id,
                'source': source,
            },
        )
        return

    # Select template from automation with highest score
    async for item in get_automations_scores(
        automations=automations,
        context=context,
    ):
        if item.score > max_score:
            template_id = item.automation.template_id
            max_score = item.score

    # Increase iteration and retry job
    await app['kafka'].send_record(
        topic=topics.FIND_DOCUMENT_AUTOMATION,
        value={
            **data,
            'max_score': max_score,
            'template_id': template_id,
            'iteration': iteration + 1,
        },
    )


async def apply_document_authomation_template(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    The last part of the three-step job of document template automation.
    This part performs update of given document based on template settings
    """
    document_id: str = data['document_id']
    template_id: str = data['template_id']
    source: Source = data.get('source', None)

    async with app['db'].acquire() as conn:
        template = await select_document_automation_template(conn, template_id=template_id)
        if not template:
            logging.info(
                msg='Template could not be applied, because it does not exists',
                extra={
                    'template_id': template_id,
                    'document_id': document_id,
                },
            )
            return

        tags = await select_tags_by_document_templates_ids(conn, [template_id])
        update_data = prepare_update_data_from_template(
            template=template,
            set_tags_ids=[tag.id for tag in tags],
        )
        update_data['document_id'] = document_id

        # Template creator will be initiator of document update
        role_id = template.assigned_to or template.created_by
        user = await select_user(conn, role_id=role_id)
        if not user:
            logger.info(
                msg='Template could not be applied, because assigned user does not exists',
                extra={
                    'company_id': template.company_id,
                    'created_by': template.created_by,
                    'document_id': document_id,
                    'role_id': template.created_by,
                },
            )
            return

        logging.info(
            msg='Automatic document update',
            extra={
                'update_data': update_data,
                'user_role_id': user.role_id,
                'user_email': user.email,
                'user_company_edrpou': user.company_edrpou,
                'document_id': document_id,
                'template_id': template_id,
            },
        )
        try:
            await handle_document_update(
                conn=conn,
                user=user,
                data=update_data,
                request_source=source,
                signers_source=SignersSource.template,
                reviewers_source=ReviewRequestSource.template,
            )
        except Error as e:
            logger.warning(
                msg='Document update by template was failed',
                extra={
                    'document_id': document_id,
                    'reason': e.reason,
                    'details': e.details,
                    'code': e.code,
                    'status': e.status,
                },
                exc_info=True,
            )
        else:
            logging.info(
                msg='Document update by template is done',
                extra={
                    'document_id': document_id,
                },
            )

            await send_to_indexator(app['redis'], [document_id], to_slow_queue=True)

            # log document template application
            documents = await select_documents_by_ids(
                conn,
                ids=[document_id],
                selectable=[
                    document_table.c.id,
                    document_table.c.title,
                    document_table.c.edrpou_owner,
                ],
            )
            document = documents[0]

            await document_actions.add_document_action(
                document_action=document_actions.DocumentAction(
                    action=document_actions.Action.template_apply_automatically,
                    document_id=document.id,
                    document_edrpou_owner=document.edrpou_owner,
                    document_title=document.title,
                    company_id=user.company_id,
                    company_edrpou=user.company_edrpou,
                    email=user.email,
                    role_id=user.role_id,
                    extra={'template_name': template.name},
                )
            )
