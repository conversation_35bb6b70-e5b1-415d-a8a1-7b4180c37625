import base64
import io
import logging
import re
from collections.abc import Sequence
from typing import (
    assert_never,
)

import sqlalchemy as sa
import ujson
from aiohttp import ClientTimeout, web
from aiohttp.web_request import FileField
from multidict import CIMultiDict, CIMultiDictProxy, MultiDict

from api.errors import DoesNotExist, InvalidRequest, Object
from app.analytics.db import select_analytics_event_for_bill_id
from app.auth.db import (
    is_document_expiring_event_sent,
    update_document_expiring_event,
)
from app.auth.enums import EmployeePositions, RoleStatus
from app.auth.tables import (
    company_table,
    is_active_filter,
    role_table,
    user_role_join,
    user_table,
)
from app.auth.types import User
from app.billing.constants import RATES_NAME_MAP
from app.billing.db import (
    COMPANY_RATE_COLUMNS,
    EMPTY_CLIENT_ACCOUNTS_CLAUSE,
    EXPIRED_BONUS_ACCOUNTS_CLAUSE,
    company_rate_join,
    insert_payment_transaction,
    select_accounts,
    select_bank_transactions_ids,
    select_bill_by_seqnum,
    select_bills_by_edrpous,
    update_bank_transaction,
)
from app.billing.enums import (
    AccountRate,
    AccountType,
    BillPaymentSource,
    BillServicesType,
    BillServiceType,
    CompanyRateStatus,
)
from app.billing.tables import (
    billing_account_table,
)
from app.billing.types import (
    Account,
    BankTransaction,
    Bill,
    BillPaymentStatus,
    CompanyRate,
)
from app.billing.utils import (
    get_bill_total,
)
from app.crm.utils import send_bill_to_crm
from app.documents.utils import send_document
from app.esputnik.enums import Event
from app.i18n import _
from app.lib import money
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import SignatureFormat, Source, UserRole
from app.lib.helpers import to_json
from app.lib.regexp import individual_company_re
from app.lib.types import DataDict, MultipartFormData
from app.models import (
    exists,
    select_all,
    select_one,
)
from app.services import services
from app.signatures import utils as signatures_utils
from app.signatures.enums import SignatureSource
from app.signatures.utils import add_signature_schedule_async_jobs
from app.uploads.types import File
from app.uploads.utils import process_upload
from worker import topics
from worker.billing.tests.constants import ESPUTNIK_BILLING_MAPPING_TTL

logger = logging.getLogger(__name__)
DELETE_ACCOUNTS_BUCKET_SIZE = 20
DEFAULT_SIGN_TIMEOUT = 10

BILL_SEQNUM_EXPRESSION = re.compile(r'ВЧ\s?[-_]?\s?[0-9]+', re.IGNORECASE)


async def filter_already_fetched_transactions(
    transactions: Sequence[BankTransaction],
) -> Sequence[BankTransaction]:
    """Filters already fetched transactions from a new transactions"""

    async with services.db.acquire() as conn:
        processed_transaction_ids = await select_bank_transactions_ids(
            conn=conn,
            transaction_ids=[t.transaction_id for t in transactions],
        )

    return [t for t in transactions if t.transaction_id not in processed_transaction_ids]


def get_bill_seqnum_from_line(line: str | None) -> str | None:
    """Searching for a bill seqnum in a string"""

    if not line or not isinstance(line, str):
        logger.info('Bill seqnum line is empty or wrong type')
        return None

    result = re.search(BILL_SEQNUM_EXPRESSION, line)
    if not result:
        logger.info('Bill seqnum is not found in description')
        return None

    return str(int(''.join([i for i in result.group() if i.isdigit()])))


def parse_bill_from_seqnum_in_transaction(transaction: BankTransaction) -> str | None:
    """
    Parsing bill seqnum from transaction descrtiption
    Bill seqnum sample: `ВЧ-00011`
    """
    bill_seqnum = get_bill_seqnum_from_line(transaction.transaction_description)
    if bill_seqnum is None:
        reason = 'Seqnum is not found in description'
        logger.info(
            reason,
            extra={
                'transaction': transaction.to_log_extra(),
                'description': transaction.transaction_description,
            },
        )
        return None

    logger.info(
        'Bill seqnum found in description',
        extra={
            'transaction': transaction.to_log_extra(),
            'bill_seqnum': bill_seqnum,
        },
    )
    return bill_seqnum


async def validate_bill_exists_by_seqnum_from_transaction(
    conn: DBConnection, transaction: BankTransaction
) -> Bill | None:
    """
    Validates if a bill exists in the database with this seqnum.
    Additionally, logs details in the database.
    """
    seqnum = parse_bill_from_seqnum_in_transaction(transaction=transaction)
    if not seqnum:
        return await handle_seqnum_not_found_case(conn, transaction)

    return await handle_seqnum_found_case(conn, transaction, seqnum)


async def handle_seqnum_found_case(
    conn: DBConnection, transaction: BankTransaction, seqnum: str
) -> Bill:
    bill = await select_bill_by_seqnum(conn, seqnum)
    if not bill:
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Bill seqnum is not found in database'),
        )

    logger.info(
        'Bill was found in database by seqnum',
        extra={'bill_id': bill.id_, 'seqnum': seqnum, 'transaction': transaction.to_log_extra()},
    )
    return bill


async def handle_seqnum_not_found_case(conn: DBConnection, transaction: BankTransaction) -> Bill:
    """
    Sometimes clients can make payment without writing a bill number in the description.
    In this case, we try to find a bill with the same total amount as the amount of the
    transaction.

    JIRA: https://tabula-rasa.atlassian.net/browse/DOC-6302
    """
    if not transaction.sender_edrpou:
        raise InvalidRequest(
            obj=Object,
            reason=_('Transaction does not contain customer account number and edrpou'),
        )

    bills = await select_bills_by_edrpous(
        conn, [transaction.sender_edrpou], exclude_status=BillPaymentStatus.completed
    )

    transaction_amount = money.to_subunits_decimal(transaction.amount)  # в копійках
    target_bills = []
    for bill in bills:
        bill_price = await get_bill_total(bill)  # в гривнях
        total_price = money.to_subunits_decimal_from_units(bill_price)  # в копійках

        is_equal_amount = total_price == transaction_amount
        is_rates_actual = all(not s.rate.is_deprecated for s in bill.rate_services)
        if is_equal_amount and is_rates_actual:
            target_bills.append(bill)

        logger.info(
            'Bill compare info',
            extra={
                'transaction_id': transaction.transaction_id,
                'transaction_amount': transaction_amount,
                'bill_id': bill.id_,
                'bill_price': bill_price,
                'bill_rates': [service.rate.value for service in bill.rate_services],
                'is_equal_amount': is_equal_amount,
                'is_any_rate_deprecated': is_rates_actual,
            },
        )

    if not target_bills:
        raise InvalidRequest(
            obj=Object.bill,
            reason=_('Company does not have unpaid bills for this amount'),
        )

    if len(target_bills) == 1:
        logger.info(
            'Appropriate unpaid bill was found in database by edrpou and amount',
            extra={'transaction': transaction.to_log_extra(), 'bill_id': target_bills[0].id_},
        )
        return target_bills[0]

    # Check that all bills have the same services
    bill_services_keys: set[tuple[str, ...]] = set()
    for bill in target_bills:
        service_keys: list[str] = []  # ex: ["rate_integration", "units"]
        for service in bill.services:
            if service.type == BillServiceType.rate:
                service_keys.append(f'rate_{service.rate.value}')
            elif service.type == BillServiceType.extension:
                service_keys.append(f'extension_{service.extension.value}')
            elif service.type == BillServiceType.units:
                service_keys.append('units')
            else:
                assert_never(service.rate)
        bill_services_keys.add(tuple(sorted(service_keys)))

    if len(bill_services_keys) == 1:
        logger.info(
            'Appropriate unpaid bill was found in database by edrpou and amount',
            extra={'transaction': transaction.to_log_extra(), 'bill_id': target_bills[0].id_},
        )
        return target_bills[0]

    raise InvalidRequest(
        obj=Object.bill,
        reason=_('Company has multiple unpaid bills with different rates for this amount'),
    )


async def update_transaction_on_fail(
    conn: DBConnection,
    transaction: BankTransaction,
    bill: Bill | None,
    error: InvalidRequest | DoesNotExist,
) -> None:
    logger.info(
        'Fail on bank_transaction bill payment validation',
        extra={
            'reason': error.reason,
            'transaction': transaction.to_log_extra(),
            'bill': bill,
        },
    )
    await update_bank_transaction(
        conn=conn,
        transaction_id=transaction.transaction_id,
        data={'details': str(error.reason)},
    )

    # TODO: investigate and add comment why we send bill to crm only for "add_employee"
    #   and not for other services
    if bill and bill.services_type != BillServicesType.add_employee:
        await send_bill_to_crm(bill_id=bill.id_)


async def update_transaction_on_success(
    conn: DBConnection,
    transaction: BankTransaction,
    bill: Bill,
) -> None:
    await update_bank_transaction(
        conn=conn,
        transaction_id=transaction.transaction_id,
        data={'is_processed': True},
    )

    await insert_payment_transaction(
        conn=conn,
        data={
            'bill_id': bill.id_,
            'company_id': bill.company_id,
            'role_id': bill.role_id,
            'payment_status': BillPaymentStatus.completed,
            'source': BillPaymentSource.transfer,
        },
    )

    analytics = await select_analytics_event_for_bill_id(
        conn=conn,
        bill_id=bill.id,
    )
    if analytics and analytics.data:
        await services.kafka.send_record(
            topic=topics.SEND_GOOGLE_ANALYTICS_EVENT_FOR_BILL_PAYMENT,
            value=analytics.data,
        )

    # Send email about successfull payment
    await services.kafka.send_record(
        topic=topics.SEND_PAYMENT_SUCCESSFULL_EMAIL,
        value={'bill_id': bill.id_},
    )


async def send_bill_to_recipient(
    conn: DBConnection,
    user: User,
    bill: Bill,
    data: DataDict,
    edrpou_upload: str,
    request_source: Source,
) -> None:
    if data.get('is_internal_bill_sending') is None:
        data['is_internal_bill_sending'] = True

    logger.info('Sending bill to recipient', extra={'bill_id': bill.id_})
    await send_document(
        conn=conn,
        user=user,
        company_edrpou=edrpou_upload,
        raw_data=data,
        request_source=request_source,
    )


async def apply_bill_signature_on_web(
    app: web.Application,
    conn: DBConnection,
    user: User,
    bill: Bill,
    sign_b64: str,
    document_id: str,
) -> None:
    """Add base64 encoded .p7s signature to bill on web"""

    logger.info('Applying signature on web', extra={'bill_id': bill.id_})

    ctx = await signatures_utils.add_signature(
        conn=conn,
        user=user,
        data={
            'key': sign_b64,
            'format': SignatureFormat.external_separated.value,
            'source': SignatureSource.api.value,
            'document_id': document_id,
        },
    )
    await add_signature_schedule_async_jobs(
        conn=conn,
        ctx=ctx,
        request_source=Source.api_public,
    )


async def sign_bill_web_signer(bill: Bill, bill_file: bytes) -> str:
    """
    Request web signer to sign bill with vchasno key.
    Returns base64 encoded string with signature bytes.
    """

    config = services.config.signer_web
    if not config:
        raise ValueError('Signer web config is not found')

    logger.info('Signing bill in web signer', extra={'bill_id': bill.id_})
    async with services.http_client.post(
        url='{domain}{endpoint}'.format(
            domain=config.host,
            endpoint='/api/sign-b64',
        ),
        headers={'X-API-KEY': config.secret},
        json={
            'data': base64.b64encode(bill_file).decode(),
            'key_id': config.key_id,
            'signature_type': 'xLong',
            'append_certificate': True,
            'external': True,
        },
        ssl=False,
        timeout=ClientTimeout(total=DEFAULT_SIGN_TIMEOUT),
    ) as response:
        response.raise_for_status()
        signature = await response.read()

    return base64.b64encode(signature).decode()


async def upload_bill_to_web(
    user: User,
    edrpou_upload: str,
    bill: Bill,
    bill_file: bytes,
) -> File:
    """Uploading bill to vchasno web"""

    config = services.config

    amount = None
    if bill_total := await get_bill_total(bill):
        # convert to int, to avoid python floating point error, otherwise,
        # bill which the amount has pennies - will raise validation error in UploadValidator
        amount = int(bill_total * 100)

    params = {
        'edrpou': edrpou_upload,
        'recipient_edrpou': bill.edrpou,
        'date_document': bill.date_created.strftime('%Y%m%d'),
        'title': f'Рахунок на оплату № {bill.number} від {bill.date_created.strftime("%Y%m%d")}',
        'doc_number': bill.number,
        'amount': amount,
        'expected_recipient_signatures': 0,
        'recipient_emails': [bill.email],
        'debug': config.app.debug,
        # document_category: bill - 2
        'category': '2',
    }

    file = FileField(
        name='file',
        filename=f'Invoice_{bill.edrpou}.pdf',
        file=io.BytesIO(bill_file),  # type: ignore
        content_type='application/octet-stream',
        headers=CIMultiDictProxy(CIMultiDict()),
    )

    post_data: MultipartFormData = MultiDict({'file': file})

    logger.info('Uploading bill to web', extra={'bill_id': bill.id_})
    document, _ = await process_upload(
        user=user,
        params=params,
        post_data=post_data,
        company_edrpou=edrpou_upload,
        request_source=Source.api_public,
        header_vendor=None,
        ensure_vendor=False,
        auth_method=None,
    )

    return document[0]


async def select_invalid_accounts(conn: DBConnection, offset: int) -> list[Account]:
    """
    Select billing accounts for deleting

    :param offset: rows offset for windowing results.
    :return: List of accounts for deleting.
    """

    return await select_accounts(
        conn,
        sa.or_(EXPIRED_BONUS_ACCOUNTS_CLAUSE, EMPTY_CLIENT_ACCOUNTS_CLAUSE),
        limit=DELETE_ACCOUNTS_BUCKET_SIZE,
        offset=offset,
    )


def get_double_activated_accounts_query() -> sa.sql.Selectable:
    return (
        sa.select([billing_account_table.c.bill_id, billing_account_table.c.rate])
        .where(
            sa.and_(
                billing_account_table.c.bill_id.isnot(None),
                billing_account_table.c.type == AccountType.client_rate,
                billing_account_table.c.status.in_(
                    [CompanyRateStatus.new, CompanyRateStatus.active]
                ),
            )
        )
        .group_by(billing_account_table.c.bill_id, billing_account_table.c.rate)
        .having(
            sa.and_(
                sa.func.count() > 1,
                sa.func.count(sa.distinct(billing_account_table.c.status)) == 2,
            )
        )
    )


async def select_double_activated_accounts(conn: DBConnection) -> DBRow:
    return await select_one(conn=conn, query=get_double_activated_accounts_query())


async def count_double_activated_accounts(conn: DBConnection) -> DBRow:
    subquery = (get_double_activated_accounts_query()).alias('subquery')
    count_query = sa.select([sa.func.count()]).select_from(subquery)
    return await select_one(conn=conn, query=count_query)


async def select_rate_for_activation(conn: DBConnection) -> CompanyRate | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select(COMPANY_RATE_COLUMNS)
            .select_from(company_rate_join)
            .where(
                sa.and_(
                    billing_account_table.c.status == CompanyRateStatus.new,
                    billing_account_table.c.activation_date <= utc_now(),
                    billing_account_table.c.type == AccountType.client_rate,
                )
            )
        ),
    )
    return CompanyRate.from_db(row) if row else None


async def select_rate_for_deactivation(conn: DBConnection) -> CompanyRate | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select(COMPANY_RATE_COLUMNS)
            .select_from(company_rate_join)
            .where(
                sa.and_(
                    billing_account_table.c.status == CompanyRateStatus.active,
                    billing_account_table.c.date_expired <= utc_now(),
                    billing_account_table.c.type == AccountType.client_rate,
                )
            )
            .limit(1)
        ),
    )
    return CompanyRate.from_db(row) if row else None


async def select_users_emails_for_esputnik(
    *,
    conn: DBConnection,
    company_id: str,
    role: UserRole | None = None,
) -> list[DBRow]:
    filters = [
        is_active_filter,
        role_table.c.company_id == company_id,
    ]

    if role:
        filters.append(role_table.c.user_role == role.value)

    return await select_all(
        conn,
        sa.select(
            [
                user_table.c.email.label('email'),
            ]
        )
        .select_from(user_role_join)
        .where(sa.and_(*filters)),
    )


async def select_admins_and_positions_by_company_id_for_notifications(
    conn: DBConnection, company_id: str
) -> list[DBRow]:
    return await select_all(
        conn,
        sa.select(
            [
                user_table.c.email.label('email'),
            ]
        )
        .select_from(user_role_join)
        .where(
            sa.and_(
                role_table.c.company_id == company_id,
                role_table.c.status == RoleStatus.active,
                sa.or_(
                    role_table.c.user_role == UserRole.admin.value,
                    role_table.c.position.in_(
                        EmployeePositions.esputnik_tov_notification_positions()
                    ),
                ),
            )
        ),
    )


async def is_company_has_another_paid_rate(conn: DBConnection, company_id: str) -> bool:
    interval_31_days = sa.text("now() + interval '31' day")

    clause = sa.and_(
        billing_account_table.c.company_id == company_id,
        ~billing_account_table.c.rate.in_(AccountRate.free_rates()),
        billing_account_table.c.date_expired >= interval_31_days,
    )
    return await exists(conn=conn, select_from=billing_account_table, clause=clause)


async def select_billing_accounts_expiring_soon_without_next_paid(
    conn: DBConnection, offset: int | None = None, limit: int | None = None
) -> list[DBRow]:
    def _get_interval(days: int) -> sa.text:
        return sa.text(f"now()::date + interval '{days} {'day' if days == 1 else 'days'}'")

    date_expired = billing_account_table.c.date_expired.cast(sa.Date)

    has_another_paid_web_rate_query = sa.select([billing_account_table.c.company_id]).where(
        sa.and_(
            ~billing_account_table.c.rate.in_(AccountRate.free_rates()),
            ~billing_account_table.c.rate.in_(AccountRate.integration_rates()),
            date_expired >= _get_interval(31),
            billing_account_table.c.status.in_([CompanyRateStatus.active, CompanyRateStatus.new]),
        )
    )
    has_another_paid_integration_rate_query = sa.select([billing_account_table.c.company_id]).where(
        sa.and_(
            billing_account_table.c.rate.in_(AccountRate.integration_rates()),
            date_expired >= _get_interval(31),
            billing_account_table.c.status.in_([CompanyRateStatus.active, CompanyRateStatus.new]),
        )
    )

    web_query = sa.select([billing_account_table]).where(
        sa.and_(
            ~billing_account_table.c.company_id.in_(has_another_paid_web_rate_query),
            sa.or_(
                date_expired == _get_interval(5),
                date_expired == _get_interval(14),
                date_expired == _get_interval(30),
            ),
            ~billing_account_table.c.rate.in_(AccountRate.free_rates()),
            ~billing_account_table.c.rate.in_(AccountRate.integration_rates()),
            billing_account_table.c.status == CompanyRateStatus.active,
        )
    )

    integration_query = sa.select([billing_account_table]).where(
        sa.and_(
            ~billing_account_table.c.company_id.in_(has_another_paid_integration_rate_query),
            sa.or_(
                date_expired == _get_interval(5),
                date_expired == _get_interval(14),
                date_expired == _get_interval(30),
            ),
            ~billing_account_table.c.rate.in_(AccountRate.free_rates()),
            billing_account_table.c.rate.in_(AccountRate.integration_rates()),
            billing_account_table.c.status == CompanyRateStatus.active,
        )
    )

    query = sa.union(web_query, integration_query).order_by(
        'date_expired',
        'date_created',
    )

    if limit is not None:
        query = query.limit(limit)
    if offset is not None:
        query = query.offset(offset)

    return await select_all(conn, query)


async def select_tov_trial_billing_accounts_expiring_soon_without_next_paid(
    conn: DBConnection, offset: int | None = None, limit: int | None = None
) -> list[DBRow]:
    def _get_interval_positive(days: int) -> sa.text:
        return sa.text(f"now()::date + interval '{days} {'day' if days == 1 else 'days'}'")

    def _get_interval_negative(days: int) -> sa.text:
        return sa.text(f"now()::date - interval '{days} {'day' if days == 1 else 'days'}'")

    date_expired = billing_account_table.c.date_expired.cast(sa.Date)
    has_another_paid_rate_query = sa.select([billing_account_table.c.company_id]).where(
        sa.and_(
            ~billing_account_table.c.rate.in_([*AccountRate.free_rates()]),
            billing_account_table.c.status.in_([CompanyRateStatus.new, CompanyRateStatus.active]),
            date_expired >= _get_interval_positive(31),
        )
    )

    query = (
        sa.select([billing_account_table, company_table.c.edrpou])
        .select_from(
            billing_account_table.join(
                company_table,
                company_table.c.id == billing_account_table.c.company_id,
            ),
        )
        .where(
            sa.and_(
                ~billing_account_table.c.company_id.in_(has_another_paid_rate_query),
                billing_account_table.c.rate.in_(AccountRate.trials()),
                sa.or_(
                    date_expired == _get_interval_positive(3),
                    date_expired == _get_interval_positive(1),
                    date_expired == _get_interval_negative(1),
                ),
            ),
        )
        .order_by(
            billing_account_table.c.date_expired,
            billing_account_table.c.date_created,
        )
    )

    if limit is not None:
        query = query.limit(limit)
    if offset is not None:
        query = query.offset(offset)

    raw_data = await select_all(conn, query)

    # TODO: When update to SQLAlchemy 2.0 change this logic to regexp_match function
    return [row for row in raw_data if not bool(individual_company_re.match(row.edrpou))]


async def send_event(
    logger: logging.Logger,
    company_id: str,
    billing_account_id: str,
    event: Event,
    account: Account,
) -> None:
    """
    Check if we sent this type of event.
    If not - send and store in db that we sent.
    """
    async with services.db.acquire() as conn:
        is_already_sent = await is_document_expiring_event_sent(
            conn=conn,
            company_id=company_id,
            latest_billing_account_id=billing_account_id,
            event=event,
        )

        if is_already_sent:
            logger.info(
                'Event is already sent',
                extra={
                    'event': event,
                    'company_id': company_id,
                    'latest_billing_account_id': billing_account_id,
                },
            )
            return

        await update_document_expiring_event(
            conn=conn,
            company_id=company_id,
            latest_billing_account_id=billing_account_id,
            event=event,
        )

    await services.kafka.send_record(
        topics.ESPUTNIK_SEND_EVENT_TO_USERS,
        {
            'event': event,
            'company_id': company_id,
            'extra_params': {'rate': RATES_NAME_MAP[account.rate]},
        },
    )


def billing_mapping_key(key: str) -> str:
    """Generate redis key for billing mapping."""
    return f'esputnik_ba:{key}'


async def set_billing_mapping(key: str, value: dict[str, str]) -> None:
    """Set billing mapping for esputnik."""
    redis_key = billing_mapping_key(key)

    await services.redis.setex(redis_key, value=to_json(value), time=ESPUTNIK_BILLING_MAPPING_TTL)


async def get_billing_mapping(key: str) -> dict[str, str]:
    """Get billing mapping for esputnik."""
    billing_key = billing_mapping_key(key)

    value = await services.redis.get(billing_key)
    if not value:
        return {}

    await services.redis.delete(billing_key)

    return ujson.loads(value)
