import logging
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from unittest.mock import AsyncMock

import pytest

from app.auth.enums import SystemAccountEmail
from app.billing.db import (
    select_active_company_rates,
    select_planned_company_rates,
)
from app.billing.enums import (
    AccountRate,
    AccountStatus,
    AccountType,
    BillServicesType,
    BillStatus,
)
from app.billing.tests.utils import get_bill
from app.billing.types import AddBillServiceRateOptions
from app.billing.utils import get_bill_total
from app.document_categories.types import PublicDocumentCategory
from app.documents.enums import FirstSignBy
from app.documents.tests.utils import get_listings
from app.flags import FeatureFlags
from app.lib import eusign_utils
from app.lib.datetime_utils import local_now
from app.lib.enums import DocumentStatus, SignatureType
from app.lib.helpers import to_base64str
from app.services import services
from app.signatures.tests.test_signatures_views import fake_get_cert_info
from app.signatures.tests.utils import get_signatures
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    TEST_USER_EMAIL,
    get_document,
    prepare_bill,
    prepare_billing_account,
    prepare_client,
    prepare_public_document_categories,
    prepare_signature_info,
    prepare_user_data,
    set_company_config,
)
from worker.billing.jobs import (
    DEV_COMPANY_EDRPOU,
    delete_double_activated_billing_accounts,
    process_bill,
)

logger = logging.getLogger(__name__)

COMPANY_ID_1 = '********-0000-0000-0000-************'


@pytest.mark.parametrize(
    'bill_data',
    [
        pytest.param(
            {
                'email': TEST_USER_EMAIL,
                'edrpou': TEST_COMPANY_EDRPOU,
                'company_id': COMPANY_ID_1,
                'services_type': BillServicesType.rate,
                'services': [
                    AddBillServiceRateOptions(
                        units=1,
                        unit_price=Decimal('100'),
                        rate=AccountRate.latest_pro(),
                        date_from=None,
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
            },
            id='latest_pro',
        ),
        pytest.param(
            {
                'email': TEST_USER_EMAIL,
                'edrpou': TEST_COMPANY_EDRPOU,
                'company_id': COMPANY_ID_1,
                'services_type': BillServicesType.rate,
                'services': [
                    AddBillServiceRateOptions(
                        units=1,
                        unit_price=Decimal('100'),
                        rate=AccountRate.latest_integration(),
                        date_from=None,
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
            },
            id='latest_integration',
        ),
        pytest.param(
            {
                'email': TEST_USER_EMAIL,
                'edrpou': TEST_COMPANY_EDRPOU,
                'company_id': COMPANY_ID_1,
                'services_type': BillServicesType.rate,
                'services': [
                    AddBillServiceRateOptions(
                        units=1,
                        unit_price=Decimal('100'),
                        rate=AccountRate.archive_small,
                        date_from=None,
                        limits_employees_count=None,
                        price_per_user=None,
                    )
                ],
            },
            id='latest_archive_small',
        ),
    ],
)
async def test_process_bill(
    aiohttp_client,
    test_flags,
    mailbox,
    monkeypatch,
    bill_data: dict,
):
    """
    Happy path test for processing a bill:
     - bill is created & uploaded
     - bill is signed by the admin company
     - bill is sent to the user
    """
    test_flags[FeatureFlags.ENABLE_BILL_SIGNING.value] = True
    app, client, system_user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=SystemAccountEmail.billing.value,
        company_edrpou=DEV_COMPANY_EDRPOU,
        enable_pro_functionality=True,
        create_billing_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )
    await prepare_public_document_categories(3)

    # target user
    await prepare_user_data(
        app=app,
        email=TEST_USER_EMAIL,
        company_edrpou=TEST_COMPANY_EDRPOU,
        company_id=COMPANY_ID_1,
        enable_pro_functionality=False,
    )

    # URL2PDF is not available in tests
    async_mock = AsyncMock(return_value=b'some_pdf')
    monkeypatch.setattr('worker.billing.jobs.bill_to_pdf', async_mock)

    # "SignerWeb" is not available in tests
    async_mock = AsyncMock(return_value=to_base64str(b'some_signature'))
    monkeypatch.setattr('worker.billing.jobs.sign_bill_web_signer', async_mock)

    # Currently it's not possible to build a valid signature in tests
    sign = prepare_signature_info(SignatureType.signature, DEV_COMPANY_EDRPOU)
    monkeypatch.setattr(eusign_utils, 'verify_sync', lambda *_, **__: sign)
    monkeypatch.setattr(eusign_utils, 'get_cert_info', fake_get_cert_info)

    async with services.db.acquire() as conn:
        bill = await prepare_bill(
            conn=conn,
            **bill_data,
        )

    data = {'bill_id': bill.id, 'is_card_payment': False}
    await process_bill(app=app, data=data, logger=logger)

    assert len(mailbox) == 2
    assert len(mailbox.by_email(TEST_USER_EMAIL)) == 1
    assert len(mailbox.by_email(services.config.app.sales_email)) == 1

    bill = await get_bill(bill_id=bill.id)
    assert bill.status == BillStatus.signed_and_sent
    assert bill.document_id is not None

    bill_amount = await get_bill_total(bill=bill)
    document = await get_document(document_id=bill.document_id)
    assert document.status == DocumentStatus.finished
    assert document.first_sign_by == FirstSignBy.owner
    assert document.expected_owner_signatures == 1
    assert document.expected_recipient_signatures == 0
    assert document.edrpou_owner == DEV_COMPANY_EDRPOU
    assert document.edrpou_recipient == TEST_COMPANY_EDRPOU
    assert document.amount == int(bill_amount * 100)
    assert document.number == bill.number
    assert document.title.startswith(f'Рахунок на оплату № {bill.number} від ')
    assert document.category == PublicDocumentCategory.bill.value
    assert document.date_document.date() == bill.date_created.date()

    signatures = await get_signatures(document_id=document.id)
    assert len(signatures) == 1
    signature = signatures[0]
    assert signature.owner_edrpou == DEV_COMPANY_EDRPOU

    listings = await get_listings(document_id=document.id)
    assert len(listings) == 2
    assert {item.access_edrpou for item in listings} == {
        DEV_COMPANY_EDRPOU,
        TEST_COMPANY_EDRPOU,
    }


async def test_process_bill_is_card_payment(aiohttp_client, test_flags, mailbox):
    """
    When `is_card_payment` is True, the bill should be marked as skipped and nothing
    else should be done (no document should be created, no email should be sent).
    """
    test_flags[FeatureFlags.ENABLE_BILL_SIGNING.value] = True

    app, client, system_user = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=SystemAccountEmail.billing.value,
        company_edrpou=DEV_COMPANY_EDRPOU,
        enable_pro_functionality=True,
        create_billing_account=True,
        feature_flags={'pass_sign_info_to_backend': False},
    )

    user = await prepare_user_data(
        app=app,
        email=TEST_USER_EMAIL,
        company_edrpou=TEST_COMPANY_EDRPOU,
        enable_pro_functionality=False,
    )

    async with services.db.acquire() as conn:
        bill = await prepare_bill(
            conn=conn,
            user=user,
            email=user.email,
            edrpou=user.company_edrpou,
            company_id=user.company_id,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=Decimal('100'),
                    rate=AccountRate.latest_pro(),
                    date_from=None,
                    limits_employees_count=None,
                    price_per_user=None,
                )
            ],
        )

    data = {'bill_id': bill.id, 'is_card_payment': True}
    await process_bill(app=app, data=data, logger=logger)

    bill = await get_bill(bill_id=bill.id)
    assert bill.status == BillStatus.skipped
    assert bill.document_id is None

    assert len(mailbox) == 0


async def test_deactivate_double_activated_bill(aiohttp_client, test_flags):
    """
    If a bill is already marked as signed_and_sent, processing it again should
    not change its status or create a new document.
    """

    app, client, superadmin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou='********',
        create_billing_account=True,
        super_admin_permissions={'can_edit_client_data': True},
        email='<EMAIL>',
    )
    await set_company_config(
        app, company_id=superadmin.company_id, master=True, admin_is_superadmin=True
    )

    user = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_COMPANY_EDRPOU,
        enable_pro_functionality=False,
    )

    async with services.db.acquire() as conn:
        bill = await prepare_bill(
            conn=conn,
            user=user,
            email=user.email,
            edrpou=user.company_edrpou,
            company_id=user.company_id,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=Decimal('100'),
                    rate=AccountRate.latest_pro(),
                    date_from=None,
                    limits_employees_count=None,
                    price_per_user=None,
                )
            ],
        )

        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_start(),
            activation_date=local_now() + timedelta(days=-2),
            status=AccountStatus.active,
            type_=AccountType.client_rate,
            bill_id=bill.id,
        )

        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_start(),
            activation_date=local_now() + timedelta(days=2),
            status=AccountStatus.new,
            type_=AccountType.client_rate,
            bill_id=bill.id,
        )

        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_start(),
            activation_date=local_now() + timedelta(days=2),
            status=AccountStatus.new,
            type_=AccountType.client_bonus,
            bill_id=bill.id,
        )

    await delete_double_activated_billing_accounts(app, {'get_count_only': True}, logger)
    # check that run job with get_count_only just count problem accounts
    async with services.db.acquire() as conn:
        planned_rates = await select_planned_company_rates(conn, user.company_id)
        assert len(planned_rates) == 1

    await delete_double_activated_billing_accounts(app, {}, logger)

    async with services.db.acquire() as conn:
        active_rates = await select_active_company_rates(conn, user.company_id)
        assert len(active_rates) == 1
        assert active_rates[0].rate == AccountRate.latest_start()
        planned_rates = await select_planned_company_rates(conn, user.company_id)
        assert len(planned_rates) == 0
