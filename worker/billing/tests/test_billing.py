import datetime
import logging
import uuid
from decimal import Decimal

import pytest

from app.auth.tables import company_meta_table
from app.billing.api import add_company_rate
from app.billing.db import (
    _insert_account,
    select_account_by_id,
    select_company_accounts,
    select_company_transactions,
    select_service_account_id,
)
from app.billing.enums import (
    AccountRate,
    BillServicesType,
    CompanyRateStatus,
)
from app.billing.tables import billing_account_table
from app.billing.tests.constants import (
    TEST_ACCOUNT_CLIENT_BONUS,
    TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
    TEST_ACCOUNT_CLIENT_DEBIT,
)
from app.billing.types import (
    AccountType,
    AddBillServiceRateOptions,
    BillingCompanyConfig,
    CompanyRate,
)
from app.billing.utils import get_billing_company_config, get_rate_end_date_from_start_date
from app.esputnik.enums import Event
from app.flags import FeatureFlags
from app.lib.datetime_utils import soft_isoformat, to_local_datetime, utc_now
from app.lib.enums import UserRole
from app.services import services
from app.tests.common import (
    insert_values,
    prepare_bill,
    prepare_client,
    prepare_user_data,
    select_all_rates,
)
from cron.jobs import call_esputnik_tov_trial_rate_expiring_event, delete_invalid_accounts
from worker.billing.jobs import (
    activate_company_rates,
    create_initial_free_company_rate,
    deactivate_company_rates,
    delete_billing_accounts,
    generate_esputnik_rate_expiring_event,
    on_document_charge_event,
)
from worker.billing.utils import select_invalid_accounts

logger = logging.getLogger(__name__)
FAKE_ID = uuid.uuid4()

TEST_UUID_1 = '7397a003-92ff-470d-93ee-42562e2f1da1'
TEST_UUID_2 = '3e5d0e37-6762-49c3-9397-98a4ac37681f'
TEST_UUID_3 = 'e39427e1-f046-4a96-82fe-7c8a8e7a9db9'
TEST_UUID_4 = '94de4100-8a02-4d7b-adf6-fd6d3bbace97'
TEST_UUID_5 = '8d9a1408-e976-49b2-9452-69b1ccabe780'

COMPANY_EDRPOU_FOP = '**********'
COMPANY_EDRPOU_TOV = '********'


OLD_DATE = datetime.datetime(year=2015, day=1, month=1)
FUTURE_DATE = datetime.datetime(year=3015, day=1, month=1)


async def test_delete_invalid_accounts_already_deleted(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    date_deleted = soft_isoformat(utc_now() - datetime.timedelta(days=1))

    async with app['db'].acquire() as conn:
        # Insert already deleted accounts
        deleted_bonus = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'date_deleted': date_deleted,
            },
        )
        await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units_left': 0,
                'date_deleted': date_deleted,
            },
        )
        await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'date_deleted': date_deleted,
            },
        )
        await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'amount_left': 0,
                'units_left': 0,
                'date_deleted': date_deleted,
            },
        )

        # Check how many accounts will be deleted
        assert len(await select_invalid_accounts(conn, offset=0)) == 0

        # Delete expired accounts
        await delete_invalid_accounts(app)

        # Check company accounts
        accounts = await select_company_accounts(conn, company_id)
        assert len(accounts) == 0

        account = await select_account_by_id(conn, deleted_bonus.id)
        assert account.amount_left == deleted_bonus.amount_left
        assert account.units_left == deleted_bonus.units_left
        assert account.date_expired == deleted_bonus.date_expired
        assert account.date_deleted == deleted_bonus.date_deleted

        # Check transactions
        transactions = await select_company_transactions(conn, company_id)
        assert len(transactions) == 0


async def test_delete_invalid_accounts_empty(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    date_expired = soft_isoformat(utc_now() - datetime.timedelta(days=1))

    async with app['db'].acquire() as conn:
        # Insert empty not deleted accounts
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units_left': 0,
                'date_expired': date_expired,
            },
        )
        bonus_account_id = account.id
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units_left': 0,
            },
        )
        custom_bonus_account_id = account.id
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'amount_left': 0,
                'units_left': 0,
            },
        )
        debit_account_id = account.id

        # Check how many accounts will be deleted
        assert len(await select_invalid_accounts(conn, offset=0)) == 3

        # Delete expired accounts
        await delete_invalid_accounts(app)

        # Check deleted accounts
        accounts = await select_company_accounts(conn, company_id)
        assert len(accounts) == 0

        account = await select_account_by_id(conn, bonus_account_id)
        assert account.amount == 0
        assert account.amount_left == 0
        assert account.units == TEST_ACCOUNT_CLIENT_BONUS['units']
        assert account.units_left == 0

        account = await select_account_by_id(conn, custom_bonus_account_id)
        assert account.amount == 0
        assert account.amount_left == 0
        assert account.units == TEST_ACCOUNT_CLIENT_BONUS_CUSTOM['units']
        assert account.units_left == 0

        account = await select_account_by_id(conn, debit_account_id)
        assert account.amount == TEST_ACCOUNT_CLIENT_DEBIT['amount']
        assert account.amount_left == 0
        assert account.units == TEST_ACCOUNT_CLIENT_DEBIT['units']
        assert account.units_left == 0

        # Check transactions
        transactions = await select_company_transactions(conn, company_id)
        assert len(transactions) == 0

        # Check service accounts
        scb_account_id = await select_service_account_id(conn, AccountType.service_credit_bonus)
        scb_account = await select_account_by_id(conn, scb_account_id)
        assert scb_account.amount == 0
        assert scb_account.amount_left == 0
        assert scb_account.units == 0
        assert scb_account.units_left == 0

        sce_account_id = await select_service_account_id(conn, AccountType.service_credit_external)
        sce_account = await select_account_by_id(conn, sce_account_id)
        assert sce_account.amount == 0
        assert sce_account.amount_left == 0
        assert sce_account.units == 0
        assert sce_account.units_left == 0


async def test_delete_invalid_accounts_expired(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    date_expired = soft_isoformat(utc_now() - datetime.timedelta(days=1))

    async with app['db'].acquire() as conn:
        # Insert expired accounts
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'date_expired': date_expired,
            },
        )
        bonus_account_id = account.id
        account = await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'date_expired': date_expired,
            },
        )
        custom_bonus_account_id = account.id

        # Check how many accounts will be deleted
        assert len(await select_invalid_accounts(conn, offset=0)) == 2

        # Delete expired accounts
        await delete_billing_accounts(app, {}, logger)

        # Check deleted accounts
        accounts = await select_company_accounts(conn, company_id)
        assert len(accounts) == 0

        account = await select_account_by_id(conn, bonus_account_id)
        assert account.amount == 0
        assert account.amount_left == 0
        assert account.units == TEST_ACCOUNT_CLIENT_BONUS['units']
        assert account.units_left == 0

        account = await select_account_by_id(conn, custom_bonus_account_id)
        assert account.amount == 0
        assert account.amount_left == 0
        assert account.units == TEST_ACCOUNT_CLIENT_BONUS_CUSTOM['units']
        assert account.units_left == 0

        # Check transactions
        transactions = await select_company_transactions(conn, company_id)
        assert len(transactions) == 2


async def test_select_invalid_accounts(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    date_expired = soft_isoformat(utc_now() - datetime.timedelta(days=1))

    async with app['db'].acquire() as conn:
        # Empty bonus
        await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units_left': 0,
            },
        )
        # Expired bonus
        await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'date_expired': date_expired,
            },
        )
        # Empty and expired bonus
        await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_BONUS_CUSTOM,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'units_left': 0,
                'date_expired': date_expired,
            },
        )
        # Empty debit
        await _insert_account(
            conn,
            {
                **TEST_ACCOUNT_CLIENT_DEBIT,
                'company_id': company_id,
                'initiator_id': FAKE_ID,
                'amount_left': 0,
                'units_left': 0,
            },
        )

        assert len(await select_invalid_accounts(conn, offset=0)) == 4


@pytest.mark.parametrize(
    'rates, statuses_map, esputnik_count',
    [
        # two updates
        (
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'activation_date': OLD_DATE,
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.new,
                    'amount': 100,
                    'activation_date': OLD_DATE,
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_3,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.new,
                    'amount': 100,
                    'activation_date': OLD_DATE,
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_4,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.canceled,
                    'amount': 100,
                    'activation_date': OLD_DATE,
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_5,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.expired,
                    'amount': 7,
                    'activation_date': OLD_DATE,
                    'type': AccountType.client_rate,
                },
            ],
            {
                TEST_UUID_1: CompanyRateStatus.active,
                TEST_UUID_2: CompanyRateStatus.active,
                TEST_UUID_3: CompanyRateStatus.active,
                TEST_UUID_4: CompanyRateStatus.canceled,
                TEST_UUID_5: CompanyRateStatus.expired,
            },
            1,  # TEST_UUID_2 pro_rate_on => 1
        ),
        # no updates for active rates
        (
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.active,
                    'amount': 10,
                    'activation_date': OLD_DATE,
                    'type': AccountType.client_rate,
                }
            ],
            {TEST_UUID_1: CompanyRateStatus.active},
            0,
        ),
        # no updates for future rates
        (
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.new,
                    'amount': 2000,
                    'activation_date': FUTURE_DATE,
                    'type': AccountType.client_rate,
                }
            ],
            {TEST_UUID_1: CompanyRateStatus.new},
            0,
        ),
    ],
)
async def test_activate_company_rates(
    aiohttp_client, esputnik_box, rates, statuses_map, esputnik_count
):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    for rate in rates:
        await insert_values(
            app=app,
            table=billing_account_table,
            company_id=user.company_id,
            **rate,
        )

    await activate_company_rates(app, {}, logger)

    async with app['db'].acquire() as conn:
        rates = await select_all_rates(conn)

    result_statuses_map = {rate.id: rate.status for rate in rates}
    assert result_statuses_map == statuses_map
    assert len(esputnik_box) == esputnik_count


async def test_send_rate_event_in_crm(aiohttp_client, crm_box):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    async with app['db'].acquire() as conn:
        rate_price = Decimal(1000)
        bill_1 = await prepare_bill(
            conn=conn,
            user=user,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=rate_price,
                    rate=AccountRate.latest_pro(),
                    date_from=OLD_DATE,
                    limits_employees_count=None,
                    price_per_user=None,
                )
            ],
        )

        bill_2 = await prepare_bill(
            conn=conn,
            user=user,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=rate_price,
                    rate=AccountRate.latest_start(),
                    date_from=OLD_DATE,
                    limits_employees_count=None,
                    price_per_user=None,
                )
            ],
        )

        await insert_values(
            app=app,
            table=billing_account_table,
            company_id=user.company_id,
            **{
                'id': TEST_UUID_1,
                'rate': AccountRate.pro,
                'status': CompanyRateStatus.new,
                'amount': 100,
                'activation_date': OLD_DATE,
                'type': AccountType.client_rate,
            },
            bill_id=bill_1.id,
        )

        await insert_values(
            app=app,
            table=billing_account_table,
            company_id=user.company_id,
            **{
                'id': TEST_UUID_2,
                'rate': AccountRate.latest_start(),
                'status': CompanyRateStatus.active,
                'amount': 100,
                'activation_date': OLD_DATE,
                'date_expired': OLD_DATE,
                'type': AccountType.client_rate,
            },
            bill_id=bill_2.id,
        )

    await activate_company_rates(app, {}, logger)
    assert len(crm_box) == 1
    event = crm_box[0]
    assert event['is_active'] is True
    assert event['status'] == CompanyRateStatus.active.value

    crm_box.clear()

    await deactivate_company_rates(app, {}, logger)
    assert len(crm_box) == 1
    event = crm_box[0]
    assert event['is_active'] is False
    assert event['status'] == CompanyRateStatus.expired.value


@pytest.mark.parametrize(
    'rates, statuses_map, esputnik_count, is_disabling',
    [
        # two rates must be deactivated
        (
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'date_expired': OLD_DATE,
                    'type': AccountType.client_rate,
                    'activation_date': OLD_DATE,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 2000,
                    'date_expired': OLD_DATE,
                    'type': AccountType.client_rate,
                    'activation_date': OLD_DATE,
                },
                {
                    'id': TEST_UUID_3,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.new,
                    'amount': 100,
                    'date_expired': OLD_DATE,
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_4,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.canceled,
                    'amount': 100,
                    'date_expired': OLD_DATE,
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_5,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.expired,
                    'amount': 7,
                    'date_expired': OLD_DATE,
                    'date_deleted': OLD_DATE,
                    'type': AccountType.client_rate,
                },
            ],
            {
                TEST_UUID_1: CompanyRateStatus.expired,
                TEST_UUID_2: CompanyRateStatus.expired,
                TEST_UUID_3: CompanyRateStatus.new,
                TEST_UUID_4: CompanyRateStatus.canceled,
                TEST_UUID_5: CompanyRateStatus.expired,
            },
            # TEST_UUID_1 pro_rate_on => 0 + send events that
            # TEST_UUID_1 and TEST_UUID_2 are expired
            3,
            True,
        ),
        # no updates for new rates
        (
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.new,
                    'amount': 200,
                    'date_expired': OLD_DATE,
                    'type': AccountType.client_rate,
                }
            ],
            {TEST_UUID_1: CompanyRateStatus.new},
            0,
            False,
        ),
        # no updates for future rates
        (
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'date_expired': FUTURE_DATE,
                    'type': AccountType.client_rate,
                }
            ],
            {TEST_UUID_1: CompanyRateStatus.active},
            0,
            False,
        ),
    ],
)
async def test_deactivate_company_rates(
    aiohttp_client, esputnik_box, rates, statuses_map, esputnik_count, is_disabling
):
    app, client, user = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_admin=True,
        enable_pro_functionality=False,
    )

    for rate in rates:
        await insert_values(
            app=app, table=billing_account_table, company_id=user.company_id, **rate
        )

    await deactivate_company_rates(app, {}, logger)

    async with app['db'].acquire() as conn:
        rates = await select_all_rates(conn)

    # if there are disabled rates, free rate must be activated
    if is_disabling:
        assert any(r.rate == AccountRate.free for r in rates)

    result_statuses_map = {rate.id: rate.status for rate in rates if rate.rate != AccountRate.free}
    assert result_statuses_map == statuses_map
    for rate in rates:
        if rate.status == CompanyRateStatus.expired:
            assert rate.date_deleted is not None, rate
        else:
            assert rate.date_deleted is None, rate
    assert len(esputnik_box) == esputnik_count


@pytest.mark.parametrize(
    ('rates', 'esputnik_count'),
    (
        pytest.param(
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=32),
                    'type': AccountType.client_rate,
                },
            ],
            0,
            id='case1: company has another paid rate, which do not expire in 30 days',
        ),
        pytest.param(
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_rate,
                },
            ],
            1,
            id='case2: company has does not have another paid rate, which do not expire in 30 days',
        ),
        pytest.param(
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=32),
                    'type': AccountType.client_rate,
                },
            ],
            1,
            id='case3: company has another paid web rate, which do not expire in 30 days'
            ' but integration rate does',
        ),
        pytest.param(
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.canceled,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=32),
                    'type': AccountType.client_rate,
                },
            ],
            1,
            id='case4: company has deleted paid rate, which do not expire in 30 days',
        ),
        pytest.param(
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_rate,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.new,
                    'amount': 7,
                    'date_expired': utc_now().date() + datetime.timedelta(days=365),
                    'type': AccountType.client_rate,
                },
            ],
            0,
            id='case5: company has new paid rate',
        ),
    ),
)
async def test_generate_esputnik_rate_expiring_event(
    aiohttp_client, esputnik_box, rates, esputnik_count
):
    # Arrange
    app, client, user = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_admin=True,
        enable_pro_functionality=False,
    )
    await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        enable_pro_functionality=False,
    )

    for rate in rates:
        await insert_values(
            app=app, table=billing_account_table, company_id=user.company_id, **rate
        )
    # Act
    await generate_esputnik_rate_expiring_event(app, {'offset': 0}, logger)

    # Assert
    assert len(esputnik_box) == esputnik_count
    if esputnik_box:
        assert any(param.get('name') == 'token' for param in esputnik_box[0]['data']['params'])


@pytest.mark.parametrize(
    'second_role, role_position, expected_events',
    [
        (True, 'Директор', 2),  # Notification for admin and 'Директор' position
        (
            True,
            'Фінансовий директор',
            2,
        ),  # Notification for admin and 'Фінансовий директор' position
        (True, 'Бухгалтер', 2),  # Notification for admin and 'Бухгалтер' position
        (True, 'Комерційний директор', 1),  # Notification only for admin
        (False, None, 0),  # Don't send events if TOV with single user
    ],
)
async def test_tov_expire_rate_events(
    aiohttp_client, esputnik_box, second_role, role_position, expected_events
):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)

    if second_role:
        await prepare_user_data(
            app, email='<EMAIL>', user_role=UserRole.user.value, role_position=role_position
        )

    await insert_values(
        app=app,
        date_expired=utc_now().date() + datetime.timedelta(days=3),
        table=billing_account_table,
        company_id=user.company_id,
        rate=AccountRate.pro_plus_trial,
        type=AccountType.client_rate,
    )

    await call_esputnik_tov_trial_rate_expiring_event(app, 0)
    assert len(esputnik_box) == expected_events


@pytest.mark.parametrize(
    'user_role, rates, expected_events',
    (
        (
            UserRole.admin,
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration_trial,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=3),
                    'type': AccountType.client_bonus,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.new,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=32),
                    'type': AccountType.client_bonus,
                },
            ],
            0,
        ),
        (
            UserRole.admin,
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration_trial,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=1),
                    'type': AccountType.client_bonus,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=32),
                    'type': AccountType.client_bonus,
                },
            ],
            0,
        ),
        (
            UserRole.admin,
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration_trial,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=1),
                    'type': AccountType.client_bonus,
                },
            ],
            2,
        ),
        (
            UserRole.user,
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration_trial,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=1),
                    'type': AccountType.client_bonus,
                },
            ],
            1,
        ),
    ),
)
async def test_tov_expire_rate_without_next_paid(
    aiohttp_client,
    esputnik_box,
    user_role,
    rates,
    expected_events,
):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    await prepare_user_data(app, user_role=user_role, email='<EMAIL>')
    for rate in rates:
        await insert_values(
            app=app, table=billing_account_table, company_id=user.company_id, **rate
        )

    await call_esputnik_tov_trial_rate_expiring_event(app, 0)

    # Assert
    assert len(esputnik_box) == expected_events


@pytest.mark.parametrize(
    ('case', 'rates', 'esputnik_count', 'store_in_meta', 'charge_bill_account_id'),
    (
        (
            'case 1: there is no document left',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_bonus,
                },
            ],
            1,
            False,
            TEST_UUID_1,
        ),
        (
            'case 2: there is less than 15 percents of document left',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 10,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_bonus,
                },
            ],
            1,
            False,
            TEST_UUID_1,
        ),
        (
            'case 3: we already sent an event before',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 10,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_bonus,
                },
            ],
            0,
            True,
            TEST_UUID_1,
        ),
        (
            "case 4: we already sent an event before but not it's zero",
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_bonus,
                },
            ],
            1,
            True,
            TEST_UUID_1,
        ),
        (
            'case 5: we already sent an event before for another bill account',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_bonus,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.pro_2022,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 10,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_bonus,
                },
            ],
            1,
            True,
            TEST_UUID_2,
        ),
    ),
)
async def test_on_document_charge_event_web(
    case,
    aiohttp_client,
    esputnik_box,
    rates,
    esputnik_count,
    store_in_meta,
    charge_bill_account_id,
):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.user.value)

    for rate in rates:
        await insert_values(
            app=app, table=billing_account_table, company_id=user.company_id, **rate
        )

    if store_in_meta:
        await insert_values(
            app=app,
            table=company_meta_table,
            company_id=user.company_id,
            documents_expires_last_billing_account_id=TEST_UUID_1,
            documents_expires_last_event=Event.documents_expires_15p,
        )

    # Act
    await on_document_charge_event(
        app,
        {
            'company_id': user.company_id,
            'charge_type': 'web',
            'billing_account_id': charge_bill_account_id,
        },
        logger,
    )

    # Assert
    assert len(esputnik_box) == esputnik_count


@pytest.mark.parametrize(
    ('case', 'rates', 'esputnik_count', 'store_in_meta', 'charge_bill_account_id'),
    (
        (
            'case 1: there is no document left',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_debit,
                },
            ],
            1,
            False,
            TEST_UUID_1,
        ),
        (
            'case 2: there is less than 15 percents of document left',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 10,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_debit,
                },
            ],
            1,
            False,
            TEST_UUID_1,
        ),
        (
            'case 3: we already sent an event before',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 10,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_debit,
                },
            ],
            0,
            True,
            TEST_UUID_1,
        ),
        (
            "case 4: we already sent an event before but not it's zero",
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_debit,
                },
            ],
            1,
            True,
            TEST_UUID_1,
        ),
        (
            'case 5: we already sent an event before for another bill account',
            [
                {
                    'id': TEST_UUID_1,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 0,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_debit,
                },
                {
                    'id': TEST_UUID_2,
                    'rate': AccountRate.integration,
                    'status': CompanyRateStatus.active,
                    'amount': 100,
                    'amount_left': 0,
                    'units': 100,
                    'units_left': 10,
                    'date_expired': utc_now().date() + datetime.timedelta(days=30),
                    'type': AccountType.client_debit,
                },
            ],
            1,
            True,
            TEST_UUID_2,
        ),
    ),
)
async def test_on_document_charge_event_api(
    case,
    aiohttp_client,
    esputnik_box,
    rates,
    esputnik_count,
    store_in_meta,
    charge_bill_account_id,
):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.user.value)

    for rate in rates:
        await insert_values(
            app=app, table=billing_account_table, company_id=user.company_id, **rate
        )

    if store_in_meta:
        await insert_values(
            app=app,
            table=company_meta_table,
            company_id=user.company_id,
            documents_expires_last_billing_account_id=TEST_UUID_1,
            documents_expires_last_event=Event.integration_expires_15p,
        )

    # Act
    await on_document_charge_event(
        app,
        {
            'company_id': user.company_id,
            'charge_type': 'api',
            'billing_account_id': charge_bill_account_id,
        },
        logger,
    )

    # Assert
    assert len(esputnik_box) == esputnik_count


@pytest.mark.parametrize(
    'prepare_company, prepare_rates, data, expected_rates, expected_config',
    [
        pytest.param(
            {
                'company_id': TEST_UUID_1,
                'company_edrpou': COMPANY_EDRPOU_FOP,
            },
            [],
            {'company_id': TEST_UUID_1},
            [AccountRate.free],
            {
                'external_comments_enabled': True,
                'internal_comments_enabled': True,
                'max_additional_fields_count': 0,
                'max_employees_count': 2,
                'max_documents_count': 50,
                'max_tags_count': 0,
                'max_automation_count': 0,
                'max_required_fields_count': 0,
                'max_versions_count': 0,
                'max_archive_documents_count': 0,
            },
            id='no_rates',
        ),
        pytest.param(
            {
                'company_id': TEST_UUID_1,
                'company_edrpou': COMPANY_EDRPOU_FOP,
            },
            [
                CompanyRate(
                    id_=TEST_UUID_1,
                    company_id=TEST_UUID_1,
                    company_edrpou=COMPANY_EDRPOU_FOP,
                    rate=AccountRate.free,
                    status=CompanyRateStatus.active,
                    start_date=utc_now(),
                    end_date=None,
                    amount=0,
                ),
            ],
            {'company_id': TEST_UUID_1},
            [AccountRate.free],
            {
                'external_comments_enabled': True,
                'internal_comments_enabled': True,
                'max_additional_fields_count': 0,
                'max_employees_count': 2,
                'max_documents_count': 50,
                'max_tags_count': 0,
                'max_automation_count': 0,
                'max_required_fields_count': 0,
                'max_versions_count': 0,
                'max_archive_documents_count': 0,
            },
            id='free_rate_already_exists',
        ),
        pytest.param(
            {
                'company_id': TEST_UUID_1,
                'company_edrpou': COMPANY_EDRPOU_FOP,
            },
            [
                CompanyRate(
                    id_=TEST_UUID_1,
                    company_id=TEST_UUID_1,
                    company_edrpou=COMPANY_EDRPOU_FOP,
                    rate=AccountRate.free,
                    status=CompanyRateStatus.canceled,
                    start_date=utc_now() - datetime.timedelta(days=2),
                    end_date=utc_now() - datetime.timedelta(days=1),
                    amount=0,
                ),
            ],
            {'company_id': TEST_UUID_1},
            [AccountRate.free, AccountRate.free],
            {
                'external_comments_enabled': True,
                'internal_comments_enabled': True,
                'max_additional_fields_count': 0,
                'max_employees_count': 2,
                'max_documents_count': 50,
                'max_tags_count': 0,
                'max_automation_count': 0,
                'max_required_fields_count': 0,
                'max_versions_count': 0,
                'max_archive_documents_count': 0,
            },
            id='free_rate_canceled',
        ),
        pytest.param(
            {
                'company_id': TEST_UUID_1,
                'company_edrpou': COMPANY_EDRPOU_FOP,
            },
            [
                CompanyRate(
                    id_=TEST_UUID_1,
                    company_id=TEST_UUID_1,
                    company_edrpou=COMPANY_EDRPOU_FOP,
                    rate=AccountRate.start,
                    status=CompanyRateStatus.active,
                    start_date=utc_now(),
                    end_date=None,
                    amount=0,
                ),
            ],
            {'company_id': TEST_UUID_1},
            [AccountRate.start],
            {
                'external_comments_enabled': True,
                'internal_comments_enabled': True,
                'can_manage_employee_access': True,
                'internal_document_enabled': True,
                'can_enforce_2fa': True,
                'reviews_enabled': True,
                'max_additional_fields_count': 5,
                'max_employees_count': 10,
                'max_documents_count': None,
                'max_tags_count': 3,
                'max_automation_count': 3,
                'max_required_fields_count': 0,
                'max_versions_count': None,
                'max_archive_documents_count': 0,
            },
            id='other_web_rate_exists',
        ),
        pytest.param(
            {
                'company_id': TEST_UUID_1,
                'company_edrpou': COMPANY_EDRPOU_TOV,
            },
            [],
            {'company_id': TEST_UUID_1},
            [AccountRate.free, AccountRate.pro_plus_trial_2022_12],
            {
                'external_comments_enabled': True,
                'internal_comments_enabled': True,
                'can_manage_employee_access': True,
                'internal_document_enabled': True,
                'can_enforce_2fa': True,
                'reviews_enabled': True,
                'max_additional_fields_count': None,
                'max_employees_count': 200,
                'max_documents_count': None,
                'max_tags_count': None,
                'max_automation_count': None,
                'max_required_fields_count': None,
                'max_versions_count': 50,
                'max_archive_documents_count': None,
            },
            id='not_fop_activate_pro_trial',
        ),
    ],
)
async def test_create_initial_free_company_rate(
    aiohttp_client,
    prepare_company: dict,
    prepare_rates: list[CompanyRate],
    data: dict,
    expected_rates: list[AccountRate],
    expected_config: dict,
    test_flags,
):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        enable_pro_functionality=False,
        **prepare_company,
    )

    test_flags[FeatureFlags.ACTIVATE_TRIAL_AUTOMATICALLY_FOR_TOV_COMPANIES.name] = True

    async with services.db.acquire() as conn:
        for rate in prepare_rates:
            await add_company_rate(conn, rate=rate, user=user)

    await create_initial_free_company_rate(app, data, logger)

    async with services.db.acquire() as conn:
        rates = await select_all_rates(conn)
        config = await get_billing_company_config(
            conn=conn,
            company_id=data['company_id'],
        )

    assert sorted([rate.rate for rate in rates]) == sorted(expected_rates)
    # At least one rate should be active
    assert any(rate.status == CompanyRateStatus.active for rate in rates)
    assert config.model_dump() == BillingCompanyConfig(**expected_config).model_dump()


async def test_initial_free_company_rate_expiration(aiohttp_client, test_flags):
    """
    Check that initial free company rate expiration is calculated like
    Start date + 1 year - 1 day at 23:59:59 in the local timezone.
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        enable_pro_functionality=False,
        company_id=TEST_UUID_1,
        company_edrpou=COMPANY_EDRPOU_FOP,
    )

    test_flags[FeatureFlags.ACTIVATE_TRIAL_AUTOMATICALLY_FOR_TOV_COMPANIES.name] = True

    data = {'company_id': TEST_UUID_1}
    await create_initial_free_company_rate(app, data, logger)

    async with services.db.acquire() as conn:
        rates = await select_all_rates(conn)

    assert len(rates) == 1
    rate = rates[0]
    local_start_date = to_local_datetime(rate.activation_date)
    assert to_local_datetime(rate.date_expired) == get_rate_end_date_from_start_date(
        local_start_date
    )
