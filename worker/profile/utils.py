from __future__ import annotations

import asyncio
import logging
from typing import Any

import sqlalchemy as sa
from citext import CIText

from app import models
from app.auth.tables import (
    company_table,
    role_table,
    user_active_role_company_join,
    user_table,
)
from app.auth.types import BaseUser
from app.billing.enums import INTEGRATION_RATES_SET
from app.billing.tables import billing_account_table
from app.contacts.tables import contact_person_table, contact_table
from app.document_automation.tables import document_automation_condition_table
from app.documents.tables import document_recipients_table, document_table
from app.lib.database import DBConnection, DBRow
from app.lib.enums import DocumentStatus
from app.models import select_all
from app.profile.emailing import send_about_email_change_email

logger = logging.getLogger(__name__)

UPDATE_RECIPIENTS_BATCH_SIZE = 5000
UPDATE_CONTACTS_BATCH_SIZE = 2000


def update_email_in_conditions(
    *,
    conditions: Any,
    old_email: str,
    new_email: str,
) -> tuple[Any, bool]:
    """
    Update all email fields in conditions that contain old email to new email recursively.
    """
    is_updated = False

    if isinstance(conditions, list):
        # We update the #document_uploaded_by key containing the old email, as this field is a
        # dropdown with coworker list on the UI side, making it impossible to enter a free-form
        # email here. Ideally, this field should be role_id, but that is how it is implemented
        # currently.
        #
        # Example:
        #  - before: ["#document_uploaded_by", "<EMAIL>"]
        #  - after: ["#document_uploaded_by", "<EMAIL>]
        if (
            len(conditions) == 2
            and isinstance(conditions[0], str)
            and isinstance(conditions[1], str)
            and conditions[0] == '#document_uploaded_by'
            and conditions[1].lower() == old_email.lower()
        ):
            return [conditions[0], new_email], True

        new_list = []
        for item in conditions:
            new_item, is_item_updated = update_email_in_conditions(
                conditions=item,
                old_email=old_email,
                new_email=new_email,
            )
            is_updated = is_updated or is_item_updated
            new_list.append(new_item)

        return new_list, is_updated

    if isinstance(conditions, dict):
        # The #document_recipients.email is a free-form field entered manually by the user and is
        # sometimes even used by users as a mechanism to redirect documents from the old email
        # to the new email. For this case, we add new email as "or" condition to the previous
        # condition to keep the old email as well.
        #
        # Example:
        #  - before: {"eq": ["#document_recipients.email", "<EMAIL>"]}
        #  - after: {"or": [
        #              {"eq": ["#document_recipients.email", "<EMAIL>"]},
        #              {"eq": ["#document_recipients.email", "<EMAIL>"]}
        #           ]}
        if (
            'eq' in conditions
            and isinstance(conditions['eq'], list)
            and len(conditions['eq']) == 2
            and conditions['eq'][0] == '#document_recipients.email'
            and conditions['eq'][1].lower() == old_email.lower()
        ):
            new_dict = {
                'or': [
                    {'eq': ['#document_recipients.email', conditions['eq'][1]]},  # keep old email
                    {'eq': ['#document_recipients.email', new_email]},  # append new email
                ]
            }
            return new_dict, True

        new_dict = {}
        for key, value in conditions.items():
            new_value, is_value_updated = update_email_in_conditions(
                conditions=value,
                old_email=old_email,
                new_email=new_email,
            )
            is_updated = is_updated or is_value_updated
            new_dict[key] = new_value
        return new_dict, is_updated

    return conditions, False


async def update_automations_on_user_email_change(
    conn: DBConnection,
    *,
    user_id: str,
    old_email: str,
    new_email: str,
) -> None:
    """
    Update all document automations with old email to new email.
    """

    rows = await select_all(
        conn=conn,
        query=(
            sa.select([document_automation_condition_table])
            .select_from(document_automation_condition_table)
            .where(
                sa.and_(
                    sa.func.cast(document_automation_condition_table.c.conditions, sa.Text).like(
                        f'%"{old_email}"%'
                    ),
                    # Update only automations for companies in which user has active roles
                    document_automation_condition_table.c.company_id.in_(
                        sa.select([role_table.c.company_id])
                        .select_from(user_active_role_company_join)
                        .where(user_table.c.id == user_id)
                    ),
                )
            )
        ),
    )

    # It's expected to have only a few automations with old email, so probably it's ok to update
    # them one by one
    for row in rows:
        new_conditions, is_updated = update_email_in_conditions(
            conditions=row.conditions,
            old_email=old_email,
            new_email=new_email,
        )
        if not is_updated:
            continue

        await conn.execute(
            sa.update(document_automation_condition_table)
            .values(conditions=new_conditions)
            .where(document_automation_condition_table.c.id == row.id)
        )


async def select_recipients_update_for_email_change(
    conn: DBConnection,
    *,
    old_email: str,
    limit: int,
    companies_edrpous: list[str],
) -> list[DBRow]:
    """
    Select documents recipients with old email for update
    """

    documents_for_update = (
        sa.select([document_recipients_table.c.id, document_recipients_table.c.document_id])
        .select_from(document_recipients_table)
        .where(
            sa.and_(
                # With old email
                models.functions.lowercase_emails_array(document_recipients_table.c.emails).op(
                    '@>'
                )([old_email.lower()]),
                # Ignore documents sent to companies in which users don't have active roles
                document_recipients_table.c.edrpou.in_(companies_edrpous),
                # Ignore finished or not sent documents
                ~sa.exists(
                    sa.select([1])
                    .select_from(document_table)
                    .where(
                        sa.and_(
                            document_table.c.id == document_recipients_table.c.document_id,
                            document_table.c.status_id.in_(
                                (
                                    DocumentStatus.finished.value,
                                    DocumentStatus.reject.value,
                                    DocumentStatus.uploaded.value,
                                    DocumentStatus.ready_to_be_signed.value,
                                )
                            ),
                        )
                    )
                ),
            )
        )
        .limit(limit)
    )
    return await select_all(
        conn=conn,
        query=documents_for_update,
    )


async def update_recipients_on_user_email_change(
    conn: DBConnection,
    user_id: str,
    old_email: str,
    new_email: str,
) -> list[str]:
    """
    Update all documents recipients with old email to new email for non-finished documents.
    """

    companies = await select_all(
        conn=conn,
        query=(
            sa.select([company_table.c.edrpou])
            .select_from(user_active_role_company_join)
            .where(user_table.c.id == user_id)
        ),
    )
    companies_edrpous: list[str] = [row.edrpou for row in companies if row.edrpou]

    rows = await select_recipients_update_for_email_change(
        conn=conn,
        old_email=old_email,
        companies_edrpous=companies_edrpous,
        limit=UPDATE_RECIPIENTS_BATCH_SIZE,
    )

    documents_ids = [row.document_id for row in rows]
    recipients_ids = [row.id for row in rows]

    await conn.execute(
        sa.update(document_recipients_table)
        .values(
            emails=sa.func.array_replace(
                sa.func.cast(document_recipients_table.c.emails, sa.ARRAY(CIText())),
                old_email,
                new_email,
            )
        )
        .where(document_recipients_table.c.id.in_(recipients_ids))
    )

    await conn.execute(
        sa.update(document_table)
        .values(
            email_recipient=sa.func.array_to_string(
                sa.func.array_replace(
                    sa.func.cast(
                        sa.func.regexp_split_to_array(document_table.c.email_recipient, r',\s*'),
                        sa.ARRAY(CIText()),
                    ),
                    old_email,
                    new_email,
                ),
                ', ',
            )
        )
        .where(
            sa.and_(
                document_table.c.id.in_(documents_ids),
                document_table.c.email_recipient.isnot(None),
            )
        )
    )

    return documents_ids


async def notify_users_about_email_change(
    conn: DBConnection,
    user: BaseUser,
    old_email: str | None,
    new_email: str,
) -> None:
    companies = await select_all(
        conn=conn,
        query=(
            sa.select(
                [
                    role_table.c.company_id,
                    sa.exists(
                        sa.select([1])
                        .select_from(billing_account_table)
                        .where(
                            sa.and_(
                                billing_account_table.c.company_id == role_table.c.company_id,
                                billing_account_table.c.rate.in_(INTEGRATION_RATES_SET),
                            )
                        )
                    ).label('has_integration_rate'),
                ]
            )
            .select_from(user_active_role_company_join)
            .where(user_table.c.id == user.id)
        ),
    )

    companies_mapping = {row.company_id: row for row in companies if row.company_id}
    companies_ids = [row.company_id for row in companies]

    roles = await select_all(
        conn=conn,
        query=(
            sa.select([user_table.c.email, role_table.c.company_id])
            .select_from(user_active_role_company_join)
            .where(
                sa.and_(
                    role_table.c.company_id.in_(companies_ids),
                    role_table.c.can_receive_email_change.is_(True),
                ),
            )
        ),
    )

    # Some user's coworkers might be in more than companies, so we need to
    # deduplicate them to avoid sending email about the same change multiple times
    admin_emails = list({role.email for role in roles})

    coroutines = []
    for admin_email in admin_emails:
        # skip sending email to user itself
        if admin_email in (old_email, new_email):
            continue

        # Is any company of admin email has integration rate?
        has_integration_rate: bool = any(
            (c := companies_mapping.get(role.company_id)) and c.has_integration_rate
            for role in roles
            if role.email == admin_email
        )

        task = send_about_email_change_email(
            admin_email=admin_email,
            new_email=new_email,
            old_email=old_email,
            user=user,
            has_integration_rate=has_integration_rate,
        )
        coroutines.append(task)

    await asyncio.gather(*coroutines)


async def update_contacts_on_user_email_change(
    conn: DBConnection,
    *,
    user_id: str,
    old_email: str,
    new_email: str,
    cursor: str | None,
) -> str | None:
    """
    Update all contacts with old email to new email.
    """

    _cursor = cursor or '00000000-0000-0000-0000-000000000000'

    # Get the next batch of potential contact persons as soon as possible,
    # and then later we will check if they actually need to be updated or not.
    #
    # April 2024 statistic:
    #  1. <1k persons per email ~ 1,797,824
    #  2. 1k-10k persons per email ~ 699
    #  3. >10k persons per email ~ 46
    batch_rows = await select_all(
        conn=conn,
        query=(
            sa.select([contact_person_table.c.id])
            .select_from(contact_person_table)
            .where(
                sa.and_(
                    contact_person_table.c.email == old_email,
                    contact_person_table.c.id > _cursor,
                )
            )
            .order_by(contact_person_table.c.email, contact_person_table.c.id)
            .limit(UPDATE_CONTACTS_BATCH_SIZE)
        ),
    )
    if not batch_rows:
        return None

    persons_ids: list[str] = [row.id for row in batch_rows]

    user_companies_cte = (
        sa.select([company_table.c.edrpou])
        .select_from(user_active_role_company_join)
        .where(user_table.c.id == user_id)
        .cte('user_companies')
    )
    contact_person_alias = contact_person_table.alias()

    contact_persons_to_update = (
        sa.select([contact_person_table.c.id])
        .select_from(
            contact_person_table.join(
                contact_table,
                contact_person_table.c.contact_id == contact_table.c.id,
            )
        )
        .where(
            sa.and_(
                contact_person_table.c.id.in_(persons_ids),
                # Update only contacts with EDRPOU of companies in which user has active roles
                contact_table.c.edrpou.in_(user_companies_cte),
                # Exclude rows where new_email already exists for the same contact_id
                ~sa.exists()
                .select_from(contact_person_alias)
                .where(
                    sa.and_(
                        contact_person_alias.c.contact_id == contact_person_table.c.contact_id,
                        contact_person_alias.c.email == new_email,
                    )
                ),
            )
        )
    )

    await conn.execute(
        sa.update(contact_person_table)
        .values(email=new_email)
        .where(contact_person_table.c.id.in_(contact_persons_to_update))
    )

    next_cursor = batch_rows[-1].id
    return next_cursor
