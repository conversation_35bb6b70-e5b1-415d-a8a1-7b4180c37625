import logging

from aiohttp import web

from app.auth.db import (
    select_company_by_edrpou,
    select_users_by_emails,
)
from app.auth.utils import get_company_config
from app.lib.sender.enums import SenderMessageType
from app.lib.types import DataDict
from app.services import services
from worker.emailing.utils import remove_unsubscription_for_registered_users

logger = logging.getLogger(__name__)


async def cleanup_unsubscriptions(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Remove users with completed registration that are in unsubscription
    list for unregistered
    """
    async with services.db.acquire() as conn:
        await remove_unsubscription_for_registered_users(conn)


async def send_sms_to_document_recipient(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    recipient_emails = data['recipient_emails']
    sender_edrpou = data['sender_edrpou']
    async with app['db'].acquire() as conn:
        company = await select_company_by_edrpou(conn, sender_edrpou)
        if not company:
            logger.info('Can not find company, sending sms notification failed', extra=data)
            return

        company_config = await get_company_config(conn, company_id=company.id)

        if not company_config.send_sms_to_document_receivers:
            return

        recipients = await select_users_by_emails(
            conn, emails=recipient_emails, verified_phone=True
        )
    if not recipients:
        return

    message = (
        f'Ви отримали документ від {company.name or company.full_name or sender_edrpou}, '
        f'ознайомтесь на edo.vchasno.ua'
    )

    for recipient in recipients:
        if not recipient.phone:
            continue  # such cases should be filtered out by select_users_by_emails

        await services.evo_sender.send_viber_or_sms(
            phone=recipient.phone,
            message=message,
            message_type=SenderMessageType.inbox_document,
        )
    logger.info('Sent SMS to recipients', extra=data)
