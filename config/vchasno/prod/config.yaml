app:
  debug: false
  demo: false
  test: false
  port: 80
  domain: https://edo.vchasno.ua
  static_host: https://edo.vchasno.ua/cloud-cgi/static/edo-static-files-prd
  static_prefix: https://edo.vchasno.ua/cloud-cgi/static/edo-static-files-prd
  client_max_size: 100
  landing_url: https://vchasno.ua

  request_timeout: 14.5
  delete_s3_timeout: 12.5
  upload_timeout: 29.5
  upload_s3_timeout: 25
  papka24_timeout: 5
  sync_roles_timeout: 7.5
  sign_timeout:
    default: 16
    slow: 50
  shutdown_timeout: 60
  slow_urls:
    - /api/private/blackbox/documents
    - /api/v1/documents
    - /api/v2/documents
    - /downloads/actions/documents.csv
    - /downloads/actions/documents.csv
    - /downloads/actions/documents.xlsx
    - /internal-api/companies
    - /internal-api/companies/mobile-id
    - /internal-api/contacts/dossier
    - /internal-api/documents
    - /internal-api/documents
    - /internal-api/graphql
    - /internal-api/proxy
    - /internal-api/registration/signers
    - /internal-api/signatures/mobile-id
    - /internal-api/statistics/users
    - /internal-api/documents/suggest
  slow_urls_patterns:
    - /internal-api/documents/.+/signatures
    - /internal-api/documents/.+/xml-to-pdf
    - /internal-api/documents/.+/suggest

  # TODO: Split this key into separate keys because we currently use this single key
  # for multiple purposes, which broadly grants equal access to all of these purposes.
  # Used for:
  #   - Allowing frontend /api/* requests from these domains
  #   - Allowing redirects to these domains
  #   - Skipping referer checks for these domains
  #   - Allowing logout requests from these domains
  trusted_origins:
    - https://edi.vchasno.ua
    - https://kasa.vchasno.ua
    - https://cap.vchasno.ua
    - http://cbc-703.np-lc.stag.stfalcon.com
    - http://pre-master.np-lc.stag.stfalcon.com
    - http://master.np-lc.stag.stfalcon.com
    - https://new.novaposhta.ua
    - https://vchasno.ua
    - https://ttn.vchasno.ua
    - https://kadry.vchasno.ua
    - https://zvit.vchasno.ua

  # Mirror these values with:
  # - Docker test env config
  # - Test env config
  # Don't forget to update:
  # - test_security_middleware.expected_csp_policy
  content_security_policy:
    default:
      - self
    script:
      - self
      - https://*.licdn.com
      - https://*.googletagmanager.com
      # TODO: Refactor FE to disallow eval and inline scripts.
      - unsafe-eval
      - unsafe-inline
    script_src_elem:
      - self
      - https://*.doubleclick.net
      - https://*.googletagmanager.com
    style:
      - self
    style_src_elem:
      - self
    img:
      - self
      - 'data:'
      - https://*.facebook.com
      - https://*.doubleclick.net
      - https://*.google.com
      - https://*.google.com.ua
      - https://*.google.de
      - https://*.google-analytics.com
      - https://*.googletagmanager.com
    font:
      - self
    connect:
      - self
      - https://*.linkedin.com
      - https://*.doubleclick.net
      - https://*.google-analytics.com
      - https://*.google.com
      - https://*.google.com.ua
      - https://*.googletagmanager.com
      - https://*.facebook.com
      - https://*.facebook.net
      - https://*.posthog.com
    frame:
      - self
      - https://*.doubleclick.net
      - https://*.facebook.com
    frame_ancestors:
      - self
    object:
      - self
    worker:
      - self

  brand: Вчасно
  logo_path: images/logos/vchasno.png
  session_max_age: 2592000  # = 60 secs * 60 mins * 24 hrs * 30 days
  cookie_name: vchasno_session
  cookie_domain: edo.vchasno.ua
  cookie_secure: true
  domain_name: edo.vchasno.ua
  info_email: <EMAIL>
  noreply_email: <EMAIL>
  support_email: <EMAIL>
  sales_email: <EMAIL>
  support_phone: '+38 044 392 03 00'
  support_phone_lifecell: '+38 063 460 5 333'
  support_phone_vodafone: '+38 050 416 5 333'
  support_phone_kyivstar: '+38 067 460 5 333'
  support_viber: 'vchasnoedo'
  support_telegram: 'vchasnoedo_bot'
  support_hours: '8:00 - 19:00'

  fernet_key: ${FERNET_KEY}
  test_edrpou: '11111111'
  sign_widget_iit_url: https://id.gov.ua/sign-widget/v20200922/
  sign_widget_iit_fozzy_url: https://eu.iit.com.ua/sign-widget/v20240301/
  default_language: uk
  available_languages:
    - en
    - uk
  proxy_ua: http://185.86.58.152:3128
theme:
  primary_color: "#FF9C01"
analytics:
  ga_event_measurement_id: '5EE52G3WCQ'
  ga_tracking_id: 'UA-88652144-1'
  ga_kasa_tracking_id: 'UA-88652144-25'
  gtm_tracking_id: 'GTM-PRJ9TST'
  gtm_common_tracking_id: 'GTM-KDBP5SJ'
  gtm_kasa_tracking_id: 'G-HF2XZD4ZQP'
  fb_pixel_id: '1527037984022259'
  fb_domain_content_id: 'ezqjkn6ai4sv4qb31xez1n7dyqmooc'
auth:
  totp_interval: 180
  totp_secret_salt: U3ISUBR5I7C2DSIY5U7QK7SLXZQYHFRFKDOBZL2NIILRO6TRPYCQ====
fcm:
  is_enabled: true
  project_id: ${FIREBASE_PROJECT_ID}
  config_path: ${FIREBASE_CONFIG_PATH}
posthog:
  # It's a public key, like sentry dsn
  api_key: phc_u3jVgBYb5JDQlXI0wRLHqZ3lWm7m9qHMux3cdAroZk6
  api_host: https://eu.i.posthog.com
feature_flags:
  pass_sign_info_to_backend: false
  multi_download: true
  new_worker: false
  pro_rate_allow_for_free: true
feature_flags_v2:
  manager: http
  project: edo
  url: ${FEATURE_FLAGS_URL}
  timeout: 60
google_analytics:
  token: ${GA_MEASUREMENT_PROTOCOL_TOKEN}
google_auth:
  # Project "Vchasno-Login-With-Google-PRD" on Google Cloud
  client_id_web_edo: '257429459814-dg05qc61l328bsntk1hvv8vf2popgt9s.apps.googleusercontent.com'
  # Google Cloud projects:
  # EDO prd (id: edo-prd-8b7b6)
  # KASA prd (id: kasa-prd)
  # KEP prd (id: kep-prd)
  client_id_ios_edo: '447768174773-2pru44o63ts8h4jof5jf3vgraumrk3ha.apps.googleusercontent.com'
  client_id_ios_kasa: '396448789439-ik9m7n3v887tial99n4e18jv7mu5dgbt.apps.googleusercontent.com'
  client_id_ios_kep: '94505074424-0i106n032po48g7mm3ndbctejfmgd3er.apps.googleusercontent.com'
  client_id_android_edo: '447768174773-dhlpophmajo3uefjekid2mr1htip9dgs.apps.googleusercontent.com'
  client_id_android_kasa: '396448789439-tdnt5jvvl9lt3u1fapb1ifs8p8o9gsg9.apps.googleusercontent.com'
  client_id_android_kep: '94505074424-it17070kb952tjg1unf9l8qsrtao7o5v.apps.googleusercontent.com'
microsoft_auth:
  client_id: bc93a50a-8a4f-458a-8fac-cca4c2411604
apple_auth:
  team_id: JNSLSBSJH7
  edo_web_client_id: ua.vchasno
  edo_client_id: ua.vchasno.edo
  kep_client_id: ua.vchasno.cap
  kasa_client_id: ua.vchasno.kasa
  key_id: S7W2LKNU9W
  key: ${APPLE_AUTH_KEY}
conversions:
  first_sign_flow:
    ignore:
      - '36507036'  # Uaprom
      - '40283641'  # Zakupki
      - '37973945'  # Portmone
      - '31738765'  # Delivery
tokens:
  public_key: /work/config/vchasno/prod/keys/tokens.pub
  private_key: /secrets/tokens.pem
db:
  url: ${DB_URL}
  url_readonly: ${DB_READ_URL}
  minsize: 1
  maxsize: 10
  timeout: 60.0
  use_aiopg_connection_pool: true
    # This mode encrypts all data in transit between our app and the database. Also, it checks that
  # we are connecting to the right database server. Similar how HTTPS works but with custom Root CA.
  # The Root CA certificate is stored in Vault and mounted into the container in the /secrets directory.
  ssl_mode: verify-full
  ssl_root_cert: /secrets/aws-rds-eu-central-1.pem
events_db:
  url: ${EVENT_DB_URL}
  minsize: 1
  maxsize: 10
  timeout: 60.0
  use_aiopg_connection_pool: true
    # This mode encrypts all data in transit between our app and the database. Also, it checks that
  # we are connecting to the right database server. Similar how HTTPS works but with custom Root CA.
  # The Root CA certificate is stored in Vault and mounted into the container in the /secrets directory.
  ssl_mode: verify-full
  ssl_root_cert: /secrets/aws-rds-eu-central-1.pem
es:
  hosts:
    - https://elastic-edo-es-http.services-edo.svc.cluster.local:9200
  document_index: documents
  comment_index: comments
  contact_recipient_index: contact_recipients
  # temporary solution, before we will configure proper TLS in whole cluster
  verify_certs: false
  username: ${ES_USERNAME}
  password: ${ES_PASSWORD}
kafka:
  bootstrap_servers:
    - 'b-1.mskclusteredoprd.mgro8a.c6.kafka.eu-central-1.amazonaws.com:9094'
    - 'b-2.mskclusteredoprd.mgro8a.c6.kafka.eu-central-1.amazonaws.com:9094'
    - 'b-3.mskclusteredoprd.mgro8a.c6.kafka.eu-central-1.amazonaws.com:9094'
    - 'b-4.mskclusteredoprd.mgro8a.c6.kafka.eu-central-1.amazonaws.com:9094'
  security_protocol: SSL
  callback_timeout: 2
  client_id: edo
  group_id: edo
  producer:
    request_timeout_ms: 40000
  consumer:
    session_timeout_ms: 60000
  topic_prefix: 'vchasno'
redis:
  host: master.vchasno-edo.trzid5.euc1.cache.amazonaws.com
  port: 6379
  db: 0
  timeout: 1.0
  use_tls: true
  password: ${REDIS_PASSWORD}
s3:
  type: aws
  access_key: ${S3_NEW_ACCESS_KEY}
  secret_key: ${S3_NEW_SECRET_KEY}
  bucket: edo-storage-prd
  region_name: eu-central-1
  read_timeout: 10
  connect_timeout: 2
  connections_limit: 1000
  signature_version: s3v4
  encryption_keys:
    - ${S3_NEW_ENCRYPTION_KEY}
bedrock:
  region_name: eu-central-1
  access_key: ${BEDROCK_KEY_ID}
  secret_key: ${BEDROCK_KEY_SECRET}
textract:
  region_name: eu-central-1
  access_key: ${TEXTRACT_KEY_ID}
  secret_key: ${TEXTRACT_KEY_SECRET}
smtp:
  host: email-smtp.eu-central-1.amazonaws.com
  port: 465
  timeout: 12.5
  use_tls: true
  username: ${SMTP_USER}
  password: ${SMTP_PASSWORD}
aws_ses:
  region_name: eu-central-1
  access_key: ${AWS_SES_ACCESS_KEY}
  secret_key: ${AWS_SES_SECRET_KEY}
evo_sender:
  project_id: vchasno.com.ua
  base_url: http://sender.vchasno.svc.cluster.local:8888
telegram:
  url: 'https://api.telegram.org/bot'
  token: ${TELEGRAM_API_TOKEN}
youcontrol:
  url: 'https://youcontrol.com.ua/api/card.php'
  login: '<EMAIL>'
  password: ${YOUCONTROL_PASSWORD}
  referral_url: 'https://youcontrol.com.ua/ref/594957/'
  youscore_domain: https://api.youscore.com.ua
  youscore_apikey: ${YOUCONTROL_YOUSCORE_APIKEY}
sync_contacts_api:
  e36507036: # promua
    secret_key: ${SYNC_CONTACTS_API_PROMUA_SECRET_KEY}
    url: https://my.prom.ua/api/_/tools/v1/find_company_contact_info
    url_by_invoice: https://my.prom.ua/api/_/tools/v1/find_company_contact_info_by_invoice_id
    param: token
    limit: 100
    prefix: 'ua-'
  e40283641: # zakupki
    secret_key: ${SYNC_CONTACTS_API_ZAKUPKI_SECRET_KEY}
    url: https://my.zakupki.prom.ua/remote/public_api/merchant_contact_info
    param: access_token
    limit: 20
contacts:
  # TODO[KG]: merge with `sync_contacts_api` (+ changes in secrets config)
  uploads:
    allowed_extensions:
      - .csv
      - .xlsx
    max_file_size: 5  # MB
url2pdf:
  url: http://url2pdf-http.vchasno.svc.cluster.local:8090
  use_xz: true
  timeout: 12.5
vchasno_signer:
  proxy_service_url: /internal-api/proxy
  use_context: true
consts:
  gen_archive_ttl: 1200 # ttl for gen archive task in seconds
  max_files: 50 # max files in one archive
crm:
  url: http://crm-service-http.vchasno.svc.cluster.local:8080
  token: ${CRM_TOKEN}
  creatio_base_url: https://vchasno.creatio.com
  landing_id: 6d3aaaf0-e97d-4670-8f7b-98eb7f646927
concierge:
  service: vchasno
  backend_url: http://concierge-service-backend.vchasno.svc.cluster.local:8000
  frontend_url: http://concierge-service-frontend.vchasno.svc.cluster.local:8000
  # WARN: to change concierge cookie name or domain, you should first update config in "concierge" service
  # because cookie is set by concierge service, not by EDO
  cookie_name: vchasno_auth
  cookie_domain: .vchasno.ua
  token: ${CONCIERGE_TOKEN}
edi:
  api_url: http://edi-service-http-server.edi.svc.cluster.local:5000/jsonrpc
  rpc_auth_token: ${EDI_RPC_AUTH_TOKEN}
  host: https://edi.vchasno.ua
  landing_url: https://edi.vchasno.com.ua
esputnik:
  url: https://esputnik.com/api/v1/
  login: <EMAIL>
  password: ${ESPUTNIK_PASSWORD}
  address_book_id: 16832
  limit_insert: 3000
ttn:
  host: https://ttn.vchasno.ua
  api_url: http://ettn-http-server.ettn.svc.cluster.local:8080
  landing_url: https://ttn.vchasno.ua
  auth_token: ${TTN_AUTH_TOKEN}
kassa:
  host: https://kasa.vchasno.ua
  api_url: http://kasa-http-server.kasa.svc.cluster.local:8080
  landing_url: https://kasa.vchasno.com.ua
  auth_token: ${KASSA_AUTH_TOKEN}
kep:
  host: https://cap.vchasno.ua
  landing_url: https://cap.vchasno.com.ua
  auth_token: ${KEP_AUTH_TOKEN}
zvit:
  # Trailing / is essential for Zvit (/app/)
  host: https://zvit.vchasno.ua/app/
  landing_url: https://zvit.vchasno.ua
cloud_signer:
  host: https://cs.vchasno.ua
  auth_token: ${CLOUD_SIGNER_AUTH_TOKEN}
diia:
  host: https://api2.diia.gov.ua
  token: ${DIIA_TOKEN}
  offer_id: ${DIIA_OFFER_ID}
  branch_id: ${DIIA_BRANCH_ID}
  auth_acquirer_token: ${DIIA_AUTH_ACQUIRER_TOKEN}
integrations:
  header: X-Vchasno-Private-Token
  zakupki:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRaVHBUMGtUZEg5NUlGSTA1U00vcVhRJDJZeFF0SGlaYS9JUlB0NDlyZHZ2MHc=
  kep:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRXNHQ1Ri9ObGpuMkNnYzl1UE9VSXB3JDlnSjloR213NVlXN1l6clRScXZDaGc=
  kasa:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xNjM4NCx0PTMscD0xJHpUWXZlT1FjcVdNZVVGKzJkSFBRaXckMHRHTmRCRmdKUVpPOTMyTWQzeUh2bmFFZE9nYlpCU2RvTnFrZkg0ZXdYOA==
  ttn:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT02NTUzNix0PTMscD00JEo0cDZaYVN0ZW4xVUZqMWgzRnR5RVEkQ3kxUksrUlUyWEk0Z05OR2p3SDExSGNZR2xVa3BVTVBsZjBpVGo2citVQQ==
  hrs:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT02NTUzNix0PTMscD00JDBEWWd1S0U3STg2YU4wcTJmeFVrZ0EkVTlwWlVTekdvV3VKV0xKUGh2dWl0dFFZNk1PdUFxSTRLb3ZIY2lRdVVkMA==
  landing:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT02NTUzNix0PTMscD00JGhHdEsxOFhVRE9FUTlYUGRxU25BeVEkTVBlZUN5Rktpbnd0RkdESi9vQ0E5WGF2RTRHTHNTcUU3dUFUYjNhYmoyMA==
  creatio:
    token_hashes:
    - JGFyZ29uMmkkdj0xOSRtPTE2LHQ9MixwPTEkVEdoMlltaGtaSEE1WWpac00yTmFiUSRqbmV4QmxOTGx0M0thb3FFbHlnOHZR
antivirus:
  host: http://antivirus-service.services-edo.svc.cluster.local:5004
signer_web:
  host: http://signer-web.vchasno.svc.cluster.local:8000
  secret: ${SIGNER_WEB_SECRET}
  key_id: ${SIGNER_WEB_KEY_ID}
evopay:
  host: https://api.rozetkapay.com
  api_key: ${EVOPAY_API_KEY}
  api_secret: ${EVOPAY_API_SECRET}
sentry:
  dsn_backend: https://<EMAIL>/2
  dsn_frontend: https://<EMAIL>/6
privatbank:
  host: https://acp.privatbank.ua
  integration_id: ${PRIVATBANK_INTEGRATION_ID}
  integration_token: ${PRIVATBANK_INTEGRATION_TOKEN}
  account_number: *****************************
pumb:
  auth_host: https://auth.fuib.com/
  api_host: https://service.fuib.com:4103/
  client_id: corpcli
  client_secret: 9160f6d8-d658-4637-9b0e-3bb30524b5d8
  username: api_user_edo
  password: ${PUMB_PASSWORD}
  account_id: *********
collabora:
  url: https://edo.vchasno.ua/collabora
gotenberg:
  url: http://gotenberg-http.vchasno.svc.cluster.local:3000
hrs:
  api_url: http://hrs-http-server.hrs.svc.cluster.local:8080
  token: ${HRS_AUTH_TOKEN}
typeform:
  form_id: null
