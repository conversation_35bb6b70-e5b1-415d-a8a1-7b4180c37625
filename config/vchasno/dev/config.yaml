app:
  debug: true
  demo: false
  test: false
  port: 8000
  domain: https://edo-dev.vchasno.com.ua
  static_host: https://edo-dev.vchasno.com.ua/cloud-cgi/static/edo-static-files
  static_prefix: https://edo-dev.vchasno.com.ua/cloud-cgi/static/edo-static-files
  client_max_size: 100

  request_timeout: 14.5
  delete_s3_timeout: 12.5
  upload_timeout: 29.5
  upload_s3_timeout: 25
  papka24_timeout: 5
  sync_roles_timeout: 7.5
  sign_timeout:
    default: 16
    slow: 50
  shutdown_timeout: 60
  slow_urls:
    - /api/private/blackbox/documents
    - /api/v1/documents
    - /api/v2/documents
    - /downloads/actions/documents.csv
    - /internal-api/companies
    - /internal-api/companies/mobile-id
    - /internal-api/contacts/dossier
    - /internal-api/contacts/sync
    - /internal-api/documents
    - /internal-api/proxy
    - /internal-api/registration/signers
    - /internal-api/signatures/mobile-id
    - /internal-api/documents/suggest
  slow_urls_patterns:
    - /internal-api/documents/.+/signatures
    - /internal-api/documents/.+/xml-to-pdf
    - /internal-api/documents/.+/suggest
  trusted_origins:
    - http://edi.vchasno.stg.evo
    - https://edi.vchasno.stg.evo
    - https://edi-dev.vchasno.com.ua
    - http://raven.vchasno.stg.evo
    - https://raven.vchasno.stg.evo
    - https://kep.vchasno.stg.evo
    - https://ttn.vchasno.stg.evo
    - http://ttn.vchasno.stg.evo
    - https://kasa-dev.vchasno.com.ua
    - https://ettn-dev.vchasno.com.ua
    - https://hrs-dev.vchasno.com.ua
    - https://zvit-dev.vchasno.com.ua
  brand: Вчасно
  logo_path: images/logos/vchasno.png
  session_max_age: 2592000  # = 60 secs * 60 mins * 24 hrs * 30 days
  cookie_name: vchasno_dev_session
  cookie_domain: edo-dev.vchasno.com.ua
  cookie_secure: false
  domain_name: edo-dev.vchasno.com.ua
  info_email: <EMAIL>
  noreply_email: <EMAIL>
  support_email: <EMAIL>
  sales_email: <EMAIL>
  support_phone: '(044) 392 03 00'
  support_phone_lifecell: '+38 063 460 5 333'
  support_phone_vodafone: '+38 050 416 5 333'
  support_phone_kyivstar: '+38 067 460 5 333'
  support_viber: 'vchasnoedo'
  support_telegram: 'vchasnoedo_bot'
  support_hours: '8:00 - 19:00'

  fernet_key: ${FERNET_KEY}
  test_edrpou: '11111111'
  sign_widget_iit_url: https://eu.iit.com.ua/sign-widget/v20200922/
  sign_widget_iit_fozzy_url: https://eu.iit.com.ua/sign-widget/v20240301/
  default_language: uk
  available_languages:
    - en
    - uk
theme:
  primary_color: "#FF9C01"
analytics:
  ga_tracking_id: 'UA-88652144-1'
  ga_kasa_tracking_id: 'UA-88652144-25'
  gtm_tracking_id: 'GTM-PRJ9TST'
  gtm_common_tracking_id: 'GTM-KDBP5SJ'
  gtm_kasa_tracking_id: 'G-HF2XZD4ZQP'

auth:
  totp_interval: 180
  totp_secret_salt: I4DGUJZ6PIBGUQFHSRC7INWR52GZGCZQTIBNJT5IBOI2MRATMI6Q====
fcm:
  is_enabled: true
  project_id: ${FIREBASE_PROJECT_ID}
  config_path: ${FIREBASE_CONFIG_PATH}
feature_flags:
  pass_sign_info_to_backend: false
  multi_download: true
  new_worker: true
  pro_rate_allow_for_free: false
feature_flags_v2:
  manager: http
  project: edo
  url: ${FEATURE_FLAGS_URL}
  timeout: 60
google_auth:
  # Project "Vchasno-Login-With-Google-DEV" on Google Cloud
  client_id_web_edo: '553068911208-q82ae2npn5o9g8chn81qnahms13cukrq.apps.googleusercontent.com'
  # Projects on Google Cloud:
  # EDO stg (id: edo-stg-90c90)
  # KASA stg (id: kasa-stg-dc85f)
  # KEP stg (id: kep-stg)
  client_id_ios_edo: '443532960258-6o4vo9vlvdf1vcn417ueu1fsm6v75p2s.apps.googleusercontent.com'
  client_id_ios_kasa: '511723122321-q6bu3dqog3kasgoavchp1ekkrboog3n3.apps.googleusercontent.com'
  client_id_ios_kep: '342419402706-q3e75unr9hjck6qbv7md8ml0mp1jqdkc.apps.googleusercontent.com'
  client_id_android_edo: '443532960258-b4su2b3j8bbrrejubfas3mqg92phc0rn.apps.googleusercontent.com'
  client_id_android_kasa: '511723122321-hdm5bahvlm9j1uesafg6dgu9lai3m6d7.apps.googleusercontent.com'
  client_id_android_kep: '342419402706-rmdm64ge6igv5k1t4ce1mg78p36rb6hl.apps.googleusercontent.com'
microsoft_auth:
  client_id: bc93a50a-8a4f-458a-8fac-cca4c2411604
apple_auth:
  team_id: JNSLSBSJH7
  edo_web_client_id: ua.vchasno.dev
  edo_client_id: ua.vchasno.edo.dev
  kep_client_id: ua.vchasno.cap.dev
  kasa_client_id: ua.vchasno.kasa.dev
  key_id: 6ZRPX8JS98
  key: |-
*****************************************************************************************************************************************************************************************************************************************************************************************
tokens:
  # Those files are mounted into the container by k8s
  public_key: config/vchasno/dev/keys/tokens.pub
  private_key: /secrets/tokens.pem
db:
  url: ${DB_URL}
  url_readonly: ${DB_READ_URL}
  minsize: 1
  maxsize: 5
  timeout: 5.0
  use_aiopg_connection_pool: true
  # This mode encrypts all data in transit between our app and the database. Also, it checks that
  # we are connecting to the right database server. Similar how HTTPS works but with custom Root CA.
  # The Root CA certificate is stored in Vault and mounted into the container in the /secrets directory.
  ssl_mode: verify-full
  ssl_root_cert: /secrets/aws-rds-eu-central-1.pem
events_db:
  url: ${EVENT_DB_URL}
  minsize: 1
  maxsize: 5
  timeout: 5.0
  use_aiopg_connection_pool: true
  ssl_mode: verify-full
  ssl_root_cert: /secrets/aws-rds-eu-central-1.pem
es:
  hosts:
    - https://elastic-edo-es-http.services-vchasno.svc.cluster.local:9200
  document_index: documents_new
  comment_index: comments
  contact_recipient_index: contact_recipients
  # temporary solution, before we will configure proper TLS in whole cluster
  verify_certs: false
  username: ${ES_USERNAME}
  password: ${ES_PASSWORD}
kafka:
  bootstrap_servers: ${KAFKA_BOOTSTRAP_SERVERS}
  callback_timeout: 2
  client_id: kafka-vchasno-dev
  group_id: kafka-vchasno-dev
  producer:
    request_timeout_ms: 40000
  consumer:
    session_timeout_ms: 30000
  topic_prefix: vchasno
  security_protocol: ${KAFKA_SECURITY_PROTOCOL}
  # Authentication options
  sasl_mechanism: SCRAM-SHA-512
  sasl_plain_username: ${KAFKA_SASL_USERNAME}
  sasl_plain_password: ${KAFKA_SASL_PASSWORD}
redis_sentinel:
  hosts:
    - host: rfs-redis-edo.services-vchasno.svc.cluster.local
      port: 26379
  db: 0
  sentinel_timeout: 0.5
  username: ${REDIS_SENTINEL_USERNAME}
  password: ${REDIS_SENTINEL_PASSWORD}
  service_name: mymaster
s3:
  type: aws
  access_key: ${S3_NEW_ACCESS_KEY}
  secret_key: ${S3_NEW_SECRET_KEY}
  bucket: edo-storage-new-dev
  region_name: eu-central-1
  read_timeout: 6
  connect_timeout: 1
  connections_limit: 1000
  signature_version: s3v4
  encryption_keys:
    - ${S3_NEW_ENCRYPTION_KEY}
bedrock:
  region_name: eu-central-1
  access_key: ${BEDROCK_KEY_ID}
  secret_key: ${BEDROCK_KEY_SECRET}
textract:
  region_name: eu-central-1
  access_key: ${TEXTRACT_KEY_ID}
  secret_key: ${TEXTRACT_KEY_SECRET}
smtp:
  host: email-smtp.eu-central-1.amazonaws.com
  port: 465
  timeout: 12.5
  use_tls: true
  username: ${SMTP_USER}
  password: ${SMTP_PASSWORD}
evo_sender:
  project_id: 'vchasno.com.ua'
  base_url: http://sender.vchasno.svc.cluster.local:8888
youcontrol:
  url: 'https://youcontrol.com.ua/api/card.php'
  login: '<EMAIL>'
  password: ${YOUCONTROL_PASSWORD}
  referral_url: 'https://youcontrol.com.ua/ref/594957/'
  youscore_domain: https://api.youscore.com.ua
  youscore_apikey: ${YOUCONTROL_YOUSCORE_APIKEY}
sync_contacts_api:
  # TODO: consider to remove this config from "dev" environment
  #   because it's higly unlikely that we will have access from
  #   "dev" environment (TabulaRasa) to "stg" environment (EVO.company)
  e36507036: # promua
    secret_key: ${SYNC_CONTACTS_API_PROMUA_SECRET_KEY}
    url: http://my.trunk.uaprom/api/_/tools/v1/find_company_contact_info
    url_by_invoice: http://my.trunk.uaprom/api/_/tools/v1/find_company_contact_info_by_invoice_id
    param: token
    limit: 100
    prefix: 'ua-'
  e40283641: # zakupki
    secret_key: ${SYNC_CONTACTS_API_ZAKUPKI_SECRET_KEY}
    url: https://my.zakupki.prom.ua/remote/public_api/merchant_contact_info
    param: access_token
    limit: 20
contacts:
  # TODO[KG]: merge with `sync_contacts_api` (+ changes in secrets config)
  uploads:
    allowed_extensions:
      - .csv
      - .xlsx
    max_file_size: 5  # MB
url2pdf:
  url: http://url2pdf-http.vchasno.svc.cluster.local:8090
  use_xz: true
  timeout: 60.0
vchasno_signer:
  proxy_service_url: /internal-api/proxy
  use_context: true
consts:
  gen_archive_ttl: 90 # ttl for gen archive task in seconds
  max_files: 100 # max files in one archive
concierge:
  service: vchasno
  backend_url: http://concierge-service-backend.vchasno.svc.cluster.local:8000
  frontend_url: http://concierge-service-frontend.vchasno.svc.cluster.local:8000
  cookie_name: vchasno_auth
  cookie_domain: .vchasno.com.ua
  token: ${CONCIERGE_TOKEN}
edi:
  landing_url: https://edi-dev.vchasno.com.ua
  rpc_auth_token: ${EDI_RPC_AUTH_TOKEN}
  api_url: http://edi-service-http-server.edi.svc.cluster.local:5000/jsonrpc
  host: https://edi-dev.vchasno.com.ua
esputnik:
  url: https://esputnik.com/api/v1/
  login: <EMAIL>
  password: ${ESPUTNIK_PASSWORD}
  address_book_id: 16832
  limit_insert: 3000
ttn:
  host: https://ettn-dev.vchasno.com.ua
  api_url: http://ettn-http-server.ettn.svc.cluster.local:8080
  landing_url: https://ttn.vchasno.com.ua
  auth_token: ${TTN_AUTH_TOKEN}
kassa:
  host: https://kasa-dev.vchasno.com.ua
  api_url: http://kasa-http-server.kasa.svc.cluster.local:8080
  landing_url: https://kasa.vchasno.com.ua
  auth_token: ${KASSA_AUTH_TOKEN}
kep:
  host: https://kep.vchasno.stg.evo
  landing_url: https://cap.vchasno.com.ua
  auth_token: AJtMNi8xyTOwbalcSbWX7V9kkv0CAs7AucvL7iLz
zvit:
  # Trailing / is essential for Zvit (/app/)
  host: https://zvit-dev.vchasno.com.ua/app/
  landing_url: https://zvit-dev.vchasno.com.ua
cloud_signer:
  host: https://cs.vchasno.stg.evo
  auth_token: 3a6c2fb7-c2c0-418a-a12a-0c6e8cb35783
diia:
  host: https://api2s.diia.gov.ua
  token: ${DIIA_TOKEN}
  offer_id: ${DIIA_OFFER_ID}
  branch_id: ${DIIA_BRANCH_ID}
  auth_acquirer_token: ${DIIA_AUTH_ACQUIRER_TOKEN}
integrations:
  header: X-Vchasno-Private-Token
  zakupki:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRnMFY4NnZ4QlhSYzFoZW1lQityeTZBJDJxeE1XM3ZhTUxyQXlUazF5aFZUREE=
  kep:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRqSFpIZWdGbXJhaHh0R0N1NkpJWERnJEhUeEN1eitHY2Q2dmFxaEtpOGczeXc=
  kasa:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xNjM4NCx0PTMscD0xJDYyK21XdW9tdytLc283cUJ1V1B6RVEkRTN5Yk5sMDNXOFFFYzNWUHl1QjQwRzQxeDlHdUxPUDVUbkI4dEFaUWpmWQ==
  ttn:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT02NTUzNix0PTMscD00JGx5TWprZGJwTUFYTFZPRklNK1VIWmckSVlLQ2dpcEYwRExQVlZhUVV4anhDLysrOTBnVGJ0L3c0bW1MUVlNQVZKOA==
  hrs:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT02NTUzNix0PTMscD00JDVoenI0eEhwdHVSd3V4MzhpMysxNWckeS9LNStxYlhDSXNLWHo0MnVpMkdsNUMyckNXbUlQR3VhOXhzVmtqQTJucw==
  landing:
    token_hashes:
    # Token: "701f62f6-c16d-4a5f-8190-1038533ed923"
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  creatio:
    token_hashes:
    - JGFyZ29uMmkkdj0xOSRtPTE2LHQ9MixwPTEkVEdoMlltaGtaSEE1WWpac00yTmFiUSRzL0orcnYrc21kVnpLVEpsZmJjdC9R
antivirus:
  host: http://antivirus-service.services-edo.svc.cluster.local:5004
signer_web:
  host: http://signer-web.vchasno.svc.cluster.local:8000
  secret: ${SIGNER_WEB_SECRET}
  key_id: ${SIGNER_WEB_KEY_ID} # ******** key
evopay:
  host: https://api-epdev.rozetkapay.com
  api_key: ${EVOPAY_API_KEY}
  api_secret: ${EVOPAY_API_SECRET}
sentry:
  dsn_backend: https://<EMAIL>/2
  dsn_frontend: https://<EMAIL>/6
privatbank:
  host: https://acp.privatbank.ua
  integration_id: ${PRIVATBANK_INTEGRATION_ID}
  integration_token: ${PRIVATBANK_INTEGRATION_TOKEN}
  account_number: *****************************
crm:
  url: http://crm-service-http.vchasno.svc.cluster.local:8080
  token: h_3vj9v_cg_YWqYN3fmtUVMje_LCn5aKMmxMTTSQ2LzhavXghJvvMfv92DEh
collabora:
  url: https://edo-dev.vchasno.com.ua/collabora
gotenberg:
  url: http://gotenberg-http.vchasno.svc.cluster.local:3000
hrs:
  api_url: http://hrs-http-server.hrs.svc.cluster.local:8080
  token: fake-token
typeform:
  form_id: null
