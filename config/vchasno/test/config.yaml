app:
  debug: true
  demo: true
  test: true
  port: 8000
  domain: http://localhost:8000
  static_host: http://localhost:8000/static
  static_prefix: /static
  client_max_size: 10

  request_timeout: 14.5
  delete_s3_timeout: 12.5
  upload_timeout: 29.5
  upload_s3_timeout: 25
  papka24_timeout: 5
  sync_roles_timeout: 7.5
  sign_timeout:
    default: 16
    slow: 50
  shutdown_timeout: 60
  slow_urls:
    - /api/private/blackbox/documents
    - /api/v1/documents
    - /api/v2/documents
    - /downloads/actions/documents.csv
    - /internal-api/companies
    - /internal-api/companies/mobile-id
    - /internal-api/contacts/dossier
    - /internal-api/contacts/sync
    - /internal-api/documents
    - /internal-api/documents/import/papka24
    - /internal-api/proxy
    - /internal-api/registration/signers
  slow_urls_patterns:
    - /internal-api/documents/.+/signatures
    - /internal-api/documents/.+/xml-to-pdf

  trusted_origins:
    - http://localhost:7777

  content_security_policy:
    default:
      - self
    script:
      - self
      - https://*.licdn.com
      - https://*.googletagmanager.com
      # TODO: Refactor FE to disallow eval and inline scripts.
      - unsafe-eval
      - unsafe-inline
    script_src_elem:
      - self
      - https://*.doubleclick.net
      - https://*.googletagmanager.com
    style:
      - self
    style_src_elem:
      - self
    img:
      - self
      - 'data:'
      - https://*.facebook.com
      - https://*.doubleclick.net
      - https://*.google.com
      - https://*.google.com.ua
      - https://*.google.de
      - https://*.google-analytics.com
      - https://*.googletagmanager.com
    font:
      - self
    connect:
      - self
      - https://*.linkedin.com
      - https://*.doubleclick.net
      - https://*.google-analytics.com
      - https://*.google.com
      - https://*.google.com.ua
      - https://*.googletagmanager.com
      - https://*.facebook.com
      - https://*.facebook.net
      - https://*.posthog.com
    frame:
      - self
      - https://*.doubleclick.net
      - https://*.facebook.com
    frame_ancestors:
      - self
    object:
      - self
    worker:
      - self

  brand: Вчасно
  logo_path: images/logos/vchasno.png
  session_max_age: 2592000  # = 60 secs * 60 mins * 24 hrs * 30 days
  cookie_name: vchasno_test_session
  cookie_domain: localhost
  cookie_secure: false
  domain_name: vchasno.com.ua
  info_email: <EMAIL>
  noreply_email: <EMAIL>
  support_email: <EMAIL>
  sales_email: <EMAIL>
  support_phone: '+38 044 392 03 00'
  support_phone_lifecell: '+38 063 460 5 333'
  support_phone_vodafone: '+38 050 416 5 333'
  support_phone_kyivstar: '+38 067 460 5 333'
  support_viber: 'vchasnoedo'
  support_telegram: 'vchasnoedo_bot'
  support_hours: '8:00 - 19:00'

  fernet_key: 1bnLESCOe_L1jynhDxUiBxSdQMzKsWWzleW9YRoPbB4=
  test_edrpou: '11111111'
  sign_widget_iit_url: https://eu.iit.com.ua/sign-widget/v20200922/
  sign_widget_iit_fozzy_url: https://eu.iit.com.ua/sign-widget/v20240301/
  default_language: uk
  available_languages:
    - en
    - uk
theme:
  primary_color: "#FF9C01"
auth:
  totp_interval: 30
  totp_secret_salt: GEPVVWZOSSJFFJNV2KD7RZD3L2VTOVFNKJL44EGPWK6IIGVQ4ZNQ====
colbert:
  api_host: 'https://colbert-trunk.dev-cluster.uaprom'
  secret: 'Qp9iFhX5zqrzvypi6BRBepcdVvCe3xilMCsYrgzXIdU'
  token: 'eXQPh8BL8Y1v1roZ'
  url: 'https://assets.colbert-trunk.dev-cluster.uaprom/static/colbert.js'
feature_flags:
  pass_sign_info_to_backend: true
  multi_download: true
  new_worker: true
  pro_rate_allow_for_free: true
feature_flags_v2:
  manager: test
microsoft_auth:
  client_id: TEST_CLIENT_ID
apple_auth:
  team_id: TEST_TEAM_ID
  edo_web_client_id: TEST_CLIENT_ID
  edo_client_id: TEST_CLIENT_ID
  kep_client_id: TEST_CLIENT_ID
  kasa_client_id: TEST_CLIENT_ID
  key_id: TEST_KEY_ID
  key: KEY
tokens:
  public_key: config/vchasno/test/keys/tokens.pub
  private_key: config/vchasno/test/keys/tokens.pem
db:
  # `render_test_config_templates` will modify dbname for run tests in parallel
  url: postgres://evodoc:evodoc@127.0.0.1:5433/evodoc?client_encoding=utf-8
  url_readonly: postgres://evodoc:evodoc@127.0.0.1:5433/evodoc?client_encoding=utf-8
  minsize: 0
  maxsize: 10
  timeout: 5.0
  use_aiopg_connection_pool: true
events_db:
  url: *********************************************/evodoc_events?client_encoding=utf-8
  minsize: 0
  maxsize: 10
  timeout: 5.0
  use_aiopg_connection_pool: true
es:
  hosts: [ 'http://elasticsearch-test:9200' ]
  document_index: test_documents
  comment_index: test_comments
  contact_recipient_index: test_contact_recipients
kafka:
  bootstrap_servers:
    - '127.0.0.1:9092'
  callback_timeout: 2
  client_id: kafka-vchasno-dev
  group_id: kafka-vchasno-dev
  producer:
    request_timeout_ms: 40000
  consumer:
    session_timeout_ms: 30000
  topic_prefix: 'vchasno'
redis:
  host: 127.0.0.1
  port: 2011
  db: 0
  timeout: 1.0
s3:
  type: test
  host: http://test
  access_key: aaaaaaaaaaaaaaaaaaaa
  secret_key: bbbbbbbbbbbbbbbbbbbb
  bucket: vchasno-test
  region_name: us-east-1
  read_timeout: 3
  connect_timeout: 0.5
  connections_limit: 100
  signature_version: s3v4
  encryption_keys:
    - 't5nk+knQXHj9/gc0XtDNXcRXmw2QFQ3EXs9Tv3xdRcc='
evo_sender:
  project_id: '__invalid-project-id__'
  base_url: 'http://sender-trunk.dev-cluster.uaprom/'
smtp:
  host: fake
  port: 25
  timeout: 12.5
  xmailq: vchasno.com.ua
aws_ses:
  region_name: eu-central-1
  access_key: fake
  secret_key: fake
youcontrol: # should be mocked in tests
  url: 'https://test'
  login: '<EMAIL>'
  password: 'test'
  referral_url: 'https://referral/'
  youscore_domain: https://test
  youscore_apikey: FAKE
sync_contacts_api:
  e36507036: # promua
    secret_key: 35bdf49d-f79b-42c1-abea-86d0242acbcf
    url: http://my.trunk.uaprom/api/_/tools/v1/find_company_contact_info
    url_by_invoice: http://my.trunk.uaprom/api/_/tools/v1/find_company_contact_info_by_invoice_id
    param: token
    limit: 100
    prefix: 'ua-'
  e40283641: # zakupki
    secret_key: 89325b62-737e-4210-a406-a2719d4b8f07
    url: https://my.zakupki.prom.ua/remote/public_api/merchant_contact_info
    param: access_token
    limit: 20
contacts:
  # TODO[KG]: merge with `sync_contacts_api` (+ changes in secrets config)
  uploads:
    allowed_extensions:
      - .csv
      - .xlsx
    max_file_size: 5  # MB
url2pdf:
  url: http://url2pdf-trunk.evo:14800/
  use_xz: true
  timeout: 12.5
doc2pdf:
  url: http://app.doc2pdf.vchasno.svc.c.evo:8080/
  timeout: 15
vchasno_signer:
  proxy_service_url: /internal-api/proxy
  use_context: true
consts:
  gen_archive_ttl: 90 # ttl for gen archive task in seconds
  max_files: 10 # max files in one archive
fcm:
  is_enabled: false
  project_id: fake
  config_path: fake
concierge:
  service: vchasno
  backend_url: http://fake
  frontend_url: http://fake
  cookie_name: vchasno_auth
  cookie_domain: localhost:8000
  token: supersecret
esputnik:
  url: https://esputnik.com/api/v1/
  login: <EMAIL>
  password: password
  address_book_id: 16832
  limit_insert: 3000
ttn:
  host: http://0.0.0.0:5060
  api_url: http://0.0.0.0:5060
  landing_url: https://ttn.vchasno.com.ua
  auth_token: supersecret
edi:
  api_url: http://localhost:5000/jsonrpc
  rpc_auth_token: 012c120d-6620-426a-9de0-413f1f101512
  host: http://0.0.0.0:5000
  landing_url: https://edi.vchasno.com.ua
kassa:
  host: http://0.0.0.0:5050
  api_url: http://0.0.0.0:5050
  landing_url: https://kasa.vchasno.com.ua
  auth_token: supersecret
kep:
  host: http://localhost:6060
  landing_url: https://cap.vchasno.com.ua
  auth_token: fake
zvit:
  # Trailing / is essential for Zvit (/app/)
  host: http://0.0.0.0:5070/app/
  landing_url: https://zvit.vchasno.ua
cloud_signer:
  host: http://localhost:5055
  auth_token: token
diia:
  host: https://api2s.diia.gov.ua
  token: TOKEN
  offer_id: OID
  branch_id: BID
  auth_acquirer_token: fake
integrations:
  header: X-Vchasno-Private-Token
  zakupki:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  kep:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  kasa:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  ttn:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  hrs:
    token_hashes:
    - JGFyZ29uMmlkJHY9MTkkbT0xMDI0MDAsdD0yLHA9OCRQd1BlbDJ4WkR2VjlNeXdRd01UQXpRJG5GdHBEc2lLbXh5eVdDUVEza2FRYUE=
  creatio:
    token_hashes:
    - JGFyZ29uMmkkdj0xOSRtPTE2LHQ9MixwPTEkVEdoMlltaGtaSEE1WWpac00yTmFiUSRzL0orcnYrc21kVnpLVEpsZmJjdC9R
antivirus:
  host: https://localhost:5004
crm:
  url: https://vchasno-crm.stg.evo
  token: fake
signer_web:
  host: https://signservice.stg.evo/rust
  secret: stg-secret-token
  key_id: d2aa024b-e35f-48e3-bdfa-af636b0d76f8 # ******** key
evopay:
  host: https://api-epdev.rozetkapay.com
  api_key: d97e6293-b4d2-4163-92c4-5fbe685a31f6
  api_secret: Z0tNdllHN3RJRDVCbHQ3VXg2SmJyVHBk
privatbank:
  host: https://acp.privatbank.ua
  integration_id: fake
  integration_token: fake
  account_number: *****************************
collabora:
  url: https://edo-dev.vchasno.com.ua/collabora
gotenberg:
  url: http://fake:3000
hrs:
  api_url: http://localhost:8080/
  token: fake-token
typeform:
  form_id: null
